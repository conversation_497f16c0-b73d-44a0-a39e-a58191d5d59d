import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAdditionalImages() {
  console.log('Starting to fix additional images...');
  
  try {
    // Fetch all products with additional_images
    const { data: products, error } = await supabase
      .from('products')
      .select('id, additional_images')
      .not('additional_images', 'is', null);
    
    if (error) {
      throw error;
    }
    
    console.log(`Found ${products.length} products with additional images to process`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      if (!product.additional_images || !Array.isArray(product.additional_images)) {
        continue;
      }
      
      // Process each additional image
      const updatedImages = product.additional_images.map(imageUrl => {
        if (!imageUrl) return imageUrl;
        
        // Remove ~mv2 part if present
        let updatedUrl = imageUrl.replace(/~mv2/g, '');
        
        // Change image extensions to .webp
        updatedUrl = updatedUrl.replace(/\.(jpe?g|png)$/i, '.webp');
        
        return updatedUrl;
      });
      
      // Check if any changes were made
      const hasChanges = JSON.stringify(updatedImages) !== JSON.stringify(product.additional_images);
      
      if (hasChanges) {
        // Update the product with fixed additional_images
        const { error: updateError } = await supabase
          .from('products')
          .update({ additional_images: updatedImages })
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.id}:`, updateError);
          continue;
        }
        
        updatedCount++;
        console.log(`Updated product ${product.id} with fixed additional images`);
      }
    }
    
    console.log(`Successfully updated ${updatedCount} products with fixed additional images`);
    
  } catch (error) {
    console.error('Error fixing additional images:', error);
  }
}

// Run the function
fixAdditionalImages()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
