#!/usr/bin/env tsx
/**
 * Organize Super Agent files and show clear status
 */

import { readdirSync, statSync } from 'fs';
import { join } from 'path';

function organizeFiles() {
  console.log('🗂️  Super Agent Files Organization\n');
  
  const superAgentDir = join(process.cwd(), 'docs', 'super_agent');
  const files = readdirSync(superAgentDir);
  
  const categories = {
    agent1_enriched: [] as string[],
    agent2_enriched: [] as string[],
    analysis_files: [] as string[],
    reports: [] as string[],
    other: [] as string[]
  };
  
  files.forEach(file => {
    const filePath = join(superAgentDir, file);
    const stats = statSync(filePath);
    
    if (stats.isFile()) {
      if (file.includes('enriched_active_seeds')) {
        categories.agent1_enriched.push(file);
      } else if (file.includes('enriched_premium') || file.includes('top_10_premium')) {
        categories.agent2_enriched.push(file);
      } else if (file.includes('active-seeds-missing-data') || file.includes('premium-inactive')) {
        categories.analysis_files.push(file);
      } else if (file.includes('.md')) {
        categories.reports.push(file);
      } else {
        categories.other.push(file);
      }
    }
  });
  
  console.log('📊 AGENT #1 (Active Products) - ENRICHED DATA:');
  categories.agent1_enriched.forEach((file, i) => {
    console.log(`   ${i + 1}. ${file}`);
  });
  console.log(`   Status: ${categories.agent1_enriched.length} batches completed\n`);
  
  console.log('💎 AGENT #2 (Premium Inactive) - ENRICHED DATA:');
  categories.agent2_enriched.forEach((file, i) => {
    console.log(`   ${i + 1}. ${file}`);
  });
  console.log(`   Status: ${categories.agent2_enriched.length} batches completed\n`);
  
  console.log('📋 ANALYSIS FILES (Input data for agents):');
  categories.analysis_files.forEach((file, i) => {
    console.log(`   ${i + 1}. ${file}`);
  });
  console.log('');
  
  console.log('📄 REPORTS & DOCUMENTATION:');
  categories.reports.forEach((file, i) => {
    console.log(`   ${i + 1}. ${file}`);
  });
  console.log('');
  
  if (categories.other.length > 0) {
    console.log('🗃️  OTHER FILES:');
    categories.other.forEach((file, i) => {
      console.log(`   ${i + 1}. ${file}`);
    });
    console.log('');
  }
  
  console.log('🎯 CURRENT STATUS:');
  console.log(`   Agent #1: ${categories.agent1_enriched.length} batches (Active products)`);
  console.log(`   Agent #2: ${categories.agent2_enriched.length} batch (Premium inactive)`);
  console.log('');
  
  console.log('💡 RECOMMENDATIONS:');
  console.log('   1. ✅ Agent #2 should continue with next 10 premium products');
  console.log('   2. ⏳ Wait for Agent #1 to finish remaining active products');
  console.log('   3. 🚀 Import all batches when complete');
  console.log('   4. 🧹 Archive analysis files after import');
  
  console.log('\n📁 KEY FILES TO IMPORT WHEN READY:');
  console.log('   Agent #1 Enriched Data:');
  categories.agent1_enriched.forEach(file => {
    console.log(`     - ${file}`);
  });
  console.log('   Agent #2 Enriched Data:');
  categories.agent2_enriched.forEach(file => {
    console.log(`     - ${file}`);
  });
}

organizeFiles();
