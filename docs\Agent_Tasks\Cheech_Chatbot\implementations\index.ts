/**
 * Cheech Chatbot Main Application
 * 
 * This file serves as the entry point for the Cheech chatbot application,
 * integrating all components and initializing the system.
 */

import { CheechChatbot } from './CheechChatbot';
import { RAGEngine } from './RAGEngine';
import { ContextManager } from './ContextManager';
import { WebNavigationAssistant } from './WebNavigationAssistant';
import { Document } from './types';

// Configuration
const config = {
  openAiApiKey: process.env.OPENAI_API_KEY || '',
  baseUrl: process.env.BASE_URL || 'https://bitsnbongs.com',
  knowledgeBasePath: './data/knowledge_base.json',
  productDatabasePath: './data/products.json'
};

/**
 * Initialize the Cheech chatbot system
 */
async function initCheechChatbot() {
  try {
    console.log('Initializing Cheech Chatbot...');
    
    // Create chatbot instance
    const chatbot = new CheechChatbot(config);
    
    // Initialize sample data for testing
    await initializeSampleData();
    
    // Start the API server
    // This would be implemented in a real application
    console.log('Cheech Chatbot initialized successfully');
    
    return chatbot;
  } catch (error) {
    console.error('Error initializing Cheech Chatbot:', error);
    throw error;
  }
}

/**
 * Initialize sample data for testing
 */
async function initializeSampleData() {
  try {
    console.log('Initializing sample data...');
    
    // Load sample knowledge base
    const knowledgeBase = await loadSampleKnowledgeBase();
    console.log(`Loaded ${knowledgeBase.length} knowledge base entries`);
    
    // Load sample products
    const products = await loadSampleProducts();
    console.log(`Loaded ${products.length} products`);
    
    // In a real application, this would load data from files or a database
  } catch (error) {
    console.error('Error initializing sample data:', error);
  }
}

/**
 * Load sample knowledge base
 */
async function loadSampleKnowledgeBase(): Promise<any[]> {
  // This would load from a file or database in a real application
  return [
    {
      id: 'kb-1',
      question: 'How do I clean my bong?',
      answer: 'To clean your bong, you\'ll need isopropyl alcohol (91% or higher), coarse salt, and warm water. Empty the bong, rinse with warm water, add alcohol and salt, cover openings, shake vigorously, rinse thoroughly, and let dry. For best results, clean your bong at least once a week.',
      category: 'cleaning',
      tags: ['bong', 'cleaning', 'maintenance']
    },
    {
      id: 'kb-2',
      question: 'What\'s the difference between CBD and THC?',
      answer: 'CBD (cannabidiol) and THC (tetrahydrocannabinol) are both cannabinoids found in cannabis plants. The key difference is that THC produces psychoactive effects ("high"), while CBD does not. CBD is often used for its potential therapeutic benefits without the intoxicating effects of THC.',
      category: 'education',
      tags: ['cbd', 'thc', 'cannabinoids']
    },
    {
      id: 'kb-3',
      question: 'How do I choose the right vaporizer?',
      answer: 'When choosing a vaporizer, consider: 1) Material (dry herb, concentrate, or both), 2) Portability needs, 3) Battery life, 4) Temperature control, 5) Budget, and 6) Vapor quality. Desktop vaporizers offer better vapor quality but aren\'t portable, while pen vaporizers are discreet but may have less battery life.',
      category: 'products',
      tags: ['vaporizer', 'vape', 'buying guide']
    }
  ];
}

/**
 * Load sample products
 */
async function loadSampleProducts(): Promise<any[]> {
  // This would load from a file or database in a real application
  return [
    {
      id: 'p-1',
      name: 'Crystal Clear Cleaning Solution',
      description: 'Premium cleaning solution specifically formulated for glass water pipes and bongs. Removes resin, tar, and buildup without harmful chemicals.',
      price: 14.99,
      imageUrl: '/images/products/crystal-clear.jpg',
      category: 'cleaning',
      tags: ['cleaning', 'solution', 'maintenance'],
      rating: 4.8,
      reviewCount: 156,
      inStock: true
    },
    {
      id: 'p-2',
      name: 'Herbal Essence Vaporizer',
      description: 'Advanced dry herb vaporizer with precision temperature control, extended battery life, and pure vapor path for the cleanest flavor.',
      price: 149.99,
      imageUrl: '/images/products/herbal-essence.jpg',
      category: 'vaporizers',
      subcategory: 'dry-herb',
      tags: ['vaporizer', 'dry herb', 'portable'],
      rating: 4.6,
      reviewCount: 89,
      inStock: true
    },
    {
      id: 'p-3',
      name: 'Full Spectrum CBD Oil (1000mg)',
      description: 'High-quality full spectrum CBD oil containing multiple cannabinoids for enhanced effectiveness. Lab tested for purity and potency.',
      price: 79.99,
      imageUrl: '/images/products/cbd-oil.jpg',
      category: 'cbd',
      subcategory: 'tinctures',
      tags: ['cbd', 'oil', 'tincture', 'full spectrum'],
      rating: 4.9,
      reviewCount: 212,
      inStock: true
    }
  ];
}

// Export the initialization function
export { initCheechChatbot };
