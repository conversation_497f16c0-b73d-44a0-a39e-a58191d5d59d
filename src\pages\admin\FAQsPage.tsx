import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { FAQ } from '@/types/database';
import { useToast } from '@/hooks/use-toast';
import { 
  Loader2, Plus, Edit, Trash2, ArrowUp, ArrowDown, 
  Eye, EyeOff, Save, X, Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const FAQsPage = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFAQ, setSelectedFAQ] = useState<FAQ | null>(null);
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: 'Products',
    is_published: true
  });

  // Fetch FAQs from Supabase
  const { data: faqs, isLoading } = useQuery({
    queryKey: ['admin-faqs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .order('order_index', { ascending: true });
      
      if (error) throw error;
      return data as FAQ[];
    }
  });

  // Create FAQ mutation
  const createFAQMutation = useMutation({
    mutationFn: async (faq: Omit<FAQ, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from('faqs')
        .insert(faq)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-faqs'] });
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({ title: "Success", description: "FAQ added successfully" });
      resetForm();
      setOpenDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add FAQ",
        variant: "destructive"
      });
    }
  });

  // Update FAQ mutation
  const updateFAQMutation = useMutation({
    mutationFn: async ({ id, ...faq }: { id: string, [key: string]: any }) => {
      const { data, error } = await supabase
        .from('faqs')
        .update(faq)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-faqs'] });
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({ title: "Success", description: "FAQ updated successfully" });
      resetForm();
      setOpenDialog(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update FAQ",
        variant: "destructive"
      });
    }
  });

  // Delete FAQ mutation
  const deleteFAQMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-faqs'] });
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({ title: "Success", description: "FAQ deleted successfully" });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete FAQ",
        variant: "destructive"
      });
    }
  });

  // Reorder FAQ mutation
  const reorderFAQMutation = useMutation({
    mutationFn: async ({ id, newIndex }: { id: string, newIndex: number }) => {
      const { error } = await supabase
        .from('faqs')
        .update({ order_index: newIndex })
        .eq('id', id);
      
      if (error) throw error;
      return { id, newIndex };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-faqs'] });
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to reorder FAQ",
        variant: "destructive"
      });
    }
  });

  // Toggle FAQ publication status
  const togglePublicationMutation = useMutation({
    mutationFn: async ({ id, isPublished }: { id: string, isPublished: boolean }) => {
      const { error } = await supabase
        .from('faqs')
        .update({ is_published: isPublished })
        .eq('id', id);
      
      if (error) throw error;
      return { id, isPublished };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['admin-faqs'] });
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({ 
        title: "Success", 
        description: `FAQ ${data.isPublished ? 'published' : 'unpublished'} successfully` 
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update publication status",
        variant: "destructive"
      });
    }
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, is_published: checked }));
  };

  // Handle select changes
  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, category: value }));
  };

  // Reset form
  const resetForm = () => {
    setSelectedFAQ(null);
    setFormData({
      question: '',
      answer: '',
      category: 'Products',
      is_published: true
    });
  };

  // Open dialog for adding/editing FAQ
  const openEditDialog = (faq?: FAQ) => {
    if (faq) {
      setSelectedFAQ(faq);
      setFormData({
        question: faq.question,
        answer: faq.answer,
        category: faq.category || 'Products',
        is_published: faq.is_published
      });
    } else {
      resetForm();
    }
    setOpenDialog(true);
  };

  // Save FAQ
  const saveFAQ = () => {
    if (!formData.question || !formData.answer) {
      toast({
        title: "Error",
        description: "Question and answer are required",
        variant: "destructive"
      });
      return;
    }

    if (selectedFAQ) {
      // Update existing FAQ
      updateFAQMutation.mutate({
        id: selectedFAQ.id,
        question: formData.question,
        answer: formData.answer,
        category: formData.category,
        is_published: formData.is_published,
        updated_at: new Date().toISOString()
      });
    } else {
      // Add new FAQ
      const maxOrderIndex = faqs?.reduce((max, faq) => Math.max(max, faq.order_index), 0) || 0;
      
      createFAQMutation.mutate({
        question: formData.question,
        answer: formData.answer,
        category: formData.category,
        is_published: formData.is_published,
        order_index: maxOrderIndex + 1
      });
    }
  };

  // Move FAQ up or down
  const moveFAQ = (faq: FAQ, direction: 'up' | 'down') => {
    if (!faqs) return;
    
    const currentIndex = faqs.findIndex(f => f.id === faq.id);
    let targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // Ensure target index is within bounds
    if (targetIndex < 0 || targetIndex >= faqs.length) return;
    
    const targetFAQ = faqs[targetIndex];
    
    // Swap order indices
    reorderFAQMutation.mutate({ id: faq.id, newIndex: targetFAQ.order_index });
    reorderFAQMutation.mutate({ id: targetFAQ.id, newIndex: faq.order_index });
  };

  // Toggle FAQ publication status
  const togglePublication = (faq: FAQ) => {
    togglePublicationMutation.mutate({
      id: faq.id,
      isPublished: !faq.is_published
    });
  };

  // Delete FAQ
  const deleteFAQ = (id: string) => {
    if (window.confirm("Are you sure you want to delete this FAQ?")) {
      deleteFAQMutation.mutate(id);
    }
  };

  // Filter FAQs by search query
  const filteredFAQs = faqs?.filter(faq => 
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (faq.category && faq.category.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Group FAQs by category
  const groupedFAQs = filteredFAQs?.reduce((acc, faq) => {
    const category = faq.category || 'General';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(faq);
    return acc;
  }, {} as Record<string, FAQ[]>) || {};

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Manage FAQs</h1>
        <Button onClick={() => openEditDialog()} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          <span>Add FAQ</span>
        </Button>
      </div>

      <div className="flex items-center space-x-2 mb-6">
        <Search className="h-5 w-5 text-gray-400" />
        <Input
          placeholder="Search FAQs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-md"
        />
      </div>

      <Tabs defaultValue="all">
        <TabsList className="mb-4">
          <TabsTrigger value="all">All FAQs</TabsTrigger>
          <TabsTrigger value="by-category">By Category</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          {filteredFAQs && filteredFAQs.length > 0 ? (
            filteredFAQs.map((faq) => (
              <FAQItem
                key={faq.id}
                faq={faq}
                onEdit={() => openEditDialog(faq)}
                onDelete={() => deleteFAQ(faq.id)}
                onMoveUp={() => moveFAQ(faq, 'up')}
                onMoveDown={() => moveFAQ(faq, 'down')}
                onTogglePublication={() => togglePublication(faq)}
                isFirst={faq.order_index === Math.min(...filteredFAQs.map(f => f.order_index))}
                isLast={faq.order_index === Math.max(...filteredFAQs.map(f => f.order_index))}
              />
            ))
          ) : (
            <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-lg">
              <p className="text-lg">No FAQs found</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="by-category" className="space-y-8">
          {Object.keys(groupedFAQs).length > 0 ? (
            Object.entries(groupedFAQs).map(([category, categoryFaqs]) => (
              <div key={category} className="space-y-4">
                <h2 className="text-xl font-semibold">{category}</h2>
                {categoryFaqs.map((faq) => (
                  <FAQItem
                    key={faq.id}
                    faq={faq}
                    onEdit={() => openEditDialog(faq)}
                    onDelete={() => deleteFAQ(faq.id)}
                    onMoveUp={() => moveFAQ(faq, 'up')}
                    onMoveDown={() => moveFAQ(faq, 'down')}
                    onTogglePublication={() => togglePublication(faq)}
                    isFirst={faq.order_index === Math.min(...filteredFAQs!.map(f => f.order_index))}
                    isLast={faq.order_index === Math.max(...filteredFAQs!.map(f => f.order_index))}
                  />
                ))}
              </div>
            ))
          ) : (
            <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-lg">
              <p className="text-lg">No FAQs found</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Add/Edit FAQ Dialog */}
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedFAQ ? 'Edit FAQ' : 'Add New FAQ'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="question">Question</Label>
              <Input
                id="question"
                name="question"
                value={formData.question}
                onChange={handleInputChange}
                placeholder="Enter the question"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="answer">Answer</Label>
              <Textarea
                id="answer"
                name="answer"
                value={formData.answer}
                onChange={handleInputChange}
                placeholder="Enter the answer"
                rows={6}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={handleSelectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Products">Products</SelectItem>
                  <SelectItem value="Usage">Usage</SelectItem>
                  <SelectItem value="Shipping">Shipping</SelectItem>
                  <SelectItem value="Legal">Legal</SelectItem>
                  <SelectItem value="General">General</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_published"
                checked={formData.is_published}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="is_published">Published</Label>
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button 
                variant="outline" 
                onClick={() => setOpenDialog(false)}
                className="flex items-center gap-1"
              >
                <X className="h-4 w-4" />
                <span>Cancel</span>
              </Button>
              <Button 
                onClick={saveFAQ}
                className="flex items-center gap-1"
                disabled={createFAQMutation.isPending || updateFAQMutation.isPending}
              >
                {(createFAQMutation.isPending || updateFAQMutation.isPending) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{selectedFAQ ? 'Update' : 'Add'} FAQ</span>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// FAQ Item Component
interface FAQItemProps {
  faq: FAQ;
  onEdit: () => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onTogglePublication: () => void;
  isFirst: boolean;
  isLast: boolean;
}

const FAQItem = ({ 
  faq, 
  onEdit, 
  onDelete, 
  onMoveUp, 
  onMoveDown, 
  onTogglePublication,
  isFirst,
  isLast
}: FAQItemProps) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg font-medium">{faq.question}</CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={faq.is_published ? "default" : "outline"}>
                {faq.is_published ? "Published" : "Draft"}
              </Badge>
              {faq.category && (
                <Badge variant="secondary">{faq.category}</Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onMoveUp}
              disabled={isFirst}
              className="h-8 w-8"
            >
              <ArrowUp className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onMoveDown}
              disabled={isLast}
              className="h-8 w-8"
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onTogglePublication}
              className="h-8 w-8"
              title={faq.is_published ? "Unpublish" : "Publish"}
            >
              {faq.is_published ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onEdit}
              className="h-8 w-8"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onDelete}
              className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <div className={`prose max-w-none ${!expanded && 'line-clamp-2'}`}>
            {faq.answer.split('\n').map((paragraph, index) => (
              <p key={index} className="my-1">{paragraph}</p>
            ))}
          </div>
          {faq.answer.split('\n').length > 1 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setExpanded(!expanded)}
              className="mt-1 h-6 text-xs"
            >
              {expanded ? "Show less" : "Show more"}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FAQsPage;
