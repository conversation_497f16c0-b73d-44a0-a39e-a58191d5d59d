// <PERSON><PERSON><PERSON> to update the database schema for subcategories
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateSchema() {
  console.log('Updating database schema...');
  
  try {
    // Add parent_id and updated_at to categories table
    console.log('Adding parent_id and updated_at to categories table...');
    const { error: categoriesError } = await supabase.rpc('execute_sql', {
      sql: `
        ALTER TABLE categories 
        ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES categories(id) NULL,
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
      `
    });
    
    if (categoriesError) {
      throw categoriesError;
    }
    
    // Add subcategory_id to products table
    console.log('Adding subcategory_id to products table...');
    const { error: productsError } = await supabase.rpc('execute_sql', {
      sql: `
        ALTER TABLE products 
        ADD COLUMN IF NOT EXISTS subcategory_id UUID REFERENCES categories(id) NULL;
      `
    });
    
    if (productsError) {
      throw productsError;
    }
    
    console.log('Schema update completed successfully!');
  } catch (error) {
    console.error('Error updating schema:', error.message);
  }
}

// Run the update
updateSchema();
