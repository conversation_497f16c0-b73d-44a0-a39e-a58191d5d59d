const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const slugify = require('slugify');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
  inputDir: 'data',
  outputDir: 'data/output',
  inputFileName: process.argv[2] || 'new_sample_products.csv', // Updated to use the current sample file
  outputFileName: 'import-compatible-products.csv',
};

// Define the columns that exist in our database
const VALID_COLUMNS = [
  'id',
  'name',
  'slug',
  'description',
  'price',
  'sale_price',
  'image',
  'additional_images',
  'category_id',
  'brand_id',
  'sku',
  'stock_quantity',
  'weight',
  'in_stock',
  'is_featured',
  'is_new',
  'is_active',
  'option_definitions',
  'created_at',
  'updated_at'
];

/**
 * Transform image URL by removing ~mv2 and converting to .webp
 * @param {string} url - The image URL to transform
 * @returns {string} - The transformed URL
 */
function transformImageUrl(url) {
  if (!url) return '';
  
  // Extract just the filename without path (if any)
  const filename = url.split('/').pop();
  
  // Handle specific format from catalog: 7caa35_6a5c1b6181f543109d047a419a70a6aa~mv2.jpg
  // Remove ~mv2 from image URLs but preserve the original filename structure
  let transformedFilename = filename.replace(/~mv2/g, '');
  
  // Also handle ~cv2 suffix that might be in some files
  transformedFilename = transformedFilename.replace(/~cv2/g, '');
  
  // Convert to .webp if it's a jpg or png
  if (transformedFilename.match(/\.(jpg|jpeg|png)$/i)) {
    transformedFilename = transformedFilename.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  
  // Use the correct Supabase storage URL with the product-images/ path prefix
  // This matches the actual structure in the storage bucket
  return `https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/product-images/${transformedFilename}`;
}

/**
 * Extract option values from option description
 * @param {string} optionDescription - The option description string
 * @returns {string[]} - Array of option values
 */
function extractOptionValues(optionDescription) {
  if (!optionDescription) return [];
  
  // Split by comma and clean up values
  return optionDescription
    .split(',')
    .map(value => value.trim())
    // Make sure to filter out any 'DROP_DOWN' values as they're not actual option values
    .filter(value => value && value !== 'DROP_DOWN');
}

/**
 * Determine the appropriate display type for an option based on its name
 * @param {string} optionName - The name of the option
 * @returns {string} - The display type for the option
 */
function getDisplayTypeForOption(optionName) {
  // Normalize the option name to lowercase for comparison
  const normalizedName = optionName.toLowerCase();
  
  // Determine display type based on option name
  if (normalizedName.includes('color') || normalizedName.includes('colour')) {
    return 'color_swatch';
  } else if (normalizedName.includes('size') || 
             normalizedName.includes('pack') || 
             normalizedName.includes('weight')) {
    return 'button';
  } else if (normalizedName.includes('material') || 
             normalizedName.includes('fabric')) {
    return 'material_button';
  } else {
    // Default to button for unknown option types
    return 'button';
  }
}

/**
 * Parse price from string
 * @param {string} price - The price string
 * @returns {number} - The parsed price
 */
function parsePrice(price) {
  if (!price) return 0;
  
  // Remove currency symbols and convert to number
  const cleanPrice = price.replace(/[£$€]/g, '').trim();
  return parseFloat(cleanPrice) || 0;
}

// Main transformation function
async function generateCompatibleCSV() {
  console.log('Starting CSV transformation...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Read input CSV
  const inputPath = path.join(CONFIG.inputDir, CONFIG.inputFileName);
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    return;
  }
  
  const inputCsv = fs.readFileSync(inputPath, 'utf8');
  
  // Parse CSV
  const { data } = Papa.parse(inputCsv, {
    header: true,
    skipEmptyLines: true,
  });
  
  // Pre-process the data to replace DROP_DOWN with appropriate display types
  // and ensure we're not keeping any DROP_DOWN values
  data.forEach(row => {
    // Replace productOptionType1 if it exists
    if (row.productOptionName1) {
      // Always use the appropriate display type based on option name
      row.productOptionType1 = getDisplayTypeForOption(row.productOptionName1);
      
      // Clean up option descriptions to remove DROP_DOWN
      if (row.productOptionDescription1) {
        row.productOptionDescription1 = row.productOptionDescription1.replace(/DROP_DOWN,?\s*/g, '');
      }
    }
    
    // Replace productOptionType2 if it exists
    if (row.productOptionName2) {
      // Always use the appropriate display type based on option name
      row.productOptionType2 = getDisplayTypeForOption(row.productOptionName2);
      
      // Clean up option descriptions to remove DROP_DOWN
      if (row.productOptionDescription2) {
        row.productOptionDescription2 = row.productOptionDescription2.replace(/DROP_DOWN,?\s*/g, '');
      }
    }
    
    // Replace productOptionType3 if it exists
    if (row.productOptionName3) {
      // Always use the appropriate display type based on option name
      row.productOptionType3 = getDisplayTypeForOption(row.productOptionName3);
      
      // Clean up option descriptions to remove DROP_DOWN
      if (row.productOptionDescription3) {
        row.productOptionDescription3 = row.productOptionDescription3.replace(/DROP_DOWN,?\s*/g, '');
      }
    }
  });
  
  console.log(`Found ${data.length} rows in input CSV`);
  
  // Transform products to match the exact column names in our database
  const compatibleProducts = data.map((row, index) => {
    // Transform image URLs
    let mainImageUrl = null;
    let additionalImageUrls = [];
    
    if (row.productImageUrl) {
      const imageUrls = row.productImageUrl.split(';');
      
      if (imageUrls.length > 0 && imageUrls[0]) {
        mainImageUrl = transformImageUrl(imageUrls[0].trim());
      }
      
      if (imageUrls.length > 1) {
        additionalImageUrls = imageUrls.slice(1)
          .map(url => url.trim())
          .filter(url => url)
          .map(url => transformImageUrl(url));
      }
    }
    
    // Generate a slug from the product name
    const slug = slugify(row.name || '', {
      lower: true,
      strict: true,
      remove: /[*+~.()'";:@]/g
    });
    
    // Create option definitions object - format as Record<string, string[]>
    // This is the format expected by the OptionDefinitionsManager component
    const optionDefinitions = {};
    
    // Process option 1
    if (row.productOptionName1 && row.productOptionDescription1) {
      const optionName = row.productOptionName1;
      const optionValues = extractOptionValues(row.productOptionDescription1);
      
      if (optionValues.length > 0) {
        // Simple array of values, not an object with values and display_type
        optionDefinitions[optionName] = optionValues;
      }
    }
    
    // Process option 2
    if (row.productOptionName2 && row.productOptionDescription2) {
      const optionName = row.productOptionName2;
      const optionValues = extractOptionValues(row.productOptionDescription2);
      
      if (optionValues.length > 0) {
        // Simple array of values, not an object with values and display_type
        optionDefinitions[optionName] = optionValues;
      }
    }
    
    // Process option 3
    if (row.productOptionName3 && row.productOptionDescription3) {
      const optionName = row.productOptionName3;
      const optionValues = extractOptionValues(row.productOptionDescription3);
      
      if (optionValues.length > 0) {
        // Simple array of values, not an object with values and display_type
        optionDefinitions[optionName] = optionValues;
      }
    }
    
    // Current timestamp for created_at and updated_at
    const now = new Date().toISOString();
    
    // Create a product with only the valid columns that exist in our database
    return {
      id: uuidv4(), // Generate a UUID for the product
      name: row.name || '',
      slug: slug,
      description: row.description || '',
      price: parsePrice(row.price) || 0,
      sale_price: row.discountValue ? parsePrice(row.price) - parsePrice(row.discountValue) : null,
      image: mainImageUrl || '',
      additional_images: additionalImageUrls.length > 0 ? JSON.stringify(additionalImageUrls) : '[]',
      category_id: null, // This would need to be mapped from collection if needed
      brand_id: null, // This would need to be mapped from brand if needed
      sku: row.sku || '',
      stock_quantity: parseInt(row.inventory || '0'),
      weight: parseFloat(row.weight || '0'),
      in_stock: parseInt(row.inventory || '0') > 0,
      is_featured: false,
      is_new: false,
      is_active: mainImageUrl ? true : false, // Set active if it has an image
      option_definitions: JSON.stringify(optionDefinitions),
      created_at: now,
      updated_at: now
    };
  });
  
  // Write output CSV
  const outputPath = path.join(CONFIG.outputDir, CONFIG.outputFileName);
  const outputCsv = Papa.unparse(compatibleProducts);
  fs.writeFileSync(outputPath, outputCsv);
  
  console.log('Transformation complete!');
  console.log(`- Output CSV: ${outputPath}`);
  console.log(`- ${compatibleProducts.length} products transformed`);
}

// Run the transformation
generateCompatibleCSV().catch(err => {
  console.error('Error during transformation:', err);
  process.exit(1);
});
