{"name": "bitsnbongs-multitenancy-test", "version": "1.0.0", "description": "Testing environment for BitsnBongs multi-tenant architecture", "main": "setup_test_environment.js", "scripts": {"setup": "node setup_test_environment.js", "test:isolation": "node -e \"require('./setup_test_environment').runIsolationTests()\"", "test:performance": "node -e \"require('./setup_test_environment').runPerformanceTests()\"", "create:testdata": "node -e \"require('./setup_test_environment').createTestData()\""}, "dependencies": {"@supabase/supabase-js": "^2.39.0"}, "devDependencies": {"dotenv": "^16.3.1"}}