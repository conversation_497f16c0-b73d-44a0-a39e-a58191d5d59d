-- Function to create a brand
CREATE OR REPLACE FUNCTION create_brand(
  p_name TEXT,
  p_slug TEXT,
  p_description TEXT,
  p_logo TEXT
) RETURNS JSONB AS $$
DECLARE
  new_brand JSONB;
BEGIN
  INSERT INTO brands (
    name,
    slug,
    description,
    logo
  ) VALUES (
    p_name,
    p_slug,
    p_description,
    p_logo
  )
  RETURNING to_jsonb(brands.*) INTO new_brand;
  
  RETURN new_brand;
END;
$$ LANGUAGE plpgsql;

-- Function to update a brand
CREATE OR REPLACE FUNCTION update_brand(
  p_id UUID,
  p_name TEXT,
  p_slug TEXT,
  p_description TEXT,
  p_logo TEXT
) RETURNS JSONB AS $$
DECLARE
  updated_brand JSONB;
BEGIN
  UPDATE brands
  SET 
    name = p_name,
    slug = p_slug,
    description = p_description,
    logo = p_logo,
    updated_at = NOW()
  WHERE id = p_id
  RETURNING to_jsonb(brands.*) INTO updated_brand;
  
  RETURN updated_brand;
END;
$$ LANGUAGE plpgsql;

-- Function to get a brand by ID
CREATE OR REPLACE FUNCTION get_brand_by_id(
  p_id UUID
) RETURNS JSONB AS $$
DECLARE
  brand_data JSONB;
BEGIN
  SELECT to_jsonb(brands.*) INTO brand_data
  FROM brands
  WHERE id = p_id;
  
  RETURN brand_data;
END;
$$ LANGUAGE plpgsql;

-- Function to get all brands
CREATE OR REPLACE FUNCTION get_all_brands()
RETURNS JSONB[] AS $$
DECLARE
  brands_data JSONB[];
BEGIN
  SELECT array_agg(to_jsonb(brands.*)) INTO brands_data
  FROM brands
  ORDER BY name;
  
  RETURN brands_data;
END;
$$ LANGUAGE plpgsql;

-- Function to delete a brand
CREATE OR REPLACE FUNCTION delete_brand(
  p_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM brands
  WHERE id = p_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;
