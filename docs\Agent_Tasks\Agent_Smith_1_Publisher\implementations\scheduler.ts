/**
 * Scheduler Implementation
 * 
 * This file implements the scheduling system for the Social Media Publishing System.
 * It allows users to schedule posts for later publishing across multiple platforms.
 */

import { 
  SocialMediaPublishingService,
  PostDraft,
  PostStatus,
  SchedulingService,
  ScheduleResult
} from './social_media_publisher';
import { DatabaseService } from './database_service';

/**
 * Interface for task scheduling
 */
interface TaskScheduler {
  scheduleTask(taskId: string, executionTime: Date, task: () => Promise<void>): Promise<boolean>;
  cancelTask(taskId: string): Promise<boolean>;
  rescheduleTask(taskId: string, newExecutionTime: Date): Promise<boolean>;
  getScheduledTasks(): Promise<Array<{ id: string, executionTime: Date }>>;
}

/**
 * Implementation of the SchedulingService interface
 */
export class SocialMediaScheduler implements SchedulingService {
  private publishingService: SocialMediaPublishingService;
  private databaseService: DatabaseService;
  private taskScheduler: TaskScheduler;
  private isRunning: boolean = false;
  private pollingInterval: number = 60000; // 1 minute
  private pollingIntervalId?: NodeJS.Timeout;
  
  /**
   * Create a new SocialMediaScheduler
   * @param publishingService The publishing service
   * @param databaseService The database service
   * @param taskScheduler The task scheduler
   */
  constructor(
    publishingService: SocialMediaPublishingService,
    databaseService: DatabaseService,
    taskScheduler: TaskScheduler
  ) {
    this.publishingService = publishingService;
    this.databaseService = databaseService;
    this.taskScheduler = taskScheduler;
  }
  
  /**
   * Schedule a post for later publishing
   * @param draft The post draft
   * @param scheduledTime The scheduled time
   * @returns Promise resolving to schedule result
   */
  async schedulePost(draft: PostDraft, scheduledTime: Date): Promise<ScheduleResult> {
    // Validate scheduled time
    if (scheduledTime <= new Date()) {
      return {
        success: false,
        message: 'Scheduled time must be in the future'
      };
    }
    
    try {
      // If draft doesn't have an ID, create it
      let draftId = draft.id;
      
      if (!draftId) {
        // Set status to scheduled
        const draftWithStatus: PostDraft = {
          ...draft,
          status: 'scheduled',
          scheduledTime
        };
        
        draftId = await this.databaseService.createPostDraft(draftWithStatus);
      } else {
        // Update existing draft
        await this.databaseService.updatePostDraft(draftId, {
          status: 'scheduled',
          scheduledTime
        });
      }
      
      // Schedule the task
      const scheduled = await this.taskScheduler.scheduleTask(
        draftId,
        scheduledTime,
        async () => {
          await this.publishScheduledPost(draftId);
        }
      );
      
      if (!scheduled) {
        return {
          success: false,
          message: 'Failed to schedule task'
        };
      }
      
      return {
        success: true,
        message: 'Post scheduled successfully',
        draftId
      };
    } catch (error) {
      console.error('Error scheduling post:', error);
      return {
        success: false,
        message: `Failed to schedule post: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
  
  /**
   * Cancel a scheduled post
   * @param draftId The draft ID
   * @returns Promise resolving to boolean indicating success
   */
  async cancelScheduledPost(draftId: string): Promise<boolean> {
    try {
      // Get the draft
      const draft = await this.databaseService.getPostDraft(draftId);
      
      if (!draft) {
        console.error(`Draft not found: ${draftId}`);
        return false;
      }
      
      // Check if draft is scheduled
      if (draft.status !== 'scheduled') {
        console.error(`Draft is not scheduled: ${draftId}`);
        return false;
      }
      
      // Cancel the task
      const cancelled = await this.taskScheduler.cancelTask(draftId);
      
      if (!cancelled) {
        console.error(`Failed to cancel task: ${draftId}`);
        return false;
      }
      
      // Update draft status
      await this.databaseService.updatePostDraft(draftId, {
        status: 'draft',
        scheduledTime: undefined
      });
      
      return true;
    } catch (error) {
      console.error('Error cancelling scheduled post:', error);
      return false;
    }
  }
  
  /**
   * Reschedule a post
   * @param draftId The draft ID
   * @param newScheduledTime The new scheduled time
   * @returns Promise resolving to boolean indicating success
   */
  async reschedulePost(draftId: string, newScheduledTime: Date): Promise<boolean> {
    // Validate scheduled time
    if (newScheduledTime <= new Date()) {
      console.error('Scheduled time must be in the future');
      return false;
    }
    
    try {
      // Get the draft
      const draft = await this.databaseService.getPostDraft(draftId);
      
      if (!draft) {
        console.error(`Draft not found: ${draftId}`);
        return false;
      }
      
      // Check if draft is scheduled
      if (draft.status !== 'scheduled') {
        console.error(`Draft is not scheduled: ${draftId}`);
        return false;
      }
      
      // Reschedule the task
      const rescheduled = await this.taskScheduler.rescheduleTask(draftId, newScheduledTime);
      
      if (!rescheduled) {
        console.error(`Failed to reschedule task: ${draftId}`);
        return false;
      }
      
      // Update draft scheduled time
      await this.databaseService.updatePostDraft(draftId, {
        scheduledTime: newScheduledTime
      });
      
      return true;
    } catch (error) {
      console.error('Error rescheduling post:', error);
      return false;
    }
  }
  
  /**
   * Get all scheduled posts
   * @returns Promise resolving to array of scheduled posts
   */
  async getScheduledPosts(): Promise<PostDraft[]> {
    try {
      return await this.databaseService.getPostDrafts({ status: 'scheduled' });
    } catch (error) {
      console.error('Error getting scheduled posts:', error);
      return [];
    }
  }
  
  /**
   * Start the scheduler
   * @returns Promise resolving to boolean indicating success
   */
  async start(): Promise<boolean> {
    if (this.isRunning) {
      return true;
    }
    
    try {
      // Check for any posts that should have been published while the system was down
      await this.checkMissedPosts();
      
      // Start polling for scheduled posts
      this.pollingIntervalId = setInterval(
        () => this.checkDueScheduledPosts(),
        this.pollingInterval
      );
      
      this.isRunning = true;
      return true;
    } catch (error) {
      console.error('Error starting scheduler:', error);
      return false;
    }
  }
  
  /**
   * Stop the scheduler
   * @returns Promise resolving to boolean indicating success
   */
  async stop(): Promise<boolean> {
    if (!this.isRunning) {
      return true;
    }
    
    try {
      // Stop polling
      if (this.pollingIntervalId) {
        clearInterval(this.pollingIntervalId);
        this.pollingIntervalId = undefined;
      }
      
      this.isRunning = false;
      return true;
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      return false;
    }
  }
  
  /**
   * Set the polling interval
   * @param intervalMs The interval in milliseconds
   */
  setPollingInterval(intervalMs: number): void {
    if (intervalMs < 1000) {
      console.warn('Polling interval too small, using 1000ms');
      intervalMs = 1000;
    }
    
    this.pollingInterval = intervalMs;
    
    // Restart polling if running
    if (this.isRunning && this.pollingIntervalId) {
      clearInterval(this.pollingIntervalId);
      this.pollingIntervalId = setInterval(
        () => this.checkDueScheduledPosts(),
        this.pollingInterval
      );
    }
  }
  
  /**
   * Check for posts that should have been published while the system was down
   */
  private async checkMissedPosts(): Promise<void> {
    try {
      const now = new Date();
      const scheduledPosts = await this.getScheduledPosts();
      
      for (const post of scheduledPosts) {
        if (post.scheduledTime && post.scheduledTime <= now) {
          console.log(`Publishing missed scheduled post: ${post.id}`);
          
          // Publish the post
          await this.publishScheduledPost(post.id);
        }
      }
    } catch (error) {
      console.error('Error checking missed posts:', error);
    }
  }
  
  /**
   * Check for scheduled posts that are due to be published
   */
  private async checkDueScheduledPosts(): Promise<void> {
    try {
      const now = new Date();
      const scheduledPosts = await this.getScheduledPosts();
      
      for (const post of scheduledPosts) {
        if (post.scheduledTime && post.scheduledTime <= now) {
          console.log(`Publishing due scheduled post: ${post.id}`);
          
          // Publish the post
          await this.publishScheduledPost(post.id);
        }
      }
    } catch (error) {
      console.error('Error checking due scheduled posts:', error);
    }
  }
  
  /**
   * Publish a scheduled post
   * @param draftId The draft ID
   */
  private async publishScheduledPost(draftId: string): Promise<void> {
    try {
      // Get the draft
      const draft = await this.databaseService.getPostDraft(draftId);
      
      if (!draft) {
        console.error(`Draft not found: ${draftId}`);
        return;
      }
      
      // Check if draft is still scheduled
      if (draft.status !== 'scheduled') {
        console.log(`Draft is no longer scheduled: ${draftId}`);
        return;
      }
      
      // Update status to publishing
      await this.databaseService.updatePostStatus(draftId, 'publishing');
      
      // Get the account
      const account = await this.databaseService.getAccount(draft.accountId);
      
      if (!account) {
        console.error(`Account not found: ${draft.accountId}`);
        await this.databaseService.updatePostStatus(draftId, 'failed');
        return;
      }
      
      // Publish the post
      try {
        const result = await this.publishingService.publishPost(draft);
        
        if (result.success) {
          // Update status to published
          await this.databaseService.updatePostDraft(draftId, {
            status: 'published',
            platformPostId: result.platformPostId
          });
          
          console.log(`Successfully published scheduled post: ${draftId}`);
        } else {
          // Update status to failed
          await this.databaseService.updatePostStatus(draftId, 'failed');
          console.error(`Failed to publish scheduled post: ${draftId}`, result.message);
        }
      } catch (error) {
        // Update status to failed
        await this.databaseService.updatePostStatus(draftId, 'failed');
        console.error(`Error publishing scheduled post: ${draftId}`, error);
      }
    } catch (error) {
      console.error('Error in publishScheduledPost:', error);
    }
  }
}

/**
 * Implementation of the TaskScheduler interface using in-memory scheduling
 */
export class InMemoryTaskScheduler implements TaskScheduler {
  private tasks: Map<string, {
    executionTime: Date;
    task: () => Promise<void>;
    timeoutId?: NodeJS.Timeout;
  }> = new Map();
  
  /**
   * Schedule a task
   * @param taskId The task ID
   * @param executionTime The execution time
   * @param task The task to execute
   * @returns Promise resolving to boolean indicating success
   */
  async scheduleTask(
    taskId: string,
    executionTime: Date,
    task: () => Promise<void>
  ): Promise<boolean> {
    try {
      // Cancel existing task if it exists
      this.cancelTask(taskId);
      
      // Calculate delay
      const now = new Date();
      const delay = Math.max(0, executionTime.getTime() - now.getTime());
      
      // Schedule the task
      const timeoutId = setTimeout(async () => {
        try {
          // Execute the task
          await task();
          
          // Remove the task
          this.tasks.delete(taskId);
        } catch (error) {
          console.error(`Error executing task ${taskId}:`, error);
        }
      }, delay);
      
      // Store the task
      this.tasks.set(taskId, {
        executionTime,
        task,
        timeoutId
      });
      
      return true;
    } catch (error) {
      console.error(`Error scheduling task ${taskId}:`, error);
      return false;
    }
  }
  
  /**
   * Cancel a task
   * @param taskId The task ID
   * @returns Promise resolving to boolean indicating success
   */
  async cancelTask(taskId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    
    if (!task) {
      return false;
    }
    
    // Clear the timeout
    if (task.timeoutId) {
      clearTimeout(task.timeoutId);
    }
    
    // Remove the task
    this.tasks.delete(taskId);
    
    return true;
  }
  
  /**
   * Reschedule a task
   * @param taskId The task ID
   * @param newExecutionTime The new execution time
   * @returns Promise resolving to boolean indicating success
   */
  async rescheduleTask(taskId: string, newExecutionTime: Date): Promise<boolean> {
    const task = this.tasks.get(taskId);
    
    if (!task) {
      return false;
    }
    
    // Reschedule the task
    return this.scheduleTask(taskId, newExecutionTime, task.task);
  }
  
  /**
   * Get all scheduled tasks
   * @returns Promise resolving to array of scheduled tasks
   */
  async getScheduledTasks(): Promise<Array<{ id: string, executionTime: Date }>> {
    const result: Array<{ id: string, executionTime: Date }> = [];
    
    for (const [id, task] of this.tasks.entries()) {
      result.push({
        id,
        executionTime: task.executionTime
      });
    }
    
    return result;
  }
}
