# Image Search Setup Guide

This guide explains how to set up the improved image search system that reduces API costs and improves reliability.

## Overview

The new system uses a multi-tier approach:
1. **Smart Caching** - Stores images locally to avoid repeated API calls
2. **Pixabay API** - Primary source (free tier: 5,000 requests/month)
3. **Unsplash API** - Secondary source (free tier: 5,000 requests/hour)
4. **Google Custom Search** - Fallback only (100 requests/day)
5. **Curated Images** - Final fallback

## Environment Variables

Add these to your `.env` file:

```env
# Image Search APIs (in order of preference)

# Pixabay (Primary - Free: 5,000 requests/month)
VITE_PIXABAY_API_KEY=your_pixabay_api_key_here

# Unsplash (Secondary - Free: 5,000 requests/hour)
VITE_UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# Google Custom Search (Fallback only - 100 requests/day)
VITE_GOOGLE_API_KEY=your_google_api_key_here
VITE_GOOGLE_CX_ID=your_custom_search_engine_id_here
```

## API Setup Instructions

### 1. Pixabay API (Recommended Primary)

1. Go to [Pixabay API](https://pixabay.com/api/docs/)
2. Create a free account
3. Get your API key from the dashboard
4. Free tier: 5,000 requests/month
5. Add to `.env`: `VITE_PIXABAY_API_KEY=your_key_here`

**Benefits:**
- High-quality stock photos
- No CORS issues when used server-side
- Generous free tier
- Good search relevance

### 2. Unsplash API (Secondary)

1. Go to [Unsplash Developers](https://unsplash.com/developers)
2. Create a developer account
3. Create a new application
4. Get your Access Key
5. Free tier: 5,000 requests/hour
6. Add to `.env`: `VITE_UNSPLASH_ACCESS_KEY=your_key_here`

**Benefits:**
- Professional photography
- Very generous rate limits
- Excellent image quality
- Good API documentation

### 3. Google Custom Search (Fallback Only)

Only set this up if you want Google as a fallback. **Not recommended as primary** due to low quotas.

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Custom Search JSON API
3. Create credentials (API Key)
4. Go to [Programmable Search Engine](https://programmablesearchengine.google.com/)
5. Create a new search engine
6. Configure it to search the entire web
7. Enable "Image search" in the setup
8. Get your Search Engine ID (CX)

Add to `.env`:
```env
VITE_GOOGLE_API_KEY=your_google_api_key
VITE_GOOGLE_CX_ID=your_search_engine_id
```

## Supabase Edge Function Setup

For server-side image search (bypasses CORS completely):

1. Set up environment variables in Supabase:
   ```bash
   supabase secrets set PIXABAY_API_KEY=your_pixabay_key
   supabase secrets set UNSPLASH_ACCESS_KEY=your_unsplash_key
   ```

2. Deploy the edge function:
   ```bash
   supabase functions deploy image-search
   ```

## How the New System Works

### 1. Cache First
- Checks local storage for previously fetched images
- Looks for similar topics in cache
- Returns cached images instantly (no API calls)

### 2. Proxy APIs
- Uses Pixabay API (5,000 free requests/month)
- Falls back to Unsplash API (5,000 requests/hour)
- Caches all results for future use

### 3. Google Fallback
- Only used if other methods fail
- Limited to single search (not multiple variations)
- Preserves your 100 daily requests

### 4. Curated Images
- High-quality fallback images for each category
- Always available, no API calls needed

## Benefits of New System

1. **Cost Reduction**: 90%+ reduction in Google API usage
2. **Better Reliability**: Multiple fallback sources
3. **Improved Performance**: Caching reduces load times
4. **Higher Quality**: Pixabay and Unsplash have better images
5. **No CORS Issues**: Server-side processing available

## Monitoring Usage

The system logs all API calls and cache hits. Monitor your console to see:
- Cache hit rates
- Which APIs are being used
- API call frequency

## Troubleshooting

### CORS Issues
If you still get CORS errors:
1. Ensure the Supabase Edge Function is deployed
2. Check that API keys are set in Supabase secrets
3. Verify the edge function is being called first

### No Images Found
1. Check that at least one API key is configured
2. Verify API keys are valid and have remaining quota
3. Check browser console for specific error messages

### Cache Issues
Clear the image cache if needed:
```javascript
// In browser console
localStorage.removeItem('bitsnbongs_image_cache');
```

## Migration from Old System

The new system is backward compatible. Simply:
1. Add the new environment variables
2. Deploy the updated code
3. The system will automatically use the new methods

Your existing Google API setup will continue to work as a fallback.
