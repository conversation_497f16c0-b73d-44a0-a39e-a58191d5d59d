import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Product } from '@/types/database';

// This hook manages product form state persistence across tab navigation
export function useProductFormState() {
  const queryClient = useQueryClient();
  const CACHE_KEY = 'product-form-state';
  
  // Initialize state from cache or defaults
  const [isAddingOrEditing, setIsAddingOrEditing] = useState<boolean>(() => {
    const cached = queryClient.getQueryData([CACHE_KEY, 'isAddingOrEditing']);
    return cached === true;
  });
  
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(() => {
    const cached = queryClient.getQueryData([CACHE_KEY, 'selectedProduct']);
    return cached as Product || null;
  });

  // Update cache when state changes
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY, 'isAddingOrEditing'], isAddingOrEditing);
  }, [isAddingOrEditing, queryClient]);

  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY, 'selectedProduct'], selectedProduct);
  }, [selectedProduct, queryClient]);

  // Handler functions
  const handleEdit = (product: Product) => {
    setSelectedProduct(product);
    setIsAddingOrEditing(true);
  };

  const handleAddNewProduct = () => {
    setSelectedProduct(null);
    setIsAddingOrEditing(true);
  };

  const handleCloseForm = () => {
    setIsAddingOrEditing(false);
    setSelectedProduct(null);
  };

  return {
    isAddingOrEditing,
    selectedProduct,
    handleEdit,
    handleAddNewProduct,
    handleCloseForm
  };
}
