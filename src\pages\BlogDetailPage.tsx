import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Blog, BlogImage, BlogComment } from '@/types/database';
import { 
  Calendar, 
  Clock, 
  Tag,
  Share2,
  Edit,
  ArrowLeft,
  MessageSquare,
  Eye,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Copy,
  ThumbsUp,
  Bookmark
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/auth.basic';

const BlogDetailPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const { isAdmin, profile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [copied, setCopied] = useState(false);
  
  // Fetch blog post by slug
  const { data: blog, isLoading, error } = useQuery({
    queryKey: ['blog', slug],
    queryFn: async () => {
      try {
        // Get the blog post
        const { data, error } = await supabase
          .from('blogs')
          .select('*')
          .eq('slug', slug)
          .single();
        
        if (error) throw error;
        
        // Increment view count
        if (data) {
          incrementViewCount(data.id);
          
          // Get author information if available
          if (data.author_id) {
            const { data: authorData } = await supabase
              .from('profiles')
              .select('id, first_name, last_name')
              .eq('id', data.author_id)
              .single();
            
            return {
              ...data,
              author: authorData || null
            };
          }
        }
        
        return data as Blog;
      } catch (error) {
        console.error('Error fetching blog post:', error);
        throw error;
      }
    },
    enabled: !!slug
  });
  
  // Fetch related blog posts
  const { data: relatedBlogs } = useQuery({
    queryKey: ['related_blogs', blog?.category, blog?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blogs')
        .select('id, title, slug, featured_image, published_at')
        .eq('category', blog?.category)
        .eq('is_published', true)
        .neq('id', blog?.id)
        .order('published_at', { ascending: false })
        .limit(3);
      
      if (error) throw error;
      return data as Blog[];
    },
    enabled: !!blog?.category && !!blog?.id
  });
  
  // Fetch blog images
  const { data: blogImages } = useQuery({
    queryKey: ['blog_images', blog?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_images')
        .select('*')
        .eq('blog_id', blog?.id)
        .order('display_order', { ascending: true });
      
      if (error) throw error;
      return data as BlogImage[];
    },
    enabled: !!blog?.id
  });
  
  // Function to increment view count
  const incrementViewCount = async (blogId: string) => {
    try {
      await supabase.rpc('increment_blog_view', { blog_id: blogId });
    } catch (error) {
      console.error('Error incrementing view count:', error);
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  // Get author name
  const getAuthorName = (blog: any) => {
    if (blog?.author?.first_name && blog?.author?.last_name) {
      return `${blog.author.first_name} ${blog.author.last_name}`;
    }
    return 'Staff Writer';
  };
  
  // Copy URL to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(window.location.href);
    setCopied(true);
    toast({
      title: "Link copied!",
      description: "The blog post URL has been copied to your clipboard."
    });
    
    setTimeout(() => setCopied(false), 3000);
  };
  
  // Format content with proper paragraphs
  const formatContent = (content: string) => {
    return content.split('\n\n').map((paragraph, index) => (
      <p key={index} className="mb-4 last:mb-0">
        {paragraph}
      </p>
    ));
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <Skeleton className="h-8 w-3/4 mb-4" />
          <div className="flex items-center gap-4 mb-8">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-64 w-full mb-8" />
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </div>
    );
  }
  
  if (error || !blog) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Blog Post Not Found</h1>
          <p className="text-gray-600 mb-6">The blog post you're looking for doesn't exist or has been removed.</p>
          <Button asChild>
            <Link to="/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
          </Button>
        </div>
      </div>
    );
  }
  
  // Check if blog is published or user is admin
  if (!blog.is_published && !isAdmin) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-yellow-500 mb-4">Blog Post Not Available</h1>
          <p className="text-gray-600 mb-6">This blog post is currently not published.</p>
          <Button asChild>
            <Link to="/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Banner */}
      <div className="relative bg-gradient-to-r from-primary/90 to-primary h-64 md:h-80 overflow-hidden">
        {blog.featured_image ? (
          <img 
            src={blog.featured_image} 
            alt={blog.title} 
            className="absolute inset-0 w-full h-full object-cover opacity-30"
          />
        ) : (
          <div className="absolute inset-0 bg-pattern opacity-10"></div>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
        <div className="container mx-auto px-4 h-full flex items-end pb-8 relative z-10">
          <div className="max-w-4xl mx-auto w-full">
            {!blog.is_published && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300 mb-4">
                Draft
              </Badge>
            )}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">{blog.title}</h1>
            <div className="flex flex-wrap items-center gap-4 text-white/80">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(blog.published_at || blog.created_at)}</span>
              </div>
              
              {blog.reading_time && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{blog.reading_time} min read</span>
                </div>
              )}
              
              {blog.category && (
                <div className="flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  <span>{blog.category}</span>
                </div>
              )}
              
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>{blog.view_count} views</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Admin Controls */}
          {isAdmin && (
            <div className="mb-8 flex justify-end">
              <Button 
                variant="outline" 
                className="mr-2"
                onClick={() => navigate(`/admin/blogs/${blog.id}`)}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Blog Post
              </Button>
            </div>
          )}
          
          {/* Author and Share */}
          <div className="flex flex-wrap justify-between items-center mb-8 pb-4 border-b">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                {blog.author?.first_name?.[0] || 'S'}
              </div>
              <div>
                <p className="font-medium">{getAuthorName(blog)}</p>
                <p className="text-sm text-gray-500">Author</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-2">Share this article</p>
              <div className="flex gap-2">
                <Button variant="outline" size="icon" className="rounded-full w-8 h-8" onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank')}>
                  <Facebook className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="rounded-full w-8 h-8" onClick={() => window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(blog.title)}`, '_blank')}>
                  <Twitter className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="rounded-full w-8 h-8" onClick={() => window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`, '_blank')}>
                  <Linkedin className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="rounded-full w-8 h-8" onClick={copyToClipboard}>
                  <Copy className={`h-4 w-4 ${copied ? 'text-green-500' : ''}`} />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Blog Content */}
          <div className="prose prose-lg max-w-none mb-12">
            {blog.summary && (
              <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-primary mb-8">
                <p className="italic text-gray-700">{blog.summary}</p>
              </div>
            )}
            
            {formatContent(blog.content)}
            
            {/* Display additional images */}
            {blogImages && blogImages.length > 0 && (
              <div className="my-8 grid grid-cols-1 md:grid-cols-2 gap-4">
                {blogImages.map(image => (
                  <figure key={image.id} className="relative">
                    <img 
                      src={image.image_url} 
                      alt={image.alt_text || blog.title} 
                      className="rounded-lg w-full h-auto"
                    />
                    {image.caption && (
                      <figcaption className="text-sm text-gray-500 mt-2">{image.caption}</figcaption>
                    )}
                  </figure>
                ))}
              </div>
            )}
            
            {/* Tags */}
            {blog.tags && blog.tags.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {blog.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="px-3 py-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* Related Articles */}
          {relatedBlogs && relatedBlogs.length > 0 && (
            <div className="mt-12">
              <h2 className="text-2xl font-bold mb-6">Related Articles</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedBlogs.map(relatedBlog => (
                  <Card key={relatedBlog.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <div className="h-40 overflow-hidden">
                      {relatedBlog.featured_image ? (
                        <img 
                          src={relatedBlog.featured_image} 
                          alt={relatedBlog.title} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40" />
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-medium line-clamp-2 mb-2">
                        <Link to={`/blog/${relatedBlog.slug}`} className="hover:text-primary transition-colors">
                          {relatedBlog.title}
                        </Link>
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatDate(relatedBlog.published_at || relatedBlog.created_at)}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
          
          {/* Back to Blog */}
          <div className="mt-12 flex justify-center">
            <Button asChild variant="outline">
              <Link to="/blog">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Blog
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogDetailPage;

