import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { generateSKU, formatSKU } from '@/utils/skuGenerator';
import { Tag, RefreshCw } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface SKUGeneratorProps {
  productName: string;
  categoryName?: string | null;
  brandName?: string | null;
  currentSKU: string;
  onSKUChange: (sku: string) => void;
}

export function SKUGenerator({
  productName,
  categoryName,
  brandName,
  currentSKU,
  onSKUChange
}: SKUGeneratorProps) {
  const [sku, setSku] = useState<string>(currentSKU || '');
  
  // Generate a new SKU based on product details
  const handleGenerateSKU = () => {
    if (!productName) {
      toast({
        title: "Missing Information",
        description: "Please enter a product name first",
        variant: "destructive",
      });
      return;
    }
    
    const newSKU = generateSKU(productName, categoryName, brandName);
    setSku(newSKU);
    onSKUChange(newSKU);
    
    toast({
      title: "SKU Generated",
      description: `New SKU: ${newSKU}`,
    });
  };
  
  // Handle manual SKU input
  const handleSKUChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSku(value);
    onSKUChange(value);
  };
  
  // Format the SKU to ensure consistency
  const handleSKUBlur = () => {
    if (sku) {
      const formattedSKU = formatSKU(sku);
      setSku(formattedSKU);
      onSKUChange(formattedSKU);
    }
  };
  
  return (
    <div className="space-y-2">
      <Label htmlFor="sku">SKU (Stock Keeping Unit)</Label>
      <div className="flex gap-2">
        <Input
          id="sku"
          value={sku}
          onChange={handleSKUChange}
          onBlur={handleSKUBlur}
          placeholder="Enter product SKU"
          className="flex-1"
        />
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleGenerateSKU}
          title="Generate SKU"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Generate
        </Button>
      </div>
      <p className="text-sm text-muted-foreground">
        A unique identifier for inventory management. Required for EPOS integration.
      </p>
    </div>
  );
}