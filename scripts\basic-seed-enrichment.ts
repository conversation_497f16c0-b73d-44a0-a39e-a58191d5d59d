#!/usr/bin/env tsx
/**
 * Basic seed enrichment script
 * This script provides basic enrichment for seed products using pattern matching
 * Use this for bulk processing, save Super Agent credits for premium products
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Basic enrichment rules based on product names
function extractSeedAttributes(productName: string) {
  const name = productName.toLowerCase();
  
  // Determine seed type
  let seedType = 'Regular';
  if (name.includes('auto') || name.includes('autoflower')) {
    seedType = 'Autoflower';
  } else if (name.includes('feminised') || name.includes('feminized') || name.includes('female')) {
    seedType = 'Feminised';
  }
  
  // Estimate flowering time based on type
  let floweringTime = '8-10 weeks';
  if (seedType === 'Autoflower') {
    floweringTime = '8-10 weeks';
  } else {
    floweringTime = '9-11 weeks';
  }
  
  // Basic effect classification based on strain names
  let effect = 'Hybrid';
  const sativaStrains = ['haze', 'jack', 'sour', 'lemon', 'grapefruit', 'green crack', 'amnesia'];
  const indicaStrains = ['kush', 'purple', 'blackberry', 'blueberry', 'northern lights', 'rhino'];
  
  if (sativaStrains.some(strain => name.includes(strain))) {
    effect = 'Sativa dominant';
  } else if (indicaStrains.some(strain => name.includes(strain))) {
    effect = 'Indica dominant';
  }
  
  // Basic THC estimation
  let thcLevel = '15-20%';
  const highThcStrains = ['gorilla', 'cookies', 'punch', 'glue'];
  const lowThcStrains = ['cbd', 'hemp'];
  
  if (highThcStrains.some(strain => name.includes(strain))) {
    thcLevel = '20-25%';
  } else if (lowThcStrains.some(strain => name.includes(strain))) {
    thcLevel = '5-15%';
  }
  
  return {
    seedType,
    floweringTime,
    effect,
    thcLevel,
    cbdLevel: '<1%'
  };
}

async function basicEnrichment() {
  console.log('🌱 Starting basic seed enrichment...\n');
  
  try {
    // Get seed products that don't have enriched data yet
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id, 
        name,
        seed_product_attributes!left(id)
      `)
      .is('seed_product_attributes.id', null);
    
    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }
    
    // Filter to only seed products
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('seed') || name.includes('auto') || 
             name.includes('feminised') || name.includes('feminized') ||
             name.includes('female') || name.includes('strain');
    }) || [];
    
    console.log(`📄 Found ${seedProducts.length} seed products without enrichment data\n`);
    
    let processed = 0;
    let errors = 0;
    
    for (const product of seedProducts) {
      try {
        console.log(`🔍 Processing: ${product.name}`);
        
        const attributes = extractSeedAttributes(product.name);
        
        const { error: insertError } = await supabase
          .from('seed_product_attributes')
          .insert({
            product_id: product.id,
            seed_type: attributes.seedType,
            flowering_time: attributes.floweringTime,
            thc_level: attributes.thcLevel,
            cbd_level: attributes.cbdLevel,
            effect: attributes.effect,
            is_manually_verified: false,
            confidence_score: 60, // Lower confidence for auto-generated
            data_source: 'Basic pattern matching'
          });
        
        if (insertError) {
          console.log(`   ❌ Error: ${insertError.message}`);
          errors++;
        } else {
          console.log(`   ✅ Enriched: ${attributes.seedType}, ${attributes.effect}, ${attributes.thcLevel}`);
          processed++;
        }
        
      } catch (err) {
        console.error(`   ❌ Exception: ${err}`);
        errors++;
      }
    }
    
    console.log('\n📊 Basic Enrichment Summary:');
    console.log(`   ✅ Successfully processed: ${processed}`);
    console.log(`   ❌ Errors: ${errors}`);
    console.log(`   📄 Total attempted: ${seedProducts.length}`);
    
    if (processed > 0) {
      console.log('\n💡 Next steps:');
      console.log('   1. Review the basic enrichment results');
      console.log('   2. Identify premium products for Super Agent enrichment');
      console.log('   3. Use Super Agent credits on high-value products only');
    }
    
  } catch (err) {
    console.error('❌ Basic enrichment failed:', err);
  }
}

// Run the basic enrichment
basicEnrichment().catch(console.error);
