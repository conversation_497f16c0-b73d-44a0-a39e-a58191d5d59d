// <PERSON><PERSON>t to create a debug stored procedure
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createDebugProcedure() {
  console.log('Creating debug stored procedure...');
  
  try {
    // Create a stored procedure to debug shipping methods
    const { error } = await supabase.rpc('create_debug_procedure', {
      sql: `
        CREATE OR REPLACE FUNCTION get_shipping_methods_debug(zone_id_param UUID)
        RETURNS TABLE (
          id UUID,
          name TEXT,
          is_active BOOLEAN,
          active_type TEXT
        ) 
        LANGUAGE plpgsql
        AS $$
        BEGIN
          RETURN QUERY
          SELECT 
            shipping_methods.id,
            shipping_methods.name,
            shipping_methods.is_active,
            pg_typeof(shipping_methods.is_active)::text as active_type
          FROM 
            shipping_methods
          WHERE 
            shipping_methods.zone_id = zone_id_param;
        END;
        $$;
      `
    });
    
    if (error) {
      console.error('Error creating stored procedure:', error);
      
      // Try direct SQL execution
      const { error: sqlError } = await supabase.from('_temp_debug').rpc('create_debug_procedure');
      
      if (sqlError) {
        console.error('Error with direct SQL execution:', sqlError);
      }
    } else {
      console.log('✅ Debug stored procedure created successfully');
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
createDebugProcedure()
  .catch(err => {
    console.error('Error running script:', err);
  });
