// server.js - Express server with image proxy for handling CORS issues
import express from 'express';
import cors from 'cors';
import axios from 'axios';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory name (ES modules don't have __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Serve static files from the 'public' directory
app.use(express.static(path.join(__dirname, 'public')));

// List of allowed domains for security
const ALLOWED_DOMAINS = [
  'unsplash.com', 'images.unsplash.com',
  'pexels.com', 'images.pexels.com',
  'pixabay.com', 'cdn.pixabay.com',
  'freepik.com', 'img.freepik.com',
  'shutterstock.com', 'image.shutterstock.com',
  'adobe.com', 'stock.adobe.com',
  // Add more trusted domains as needed
];

// Image proxy endpoint
app.get('/api/proxy', async (req, res) => {
  const { url } = req.query;
  
  console.log('Received proxy request for URL:', url);
  
  // Validate URL parameter
  if (!url || typeof url !== 'string') {
    console.error('Invalid URL parameter:', url);
    return res.status(400).json({ error: 'Image URL is required' });
  }
  
  try {
    // Decode and validate the URL
    const imageUrl = decodeURIComponent(url);
    console.log('Decoded URL:', imageUrl);
    
    const urlObj = new URL(imageUrl);
    console.log('URL hostname:', urlObj.hostname);
    
    // Security check: only allow specific domains
    const isDomainAllowed = ALLOWED_DOMAINS.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
    );
    
    if (!isDomainAllowed) {
      console.warn(`Blocked request to non-allowed domain: ${urlObj.hostname}`);
      return res.status(403).json({ error: 'Domain not allowed for security reasons' });
    }
    
    console.log('Domain allowed, fetching image from:', imageUrl);
    
    // Fetch the image
    const response = await axios({
      method: 'get',
      url: imageUrl,
      responseType: 'arraybuffer',
      timeout: 10000, // Increased timeout to 10 seconds
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Referer': process.env.APP_URL || 'https://budsnbotboutique.com',
        'Origin': process.env.APP_URL || 'https://budsnbotboutique.com'
      }
    });
    
    console.log('Image fetched successfully, content type:', response.headers['content-type']);
    
    // Set appropriate headers
    res.setHeader('Content-Type', response.headers['content-type'] || 'image/jpeg');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
    res.setHeader('Access-Control-Allow-Origin', '*'); // Ensure CORS headers are set
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    
    // Return the image data
    return res.send(Buffer.from(response.data));
    
  } catch (error) {
    console.error('Image proxy error:', error.message);
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    }
    
    // Return a more helpful error message
    return res.status(500).json({ 
      error: 'Failed to proxy image', 
      message: error.message,
      url: req.query.url
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Image proxy server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Image proxy server running on port ${PORT}`);
});