// Script to update product image references in the database
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import Papa<PERSON>arse from 'papaparse';

// Extract parse function from PapaParse
const { parse } = PapaParse;

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to CSV file
const csvFilePath = path.join(__dirname, 'docs', 'catalog_products.csv');

// Path to local images
const localImagesPath = path.join(__dirname, 'public', 'images', 'products', 'wix-imports');

// Function to sanitize filenames by removing ~mv2
function sanitizeFilename(filename) {
  if (!filename) return '';
  return filename.replace(/~mv2/g, '');
}

async function updateProductImagesInDB() {
  console.log('Starting product image update in database...');
  
  // Read CSV file
  const csvFile = fs.readFileSync(csvFilePath, 'utf8');
  
  // Parse CSV
  const { data } = parse(csvFile, {
    header: true,
    skipEmptyLines: true
  });
  
  console.log(`Found ${data.length} products in CSV`);
  
  // Process each product
  let updated = 0;
  let failed = 0;
  
  for (const product of data) {
    try {
      if (!product.name) {
        continue;
      }
      
      // Generate slug from name
      const slug = product.name.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
      
      // Find the product in the database by slug
      const { data: existingProduct, error: findError } = await supabase
        .from('products')
        .select('*')
        .eq('slug', slug)
        .single();
      
      if (findError) {
        console.log(`Product not found: ${product.name} (${slug})`);
        failed++;
        continue;
      }
      
      // Process images
      let imageUrl = '';
      let additionalImageUrls = [];
      
      if (product.productImageUrl) {
        // Handle multiple images (semicolon-separated)
        const images = product.productImageUrl.split(';');
        
        if (images.length > 0) {
          // Process main image
          const firstImage = images[0].trim();
          
          // Check if this is a Wix image filename
          if (firstImage) {
            // Get the base filename without extension
            const baseFilename = firstImage.split('.')[0];
            
            // Create the sanitized filename
            const sanitizedFilename = sanitizeFilename(baseFilename) + '.webp';
            
            // Check if the sanitized file exists locally
            const localPath = path.join(localImagesPath, sanitizedFilename);
            
            if (fs.existsSync(localPath)) {
              // Generate the public URL for Supabase storage
              const publicUrl = `${supabaseUrl}/storage/v1/object/public/product-images/${sanitizedFilename}`;
              imageUrl = publicUrl;
            }
          }
          
          // Process additional images
          if (images.length > 1) {
            for (const img of images.slice(1)) {
              if (img.trim()) {
                const baseFilename = img.trim().split('.')[0];
                const sanitizedFilename = sanitizeFilename(baseFilename) + '.webp';
                
                // Check if the sanitized file exists locally
                const localPath = path.join(localImagesPath, sanitizedFilename);
                
                if (fs.existsSync(localPath)) {
                  // Generate the public URL for Supabase storage
                  const publicUrl = `${supabaseUrl}/storage/v1/object/public/product-images/${sanitizedFilename}`;
                  additionalImageUrls.push(publicUrl);
                }
              }
            }
          }
        }
      }
      
      // Only update if we found images
      if (imageUrl || additionalImageUrls.length > 0) {
        const updateData = {};
        
        if (imageUrl) {
          updateData.image = imageUrl;
        }
        
        if (additionalImageUrls.length > 0) {
          updateData.additional_images = additionalImageUrls;
        }
        
        // Update the product in the database
        const { error: updateError } = await supabase
          .from('products')
          .update(updateData)
          .eq('id', existingProduct.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          failed++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      }
    } catch (error) {
      console.error(`Error processing product ${product.name}:`, error);
      failed++;
    }
    
    // Log progress every 10 products
    if ((updated + failed) % 10 === 0) {
      console.log(`Progress: ${updated + failed}/${data.length} (${updated} updated, ${failed} failed)`);
    }
  }
  
  console.log(`Finished! ${updated} products updated, ${failed} failed`);
}

// Run the script
updateProductImagesInDB().catch(console.error);
