// Script to verify all shipping methods and their active status
import { createClient } from '@supabase/supabase-js';

// Use the same credentials as the app
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyShippingMethods() {
  console.log('🔍 Verifying all shipping methods in the database...');
  
  try {
    // Get all shipping methods
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*,zone:shipping_zones(*)');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} total shipping methods`);
    
    // Display all methods with their properties
    console.log('\nAll shipping methods:');
    methods.forEach(method => {
      console.log(`\n-- ${method.name} (${method.id}) --`);
      console.log(`Zone: ${method.zone?.name || 'Unknown'} (${method.zone_id})`);
      console.log(`Description: ${method.description}`);
      console.log(`Price: £${method.price}`);
      console.log(`Active: ${method.is_active}`);
      console.log(`Type of is_active: ${typeof method.is_active}`);
      console.log(`Sort Order: ${method.sort_order}`);
      console.log(`Delivery Estimate: ${method.estimated_days_min}-${method.estimated_days_max} days`);
      console.log(`Last Updated: ${method.updated_at}`);
    });
    
    // Also query the database using exactly the same query as in getCheckoutShippingMethods
    console.log('\n🔍 Simulating the checkout shipping query...');
    
    // Find active zones for UK
    const { data: zonesData, error: zonesError } = await supabase
      .from('shipping_zones')
      .select('*')
      .eq('is_active', true);
      
    if (zonesError) {
      console.error('Error fetching shipping zones:', zonesError);
      return;
    }
    
    const zones = zonesData || [];
    const zone = zones.find(z => z.countries.includes('United Kingdom'));
    
    if (!zone) {
      console.log('No shipping zone found for United Kingdom');
      return;
    }
    
    console.log(`Found shipping zone: ${zone.name} (${zone.id})`);
    
    // Get active shipping methods for this zone
    const { data: methodsData, error: methodsError } = await supabase
      .from('shipping_methods')
      .select('*,zone:shipping_zones(*)')
      .eq('zone_id', zone.id)
      .eq('is_active', true)
      .order('sort_order');
      
    if (methodsError) {
      console.error('Error fetching checkout shipping methods:', methodsError);
      return;
    }
    
    console.log(`Found ${methodsData?.length || 0} active methods for checkout`);
    
    methodsData?.forEach(method => {
      console.log(`- ${method.name} (active: ${method.is_active})`);
    });
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
verifyShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
  }); 