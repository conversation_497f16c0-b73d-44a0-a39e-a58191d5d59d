import React, { useMemo } from 'react';

interface VisualOptionCardProps {
  optionName: string;
  value: string;
  isSelected: boolean;
  priceAdjustment: number | null;
  isPercentage?: boolean;
  imageUrl?: string;
  disabled?: boolean;
  onClick: () => void;
}

export const VisualOptionCard: React.FC<VisualOptionCardProps> = ({
  optionName,
  value,
  isSelected,
  priceAdjustment,
  isPercentage = false,
  imageUrl,
  disabled = false,
  onClick,
}) => {
  // Format price adjustment text
  const priceAdjustmentText = priceAdjustment !== null && priceAdjustment !== 0
    ? priceAdjustment > 0
      ? isPercentage
        ? `+${priceAdjustment.toFixed(2)}%`
        : `+£${priceAdjustment.toFixed(2)}`
      : isPercentage
        ? `-${Math.abs(priceAdjustment).toFixed(2)}%`
        : `-£${Math.abs(priceAdjustment).toFixed(2)}`
    : '';

  // Debug price adjustment
  console.log(`VisualOptionCard for ${optionName}=${value}: priceAdjustment=${priceAdjustment}, text=${priceAdjustmentText}`);

  // Generate a fallback image URL based on the option value
  const fallbackImageUrl = useMemo(() => {
    const normalizedValue = value.toLowerCase();

    // Common colors that we have SVGs for
    const commonColors = ['red', 'blue', 'green', 'yellow', 'purple', 'black', 'white'];

    // Common sizes that we have SVGs for
    const commonSizes = ['small', 'medium', 'large', 'xlarge'];

    if (commonColors.includes(normalizedValue)) {
      return `/images/options/${normalizedValue}.svg`;
    }

    if (commonSizes.includes(normalizedValue)) {
      return `/images/options/${normalizedValue}.svg`;
    }

    // Default fallback for other values
    return null;
  }, [value]);

  // Use provided imageUrl or fallback
  const displayImageUrl = imageUrl || fallbackImageUrl;

  // Handle click with event propagation stopped
  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (!disabled) {
      onClick();
    }
  };

  return (
    <button
      type="button"
      disabled={disabled}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '12px',
        borderRadius: '8px',
        width: '100%',
        position: 'relative',
        zIndex: 100,
        backgroundColor: isSelected ? '#4CAF50' : disabled ? 'rgba(240, 240, 240, 0.3)' : 'rgba(255, 255, 255, 0.7)',
        color: isSelected ? 'white' : disabled ? '#999' : '#333',
        border: isSelected ? 'none' : disabled ? '1px solid rgba(220, 220, 220, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',
        boxShadow: isSelected ? '0 2px 8px rgba(76, 175, 80, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        transition: 'all 0.2s ease',
        fontWeight: '500'
      }}
      onClick={handleClick}
    >
      {/* Option image if available */}
      {displayImageUrl && (
        <div className="w-14 h-14 mr-3 bg-white/30 rounded-lg overflow-hidden flex-shrink-0 shadow-inner">
          <img
            src={displayImageUrl}
            alt={value}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <div className="flex-1 text-left">
        <div className={`font-medium ${isSelected ? 'text-white' : 'text-gray-800'}`}>
          {value}
        </div>
        {priceAdjustmentText && (
          <div className={`text-sm mt-0.5 ${isSelected ? 'text-white/90' : 'text-gray-600'}`}>
            {priceAdjustmentText}
          </div>
        )}
      </div>
    </button>
  );
};
