// fix-santa-cruz-products.mjs
// Direct fix for Santa Cruz Shredder products

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Define the products to fix
const productsToFix = [
  {
    id: "27ba2f04-4991-42f5-ba92-afed0e236542",
    name: "Santa Cruz Shredder -4pc large",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Gold;Green;Grey;Purple;Rasta",
      option_description1: "Choose your preferred color"
    }
  },
  {
    id: "318700b2-c7c8-47d3-b064-7dc4ac532535",
    name: "Santa Cruz Shredder 2pc Large Grinder",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Green;Purple",
      option_description1: "Choose your preferred color"
    }
  },
  {
    id: "326e2566-98a8-451c-8448-b8d14ced305d",
    name: "Santa Cruz Shredder 3pc Medium Grinder",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Purple;Rasta;Green",
      option_description1: "Choose your preferred color"
    }
  },
  {
    id: "3bf42578-1230-4e18-b094-ac8981ddbd92",
    name: "(Cookies) 4pc Medium By Santa Cruz Shredder",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Blue;Red",
      option_description1: "Choose your preferred color"
    }
  },
  {
    id: "29f24d38-5047-41d8-b15d-4b0613113b40",
    name: "Santa Cruz Shredder -4pc Medium",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Purple;Green;Blue;Grey;Gold;Rasta",
      option_description1: "Choose your preferred color"
    }
  },
  {
    id: "6ed1f9fe-1783-4222-bc34-a65f48bc3d41",
    name: "Santa Cruz Shredder 3 pcs Large",
    updates: {
      option_name1: "Colour",
      option_type1: "Black;Rasta;Green;Grey;Red;Purple",
      option_description1: "Choose your preferred color"
    }
  }
];

async function fixSantaCruzProducts() {
  console.log('Starting to fix Santa Cruz products...');
  
  let updatedCount = 0;
  let errorCount = 0;
  
  // Update each product
  for (const product of productsToFix) {
    console.log(`Updating product: ${product.name} (ID: ${product.id})`);
    
    const { error } = await supabase
      .from('products')
      .update(product.updates)
      .eq('id', product.id);
    
    if (error) {
      console.error(`Error updating product ${product.name}:`, error);
      errorCount++;
    } else {
      console.log(`Successfully updated: ${product.name}`);
      updatedCount++;
    }
  }
  
  console.log('\nUpdate complete!');
  console.log(`Updated ${updatedCount} products successfully.`);
  console.log(`Failed to update ${errorCount} products.`);
}

// Run the script
fixSantaCruzProducts().catch(error => {
  console.error('Error running script:', error);
});
