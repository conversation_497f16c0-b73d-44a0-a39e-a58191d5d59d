-- Truncate products and variants tables
-- This script will remove all data from the products and product_variants tables
-- Use with caution as this operation cannot be undone

-- First truncate product_variants table (child table with foreign key constraints)
TRUNCATE TABLE product_variants CASCADE;

-- Then truncate products table
TRUNCATE TABLE products CASCADE;

-- Output confirmation
SELECT 'Products and product_variants tables have been truncated.' AS result;
