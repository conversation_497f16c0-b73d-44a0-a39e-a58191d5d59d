#!/usr/bin/env node

// Import required modules
const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key length:', supabaseKey.length);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Check if a file path is provided
if (process.argv.length < 3) {
  console.error('Please provide a CSV file path');
  process.exit(1);
}

// Get the file path from command line arguments
const filePath = process.argv[2];

// First, check if the options column exists in the product_variants table
async function checkOptionsColumn() {
  try {
    // Try to get the schema of the product_variants table
    const { data, error } = await supabase.rpc('get_schema_info', {
      table_name: 'product_variants'
    });

    if (error) {
      console.error('Error getting schema info:', error);
      return false;
    }

    // Check if the options column exists
    const hasOptionsColumn = data.some(column => column.column_name === 'options');
    
    if (!hasOptionsColumn) {
      console.log('The options column does not exist in the product_variants table. Adding it now...');
      
      // Add the options column
      const { error: alterError } = await supabase.rpc('execute_sql', {
        sql: 'ALTER TABLE public.product_variants ADD COLUMN IF NOT EXISTS options JSONB;'
      });
      
      if (alterError) {
        console.error('Error adding options column:', alterError);
        return false;
      }
      
      console.log('Added options column to product_variants table.');
    } else {
      console.log('The options column already exists in the product_variants table.');
    }
    
    return true;
  } catch (error) {
    console.error('Error checking options column:', error);
    
    // Try a direct approach if the RPC method fails
    try {
      console.log('Trying direct approach to add options column...');
      
      // Add the options column
      const { error: alterError } = await supabase.rpc('execute_sql', {
        sql: 'ALTER TABLE public.product_variants ADD COLUMN IF NOT EXISTS options JSONB;'
      });
      
      if (alterError) {
        console.error('Error adding options column:', alterError);
        return false;
      }
      
      console.log('Added options column to product_variants table.');
      return true;
    } catch (directError) {
      console.error('Error with direct approach:', directError);
      return false;
    }
  }
}

// Import variants from CSV
async function importVariants() {
  // First, check and add the options column if needed
  const columnCheck = await checkOptionsColumn();
  
  if (!columnCheck) {
    console.log('WARNING: Could not verify or add the options column. Proceeding anyway...');
  }
  
  // Read the CSV file
  const fileContent = fs.readFileSync(filePath, 'utf8');
  
  // Parse the CSV data
  Papa.parse(fileContent, {
    header: true,
    skipEmptyLines: true,
    complete: async (results) => {
      const variants = results.data;
      console.log(`Found ${variants.length} variants in the CSV file.`);
      
      // Process variants in batches to avoid overwhelming the database
      const batchSize = 50;
      const batches = [];
      
      for (let i = 0; i < variants.length; i += batchSize) {
        batches.push(variants.slice(i, i + batchSize));
      }
      
      console.log(`Processing variants in ${batches.length} batches of up to ${batchSize} variants each.`);
      
      let successCount = 0;
      let errorCount = 0;
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} variants)...`);
        
        // Process each variant in the batch
        for (const variant of batch) {
          try {
            // Convert string fields to their proper types
            const processedVariant = {
              ...variant,
              price_adjustment: parseFloat(variant.price_adjustment) || 0,
              stock_quantity: parseInt(variant.stock_quantity) || 0,
              options: typeof variant.options === 'string' ? JSON.parse(variant.options) : variant.options
            };
            
            // Insert the variant into the database
            const { data, error } = await supabase
              .from('product_variants')
              .insert([processedVariant]);
            
            if (error) {
              console.error(`Error inserting variant ${variant.id}:`, error);
              errorCount++;
            } else {
              successCount++;
              if (successCount % 10 === 0) {
                console.log(`Successfully imported ${successCount} variants so far...`);
              }
            }
          } catch (error) {
            console.error(`Error processing variant:`, error);
            errorCount++;
          }
        }
      }
      
      console.log(`Import completed! Successfully imported ${successCount} variants with ${errorCount} errors.`);
    },
    error: (error) => {
      console.error('Error parsing CSV:', error);
      process.exit(1);
    }
  });
}

// Run the import
importVariants().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
