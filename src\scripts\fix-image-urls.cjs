#!/usr/bin/env node

// Import required modules
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase connection details
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY or VITE_SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to check if an image URL is valid
async function checkImageUrl(url) {
  if (!url) return false;
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error(`Error checking image URL ${url}:`, error.message);
    return false;
  }
}

// Function to fix image URLs in the database
async function fixImageUrls() {
  console.log('Checking and fixing image URLs...');
  
  // Get all products
  const { data: products, error: productsError } = await supabase
    .from('products')
    .select('*');
  
  if (productsError) {
    console.error('Error fetching products:', productsError);
    return;
  }
  
  console.log(`Found ${products.length} products to check`);
  
  // Check and fix each product's image URL
  for (const product of products) {
    console.log(`Checking product: ${product.name} (${product.id})`);
    
    // Check main image
    if (product.image) {
      const isValid = await checkImageUrl(product.image);
      console.log(`  Main image ${product.image}: ${isValid ? 'Valid' : 'Invalid'}`);
      
      if (!isValid) {
        // Set product to inactive if image is invalid
        const { error: updateError } = await supabase
          .from('products')
          .update({ is_active: false })
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`  Error updating product ${product.id}:`, updateError);
        } else {
          console.log(`  Set product ${product.id} to inactive due to invalid image`);
        }
      }
    } else {
      console.log(`  No main image for product ${product.id}`);
      
      // Set product to inactive if no image
      const { error: updateError } = await supabase
        .from('products')
        .update({ is_active: false })
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`  Error updating product ${product.id}:`, updateError);
      } else {
        console.log(`  Set product ${product.id} to inactive due to missing image`);
      }
    }
  }
  
  // Get all variants
  const { data: variants, error: variantsError } = await supabase
    .from('product_variants')
    .select('*');
  
  if (variantsError) {
    console.error('Error fetching variants:', variantsError);
    return;
  }
  
  console.log(`Found ${variants.length} variants to check`);
  
  // Check and fix each variant's image URL
  for (const variant of variants) {
    console.log(`Checking variant: ${variant.id} (Product: ${variant.product_id})`);
    
    // Check if variant has an image field
    if (variant.image) {
      const isValid = await checkImageUrl(variant.image);
      console.log(`  Variant image ${variant.image}: ${isValid ? 'Valid' : 'Invalid'}`);
      
      if (!isValid) {
        // Get the parent product's image
        const { data: product, error: productError } = await supabase
          .from('products')
          .select('image')
          .eq('id', variant.product_id)
          .single();
        
        if (productError) {
          console.error(`  Error fetching product ${variant.product_id}:`, productError);
        } else if (product.image) {
          // Update variant with parent product's image
          const { error: updateError } = await supabase
            .from('product_variants')
            .update({ image: product.image })
            .eq('id', variant.id);
          
          if (updateError) {
            console.error(`  Error updating variant ${variant.id}:`, updateError);
          } else {
            console.log(`  Updated variant ${variant.id} with parent product's image`);
          }
        }
      }
    } else {
      // Get the parent product's image
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('image')
        .eq('id', variant.product_id)
        .single();
      
      if (productError) {
        console.error(`  Error fetching product ${variant.product_id}:`, productError);
      } else if (product.image) {
        // Update variant with parent product's image
        const { error: updateError } = await supabase
          .from('product_variants')
          .update({ image: product.image })
          .eq('id', variant.id);
        
        if (updateError) {
          console.error(`  Error updating variant ${variant.id}:`, updateError);
        } else {
          console.log(`  Updated variant ${variant.id} with parent product's image`);
        }
      }
    }
  }
  
  console.log('Image URL check and fix completed!');
}

// Run the fix
fixImageUrls().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
