// <PERSON><PERSON><PERSON> to forcefully update shipping methods status in the database
import { createClient } from '@supabase/supabase-js';

// Use the same credentials as the app
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// This script will:
// 1. Log all current shipping methods
// 2. Forcefully update the database to match a specific configuration (only one active method)
// 3. Clear all caches
// 4. Verify the changes took effect

async function forceUpdateShippingMethods() {
  console.log('🔨 Forcefully updating shipping methods...');
  
  try {
    // 1. Get all shipping methods
    console.log('\n📋 Current shipping methods in database:');
    const { data: allMethods, error: getError } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (getError) {
      console.error('Error fetching shipping methods:', getError);
      return;
    }
    
    if (!allMethods || allMethods.length === 0) {
      console.log('No shipping methods found in database.');
      return;
    }
    
    allMethods.forEach(method => {
      console.log(`- ${method.name} (${method.id}): is_active = ${method.is_active}`);
    });
    
    // 2. Get the method ID you want to keep active from the user
    console.log('\n🔧 Updating all shipping methods...');
    
    // Either keep Standard Shipping or the first method in the list
    const standardShipping = allMethods.find(m => m.name.includes('Standard')) || allMethods[0];
    const activeMethodId = standardShipping.id;
    
    console.log(`Setting ONLY method "${standardShipping.name}" (${activeMethodId}) to active`);
    
    // 3. Update ALL methods in the database
    for (const method of allMethods) {
      // Set the active status based on whether this is the method we want to keep active
      const shouldBeActive = method.id === activeMethodId;
      
      // Only update if the current active status doesn't match what it should be
      if (method.is_active !== shouldBeActive) {
        const { error: updateError } = await supabase
          .from('shipping_methods')
          .update({ 
            is_active: shouldBeActive,
            updated_at: new Date().toISOString() 
          })
          .eq('id', method.id);
          
        if (updateError) {
          console.error(`Error updating method ${method.id}:`, updateError);
        } else {
          console.log(`✅ Updated "${method.name}" (${method.id}): is_active = ${shouldBeActive}`);
        }
      } else {
        console.log(`ℹ️ No change needed for "${method.name}" (${method.id}): already is_active = ${shouldBeActive}`);
      }
    }
    
    // 4. Add a cache busting timestamp to the settings table
    console.log('\n🧹 Adding cache invalidation timestamp...');
    
    const timestamp = new Date().toISOString();
    
    const { error: settingsError } = await supabase
      .from('settings')
      .upsert({ 
        key: 'shipping_cache_timestamp', 
        value: timestamp
      });
      
    if (settingsError) {
      console.error('Error updating settings:', settingsError);
    } else {
      console.log('✅ Cache timestamp updated');
    }
    
    // 5. Verify the changes
    console.log('\n🔍 Verifying changes...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (verifyError) {
      console.error('Error verifying shipping methods:', verifyError);
      return;
    }
    
    console.log('Current shipping method statuses:');
    verifyData.forEach(method => {
      const status = method.is_active ? '✅ ACTIVE' : '❌ INACTIVE';
      console.log(`- ${method.name}: ${status}`);
    });
    
    console.log('\n🔄 IMPORTANT NEXT STEPS:');
    console.log('1. Restart the development server (npm run dev)');
    console.log('2. Clear your browser cache or open in incognito mode');
    console.log('3. Check the checkout page again');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
forceUpdateShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
  }); 