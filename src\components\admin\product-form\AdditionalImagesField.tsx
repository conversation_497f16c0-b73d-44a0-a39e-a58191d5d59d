import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, X } from 'lucide-react';
import { ImageUploader } from './ImageUploader';

interface AdditionalImagesFieldProps {
  images: string[];
  onChange: (images: string[]) => void;
}

export function AdditionalImagesField({ images, onChange }: AdditionalImagesFieldProps) {
  const addImage = (url: string) => {
    const newImages = [...images, url];
    console.log('Adding image to additional images:', url);
    console.log('New additional images array:', newImages);
    onChange(newImages);
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    onChange(newImages);
  };

  const handleAddImageUrl = () => {
    const url = window.prompt('Enter image URL:');
    if (url) {
      addImage(url);
    }
  };

  return (
    <div className="space-y-2 md:col-span-2">
      <Label htmlFor="additional_images">Additional Images</Label>
      <div className="space-y-4">
        {/* Image grid */}
        {images.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
            {images.map((img, index) => (
              <div key={index} className="relative rounded-md overflow-hidden border border-gray-200 aspect-square group">
                <img 
                  src={img} 
                  alt={`Product image ${index + 1}`} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Show a placeholder on error
                    e.currentTarget.src = "https://placehold.co/200x200/e5e7eb/a1a1aa?text=Image+Error";
                  }}
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => removeImage(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
        
        {/* Upload controls */}
        <div className="flex flex-wrap gap-2">
          <ImageUploader 
            onImageUploaded={addImage} 
            buttonText="Upload New Image"
          />
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddImageUrl}
          >
            <Plus className="mr-1 h-4 w-4" />
            Add Image URL
          </Button>
        </div>
      </div>
      <p className="text-xs text-gray-500">
        Additional images will be shown in the product gallery
      </p>
    </div>
  );
}
