// Multi-Tenant System Types
// Generated for BitsNBongs Multi-Tenant Architecture

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  subdomain?: string;
  status: 'active' | 'inactive' | 'suspended';
  plan_type: 'basic' | 'premium' | 'enterprise';
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface TenantUser {
  id: string;
  tenant_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'member';
  permissions: Record<string, any>;
  created_at: string;
}

export interface TenantContext {
  tenant: Tenant;
  user_role: 'owner' | 'admin' | 'member';
  permissions: Record<string, any>;
}

export interface UserTenant {
  tenant_id: string;
  tenant_name: string;
  tenant_slug: string;
  user_role: 'owner' | 'admin' | 'member';
  permissions: Record<string, any>;
}

export interface TenantStatistics {
  tenant_id: string;
  tenant_name: string;
  tenant_slug: string;
  product_count: number;
  category_count: number;
  brand_count: number;
  order_count: number;
  blog_count: number;
  newsletter_subscriber_count: number;
  created_at: string;
  plan_type: string;
  status: string;
}

// Database table interfaces with tenant_id
export interface TenantAwareProduct {
  id: string;
  tenant_id: string;
  name: string;
  slug: string;
  price: number;
  category_id?: string;
  brand_id?: string;
  // ... other product fields
}

export interface TenantAwareCategory {
  id: string;
  tenant_id: string;
  name: string;
  slug: string;
  // ... other category fields
}

export interface TenantAwareBrand {
  id: string;
  tenant_id: string;
  name: string;
  slug: string;
  // ... other brand fields
}

export interface TenantAwareOrder {
  id: string;
  tenant_id: string;
  user_id: string;
  status: string;
  total: number;
  // ... other order fields
}

export interface TenantAwareBlog {
  id: string;
  tenant_id: string;
  title: string;
  slug: string;
  content: string;
  published_at?: string;
  // ... other blog fields
}

// API Response types
export interface TenantApiResponse<T = any> {
  data: T;
  tenant_context: {
    tenant_id: string;
    tenant_slug: string;
  };
  success: boolean;
  message?: string;
}

export interface TenantListResponse {
  tenants: UserTenant[];
  current_tenant?: UserTenant;
}

// Error types
export class TenantError extends Error {
  constructor(
    message: string,
    public code: string,
    public tenant_id?: string
  ) {
    super(message);
    this.name = 'TenantError';
  }
}

export class TenantAccessError extends TenantError {
  constructor(tenant_id: string, action: string) {
    super(
      `Access denied for tenant ${tenant_id} on action: ${action}`,
      'TENANT_ACCESS_DENIED',
      tenant_id
    );
    this.name = 'TenantAccessError';
  }
}

export class TenantNotFoundError extends TenantError {
  constructor(identifier: string) {
    super(
      `Tenant not found: ${identifier}`,
      'TENANT_NOT_FOUND'
    );
    this.name = 'TenantNotFoundError';
  }
}

// Utility types
export type TenantRole = 'owner' | 'admin' | 'member';
export type TenantStatus = 'active' | 'inactive' | 'suspended';
export type TenantPlan = 'basic' | 'premium' | 'enterprise';

// Configuration types
export interface TenantConfig {
  default_tenant_id?: string;
  enable_subdomain_routing: boolean;
  enable_custom_domains: boolean;
  max_tenants_per_user: number;
  tenant_isolation_level: 'strict' | 'relaxed';
}

// Middleware types
export interface TenantMiddlewareContext {
  tenant_id: string;
  tenant_slug: string;
  user_id?: string;
  user_role?: TenantRole;
  request_path: string;
  request_method: string;
}

// Hook types for React
export interface UseTenantResult {
  tenant: Tenant | null;
  tenants: UserTenant[];
  currentTenant: UserTenant | null;
  isLoading: boolean;
  error: TenantError | null;
  switchTenant: (tenantId: string) => Promise<void>;
  refreshTenants: () => Promise<void>;
}

// Form types
export interface CreateTenantForm {
  name: string;
  slug: string;
  domain?: string;
  subdomain?: string;
  plan_type: TenantPlan;
}

export interface UpdateTenantForm {
  name?: string;
  domain?: string;
  subdomain?: string;
  plan_type?: TenantPlan;
  settings?: Record<string, any>;
}

export interface InviteTenantUserForm {
  email: string;
  role: TenantRole;
  permissions?: Record<string, any>;
}

// Database function result types
export interface TenantIsolationTestResult {
  test_name: string;
  status: 'PASS' | 'FAIL';
  message: string;
}

export interface TenantPerformanceTestResult {
  test_name: string;
  execution_time_ms: number;
  status: 'GOOD' | 'SLOW';
}

export interface RlsPolicyValidationResult {
  table_name: string;
  rls_enabled: boolean;
  policy_count: number;
  status: 'PROTECTED' | 'RLS_ENABLED_NO_POLICIES' | 'NOT_PROTECTED';
}

export interface TenantDataDistribution {
  tenant_name: string;
  tenant_slug: string;
  product_count: number;
  category_count: number;
  brand_count: number;
  order_count: number;
  blog_count: number;
}