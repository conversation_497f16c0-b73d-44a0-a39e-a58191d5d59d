/**
 * BulkImageProcessor.ts
 * 
 * Handles batch processing of multiple products for image scraping
 * Manages rate limiting, error handling, and reporting
 */

import { PlaywrightImageScraper } from './PlaywrightImageScraper';
import { ImageQualityAssessor } from './ImageQualityAssessor';
import { SourceManager } from './SourceManager';
import { 
  ProductImage, 
  ImageSearchOptions, 
  BulkProcessingReport 
} from './types/ImageScrapingTypes';

/**
 * Bulk processing configuration
 */
interface BulkProcessorConfig {
  batchSize?: number;
  delayBetweenBatches?: number;
  maxConcurrent?: number;
  retryAttempts?: number;
  retryDelay?: number;
  defaultSearchOptions?: ImageSearchOptions;
}

/**
 * Processing result for a single product
 */
interface ProductProcessingResult {
  product_id: string;
  product_name: string;
  success: boolean;
  images: ProductImage[];
  error?: string;
  processing_time: number;
}

/**
 * Processes multiple products in bulk
 */
export class BulkImageProcessor {
  private scraper: PlaywrightImageScraper;
  private qualityAssessor: ImageQualityAssessor;
  private sourceManager: SourceManager;
  private batchSize: number;
  private delayBetweenBatches: number;
  private maxConcurrent: number;
  private retryAttempts: number;
  private retryDelay: number;
  private defaultSearchOptions: ImageSearchOptions;
  private currentSession: {
    sessionId: string;
    startTime: Date;
    totalProducts: number;
    processedProducts: number;
    successfulProducts: number;
    failedProducts: number;
    totalImagesFound: number;
  } | null = null;

  /**
   * Constructor
   * @param scraper - Image scraper
   * @param qualityAssessor - Quality assessor
   * @param sourceManager - Source manager
   * @param config - Bulk processor configuration
   */
  constructor(
    scraper: PlaywrightImageScraper,
    qualityAssessor: ImageQualityAssessor,
    sourceManager: SourceManager,
    config: BulkProcessorConfig = {}
  ) {
    this.scraper = scraper;
    this.qualityAssessor = qualityAssessor;
    this.sourceManager = sourceManager;
    this.batchSize = config.batchSize || 10;
    this.delayBetweenBatches = config.delayBetweenBatches || 2000;
    this.maxConcurrent = config.maxConcurrent || 1;
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 5000;
    this.defaultSearchOptions = config.defaultSearchOptions || {
      max_images: 5,
      min_quality_score: 50,
      min_dimensions: { width: 400, height: 400 }
    };
  }

  /**
   * Process multiple products in bulk
   * @param products - Array of products
   * @param options - Search options
   * @returns Bulk processing report
   */
  async processProducts(
    products: { name: string; category?: string; id: string }[],
    options: ImageSearchOptions = {}
  ): Promise<BulkProcessingReport> {
    // Start session
    this.startSession(products.length);
    
    // Merge options with defaults
    const searchOptions = { ...this.defaultSearchOptions, ...options };
    
    // Split products into batches
    const batches = this.splitIntoBatches(products);
    
    // Process batches
    const results: ProductProcessingResult[] = [];
    const startTime = Date.now();
    
    try {
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        // Process batch
        const batchResults = await this.processBatch(batch, searchOptions);
        results.push(...batchResults);
        
        // Update session stats
        this.updateSessionStats(batchResults);
        
        // Delay between batches (except for the last batch)
        if (i < batches.length - 1) {
          await this.delay(this.delayBetweenBatches);
        }
      }
      
      // Generate report
      const endTime = Date.now();
      const processingTime = (endTime - startTime) / 1000; // in seconds
      
      const report = this.generateReport(results, processingTime);
      
      return report;
    } catch (error) {
      console.error('Error processing products in bulk:', error);
      throw error;
    } finally {
      // End session
      this.endSession();
    }
  }

  /**
   * Process a batch of products
   * @param batch - Batch of products
   * @param options - Search options
   * @returns Array of processing results
   */
  private async processBatch(
    batch: { name: string; category?: string; id: string }[],
    options: ImageSearchOptions
  ): Promise<ProductProcessingResult[]> {
    // Process products in batch concurrently
    const promises = batch.map(product => this.processProduct(product, options));
    
    // Wait for all products to be processed
    return await Promise.all(promises);
  }

  /**
   * Process a single product
   * @param product - Product
   * @param options - Search options
   * @returns Processing result
   */
  private async processProduct(
    product: { name: string; category?: string; id: string },
    options: ImageSearchOptions
  ): Promise<ProductProcessingResult> {
    const startTime = Date.now();
    
    try {
      // Try to find images with retries
      let images: ProductImage[] = [];
      let error: Error | null = null;
      
      for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
        try {
          // Find images
          images = await this.scraper.findProductImages(product, options);
          
          // If we found images, break the retry loop
          if (images.length > 0) {
            break;
          }
          
          // If we didn't find images but this isn't the last attempt,
          // wait before retrying
          if (attempt < this.retryAttempts) {
            await this.delay(this.retryDelay);
          }
        } catch (err) {
          error = err;
          
          // If this isn't the last attempt, wait before retrying
          if (attempt < this.retryAttempts) {
            await this.delay(this.retryDelay);
          }
        }
      }
      
      // If we still have an error after all retries, throw it
      if (images.length === 0 && error) {
        throw error;
      }
      
      // Calculate processing time
      const processingTime = (Date.now() - startTime) / 1000; // in seconds
      
      // Return result
      return {
        product_id: product.id,
        product_name: product.name,
        success: images.length > 0,
        images,
        processing_time: processingTime
      };
    } catch (error) {
      // Calculate processing time
      const processingTime = (Date.now() - startTime) / 1000; // in seconds
      
      // Return error result
      return {
        product_id: product.id,
        product_name: product.name,
        success: false,
        images: [],
        error: error.message,
        processing_time: processingTime
      };
    }
  }

  /**
   * Generate bulk processing report
   * @param results - Array of processing results
   * @param processingTime - Total processing time in seconds
   * @returns Bulk processing report
   */
  private generateReport(
    results: ProductProcessingResult[],
    processingTime: number
  ): BulkProcessingReport {
    // Count successful and failed products
    const successfulProducts = results.filter(result => result.success);
    const failedProducts = results.filter(result => !result.success);
    
    // Count total images found
    const totalImagesFound = results.reduce(
      (total, result) => total + result.images.length,
      0
    );
    
    // Calculate average images per product
    const averageImagesPerProduct = successfulProducts.length > 0
      ? totalImagesFound / successfulProducts.length
      : 0;
    
    // Calculate cost savings (assuming £0.05 per image with Google)
    const costSavings = totalImagesFound * 0.05;
    
    // Create report
    const report: BulkProcessingReport = {
      total_products: results.length,
      successful_products: successfulProducts.length,
      failed_products: failedProducts.length,
      total_images_found: totalImagesFound,
      average_images_per_product: averageImagesPerProduct,
      processing_time: processingTime,
      cost_savings: costSavings,
      errors: failedProducts.map(result => ({
        product_id: result.product_id,
        product_name: result.product_name,
        error: result.error || 'Unknown error'
      })),
      success_details: successfulProducts.map(result => ({
        product_id: result.product_id,
        product_name: result.product_name,
        images_found: result.images.length,
        best_image_url: result.images.length > 0
          ? result.images[0].url
          : ''
      }))
    };
    
    return report;
  }

  /**
   * Split products into batches
   * @param products - Array of products
   * @returns Array of batches
   */
  private splitIntoBatches(
    products: { name: string; category?: string; id: string }[]
  ): Array<{ name: string; category?: string; id: string }[]> {
    const batches: Array<{ name: string; category?: string; id: string }[]> = [];
    
    for (let i = 0; i < products.length; i += this.batchSize) {
      batches.push(products.slice(i, i + this.batchSize));
    }
    
    return batches;
  }

  /**
   * Start processing session
   * @param totalProducts - Total number of products
   */
  private startSession(totalProducts: number): void {
    this.currentSession = {
      sessionId: `bulk-${Date.now()}`,
      startTime: new Date(),
      totalProducts,
      processedProducts: 0,
      successfulProducts: 0,
      failedProducts: 0,
      totalImagesFound: 0
    };
  }

  /**
   * Update session statistics
   * @param results - Array of processing results
   */
  private updateSessionStats(results: ProductProcessingResult[]): void {
    if (!this.currentSession) return;
    
    // Update processed products
    this.currentSession.processedProducts += results.length;
    
    // Update successful and failed products
    for (const result of results) {
      if (result.success) {
        this.currentSession.successfulProducts++;
        this.currentSession.totalImagesFound += result.images.length;
      } else {
        this.currentSession.failedProducts++;
      }
    }
  }

  /**
   * End processing session
   */
  private endSession(): void {
    this.currentSession = null;
  }

  /**
   * Delay execution
   * @param ms - Milliseconds to delay
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
