import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import ProductCard from './ProductCard';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ProductGridSimpleProps {
  categoryId?: string;
  subcategoryId?: string;
  searchQuery?: string;
  initialPageSize?: number;
  showAdminFilters?: boolean;
}

const ProductGridSimple: React.FC<ProductGridSimpleProps> = ({ 
  categoryId,
  subcategoryId,
  searchQuery,
  initialPageSize = 50,
  showAdminFilters = false
}) => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [hideAssigned, setHideAssigned] = useState(false);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const pageSize = initialPageSize;

  // Function to fetch products
  const fetchProducts = async (page = 0, append = false) => {
    try {
      console.log('Fetching products...');
      console.log(`Category: ${categoryId || 'all'}, Subcategory: ${subcategoryId || 'all'}, Search: ${searchQuery || 'none'}`);
      
      if (page === 0 && !append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // Calculate range for pagination
      const from = page * pageSize;
      const to = from + pageSize - 1;

      // Build query with filters
      let query = supabase
        .from('products')
        .select('*', { count: 'exact' })
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .range(from, to);

      // Apply filters
      if (categoryId && categoryId !== 'all') {
        query = query.eq('category_id', categoryId);
      }

      if (subcategoryId && subcategoryId !== 'all') {
        query = query.eq('subcategory_id', subcategoryId);
      }

      if (searchQuery && searchQuery.trim() !== '') {
        query = query.ilike('name', `%${searchQuery}%`);
      }

      // Execute query
      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching products:', error);
        setError(`Error: ${error.message}`);
        setLoading(false);
        setLoadingMore(false);
        return;
      }

      console.log(`Fetched ${data?.length || 0} products`);
      
      // Update products state
      if (append && page > 0) {
        setProducts(prev => [...prev, ...(data || [])]);
      } else {
        setProducts(data || []);
      }
      
      // Check if there are more products to load
      if (count !== null) {
        setHasMore(from + pageSize < count);
      } else {
        setHasMore(data && data.length === pageSize);
      }
      
      setLoading(false);
      setLoadingMore(false);
    } catch (err) {
      console.error('Exception in product grid:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more products
  const handleLoadMore = () => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchProducts(nextPage, true);
  };

  // Apply the hide assigned filter
  useEffect(() => {
    if (hideAssigned) {
      const filtered = products.filter(product => {
        if (categoryId) {
          return !product.subcategory_id;
        }
        return !product.category_id && !product.subcategory_id;
      });
      setFilteredProducts(filtered);
    } else {
      setFilteredProducts(products);
    }
  }, [products, hideAssigned, categoryId]);

  // Fetch products when filters change
  useEffect(() => {
    setCurrentPage(0);
    setHasMore(true);
    fetchProducts(0, false);
  }, [categoryId, subcategoryId, searchQuery]);

  return (
    <div className="py-4 min-h-[300px] relative">
      {/* Admin Filters */}
      {showAdminFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={hideAssigned}
                onChange={() => setHideAssigned(!hideAssigned)}
                className="mr-2 h-4 w-4 rounded border-gray-300 text-sage-600 focus:ring-sage-500"
              />
              <span>Hide products already assigned to categories</span>
            </label>
            {hideAssigned && (
              <span className="ml-4 text-sm text-gray-500">
                Showing {filteredProducts.length} of {products.length} products
              </span>
            )}
          </div>
        </div>
      )}
      
      {/* Loading State */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
          <p className="mt-2">Loading products...</p>
        </div>
      )}
      
      {/* Error Message */}
      {error && !loading && (
        <div className="text-center py-10 bg-red-50 rounded-lg">
          <h3 className="text-xl font-medium text-red-900">Error loading products</h3>
          <p className="mt-2 text-red-500">{error}</p>
        </div>
      )}
      
      {/* No Products Message */}
      {(hideAssigned ? filteredProducts.length === 0 : products.length === 0) && !loading && !error && (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p>No products found</p>
        </div>
      )}
      
      {/* Products Grid */}
      {!loading && !error && (hideAssigned ? filteredProducts.length > 0 : products.length > 0) && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {(hideAssigned ? filteredProducts : products).map(product => (
              <ProductCard 
                key={product.id}
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  description: product.description,
                  category_id: product.category_id,
                  subcategory_id: product.subcategory_id,
                  brand_id: product.brand_id,
                  slug: product.slug,
                  in_stock: product.in_stock ?? true,
                  is_featured: product.is_featured ?? false,
                  is_new: product.is_new ?? false,
                  is_best_seller: product.is_best_seller ?? false,
                  sale_price: product.sale_price,
                  image: product.image,
                  created_at: product.created_at || new Date().toISOString(),
                  updated_at: product.updated_at || new Date().toISOString(),
                  rating: product.rating ?? 0,
                  review_count: product.review_count ?? 0
                }}
              />
            ))}
          </div>
          
          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button 
                onClick={handleLoadMore} 
                disabled={loadingMore}
                className="min-w-[200px]"
                variant="outline"
              >
                {loadingMore ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More Products'
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProductGridSimple;
