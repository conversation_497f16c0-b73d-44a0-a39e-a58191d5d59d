// React and Hooks
import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Blog, BlogCategory } from '@/types/database';
import type { Database } from '@/types/supabase';
import { useAuth } from '@/hooks/auth.basic';
import { getImageForTopic, getImageForTopicAsync, searchImagesForTopic } from '@/api/directImageUrl';
import { generateContent, AIProvider, ContentType } from '@/lib/ai';
import { isGoogleImageSearchConfigured } from '@/api/googleImageSearch';
import { 
  Save,
  Trash2,
  Image as ImageIcon,
  RefreshCw,
  ArrowLeft,
  Wand2,
  XCircle,
  Loader2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { slugify } from '@/lib/utils';
import { v4 as uuidv4 } from 'uuid';

// Types
type BlogInsert = Database['public']['Tables']['blogs']['Insert'];
type BlogUpdate = Database['public']['Tables']['blogs']['Update'];

// Add TypeScript interface for our global app state
declare global {
  interface Window {
    __blogApp?: {
      recentlyUsedImageIndices: number[];
      formState?: {
        title?: string;
        content?: string;
        featuredImage?: string;
        isPublished?: boolean;
      };
    };
  }
}

type BlogFormData = Omit<BlogInsert, 'id' | 'created_at' | 'updated_at' | 'published_at'> & {
  updated_at: string;
  published_at?: string;
  is_published: boolean;
  is_featured: boolean;
  reading_time: number;
};

type BlogCategoryType = 'cbd-products' | 'health-wellness' | 'how-to-guides' | 'industry-news' | 'smoking-accessories';

interface BlogCategoryItem {
  id: BlogCategoryType;
  name: string;
  slug: string;
}

// Constants
const AVAILABLE_CATEGORIES: BlogCategoryItem[] = [
  { id: 'cbd-products', name: 'CBD Products', slug: 'cbd-products' },
  { id: 'health-wellness', name: 'Health & Wellness', slug: 'health-wellness' },
  { id: 'how-to-guides', name: 'How-To Guides', slug: 'how-to-guides' },
  { id: 'industry-news', name: 'Industry News', slug: 'industry-news' },
  { id: 'smoking-accessories', name: 'Smoking Accessories', slug: 'smoking-accessories' },
];

// Helper function to get category name
const getSafeCategoryName = (category: BlogCategoryType | null | string): string => {
  if (!category) return 'Uncategorized';
  const categoryId = typeof category === 'string' ? category as BlogCategoryType : category;
  const found = AVAILABLE_CATEGORIES.find(cat => cat.id === categoryId);
  return found ? found.name : 'Uncategorized';
};

const BlogEditorPage = () => {
  // 1. Router and auth hooks
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const isEditMode = !!id;

  // 2. Form state - initialize with saved state if available
  const [title, setTitle] = useState(() => window.__blogApp?.formState?.title || '');
  const [slug, setSlug] = useState('');
  const [content, setContent] = useState(() => window.__blogApp?.formState?.content || '');
  const [summary, setSummary] = useState('');
  const [category, setCategory] = useState<BlogCategoryType | null>(null);
  const [featuredImage, setFeaturedImage] = useState(() => window.__blogApp?.formState?.featuredImage || '');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isPublished, setIsPublished] = useState(() => window.__blogApp?.formState?.isPublished || false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [readingTime, setReadingTime] = useState(5);
  
  // 3. UI State
  const [isLoading, setIsLoading] = useState(false);
  const [showAiDialog, setShowAiDialog] = useState(false);
  
  // Initialize the blog app state if it doesn't exist
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.__blogApp) {
      window.__blogApp = {
        recentlyUsedImageIndices: [],
        formState: {}
      };
    }
  }, []);
  
  // Save form state when values change to prevent losing data when switching tabs
  useEffect(() => {
    if (typeof window !== 'undefined' && window.__blogApp) {
      window.__blogApp.formState = {
        title,
        content,
        featuredImage,
        isPublished
      };
    }
  }, [title, content, featuredImage, isPublished]);
  const [aiProvider, setAiProvider] = useState<AIProvider>('gemini');
  const [aiPrompt, setAiPrompt] = useState('');
  const [generationType, setGenerationType] = useState<'title' | 'content' | 'both'>('both');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [aiImagePrompt, setAiImagePrompt] = useState('');
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  
  // 4. Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 5. Helper functions
  const getCategoryById = useCallback((categoryId: BlogCategoryType | null) => {
    if (!categoryId) return null;
    return AVAILABLE_CATEGORIES.find(cat => cat.id === categoryId) || null;
  }, []);
  
  // Generate content using AI
  const generateWithAI = async () => {
    try {
      setIsGenerating(true);
      
      // Determine what content to generate based on the selection
      const contentType = generationType === 'title' ? 'topic' : 
                         generationType === 'content' ? 'full' : 'full';
      
      // Generate content using the selected AI provider
      const generatedContent = await generateContent({
        provider: aiProvider,
        contentType,
        topic: aiPrompt || title || 'CBD wellness',
        title: title,
        existingContent: content,
        tone: 'informative',
        length: 'medium'
      });
      
      // Update the appropriate fields based on what was generated
      if (generationType === 'title' || generationType === 'both') {
        // If generating a title, extract the first line or sentence
        const titleMatch = generatedContent.match(/^#\s*(.+)$|^(.+?)(?:\.|\n)/m);
        if (titleMatch) {
          const newTitle = (titleMatch[1] || titleMatch[2] || '').trim();
          setTitle(newTitle);
          
          // Also generate a slug from the title
          const generatedSlug = newTitle
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
          setSlug(generatedSlug);
        }
      }
      
      if (generationType === 'content' || generationType === 'both') {
        // If generating content, update the content field
        setContent(generatedContent);
        
        // Always generate a summary from the first paragraph
        const summaryMatch = generatedContent.match(/(?:\n|^)([^#\n].+?)(?:\n\n|$)/m);
        if (summaryMatch) {
          const newSummary = summaryMatch[1].trim();
          setSummary(newSummary.length > 200 ? newSummary.substring(0, 197) + '...' : newSummary);
        }
      }
      
      // Close the dialog
      setShowAiDialog(false);
      
      toast({
        title: "AI Content Generated",
        description: `Successfully generated ${generationType === 'both' ? 'title and content' : generationType} using ${aiProvider}.`,
      });
      
      // Optionally generate an image as well
      if (!featuredImage) {
        refreshAiImage();
      }
      
    } catch (error) {
      console.error('Error generating content with AI:', error);
      toast({
        title: "AI Generation Failed",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Get a new AI-generated image using Google Image Search
  const refreshAiImage = async () => {
    try {
      // Get the topic from the title or prompt
      const searchTerm = aiPrompt || title || 'CBD wellness';
      console.log(`Searching for images related to: ${searchTerm}`);
      
      // Clear the featured image first to ensure the UI updates
      setFeaturedImage('');
      setIsUploading(true);
      
      // Show loading state
      toast({
        title: "Searching for Images",
        description: "Looking for relevant images for your blog post...",
      });
      
      // Check if Google Image Search is configured
      const googleConfigured = isGoogleImageSearchConfigured();
      console.log(`Google Image Search configured: ${googleConfigured}`);
      
      // Always use Google Image Search as the primary source
      if (googleConfigured) {
        try {
          // Import the client-side implementation dynamically
          const { searchGoogleImages } = await import("@/api/googleImageSearch");
          
          // Create multiple search variations to get better results
          const searchVariations = [
            // Try with the exact search term first
            searchTerm,
            
            // Add CBD-specific terms if relevant
            searchTerm.toLowerCase().includes('cbd') ? `${searchTerm} CBD oil product` : null,
            searchTerm.toLowerCase().includes('vape') ? `${searchTerm} vaping product` : null,
            
            // Try with quality descriptors
            `${searchTerm} high quality image`,
            
            // Try with stock photo descriptors
            `${searchTerm} professional photo`,
            
            // Try with relevant industry terms
            `${searchTerm} wellness`,
            `${searchTerm} health`
          ].filter(Boolean); // Remove null entries
          
          console.log("Search variations:", searchVariations);
          
          // Try each search variation until we find images
          let allImages: string[] = [];
          
          for (const variation of searchVariations) {
            if (allImages.length >= 10) break; // Stop if we have enough images
            
            try {
              // Add a timestamp to avoid caching issues
              const timestamp = Date.now();
              const finalQuery = `${variation} ${timestamp % 1000}`;
              
              console.log(`Trying search variation: "${finalQuery}"`);
              const images = await searchGoogleImages(finalQuery, 5);
              
              if (images && images.length > 0) {
                // Add unique images to our collection
                images.forEach(img => {
                  if (!allImages.includes(img)) {
                    allImages.push(img);
                  }
                });
                
                console.log(`Found ${images.length} images for "${variation}", total: ${allImages.length}`);
              }
            } catch (variationError) {
              console.warn(`Search failed for variation "${variation}":`, variationError);
              // Continue with next variation
            }
          }
          
          if (allImages.length > 0) {
            // Randomly select an image from all found images for variety
            const randomIndex = Math.floor(Math.random() * allImages.length);
            const imageUrl = allImages[randomIndex];
            console.log(`Selected image ${randomIndex + 1} of ${allImages.length}:`, imageUrl);
            
            // Set the image URL directly
            setFeaturedImage(imageUrl);
            
            toast({
              title: "Image Found",
              description: `Found ${allImages.length} suitable images using Google Image Search.`,
            });
            
            return; // Exit early if we found an image
          } else {
            console.warn('No images found from any search variation, trying fallback');
          }
        } catch (searchError) {
          console.warn('Google Image Search failed, using fallback:', searchError);
        }
      }
      
      // Fallback to category-based images if Google search fails
      console.log('Using fallback images');
      const fallbackImage = getImageForTopic(searchTerm);
      setFeaturedImage(fallbackImage);
      
      toast({
        title: "Using Fallback Image",
        description: "Google Image Search failed. Using a fallback image instead.",
      });
    } catch (error) {
      console.error('Error finding image:', error);
      
      // Fallback to SVG on error
      const searchTerm = aiPrompt || title || 'CBD wellness';
      const svgImage = getImageForTopic(searchTerm);
      setFeaturedImage(svgImage);
      
      toast({
        title: "Image Search Failed",
        description: "Failed to find suitable images. Using a placeholder instead.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  // Function to process and save existing images to Supabase storage
  const processExistingImage = async (imageUrl: string) => {
    if (!imageUrl) return '';
    
    // Skip if the image is already from Supabase storage
    if (imageUrl.includes('supabase.co/storage')) {
      console.log('Image is already in Supabase storage:', imageUrl);
      return imageUrl;
    }
    
    try {
      setIsUploadingImage(true);
      console.log('Processing existing image:', imageUrl);
      
      toast({
        title: "Processing Image",
        description: "Saving image to storage...",
      });
      
      // Fetch the image directly - this works in the product page
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }
      
      // Check if the response is an image by examining Content-Type
      const contentType = response.headers.get('Content-Type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error('The URL did not return a valid image');
      }
      
      // Convert to blob
      const blob = await response.blob();
      
      // Create a safe filename
      const safeTitle = (title || 'blog-post').replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const fileExt = contentType.split('/')[1] || 'jpg';
      const fileName = `${safeTitle}-${Math.random().toString(36).substring(2, 8)}.${fileExt}`;
      const filePath = `blog-images/${fileName}`;
      
      // Upload to Supabase storage
      const bucketName = 'product-images'; // Using the same bucket as products
      console.log(`Uploading image to bucket: ${bucketName}, path: ${filePath}`);
      
      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, blob, { contentType });
      
      if (uploadError) throw uploadError;
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);
      
      console.log(`Image saved successfully: ${publicUrl}`);
      
      toast({
        title: "Image Processed",
        description: "Successfully saved the image to storage.",
      });
      
      return publicUrl;
    } catch (error) {
      console.error('Error processing image:', error);
      
      // If direct fetch fails, try using a proxy service
      try {
        console.log('Trying to use a proxy service for the image');
        
        // Import the Google Image Search to find an alternative image
        const { searchGoogleImages } = await import("@/api/googleImageSearch");
        const searchTerm = title || 'CBD wellness';
        const images = await searchGoogleImages(searchTerm, 3);
        
        if (images && images.length > 0) {
          // Use the first result from the new search
          const newImageUrl = images[0];
          console.log('Found alternative image:', newImageUrl);
          
          // Try to process this new image
          const response = await fetch(newImageUrl);
          if (!response.ok) throw new Error('Failed to fetch alternative image');
          
          const blob = await response.blob();
          const fileName = `blog-image-fallback-${Math.random().toString(36).substring(2, 8)}.jpg`;
          const filePath = `blog-images/${fileName}`;
          
          const { error: uploadError } = await supabase.storage
            .from('product-images')
            .upload(filePath, blob);
          
          if (uploadError) throw uploadError;
          
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(filePath);
          
          toast({
            title: "Alternative Image Found",
            description: "Used Google Image Search to find a replacement image.",
          });
          
          return publicUrl;
        }
      } catch (proxyError) {
        console.error('Proxy attempt also failed:', proxyError);
      }
      
      // Final fallback to a category-based image
      const topic = title || 'CBD products';
      const fallbackImage = getImageForTopic(topic);
      
      toast({
        title: "Image Processing Failed",
        description: "Using a fallback image instead.",
        variant: "destructive"
      });
      
      return fallbackImage;
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Function to handle regenerating and saving images for existing blogs
  const processExistingBlogImage = async () => {
    if (!blog || !blog.featured_image) return;
    
    // Skip if the image is already from Supabase storage
    if (blog.featured_image.includes('supabase.co/storage')) {
      console.log('Image is already in Supabase storage:', blog.featured_image);
      return;
    }
    
    // Process the existing image
    const newImageUrl = await processExistingImage(blog.featured_image);
    if (newImageUrl && newImageUrl !== blog.featured_image) {
      // Update the image in the form
      setFeaturedImage(newImageUrl);
      
      // Update the image in the database
      try {
        const { error } = await supabase
          .from('blogs')
          .update({ featured_image: newImageUrl })
          .eq('id', id);
          
        if (error) throw error;
        
        toast({
          title: "Image Updated",
          description: "Successfully saved the blog image to storage and updated the database.",
        });
        
        // Invalidate the query to refresh the data
        queryClient.invalidateQueries({ queryKey: ['blog', id] });
      } catch (error) {
        console.error('Error updating blog image in database:', error);
        toast({
          title: "Database Update Failed",
          description: "The image was saved to storage but could not be updated in the database.",
          variant: "destructive"
        });
      }
    }
  };
  
  // 6. Data fetching  // Fetch blog data if editing an existing blog
  const { data: blog, isLoading: isBlogLoading } = useQuery({
    queryKey: ['blog', id],
    queryFn: async () => {
      if (!id) return null;
      
      const { data, error } = await supabase
        .from('blogs')
        .select('*')
        .eq('id', id)
        .single();
        
      if (error) throw error;
      return data as Blog;
    },
    enabled: !!id,
    initialData: null
  });
  
  // Handle blog data when it's loaded
  useEffect(() => {
    if (blog) {
      console.log('Blog data loaded:', blog);
      // Populate form with existing blog data
      setTitle(blog.title || '');
      setContent(blog.content || '');
      setSummary(blog.summary || '');
      setFeaturedImage(blog.featured_image || '');
      setSlug(blog.slug || '');
      setIsPublished(blog.is_published || false);
      setIsFeatured(blog.is_featured || false);
      setReadingTime(blog.reading_time || 5);
      setCategory(blog.category as BlogCategoryType || null);
      setTags(blog.tags || []);
      
      // Check if the featured image needs to be regenerated
      if (blog.featured_image && !blog.featured_image.includes('supabase.co/storage')) {
        console.log('Existing blog has an image that may need regeneration:', blog.featured_image);
        // Process the existing image in the background
        processExistingImage(blog.featured_image).then(newUrl => {
          if (newUrl && newUrl !== blog.featured_image) {
            setFeaturedImage(newUrl);
            console.log('Updated image URL:', newUrl);
          }
        });
      }
    }
  }, [blog]);

  // 7. Mutations
  const saveBlogMutation = useMutation<Blog, Error, BlogFormData>({
    mutationFn: async (blogData) => {
      try {
        const { published_at, ...dataToSave } = blogData;
        const dataToSaveWithAuthor = {
          ...dataToSave,
          author_id: user?.id || null,
        };

        if (id) {
          // Update existing blog
          const { data, error } = await supabase
            .from('blogs')
            .update(dataToSaveWithAuthor)
            .eq('id', id)
            .select()
            .single();
          
          if (error) throw error;
          return data as Blog;
        } else {
          // Create new blog
          const { data, error } = await supabase
            .from('blogs')
            .insert([{
              ...dataToSaveWithAuthor,
              published_at: published_at || null
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data as Blog;
        }
      } catch (error) {
        console.error('Error saving blog:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      if (id) {
        queryClient.invalidateQueries({ queryKey: ['blog', id] });
      }
      toast({
        title: 'Success',
        description: id ? 'Blog post updated successfully' : 'Blog post created successfully',
      });
      navigate('/admin/blogs');
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save blog post',
        variant: 'destructive',
      });
    },
  });

  // 8. Event handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim() || !content.trim()) {
      toast({
        title: 'Error',
        description: 'Title and content are required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);
      let imageUrl = featuredImage;
      
      // Process the featured image if it exists and isn't already in Supabase storage
      if (imageUrl && !imageUrl.includes('supabase.co/storage')) {
        try {
          console.log('Processing featured image before saving:', imageUrl);
          toast({
            title: "Processing Image",
            description: "Saving image to storage for persistence...",
          });
          
          // Process the existing image to handle CORS issues
          imageUrl = await processExistingImage(imageUrl);
          console.log('Processed image URL:', imageUrl);
        } catch (processError) {
          console.error('Error processing featured image:', processError);
          // Continue with the original URL if processing fails
        }
      }
      
      // Upload new image if a file was selected
      if (imageFile) {
        try {
          const fileExt = imageFile.name.split('.').pop();
          const fileName = `${uuidv4()}.${fileExt}`;
          const filePath = `blog-images/${fileName}`;
          
          // Use the product-images bucket with blog-images folder
          const bucketName = 'product-images';
          console.log(`Uploading to bucket: ${bucketName}, path: ${filePath}`);
          
          const { error: uploadError } = await supabase.storage
            .from(bucketName)
            .upload(filePath, imageFile);
            
          if (uploadError) throw uploadError;
          
          // Get the public URL
          const { data: { publicUrl } } = supabase.storage
            .from(bucketName)
            .getPublicUrl(filePath);
            
          imageUrl = publicUrl;
        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast({
            title: 'Error',
            description: 'Failed to upload image',
            variant: 'destructive',
          });
          return;
        }
      }

      // Calculate reading time (words per minute)
      const words = content.trim().split(/\s+/).length;
      const readingTime = Math.ceil(words / 200); // Average reading speed: 200 words per minute

      const blogData: BlogFormData = {
        title: title.trim(),
        slug: slug || title.trim().toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
        content: content.trim(),
        summary: summary.trim() || content.trim().substring(0, 200) + '...',
        category: category || null,
        featured_image: imageUrl || null,
        tags: tags,
        is_published: isPublished,
        is_featured: isFeatured,
        reading_time: readingTime,
        updated_at: new Date().toISOString(),
      };

      await saveBlogMutation.mutateAsync(blogData);
    } catch (error) {
      console.error('Error saving blog:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while saving the blog post',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 9. Render
  if (isBlogLoading) {
    return (
      <div className="container mx-auto py-8">
        <Skeleton className="h-12 w-64 mb-8" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="container mx-auto py-8">
        <div className="flex flex-col space-y-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">
            {isEditMode ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h1>
          <Button
            variant="outline"
            onClick={() => navigate('/admin/blogs')}
          >
            Back to Blog Posts
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Blog Content</CardTitle>
              <CardDescription>
                Create and edit your blog post content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter blog post title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug (URL)</Label>
                <div className="flex space-x-2">
                  <Input
                    id="slug"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="blog-post-url"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const generatedSlug = title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/(^-|-$)/g, '');
                      setSlug(generatedSlug);
                    }}
                  >
                    Generate
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Featured Image</Label>
                  <div className="flex space-x-2">
                    {isEditMode && blog?.featured_image && (
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => processExistingImage(blog.featured_image).then(newUrl => {
                          if (newUrl) {
                            setFeaturedImage(newUrl);
                            toast({
                              title: "Image Processed",
                              description: "Successfully saved the image to storage for persistence."
                            });
                          }
                        })}
                        disabled={isUploading}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Save to Storage
                      </Button>
                    )}
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={refreshAiImage}
                      disabled={isUploading}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      {isUploading ? 'Generating...' : 'Generate Image'}
                    </Button>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  {featuredImage ? (
                    <div className="relative group">
                      <img
                        src={featuredImage}
                        alt="Featured"
                        className="h-32 w-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          setFeaturedImage('');
                          setImageFile(null);
                        }}
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed rounded-md p-4 text-center">
                      <p className="text-sm text-gray-500 mb-2">
                        No image selected
                      </p>
                      <div className="flex space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Image
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={refreshAiImage}
                          disabled={isUploadingImage}
                        >
                          {isUploadingImage ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            <>
                              <Wand2 className="h-4 w-4 mr-2" />
                              AI Image
                            </>
                          )}
                        </Button>
                      </div>
                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setImageFile(file);
                            setFeaturedImage(URL.createObjectURL(file));
                          }
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="content">Content</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAiDialog(true)}
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    AI Writer
                  </Button>
                </div>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your blog post content here..."
                  rows={12}
                  className="font-mono text-sm"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="summary">Summary (Optional)</Label>
                <Textarea
                  id="summary"
                  value={summary}
                  onChange={(e) => setSummary(e.target.value)}
                  placeholder="A short summary of your blog post"
                  rows={3}
                />
                <p className="text-xs text-gray-500">
                  If left empty, a summary will be generated from the first 200 characters of your content.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>
                Additional information about your blog post
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={category || ''}
                    onValueChange={(value: BlogCategoryType) => setCategory(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {AVAILABLE_CATEGORIES.map((cat) => (
                        <SelectItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1.5 inline-flex items-center justify-center h-4 w-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white"
                          onClick={() =>
                            setTags(tags.filter((t) => t !== tag))
                          }
                        >
                          <span className="sr-only">Remove tag</span>
                          <svg
                            className="h-2 w-2"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 8 8"
                          >
                            <path
                              strokeLinecap="round"
                              strokeWidth="1.5"
                              d="M1 1l6 6m0-6L1 7"
                            />
                          </svg>
                        </button>
                      </span>
                    ))}
                    <div className="flex">
                      <Input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && tagInput.trim()) {
                            e.preventDefault();
                            if (!tags.includes(tagInput.trim())) {
                              setTags([...tags, tagInput.trim()]);
                            }
                            setTagInput('');
                          }
                        }}
                        placeholder="Add a tag..."
                        className="w-32"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="ml-2"
                        onClick={() => {
                          if (tagInput.trim() && !tags.includes(tagInput.trim())) {
                            setTags([...tags, tagInput.trim()]);
                            setTagInput('');
                          }
                        }}
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-published"
                    checked={isPublished}
                    onCheckedChange={setIsPublished}
                  />
                  <Label htmlFor="is-published">Publish</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-featured"
                    checked={isFeatured}
                    onCheckedChange={setIsFeatured}
                  />
                  <Label htmlFor="is-featured">Featured</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/blogs')}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUploading}>
              {isUploading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Saving...
                </>
              ) : isEditMode ? (
                'Update Post'
              ) : (
                'Create Post'
              )}
            </Button>
          </div>
        </form>
        </div>
      </div>
      
      {/* AI Content Generation Dialog */}
      <Dialog open={showAiDialog} onOpenChange={setShowAiDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Generate Content with AI</DialogTitle>
            <DialogDescription>
              Use AI to generate content for your blog post. Choose what you want to generate and which AI provider to use.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="ai-prompt">Prompt or Topic</Label>
              <Input
                id="ai-prompt"
                placeholder="Enter a topic or prompt for the AI"
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
              />
              <p className="text-xs text-gray-500">
                Describe what you want the AI to write about. If left empty, your current title will be used.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="generation-type">What to Generate</Label>
              <Select
                value={generationType}
                onValueChange={(value) => setGenerationType(value as 'title' | 'content' | 'both')}
              >
                <SelectTrigger id="generation-type">
                  <SelectValue placeholder="Select what to generate" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="title">Title Only</SelectItem>
                  <SelectItem value="content">Content Only</SelectItem>
                  <SelectItem value="both">Both Title and Content</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="ai-provider">AI Provider</Label>
              <Select
                value={aiProvider}
                onValueChange={(value) => setAiProvider(value as AIProvider)}
              >
                <SelectTrigger id="ai-provider">
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini">Google Gemini</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Different AI providers may generate different quality content. Try them to see which works best for you.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowAiDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              type="button"
              onClick={generateWithAI}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BlogEditorPage;

