import * as React from "react"
import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts, dismiss } = useToast()

  // Add effect to ensure toasts are dismissed after their duration
  React.useEffect(() => {
    // For each toast, set up a timer to dismiss it
    toasts.forEach(toast => {
      if (toast.open && toast.id) {
        // Use the toast's duration or default to 3000ms
        const duration = toast.duration || 3000;

        // Set a timeout to dismiss the toast
        const timer = setTimeout(() => {
          dismiss(toast.id);
        }, duration);

        // Set another timeout to completely remove the toast
        const removeTimer = setTimeout(() => {
          // Force remove any toast that hasn't been removed after duration + 500ms
          if (toasts.some(t => t.id === toast.id)) {
            dismiss(toast.id);
          }
        }, duration + 500);

        // Clean up the timers when the component unmounts or the toast changes
        return () => {
          clearTimeout(timer);
          clearTimeout(removeTimer);
        };
      }
    });
  }, [toasts, dismiss]);

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose onClick={() => dismiss(id)} />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
