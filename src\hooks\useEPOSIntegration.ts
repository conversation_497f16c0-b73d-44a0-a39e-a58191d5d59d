import { useState, useEffect } from 'react';
import { getEPOSService } from '@/services/eposIntegration';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

/**
 * Hook for integrating with EPOS system in the admin interface
 */
export function useEPOSIntegration() {
  const queryClient = useQueryClient();
  const [isSyncing, setIsSyncing] = useState(false);
  const eposService = getEPOSService();

  // Mutation for manually triggering a sync
  const syncMutation = useMutation({
    mutationFn: async () => {
      setIsSyncing(true);
      try {
        await eposService.syncProductsFromEPOS();
        return { success: true };
      } finally {
        setIsSyncing(false);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: 'EPOS Sync Completed',
        description: 'Products have been synchronized with the EPOS system.',
      });
    },
    onError: (error) => {
      toast({
        title: 'EPOS Sync Failed',
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    },
  });

  // Mutation for mapping products to EPOS
  const mapProductsMutation = useMutation({
    mutationFn: async (mappings: { productId: string; eposId: string; sku: string }[]) => {
      return eposService.mapProductsToEPOS(mappings);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: 'Products Mapped',
        description: 'Products have been successfully mapped to the EPOS system.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Mapping Failed',
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    },
  });

  // Query for getting products that need mapping (missing SKU or external_id)
  const { data: unmappedProducts, isLoading: isLoadingUnmapped } = useQuery({
    queryKey: ['unmapped-products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('id, name, sku, external_id')
        .or('sku.is.null,external_id.is.null');

      if (error) throw error;
      return data || [];
    },
  });

  // Bulk update SKUs mutation
  const bulkUpdateSKUsMutation = useMutation({
    mutationFn: async (updates: { id: string; sku: string }[]) => {
      // Process in batches to avoid overwhelming the database
      const batchSize = 50;
      const batches = [];
      
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);
        batches.push(batch);
      }
      
      for (const batch of batches) {
        for (const update of batch) {
          const { error } = await supabase
            .from('products')
            .update({ sku: update.sku })
            .eq('id', update.id);
            
          if (error) throw error;
        }
      }
      
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['unmapped-products'] });
      toast({
        title: 'SKUs Updated',
        description: 'Product SKUs have been successfully updated.',
      });
    },
    onError: (error) => {
      toast({
        title: 'SKU Update Failed',
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    },
  });

  return {
    isSyncing,
    unmappedProducts,
    isLoadingUnmapped,
    syncWithEPOS: syncMutation.mutate,
    mapProductsToEPOS: mapProductsMutation.mutate,
    bulkUpdateSKUs: bulkUpdateSKUsMutation.mutate,
  };
}

/**
 * Hook for checking real-time stock in the shopping cart
 */
export function useEPOSStockCheck() {
  const eposService = getEPOSService();

  // Function to check if a product is in stock
  const checkProductStock = async (productId: string, quantity: number = 1): Promise<boolean> => {
    try {
      const availableStock = await eposService.checkProductStock(productId);
      return availableStock >= quantity;
    } catch (error) {
      console.error('Error checking product stock:', error);
      // Default to true to prevent blocking purchases if stock check fails
      return true;
    }
  };

  return {
    checkProductStock,
  };
}