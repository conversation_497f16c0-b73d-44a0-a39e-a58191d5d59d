import { getProxiedImageUrl } from './imageProxy';

// Define fallback images for different categories
const fallbackImages: Record<string, string[]> = {
  'vaping': [
    'https://cdn.pixabay.com/photo/2019/06/25/04/01/smoke-4297364_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/electronic-cigarette-925759_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/smoking-925758_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/01/28/22/20/electronic-cigarette-615000_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/29/13/10/adult-1869690_1280.jpg',
  ],
  'cbd oil': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/05/13/22/29/cbd-5169412_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/09/18/04/40/hemp-4485876_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/04/25/10/12/cbd-oil-5090cbd-5090217_1280.jpg',
  ],
  'anxiety': [
    'https://cdn.pixabay.com/photo/2019/06/25/04/01/smoke-4297364_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/10/03/47/guy-2617866_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/03/26/21/54/yoga-2176668_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/06/00/27/yoga-2587066_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/07/02/10/23/training-828764_1280.jpg',
  ],
  'sleep': [
    'https://cdn.pixabay.com/photo/2016/11/18/17/20/woman-1835799_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/29/06/16/home-1867772_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/02/00/49/people-2569234_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/10/28/07/47/woman-2896389_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/01/11/08/bedroom-4593423_1280.jpg',
  ],
  'pain': [
    'https://cdn.pixabay.com/photo/2021/01/05/06/40/acupuncture-5889256_1280.jpg',
    'https://cdn.pixabay.com/photo/2014/09/25/20/32/healing-460546_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/04/09/12/45/massage-2215920_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/01/30/21/14/massage-2022158_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/07/02/10/22/training-828726_1280.jpg',
  ],
  'legalization': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/39/cbd-4391539_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/04/15/11/47/cbd-4129323_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
    'https://cdn.pixabay.com/photo/2021/01/29/08/08/hemp-5960309_1280.jpg',
    'https://cdn.pixabay.com/photo/2021/01/29/08/08/hemp-5960308_1280.jpg',
  ],
  'policy': [
    'https://cdn.pixabay.com/photo/2018/01/18/20/42/pencil-3091204_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/11/15/21/31/read-1044914_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/07/19/10/00/still-life-851328_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/11/19/21/10/glasses-1052010_1280.jpg',
    'https://cdn.pixabay.com/photo/2018/01/17/07/06/laptop-3087585_1280.jpg',
  ],
  'general': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/39/cbd-4391539_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/04/15/11/47/cbd-4129323_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/05/13/22/29/cbd-5169412_1280.jpg',
  ],
};

// Keep track of recently used images to avoid repetition
let recentlyUsedImages: string[] = [];

// Track the last query and its results to ensure variety
let lastQuery = '';
let lastQueryResults: string[] = [];
const MAX_RECENT_IMAGES = 10; // How many recent images to remember

/**
 * Determine the category of a topic based on content analysis
 * @param query The search query
 * @returns The most relevant category
 */
export function getTopicCategory(query: string): string {
  const queryLower = query.toLowerCase();
  
  if (queryLower.includes('vape') || queryLower.includes('vaping')) {
    return 'vaping';
  }
  
  if (queryLower.includes('cbd oil') || queryLower.includes('tincture')) {
    return 'cbd oil';
  }
  
  if (queryLower.includes('anxiety') || queryLower.includes('stress')) {
    return 'anxiety';
  }
  
  if (queryLower.includes('sleep') || queryLower.includes('insomnia')) {
    return 'sleep';
  }
  
  if (queryLower.includes('pain') || queryLower.includes('relief')) {
    return 'pain';
  }
  
  if (queryLower.includes('legal') || queryLower.includes('law') || queryLower.includes('legalization')) {
    return 'legalization';
  }
  
  if (queryLower.includes('policy') || queryLower.includes('regulation')) {
    return 'policy';
  }
  
  return 'general';
}

/**
 * Get a random image URL for a specific category
 * @param category The category to get an image for
 * @returns A random image URL that's been proxied to avoid CORS issues
 */
export function getRandomImage(category: string): string {
  // Default to general category if the requested one doesn't exist
  const categoryImages = fallbackImages[category] || fallbackImages['general'];
  
  // Filter out recently used images if possible
  const availableImages = categoryImages.filter(img => !recentlyUsedImages.includes(img));
  
  // If all images have been recently used, reset tracking and use all images
  const imagesToUse = availableImages.length > 0 ? availableImages : categoryImages;
  
  // Get a random image from the available ones
  const randomIndex = Math.floor(Math.random() * imagesToUse.length);
  const selectedImage = imagesToUse[randomIndex];
  
  // Update the recently used images list
  recentlyUsedImages.push(selectedImage);
  
  // Keep only the last 5 images in the history to avoid memory issues
  if (recentlyUsedImages.length > 5) {
    recentlyUsedImages = recentlyUsedImages.slice(-5);
  }
  
  // Proxy the image URL to avoid CORS issues
  return getProxiedImageUrl(selectedImage);
}

/**
 * Search for images based on a query
 * @param query The search query
 * @returns An array of image URLs
 */
export async function searchImages(query: string): Promise<string[]> {
  const normalizedQuery = query.toLowerCase();
  
  try {
    // Check if this is a new query or the same as the last one
    const isNewQuery = normalizedQuery !== lastQuery;
    
    // If it's the same query but we have previous results, return a different one
    // This ensures variety when clicking "New AI Image" multiple times for the same topic
    if (!isNewQuery && lastQueryResults.length > 1) {
      console.log('Using cached results for repeated query, but selecting a different image');
      
      // Filter out recently used images
      const unusedImages = lastQueryResults.filter(img => !recentlyUsedImages.includes(img));
      
      // If we have unused images, return one of those
      if (unusedImages.length > 0) {
        const randomIndex = Math.floor(Math.random() * unusedImages.length);
        const selectedImage = unusedImages[randomIndex];
        
        // Track this image as recently used
        recentlyUsedImages.push(selectedImage);
        if (recentlyUsedImages.length > MAX_RECENT_IMAGES) {
          recentlyUsedImages = recentlyUsedImages.slice(-MAX_RECENT_IMAGES);
        }
        
        return [selectedImage];
      }
    }
    
    console.log(`Searching for images related to: ${normalizedQuery}`);
    
    // Get images from our smart categorization system
    const category = getTopicCategory(normalizedQuery);
    console.log(`Determined category: ${category}`);
    
    // Get a random image for this category
    const randomImage = getRandomImage(category);
    console.log(`Selected random image from ${category} category`);
    
    // Update tracking variables
    lastQuery = normalizedQuery;
    lastQueryResults = [randomImage];
    
    return [randomImage];
  } catch (error) {
    console.error('Error searching for images:', error);
    
    // Fallback to a random image from the general category
    const fallbackImage = getRandomImage('general');
    return [fallbackImage];
  }
}
