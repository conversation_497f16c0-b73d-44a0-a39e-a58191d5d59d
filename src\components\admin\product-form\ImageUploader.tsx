import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';
import { toast } from '@/components/ui/use-toast';

// Fallback method to handle image uploads when Supabase storage has RLS issues
const handleImageUploadFallback = async (file: File): Promise<string> => {
  // For demo purposes, we'll create a data URL from the file
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      // This creates a base64 data URL that can be used as an image source
      const dataUrl = reader.result as string;
      resolve(dataUrl);
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(file);
  });
};

interface ImageUploaderProps {
  onImageUploaded: (url: string) => void;
  buttonText?: string;
  disabled?: boolean;
}

export function ImageUploader({ onImageUploaded, buttonText = 'Upload Image', disabled = false }: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUpload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Upload button clicked");
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("File input changed");
    const files = e.target.files;
    if (!files || files.length === 0) {
      console.log("No files selected");
      return;
    }

    const file = files[0];
    console.log("File selected:", file.name);
    setIsUploading(true);

    try {
      let imageUrl = '';

      // First try to upload to Supabase
      try {
        // Generate a unique file name to avoid collisions
        const fileExt = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        // Use the correct path structure with product-images/ prefix
        const filePath = `product-images/${fileName}`;

        console.log("Attempting to upload to Supabase path:", filePath);

        // Ensure we have a valid Supabase client
        if (!supabase) {
          throw new Error("Supabase client is not initialized");
        }

        // Upload the file to Supabase Storage
        const { data, error } = await supabase.storage
          .from('product-images')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true // Changed to true to overwrite if file exists
          });

        if (error) {
          console.error("Supabase upload error:", error);
          throw error;
        }

        console.log("Upload successful, getting public URL");

        // Get the public URL for the uploaded file
        const { data: publicUrlData } = supabase.storage
          .from('product-images')
          .getPublicUrl(filePath);

        if (!publicUrlData || !publicUrlData.publicUrl) {
          throw new Error("Failed to get public URL");
        }

        imageUrl = publicUrlData.publicUrl;
        console.log('Image uploaded successfully to Supabase:', imageUrl);
      } catch (supabaseError) {
        console.warn('Supabase upload failed, using fallback method:', supabaseError);

        // If Supabase upload fails, use the fallback method
        imageUrl = await handleImageUploadFallback(file);
        console.log('Image converted to data URL as fallback');
      }

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Notify the parent component with the new URL
      onImageUploaded(imageUrl);

      toast({
        title: 'Image uploaded',
        description: 'Your image has been uploaded successfully',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'There was an error uploading your image',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleUpload}
        disabled={isUploading || disabled}
      >
        {isUploading ? 'Uploading...' : (
          <>
            <Upload className="mr-1 h-4 w-4" />
            {buttonText}
          </>
        )}
      </Button>
    </div>
  );
}
