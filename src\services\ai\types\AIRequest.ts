/**
 * AI Request Types for Unified AI System
 * 
 * Defines the structure for all AI requests across the platform
 */

export type AIRequestType = 
  // Content Generation
  | 'blog_content'
  | 'blog_title'
  | 'blog_summary'
  | 'product_description'
  | 'product_seo'
  | 'newsletter_content'
  | 'newsletter_subject'
  | 'social_media_post'
  | 'social_media_caption'
  
  // Image & Media
  | 'image_search_query'
  | 'image_alt_text'
  | 'video_script'
  
  // E-commerce Specific
  | 'customer_analysis'
  | 'inventory_optimization'
  | 'price_optimization'
  | 'fraud_detection'
  | 'product_recommendations'
  
  // Assistance & Support
  | 'frontend_help'
  | 'backend_help'
  | 'voice_command'
  | 'chat_response'
  
  // SEO & Marketing
  | 'seo_optimization'
  | 'meta_description'
  | 'hashtag_generation'
  | 'keyword_analysis';

export type AIProvider = 'deepseek' | 'gemini' | 'openrouter' | 'auto';

export type AIComplexity = 'simple' | 'medium' | 'complex';

export type AIUrgency = 'low' | 'medium' | 'high';

export interface AIRequestContext {
  // Business Context
  business_type?: 'cannabis' | 'cbd' | 'vaping' | 'general';
  brand_voice?: {
    tone: 'professional' | 'casual' | 'technical' | 'friendly';
    personality: string;
    compliance_requirements?: string[];
  };
  
  // Content Context
  category?: string;
  target_audience?: string;
  previous_content?: string[];
  related_products?: any[];
  
  // Technical Context
  max_length?: number;
  format?: 'html' | 'markdown' | 'plain' | 'json';
  language?: string;
  
  // Multi-tenant Context
  tenant_id?: string;
  tenant_config?: any;
}

export interface AIRequest {
  // Core Request
  type: AIRequestType;
  content: string;
  context?: AIRequestContext;
  
  // Provider Selection
  provider?: AIProvider;
  complexity?: AIComplexity;
  urgency?: AIUrgency;
  
  // Processing Options
  stream?: boolean;
  cache_key?: string;
  fallback_enabled?: boolean;
  
  // Metadata
  request_id?: string;
  user_id?: string;
  session_id?: string;
  timestamp?: Date;
}

export interface AIRequestOptions {
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop_sequences?: string[];
  tools?: any[];
}
