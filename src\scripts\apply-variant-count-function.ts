import { customSupabase } from '../integrations/supabase/customClient';
import fs from 'fs';
import path from 'path';

async function applyMigration() {
  try {
    console.log('Applying variant count function migration...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../migrations/add_variant_count_function.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { error } = await customSupabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error('Error applying migration:', error);
      return;
    }
    
    console.log('Migration applied successfully!');
    
    // Test the function
    const { data, error: testError } = await customSupabase.rpc('get_variant_counts_per_product');
    
    if (testError) {
      console.error('Error testing function:', testError);
      return;
    }
    
    console.log('Function test result:', data);
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applyMigration();
