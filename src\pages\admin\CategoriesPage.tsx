
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Category } from "@/types/database";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { PlusCircle, Edit, Trash, X, List, GripVertical } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SimpleCategoryOrderer from "@/components/admin/SimpleCategoryOrderer";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CategoryForm } from "@/components/admin/CategoryForm";
import { AspectRatio } from "@/components/ui/aspect-ratio";

export default function CategoriesPage() {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"table" | "sort">("table");
  const queryClient = useQueryClient();

  // Fetch categories with parent relationships
  const {
    data: categories,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      console.log('Fetching categories...');
      const { data, error } = await supabase
        .from("categories")
        .select("*, parent:parent_id(id, name)")
        .order("display_order", { ascending: true });

      if (error) throw error;
      console.log('Categories fetched:', data);
      return data as (Category & { parent: { id: string; name: string } | null })[];
    },
  });

  // Delete category mutation
  const deleteCategory = useMutation({
    mutationFn: async (categoryId: string) => {
      const { error } = await supabase
        .from("categories")
        .delete()
        .eq("id", categoryId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Category deleted",
        description: "The category has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      setIsDeleteDialogOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete category: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setIsDialogOpen(true);
  };

  const handleDelete = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedCategory(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h3 className="text-xl font-bold text-red-600">Error loading categories</h3>
        <p>{(error as Error).message}</p>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Categories</h1>
          <p className="text-gray-500">Manage your product categories</p>
        </div>
        <div className="flex items-center space-x-2">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "table" | "sort")} className="mr-4">
            <TabsList>
              <TabsTrigger value="table">
                <List className="h-4 w-4 mr-1" />
                Table View
              </TabsTrigger>
              <TabsTrigger value="sort">
                <GripVertical className="h-4 w-4 mr-1" />
                Sort View
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <Button
            onClick={() => {
              setSelectedCategory(null);
              setIsDialogOpen(true);
            }}
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add Category
          </Button>
        </div>
      </div>

      {categories && categories.length > 0 ? (
        <div>
          {viewMode === "table" ? (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Image</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Slug</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Display Order</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell>
                          {category.image ? (
                            <div className="w-10 h-10 overflow-hidden rounded">
                              <AspectRatio ratio={1}>
                                <img 
                                  src={category.image} 
                                  alt={category.name} 
                                  className="object-cover w-full h-full"
                                  onError={(e) => {
                                    e.currentTarget.src = 'https://placehold.co/100x100?text=No+Image';
                                  }}
                                />
                              </AspectRatio>
                            </div>
                          ) : (
                            <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                              <span className="text-xs text-gray-500">No img</span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">{category.name}</TableCell>
                        <TableCell>
                          {category.parent_id ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Subcategory of {categories.find(c => c.id === category.parent_id)?.name || 'Unknown'}
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Main Category
                            </span>
                          )}
                        </TableCell>
                        <TableCell>{category.slug}</TableCell>
                        <TableCell>
                          {category.description 
                            ? category.description.length > 50 
                              ? `${category.description.substring(0, 50)}...` 
                              : category.description
                            : "-"}
                        </TableCell>
                        <TableCell>{category.display_order}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEdit(category)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(category)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {/* Main Categories */}
              <SimpleCategoryOrderer
                categories={categories}
                onEdit={handleEdit}
                onDelete={handleDelete}
                parentId={null}
                title="Main Categories"
              />
              
              {/* Group subcategories by parent */}
              {categories
                .filter(cat => !cat.parent_id) // Get main categories
                .sort((a, b) => a.display_order - b.display_order)
                .map(mainCategory => {
                  // Check if this main category has any subcategories
                  const hasSubcategories = categories.some(cat => cat.parent_id === mainCategory.id);
                  
                  if (!hasSubcategories) return null;
                  
                  return (
                    <SimpleCategoryOrderer
                      key={mainCategory.id}
                      categories={categories}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      parentId={mainCategory.id}
                      title={`${mainCategory.name} Subcategories`}
                    />
                  );
                })}
            </div>
          )}
        </div>
      ) : (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>No Categories Found</CardTitle>
            <CardDescription>
              You haven't added any categories to your store yet.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              onClick={() => {
                setSelectedCategory(null);
                setIsDialogOpen(true);
              }}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add Your First Category
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Category Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {selectedCategory ? "Edit Category" : "Add New Category"}
            </DialogTitle>
            <DialogDescription>
              Enter the category details below to {selectedCategory ? "update" : "create"} a category.
            </DialogDescription>
          </DialogHeader>
          <CategoryForm
            category={selectedCategory}
            onSuccess={handleCloseDialog}
            onCancel={handleCloseDialog}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedCategory?.name}"? This will also affect any products associated with this category.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedCategory && deleteCategory.mutate(selectedCategory.id)}
              disabled={deleteCategory.isPending}
            >
              {deleteCategory.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
