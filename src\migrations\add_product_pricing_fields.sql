-- Add new pricing fields to the products table

-- Add cost_price column
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS cost_price DECIMAL(10, 2);

-- Add pricing_model column
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS pricing_model VARCHAR(20);

-- Add bulk_pricing_tiers column
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS bulk_pricing_tiers TEXT;

-- Add option price adjustment columns
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS option_price_adjustment1 TEXT,
ADD COLUMN IF NOT EXISTS option_price_adjustment2 TEXT,
ADD COLUMN IF NOT EXISTS option_price_adjustment3 TEXT;

-- Add comment to explain the format of price adjustments
COMMENT ON COLUMN products.option_price_adjustment1 IS 'Price adjustments for option values (e.g., 0;+2.00;-1.50)';
COMMENT ON COLUMN products.option_price_adjustment2 IS 'Price adjustments for option values (e.g., 0;+1.50;-2.50)';
COMMENT ON COLUMN products.option_price_adjustment3 IS 'Price adjustments for option values (e.g., 0;-1.00;+3.50)';
COMMENT ON COLUMN products.pricing_model IS 'Pricing model: standard, variant, or bulk';
COMMENT ON COLUMN products.bulk_pricing_tiers IS 'Format: quantity:discount;quantity:discount (e.g., 5:10%;10:15%;20:20%)';
COMMENT ON COLUMN products.cost_price IS 'Purchase cost (not shown to customers)';
