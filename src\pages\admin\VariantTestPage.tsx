import React, { useState, useEffect } from 'react';
// No need to import AdminLayout as it's provided by the router
import { VariantComponentsTest } from '@/test/variant-components-test';
import { customSupabase } from '@/integrations/supabase/customClient';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

export default function VariantTestPage() {
  const [products, setProducts] = useState<any[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [selectedProductName, setSelectedProductName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await customSupabase
          .from('products')
          .select('id, name')
          .order('name')
          .limit(50);

        if (error) {
          throw error;
        }

        setProducts(data || []);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Handle product selection
  const handleProductChange = (productId: string) => {
    setSelectedProductId(productId);
    const product = products.find(p => p.id === productId);
    setSelectedProductName(product?.name || '');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Variant System Test Page</h1>
      <p className="text-muted-foreground">
        Use this page to test the variant-related components and ensure they're working correctly.
      </p>

        <Card>
          <CardHeader>
            <CardTitle>Select a Product</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="product-select">Product</Label>
                <Select
                  value={selectedProductId}
                  onValueChange={handleProductChange}
                  disabled={isLoading}
                >
                  <SelectTrigger id="product-select">
                    <SelectValue placeholder="Select a product" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {selectedProductId && (
          <VariantComponentsTest
            productId={selectedProductId}
            productName={selectedProductName}
          />
        )}
      </div>
  );
}
