import React, { useState } from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { useAuth } from '@/hooks/auth.basic';
import { toast } from '@/components/ui/use-toast';

interface WishlistButtonProps {
  productId: string;
  className?: string;
}

/**
 * Simple Wishlist Button that works without WishlistsProvider
 * Shows a basic heart icon and prompts user to sign in
 */
const SimpleWishlistButton: React.FC<WishlistButtonProps> = ({ productId, className }) => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!user) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to use the wishlist feature',
        variant: 'destructive',
      });
      return;
    }

    // For now, just show a message that wishlist functionality is being loaded
    toast({
      title: 'Wishlist feature loading',
      description: 'Wishlist functionality is being initialized...',
    });
  };

  return (
    <button
      onClick={handleClick}
      className={`p-1.5 rounded-full bg-white text-gray-400 hover:text-red-500 transition-colors duration-200 ${className}`}
      aria-label="Add to wishlist"
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-5 w-5 animate-spin" />
      ) : (
        <Heart className="h-5 w-5" />
      )}
    </button>
  );
};

export default SimpleWishlistButton;
