
import { useState, useEffect } from 'react';

interface UseCarouselProps {
  images: string[];
  autoplayInterval?: number;
}

interface UseCarouselReturn {
  currentIndex: number;
  setCurrentIndex: React.Dispatch<React.SetStateAction<number>>;
  direction: number;
  setDirection: React.Dispatch<React.SetStateAction<number>>;
  isAutoPlaying: boolean;
  setIsAutoPlaying: React.Dispatch<React.SetStateAction<boolean>>;
  handlePrevious: () => void;
  handleNext: () => void;
  handleDotClick: (index: number) => void;
}

export const useCarousel = ({
  images,
  autoplayInterval = 8000 // Increased to 8 seconds for smoother transitions
}: UseCarouselProps): UseCarouselReturn => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right
  
  // Auto-advance carousel with improved timing
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, autoplayInterval);
    
    return () => clearInterval(interval);
  }, [isAutoPlaying, images.length, autoplayInterval]);

  const handlePrevious = () => {
    setIsAutoPlaying(false);
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };

  const handleNext = () => {
    setIsAutoPlaying(false);
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  const handleDotClick = (index: number) => {
    setIsAutoPlaying(false);
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
  };

  return {
    currentIndex,
    setCurrentIndex,
    direction,
    setDirection,
    isAutoPlaying,
    setIsAutoPlaying,
    handlePrevious,
    handleNext,
    handleDotClick
  };
};
