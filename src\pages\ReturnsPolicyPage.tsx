import { motion } from 'framer-motion';
import { RefreshCw, CheckCircle, XCircle, HelpCircle, ArrowLeftRight } from 'lucide-react';
import PageHeader from '../components/PageHeader';

const ReturnsPolicyPage = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="bg-clay-50 min-h-screen">
      <PageHeader
        title="Returns & Refunds Policy"
        description="Our commitment to your satisfaction"
        bgImage="/images/headers/returns-policy.jpg"
      />

      <motion.div 
        className="container mx-auto px-4 py-12"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Introduction */}
        <motion.div 
          className="max-w-4xl mx-auto mb-12"
          variants={itemVariants}
        >
          <div className="flex items-center mb-6">
            <RefreshCw className="h-8 w-8 text-sage-600 mr-3" />
            <h2 className="text-2xl font-bold text-clay-900">Our Returns Policy</h2>
          </div>
          <p className="text-clay-700 mb-4">
            At BITS N BONGS, we want you to be completely satisfied with your purchase. We understand that sometimes a product may not meet your expectations, which is why we've created a straightforward returns and refunds policy.
          </p>
          <p className="text-clay-700">
            This policy outlines the process for returning items, eligibility requirements, refund methods, and more. Please read it carefully before making a return.
          </p>
          <p className="text-clay-600 italic mt-4">Last updated: May 18, 2025</p>
        </motion.div>

        {/* Return Eligibility */}
        <motion.div 
          className="max-w-4xl mx-auto mb-12 p-8 bg-white rounded-xl shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-6">
            <CheckCircle className="h-8 w-8 text-sage-600 mr-3" />
            <h2 className="text-2xl font-bold text-clay-900">Return Eligibility</h2>
          </div>
          
          <p className="text-clay-700 mb-6">
            You may return most new, unopened items within 30 days of delivery for a full refund. We also accept returns of opened items within 14 days if the item is defective, damaged, or not as described.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-green-50 p-5 rounded-lg border-l-4 border-green-500">
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Eligible for Return</h3>
              <ul className="list-disc pl-6 text-clay-700 space-y-1">
                <li>Unopened items (within 30 days)</li>
                <li>Defective or damaged items (within 14 days)</li>
                <li>Items not as described (within 14 days)</li>
                <li>Incorrect items shipped to you</li>
              </ul>
            </div>
            <div className="bg-red-50 p-5 rounded-lg border-l-4 border-red-500">
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Not Eligible for Return</h3>
              <ul className="list-disc pl-6 text-clay-700 space-y-1">
                <li>Used or damaged items (unless received damaged)</li>
                <li>Consumable products that have been opened</li>
                <li>Items marked as "Final Sale" or "Non-Returnable"</li>
                <li>Gift cards</li>
                <li>Items returned more than 30 days after delivery</li>
              </ul>
            </div>
          </div>
          
          <p className="text-clay-700">
            For hygiene reasons, certain products cannot be returned once opened or used, unless they are defective. These include but are not limited to: mouthpieces, certain accessories, and any product that comes into direct contact with the mouth.
          </p>
        </motion.div>

        {/* Return Process */}
        <motion.div 
          className="max-w-4xl mx-auto mb-12 p-8 bg-white rounded-xl shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-6">
            <ArrowLeftRight className="h-8 w-8 text-sage-600 mr-3" />
            <h2 className="text-2xl font-bold text-clay-900">Return Process</h2>
          </div>
          
          <p className="text-clay-700 mb-6">
            To initiate a return, please follow these steps:
          </p>

          <div className="relative mb-8">
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-sage-200"></div>
            
            <div className="relative pl-12 pb-8">
              <div className="absolute left-2.5 top-1 w-5 h-5 rounded-full bg-sage-500 flex items-center justify-center text-white font-bold text-xs">1</div>
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Contact Us</h3>
              <p className="text-clay-700">
                Email our customer service team at <a href="mailto:<EMAIL>" className="text-sage-600 hover:text-sage-700 underline"><EMAIL></a> or call us at +44 (123) 456-7890 to request a return. Please include your order number and reason for return.
              </p>
            </div>
            
            <div className="relative pl-12 pb-8">
              <div className="absolute left-2.5 top-1 w-5 h-5 rounded-full bg-sage-500 flex items-center justify-center text-white font-bold text-xs">2</div>
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Receive Return Authorization</h3>
              <p className="text-clay-700">
                We'll review your request and, if approved, provide you with a Return Authorization Number (RA#) and return instructions. Please do not send items back without an RA#.
              </p>
            </div>
            
            <div className="relative pl-12 pb-8">
              <div className="absolute left-2.5 top-1 w-5 h-5 rounded-full bg-sage-500 flex items-center justify-center text-white font-bold text-xs">3</div>
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Package Your Return</h3>
              <p className="text-clay-700">
                Pack the items securely in their original packaging if possible. Include the RA# prominently on the outside of the package and any paperwork provided with your return authorization.
              </p>
            </div>
            
            <div className="relative pl-12">
              <div className="absolute left-2.5 top-1 w-5 h-5 rounded-full bg-sage-500 flex items-center justify-center text-white font-bold text-xs">4</div>
              <h3 className="text-lg font-semibold text-clay-800 mb-2">Ship Your Return</h3>
              <p className="text-clay-700">
                Send your return to the address provided in the return instructions. We recommend using a tracked shipping method. Return shipping costs are the responsibility of the customer unless the return is due to our error.
              </p>
            </div>
          </div>
          
          <div className="bg-clay-100 p-5 rounded-lg">
            <h3 className="text-lg font-semibold text-clay-800 mb-2">Processing Time</h3>
            <p className="text-clay-700">
              Once we receive your return, we'll inspect the item(s) and process your refund or exchange. This typically takes 3-5 business days. You'll receive an email notification when your return has been processed.
            </p>
          </div>
        </motion.div>

        {/* Refunds */}
        <motion.div 
          className="max-w-4xl mx-auto mb-12 p-8 bg-white rounded-xl shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-6">
            <XCircle className="h-8 w-8 text-sage-600 mr-3" />
            <h2 className="text-2xl font-bold text-clay-900">Refunds & Exchanges</h2>
          </div>
          
          <h3 className="text-xl font-semibold text-clay-800 mb-4">Refund Methods</h3>
          <p className="text-clay-700 mb-6">
            Refunds will be issued to the original payment method used for the purchase. Please allow 5-10 business days for the refund to appear in your account, depending on your payment provider's processing times.
          </p>

          <h3 className="text-xl font-semibold text-clay-800 mb-4">Refund Amount</h3>
          <p className="text-clay-700 mb-4">
            For eligible returns, we will refund:
          </p>
          <ul className="list-disc pl-6 mb-6 text-clay-700 space-y-2">
            <li>The full purchase price of the returned item(s)</li>
            <li>Original shipping costs (only if the return is due to our error)</li>
          </ul>
          <p className="text-clay-700 mb-6">
            Return shipping costs are generally not refunded unless the return is due to our error (such as sending the wrong item or a defective product).
          </p>

          <h3 className="text-xl font-semibold text-clay-800 mb-4">Exchanges</h3>
          <p className="text-clay-700 mb-4">
            If you'd prefer to exchange an item rather than receive a refund, please indicate this when requesting your return. Exchanges are subject to product availability. If the replacement item costs more than the original, you'll need to pay the difference. If it costs less, we'll refund the difference.
          </p>
          
          <div className="bg-sage-50 border-l-4 border-sage-500 p-4 rounded-r-lg mt-6">
            <p className="text-clay-800 font-medium">Please Note:</p>
            <p className="text-clay-700 mt-1">
              We reserve the right to refuse returns or to offer only partial refunds for items that are returned in a condition different from how they were received, show signs of use, or are missing parts or packaging.
            </p>
          </div>
        </motion.div>

        {/* Damaged or Defective Items */}
        <motion.div 
          className="max-w-4xl mx-auto mb-12 p-8 bg-white rounded-xl shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-6">
            <HelpCircle className="h-8 w-8 text-sage-600 mr-3" />
            <h2 className="text-2xl font-bold text-clay-900">Damaged or Defective Items</h2>
          </div>
          
          <p className="text-clay-700 mb-4">
            If you receive a damaged or defective item, please contact us within 48 hours of receipt. Please provide:
          </p>
          <ul className="list-disc pl-6 mb-6 text-clay-700 space-y-2">
            <li>Your order number</li>
            <li>A description of the damage or defect</li>
            <li>Photos of the damaged item and packaging (if applicable)</li>
          </ul>
          
          <p className="text-clay-700 mb-4">
            For damaged or defective items, we will cover the cost of return shipping and will send a replacement or issue a full refund, including original shipping costs.
          </p>
          
          <div className="bg-amber-50 border-l-4 border-amber-500 p-4 rounded-r-lg">
            <p className="text-clay-800 font-medium">Important:</p>
            <p className="text-clay-700 mt-1">
              Please do not return damaged items without contacting us first. We may ask you to provide additional information or to dispose of the item locally instead of returning it.
            </p>
          </div>
        </motion.div>

        {/* Contact Us */}
        <motion.div 
          className="max-w-4xl mx-auto text-center"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-clay-900 mb-4">Need Help with a Return?</h2>
          <p className="text-clay-700 mb-6">
            If you have any questions about our returns policy or need assistance with a return, please contact us:
          </p>
          <div className="inline-block bg-white px-6 py-4 rounded-lg shadow-sm">
            <p className="text-clay-800">By email: <a href="mailto:<EMAIL>" className="text-sage-600 hover:text-sage-700 underline"><EMAIL></a></p>
            <p className="text-clay-800">By phone: 0141 737 3717</p>
            <p className="text-clay-800">Customer service hours: Monday-Friday, 9AM-5PM GMT</p>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ReturnsPolicyPage;
