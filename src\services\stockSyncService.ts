import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";

// CSV row structure for stock data
interface StockCSVRow {
  sku?: string;
  product_name?: string;
  barcode?: string;
  stock_quantity?: string | number;
  price?: string | number;
  sale_price?: string | number;
  description?: string;
  category?: string;
  brand?: string;
  [key: string]: any; // Allow for additional fields
}

// Product matching result
interface ProductMatch {
  product: Product;
  matchType: 'sku' | 'name' | 'barcode' | 'fuzzy';
  confidence: number;
}

// Sync results
interface SyncResults {
  updated: number;
  activated: number;
  failed: number;
  unmatched: StockCSVRow[];
  incompleteProducts: IncompleteProduct[];
  summary: string;
}

// Incomplete product analysis
interface IncompleteProduct {
  id: string;
  name: string;
  sku?: string;
  issues: string[];
  csvMatch?: StockCSVRow;
  canBeActivated: boolean;
}

export class StockSyncService {
  /**
   * Main function to sync stock from CSV data
   */
  public async syncStockFromCSV(csvData: StockCSVRow[]): Promise<SyncResults> {
    console.log(`Starting stock sync for ${csvData.length} CSV rows`);
    
    const results: SyncResults = {
      updated: 0,
      activated: 0,
      failed: 0,
      unmatched: [],
      incompleteProducts: [],
      summary: ''
    };

    // First, analyze incomplete products
    results.incompleteProducts = await this.analyzeIncompleteProducts(csvData);

    // Process each CSV row
    for (const row of csvData) {
      try {
        const match = await this.findBestProductMatch(row);
        
        if (match) {
          const updateResult = await this.updateProductFromCSV(match.product, row);
          if (updateResult.updated) results.updated++;
          if (updateResult.activated) results.activated++;
        } else {
          results.unmatched.push(row);
        }
      } catch (error) {
        console.error('Error processing CSV row:', error);
        results.failed++;
      }
    }

    results.summary = this.generateSummary(results);
    return results;
  }

  /**
   * Find the best matching product for a CSV row
   */
  private async findBestProductMatch(csvRow: StockCSVRow): Promise<ProductMatch | null> {
    const matches: ProductMatch[] = [];

    // 1. Try exact SKU match (highest confidence)
    if (csvRow.sku) {
      const skuMatch = await this.findProductBySKU(csvRow.sku.toString());
      if (skuMatch) {
        matches.push({ product: skuMatch, matchType: 'sku', confidence: 1.0 });
      }
    }

    // 2. Try barcode match
    if (csvRow.barcode) {
      const barcodeMatch = await this.findProductByBarcode(csvRow.barcode.toString());
      if (barcodeMatch) {
        matches.push({ product: barcodeMatch, matchType: 'barcode', confidence: 0.95 });
      }
    }

    // 3. Try exact name match
    if (csvRow.product_name) {
      const nameMatch = await this.findProductByExactName(csvRow.product_name.toString());
      if (nameMatch) {
        matches.push({ product: nameMatch, matchType: 'name', confidence: 0.9 });
      }
    }

    // 4. Try fuzzy name matching
    if (csvRow.product_name && matches.length === 0) {
      const fuzzyMatches = await this.findProductsByFuzzyName(csvRow.product_name.toString());
      matches.push(...fuzzyMatches);
    }

    // Return the best match (highest confidence)
    return matches.length > 0 ? matches.sort((a, b) => b.confidence - a.confidence)[0] : null;
  }

  /**
   * Find product by SKU
   */
  private async findProductBySKU(sku: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('sku', sku)
      .maybeSingle();

    if (error) {
      console.error('Error finding product by SKU:', error);
      return null;
    }

    return data;
  }

  /**
   * Find product by barcode (if you have a barcode field)
   */
  private async findProductByBarcode(barcode: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('barcode', barcode)
      .maybeSingle();

    if (error) {
      console.error('Error finding product by barcode:', error);
      return null;
    }

    return data;
  }

  /**
   * Find product by exact name match
   */
  private async findProductByExactName(name: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .ilike('name', name)
      .maybeSingle();

    if (error) {
      console.error('Error finding product by name:', error);
      return null;
    }

    return data;
  }

  /**
   * Find products using fuzzy name matching
   */
  private async findProductsByFuzzyName(name: string): Promise<ProductMatch[]> {
    // Get products with similar names
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .ilike('name', `%${name.split(' ')[0]}%`)
      .limit(10);

    if (error || !data) {
      return [];
    }

    // Calculate similarity scores
    const matches: ProductMatch[] = [];
    for (const product of data) {
      const similarity = this.calculateStringSimilarity(name.toLowerCase(), product.name.toLowerCase());
      if (similarity > 0.7) { // 70% similarity threshold
        matches.push({
          product,
          matchType: 'fuzzy',
          confidence: similarity * 0.8 // Reduce confidence for fuzzy matches
        });
      }
    }

    return matches;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }

  /**
   * Update product from CSV data
   */
  private async updateProductFromCSV(product: Product, csvRow: StockCSVRow): Promise<{ updated: boolean; activated: boolean }> {
    const updates: Partial<Product> = {
      last_synced_at: new Date().toISOString(),
      sync_status: 'synced'
    };

    let wasActivated = false;

    // Update stock quantity
    if (csvRow.stock_quantity !== undefined) {
      const stockQty = parseInt(csvRow.stock_quantity.toString()) || 0;
      updates.stock_quantity = stockQty;
      updates.in_stock = stockQty > 0;
    }

    // Update pricing if provided
    if (csvRow.price !== undefined) {
      updates.price = parseFloat(csvRow.price.toString()) || product.price;
    }

    if (csvRow.sale_price !== undefined) {
      updates.sale_price = parseFloat(csvRow.sale_price.toString()) || null;
    }

    // Update SKU if missing
    if (!product.sku && csvRow.sku) {
      updates.sku = csvRow.sku.toString();
    }

    // Check if product can be activated
    if (!product.is_active) {
      const canActivate = this.canProductBeActivated(product, updates, csvRow);
      if (canActivate) {
        updates.is_active = true;
        wasActivated = true;
      }
    }

    const { error } = await supabase
      .from('products')
      .update(updates)
      .eq('id', product.id);

    if (error) {
      console.error('Error updating product:', error);
      return { updated: false, activated: false };
    }

    return { updated: true, activated: wasActivated };
  }

  /**
   * Check if a product can be activated based on completeness
   */
  private canProductBeActivated(product: Product, updates: Partial<Product>, csvRow: StockCSVRow): boolean {
    // Check required fields
    const hasName = !!product.name;
    const hasPrice = !!(updates.price || product.price);
    const hasStock = !!(updates.stock_quantity || product.stock_quantity);
    const hasImage = !!product.image;
    const hasDescription = !!(product.description || csvRow.description);

    // Product can be activated if it has name, price, and stock
    // Image and description are nice-to-have but not required
    return hasName && hasPrice && hasStock;
  }

  /**
   * Analyze incomplete products and find potential matches in CSV
   */
  private async analyzeIncompleteProducts(csvData: StockCSVRow[]): Promise<IncompleteProduct[]> {
    // Get all inactive products
    const { data: inactiveProducts, error } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', false)
      .limit(1000);

    if (error || !inactiveProducts) {
      console.error('Error fetching inactive products:', error);
      return [];
    }

    const incompleteProducts: IncompleteProduct[] = [];

    for (const product of inactiveProducts) {
      const issues = this.identifyProductIssues(product);
      const csvMatch = this.findCSVMatchForProduct(product, csvData);
      const canBeActivated = csvMatch ? this.canProductBeActivated(product, {}, csvMatch) : false;

      incompleteProducts.push({
        id: product.id,
        name: product.name,
        sku: product.sku,
        issues,
        csvMatch,
        canBeActivated
      });
    }

    return incompleteProducts.sort((a, b) => {
      // Prioritize products that can be activated
      if (a.canBeActivated && !b.canBeActivated) return -1;
      if (!a.canBeActivated && b.canBeActivated) return 1;
      return a.issues.length - b.issues.length;
    });
  }

  /**
   * Identify what's missing from a product
   */
  private identifyProductIssues(product: Product): string[] {
    const issues: string[] = [];

    if (!product.name || product.name.trim() === '') issues.push('Missing name');
    if (!product.price || product.price <= 0) issues.push('Missing or invalid price');
    if (!product.image) issues.push('Missing image');
    if (!product.description) issues.push('Missing description');
    if (!product.sku) issues.push('Missing SKU');
    if (product.stock_quantity === null || product.stock_quantity === undefined) issues.push('Missing stock quantity');
    if (!product.category_id) issues.push('Missing category');

    return issues;
  }

  /**
   * Find CSV row that might match an incomplete product
   */
  private findCSVMatchForProduct(product: Product, csvData: StockCSVRow[]): StockCSVRow | undefined {
    return csvData.find(row => {
      if (product.sku && row.sku && product.sku === row.sku.toString()) return true;
      if (product.name && row.product_name) {
        const similarity = this.calculateStringSimilarity(
          product.name.toLowerCase(),
          row.product_name.toString().toLowerCase()
        );
        return similarity > 0.8;
      }
      return false;
    });
  }

  /**
   * Generate summary of sync results
   */
  private generateSummary(results: SyncResults): string {
    const lines = [
      `Stock Sync Complete:`,
      `• ${results.updated} products updated`,
      `• ${results.activated} products activated`,
      `• ${results.unmatched.length} unmatched CSV rows`,
      `• ${results.failed} failed updates`,
      `• ${results.incompleteProducts.length} incomplete products analyzed`,
      `• ${results.incompleteProducts.filter(p => p.canBeActivated).length} products ready for activation`
    ];

    return lines.join('\n');
  }
}

// Export singleton instance
export const stockSyncService = new StockSyncService();
