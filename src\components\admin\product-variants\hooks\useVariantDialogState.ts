import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { ProductVariant } from '@/types/database-with-variants';

// Cache keys
const CACHE_KEY_BASE = 'variant-dialog-state';
const VARIANT_FORM_OPEN = 'variant-form-open';
const VARIANTS_DIALOG_OPEN = 'variants-dialog-open';
const BULK_GENERATOR_OPEN = 'bulk-generator-open';
const SELECTED_VARIANT = 'selected-variant';
const PRODUCT_ID = 'product-id';
const PRODUCT_NAME = 'product-name';
const PRODUCT_PRICE = 'product-price';

/**
 * Hook to manage persistent state for variant dialogs across tab navigation
 */
export function useVariantDialogState(initialProductId?: string, initialProductName?: string, initialProductPrice?: number) {
  const queryClient = useQueryClient();
  
  // Initialize state from cache or defaults
  const [isVariantFormOpen, setIsVariantFormOpen] = useState<boolean>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, VARIANT_FORM_OPEN]) === true;
  });
  
  const [isVariantsDialogOpen, setIsVariantsDialogOpen] = useState<boolean>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, VARIANTS_DIALOG_OPEN]) === true;
  });
  
  const [isBulkGeneratorOpen, setIsBulkGeneratorOpen] = useState<boolean>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, BULK_GENERATOR_OPEN]) === true;
  });
  
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, SELECTED_VARIANT]) as ProductVariant || null;
  });
  
  const [productId, setProductId] = useState<string>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, PRODUCT_ID]) as string || initialProductId || '';
  });
  
  const [productName, setProductName] = useState<string>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, PRODUCT_NAME]) as string || initialProductName || '';
  });
  
  const [productPrice, setProductPrice] = useState<number>(() => {
    return queryClient.getQueryData([CACHE_KEY_BASE, PRODUCT_PRICE]) as number || initialProductPrice || 0;
  });
  
  // Update cache when state changes
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, VARIANT_FORM_OPEN], isVariantFormOpen);
  }, [isVariantFormOpen, queryClient]);
  
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, VARIANTS_DIALOG_OPEN], isVariantsDialogOpen);
  }, [isVariantsDialogOpen, queryClient]);
  
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, BULK_GENERATOR_OPEN], isBulkGeneratorOpen);
  }, [isBulkGeneratorOpen, queryClient]);
  
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, SELECTED_VARIANT], selectedVariant);
  }, [selectedVariant, queryClient]);
  
  useEffect(() => {
    if (initialProductId) {
      setProductId(initialProductId);
      queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_ID], initialProductId);
    }
  }, [initialProductId, queryClient]);
  
  useEffect(() => {
    if (initialProductName) {
      setProductName(initialProductName);
      queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_NAME], initialProductName);
    }
  }, [initialProductName, queryClient]);
  
  useEffect(() => {
    if (initialProductPrice !== undefined) {
      setProductPrice(initialProductPrice);
      queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_PRICE], initialProductPrice);
    }
  }, [initialProductPrice, queryClient]);
  
  // Update product ID in cache
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_ID], productId);
  }, [productId, queryClient]);
  
  // Update product name in cache
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_NAME], productName);
  }, [productName, queryClient]);
  
  // Update product price in cache
  useEffect(() => {
    queryClient.setQueryData([CACHE_KEY_BASE, PRODUCT_PRICE], productPrice);
  }, [productPrice, queryClient]);
  
  // Handler for editing a variant
  const handleEditVariant = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setIsVariantFormOpen(true);
  };
  
  // Handler for adding a new variant
  const handleAddVariant = () => {
    setSelectedVariant(null);
    setIsVariantFormOpen(true);
  };
  
  // Handler for variant success (create/update/delete)
  const handleVariantSuccess = () => {
    // Don't automatically close dialogs - let the user decide
    // This helps prevent the dialogs from disappearing unexpectedly
  };
  
  // Clear all dialog state
  const clearDialogState = () => {
    setIsVariantFormOpen(false);
    setIsVariantsDialogOpen(false);
    setIsBulkGeneratorOpen(false);
    setSelectedVariant(null);
    
    queryClient.setQueryData([CACHE_KEY_BASE, VARIANT_FORM_OPEN], false);
    queryClient.setQueryData([CACHE_KEY_BASE, VARIANTS_DIALOG_OPEN], false);
    queryClient.setQueryData([CACHE_KEY_BASE, BULK_GENERATOR_OPEN], false);
    queryClient.setQueryData([CACHE_KEY_BASE, SELECTED_VARIANT], null);
  };
  
  return {
    isVariantFormOpen,
    setIsVariantFormOpen,
    isVariantsDialogOpen,
    setIsVariantsDialogOpen,
    isBulkGeneratorOpen,
    setIsBulkGeneratorOpen,
    selectedVariant,
    setSelectedVariant,
    productId,
    setProductId,
    productName,
    setProductName,
    productPrice,
    setProductPrice,
    handleEditVariant,
    handleAddVariant,
    handleVariantSuccess,
    clearDialogState
  };
}
