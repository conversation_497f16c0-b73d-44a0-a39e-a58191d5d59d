import React, { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSupabase } from "@/lib/supabase/provider";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, RefreshCw, Save, Loader2 } from "lucide-react";
import { SimpleProductForm } from "@/components/admin/SimpleProductForm";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Define types
interface Product {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  category_id: string | null;
  brand_id: string | null;
  image: string | null;
}

interface FilterCategory {
  id: string;
  name: string;
  display_name: string;
}

interface FilterOption {
  id: string;
  category_id: string;
  name: string;
  display_name: string;
}

interface Brand {
  id: string;
  name: string;
}

export function ProductFilterManager() {
  const { supabase } = useSupabase();
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  
  // State for filters
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("all");
  const [selectedBrandId, setSelectedBrandId] = useState<string>("all");
  const [activeFilter, setActiveFilter] = useState<string>("all"); // "active", "inactive", or "all"
  const [isEditingProduct, setIsEditingProduct] = useState<boolean>(false);
  const [isEditingFilters, setIsEditingFilters] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [isScanningProducts, setIsScanningProducts] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const queryClient = useQueryClient();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch product categories
  const { data: productCategories = [] } = useQuery({
    queryKey: ["product-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");
      
      if (error) {
        console.error("Error fetching categories:", error);
        return [];
      }
      
      return data || [];
    },
  });

  // Fetch brands
  const { data: brands = [] } = useQuery({
    queryKey: ["brands"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("brands")
        .select("*")
        .order("name");
      
      if (error) {
        console.error("Error fetching brands:", error);
        return [];
      }
      
      return data || [];
    },
  });

  // Fetch seed products with filtering
  const fetchProducts = useCallback(async () => {
    try {
      console.log('Fetching products with filters:', {
        activeFilter,
        selectedCategoryId,
        searchQuery: debouncedSearchQuery
      });

      // Determine the category ID to filter by
      const categoryId = selectedCategoryId === "all" ? null : selectedCategoryId;
      
      // Set active only based on active filter
      const showActiveOnly = activeFilter === "active";
      const showInactiveOnly = activeFilter === "inactive";

      // Use RPC call to get seed products
      const { data, error } = await supabase.rpc('get_seed_products', {
        p_active_only: showActiveOnly,
        p_category_id: categoryId,
        p_search_term: debouncedSearchQuery || null
      });

      if (error) {
        console.error('Error fetching products:', error);
        return [];
      }

      // If we're showing only active products, we're done
      if (activeFilter === "active") {
        return data || [];
      }

      // If we need inactive products (either all or inactive only)
      // Use the find_inactive_seed_products function to get inactive seed products
      const { data: inactiveProducts, error: inactiveError } = await supabase.rpc('find_inactive_seed_products');
      
      if (inactiveError) {
        console.error('Error fetching inactive products:', inactiveError);
      }
      
      // Combine active and inactive products based on filter
      const activeProductsArray = activeFilter === "inactive" ? [] : (Array.isArray(data) ? data : []);
      
      // Filter inactive products to only include seed-related ones
      // This uses name matching to identify likely seed products
      let inactiveProductsArray = [];
      if (Array.isArray(inactiveProducts)) {
        inactiveProductsArray = inactiveProducts.filter(product => {
          const name = product.name?.toLowerCase() || '';
          // Check if product name contains seed-related keywords
          return (
            name.includes('seed') || 
            name.includes('germination') || 
            name.includes('sprouting') ||
            name.includes('plant') ||
            name.includes('garden') ||
            name.includes('grow') ||
            name.includes('flower') ||
            name.includes('herb')
          );
        });
      }
      
      // Create a Set of IDs to prevent duplicates
      const productIds = new Set();
      
      // Add active products first (if not showing inactive only)
      const combinedProducts = [];
      if (activeFilter !== "inactive") {
        for (const product of activeProductsArray) {
          if (product.id && !productIds.has(product.id)) {
            productIds.add(product.id);
            combinedProducts.push(product);
          }
        }
      }
      
      // Then add filtered inactive products (if not showing active only)
      if (activeFilter !== "active") {
        for (const product of inactiveProductsArray) {
          if (product.id && !productIds.has(product.id)) {
            productIds.add(product.id);
            combinedProducts.push(product);
          }
        }
      }
      
      console.log(`Found ${activeProductsArray.length} active and ${inactiveProductsArray.length} seed-related inactive products`);
      
      // Apply brand filter if selected
      let filteredProducts = combinedProducts;
      if (selectedBrandId !== "all") {
        filteredProducts = filteredProducts.filter(product => product.brand_id === selectedBrandId);
      }

      return filteredProducts;
    } catch (error) {
      console.error('Error in fetchProducts:', error);
      return [];
    }
  }, [supabase, activeFilter, selectedCategoryId, selectedBrandId, debouncedSearchQuery]);

  // Fetch products query
  const {
    data: products = [],
    isLoading,
    isFetching,
    error,
    refetch
  } = useQuery({
    queryKey: ['seed-products', selectedCategoryId, selectedBrandId, activeFilter, debouncedSearchQuery, currentPage],
    queryFn: fetchProducts,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Log any errors
  useEffect(() => {
    if (error) {
      console.error("Products query error:", error);
    }
  }, [error]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    if (!products || products.length === 0) return 1;
    return Math.ceil(products.length / pageSize);
  }, [products, pageSize]);

  // Calculate pagination
  const paginatedProducts = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return products?.slice(start, end) || [];
  }, [products, currentPage, pageSize]);

  // Handler for opening the product edit modal
  const handleEditProduct = (product: Product) => {
    console.log('Opening product edit modal for:', product.name);
    setSelectedProduct(product);
    setIsEditingProduct(true);
  };
  
  // Handle product status toggle
  const handleToggleProductStatus = async (product: Product) => {
    try {
      // Update the product status in the database
      const { error } = await supabase
        .from("products")
        .update({
          is_active: !product.is_active
        })
        .eq("id", product.id);

      if (error) {
        toast.error(`Error updating product status: ${error.message}`);
        return;
      }

      // Success
      toast.success(`Product ${product.is_active ? 'deactivated' : 'activated'} successfully`);
      // Invalidate both queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['seed-products'] });
    } catch (error) {
      console.error("Error toggling product status:", error);
      toast.error("Failed to update product status");
    }
  };

  // Render the component
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Product Filter Manager</CardTitle>
          <CardDescription>
            Manage filters for your products
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col space-y-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Input
                  placeholder="Search products..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="w-full sm:w-48">
                  <Select
                    value={selectedCategoryId}
                    onValueChange={setSelectedCategoryId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {productCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="w-full sm:w-48">
                  <Select
                    value={selectedBrandId}
                    onValueChange={setSelectedBrandId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Brand" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Brands</SelectItem>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            
            {/* Active/Inactive Filter Controls */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 bg-muted p-4 rounded-md border">
              <div className="flex flex-col space-y-1 min-w-[180px]">
                <Label htmlFor="active-filter" className="font-medium">Product Status</Label>
                <Select 
                  value={activeFilter} 
                  onValueChange={(value) => {
                    console.log('Setting active filter to:', value);
                    setActiveFilter(value);
                    // Reset to page 1 when changing filter
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger id="active-filter" className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active Products Only</SelectItem>
                    <SelectItem value="inactive">Inactive Products Only</SelectItem>
                    <SelectItem value="all">All Products</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1 flex items-center gap-4">
                {activeFilter === "active" && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-sm px-3 py-1">
                    Showing active products only
                  </Badge>
                )}
                {activeFilter === "inactive" && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-sm px-3 py-1">
                    Showing inactive products only
                  </Badge>
                )}
                {activeFilter === "all" && (
                  <Badge variant="outline" className="text-sm px-3 py-1">
                    Showing both active and inactive products
                  </Badge>
                )}
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-auto" 
                  onClick={() => {
                    // Force refetch all data
                    queryClient.invalidateQueries({ queryKey: ['seed-products'] });
                    queryClient.invalidateQueries({ queryKey: ['products'] });
                    toast.success('Refreshed product data');
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Rescan Products
                </Button>
              </div>
            </div>
          </div>
          
          {/* Products Table */}
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : !products || products.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No products found matching your filters.</p>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Brand</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.name} 
                            className="w-16 h-16 object-cover rounded-md"
                          />
                        ) : (
                          <div className="w-16 h-16 bg-muted rounded-md flex items-center justify-center text-muted-foreground">
                            No image
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>
                        {productCategories.find(cat => cat.id === product.category_id)?.name || 'Uncategorized'}
                      </TableCell>
                      <TableCell>
                        {brands.find(brand => brand.id === product.brand_id)?.name || 'No brand'}
                      </TableCell>
                      <TableCell>
                        {product.is_active ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center space-x-2 justify-end">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEditProduct(product)}
                          >
                            View Details
                          </Button>
                          <Button
                            variant={product.is_active ? "destructive" : "default"}
                            size="sm"
                            onClick={() => handleToggleProductStatus(product)}
                          >
                            {product.is_active ? "Deactivate" : "Activate"}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          
          {/* Pagination */}
          {!isLoading && products && products.length > 0 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {paginatedProducts.length} of {products.length} products
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  disabled={currentPage >= totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Edit Dialog - Using SimpleProductForm */}
      {isEditingProduct && selectedProduct && (
        <Dialog open={isEditingProduct} onOpenChange={setIsEditingProduct}>
          <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Seed Product</DialogTitle>
              <DialogDescription>
                Update product details and assign to the seed category
              </DialogDescription>
            </DialogHeader>
            
            <div className="mt-4">
              <SimpleProductForm 
                product={selectedProduct as any}
                onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['products'] });
                  // Also invalidate the seed products query
                  queryClient.invalidateQueries({ queryKey: ['seed-products'] });
                  setIsEditingProduct(false);
                  toast.success('Product updated successfully');
                }}
                onCancel={() => setIsEditingProduct(false)}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
