import { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface VideoHeroProps {
  title: string;
  subtitle: string;
  videoUrl: string;
  posterImage?: string;
  ctaText?: string;
  ctaAction?: () => void;
}

const VideoHero = ({ 
  title, 
  subtitle, 
  videoUrl, 
  posterImage, 
  ctaText, 
  ctaAction 
}: VideoHeroProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const videoBgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Auto-play video when component mounts
    const playVideo = async () => {
      if (videoRef.current) {
        try {
          // Reset the video
          videoRef.current.currentTime = 0;
          // Play with muted to avoid autoplay restrictions
          videoRef.current.muted = true;
          await videoRef.current.play();
        } catch (error) {
          console.error("Error playing video:", error);
        }
      }
    };
    
    playVideo();
    
    // Clean up
    return () => {
      if (videoRef.current) {
        videoRef.current.pause();
      }
    };
  }, []);

  // Parallax effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      if (videoBgRef.current) {
        videoBgRef.current.style.transform = `translateY(${scrollY * 0.2}px)`;
      }
      if (contentRef.current) {
        contentRef.current.style.transform = `translateY(${scrollY * 0.4}px)`;
        contentRef.current.style.opacity = `${1 - scrollY * 0.0015}`;
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <div className="relative w-full h-[70vh] overflow-hidden pb-8">
      {/* Video background with parallax */}
      <div ref={videoBgRef} className="absolute inset-0 will-change-transform transition-transform duration-200 ease-out">
        <video 
          ref={videoRef}
          className="w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          poster={posterImage}
        >
          <source src={videoUrl} type="video/webm" />
          Your browser does not support the video tag.
        </video>
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
      </div>
      
      {/* Content with parallax and animated text */}
      <div ref={contentRef} className="relative h-full flex flex-col items-center justify-center text-center px-4 z-10 will-change-transform transition-all duration-300 ease-out">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-extrabold mb-6 text-white drop-shadow-lg animate-hero-bounce-in">
            Premium CBD, Seeds & Smoking Accessories
          </h1>
          <p className="text-lg md:text-xl mb-8 text-white/90 max-w-2xl mx-auto animate-fade-in-delayed">
            {subtitle}
          </p>
          
          {ctaText && (
            <Button 
              onClick={ctaAction}
              className="bg-sage-500 hover:bg-sage-600 text-white px-8 py-6 text-lg shadow-lg transform transition-transform duration-300 hover:scale-105 animate-fade-in-delayed"
            >
              {ctaText}
            </Button>
          )}
        </div>
      </div>
      {/* Scroll indicator animation - absolutely positioned above the wave, with accent color and shadow */}
      <div className="absolute left-1/2 -translate-x-1/2 bottom-6 z-30 flex justify-center animate-bounce">
        <div className="w-8 h-12 rounded-full border-2 border-green-500 shadow-lg bg-white/80 flex items-start justify-center">
          <div className="w-1 h-3 bg-green-500 rounded-full mt-2 animate-pulse shadow-md"></div>
        </div>
      </div>
    </div>
  );
};

export default VideoHero;
