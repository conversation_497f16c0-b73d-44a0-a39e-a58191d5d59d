-- Create a database function to save addresses
-- This bypasses RLS policies by using security definer
CREATE OR REPLACE FUNCTION public.save_user_address(
  p_user_id UUID,
  p_full_name TEXT,
  p_street TEXT,
  p_city TEXT,
  p_state TEXT,
  p_postal_code TEXT,
  p_country TEXT,
  p_phone TEXT,
  p_is_default BOOLEAN
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_address_id UUID;
  v_result JSONB;
BEGIN
  -- If this is the default address, update all other addresses to not be default
  IF p_is_default THEN
    UPDATE public.addresses
    SET is_default = false
    WHERE user_id = p_user_id;
  END IF;

  -- Insert the new address
  INSERT INTO public.addresses (
    user_id,
    full_name,
    street,
    city,
    state,
    postal_code,
    country,
    phone,
    is_default,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_full_name,
    p_street,
    p_city,
    p_state,
    p_postal_code,
    p_country,
    p_phone,
    p_is_default,
    NOW(),
    NOW()
  )
  RETURNING id INTO v_address_id;

  -- Get the inserted address
  SELECT jsonb_build_object(
    'id', id,
    'user_id', user_id,
    'full_name', full_name,
    'street', street,
    'city', city,
    'state', state,
    'postal_code', postal_code,
    'country', country,
    'phone', phone,
    'is_default', is_default,
    'created_at', created_at,
    'updated_at', updated_at
  ) INTO v_result
  FROM public.addresses
  WHERE id = v_address_id;

  RETURN v_result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.save_user_address TO authenticated;
