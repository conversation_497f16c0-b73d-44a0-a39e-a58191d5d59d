import { supabase } from "@/integrations/supabase/client";
import { CartItem } from "@/context/CartContext";
import { StripePaymentService } from "./stripePaymentService";

// Payment provider types
export type PaymentProvider = 'paypal' | 'stripe';

// Payment status types
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';

// Payment session interface
export interface PaymentSession {
  id: string;
  provider: PaymentProvider;
  amount: number;
  currency: string;
  status: PaymentStatus;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// PayPal specific configuration
interface PayPalConfig {
  merchantEmail: string;
  returnUrl: string;
  cancelUrl: string;
  currency: string;
  locale: string;
}

// Stripe specific configuration
interface StripeConfig {
  publishableKey: string;
  secretKey: string;
  webhookSecret: string;
  currency: string;
  returnUrl: string;
  cancelUrl: string;
}

/**
 * Service to handle payment processing with different providers
 */
export class PaymentService {
  private paypalConfig: PayPalConfig;
  private stripeConfig: StripeConfig;
  
  constructor() {
    // Initialize with default config - should be loaded from environment variables in production
    this.paypalConfig = {
      merchantEmail: import.meta.env.VITE_PAYPAL_MERCHANT_EMAIL || '<EMAIL>',
      returnUrl: `${window.location.origin}/checkout/confirmation`,
      cancelUrl: `${window.location.origin}/checkout`,
      currency: 'GBP',
      locale: 'en_GB'
    };
    
    // Initialize Stripe config - these would be loaded from environment variables when available
    this.stripeConfig = {
      publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_placeholder',
      secretKey: import.meta.env.VITE_STRIPE_SECRET_KEY || 'sk_test_placeholder',
      webhookSecret: import.meta.env.VITE_STRIPE_WEBHOOK_SECRET || 'whsec_placeholder',
      currency: 'GBP',
      returnUrl: `${window.location.origin}/checkout/confirmation`,
      cancelUrl: `${window.location.origin}/checkout`
    };
  }

  /**
   * Create a PayPal payment session
   * @param items Cart items to be purchased
   * @param shipping Shipping cost
   * @param tax Tax amount
   * @returns PayPal checkout URL and session ID
   */
  public async createPayPalPayment(items: CartItem[], shipping: number, tax: number): Promise<{ checkoutUrl: string, sessionId: string }> {
    try {
      // Calculate total amount
      const itemTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const total = itemTotal + shipping + tax;
      
      // Generate a unique session ID
      const sessionId = crypto.randomUUID();
      
      // In a real implementation, this would call the PayPal API to create a payment
      // For now, we'll create a simulated PayPal checkout URL
      
      // Store the payment session in the database
      const { error } = await supabase
        .from('payment_sessions')
        .insert({
          id: sessionId,
          provider: 'paypal',
          amount: total,
          currency: this.paypalConfig.currency,
          status: 'pending',
          metadata: {
            items: items.map(item => ({
              id: item.id,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              options: item.selectedOptions
            })),
            shipping,
            tax
          }
        });
      
      if (error) throw error;
      
      // Construct PayPal checkout URL
      // In a real implementation, this would be returned from the PayPal API
      const checkoutUrl = this.constructPayPalCheckoutUrl(total, sessionId);
      
      return {
        checkoutUrl,
        sessionId
      };
    } catch (error) {
      console.error('Error creating PayPal payment:', error);
      throw error;
    }
  }

  /**
   * Construct a PayPal checkout URL
   * This is a simplified version for demonstration purposes
   * In a real implementation, you would use the PayPal SDK or API
   */
  private constructPayPalCheckoutUrl(amount: number, sessionId: string): string {
    const baseUrl = 'https://www.paypal.com/cgi-bin/webscr';
    const params = new URLSearchParams({
      cmd: '_xclick',
      business: this.paypalConfig.merchantEmail,
      currency_code: this.paypalConfig.currency,
      amount: amount.toFixed(2),
      item_name: 'Order from BITS N BONGS',
      invoice: sessionId,
      return: this.paypalConfig.returnUrl,
      cancel_return: this.paypalConfig.cancelUrl,
      notify_url: `${window.location.origin}/api/webhooks/paypal`
    });
    
    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Process a Stripe payment confirmation
   * @param paymentIntentId The Stripe payment intent ID
   * @returns The order ID
   */
  public async processStripePaymentConfirmation(paymentIntentId: string): Promise<string> {
    const stripeService = new StripePaymentService();
    return stripeService.processStripePaymentConfirmation(paymentIntentId);
  }

  /**
   * Verify a PayPal payment
   * @param sessionId The payment session ID
   * @param paypalTransactionId The PayPal transaction ID
   * @returns Whether the payment was successful
   */
  public async verifyPayPalPayment(sessionId: string, paypalTransactionId: string): Promise<boolean> {
    try {
      // In a real implementation, this would verify the payment with PayPal's API
      // For now, we'll simulate a successful verification
      
      // Update the payment session status
      const { error } = await supabase
        .from('payment_sessions')
        .update({
          status: 'completed',
          metadata: supabase.sql`jsonb_set(metadata, '{paypal_transaction_id}', '"${paypalTransactionId}"'::jsonb)`
        })
        .eq('id', sessionId);
      
      if (error) throw error;
      
      return true;
    } catch (error) {
      console.error('Error verifying PayPal payment:', error);
      return false;
    }
  }

  /**
   * Create a Stripe payment session
   * @param items Cart items to be purchased
   * @param shipping Shipping cost
   * @param tax Tax amount
   * @returns Client secret for Stripe Elements
   */
  public async createStripePayment(items: CartItem[], shipping: number, tax: number): Promise<{ clientSecret: string, sessionId: string }> {
    try {
      // Calculate total amount
      const itemTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const total = itemTotal + shipping + tax;
      
      // Generate a unique session ID
      const sessionId = crypto.randomUUID();
      
      // In a real implementation, this would call the Stripe API to create a PaymentIntent
      // For now, we'll create a simulated client secret
      const simulatedClientSecret = `pi_${sessionId}_secret_${Math.random().toString(36).substring(2, 15)}`;
      
      // Store the payment session in the database
      const { error } = await supabase
        .from('payment_sessions')
        .insert({
          id: sessionId,
          provider: 'stripe',
          amount: total,
          currency: this.stripeConfig.currency,
          status: 'pending',
          metadata: {
            items: items.map(item => ({
              id: item.id,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              options: item.selectedOptions
            })),
            shipping,
            tax,
            client_secret: simulatedClientSecret
          }
        });
      
      if (error) throw error;
      
      return {
        clientSecret: simulatedClientSecret,
        sessionId
      };
    } catch (error) {
      console.error('Error creating Stripe payment:', error);
      throw error;
    }
  }

  /**
   * Verify a Stripe payment
   * @param sessionId The payment session ID
   * @param stripePaymentIntentId The Stripe PaymentIntent ID
   * @returns Whether the payment was successful
   */
  public async verifyStripePayment(sessionId: string, stripePaymentIntentId: string): Promise<boolean> {
    try {
      // In a real implementation, this would verify the payment with Stripe's API
      // For now, we'll simulate a successful verification
      
      // Update the payment session status
      const { error } = await supabase
        .from('payment_sessions')
        .update({
          status: 'completed',
          metadata: supabase.sql`jsonb_set(metadata, '{stripe_payment_intent_id}', '"${stripePaymentIntentId}"'::jsonb)`
        })
        .eq('id', sessionId);
      
      if (error) throw error;
      
      return true;
    } catch (error) {
      console.error('Error verifying Stripe payment:', error);
      return false;
    }
  }

  /**
   * Process a successful payment and create an order
   * @param sessionId The payment session ID
   * @returns The created order ID
   */
  public async processSuccessfulPayment(sessionId: string): Promise<string> {
    try {
      // Get the payment session
      const { data: session, error: sessionError } = await supabase
        .from('payment_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();
      
      if (sessionError || !session) throw sessionError || new Error('Payment session not found');
      
      // Extract order details from session metadata
      const { items, shipping, tax } = session.metadata;
      const subtotal = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      const total = subtotal + shipping + tax;
      
      // Create the order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          payment_session_id: sessionId,
          payment_provider: session.provider,
          status: 'processing',
          subtotal,
          shipping_cost: shipping,
          tax_amount: tax,
          total_amount: total,
          items: items
        })
        .select()
        .single();
      
      if (orderError) throw orderError;
      
      return order.id;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }
}

// Create a singleton instance
let paymentService: PaymentService | null = null;

/**
 * Get the payment service instance
 */
export function getPaymentService(): PaymentService {
  if (!paymentService) {
    paymentService = new PaymentService();
  }
  
  return paymentService;
}