const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

const inputFile = 'transformed_variants.csv';
const outputFile = 'transformed_variants_fixed_new.csv';

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Process the CSV file
console.log(`Reading variants from ${inputFile}...`);
const variants = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    // Create a new variant object with only the fields we want
    const variant = {
      id: row.id,
      product_id: row.product_id,
      variant_name: row.variant_name,
      sku: row.sku,
      price: row.price,
      sale_price: row.sale_price,
      stock_quantity: row.stock_quantity,
      in_stock: row.in_stock,
      image: row.image,
      option_combination: row.option_combination,
      is_active: row.is_active,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
    variants.push(variant);
  })
  .on('end', async () => {
    console.log(`Read ${variants.length} variants from CSV`);

    // Write variants to CSV
    await csvWriter.writeRecords(variants);
    console.log(`Wrote ${variants.length} variants to ${outputFile}`);

    console.log('Transformation complete!');
  });
