/**
 * ImageQualityAssessor.ts
 * 
 * Assesses image quality and relevance for product images
 * Provides scoring based on multiple factors
 */

import { ProductImage, ImageQualityScore } from './types/ImageScrapingTypes';

/**
 * Quality assessment configuration
 */
interface QualityAssessorConfig {
  minDimensions?: { width: number; height: number };
  minQualityScore?: number;
  weightRelevance?: number;
  weightTechnical?: number;
  weightDimensions?: number;
  weightSource?: number;
}

/**
 * Image metadata
 */
interface ImageMetadata {
  width?: number;
  height?: number;
  format?: string;
  fileSize?: number;
}

/**
 * Assesses image quality and relevance
 */
export class ImageQualityAssessor {
  private minDimensions: { width: number; height: number };
  private minQualityScore: number;
  private weightRelevance: number;
  private weightTechnical: number;
  private weightDimensions: number;
  private weightSource: number;

  /**
   * Constructor
   * @param config - Quality assessor configuration
   */
  constructor(config: QualityAssessorConfig = {}) {
    this.minDimensions = config.minDimensions || { width: 400, height: 400 };
    this.minQualityScore = config.minQualityScore || 50;
    this.weightRelevance = config.weightRelevance || 0.4;
    this.weightTechnical = config.weightTechnical || 0.2;
    this.weightDimensions = config.weightDimensions || 0.2;
    this.weightSource = config.weightSource || 0.2;
  }

  /**
   * Assess image quality
   * @param image - Product image
   * @param productName - Product name
   * @param sourceReliability - Source reliability score
   * @returns Image quality score
   */
  async assessImageQuality(
    image: ProductImage,
    productName: string,
    sourceReliability: number = 5
  ): Promise<ImageQualityScore> {
    // Get image metadata if not already available
    const metadata = await this.getImageMetadata(image);
    
    // Update image with metadata
    if (metadata.width) image.width = metadata.width;
    if (metadata.height) image.height = metadata.height;
    if (metadata.format) image.format = metadata.format;
    if (metadata.fileSize) image.file_size = metadata.fileSize;
    
    // Calculate individual scores
    const relevanceScore = this.calculateRelevanceScore(image, productName);
    const technicalScore = this.calculateTechnicalScore(image);
    const dimensionsScore = this.calculateDimensionsScore(image);
    const sourceReliabilityScore = this.normalizeSourceReliability(sourceReliability);
    
    // Calculate overall score
    const overallScore = this.calculateOverallScore(
      relevanceScore,
      technicalScore,
      dimensionsScore,
      sourceReliabilityScore
    );
    
    // Create quality score object
    const qualityScore: ImageQualityScore = {
      overall_score: overallScore,
      relevance_score: relevanceScore,
      technical_score: technicalScore,
      dimensions_score: dimensionsScore,
      source_reliability_score: sourceReliabilityScore,
      factors: {
        has_product_in_alt: this.hasProductInAlt(image, productName),
        good_dimensions: this.hasGoodDimensions(image),
        from_product_page: this.isFromProductPage(image),
        not_thumbnail: this.isNotThumbnail(image),
        clear_image: this.isClearImage(image)
      }
    };
    
    // Update image quality score
    image.quality_score = overallScore;
    
    return qualityScore;
  }

  /**
   * Filter images by quality
   * @param images - Array of product images
   * @param minQualityScore - Minimum quality score
   * @returns Filtered array of product images
   */
  filterImagesByQuality(
    images: ProductImage[],
    minQualityScore: number = this.minQualityScore
  ): ProductImage[] {
    return images.filter(image => image.quality_score >= minQualityScore);
  }

  /**
   * Sort images by quality
   * @param images - Array of product images
   * @returns Sorted array of product images
   */
  sortImagesByQuality(images: ProductImage[]): ProductImage[] {
    return [...images].sort((a, b) => b.quality_score - a.quality_score);
  }

  /**
   * Get top quality images
   * @param images - Array of product images
   * @param count - Number of images to return
   * @param minQualityScore - Minimum quality score
   * @returns Top quality images
   */
  getTopQualityImages(
    images: ProductImage[],
    count: number = 5,
    minQualityScore: number = this.minQualityScore
  ): ProductImage[] {
    // Filter by quality
    const qualityImages = this.filterImagesByQuality(images, minQualityScore);
    
    // Sort by quality
    const sortedImages = this.sortImagesByQuality(qualityImages);
    
    // Return top images
    return sortedImages.slice(0, count);
  }

  /**
   * Calculate relevance score
   * @param image - Product image
   * @param productName - Product name
   * @returns Relevance score (0-100)
   */
  private calculateRelevanceScore(image: ProductImage, productName: string): number {
    let score = 0;
    
    // Check alt text
    if (this.hasProductInAlt(image, productName)) {
      score += 60;
    } else if (image.alt && image.alt.length > 0) {
      // Partial match
      const altText = image.alt.toLowerCase();
      const productWords = productName.toLowerCase().split(/\s+/);
      
      let matchCount = 0;
      for (const word of productWords) {
        if (word.length > 2 && altText.includes(word)) {
          matchCount++;
        }
      }
      
      if (matchCount > 0) {
        score += 30 * (matchCount / productWords.length);
      }
    }
    
    // Check URL for product name
    if (image.url) {
      const url = image.url.toLowerCase();
      const productNameLower = productName.toLowerCase();
      
      if (url.includes(productNameLower.replace(/\s+/g, '-')) || 
          url.includes(productNameLower.replace(/\s+/g, '_'))) {
        score += 30;
      } else {
        // Partial match
        const productWords = productNameLower.split(/\s+/);
        
        let matchCount = 0;
        for (const word of productWords) {
          if (word.length > 2 && url.includes(word)) {
            matchCount++;
          }
        }
        
        if (matchCount > 0) {
          score += 10 * (matchCount / productWords.length);
        }
      }
    }
    
    // Check if from product page
    if (this.isFromProductPage(image)) {
      score += 10;
    }
    
    // Normalize score to 0-100
    return Math.min(100, score);
  }

  /**
   * Calculate technical score
   * @param image - Product image
   * @returns Technical score (0-100)
   */
  private calculateTechnicalScore(image: ProductImage): number {
    let score = 50; // Start with neutral score
    
    // Check if image is a thumbnail
    if (!this.isNotThumbnail(image)) {
      score -= 30;
    }
    
    // Check image format
    if (image.format) {
      const format = image.format.toLowerCase();
      
      if (format === 'webp' || format === 'png') {
        score += 20;
      } else if (format === 'jpeg' || format === 'jpg') {
        score += 15;
      } else if (format === 'gif') {
        score += 5;
      }
    }
    
    // Check file size if available
    if (image.file_size) {
      const sizeKB = image.file_size / 1024;
      
      if (sizeKB > 100) {
        score += 20;
      } else if (sizeKB > 50) {
        score += 10;
      } else if (sizeKB > 20) {
        score += 5;
      }
    }
    
    // Check if clear image
    if (this.isClearImage(image)) {
      score += 10;
    }
    
    // Normalize score to 0-100
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Calculate dimensions score
   * @param image - Product image
   * @returns Dimensions score (0-100)
   */
  private calculateDimensionsScore(image: ProductImage): number {
    if (!image.width || !image.height) {
      return 50; // Neutral score if dimensions not available
    }
    
    const { width, height } = image;
    const minWidth = this.minDimensions.width;
    const minHeight = this.minDimensions.height;
    
    // Base score on dimensions
    let score = 0;
    
    if (width >= 1000 && height >= 1000) {
      score = 100; // Excellent dimensions
    } else if (width >= 800 && height >= 800) {
      score = 90; // Very good dimensions
    } else if (width >= 600 && height >= 600) {
      score = 80; // Good dimensions
    } else if (width >= minWidth && height >= minHeight) {
      score = 70; // Acceptable dimensions
    } else if (width >= minWidth / 2 && height >= minHeight / 2) {
      score = 40; // Below minimum but still usable
    } else {
      score = 10; // Too small
    }
    
    // Check aspect ratio
    const aspectRatio = width / height;
    
    if (aspectRatio > 0.9 && aspectRatio < 1.1) {
      score += 10; // Square-ish images are good for products
    } else if (aspectRatio > 0.7 && aspectRatio < 1.3) {
      score += 5; // Slightly rectangular is fine
    } else if (aspectRatio < 0.5 || aspectRatio > 2) {
      score -= 20; // Very non-square is bad for products
    }
    
    // Normalize score to 0-100
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Normalize source reliability
   * @param reliability - Source reliability (0-10)
   * @returns Normalized source reliability (0-100)
   */
  private normalizeSourceReliability(reliability: number): number {
    return Math.max(0, Math.min(100, reliability * 10));
  }

  /**
   * Calculate overall score
   * @param relevanceScore - Relevance score
   * @param technicalScore - Technical score
   * @param dimensionsScore - Dimensions score
   * @param sourceReliabilityScore - Source reliability score
   * @returns Overall score (0-100)
   */
  private calculateOverallScore(
    relevanceScore: number,
    technicalScore: number,
    dimensionsScore: number,
    sourceReliabilityScore: number
  ): number {
    const weightedScore = 
      (relevanceScore * this.weightRelevance) +
      (technicalScore * this.weightTechnical) +
      (dimensionsScore * this.weightDimensions) +
      (sourceReliabilityScore * this.weightSource);
    
    return Math.round(weightedScore);
  }

  /**
   * Check if image alt text contains product name
   * @param image - Product image
   * @param productName - Product name
   * @returns True if alt text contains product name
   */
  private hasProductInAlt(image: ProductImage, productName: string): boolean {
    if (!image.alt || !productName) return false;
    
    const altText = image.alt.toLowerCase();
    const productNameLower = productName.toLowerCase();
    
    return altText.includes(productNameLower);
  }

  /**
   * Check if image has good dimensions
   * @param image - Product image
   * @returns True if image has good dimensions
   */
  private hasGoodDimensions(image: ProductImage): boolean {
    if (!image.width || !image.height) return false;
    
    return image.width >= this.minDimensions.width && 
           image.height >= this.minDimensions.height;
  }

  /**
   * Check if image is from product page
   * @param image - Product image
   * @returns True if image is from product page
   */
  private isFromProductPage(image: ProductImage): boolean {
    if (!image.url) return false;
    
    const url = image.url.toLowerCase();
    
    return url.includes('/product/') || 
           url.includes('/products/') || 
           url.includes('/item/') || 
           url.includes('/p/');
  }

  /**
   * Check if image is not a thumbnail
   * @param image - Product image
   * @returns True if image is not a thumbnail
   */
  private isNotThumbnail(image: ProductImage): boolean {
    if (!image.url) return true;
    
    const url = image.url.toLowerCase();
    
    return !url.includes('thumb') && 
           !url.includes('small') && 
           !url.includes('tiny') && 
           !url.includes('icon');
  }

  /**
   * Check if image is clear
   * @param image - Product image
   * @returns True if image is clear
   */
  private isClearImage(image: ProductImage): boolean {
    // This is a placeholder - in a real implementation, we would
    // use image analysis to determine if the image is clear
    return true;
  }

  /**
   * Get image metadata
   * @param image - Product image
   * @returns Image metadata
   */
  private async getImageMetadata(image: ProductImage): Promise<ImageMetadata> {
    // If we already have metadata, return it
    if (image.width && image.height) {
      return {
        width: image.width,
        height: image.height,
        format: image.format,
        fileSize: image.file_size
      };
    }
    
    try {
      // In a real implementation, we would fetch the image and extract metadata
      // For now, we'll return placeholder metadata
      return {
        width: image.width || 0,
        height: image.height || 0,
        format: image.format || 'unknown',
        fileSize: image.file_size || 0
      };
    } catch (error) {
      console.error('Failed to get image metadata:', error);
      return {};
    }
  }
}
