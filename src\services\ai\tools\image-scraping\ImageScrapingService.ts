/**
 * ImageScrapingService.ts
 * 
 * Main implementation of the ImageScrapingService interface
 * Coordinates all components of the image scraping system
 */

import { PlaywrightMCPClient } from '../mcp-integration/PlaywrightMCPClient';
import { MCPConnectionManager } from '../mcp-integration/MCPConnectionManager';
import { MCPErrorHandler } from '../mcp-integration/MCPErrorHandler';
import { PlaywrightImageScraper } from './PlaywrightImageScraper';
import { ImageQualityAssessor } from './ImageQualityAssessor';
import { SourceManager } from './SourceManager';
import { BulkImageProcessor } from './BulkImageProcessor';
import {
  ImageScrapingService as IImageScrapingService,
  ProductImage,
  ImageSearchOptions,
  BulkProcessingReport,
  ImageQualityScore,
  RetailerSource
} from './types/ImageScrapingTypes';

/**
 * Service configuration
 */
interface ServiceConfig {
  mcpServerUrl?: string;
  maxConnections?: number;
  defaultSearchOptions?: ImageSearchOptions;
  sourcesFilePath?: string;
}

/**
 * Main implementation of the ImageScrapingService interface
 */
export class ImageScrapingService implements IImageScrapingService {
  private connectionManager: MCPConnectionManager;
  private errorHandler: MCPErrorHandler;
  private sourceManager: SourceManager;
  private qualityAssessor: ImageQualityAssessor;
  private defaultSearchOptions: ImageSearchOptions;

  /**
   * Constructor
   * @param config - Service configuration
   */
  constructor(config: ServiceConfig = {}) {
    // Create connection manager
    this.connectionManager = new MCPConnectionManager({
      maxConnections: config.maxConnections || 3
    });
    
    // Create error handler
    this.errorHandler = new MCPErrorHandler();
    
    // Create source manager
    this.sourceManager = new SourceManager({
      sourcesFilePath: config.sourcesFilePath
    });
    
    // Create quality assessor
    this.qualityAssessor = new ImageQualityAssessor();
    
    // Set default search options
    this.defaultSearchOptions = config.defaultSearchOptions || {
      max_images: 5,
      min_quality_score: 50,
      min_dimensions: { width: 400, height: 400 }
    };
  }

  /**
   * Find images for a single product
   * @param product - Product information
   * @param options - Search options
   * @returns Array of product images
   */
  async findProductImages(
    product: { name: string; category?: string; id?: string },
    options: ImageSearchOptions = {}
  ): Promise<ProductImage[]> {
    try {
      // Get MCP client
      const client = await this.connectionManager.getConnection();
      
      try {
        // Create image scraper
        const scraper = new PlaywrightImageScraper(
          client,
          this.sourceManager.getAllSources()
        );
        
        // Find images
        const images = await this.errorHandler.withRetry(
          () => scraper.findProductImages(product, {
            ...this.defaultSearchOptions,
            ...options
          }),
          { product }
        );
        
        // Return images
        return images;
      } finally {
        // Release client
        this.connectionManager.releaseConnection(client);
      }
    } catch (error) {
      console.error('Error finding product images:', error);
      throw error;
    }
  }

  /**
   * Process multiple products in bulk
   * @param products - Array of products
   * @param options - Search options
   * @returns Bulk processing report
   */
  async bulkProcessProducts(
    products: { name: string; category?: string; id: string }[],
    options: ImageSearchOptions = {}
  ): Promise<BulkProcessingReport> {
    try {
      // Get MCP client
      const client = await this.connectionManager.getConnection();
      
      try {
        // Create image scraper
        const scraper = new PlaywrightImageScraper(
          client,
          this.sourceManager.getAllSources()
        );
        
        // Create bulk processor
        const bulkProcessor = new BulkImageProcessor(
          scraper,
          this.qualityAssessor,
          this.sourceManager,
          {
            defaultSearchOptions: this.defaultSearchOptions
          }
        );
        
        // Process products
        const report = await this.errorHandler.withRetry(
          () => bulkProcessor.processProducts(products, {
            ...this.defaultSearchOptions,
            ...options
          }),
          { productCount: products.length }
        );
        
        // Return report
        return report;
      } finally {
        // Release client
        this.connectionManager.releaseConnection(client);
      }
    } catch (error) {
      console.error('Error processing products in bulk:', error);
      throw error;
    }
  }

  /**
   * Validate image quality and relevance
   * @param imageUrl - Image URL
   * @param productName - Product name
   * @returns Image quality score
   */
  async validateImageQuality(
    imageUrl: string,
    productName: string
  ): Promise<ImageQualityScore> {
    try {
      // Create product image object
      const image: ProductImage = {
        url: imageUrl,
        alt: productName,
        quality_score: 0,
        source: 'external'
      };
      
      // Assess image quality
      const qualityScore = await this.qualityAssessor.assessImageQuality(
        image,
        productName
      );
      
      // Return quality score
      return qualityScore;
    } catch (error) {
      console.error('Error validating image quality:', error);
      throw error;
    }
  }

  /**
   * Get available retailer sources
   * @param category - Product category
   * @returns Array of retailer sources
   */
  async getAvailableSources(category?: string): Promise<RetailerSource[]> {
    try {
      if (category) {
        return this.sourceManager.getSourcesByCategory(category);
      } else {
        return this.sourceManager.getAllSources();
      }
    } catch (error) {
      console.error('Error getting available sources:', error);
      throw error;
    }
  }

  /**
   * Check scraping service health
   * @returns Service health status
   */
  async checkHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'down';
    sources_available: number;
    last_successful_scrape: Date;
    error_rate: number;
  }> {
    try {
      // Get all sources
      const sources = this.sourceManager.getAllSources();
      
      // Check if we have sources
      if (sources.length === 0) {
        return {
          status: 'degraded',
          sources_available: 0,
          last_successful_scrape: new Date(0),
          error_rate: 1.0
        };
      }
      
      // Try to get a client
      let client: PlaywrightMCPClient | null = null;
      
      try {
        client = await this.connectionManager.getConnection();
        
        // If we got a client, the service is healthy
        return {
          status: 'healthy',
          sources_available: sources.length,
          last_successful_scrape: new Date(),
          error_rate: 0.0
        };
      } catch (error) {
        // If we couldn't get a client, the service is down
        return {
          status: 'down',
          sources_available: sources.length,
          last_successful_scrape: new Date(0),
          error_rate: 1.0
        };
      } finally {
        // Release client if we got one
        if (client) {
          this.connectionManager.releaseConnection(client);
        }
      }
    } catch (error) {
      console.error('Error checking service health:', error);
      
      // If we got an error, the service is degraded
      return {
        status: 'degraded',
        sources_available: 0,
        last_successful_scrape: new Date(0),
        error_rate: 0.5
      };
    }
  }

  /**
   * Close all connections
   */
  async close(): Promise<void> {
    try {
      await this.connectionManager.closeAll();
    } catch (error) {
      console.error('Error closing connections:', error);
    }
  }
}
