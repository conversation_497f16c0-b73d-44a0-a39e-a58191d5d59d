import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Loader2, ShoppingCart } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

// Define a simple Product interface for our needs
interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  image: string;
  category_id?: string;
  subcategory_id?: string;
  brand_id?: string;
  brand?: string;
  is_active?: boolean;
  is_new?: boolean;
  // Seed-specific properties
  seed_type?: string;
  flowering_time?: string;
  yield?: string;
  thc_content?: string;
}

// Removed mock data - now using real database products

interface SimpleProductGridProps {
  categoryId?: string;
  subcategoryId?: string;
  brandId?: string;
  searchQuery?: string;
  seedFilters?: Record<string, string[]>;
}

const SimpleProductGrid: React.FC<SimpleProductGridProps> = ({
  categoryId,
  subcategoryId,
  brandId,
  searchQuery,
  seedFilters
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Check if we're in the seeds category (remove URL dependency to prevent infinite loops)
  const isSeedsCategory =
    categoryId === '9606c3d0-e84e-450f-a09a-53f6420bfd58' ||
    categoryId === 'cat-2'; // Keep legacy support

  // Fetch products from Supabase with proper filtering
  useEffect(() => {
    let isCancelled = false;

    const fetchProducts = async () => {
      if (isCancelled) return;

      setLoading(true);

      console.log('SimpleProductGrid: Fetching products with params:', {
        categoryId,
        subcategoryId,
        brandId,
        searchQuery,
        isSeedsCategory,
        seedFilters
      });

      try {
        // For seed category, add special logging but use standard query
        if (isSeedsCategory) {
          console.log('Fetching seed products', seedFilters ? 'with filters:' : 'without filters', seedFilters);
        }

        // For all categories (including seeds without filters), use standard query
        let query = supabase
          .from('products')
          .select('*')
          .eq('is_active', true);

        // Apply category filter if provided
        if (categoryId && categoryId !== 'all') {
          query = query.eq('category_id', categoryId);
        }

        // Apply subcategory filter if provided
        if (subcategoryId && subcategoryId !== 'all') {
          query = query.eq('subcategory_id', subcategoryId);
        }

        // Apply brand filter if provided
        if (brandId && brandId !== 'all') {
          query = query.eq('brand_id', brandId);
        }

        // Apply search query if provided
        if (searchQuery) {
          query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching products:', error);

          // If it's a resource error, try a simpler query
          if (error.message?.includes('Failed to fetch') || error.message?.includes('INSUFFICIENT_RESOURCES')) {
            console.log('Trying fallback query due to resource error...');
            try {
              const fallbackQuery = await supabase
                .from('products')
                .select('id, name, price, image, is_active')
                .eq('is_active', true)
                .limit(10);

              if (fallbackQuery.data && !isCancelled) {
                console.log('Fallback query succeeded with', fallbackQuery.data.length, 'products');
                setProducts(fallbackQuery.data);
                return;
              }
            } catch (fallbackError) {
              console.error('Fallback query also failed:', fallbackError);
            }
          }

          if (!isCancelled) {
            setProducts([]);
          }
          return;
        }

        if (isCancelled) return;

        if (data && data.length > 0) {
          let filteredProducts = data;

          // Apply seed filtering if we're in the seed category and have filters
          if (isSeedsCategory && seedFilters && Object.keys(seedFilters).length > 0) {
            filteredProducts = data.filter(product => {
              return Object.entries(seedFilters).every(([filterKey, filterValues]) => {
                if (!filterValues || filterValues.length === 0) return true; // Skip empty filters

                // Map filter keys to product properties
                switch (filterKey) {
                  case 'seed_type':
                    return filterValues.some(value =>
                      product.seed_type?.toLowerCase().includes(value.toLowerCase())
                    );
                  case 'flowering_time':
                    return filterValues.some(value =>
                      product.flowering_time?.toLowerCase().includes(value.toLowerCase())
                    );
                  case 'yield':
                    return filterValues.some(value =>
                      product.yield?.toLowerCase().includes(value.toLowerCase())
                    );
                  case 'thc_content':
                    return filterValues.some(value =>
                      product.thc_content?.toLowerCase().includes(value.toLowerCase())
                    );
                  default:
                    return true;
                }
              });
            });
            console.log(`Applied seed filters: ${data.length} -> ${filteredProducts.length} products`);
          }

          console.log(`Found ${filteredProducts.length} products`);
          setProducts(filteredProducts);
        } else {
          console.log('No products found');
          setProducts([]);
        }
      } catch (err) {
        console.error('Error:', err);
        if (!isCancelled) {
          setProducts([]);
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };

    fetchProducts();

    return () => {
      isCancelled = true;
    };
  }, [categoryId, subcategoryId, brandId, searchQuery, seedFilters, isSeedsCategory]);

  // Format currency helper function
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  }

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 text-sage-600 animate-spin" />
        <span className="ml-2 text-sage-600">Loading products...</span>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-20 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-medium text-gray-900">No products found</h3>
        <p className="mt-2 text-gray-500">
          Try adjusting your filters or check back later as we add new products to our store.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Product grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div key={product.id} className="relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
            {/* Product badges */}
            {product.is_new && (
              <div className="absolute top-2 left-2 z-10">
                <span className="bg-sage-500 text-white text-xs font-bold px-2 py-1 rounded-md">New</span>
              </div>
            )}

            {/* Wishlist button */}
            <div className="absolute top-2 right-2 z-10">
              <button className="text-gray-400 hover:text-sage-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>

            {/* Product link */}
            <Link to={`/shop/${product.id}`} className="group">
              {/* Product image */}
              <div className="aspect-square overflow-hidden relative">
                <img
                  src={product.image}
                  alt={product.name}
                  className="h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                />
              </div>

              {/* Product info */}
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">{product.name}</h3>

                {/* Brand name */}
                {product.brand && (
                  <div className="mt-1 text-xs text-gray-500">
                    <span>By {product.brand}</span>
                  </div>
                )}

                <div className="mt-2 flex justify-between items-center">
                  <span className="text-lg font-bold text-gray-900">{formatCurrency(product.price)}</span>

                  {/* Add to cart button */}
                  <button
                    className="h-8 w-8 rounded-full hover:bg-sage-100 hover:text-sage-700 flex items-center justify-center"
                    aria-label="Add to cart"
                  >
                    <ShoppingCart className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimpleProductGrid;
