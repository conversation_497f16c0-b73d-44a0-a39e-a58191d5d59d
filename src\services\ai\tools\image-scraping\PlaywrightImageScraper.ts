/**
 * PlaywrightImageScraper.ts
 * 
 * Main scraper using Model Context Protocol (MCP) with <PERSON><PERSON>
 * Responsible for finding product images from targeted retailers
 */

import { 
  ProductImage, 
  ImageSearchOptions, 
  ImageQualityScore,
  RetailerSource
} from './types/ImageScrapingTypes';

/**
 * Interface for MCP Playwright client
 */
interface PlaywrightMCPClient {
  navigate(url: string): Promise<void>;
  snapshot(): Promise<any>;
  click(options: { element: string, ref: string }): Promise<void>;
  type(options: { element: string, ref: string, text: string, submit?: boolean }): Promise<void>;
  takeScreenshot(options?: { element?: string, ref?: string }): Promise<string>;
  waitFor(options: { text?: string, textGone?: string, time?: number }): Promise<void>;
  extractElements?(selector: string, attributes: string[]): Promise<any[]>;
}

/**
 * Class to handle image scraping using Playwright MCP
 */
export class PlaywrightImageScraper {
  private client: PlaywrightMCPClient;
  private sources: RetailerSource[];
  private currentSession: { sessionId: string; startTime: Date } | null = null;

  /**
   * Constructor
   * @param client - MCP Playwright client
   * @param sources - List of retailer sources
   */
  constructor(client: PlaywrightMCPClient, sources: RetailerSource[]) {
    this.client = client;
    this.sources = sources;
  }

  /**
   * Find product images from various sources
   * @param product - Product information
   * @param options - Search options
   * @returns Array of product images
   */
  async findProductImages(
    product: { name: string; category?: string; id?: string },
    options: ImageSearchOptions = {}
  ): Promise<ProductImage[]> {
    try {
      // Start a new scraping session
      this.startSession();
      
      // Set default options
      const searchOptions = this.getDefaultOptions(options);
      
      // Get relevant sources based on product category
      const relevantSources = this.getRelevantSources(product.category);
      
      // Collect images from all relevant sources
      const allImages: ProductImage[] = [];
      
      for (const source of relevantSources) {
        try {
          const sourceImages = await this.scrapeImagesFromSource(source, product, searchOptions);
          allImages.push(...sourceImages);
          
          // If we have enough high-quality images, stop searching
          if (this.hasEnoughQualityImages(allImages, searchOptions)) {
            break;
          }
        } catch (error) {
          console.error(`Error scraping from ${source.name}:`, error);
          // Continue with next source
        }
        
        // Respect rate limits
        await this.delay(source.rate_limit.delay_between_requests);
      }
      
      // Sort images by quality score and take the top ones
      const sortedImages = this.sortImagesByQuality(allImages);
      const topImages = sortedImages.slice(0, searchOptions.max_images);
      
      return topImages;
    } catch (error) {
      console.error('Error finding product images:', error);
      throw error;
    } finally {
      // End the session
      this.endSession();
    }
  }

  /**
   * Scrape images from a specific source
   * @param source - Retailer source
   * @param product - Product information
   * @param options - Search options
   * @returns Array of product images
   */
  private async scrapeImagesFromSource(
    source: RetailerSource,
    product: { name: string; category?: string; id?: string },
    options: Required<ImageSearchOptions>
  ): Promise<ProductImage[]> {
    // Construct search URL
    const searchUrl = this.constructSearchUrl(source, product);
    
    // Navigate to search page
    await this.client.navigate(searchUrl);
    
    // Wait for page to load
    await this.client.waitFor({ time: 3 });
    
    // Get page snapshot
    const snapshot = await this.client.snapshot();
    
    // Extract images using selectors
    const images: ProductImage[] = [];
    
    // Process each selector to find images
    for (const selector of source.selectors.product_images) {
      try {
        // Find elements matching the selector
        const elements = this.findElementsInSnapshot(snapshot, selector);
        
        // Extract image information
        for (const element of elements) {
          const imageUrl = this.extractAttributeValue(element, 'src') || 
                          this.extractAttributeValue(element, 'data-src');
          
          if (!imageUrl) continue;
          
          // Get alt text
          const alt = this.extractAttributeValue(element, 'alt') || product.name;
          
          // Create product image object
          const image: ProductImage = {
            url: this.normalizeUrl(imageUrl, source.base_url),
            alt,
            quality_score: 0, // Will be calculated later
            source: source.name,
          };
          
          // Add image dimensions if available
          const width = this.extractAttributeValue(element, 'width');
          const height = this.extractAttributeValue(element, 'height');
          
          if (width) image.width = parseInt(width, 10);
          if (height) image.height = parseInt(height, 10);
          
          // Calculate initial quality score
          image.quality_score = this.calculateInitialQualityScore(image, product.name, source);
          
          // Add to images array if it meets minimum quality
          if (image.quality_score >= options.min_quality_score) {
            images.push(image);
          }
        }
      } catch (error) {
        console.error(`Error extracting images with selector ${selector}:`, error);
        // Continue with next selector
      }
    }
    
    return images;
  }

  /**
   * Construct search URL for a source
   * @param source - Retailer source
   * @param product - Product information
   * @returns Search URL
   */
  private constructSearchUrl(
    source: RetailerSource,
    product: { name: string; category?: string; id?: string }
  ): string {
    // Create search query
    const searchQuery = encodeURIComponent(product.name);
    
    // Construct URL
    let url = `${source.base_url}${source.search_path}`;
    
    // Replace placeholders in URL
    url = url.replace('{query}', searchQuery);
    
    if (product.category && url.includes('{category}')) {
      url = url.replace('{category}', encodeURIComponent(product.category));
    }
    
    return url;
  }

  /**
   * Find elements in page snapshot
   * @param snapshot - Page snapshot
   * @param selector - CSS selector
   * @returns Array of elements
   */
  private findElementsInSnapshot(snapshot: any, selector: string): any[] {
    // This is a placeholder implementation
    // In a real implementation, we would use the snapshot structure
    // to find elements matching the selector
    return [];
  }

  /**
   * Extract attribute value from element
   * @param element - Element
   * @param attribute - Attribute name
   * @returns Attribute value
   */
  private extractAttributeValue(element: any, attribute: string): string | null {
    // This is a placeholder implementation
    // In a real implementation, we would extract the attribute value
    // from the element
    return null;
  }

  /**
   * Normalize URL
   * @param url - URL
   * @param baseUrl - Base URL
   * @returns Normalized URL
   */
  private normalizeUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) {
      return url;
    }
    
    if (url.startsWith('//')) {
      return `https:${url}`;
    }
    
    if (url.startsWith('/')) {
      const domain = new URL(baseUrl).origin;
      return `${domain}${url}`;
    }
    
    return `${baseUrl}/${url}`;
  }

  /**
   * Calculate initial quality score
   * @param image - Product image
   * @param productName - Product name
   * @param source - Retailer source
   * @returns Quality score
   */
  private calculateInitialQualityScore(
    image: ProductImage,
    productName: string,
    source: RetailerSource
  ): number {
    let score = 0;
    
    // Source reliability (0-30 points)
    score += source.reliability_score * 3;
    
    // Alt text relevance (0-30 points)
    if (image.alt) {
      const altTextLower = image.alt.toLowerCase();
      const productNameLower = productName.toLowerCase();
      
      // Check if alt text contains product name
      if (altTextLower.includes(productNameLower)) {
        score += 30;
      } else {
        // Check for partial matches
        const productWords = productNameLower.split(' ');
        let matchCount = 0;
        
        for (const word of productWords) {
          if (word.length > 2 && altTextLower.includes(word)) {
            matchCount++;
          }
        }
        
        score += (matchCount / productWords.length) * 20;
      }
    }
    
    // Image dimensions (0-20 points)
    if (image.width && image.height) {
      // Prefer larger images
      if (image.width >= 400 && image.height >= 400) {
        score += 20;
      } else if (image.width >= 200 && image.height >= 200) {
        score += 10;
      } else {
        score += 5;
      }
    }
    
    // URL quality (0-20 points)
    const url = image.url.toLowerCase();
    
    // Avoid thumbnails
    if (url.includes('thumb') || url.includes('small') || url.includes('tiny')) {
      score -= 10;
    }
    
    // Prefer product images
    if (url.includes('product') || url.includes('large') || url.includes('full')) {
      score += 10;
    }
    
    // Normalize score to 0-100
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get default options
   * @param options - Search options
   * @returns Default options
   */
  private getDefaultOptions(options: ImageSearchOptions): Required<ImageSearchOptions> {
    return {
      max_images: options.max_images || 5,
      min_quality_score: options.min_quality_score || 50,
      preferred_sources: options.preferred_sources || [],
      image_types: options.image_types || ['product', 'detail', 'packaging', 'lifestyle'],
      min_dimensions: options.min_dimensions || { width: 400, height: 400 }
    };
  }

  /**
   * Get relevant sources based on product category
   * @param category - Product category
   * @returns Array of relevant sources
   */
  private getRelevantSources(category?: string): RetailerSource[] {
    if (!category) {
      return this.sources;
    }
    
    // Filter sources by category
    const relevantSources = this.sources.filter(source => 
      source.categories.some(cat => 
        cat.toLowerCase().includes(category.toLowerCase()) || 
        category.toLowerCase().includes(cat.toLowerCase())
      )
    );
    
    return relevantSources.length > 0 ? relevantSources : this.sources;
  }

  /**
   * Check if we have enough quality images
   * @param images - Array of product images
   * @param options - Search options
   * @returns True if we have enough quality images
   */
  private hasEnoughQualityImages(
    images: ProductImage[],
    options: Required<ImageSearchOptions>
  ): boolean {
    const qualityImages = images.filter(img => img.quality_score >= options.min_quality_score);
    return qualityImages.length >= options.max_images;
  }

  /**
   * Sort images by quality score
   * @param images - Array of product images
   * @returns Sorted array of product images
   */
  private sortImagesByQuality(images: ProductImage[]): ProductImage[] {
    return [...images].sort((a, b) => b.quality_score - a.quality_score);
  }

  /**
   * Start a new scraping session
   */
  private startSession(): void {
    this.currentSession = {
      sessionId: `scrape-${Date.now()}`,
      startTime: new Date()
    };
  }

  /**
   * End the current scraping session
   */
  private endSession(): void {
    this.currentSession = null;
  }

  /**
   * Delay execution
   * @param ms - Milliseconds to delay
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
