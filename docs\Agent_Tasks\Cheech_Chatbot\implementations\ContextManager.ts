/**
 * Context Manager Implementation
 *
 * This file implements the Context Manager for the Cheech chatbot.
 * It handles conversation memory, user preferences, and context tracking.
 * Uses the existing AI orchestration system with Gemini and Deepseek.
 */

import {
  ContextService,
  ConversationContext,
  Message,
  MemoryItem
} from './types';
import { UnifiedAIService } from '../../../services/ai/core/UnifiedAIService';
import { AIRequest, AIRequestType } from '../../../services/ai/types/AIRequest';

/**
 * Implementation of the Context Service using the AI orchestration system
 */
export class ContextManager implements ContextService {
  private sessionContexts: Map<string, ConversationContext> = new Map();
  private conversationSummaries: Map<string, string> = new Map();
  private aiService: UnifiedAIService;

  /**
   * Create a new ContextManager
   * @param aiService The unified AI service
   */
  constructor(aiService: UnifiedAIService) {
    this.aiService = aiService;
  }

  /**
   * Get context for a session
   * @param sessionId The session ID
   * @returns Promise resolving to conversation context
   */
  async getContext(sessionId: string): Promise<ConversationContext> {
    // Check if context exists for this session
    if (!this.sessionContexts.has(sessionId)) {
      // Initialize new context
      this.sessionContexts.set(sessionId, {
        recentMessages: [],
        userPreferences: {},
        memoryItems: []
      });

      // Initialize empty conversation summary
      this.conversationSummaries.set(sessionId, '');
    }

    return this.sessionContexts.get(sessionId)!;
  }

  /**
   * Update context for a session
   * @param sessionId The session ID
   * @param context The partial context to update
   * @returns Promise resolving when context is updated
   */
  async updateContext(
    sessionId: string,
    context: Partial<ConversationContext>
  ): Promise<void> {
    // Get current context
    const currentContext = await this.getContext(sessionId);

    // Update context
    if (context.recentMessages) {
      currentContext.recentMessages = context.recentMessages;

      // Update conversation summary if we have enough messages
      if (context.recentMessages.length >= 10) {
        await this.updateConversationSummary(sessionId, context.recentMessages);
      }
    }

    if (context.userPreferences) {
      currentContext.userPreferences = {
        ...currentContext.userPreferences,
        ...context.userPreferences
      };
    }

    if (context.memoryItems) {
      currentContext.memoryItems = context.memoryItems;
    }

    // Save updated context
    this.sessionContexts.set(sessionId, currentContext);
  }

  /**
   * Add a memory item to a session
   * @param sessionId The session ID
   * @param item The memory item to add
   * @returns Promise resolving when item is added
   */
  async addMemoryItem(
    sessionId: string,
    item: Omit<MemoryItem, 'timestamp'>
  ): Promise<void> {
    // Get current context
    const currentContext = await this.getContext(sessionId);

    // Add memory item
    currentContext.memoryItems.push({
      ...item,
      timestamp: new Date()
    });

    // Sort memory items by importance (descending)
    currentContext.memoryItems.sort((a, b) => b.importance - a.importance);

    // Limit to 20 memory items to prevent context overflow
    if (currentContext.memoryItems.length > 20) {
      currentContext.memoryItems = currentContext.memoryItems.slice(0, 20);
    }

    // Save updated context
    this.sessionContexts.set(sessionId, currentContext);
  }

  /**
   * Get conversation summary for a session
   * @param sessionId The session ID
   * @returns Promise resolving to conversation summary
   */
  async getConversationSummary(sessionId: string): Promise<string> {
    return this.conversationSummaries.get(sessionId) || '';
  }

  /**
   * Update conversation summary using AI
   * @param sessionId The session ID
   * @param messages The conversation messages
   * @returns Promise resolving when summary is updated
   */
  private async updateConversationSummary(sessionId: string, messages: Message[]): Promise<void> {
    try {
      // Create a summary of the conversation using AI
      const conversationText = messages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Cheech'}: ${msg.content}`)
        .join('\n');

      const prompt = `Please summarize the following conversation between a user and Cheech (a cannabis/CBD product assistant). Focus on:
- User's interests and preferences
- Products discussed
- Key questions asked
- Important context for future conversations

Conversation:
${conversationText}

Summary:`;

      const aiRequest: AIRequest = {
        type: 'chat_response' as AIRequestType,
        content: prompt,
        context: {
          business_type: 'cannabis',
          max_length: 500,
          format: 'plain'
        },
        provider: 'auto',
        complexity: 'simple',
        urgency: 'low',
        session_id: sessionId
      };

      const response = await this.aiService.processRequest(aiRequest);

      if (response.success && response.content) {
        this.conversationSummaries.set(sessionId, response.content);
      }
    } catch (error) {
      console.error('Error updating conversation summary:', error);
    }
  }

  /**
   * Extract user preferences from conversation
   * @param messages The conversation messages
   * @returns Extracted user preferences
   */
  extractUserPreferences(messages: Message[]): Record<string, any> {
    const preferences: Record<string, any> = {};

    // Extract product categories of interest
    const categoryRegex = /interested in ([\w\s,]+)/i;
    const productRegex = /looking for ([\w\s]+)/i;

    for (const message of messages) {
      if (message.role === 'user') {
        // Extract categories
        const categoryMatch = message.content.match(categoryRegex);
        if (categoryMatch && categoryMatch[1]) {
          const categories = categoryMatch[1]
            .split(',')
            .map(cat => cat.trim().toLowerCase())
            .filter(Boolean);

          if (categories.length > 0) {
            preferences.interestedCategories = categories;
          }
        }

        // Extract products
        const productMatch = message.content.match(productRegex);
        if (productMatch && productMatch[1]) {
          preferences.lookingForProduct = productMatch[1].trim().toLowerCase();
        }

        // Check for price sensitivity
        if (
          message.content.toLowerCase().includes('cheap') ||
          message.content.toLowerCase().includes('affordable') ||
          message.content.toLowerCase().includes('budget')
        ) {
          preferences.priceSensitive = true;
        }

        // Check for quality preference
        if (
          message.content.toLowerCase().includes('best quality') ||
          message.content.toLowerCase().includes('premium') ||
          message.content.toLowerCase().includes('high-end')
        ) {
          preferences.qualityFocused = true;
        }
      }
    }

    return preferences;
  }

  /**
   * Get assistant response for a user message
   * @param userMessage The user message
   * @param messages All messages in the conversation
   * @returns The assistant response
   */
  private getAssistantResponseForMessage(
    userMessage: Message,
    messages: Message[]
  ): string {
    // Find the assistant response that follows this user message
    const userMessageIndex = messages.findIndex(m => m.id === userMessage.id);

    if (userMessageIndex >= 0 && userMessageIndex < messages.length - 1) {
      const nextMessage = messages[userMessageIndex + 1];

      if (nextMessage.role === 'assistant') {
        return nextMessage.content;
      }
    }

    return '';
  }

  /**
   * Generate context prompt for LLM
   * @param sessionId The session ID
   * @returns Promise resolving to context prompt
   */
  async generateContextPrompt(sessionId: string): Promise<string> {
    const context = await this.getContext(sessionId);
    const summary = await this.getConversationSummary(sessionId);

    let prompt = '';

    // Add conversation summary
    if (summary) {
      prompt += `Conversation Summary: ${summary}\n\n`;
    }

    // Add recent messages (last 5)
    if (context.recentMessages.length > 0) {
      prompt += 'Recent Messages:\n';

      const recentMessages = context.recentMessages.slice(-5);
      for (const message of recentMessages) {
        prompt += `${message.role === 'user' ? 'User' : 'Cheech'}: ${message.content}\n`;
      }

      prompt += '\n';
    }

    // Add user preferences
    if (Object.keys(context.userPreferences).length > 0) {
      prompt += 'User Preferences:\n';

      for (const [key, value] of Object.entries(context.userPreferences)) {
        prompt += `- ${key}: ${JSON.stringify(value)}\n`;
      }

      prompt += '\n';
    }

    // Add important memory items
    if (context.memoryItems.length > 0) {
      prompt += 'Important Information:\n';

      // Sort by importance and take top 5
      const importantItems = [...context.memoryItems]
        .sort((a, b) => b.importance - a.importance)
        .slice(0, 5);

      for (const item of importantItems) {
        prompt += `- ${item.key}: ${JSON.stringify(item.value)}\n`;
      }
    }

    return prompt;
  }
}
