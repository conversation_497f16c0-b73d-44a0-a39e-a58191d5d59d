
import { Product } from "@/types/database";
import { ProductFormFields } from "./product-form/ProductFormFields";
import { ProductFormToggles } from "./product-form/ProductFormToggles";
import { ProductFormActions } from "./product-form/ProductFormActions";
import { useProductForm } from "./product-form/useProductForm";
import { useCategoriesQuery } from "./product-form/useCategoriesQuery";
import { useBrandsQuery } from "./product-form/hooks/useBrandsQuery";

interface ProductFormProps {
  product: Product | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const { data: categories, isLoading: categoriesLoading } = useCategoriesQuery();
  const { data: brands, isLoading: brandsLoading } = useBrandsQuery();
  
  const {
    formData,
    setFormData,
    isSubmitting,
    isGeneratingDescription,
    isFindingImages,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
    handleSubmit,
    handleGenerateDescription,
    handleFindImages,
    // Related products
    relatedProducts,
    handleAddRelatedProduct,
    handleRemoveRelatedProduct,
    handleReorderRelatedProducts,
  } = useProductForm({ product, onSuccess });
  
  // Direct function to handle swapping main image with an additional image
  const swapMainImage = (newMainImageUrl: string) => {
    console.log('ProductForm.swapMainImage called with:', newMainImageUrl);
    
    // Get current state
    const currentMainImage = formData.image;
    const currentAdditionalImages = [...(formData.additional_images || [])];
    
    // Create new state
    let newAdditionalImages = [...currentAdditionalImages];
    
    // 1. If we have a current main image, add it to additional images (if not already there)
    if (currentMainImage && currentMainImage.trim() !== '' && currentMainImage !== newMainImageUrl) {
      if (!newAdditionalImages.includes(currentMainImage)) {
        newAdditionalImages.push(currentMainImage);
        console.log('Added previous main image to additional images:', currentMainImage);
      }
    }
    
    // 2. Remove the new main image from additional images
    if (newAdditionalImages.includes(newMainImageUrl)) {
      newAdditionalImages = newAdditionalImages.filter(img => img !== newMainImageUrl);
      console.log('Removed new main image from additional images:', newMainImageUrl);
    }
    
    // 3. Update the form data directly
    setFormData({
      ...formData,
      image: newMainImageUrl,
      additional_images: newAdditionalImages
    });
    
    console.log('Updated form data:', {
      mainImage: newMainImageUrl,
      additionalImages: newAdditionalImages
    });
  };

  console.log("Rendering ProductForm with data:", { 
    productId: product?.id,
    hasCategories: !!categories?.length,
    formDataKeys: Object.keys(formData)
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <ProductFormFields
        formData={formData}
        categories={categories || []}
        brands={brands || []}
        handleChange={handleChange}
        handleSwitchChange={handleSwitchChange}
        handleSelectChange={handleSelectChange}
        handleGenerateDescription={handleGenerateDescription}
        handleFindImages={handleFindImages}
        isGeneratingDescription={isGeneratingDescription}
        isFindingImages={isFindingImages}
        swapMainImage={swapMainImage}
        // Related products
        relatedProducts={relatedProducts}
        onAddRelatedProduct={handleAddRelatedProduct}
        onRemoveRelatedProduct={handleRemoveRelatedProduct}
        onReorderRelatedProducts={handleReorderRelatedProducts}
      />

      <ProductFormToggles
        formData={formData}
        handleSwitchChange={handleSwitchChange}
      />

      <ProductFormActions
        isSubmitting={isSubmitting}
        isEditing={!!product}
        onCancel={onCancel}
      />
    </form>
  );
}
