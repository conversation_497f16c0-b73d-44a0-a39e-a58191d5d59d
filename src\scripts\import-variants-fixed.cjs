#!/usr/bin/env node

// Import required modules
const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key length:', supabaseKey.length);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Check if a file path is provided
if (process.argv.length < 3) {
  console.error('Please provide a CSV file path');
  process.exit(1);
}

// Get the file path from command line arguments
const filePath = process.argv[2];

// Create output directory if it doesn't exist
const outputDir = path.join(path.dirname(filePath), 'output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Process image URLs to match Supabase storage format
function processImageUrl(imageUrl) {
  if (!imageUrl) return null;
  
  // Extract just the filename from the URL
  const filename = imageUrl.split('/').pop();
  if (!filename) return null;
  
  // Remove the ~mv2 suffix and change extension to .webp
  const processedFilename = filename.replace(/~mv2\.(jpg|jpeg|png|webp|gif)$/i, '.webp');
  
  // Return the full URL
  return `${supabaseUrl}/storage/v1/object/public/product-images/${processedFilename}`;
}

// Process multiple image URLs
function processImageUrls(imageUrlString) {
  if (!imageUrlString) return [];
  
  // Split by semicolons or commas
  const urls = imageUrlString.split(/[;,]/).map(url => url.trim()).filter(Boolean);
  
  // Process each URL
  return urls.map(processImageUrl).filter(Boolean);
}

// Extract option definitions from product
function extractOptionDefinitions(product) {
  const optionDefinitions = {};
  
  // Look for option names and descriptions in the product
  for (let i = 1; i <= 6; i++) {
    const optionName = product[`productOptionName${i}`];
    const optionType = product[`productOptionType${i}`];
    const optionDescription = product[`productOptionDescription${i}`];
    
    if (optionName && optionType === 'DROP_DOWN' && optionDescription) {
      // Split the description by semicolons or commas to get individual values
      const values = optionDescription.split(/[;,]/).map(value => value.trim()).filter(Boolean);
      if (values.length > 0) {
        optionDefinitions[optionName] = {
          name: optionName,
          values: values,
          display_type: optionName.toLowerCase().includes('color') ? 'color' : 'dropdown'
        };
      }
    }
  }
  
  return optionDefinitions;
}

// Extract option combination from variant
function extractOptionCombination(variant, product) {
  const combination = {};
  
  // For Variant fieldType, the option value is directly in the productOptionDescription1 field
  if (variant.fieldType === 'Variant') {
    // Find the option name from the product
    for (let i = 1; i <= 6; i++) {
      const optionName = product[`productOptionName${i}`];
      if (optionName) {
        // The option value is in the same position in the variant
        const optionValue = variant[`productOptionDescription${i}`];
        if (optionValue) {
          combination[optionName] = optionValue.trim();
        }
      }
    }
  } else {
    // For Choice fieldType (legacy support)
    for (let i = 1; i <= 6; i++) {
      const optionName = variant[`productOptionName${i}`];
      const optionValue = variant[`productOptionDescription${i}`];
      
      if (optionName && optionValue) {
        combination[optionName] = optionValue.trim();
      }
    }
  }
  
  return combination;
}

// Process product data
function processProduct(product) {
  const {
    handleId,
    name,
    description,
    productImageUrl,
    price,
    salePrice,
    inventory,
    weight,
  } = product;

  // Extract option definitions
  const optionDefinitions = extractOptionDefinitions(product);

  // Generate a unique slug by adding a short unique ID at the end
  const uniqueId = uuidv4().substring(0, 8);
  const baseSlug = slugify(name || '', { lower: true, strict: true });
  const uniqueSlug = `${baseSlug}-${uniqueId}`;
  
  // Process the main image and additional images
  const mainImage = processImageUrl(productImageUrl ? productImageUrl.split(';')[0] : null);
  const additionalImages = processImageUrls(productImageUrl).slice(1);
  
  // Set is_active to false if there's no image
  const isActive = mainImage !== null;
  
  if (!isActive) {
    console.log(`Product "${name}" has no image and will be set to inactive`);
  }
  
  return {
    id: uuidv4(),
    name: name || '',
    slug: uniqueSlug,
    description: description || '',
    price: parseFloat(price) || 0,
    sale_price: salePrice ? parseFloat(salePrice) : null,
    image: mainImage,
    additional_images: additionalImages,
    category_id: null,
    brand_id: null,
    sku: product.sku || '',
    stock_quantity: parseInt(inventory) || 0,
    weight: parseFloat(weight) || 0,
    in_stock: true,
    is_featured: false,
    is_new: false,
    is_active: isActive, // Only set active if there's an image
    option_definitions: optionDefinitions,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

// Process variant data
function processVariant(variant, productId, productPrice, productImage, product) {
  // Extract option combination
  const optionCombination = extractOptionCombination(variant, product);
  
  // Create variant name from option values
  const variantName = Object.values(optionCombination).join(' - ');
  
  // Handle variant pricing
  let priceAdjustment = 0;
  
  // If variant has a price, it's a surcharge to be added to the base price
  if (variant.price && parseFloat(variant.price) !== 0) {
    priceAdjustment = parseFloat(variant.price);
  } else if (variant.surcharge && parseFloat(variant.surcharge) !== 0) {
    // Some variants use 'surcharge' field instead of 'price'
    priceAdjustment = parseFloat(variant.surcharge);
  }

  return {
    id: uuidv4(),
    product_id: productId,
    sku: variant.sku || `${productId.substring(0, 8)}-${Object.values(optionCombination).join('-')}`,
    price_adjustment: priceAdjustment,
    stock_quantity: parseInt(variant.inventory) || 30,
    options: optionCombination,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

// Process the CSV data
function processData(data) {
  const products = [];
  const variants = [];
  
  // Group products and variants
  const productGroups = {};
  
  data.forEach(item => {
    if (item.fieldType === 'Product') {
      const productId = item.handleId;
      productGroups[productId] = {
        product: item,
        variants: []
      };
    } else if (item.fieldType === 'Variant' || item.fieldType === 'Choice') {
      // Handle both Variant and Choice fieldTypes
      const productId = item.handleId;
      if (productGroups[productId]) {
        productGroups[productId].variants.push(item);
      }
    }
  });
  
  // Process each product group
  Object.values(productGroups).forEach(group => {
    const { product, variants: productVariants } = group;
    
    // Process the main product
    const processedProduct = processProduct(product);
    products.push(processedProduct);
    
    // Process variants if any
    if (productVariants.length > 0) {
      productVariants.forEach(variant => {
        const processedVariant = processVariant(
          variant, 
          processedProduct.id, 
          processedProduct.price,
          processedProduct.image,
          product // Pass the original product data
        );
        variants.push(processedVariant);
        console.log(`Processed variant: ${JSON.stringify(processedVariant.options)} for product ${processedProduct.id}`);
      });
    }
  });
  
  return { products, variants };
}

// Read and parse the CSV file
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    process.exit(1);
  }

  // Parse the CSV data
  Papa.parse(data, {
    header: true,
    skipEmptyLines: true,
    complete: (results) => {
      console.log(`Reading products and variants from ${filePath}`);
      
      // Process the data
      const { products, variants } = processData(results.data);
      
      console.log(`Found ${products.length} products and ${variants.length} variants in the CSV`);
      
      // Output the processed products to a CSV file
      const productsOutputPath = path.join(outputDir, 'products-transformed.csv');
      const productsOutput = Papa.unparse(products);
      fs.writeFileSync(productsOutputPath, productsOutput);
      
      // Output the processed variants to a CSV file
      const variantsOutputPath = path.join(outputDir, 'product_variants.csv');
      const variantsOutput = Papa.unparse(variants);
      fs.writeFileSync(variantsOutputPath, variantsOutput);
      
      console.log(`\nCSV files generated successfully:
- Products: ${productsOutputPath} (${products.length} products)
- Variants: ${variantsOutputPath} (${variants.length} variants)

You can now import these CSV files into your database.`);
    },
    error: (error) => {
      console.error('Error parsing CSV:', error);
      process.exit(1);
    }
  });
});
