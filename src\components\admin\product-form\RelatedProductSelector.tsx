import React, { useState, useEffect } from 'react';
// Using a simplified product type to avoid type errors
type SimpleProduct = {
  id: string;
  name: string;
  image?: string;
  price?: number;
  sku?: string;
};
import { supabase } from '@/integrations/supabase/client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, X, Plus, ArrowUp, ArrowDown } from 'lucide-react';

// Custom hook for debouncing values
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

interface RelatedProductSelectorProps {
  productId?: string; // Current product ID (to exclude from search)
  selectedProducts: SimpleProduct[]; // Already selected related products
  onAddProduct: (product: SimpleProduct) => void;
  onRemoveProduct: (productId: string) => void;
  onReorderProducts: (products: SimpleProduct[]) => void;
}

export function RelatedProductSelector({
  productId,
  selectedProducts,
  onAddProduct,
  onRemoveProduct,
  onReorderProducts
}: RelatedProductSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  
  // Search for products when query changes
  useEffect(() => {
    const searchProducts = async () => {
      if (!debouncedSearchQuery.trim()) {
        setSearchResults([]);
        return;
      }
      
      setIsSearching(true);
      
      try {
        const { data, error } = await supabase
          .from('products')
          .select('id, name, image, price, sku')
          .ilike('name', `%${debouncedSearchQuery}%`)
          .limit(10);
          
        if (error) throw error;
        
        // Filter out the current product and already selected products
        const filteredResults = data.filter(product => 
          product.id !== productId && 
          !selectedProducts.some(selected => selected.id === product.id)
        );
        
        setSearchResults(filteredResults);
      } catch (error) {
        console.error('Error searching products:', error);
      } finally {
        setIsSearching(false);
      }
    };
    
    searchProducts();
  }, [debouncedSearchQuery, productId, selectedProducts]);

  // Move a product up in the order
  const moveProductUp = (index: number) => {
    if (index <= 0) return;
    
    const newProducts = [...selectedProducts];
    const temp = newProducts[index];
    newProducts[index] = newProducts[index - 1];
    newProducts[index - 1] = temp;
    
    onReorderProducts(newProducts);
  };

  // Move a product down in the order
  const moveProductDown = (index: number) => {
    if (index >= selectedProducts.length - 1) return;
    
    const newProducts = [...selectedProducts];
    const temp = newProducts[index];
    newProducts[index] = newProducts[index + 1];
    newProducts[index + 1] = temp;
    
    onReorderProducts(newProducts);
  };
  
  // Handle click outside to close search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const searchContainer = document.getElementById('product-search-container');
      if (searchContainer && !searchContainer.contains(event.target as Node)) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className="space-y-4">
      <div id="product-search-container" className="relative">
        <Input
          placeholder="Search for products to add..."
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setShowResults(true);
          }}
          onFocus={() => setShowResults(true)}
          className="pl-10"
        />
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        
        {/* Search Results Dropdown */}
        {showResults && (searchResults.length > 0 || isSearching) && (
          <Card className="absolute z-10 w-full mt-1 shadow-lg">
            <CardContent className="p-2">
              {isSearching ? (
                <div className="p-2 text-center text-sm text-muted-foreground">
                  Searching...
                </div>
              ) : searchResults.length > 0 ? (
                <ul className="max-h-60 overflow-auto">
                  {searchResults.map(product => (
                    <li 
                      key={product.id}
                      className="flex items-center gap-2 p-2 hover:bg-muted rounded-md cursor-pointer"
                      onClick={() => {
                        onAddProduct(product);
                        setSearchQuery('');
                        setShowResults(false);
                      }}
                    >
                      {product.image ? (
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="h-8 w-8 object-cover rounded-md"
                        />
                      ) : (
                        <div className="h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center text-gray-400 text-xs">
                          No img
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium">{product.name}</p>
                        <p className="text-xs text-muted-foreground">
                          SKU: {product.sku || 'N/A'} - ${product.price?.toFixed(2) || '0.00'}
                        </p>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Convert database product to SimpleProduct
                          onAddProduct({
                            id: product.id,
                            name: product.name,
                            image: product.image,
                            price: product.price,
                            sku: product.sku
                          });
                          setSearchQuery('');
                          setShowResults(false);
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="p-2 text-center text-sm text-muted-foreground">
                  No products found
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* Selected Products */}
      {selectedProducts.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Selected Products</h4>
          <ul className="space-y-2">
            {selectedProducts.map((product, index) => (
              <li 
                key={product.id}
                className="flex items-center gap-2 p-2 bg-muted rounded-md"
              >
                {product.image ? (
                  <img 
                    src={product.image} 
                    alt={product.name}
                    className="h-10 w-10 object-cover rounded-md"
                  />
                ) : (
                  <div className="h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center text-gray-400 text-xs">
                    No img
                  </div>
                )}
                <div className="flex-1">
                  <p className="text-sm font-medium">{product.name}</p>
                  <p className="text-xs text-muted-foreground">
                    SKU: {product.sku || 'N/A'} - ${product.price?.toFixed(2) || '0.00'}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => moveProductUp(index)}
                    disabled={index === 0}
                    className="h-8 w-8"
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => moveProductDown(index)}
                    disabled={index === selectedProducts.length - 1}
                    className="h-8 w-8"
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => onRemoveProduct(product.id)}
                    className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
