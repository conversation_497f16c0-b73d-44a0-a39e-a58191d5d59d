import React from 'react';
import { Link } from 'react-router-dom';
import { ShoppingCart } from 'lucide-react';

// Define a simple Product interface for our needs
interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  image: string;
  category_id?: string;
  subcategory_id?: string;
  brand_id?: string;
  brand?: string;
  is_active?: boolean;
  is_new?: boolean;
  // Seed-specific properties
  seed_type?: string;
  flowering_time?: string;
  yield?: string;
  thc_content?: string;
}

// Mock seed products - these will be displayed immediately while we try to load real data
const mockSeedProducts: Product[] = [
  {
    id: 'seed-1',
    name: 'Paradise Seeds Chocolate Wafflez (5 Female)',
    price: 70.00,
    image: 'https://bitsnbongs.co.uk/wp-content/uploads/2023/05/Chocolate-Wafflez-Paradise-Seeds-600x600.jpg',
    is_active: true,
    is_new: false,
    category_id: 'cat-2',
    brand: 'Paradise Seeds',
    seed_type: 'feminized'
  },
  {
    id: 'seed-2',
    name: '<PERSON><PERSON> Kush 5 female',
    price: 60.00,
    image: 'https://bitsnbongs.co.uk/wp-content/uploads/2023/05/Banana-Candy-Kush-600x600.jpg',
    is_active: true,
    is_new: true,
    category_id: 'cat-2',
    brand: "Barney's Farm",
    seed_type: 'feminized',
    flowering_time: '8-10'
  },
  {
    id: 'seed-3',
    name: 'Gorilla Zkittlez 5 female',
    price: 65.00,
    image: 'https://bitsnbongs.co.uk/wp-content/uploads/2023/05/Gorilla-Zkittlez-600x600.jpg',
    is_active: true,
    is_new: false,
    category_id: 'cat-2',
    brand: "Barney's Farm",
    seed_type: 'feminized',
    yield: 'high'
  },
  {
    id: 'seed-4',
    name: 'Blue Gelato 5 female',
    price: 65.00,
    image: 'https://bitsnbongs.co.uk/wp-content/uploads/2023/05/Blue-Gelato-600x600.jpg',
    is_active: true,
    is_new: false,
    category_id: 'cat-2',
    brand: "Barney's Farm",
    seed_type: 'feminized',
    thc_content: 'high'
  }
];

interface SeedProductGridProps {
  filters?: Record<string, string[]>;
}

const SeedProductGrid: React.FC<SeedProductGridProps> = ({ filters }) => {
  console.log('SeedProductGrid rendering with filters:', filters);
  
  // Format currency helper function
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };
  
  // Filter products based on provided filters
  const filteredProducts = React.useMemo(() => {
    if (!filters || Object.values(filters).every(values => values.length === 0)) {
      console.log('No active filters, showing all seed products');
      return mockSeedProducts;
    }
    
    console.log('Applying filters to seed products:', filters);
    return mockSeedProducts.filter(product => {
      // Check each filter group
      return Object.entries(filters).every(([key, values]) => {
        // If no values selected for this filter group, it passes
        if (values.length === 0) return true;
        
        // Get the product's value for this filter key
        const productValue = product[key as keyof Product];
        
        // If the product doesn't have this property, it fails this filter
        if (!productValue) return false;
        
        // Check if the product's value is in the selected values
        return values.includes(productValue.toString());
      });
    });
  }, [filters]);
  
  console.log(`Displaying ${filteredProducts.length} seed products after filtering`);
  
  if (filteredProducts.length === 0) {
    return (
      <div className="text-center py-20 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-medium text-gray-900">No seed products found</h3>
        <p className="mt-2 text-gray-500">
          Try adjusting your filters or check back later as we add new products to our store.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {filteredProducts.map((product) => (
          <div key={product.id} className="relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
            {/* Product badges */}
            {product.is_new && (
              <div className="absolute top-2 left-2 z-10">
                <span className="bg-sage-500 text-white text-xs font-bold px-2 py-1 rounded-md">New</span>
              </div>
            )}
            
            {/* Wishlist button */}
            <div className="absolute top-2 right-2 z-10">
              <button className="text-gray-400 hover:text-sage-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>
            
            {/* Product link */}
            <Link to={`/shop/${product.id}`} className="group">
              {/* Product image */}
              <div className="aspect-square overflow-hidden relative">
                <img
                  src={product.image}
                  alt={product.name}
                  className="h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              
              {/* Product info */}
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">{product.name}</h3>
                
                {/* Brand name */}
                {product.brand && (
                  <div className="mt-1 text-xs text-gray-500">
                    <span>By {product.brand}</span>
                  </div>
                )}
                
                <div className="mt-2 flex justify-between items-center">
                  <span className="text-lg font-bold text-gray-900">{formatCurrency(product.price)}</span>
                  
                  {/* Add to cart button */}
                  <button
                    className="h-8 w-8 rounded-full hover:bg-sage-100 hover:text-sage-700 flex items-center justify-center"
                    aria-label="Add to cart"
                  >
                    <ShoppingCart className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SeedProductGrid;
