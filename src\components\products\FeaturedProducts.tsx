import React from 'react';
import { Link } from 'react-router-dom';
import ProductGrid from './ProductGrid';
import { Button } from '@/components/ui/button';

interface FeaturedProductsProps {
  title?: string;
  subtitle?: string;
  viewAllLink?: string;
  limit?: number;
  featured?: boolean;
  newProducts?: boolean;
  bestSellers?: boolean;
  categoryId?: string;
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({
  title = 'Featured Products',
  subtitle = 'Check out our selection of premium products',
  viewAllLink = '/shop',
  limit = 4,
  featured = true,
  newProducts = false,
  bestSellers = false,
  categoryId,
}) => {
  return (
    <section className="py-12 bg-white">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-clay-900">{title}</h2>
            <p className="mt-2 text-gray-600">{subtitle}</p>
          </div>
          
          {viewAllLink && (
            <Link to={viewAllLink}>
              <Button variant="outline" className="mt-4 md:mt-0">
                View All
              </Button>
            </Link>
          )}
        </div>
        
        <ProductGrid 
          limit={limit}
          featured={featured}
          newProducts={newProducts}
          bestSellers={bestSellers}
          categoryId={categoryId}
        />
      </div>
    </section>
  );
};

export default FeaturedProducts;
