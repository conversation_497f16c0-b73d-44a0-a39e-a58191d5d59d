import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/auth.basic';
import { useCart } from '@/hooks/useCart';
import { useCheckoutShipping } from '@/hooks/useShipping';
import { useAddresses, Address } from '@/hooks/useAddresses';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ShippingStep } from '@/components/checkout/ShippingStep';
import { PaymentStep } from '@/components/checkout/PaymentStep';
import { Separator } from '@/components/ui/separator';
import { CheckoutSummary } from '@/components/checkout/CheckoutSummary';
import { stripePaymentService } from '@/services/stripePaymentService';
import { ForceShippingRefresh } from '@/components/checkout/ForceShippingRefresh';
import { ClearShippingCache } from '@/components/checkout/ClearShippingCache';

// Existing checkout steps
enum CheckoutStep {
  SHIPPING = 0,
  PAYMENT = 1,
  CONFIRMATION = 2,
}

export default function CheckoutPage() {
  // state and hooks
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cart, removeItem, updateQuantity, clearCart, cartTotal } = useCart();
  const { shippingMethods, isLoading: isShippingLoading } = useCheckoutShipping();
  const { toast } = useToast();
  
  const [currentStep, setCurrentStep] = useState<CheckoutStep>(CheckoutStep.SHIPPING);
  const [selectedAddressId, setSelectedAddressId] = useState<string | undefined>(undefined);
  const [selectedShippingMethodId, setSelectedShippingMethodId] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<string | undefined>(undefined);
  const [stripeClientSecret, setStripeClientSecret] = useState<string | null>(null);
  const [orderError, setOrderError] = useState<string | null>(null);
  const [isPaymentFormComplete, setIsPaymentFormComplete] = useState(false);
  
  // Reset everything when cart changes
  useEffect(() => {
    if (cart.length === 0) {
      navigate('/cart');
    }
  }, [cart, navigate]);
  
  // Find selected shipping method
  const selectedShippingMethod = shippingMethods.find(
    method => method.id === selectedShippingMethodId
  );

  // Handle continuing to next step
  const handleContinueFromShipping = () => {
    setCurrentStep(CheckoutStep.PAYMENT);
    window.scrollTo(0, 0);
  };

  // Handle shipping method selection
  const handleShippingMethodSelect = (methodId: string) => {
    setSelectedShippingMethodId(methodId);
  };

  return (
    <div className="bg-gradient-to-b from-gray-50 to-gray-100 min-h-screen py-8">
      {/* Add shipping cache clearers */}
      <ClearShippingCache />
      <ForceShippingRefresh />
      
      <div className="container-custom">
        <h1 className="text-3xl font-bold mb-8 text-center">Checkout</h1>
      </div>
    </div>
  );
}

