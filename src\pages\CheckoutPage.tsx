import { useState, useEffect } from 'react';
import { useCart } from '@/hooks/useCart';
import { useAuth } from '@/hooks/auth.basic';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ShippingAddressForm, ShippingAddressFormValues } from '@/components/checkout/ShippingAddressForm';
import { AddressList } from '@/components/checkout/AddressList';
import { ShippingMethodSelector, ShippingMethod } from '@/components/checkout/ShippingMethodSelector';
import { useAddresses, Address } from '@/hooks/useAddresses';
import { useCheckoutShipping } from '@/hooks/useShipping';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusCircle,
  Truck,
  CreditCard,
  CheckCircle,
  MapPin,
  Package,
  ShoppingBag,
  Clock,
  ChevronRight,
  ShieldCheck,
  ArrowLeft,
  Loader2,
  Home,
  Phone,
  User
} from 'lucide-react';
import { ForceShippingRefresh } from '@/components/checkout/ForceShippingRefresh';
import { ShippingRefreshButton } from '@/components/checkout/ShippingRefreshButton';

// Shipping methods are now loaded dynamically from the database

// Define checkout steps
type CheckoutStep = 'shipping' | 'payment' | 'review';

const CheckoutPage = () => {
  const { cartItems, subtotal, clearCart, isLoading: isCartLoading } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    addresses,
    defaultAddress,
    isLoading: isAddressesLoading,
    addAddress,
    updateAddress,
    deleteAddress,
    setAsDefault
  } = useAddresses();

  // State for checkout process
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);

  // Get customer country from selected address (default to UK)
  const customerCountry = selectedAddress?.country || 'United Kingdom';

  // Load dynamic shipping methods
  const { shippingMethods, isLoading: isShippingLoading } = useCheckoutShipping(customerCountry);

  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedShippingMethod, setSelectedShippingMethod] = useState<string>('');
  const [isAddingNewAddress, setIsAddingNewAddress] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState<Address | null>(null);

  // Calculate order totals
  const shippingMethod = shippingMethods.find(method => method.id === selectedShippingMethod) || shippingMethods[0];
  const shippingCost = shippingMethod?.price || 0;
  // Extract VAT from subtotal (assuming prices already include VAT)
  const vatRate = 0.2; // 20% VAT
  const subtotalExVat = subtotal / (1 + vatRate);
  const tax = subtotal - subtotalExVat; // VAT amount
  const total = subtotal + shippingCost; // Total with VAT already included

  // Set default address when addresses load
  useEffect(() => {
    if (defaultAddress && !selectedAddress) {
      setSelectedAddress(defaultAddress);
    }
  }, [defaultAddress]);

  // Set default shipping method when shipping methods load
  useEffect(() => {
    if (shippingMethods.length > 0 && !selectedShippingMethod) {
      setSelectedShippingMethod(shippingMethods[0].id);
    }
  }, [shippingMethods, selectedShippingMethod]);

  // Redirect if not logged in
  if (!user) {
    navigate('/auth');
    return null;
  }

  // Show empty cart message
  if (cartItems.length === 0 && !isCartLoading) {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">Your cart is empty</h1>
        <p className="mb-6 text-gray-600">Add some items to your cart before checking out.</p>
        <Button onClick={() => navigate('/shop')}>Continue Shopping</Button>
      </div>
    );
  }

  // Handle new address submission
  const handleAddressSubmit = async (data: ShippingAddressFormValues) => {
    try {
      if (isEditingAddress) {
        // Update existing address
        const success = await updateAddress(isEditingAddress.id, data);
        if (success) {
          setIsEditingAddress(null);
          toast({
            title: "Address Updated",
            description: "Your shipping address has been updated.",
          });
        }
      } else {
        // Add new address
        const newAddress = await addAddress(data);
        if (newAddress) {
          setSelectedAddress(newAddress);
          setIsAddingNewAddress(false);
          toast({
            title: "Address Added",
            description: "Your shipping address has been saved.",
          });
        }
      }
    } catch (error) {
      console.error('Error saving address:', error);
      toast({
        title: "Error",
        description: "There was a problem saving your address.",
        variant: "destructive",
      });
    }
  };

  // Handle address selection
  const handleAddressSelect = (address: Address) => {
    setSelectedAddress(address);
  };

  // Handle address edit
  const handleAddressEdit = (address: Address) => {
    setIsEditingAddress(address);
    setIsAddingNewAddress(true);
  };

  // Handle shipping method selection
  const handleShippingMethodSelect = (methodId: string) => {
    setSelectedShippingMethod(methodId);
  };

  // Handle continue to next step
  const handleContinue = () => {
    if (currentStep === 'shipping') {
      if (!selectedAddress) {
        toast({
          title: "Shipping Address Required",
          description: "Please select or add a shipping address to continue.",
          variant: "destructive",
        });
        return;
      }
      setCurrentStep('payment');
    } else if (currentStep === 'payment') {
      // In a real implementation, this would validate payment info
      setCurrentStep('review');
    }
  };

  // Handle place order
  const handlePlaceOrder = async () => {
    if (!user || !selectedAddress) {
      toast({
        title: "Information Missing",
        description: "Please complete all required information to place your order.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Insert order with shipping information
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: user.id,
          total: total,
          status: 'pending',
          shipping_address_id: selectedAddress.id,
          shipping_method: selectedShippingMethod,
          shipping_cost: shippingCost,
          payment_status: 'pending'
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Insert order items
      if (order) {
        const orderItems = cartItems.map(item => ({
          order_id: order.id,
          product_id: item.product_id,
          quantity: item.quantity,
          price: item.product?.price || 0,
          variant_id: item.variant_id || null
        }));

        const { error: itemsError } = await supabase
          .from('order_items')
          .insert(orderItems);

        if (itemsError) throw itemsError;

        // Clear cart
        await clearCart();

        toast({
          title: "Order placed successfully!",
          description: "Thank you for your purchase.",
        });

        // Navigate to account page
        navigate('/account');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Error placing order",
        description: "There was a problem processing your order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Animation variants for page transitions
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  };

  // Animation variants for list items
  const listItemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.3
      }
    })
  };

  // Get step status
  const getStepStatus = (step: CheckoutStep) => {
    const order: CheckoutStep[] = ['shipping', 'payment', 'review'];
    const currentIndex = order.indexOf(currentStep);
    const stepIndex = order.indexOf(step);

    if (stepIndex < currentIndex) return 'complete';
    if (stepIndex === currentIndex) return 'current';
    return 'upcoming';
  };

  return (
    <div className="container py-8 max-w-6xl mx-auto">
      {/* Add ForceShippingRefresh to ensure we always show fresh shipping data */}
      <ForceShippingRefresh />
      
      <h1 className="text-3xl font-bold mb-8 text-center">Checkout</h1>

      <div className="bg-gradient-to-b from-gray-50 to-gray-100 min-h-screen py-8">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-3xl font-bold mb-2">Checkout</h1>
            <p className="text-gray-600">Complete your purchase securely</p>
          </motion.div>

          {/* Checkout Progress Indicator */}
          <div className="mb-10">
            <div className="flex justify-center items-center mb-8">
              <div className="w-full max-w-3xl flex items-center">
                {/* Shipping Step */}
                <div className="flex-1 relative">
                  <div className="flex flex-col items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${getStepStatus('shipping') === 'complete' ? 'bg-green-500 border-green-500 text-white' : getStepStatus('shipping') === 'current' ? 'bg-blue-500 border-blue-500 text-white' : 'bg-white border-gray-300 text-gray-500'}`}>
                      {getStepStatus('shipping') === 'complete' ? <CheckCircle className="w-5 h-5" /> : <MapPin className="w-5 h-5" />}
                    </div>
                    <p className={`mt-2 text-sm font-medium ${getStepStatus('shipping') === 'complete' ? 'text-green-500' : getStepStatus('shipping') === 'current' ? 'text-blue-500' : 'text-gray-500'}`}>Shipping</p>
                  </div>
                  <div className="absolute top-5 left-1/2 w-full h-0.5 bg-gray-200">
                    <div className={`h-full ${getStepStatus('shipping') === 'complete' ? 'bg-green-500' : 'bg-gray-200'}`} style={{ width: getStepStatus('shipping') === 'complete' ? '100%' : '0%' }}></div>
                  </div>
                </div>
              <div className="flex items-center">
                <div className={`rounded-full p-2 ${currentStep === 'payment' ? 'bg-primary text-white' : 'bg-gray-200'}`}>
                  <CreditCard className="h-5 w-5" />
                </div>
                <span className="ml-2 font-medium">Payment</span>
              </div>
              <div className="h-px bg-gray-300 flex-grow mx-4"></div>
              <div className="flex items-center">
                <div className={`rounded-full p-2 ${currentStep === 'review' ? 'bg-primary text-white' : 'bg-gray-200'}`}>
                  <CheckCircle className="h-5 w-5" />
                </div>
                <span className="ml-2 font-medium">Review</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main checkout content */}
            <div className="lg:col-span-2">
              {/* Shipping Step */}
              {currentStep === 'shipping' && (
                <div className="space-y-6">
                  {/* Shipping Address Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Shipping Address</CardTitle>
                      <CardDescription>Select a shipping address or add a new one</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isAddingNewAddress ? (
                        <div className="space-y-4">
                          <ShippingAddressForm
                            onSubmit={handleAddressSubmit}
                            initialData={isEditingAddress || undefined}
                            isLoading={isAddressesLoading}
                            buttonText={isEditingAddress ? 'Update Address' : 'Save Address'}
                          />
                          <Button
                            variant="outline"
                            onClick={() => {
                              setIsAddingNewAddress(false);
                              setIsEditingAddress(null);
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {addresses.length > 0 ? (
                            <AddressList
                              addresses={addresses}
                              selectedAddressId={selectedAddress?.id}
                              onSelect={handleAddressSelect}
                              onEdit={handleAddressEdit}
                              onDelete={deleteAddress}
                              onSetDefault={setAsDefault}
                              isLoading={isAddressesLoading}
                            />
                          ) : (
                            <p className="text-gray-500">You don't have any saved addresses yet.</p>
                          )}

                          <Button
                            variant="outline"
                            onClick={() => setIsAddingNewAddress(true)}
                            disabled={isAddressesLoading}
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Add New Address
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Shipping Method Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Shipping Method</CardTitle>
                      <CardDescription>Select how you want your order delivered</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isShippingLoading ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                            <p className="text-sm text-gray-500">Loading shipping options...</p>
                          </div>
                        </div>
                      ) : shippingMethods.length > 0 ? (
                        <ShippingMethodSelector
                          methods={shippingMethods}
                          selectedMethodId={selectedShippingMethod}
                          onSelect={handleShippingMethodSelect}
                          disabled={!selectedAddress}
                        />
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-gray-500">No shipping methods available for this location.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <div className="flex justify-end">
                    <Button onClick={handleContinue} disabled={!selectedAddress}>
                      Continue to Payment
                    </Button>
                  </div>
                </div>
              )}

              {/* Payment Step - Placeholder for now */}
              {currentStep === 'payment' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Payment Method</CardTitle>
                      <CardDescription>This is a placeholder for the payment form</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-500 italic">
                        In a real implementation, this would include Stripe or PayPal payment forms.
                        For now, you can continue to the review step.
                      </p>
                    </CardContent>
                  </Card>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep('shipping')}>
                      Back to Shipping
                    </Button>
                    <Button onClick={handleContinue}>
                      Continue to Review
                    </Button>
                  </div>
                </div>
              )}

              {/* Review Step */}
              {currentStep === 'review' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Order Review</CardTitle>
                      <CardDescription>Review your order before placing it</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Order Items */}
                        <div>
                          <h3 className="font-medium mb-2">Items</h3>
                          <div className="space-y-2">
                            {cartItems.map(item => (
                              <div key={item.id} className="flex justify-between py-2 border-b">
                                <div>
                                  <p className="font-medium">{item.product?.name}</p>
                                  <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                                </div>
                                <p className="font-medium">
                                  £{((item.product?.sale_price || item.product?.price || 0) * item.quantity).toFixed(2)}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Shipping Address */}
                        {selectedAddress && (
                          <div>
                            <h3 className="font-medium mb-2">Shipping Address</h3>
                            <p>{selectedAddress.full_name}</p>
                            <p>{selectedAddress.street}</p>
                            <p>{selectedAddress.city}{selectedAddress.state ? `, ${selectedAddress.state}` : ''}</p>
                            <p>{selectedAddress.postal_code}</p>
                            <p>{selectedAddress.country}</p>
                            <p>{selectedAddress.phone}</p>
                          </div>
                        )}

                        {/* Shipping Method */}
                        {shippingMethod && (
                          <div>
                            <h3 className="font-medium mb-2">Shipping Method</h3>
                            <p>{shippingMethod.name} - £{shippingMethod.price.toFixed(2)}</p>
                            <p className="text-sm text-gray-500">{shippingMethod.estimatedDays}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep('payment')}>
                      Back to Payment
                    </Button>
                    <Button onClick={handlePlaceOrder} disabled={isProcessing}>
                      {isProcessing ? 'Processing...' : 'Place Order'}
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Order summary sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-8">
                <h2 className="text-xl font-bold mb-4">Order Summary</h2>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span>Subtotal (inc. VAT)</span>
                    <span>£{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>VAT included (20%)</span>
                    <span>£{tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>£{shippingCost.toFixed(2)}</span>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="flex justify-between text-lg font-bold mb-6">
                  <span>Total</span>
                  <span>£{total.toFixed(2)}</span>
                </div>

                {currentStep === 'review' && (
                  <Button
                    onClick={handlePlaceOrder}
                    className="w-full"
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Place Order'}
                  </Button>
                )}

                <p className="text-xs text-center text-gray-500 mt-4">
                  By placing your order, you agree to our Terms of Service and Privacy Policy
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;

