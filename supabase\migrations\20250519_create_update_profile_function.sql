-- Migration to create a secure RPC function for updating user profiles

-- Create a function to update user profiles securely
CREATE OR REPLACE FUNCTION public.update_user_profile(
  p_first_name TEXT DEFAULT NULL,
  p_last_name TEXT DEFAULT NULL,
  p_address TEXT DEFAULT NULL,
  p_city TEXT DEFAULT NULL,
  p_postal_code TEXT DEFAULT NULL,
  p_country TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Update the profile for the authenticated user
  UPDATE public.profiles
  SET
    first_name = COALESCE(p_first_name, first_name),
    last_name = COALESCE(p_last_name, last_name),
    address = COALESCE(p_address, address),
    city = COALESCE(p_city, city),
    postal_code = COALESCE(p_postal_code, postal_code),
    country = COALESCE(p_country, country),
    updated_at = now()
  WHERE id = auth.uid();
  
  RETURN TRUE;
END;
$$;
