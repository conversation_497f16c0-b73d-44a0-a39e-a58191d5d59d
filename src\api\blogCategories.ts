/**
 * API functions for managing blog categories
 */

import { supabase } from '@/lib/supabase';
import { BlogCategory } from '@/types/database';

export interface CreateCategoryData {
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
}

export interface UpdateCategoryData {
  name?: string;
  slug?: string;
  description?: string;
  parent_id?: string;
}

/**
 * Fetch all blog categories
 */
export async function fetchBlogCategories(): Promise<BlogCategory[]> {
  const { data, error } = await supabase
    .from('blog_categories')
    .select('*')
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching blog categories:', error);
    throw error;
  }

  return data || [];
}

/**
 * Fetch a single blog category by ID
 */
export async function fetchBlogCategory(id: string): Promise<BlogCategory | null> {
  const { data, error } = await supabase
    .from('blog_categories')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching blog category:', error);
    throw error;
  }

  return data;
}

/**
 * Create a new blog category
 */
export async function createBlogCategory(categoryData: CreateCategoryData): Promise<BlogCategory> {
  // Generate slug if not provided
  if (!categoryData.slug) {
    categoryData.slug = categoryData.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  const { data, error } = await supabase
    .from('blog_categories')
    .insert([categoryData])
    .select()
    .single();

  if (error) {
    console.error('Error creating blog category:', error);
    throw error;
  }

  return data;
}

/**
 * Update an existing blog category
 */
export async function updateBlogCategory(id: string, categoryData: UpdateCategoryData): Promise<BlogCategory> {
  const updateData = {
    ...categoryData,
    updated_at: new Date().toISOString()
  };

  const { data, error } = await supabase
    .from('blog_categories')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating blog category:', error);
    throw error;
  }

  return data;
}

/**
 * Delete a blog category
 */
export async function deleteBlogCategory(id: string): Promise<void> {
  // First check if any blogs are using this category
  const { data: blogsUsingCategory, error: checkError } = await supabase
    .from('blogs')
    .select('id')
    .eq('category', id)
    .limit(1);

  if (checkError) {
    console.error('Error checking category usage:', checkError);
    throw checkError;
  }

  if (blogsUsingCategory && blogsUsingCategory.length > 0) {
    throw new Error('Cannot delete category that is being used by blog posts. Please reassign or delete those posts first.');
  }

  const { error } = await supabase
    .from('blog_categories')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting blog category:', error);
    throw error;
  }
}

/**
 * Check if a category slug is available
 */
export async function isCategorySlugAvailable(slug: string, excludeId?: string): Promise<boolean> {
  let query = supabase
    .from('blog_categories')
    .select('id')
    .eq('slug', slug);

  if (excludeId) {
    query = query.neq('id', excludeId);
  }

  const { data, error } = await query.limit(1);

  if (error) {
    console.error('Error checking slug availability:', error);
    throw error;
  }

  return !data || data.length === 0;
}

/**
 * Get category usage statistics
 */
export async function getCategoryUsageStats(): Promise<Array<{ category_id: string; category_name: string; blog_count: number }>> {
  const { data, error } = await supabase
    .from('blogs')
    .select('category')
    .not('category', 'is', null);

  if (error) {
    console.error('Error fetching category usage:', error);
    throw error;
  }

  // Get all categories
  const categories = await fetchBlogCategories();
  
  // Count usage
  const usage = categories.map(category => {
    const count = data?.filter(blog => blog.category === category.id).length || 0;
    return {
      category_id: category.id,
      category_name: category.name,
      blog_count: count
    };
  });

  return usage.sort((a, b) => b.blog_count - a.blog_count);
}

/**
 * Generate a unique slug from a name
 */
export async function generateUniqueSlug(name: string, excludeId?: string): Promise<string> {
  let baseSlug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');

  let slug = baseSlug;
  let counter = 1;

  while (!(await isCategorySlugAvailable(slug, excludeId))) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}
