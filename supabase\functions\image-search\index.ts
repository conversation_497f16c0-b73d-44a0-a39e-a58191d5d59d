import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ImageResult {
  url: string;
  thumbnail: string;
  title: string;
  source: string;
}

async function searchPixabay(query: string, count: number = 5): Promise<ImageResult[]> {
  const apiKey = Deno.env.get('PIXABAY_API_KEY');
  
  if (!apiKey) {
    console.error('Pixabay API key not found');
    return [];
  }

  try {
    const cleanQuery = encodeURIComponent(query.trim());
    const url = `https://pixabay.com/api/?key=${apiKey}&q=${cleanQuery}&image_type=photo&per_page=${Math.min(count, 20)}&safesearch=true&category=backgrounds,business,health,nature,places,science&min_width=800&min_height=600`;

    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status}`);
    }

    const data = await response.json();
    
    return data.hits?.map((hit: any) => ({
      url: hit.webformatURL || hit.largeImageURL,
      thumbnail: hit.previewURL,
      title: hit.tags,
      source: 'pixabay'
    })) || [];

  } catch (error) {
    console.error('Pixabay search failed:', error);
    return [];
  }
}

async function searchUnsplash(query: string, count: number = 5): Promise<ImageResult[]> {
  const accessKey = Deno.env.get('UNSPLASH_ACCESS_KEY');
  
  if (!accessKey) {
    console.log('Unsplash access key not found, skipping');
    return [];
  }

  try {
    const cleanQuery = encodeURIComponent(query.trim());
    const url = `https://api.unsplash.com/search/photos?query=${cleanQuery}&per_page=${count}&orientation=landscape`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Client-ID ${accessKey}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Unsplash API error: ${response.status}`);
    }

    const data = await response.json();
    
    return data.results?.map((photo: any) => ({
      url: photo.urls.regular,
      thumbnail: photo.urls.thumb,
      title: photo.alt_description || photo.description || query,
      source: 'unsplash'
    })) || [];

  } catch (error) {
    console.error('Unsplash search failed:', error);
    return [];
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { query, count = 5 } = await req.json()
    
    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Query parameter is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`Searching for images: ${query}`)
    
    // Search multiple sources
    const [pixabayResults, unsplashResults] = await Promise.all([
      searchPixabay(query, count),
      searchUnsplash(query, Math.max(1, count - 3)) // Get fewer from Unsplash as backup
    ]);

    // Combine results, prioritizing Pixabay (free tier friendly)
    const allResults = [...pixabayResults, ...unsplashResults];
    const limitedResults = allResults.slice(0, count);

    const response = {
      images: limitedResults,
      totalHits: limitedResults.length,
      query: query,
      sources: [...new Set(limitedResults.map(r => r.source))]
    };

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Image search error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        images: [],
        totalHits: 0
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
