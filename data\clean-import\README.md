# Clean Product Import

This folder contains files for a clean, organized product import process.

## Files

- `product_template.csv`: CSV template with headers matching the database schema
- `transform_products.js`: <PERSON>ript to transform data from the original CSV to our template format
- `import_products.sql`: SQL script to import the transformed data into the database

## Process

1. Fill the template CSV with product data (manually or using the transform script)
2. Review the CSV to ensure data is correct
3. Run the SQL import script to load the data into the database
4. Verify the products in the admin interface

## Notes

- Images should be in the format `product-images/filename.webp`
- Product options should be formatted as JSON in the `option_definitions` column
- For variants, use the `variants.csv` template
