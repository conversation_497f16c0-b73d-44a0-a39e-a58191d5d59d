// Script to update product option pricing from CSV variant data
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import Papa from 'papaparse';
import dotenv from 'dotenv';

// Configure dotenv
dotenv.config();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Log the Supabase connection details (without the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'undefined';
console.log(`Connecting to Supabase at: ${supabaseUrl} with key: ${maskedKey}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase URL or key is missing. Make sure your .env file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const CSV_FILE_PATH = path.resolve(__dirname, '../docs/catalog_products.csv');
const DRY_RUN = process.argv.includes('--dry-run');

// Utility function to normalize text for comparison
function normalizeText(text) {
  if (!text) return '';
  return text.toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric
    .replace(/\\s+/g, ''); // Remove whitespace
}

// Utility function to calculate similarity between two strings (0-1)
function stringSimilarity(str1, str2) {
  const s1 = normalizeText(str1);
  const s2 = normalizeText(str2);
  
  if (s1 === s2) return 1; // Exact match
  if (s1.includes(s2) || s2.includes(s1)) return 0.9; // One is subset of other
  
  // Calculate Levenshtein distance
  const len1 = s1.length;
  const len2 = s2.length;
  const max = Math.max(len1, len2);
  if (max === 0) return 1; // Both empty strings
  
  const distance = levenshteinDistance(s1, s2);
  return 1 - distance / max;
}

// Levenshtein distance calculation
function levenshteinDistance(str1, str2) {
  const len1 = str1.length;
  const len2 = str2.length;
  
  // Create matrix
  const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
  
  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  // Fill matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }
  
  return matrix[len1][len2];
}

// Main function
async function updateVariantPricing() {
  console.log('Starting variant pricing update...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);
  
  // Set a similarity threshold for name matching (0.8 = 80% similar)
  const SIMILARITY_THRESHOLD = 0.8;

  try {
    // 1. Read and parse the CSV file
    console.log('Reading CSV file...');
    const csvData = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    
    // Parse CSV
    const parseResult = Papa.parse(csvData, {
      header: true,
      skipEmptyLines: true,
    });
    
    if (parseResult.errors.length > 0) {
      console.error('CSV parsing errors:', parseResult.errors);
      return;
    }
    
    const csvProducts = parseResult.data;
    console.log(`Found ${csvProducts.length} rows in CSV file`);

    // 2. Process the CSV data to extract products and their variants
    const productsMap = new Map(); // Map to store products by ID

    // First pass: identify main products with options
    for (let i = 0; i < csvProducts.length; i++) {
      const row = csvProducts[i];
      const productId = row.handleId;
      const fieldType = row.fieldType;
      
      if (!productId || !fieldType) continue;
      
      // Only process main products with options
      if (fieldType === 'Product' && row.productOptionName1 && row.productOptionValues1) {
        // Extract option values
        const optionName = row.productOptionName1;
        const optionValues = row.productOptionValues1.split(';').map(v => v.trim());
        const basePrice = parseFloat(row.price) || 0;
        
        if (optionValues.length > 0) {
          productsMap.set(productId, {
            id: productId,
            name: row.name,
            basePrice: basePrice,
            optionName: optionName,
            optionValues: optionValues,
            variants: []
          });
        }
      }
    }
    
    // Second pass: find variants for each product
    for (let i = 0; i < csvProducts.length; i++) {
      const row = csvProducts[i];
      const productId = row.handleId;
      const fieldType = row.fieldType;
      
      if (!productId || fieldType !== 'Variant') continue;
      
      // Extract parent product ID
      const parentId = productId.split(',')[0];
      
      if (productsMap.has(parentId)) {
        const product = productsMap.get(parentId);
        const optionName = product.optionName;
        
        // Get the option value for this variant
        const optionValue = row[optionName];
        
        if (optionValue) {
          // Add variant to product
          product.variants.push({
            optionValue: optionValue,
            price: parseFloat(row.price) || 0
          });
        }
      }
    }
    
    console.log(`Identified ${productsMap.size} products with options in CSV`);
    
    // Log some sample products to help with debugging
    let sampleCount = 0;
    for (const [productId, product] of productsMap.entries()) {
      if (sampleCount < 3 && product.variants.length > 0) {
        console.log(`Sample product: ${product.name} (${productId})`);
        console.log(`  Base price: ${product.basePrice}`);
        console.log(`  Option name: ${product.optionName}`);
        console.log(`  Option values: ${product.optionValues.join(', ')}`);
        console.log(`  Variants: ${product.variants.length}`);
        product.variants.forEach(v => {
          console.log(`    ${v.optionValue}: ${v.price} (adjustment: ${v.price})`);
        });
        sampleCount++;
      }
    }

    // 3. Fetch products from the database
    console.log('Fetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('option_name1', 'is', null)
      .not('option_values1', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts?.length || 0} products in database with options`);

    // 4. Match products and update price adjustments
    const productsToUpdate = [];
    const productsWithIssues = [];
    const matchedProducts = [];

    for (const dbProduct of dbProducts) {
      // Skip products without options
      if (!dbProduct.option_values1 || !dbProduct.option_name1) continue;

      // Get option values as an array
      const optionValues = dbProduct.option_values1.split(';').map(v => v.trim());
      if (optionValues.length === 0) continue;

      // Get the base price
      const basePrice = dbProduct.price || 0;
      if (basePrice <= 0) {
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Invalid base price: ${basePrice}`,
        });
        continue;
      }

      // Find the best matching product from CSV
      let bestMatch = null;
      let bestSimilarity = 0;
      
      for (const [productId, csvProduct] of productsMap.entries()) {
        const similarity = stringSimilarity(dbProduct.name, csvProduct.name);
        
        if (similarity > bestSimilarity && similarity >= SIMILARITY_THRESHOLD) {
          bestMatch = csvProduct;
          bestSimilarity = similarity;
        }
      }
      
      if (bestMatch) {
        matchedProducts.push({
          dbProduct: dbProduct.name,
          csvProduct: bestMatch.name,
          similarity: bestSimilarity
        });
        
        try {
          // Calculate price adjustments for each option value
          const priceAdjustments = [];
          let allValuesFound = true;
          
          for (const optionValue of optionValues) {
            // Find the variant with this option value
            const variant = bestMatch.variants.find(v => 
              stringSimilarity(v.optionValue, optionValue) >= 0.8
            );
            
            if (variant) {
              // Calculate the price adjustment
              const adjustment = variant.price;
              priceAdjustments.push(adjustment);
            } else {
              // If we can't find a variant for this option value, use 0 as the adjustment
              console.log(`  No variant found for option value '${optionValue}' in product ${dbProduct.name}`);
              priceAdjustments.push(0);
              allValuesFound = false;
            }
          }
          
          // Format price adjustments as semicolon-separated string
          const priceAdjustmentStr = priceAdjustments.join(';');
          
          // Add to update list
          productsToUpdate.push({
            id: dbProduct.id,
            name: dbProduct.name,
            csvName: bestMatch.name,
            similarity: bestSimilarity,
            option_price_adjustment1: priceAdjustmentStr,
            option_values: optionValues.join(', '),
            adjustments: priceAdjustments.join(', ')
          });
          
          console.log(`Matched: ${dbProduct.name} -> ${bestMatch.name} (${bestSimilarity.toFixed(2)})`);
          console.log(`  Option values: ${optionValues.join(', ')}`);
          console.log(`  Price adjustments: ${priceAdjustmentStr}`);
          
          if (!allValuesFound) {
            productsWithIssues.push({
              id: dbProduct.id,
              name: dbProduct.name,
              issue: 'Some option values could not be matched to variants',
            });
          }
        } catch (err) {
          console.error(`Error processing product ${dbProduct.name}:`, err);
          productsWithIssues.push({
            id: dbProduct.id,
            name: dbProduct.name,
            issue: `Error: ${err.message}`,
          });
        }
      } else {
        console.log(`No CSV match found for DB product: ${dbProduct.name}`);
      }
    }

    // 5. Update products in the database (if not dry run)
    if (productsToUpdate.length > 0) {
      console.log(`\nReady to update ${productsToUpdate.length} products with price adjustments`);
      
      if (!DRY_RUN) {
        console.log('Updating products in database...');
        
        // Update products in batches to avoid hitting API limits
        const BATCH_SIZE = 50;
        for (let i = 0; i < productsToUpdate.length; i += BATCH_SIZE) {
          const batch = productsToUpdate.slice(i, i + BATCH_SIZE);
          
          // Update each product individually to avoid issues
          for (const product of batch) {
            const { error } = await supabase
              .from('products')
              .update({ option_price_adjustment1: product.option_price_adjustment1 })
              .eq('id', product.id);
              
            if (error) {
              console.error(`Error updating product ${product.id}:`, error);
              productsWithIssues.push({
                id: product.id,
                name: product.name,
                issue: `Update error: ${error.message}`,
              });
            }
          }
          
          console.log(`Updated batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(productsToUpdate.length / BATCH_SIZE)}`);
        }
        
        console.log('Update completed successfully!');
      } else {
        console.log('DRY RUN: No changes were made to the database');
        console.log('\nSample of products that would be updated:');
        for (let i = 0; i < Math.min(10, productsToUpdate.length); i++) {
          const product = productsToUpdate[i];
          console.log(`- ${product.name} -> ${product.csvName} (similarity: ${product.similarity.toFixed(2)}):`);
          console.log(`  Options: ${product.option_values}`);
          console.log(`  Adjustments: ${product.adjustments}`);
        }
      }
    } else {
      console.log('No products to update');
    }

    // 6. Report issues
    if (productsWithIssues.length > 0) {
      console.log(`\n${productsWithIssues.length} products had issues:`);
      productsWithIssues.forEach(p => {
        console.log(`- ${p.name} (${p.id}): ${p.issue}`);
      });
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
updateVariantPricing().catch(console.error);
