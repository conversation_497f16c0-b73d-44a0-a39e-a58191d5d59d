import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bug, X, Wrench } from "lucide-react";
import { ShippingMethod } from './ShippingMethodSelector';
import { shippingService } from '@/services/shippingService';
import { useQueryClient } from '@tanstack/react-query';

interface DebuggingPanelProps {
  methods: ShippingMethod[];
}

export function DebuggingPanel({ methods }: DebuggingPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [fixResult, setFixResult] = useState<string | null>(null);
  const queryClient = useQueryClient();
  
  // Filter methods with is_active true
  const activeByTrue = methods.filter(m => m.is_active === true);
  
  // Filter methods that don't have is_active=false
  const activeByNotFalse = methods.filter(m => m.is_active !== false);
  
  const applyLocalFix = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      // 1. Force the local cache to only include Standard Shipping as active
      const fixedMethods = methods.map(method => ({
        ...method,
        is_active: method.id === '7e357480-9d1e-4b0c-bcc8-763d9120d7b6' // Standard Shipping ID
      }));
      
      // 2. Update the query cache
      queryClient.setQueryData(['checkout-shipping', 'United Kingdom'], fixedMethods);
      
      // 3. Force a hard refresh with direct database access
      const freshMethods = await shippingService.getCheckoutShippingMethods('United Kingdom');
      console.log('Directly fetched fresh shipping methods:', freshMethods);
      
      // 4. Update the cache again with the fresh data
      queryClient.setQueryData(['checkout-shipping', 'United Kingdom'], freshMethods);
      
      // 5. Invalidate queries to trigger a refresh
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      
      setFixResult('✅ Fix applied successfully! The page should update with only Standard Shipping.');
    } catch (error) {
      console.error('Error applying fix:', error);
      setFixResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsFixing(false);
    }
  };
  
  const forceDatabaseFix = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      // Call a direct script to fix the database
      const response = await fetch('/api/fix-shipping-methods', { method: 'POST' });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}`);
      }
      
      const result = await response.json();
      
      // Force refresh after database update
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      localStorage.setItem('shipping_refresh_timestamp', Date.now().toString());
      
      setFixResult(`✅ Database fix applied: ${result.message}`);
    } catch (error) {
      console.error('Error applying database fix:', error);
      setFixResult('❌ Error applying database fix. See console for details.');
    } finally {
      setIsFixing(false);
    }
  };
  
  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-50"
      >
        <Bug className="h-4 w-4 mr-2" />
        Debug Shipping
      </Button>
      
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-4xl max-h-[80vh] overflow-auto">
            <CardHeader className="flex flex-row items-center justify-between sticky top-0 bg-background z-10">
              <CardTitle>Shipping Methods Debug</CardTitle>
              <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Quick Summary</h3>
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={applyLocalFix}
                    disabled={isFixing}
                  >
                    <Wrench className="h-4 w-4 mr-2" />
                    Apply Frontend Fix
                  </Button>
                </div>
              </div>
              
              {fixResult && (
                <div className={`p-3 rounded-md ${fixResult.startsWith('✅') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                  {fixResult}
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border p-4 rounded-md">
                  <p className="font-medium">Total methods: {methods.length}</p>
                  <p className="font-medium">Active methods (is_active === true): {activeByTrue.length}</p>
                  <p className="font-medium">Loose active (is_active !== false): {activeByNotFalse.length}</p>
                  <p className="font-medium">Frontend display count: {activeByTrue.length}</p>
                </div>
                
                <div className="border p-4 rounded-md">
                  <p className="font-medium">Standard Shipping Active: {methods.find(m => m.name === 'Standard Shipping')?.is_active === true ? 'Yes ✅' : 'No ❌'}</p>
                  <p className="font-medium">Types of is_active values: {Array.from(new Set(methods.map(m => typeof m.is_active))).join(', ')}</p>
                  <p className="font-medium">Abnormal values: {methods.some(m => typeof m.is_active !== 'boolean') ? 'Yes ❌' : 'No ✅'}</p>
                  <p className="font-medium">Next Day Delivery Active: {methods.find(m => m.name === 'Next Day Delivery')?.is_active === true ? 'Yes ❌' : 'No ✅'}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Raw Methods from API ({methods.length})</h3>
                <div className="space-y-2">
                  {methods.map(method => (
                    <div key={method.id} className="border p-3 rounded-md">
                      <div className="flex justify-between">
                        <span className="font-medium">{method.name}</span>
                        <Badge variant={method.is_active ? "default" : "outline"}>
                          is_active: {String(method.is_active)} ({typeof method.is_active})
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">ID: {method.id}</div>
                      <div className="text-sm text-gray-500">{method.description}</div>
                      <div className="text-xs text-gray-400 mt-1">Raw data: {JSON.stringify(method)}</div>
                      <div className="mt-2">
                        <p className="text-xs text-blue-500">
                          method.is_active === true: {String(method.is_active === true)}
                        </p>
                        <p className="text-xs text-blue-500">
                          method.is_active !== false: {String(method.is_active !== false)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Filtered by is_active === true ({activeByTrue.length})</h3>
                  <div className="space-y-2">
                    {activeByTrue.length > 0 ? activeByTrue.map(method => (
                      <div key={method.id} className="border p-3 rounded-md">
                        <div className="font-medium">{method.name}</div>
                        <div className="text-sm text-gray-500">is_active: {String(method.is_active)}</div>
                      </div>
                    )) : (
                      <div className="text-sm text-gray-500 border p-3 rounded-md">No methods pass this filter</div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-2">Filtered by is_active !== false ({activeByNotFalse.length})</h3>
                  <div className="space-y-2">
                    {activeByNotFalse.length > 0 ? activeByNotFalse.map(method => (
                      <div key={method.id} className="border p-3 rounded-md">
                        <div className="font-medium">{method.name}</div>
                        <div className="text-sm text-gray-500">is_active: {String(method.is_active)}</div>
                      </div>
                    )) : (
                      <div className="text-sm text-gray-500 border p-3 rounded-md">No methods pass this filter</div>
                    )}
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Technical Information</h3>
                <pre className="bg-gray-100 p-3 rounded-md text-xs overflow-auto">
                  {JSON.stringify(methods, null, 2)}
                </pre>
              </div>
              
              <Button onClick={() => {
                localStorage.setItem('shipping_refresh_timestamp', Date.now().toString());
                window.location.reload();
              }} className="w-full">
                Force Refresh Page and Cache
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
} 