export function useCheckoutShipping(country: string = 'United Kingdom') {
  const queryClient = useQueryClient();

  // Remove the problematic effect that was causing infinite loops
  // No useEffect cache invalidation here!

  const {
    data: shippingMethods = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['checkout-shipping', country],
    queryFn: async () => {
      console.log('Fetching shipping methods for checkout');
      const methods = await shippingService.getCheckoutShippingMethods(country);
      console.log('Received shipping methods:', methods);
      // Extra safety filter
      return methods.filter(method => method && method.id && method.is_active !== false);
    },
    enabled: !!country,
    staleTime: 30000, // Cache data for 30 seconds
    gcTime: 60000, // Keep unused data for 1 minute
    refetchOnMount: false, // Don't refetch automatically on mount
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
  });

  const refreshShippingMethods = () => {
    console.log('Manually refreshing shipping methods');
    // Force complete cache removal for shipping
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    // Wait a tiny bit before refetching to ensure cache is cleared
    setTimeout(() => refetch(), 10);
  };

  return {
    shippingMethods,
    isLoading,
    error,
    refreshShippingMethods,
  };
} 