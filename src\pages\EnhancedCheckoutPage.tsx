import { useState, useEffect } from 'react';
import { useCart } from '@/hooks/useCart';
import { useAuth } from '@/hooks/auth.basic';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { AppliedDiscount } from '@/types/discount';
import { ShippingAddressForm, ShippingAddressFormValues } from '@/components/checkout/ShippingAddressForm';
import { useAddresses, Address, AddressInput } from '@/hooks/useAddresses';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  PlusCircle,
  ArrowLeft,
  ArrowRight,
  Loader2,
  ShoppingBag,
  CreditCard,
  RefreshCw
} from 'lucide-react';

// Import our enhanced components
import { CheckoutProgress } from '@/components/checkout/CheckoutProgress';
import { EnhancedOrderSummary } from '@/components/checkout/EnhancedOrderSummary';
import { AddressCard } from '@/components/checkout/AddressCard';
import { EnhancedShippingMethodSelector } from '@/components/checkout/EnhancedShippingMethodSelector';

// Import shipping service and hooks
import { useCheckoutShipping } from '@/hooks/useShipping';
import { shippingService } from '@/services/shippingService';

// Define enhanced shipping method type
type EnhancedShippingMethod = {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
  icon: 'standard' | 'express' | 'nextDay';
};

// We'll use the shipping service to get real-time shipping methods
// This ensures we always have the latest shipping methods, including active status

// Define checkout steps
type CheckoutStep = 'shipping' | 'payment' | 'review';

const EnhancedCheckoutPage = () => {
  const { cartItems, subtotal, clearCart, isLoading: isCartLoading } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    addresses,
    defaultAddress,
    isLoading: isAddressesLoading,
    addAddress,
    updateAddress,
    deleteAddress,
    setAsDefault
  } = useAddresses();
  
  // State for checkout process
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [selectedShippingMethod, setSelectedShippingMethod] = useState<string>('');
  const [isAddingNewAddress, setIsAddingNewAddress] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState<Address | null>(null);
  const [appliedDiscount, setAppliedDiscount] = useState<AppliedDiscount | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Get customer country from selected address (default to UK)
  const customerCountry = selectedAddress?.country || 'United Kingdom';
  
  // Get shipping methods from the shipping service for the customer's country
  const { shippingMethods, isLoading: isShippingLoading, refreshShippingMethods } = useCheckoutShipping(customerCountry);

  // Calculate order totals
  const shippingMethod = shippingMethods.find(method => method.id === selectedShippingMethod) || (shippingMethods.length > 0 ? shippingMethods[0] : { price: 0 });
  const shippingCost = shippingMethod.price || 0;
  // Extract VAT from subtotal (assuming prices already include VAT)
  const vatRate = 0.2; // 20% VAT
  const subtotalExVat = subtotal / (1 + vatRate);
  const tax = subtotal - subtotalExVat; // VAT amount

  // Calculate discount amount
  const discountAmount = appliedDiscount ? appliedDiscount.discountAmount : 0;

  // Calculate total with discount
  const total = subtotal + shippingCost; // Total before discount
  const finalTotal = total - discountAmount; // Final total after discount

  // Handle applying and removing discount
  const handleApplyDiscount = (discount: AppliedDiscount) => {
    setAppliedDiscount(discount);
    toast({
      title: "Discount Applied",
      description: `Discount code ${discount.code} has been applied to your order.`,
    });
  };

  const handleRemoveDiscount = () => {
    setAppliedDiscount(null);
    toast({
      title: "Discount Removed",
      description: "The discount code has been removed from your order.",
    });
  };

  // Set default address when addresses load
  useEffect(() => {
    if (defaultAddress && !selectedAddress) {
      setSelectedAddress(defaultAddress);
    }
  }, [defaultAddress]);
  
  // Set initial shipping method when shipping methods load
  useEffect(() => {
    if (shippingMethods.length > 0 && !selectedShippingMethod) {
      setSelectedShippingMethod(shippingMethods[0].id);
    }
  }, [shippingMethods]);
  
  // Function to handle refreshing shipping methods
  const handleRefreshShippingMethods = async () => {
    setIsRefreshing(true);
    await refreshShippingMethods();
    setIsRefreshing(false);
    toast({
      title: "Shipping Methods Refreshed",
      description: "The latest shipping options have been loaded.",
    });
  };

  // Redirect if not logged in
  if (!user) {
    navigate('/auth');
    return null;
  }

  // Show empty cart message
  if (cartItems.length === 0 && !isCartLoading) {
    return (
      <div className="container-custom py-12 text-center">
        <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
          <ShoppingBag className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h1 className="text-2xl font-bold mb-4">Your cart is empty</h1>
          <p className="mb-6 text-gray-600">Add some items to your cart before checking out.</p>
          <Button
            onClick={() => navigate('/shop')}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  }

  // Handle new address submission
  const handleAddressSubmit = async (formData: ShippingAddressFormValues) => {
    try {
      // Convert form data to AddressInput
      const addressData: AddressInput = {
        full_name: formData.full_name,
        street: formData.street,
        city: formData.city,
        state: formData.state,
        postal_code: formData.postal_code,
        country: formData.country,
        phone: formData.phone,
        is_default: formData.save_address
      };

      if (isEditingAddress) {
        // Update existing address
        const success = await updateAddress(isEditingAddress.id, addressData);
        if (success) {
          setIsEditingAddress(null);
          setIsAddingNewAddress(false);
          toast({
            title: "Address Updated",
            description: "Your shipping address has been updated.",
          });
        }
      } else {
        // Add new address
        const newAddress = await addAddress(addressData);
        if (newAddress) {
          setSelectedAddress(newAddress);
          setIsAddingNewAddress(false);
          toast({
            title: "Address Added",
            description: "Your shipping address has been saved.",
          });
        }
      }
    } catch (error) {
      console.error('Error saving address:', error);
      toast({
        title: "Error",
        description: "There was a problem saving your address.",
        variant: "destructive",
      });
    }
  };

  // Handle address selection
  const handleAddressSelect = (address: Address) => {
    setSelectedAddress(address);
  };

  // Handle address edit
  const handleAddressEdit = (address: Address) => {
    setIsEditingAddress(address);
    setIsAddingNewAddress(true);
  };

  // Handle shipping method selection
  const handleShippingMethodSelect = (methodId: string) => {
    setSelectedShippingMethod(methodId);
  };

  // Handle continue to next step
  const handleContinue = () => {
    if (currentStep === 'shipping') {
      if (!selectedAddress) {
        toast({
          title: "Shipping Address Required",
          description: "Please select or add a shipping address to continue.",
          variant: "destructive",
        });
        return;
      }
      setCurrentStep('payment');
    } else if (currentStep === 'payment') {
      // In a real implementation, this would validate payment info
      setCurrentStep('review');
    }
  };

  // Handle back to previous step
  const handleBack = () => {
    if (currentStep === 'payment') {
      setCurrentStep('shipping');
    } else if (currentStep === 'review') {
      setCurrentStep('payment');
    }
  };

  // Handle place order
  const handlePlaceOrder = async () => {
    if (!user || !selectedAddress) {
      toast({
        title: "Information Missing",
        description: "Please complete all required information to place your order.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // In a real implementation, this would create an order in the database
      // with the discount code and amount applied

      // Example of how the order creation would include discount information
      const orderData = {
        user_id: user.id,
        shipping_address_id: selectedAddress.id,
        shipping_method: selectedShippingMethod,
        subtotal: subtotal,
        shipping_cost: shippingCost,
        tax: tax,
        discount_code: appliedDiscount?.code || null,
        discount_amount: discountAmount,
        total_amount: finalTotal,
        status: 'pending',
        // Add other order details as needed
      };

      console.log('Creating order with data:', orderData);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, you would save the order to the database:
      // const { data, error } = await supabase.from('orders').insert(orderData).select().single();

      // Clear the cart and applied discount
      clearCart();
      setAppliedDiscount(null);

      // Show success message
      toast({
        title: "Order Placed Successfully",
        description: "Thank you for your order! We'll send you a confirmation email shortly.",
      });

      // Redirect to order confirmation page
      navigate('/order-confirmation');
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: "Error",
        description: "There was a problem placing your order. Please try again.",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-gradient-to-b from-gray-50 to-gray-100 min-h-screen py-8">
      <div className="container-custom">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Checkout</h1>
          <p className="text-gray-600">Complete your purchase securely</p>
        </div>

        {/* Checkout Progress Indicator */}
        <CheckoutProgress currentStep={currentStep} />

        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="shadow-md border-0 mb-8">
              {/* Checkout steps */}
              {currentStep === 'shipping' && (
                <>
                  <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-gray-100">
                    <CardTitle className="text-xl">Shipping Information</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    {!isAddingNewAddress ? (
                      <>
                        {/* Address selection */}
                        {addresses.length > 0 ? (
                          <div className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-4">
                              {addresses.map(address => (
                                <AddressCard
                                  key={address.id}
                                  address={address}
                                  isSelected={selectedAddress?.id === address.id}
                                  onSelect={handleAddressSelect}
                                  onEdit={handleAddressEdit}
                                />
                              ))}
                            </div>

                            <Button
                              variant="outline"
                              onClick={() => setIsAddingNewAddress(true)}
                              className="mt-4"
                            >
                              <PlusCircle className="mr-2 h-4 w-4" />
                              Add New Address
                            </Button>
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <p className="text-gray-500 mb-4">You don't have any saved addresses yet.</p>
                            <Button onClick={() => setIsAddingNewAddress(true)}>
                              <PlusCircle className="mr-2 h-4 w-4" />
                              Add New Address
                            </Button>
                          </div>
                        )}

                        {/* Shipping method selection */}
                        {selectedAddress && (
                          <div className="mt-8">
                            <div className="flex justify-between items-center mb-4">
                              <h3 className="text-lg font-medium">Shipping Method</h3>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={handleRefreshShippingMethods}
                                disabled={isRefreshing || isShippingLoading}
                              >
                                {isRefreshing ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Refreshing...
                                  </>
                                ) : (
                                  <>
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    Refresh Options
                                  </>
                                )}
                              </Button>
                            </div>
                            {isShippingLoading ? (
                              <div className="flex justify-center items-center p-8">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                <span className="ml-2">Loading shipping options...</span>
                              </div>
                            ) : shippingMethods.length === 0 ? (
                              <div className="p-6 text-center border rounded-md">
                                <p className="text-gray-500">No shipping methods available for your location.</p>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={handleRefreshShippingMethods}
                                  className="mt-2"
                                >
                                  Try Again
                                </Button>
                              </div>
                            ) : (
                              <EnhancedShippingMethodSelector
                                methods={shippingMethods}
                                selectedMethodId={selectedShippingMethod}
                                onSelect={handleShippingMethodSelect}
                              />
                            )}
                          </div>
                        )}
                      </>
                    ) : (
                      <div>
                        <Button
                          variant="ghost"
                          onClick={() => {
                            setIsAddingNewAddress(false);
                            setIsEditingAddress(null);
                          }}
                          className="mb-4"
                        >
                          <ArrowLeft className="mr-2 h-4 w-4" />
                          Back to Addresses
                        </Button>

                        <ShippingAddressForm
                          onSubmit={handleAddressSubmit}
                          initialData={isEditingAddress || undefined}
                          isLoading={isAddressesLoading}
                          buttonText={isEditingAddress ? "Update Address" : "Save Address"}
                        />
                      </div>
                    )}
                  </CardContent>
                </>
              )}

              {currentStep === 'payment' && (
                <>
                  <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-gray-100">
                    <CardTitle className="text-xl">Payment Information</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="text-center py-12">
                      <CreditCard className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium mb-2">Payment Integration Coming Soon</h3>
                      <p className="text-gray-500 mb-4">
                        This is a demo application. In a real implementation, this would integrate with Stripe or PayPal.
                      </p>
                      <p className="text-sm text-gray-400 mb-8">
                        For now, you can proceed to review your order.
                      </p>
                    </div>
                  </CardContent>
                </>
              )}

              {currentStep === 'review' && (
                <>
                  <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-gray-100">
                    <CardTitle className="text-xl">Review Your Order</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-6">
                      {/* Order items */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">Order Items</h3>
                        <div className="space-y-4">
                          {cartItems.map(item => (
                            <div key={item.id} className="flex items-center p-4 border rounded-lg">
                              {item.product?.image && (
                                <div className="w-16 h-16 rounded-md overflow-hidden mr-4">
                                  <img
                                    src={item.product.image}
                                    alt={item.product?.name}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              )}
                              <div className="flex-grow">
                                <h4 className="font-medium">{item.product?.name}</h4>
                                {item.variant && typeof item.variant === 'string' && (
                                  <p className="text-sm text-gray-500">
                                    Variant: {item.variant}
                                  </p>
                                )}
                                {item.variant && typeof item.variant !== 'string' && (
                                  <p className="text-sm text-gray-500">
                                    Selected options
                                  </p>
                                )}
                                <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">
                                  £{((item.product?.sale_price || item.product?.price || 0) * item.quantity).toFixed(2)}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Shipping Address */}
                      {selectedAddress && (
                        <div>
                          <h3 className="text-lg font-medium mb-4">Shipping Address</h3>
                          <AddressCard
                            address={selectedAddress}
                            isSelected={true}
                            onEdit={() => {
                              setIsEditingAddress(selectedAddress);
                              setIsAddingNewAddress(true);
                              setCurrentStep('shipping');
                            }}
                          />
                        </div>
                      )}

                      {/* Shipping Method */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">Shipping Method</h3>
                        <EnhancedShippingMethodSelector
                          methods={[shippingMethod]}
                          selectedMethodId={selectedShippingMethod}
                          onSelect={() => {}}
                        />
                      </div>
                    </div>
                  </CardContent>
                </>
              )}
            </Card>

            {/* Navigation buttons */}
            <div className="flex justify-between">
              {currentStep !== 'shipping' && (
                <Button variant="outline" onClick={handleBack}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
              )}

              {currentStep !== 'review' ? (
                <Button
                  onClick={handleContinue}
                  className="ml-auto bg-sage-500 hover:bg-sage-600"
                  disabled={isAddingNewAddress}
                >
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              ) : (
                <Button
                  onClick={handlePlaceOrder}
                  className="ml-auto bg-sage-500 hover:bg-sage-600"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Place Order
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Order summary sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <EnhancedOrderSummary
                subtotal={subtotal}
                tax={tax}
                shippingCost={shippingCost}
                total={total}
                isProcessing={isProcessing}
                onPlaceOrder={currentStep === 'review' ? handlePlaceOrder : undefined}
                showPlaceOrderButton={currentStep === 'review'}
                discount={appliedDiscount}
                onApplyDiscount={handleApplyDiscount}
                onRemoveDiscount={handleRemoveDiscount}
                showDiscountInput={currentStep === 'payment' || currentStep === 'review'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedCheckoutPage;
