const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');

// Configuration
const CONFIG = {
  inputDir: 'data',
  outputDir: 'data/output',
  inputFileName: process.argv[2] || 'completely_new_products.csv',
  productsOutputFileName: 'import-ready-products.csv',
  variantsOutputFileName: 'import-ready-variants.csv',
};

/**
 * Transform image URL by removing ~mv2 and converting to .webp
 * @param {string} url - The image URL to transform
 * @returns {string} - The transformed URL
 */
function transformImageUrl(url) {
  if (!url) return '';
  
  // Extract just the filename without path (if any)
  const filename = url.split('/').pop();
  
  // Handle specific format from catalog: 7caa35_6a5c1b6181f543109d047a419a70a6aa~mv2.jpg
  // Remove ~mv2 from image URLs
  let transformedFilename = filename.replace(/~mv2/g, '');
  
  // Convert to .webp if it's a jpg or png
  if (transformedFilename.match(/\.(jpg|jpeg|png)$/i)) {
    transformedFilename = transformedFilename.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  
  // Use the correct Supabase storage URL
  return `https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/${transformedFilename}`;
}

/**
 * Determine the appropriate display type for an option based on its name
 * @param {string} optionName - The name of the option
 * @returns {string} - The display type for the option
 */
function getDisplayTypeForOption(optionName) {
  // Normalize the option name to lowercase for comparison
  const normalizedName = optionName.toLowerCase();
  
  // Determine display type based on option name
  if (normalizedName.includes('color') || normalizedName.includes('colour')) {
    return 'color_swatch';
  } else if (normalizedName.includes('size') || 
             normalizedName.includes('pack') || 
             normalizedName.includes('weight')) {
    return 'button';
  } else if (normalizedName.includes('material') || 
             normalizedName.includes('fabric')) {
    return 'material_button';
  } else {
    // Default to button for unknown option types
    return 'button';
  }
}

/**
 * Extract option values from option description
 * @param {string} optionDescription - The option description string
 * @returns {string[]} - Array of option values
 */
function extractOptionValues(optionDescription) {
  if (!optionDescription) return [];
  
  // Split by comma and clean up values
  return optionDescription
    .split(',')
    .map(value => value.trim())
    .filter(value => value && value !== 'DROP_DOWN');
}

/**
 * Parse price from string
 * @param {string} price - The price string
 * @returns {number} - The parsed price
 */
function parsePrice(price) {
  if (!price) return 0;
  
  // Remove currency symbols and convert to number
  const cleanPrice = price.replace(/[£$€]/g, '').trim();
  return parseFloat(cleanPrice) || 0;
}

/**
 * Generate slug from product name
 * @param {string} name - The product name
 * @returns {string} - The generated slug
 */
function generateSlug(name) {
  return slugify(name, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
}

// Main transformation function
async function generateImportCompatibleCSV() {
  console.log('Starting CSV transformation...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Read input CSV
  const inputPath = path.join(CONFIG.inputDir, CONFIG.inputFileName);
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    return;
  }
  
  const inputCsv = fs.readFileSync(inputPath, 'utf8');
  
  // Parse CSV
  const { data } = Papa.parse(inputCsv, {
    header: true,
    skipEmptyLines: true,
  });
  
  console.log(`Found ${data.length} rows in input CSV`);
  
  // Group products by name (base products)
  const productGroups = new Map();
  
  data.forEach(row => {
    const name = row.name;
    if (!productGroups.has(name)) {
      productGroups.set(name, []);
    }
    productGroups.get(name).push(row);
  });
  
  console.log(`Grouped into ${productGroups.size} unique products`);
  
  // Transform products and create variants
  const importReadyProducts = [];
  const importReadyVariants = [];
  
  productGroups.forEach((rows, productName) => {
    // Use the first row as the base product
    const baseRow = rows[0];
    const productId = uuidv4();
    const now = new Date().toISOString();
    
    // Extract option definitions
    const optionDefinitions = {};
    
    if (baseRow.productOptionName1 && baseRow.productOptionDescription1) {
      const optionName = baseRow.productOptionName1;
      const optionValues = extractOptionValues(baseRow.productOptionDescription1);
      
      optionDefinitions[optionName] = {
        values: optionValues,
        display_type: getDisplayTypeForOption(optionName)
      };
    }
    
    if (baseRow.productOptionName2 && baseRow.productOptionDescription2) {
      const optionName = baseRow.productOptionName2;
      const optionValues = extractOptionValues(baseRow.productOptionDescription2);
      
      optionDefinitions[optionName] = {
        values: optionValues,
        display_type: getDisplayTypeForOption(optionName)
      };
    }
    
    if (baseRow.productOptionName3 && baseRow.productOptionDescription3) {
      const optionName = baseRow.productOptionName3;
      const optionValues = extractOptionValues(baseRow.productOptionDescription3);
      
      optionDefinitions[optionName] = {
        values: optionValues,
        display_type: getDisplayTypeForOption(optionName)
      };
    }
    
    // Transform image URLs
    let transformedImageUrl = null;
    let additionalImageUrls = [];
    
    if (baseRow.productImageUrl) {
      const imageUrls = baseRow.productImageUrl.split(';');
      
      if (imageUrls.length > 0 && imageUrls[0]) {
        transformedImageUrl = transformImageUrl(imageUrls[0].trim());
      }
      
      if (imageUrls.length > 1) {
        additionalImageUrls = imageUrls.slice(1)
          .map(url => url.trim())
          .filter(url => url)
          .map(url => transformImageUrl(url));
      }
    }
    
    // Create import-ready product with the exact column names expected by the import process
    const importReadyProduct = {
      name: productName,
      slug: generateSlug(productName),
      description: baseRow.description || '',
      price: parsePrice(baseRow.price),
      sale_price: baseRow.discountValue ? parsePrice(baseRow.price) - parsePrice(baseRow.discountValue) : null,
      image: transformedImageUrl || null,
      additional_images: JSON.stringify(additionalImageUrls),
      category_id: null, // Will need to be set manually during import
      brand_id: null, // Will need to be set manually during import
      sku: baseRow.sku || null,
      stock_quantity: parseInt(baseRow.inventory || '0'),
      weight: baseRow.weight || null,
      in_stock: parseInt(baseRow.inventory || '0') > 0,
      is_featured: false,
      is_new: false,
      is_active: transformedImageUrl ? true : false,
      option_definitions: JSON.stringify(optionDefinitions),
    };
    
    importReadyProducts.push(importReadyProduct);
    
    // Create variants
    const optionNames = Object.keys(optionDefinitions);
    
    if (optionNames.length > 0) {
      // For simple cases with just one option
      if (optionNames.length === 1) {
        const optionName = optionNames[0];
        const optionDef = optionDefinitions[optionName];
        
        optionDef.values.forEach(value => {
          const variantId = uuidv4();
          const optionCombination = { [optionName]: value };
          
          const variant = {
            product_id: productId, // This will be replaced during import
            variant_name: `${productName} - ${value}`,
            sku: baseRow.sku ? `${baseRow.sku}-${value.substring(0, 2)}` : null,
            price: parsePrice(baseRow.price),
            sale_price: baseRow.discountValue ? parsePrice(baseRow.price) - parsePrice(baseRow.discountValue) : null,
            stock_quantity: parseInt(baseRow.inventory || '0'),
            in_stock: parseInt(baseRow.inventory || '0') > 0,
            image: transformedImageUrl || null,
            option_combination: JSON.stringify(optionCombination),
            is_active: transformedImageUrl ? true : false,
          };
          
          importReadyVariants.push(variant);
        });
      }
      // For products with multiple options, we'd need to create combinations
      else if (optionNames.length > 1) {
        // Create a default variant
        const variantId = uuidv4();
        const optionCombination = optionNames.reduce((acc, name) => {
          acc[name] = optionDefinitions[name].values[0] || '';
          return acc;
        }, {});
        
        const variant = {
          product_id: productId, // This will be replaced during import
          variant_name: `${productName} - Default`,
          sku: baseRow.sku || null,
          price: parsePrice(baseRow.price),
          sale_price: baseRow.discountValue ? parsePrice(baseRow.price) - parsePrice(baseRow.discountValue) : null,
          stock_quantity: parseInt(baseRow.inventory || '0'),
          in_stock: parseInt(baseRow.inventory || '0') > 0,
          image: transformedImageUrl || null,
          option_combination: JSON.stringify(optionCombination),
          is_active: transformedImageUrl ? true : false,
        };
        
        importReadyVariants.push(variant);
      }
    } else {
      // If no option definitions, create a default variant
      const variantId = uuidv4();
      
      const variant = {
        product_id: productId, // This will be replaced during import
        variant_name: productName,
        sku: baseRow.sku || null,
        price: parsePrice(baseRow.price),
        sale_price: baseRow.discountValue ? parsePrice(baseRow.price) - parsePrice(baseRow.discountValue) : null,
        stock_quantity: parseInt(baseRow.inventory || '0'),
        in_stock: parseInt(baseRow.inventory || '0') > 0,
        image: transformedImageUrl || null,
        option_combination: '{}',
        is_active: transformedImageUrl ? true : false,
      };
      
      importReadyVariants.push(variant);
    }
  });
  
  // Write output CSVs
  const productsOutputPath = path.join(CONFIG.outputDir, CONFIG.productsOutputFileName);
  const variantsOutputPath = path.join(CONFIG.outputDir, CONFIG.variantsOutputFileName);
  
  const productsOutput = Papa.unparse(importReadyProducts);
  const variantsOutput = Papa.unparse(importReadyVariants);
  
  fs.writeFileSync(productsOutputPath, productsOutput);
  fs.writeFileSync(variantsOutputPath, variantsOutput);
  
  console.log('Transformation complete!');
  console.log(`- Products CSV: ${productsOutputPath}`);
  console.log(`- Variants CSV: ${variantsOutputPath}`);
  console.log(`- ${importReadyProducts.length} products and ${importReadyVariants.length} variants created`);
  console.log(`- ${importReadyProducts.filter(p => !p.is_active).length} products flagged as inactive (no images)`);
}

// Run the transformation
generateImportCompatibleCSV().catch(err => {
  console.error('Error during transformation:', err);
  process.exit(1);
});
