/**
 * Blog Categories Management Page
 */

import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import CategoryManager from '@/components/admin/CategoryManager';
import '@/utils/seedBlogCategories'; // Auto-seed categories in development

const BlogCategoriesPage = () => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/admin/blogs')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Blogs
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Blog Categories</h1>
          <p className="text-gray-600 mt-1">
            Manage your blog categories. Create, edit, and organize categories for better content organization.
          </p>
        </div>
      </div>

      <CategoryManager />
    </div>
  );
};

export default BlogCategoriesPage;
