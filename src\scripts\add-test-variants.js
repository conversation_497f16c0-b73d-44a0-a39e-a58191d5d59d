// Import the Supabase client
import { createClient } from '@supabase/supabase-js';

// Create a custom Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://bnbpnbxvxzjcwfkfssrg.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJuYnBuYnh2eHpqY3dma2Zzc3JnIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk5NzI1NzYsImV4cCI6MjAxNTU0ODU3Nn0.Uh9cF5JdYKrDCUFzNsXlpRFjh7_MwQUVVOKcbJ11eXw';

const customSupabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'public',
  },
});

// Configuration - Update these values
const productId = 'product-1'; // Using a test product ID
const productName = 'Test Product with Variants'; // For reference only

// Example option definitions with special colors
const optionDefinitions = {
  'Size': ['Small', 'Medium', 'Large', 'XL'],
  'Color': ['Red', 'Blue', 'Gold', 'Rasta', 'Metallic', 'Holographic'],
  'Pack Size': ['3 Pack', '5 Pack', '10 Pack']
};

// Example variants to create with special colors
const variants = [
  // Size: Small with various colors
  {
    variant_name: `${productName} - Small/Red/3 Pack`,
    option_combination: { 'Size': 'Small', 'Color': 'Red', 'Pack Size': '3 Pack' },
    price: 9.99,
    stock_quantity: 10
  },
  {
    variant_name: `${productName} - Small/Gold/5 Pack`,
    option_combination: { 'Size': 'Small', 'Color': 'Gold', 'Pack Size': '5 Pack' },
    price: 16.99,
    stock_quantity: 8
  },
  {
    variant_name: `${productName} - Small/Rasta/10 Pack`,
    option_combination: { 'Size': 'Small', 'Color': 'Rasta', 'Pack Size': '10 Pack' },
    price: 24.99,
    stock_quantity: 5
  },
  
  // Size: Medium with various colors
  {
    variant_name: `${productName} - Medium/Blue/3 Pack`,
    option_combination: { 'Size': 'Medium', 'Color': 'Blue', 'Pack Size': '3 Pack' },
    price: 12.99,
    stock_quantity: 15
  },
  {
    variant_name: `${productName} - Medium/Metallic/5 Pack`,
    option_combination: { 'Size': 'Medium', 'Color': 'Metallic', 'Pack Size': '5 Pack' },
    price: 19.99,
    stock_quantity: 7
  },
  {
    variant_name: `${productName} - Medium/Holographic/10 Pack`,
    option_combination: { 'Size': 'Medium', 'Color': 'Holographic', 'Pack Size': '10 Pack' },
    price: 29.99,
    stock_quantity: 3
  },
  
  // Size: Large with various colors
  {
    variant_name: `${productName} - Large/Red/5 Pack`,
    option_combination: { 'Size': 'Large', 'Color': 'Red', 'Pack Size': '5 Pack' },
    price: 19.99,
    stock_quantity: 12
  },
  {
    variant_name: `${productName} - Large/Gold/10 Pack`,
    option_combination: { 'Size': 'Large', 'Color': 'Gold', 'Pack Size': '10 Pack' },
    price: 34.99,
    stock_quantity: 6
  },
  
  // Size: XL with various colors
  {
    variant_name: `${productName} - XL/Rasta/3 Pack`,
    option_combination: { 'Size': 'XL', 'Color': 'Rasta', 'Pack Size': '3 Pack' },
    price: 14.99,
    stock_quantity: 9
  },
  {
    variant_name: `${productName} - XL/Holographic/10 Pack`,
    option_combination: { 'Size': 'XL', 'Color': 'Holographic', 'Pack Size': '10 Pack' },
    price: 39.99,
    stock_quantity: 4
  }
];

/**
 * Adds option definitions to a product
 */
async function addOptionDefinitions() {
  try {
    const { data, error } = await customSupabase
      .from('products')
      .update({ option_definitions: optionDefinitions })
      .eq('id', productId)
      .select();
      
    if (error) {
      throw error;
    }
    
    console.log('✅ Added option definitions to product:', data);
    return true;
  } catch (error) {
    console.error('❌ Error adding option definitions:', error);
    return false;
  }
}

/**
 * Adds variants to a product
 */
async function addVariants() {
  try {
    // Prepare variants data
    const variantsToCreate = variants.map(variant => ({
      product_id: productId,
      variant_name: variant.variant_name,
      sku: `${productId.substring(0, 6)}-${variant.option_combination.Size?.substring(0, 1)}${variant.option_combination.Color?.substring(0, 1)}${variant.option_combination['Pack Size']?.replace(/\D/g, '')}`,
      price: variant.price,
      sale_price: null,
      stock_quantity: variant.stock_quantity,
      in_stock: true,
      image: null,
      option_combination: variant.option_combination,
      is_active: true,
      external_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));
    
    const { data, error } = await customSupabase
      .from('product_variants')
      .insert(variantsToCreate)
      .select();
      
    if (error) {
      throw error;
    }
    
    console.log(`✅ Added ${variantsToCreate.length} variants to product:`, data);
    return true;
  } catch (error) {
    console.error('❌ Error adding variants:', error);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Adding test variants to product:', productId);
  
  // Step 1: Add option definitions
  const optionsAdded = await addOptionDefinitions();
  if (!optionsAdded) {
    console.error('❌ Failed to add option definitions. Aborting.');
    return;
  }
  
  // Step 2: Add variants
  const variantsAdded = await addVariants();
  if (!variantsAdded) {
    console.error('❌ Failed to add variants.');
    return;
  }
  
  console.log('✅ Successfully added test variants to product!');
}

// Run the script
main().catch(console.error);
