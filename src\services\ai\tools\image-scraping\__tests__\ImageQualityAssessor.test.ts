/**
 * ImageQualityAssessor.test.ts
 * 
 * Tests for the ImageQualityAssessor component
 */

import { ImageQualityAssessor } from '../ImageQualityAssessor';
import { ProductImage } from '../types/ImageScrapingTypes';

describe('ImageQualityAssessor', () => {
  let assessor: ImageQualityAssessor;

  beforeEach(() => {
    // Create assessor with default configuration
    assessor = new ImageQualityAssessor();
  });

  describe('assessImageQuality', () => {
    it('should assess image quality correctly', async () => {
      // Create test image
      const image: ProductImage = {
        url: 'https://example.com/product-image.jpg',
        alt: 'Test CBD Oil 500mg',
        quality_score: 0,
        source: 'Test Source',
        width: 800,
        height: 800,
        format: 'jpg',
        file_size: 102400 // 100KB
      };

      // Assess quality
      const productName = 'Test CBD Oil 500mg';
      const sourceReliability = 8;
      const result = await assessor.assessImageQuality(image, productName, sourceReliability);

      // Check result
      expect(result).toBeDefined();
      expect(result.overall_score).toBeGreaterThan(0);
      expect(result.relevance_score).toBeGreaterThan(0);
      expect(result.technical_score).toBeGreaterThan(0);
      expect(result.dimensions_score).toBeGreaterThan(0);
      expect(result.source_reliability_score).toBeGreaterThan(0);
      
      // Check factors
      expect(result.factors.has_product_in_alt).toBe(true);
      expect(result.factors.good_dimensions).toBe(true);
      
      // Check that image quality_score was updated
      expect(image.quality_score).toBeGreaterThan(0);
    });

    it('should handle images with no dimensions', async () => {
      // Create test image without dimensions
      const image: ProductImage = {
        url: 'https://example.com/product-image.jpg',
        alt: 'Test CBD Oil',
        quality_score: 0,
        source: 'Test Source'
      };

      // Assess quality
      const productName = 'Test CBD Oil';
      const result = await assessor.assessImageQuality(image, productName);

      // Check result
      expect(result).toBeDefined();
      expect(result.overall_score).toBeGreaterThan(0);
      expect(result.dimensions_score).toBeDefined();
    });

    it('should give higher scores to more relevant images', async () => {
      // Create relevant test image
      const relevantImage: ProductImage = {
        url: 'https://example.com/test-vape-pen-product.jpg',
        alt: 'Test Vape Pen',
        quality_score: 0,
        source: 'Test Source',
        width: 600,
        height: 600
      };

      // Create less relevant test image
      const lessRelevantImage: ProductImage = {
        url: 'https://example.com/generic-image.jpg',
        alt: 'Vaping Products',
        quality_score: 0,
        source: 'Test Source',
        width: 600,
        height: 600
      };

      // Assess quality
      const productName = 'Test Vape Pen';
      const relevantResult = await assessor.assessImageQuality(relevantImage, productName);
      const lessRelevantResult = await assessor.assessImageQuality(lessRelevantImage, productName);

      // Check that relevant image has higher relevance score
      expect(relevantResult.relevance_score).toBeGreaterThan(lessRelevantResult.relevance_score);
    });
  });

  describe('filterImagesByQuality', () => {
    it('should filter images by quality score', () => {
      // Create test images
      const images: ProductImage[] = [
        {
          url: 'https://example.com/high-quality.jpg',
          alt: 'High Quality',
          quality_score: 80,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/medium-quality.jpg',
          alt: 'Medium Quality',
          quality_score: 60,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/low-quality.jpg',
          alt: 'Low Quality',
          quality_score: 30,
          source: 'Test Source'
        }
      ];

      // Filter images
      const filteredImages = assessor.filterImagesByQuality(images, 50);

      // Check result
      expect(filteredImages.length).toBe(2);
      expect(filteredImages[0].quality_score).toBe(80);
      expect(filteredImages[1].quality_score).toBe(60);
    });
  });

  describe('sortImagesByQuality', () => {
    it('should sort images by quality score', () => {
      // Create test images in random order
      const images: ProductImage[] = [
        {
          url: 'https://example.com/medium-quality.jpg',
          alt: 'Medium Quality',
          quality_score: 60,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/high-quality.jpg',
          alt: 'High Quality',
          quality_score: 80,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/low-quality.jpg',
          alt: 'Low Quality',
          quality_score: 30,
          source: 'Test Source'
        }
      ];

      // Sort images
      const sortedImages = assessor.sortImagesByQuality(images);

      // Check result
      expect(sortedImages.length).toBe(3);
      expect(sortedImages[0].quality_score).toBe(80);
      expect(sortedImages[1].quality_score).toBe(60);
      expect(sortedImages[2].quality_score).toBe(30);
    });
  });

  describe('getTopQualityImages', () => {
    it('should get top quality images', () => {
      // Create test images
      const images: ProductImage[] = [
        {
          url: 'https://example.com/quality1.jpg',
          alt: 'Quality 1',
          quality_score: 90,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality2.jpg',
          alt: 'Quality 2',
          quality_score: 85,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality3.jpg',
          alt: 'Quality 3',
          quality_score: 70,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality4.jpg',
          alt: 'Quality 4',
          quality_score: 60,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality5.jpg',
          alt: 'Quality 5',
          quality_score: 40,
          source: 'Test Source'
        }
      ];

      // Get top 3 images with minimum quality 50
      const topImages = assessor.getTopQualityImages(images, 3, 50);

      // Check result
      expect(topImages.length).toBe(3);
      expect(topImages[0].quality_score).toBe(90);
      expect(topImages[1].quality_score).toBe(85);
      expect(topImages[2].quality_score).toBe(70);
    });

    it('should respect minimum quality score', () => {
      // Create test images
      const images: ProductImage[] = [
        {
          url: 'https://example.com/quality1.jpg',
          alt: 'Quality 1',
          quality_score: 90,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality2.jpg',
          alt: 'Quality 2',
          quality_score: 85,
          source: 'Test Source'
        },
        {
          url: 'https://example.com/quality3.jpg',
          alt: 'Quality 3',
          quality_score: 40,
          source: 'Test Source'
        }
      ];

      // Get top 3 images with minimum quality 80
      const topImages = assessor.getTopQualityImages(images, 3, 80);

      // Check result
      expect(topImages.length).toBe(2);
      expect(topImages[0].quality_score).toBe(90);
      expect(topImages[1].quality_score).toBe(85);
    });
  });
});
