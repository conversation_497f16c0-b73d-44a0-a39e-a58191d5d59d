import { supabase } from '../src/integrations/supabase/client';
import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define types
interface CSVProduct {
  handleId: string;
  fieldType: string;
  name: string;
  price: string;
  // Other fields
  productOptionName1?: string;
  productOptionType1?: string;
  productOptionValues1?: string;
}

interface ProductVariant {
  parentId: string;
  optionValue: string;
  price: number;
}

interface DBProduct {
  id: string;
  external_id: string;
  name: string;
  price: number;
  option_name1?: string;
  option_type1?: string;
  option_values1?: string;
  option_price_adjustment1?: string;
}

// Configuration
const CSV_FILE_PATH = path.resolve(__dirname, '../docs/catalog_products.csv');
const DRY_RUN = process.argv.includes('--dry-run');

// Main function
async function updateProductOptionPricing() {
  console.log('Starting product option pricing update...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);

  try {
    // 1. Read and parse the CSV file
    console.log('Reading CSV file...');
    const csvData = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    
    // Parse CSV
    const parseResult = Papa.parse(csvData, {
      header: true,
      skipEmptyLines: true,
    });
    
    if (parseResult.errors.length > 0) {
      console.error('CSV parsing errors:', parseResult.errors);
      return;
    }
    
    const csvProducts = parseResult.data as any[];
    console.log(`Found ${csvProducts.length} rows in CSV file`);

    // 2. Process the CSV data to extract product variants
    const productMap = new Map<string, any>(); // Map to store main products
    const variantMap = new Map<string, ProductVariant[]>(); // Map to store variants by parent product ID

    // First pass: identify main products and variants
    csvProducts.forEach((row: any) => {
      // Map CSV column names to our expected structure
      const product: CSVProduct = {
        handleId: row.Product_ID || '',
        fieldType: row.Type || '',
        name: row.Title || '',
        price: row.Price || '0',
        productOptionName1: row['Option Name'] || '',
        productOptionType1: row['Option Type'] || '',
        productOptionValues1: row['Option Values'] || '',
      };

      // Skip rows that aren't products or variants
      if (!product.handleId) return;

      if (product.fieldType === 'Product') {
        // This is a main product
        productMap.set(product.handleId, product);
        
        // Initialize variant array for this product
        if (!variantMap.has(product.handleId)) {
          variantMap.set(product.handleId, []);
        }
      } else if (product.fieldType === 'Variant') {
        // This is a variant
        // Extract parent ID from the variant ID (assuming format like "parentId_variant")
        const parentId = product.handleId.split('_')[0];
        
        if (parentId) {
          // Get the option value for this variant
          const optionValue = row['Option Value'] || '';
          
          if (optionValue) {
            // Add to variants map
            const variants = variantMap.get(parentId) || [];
            variants.push({
              parentId,
              optionValue,
              price: parseFloat(product.price) || 0,
            });
            variantMap.set(parentId, variants);
          }
        }
      }
    });

    console.log(`Identified ${productMap.size} main products and variants for ${variantMap.size} products`);

    // 3. Fetch products from the database
    console.log('Fetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, external_id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('external_id', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts.length} products in database`);

    // 4. Process each product with options
    const productsToUpdate: { id: string; option_price_adjustment1: string }[] = [];
    const productsWithIssues: { id: string; name: string; issue: string }[] = [];

    for (const dbProduct of dbProducts) {
      // Skip products without options
      if (!dbProduct.option_values1 || !dbProduct.option_name1) continue;

      // Get the external ID (from CSV)
      const externalId = dbProduct.external_id;
      if (!externalId) continue;

      // Find variants for this product
      const variants = variantMap.get(externalId) || [];
      if (variants.length === 0) continue;

      // Get option values as an array
      const optionValues = dbProduct.option_values1.split(';').map(v => v.trim());
      if (optionValues.length === 0) continue;

      // Get the base price
      const basePrice = dbProduct.price || 0;
      if (basePrice <= 0) {
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Invalid base price: ${basePrice}`,
        });
        continue;
      }

      try {
        // Calculate price adjustments for each option value
        const priceAdjustments: number[] = [];
        let allValuesFound = true;

        for (const optionValue of optionValues) {
          // Find the variant with this option value
          const variant = variants.find(v => v.optionValue === optionValue);
          
          if (variant) {
            // Calculate the price adjustment (variant price - base price)
            const adjustment = variant.price - basePrice;
            priceAdjustments.push(adjustment);
          } else {
            // If we can't find a variant for this option value, use 0 as the adjustment
            console.warn(`No variant found for product ${dbProduct.name} with option value ${optionValue}`);
            priceAdjustments.push(0);
            allValuesFound = false;
          }
        }

        // Format price adjustments as semicolon-separated string
        const priceAdjustmentStr = priceAdjustments.join(';');

        // Add to update list
        productsToUpdate.push({
          id: dbProduct.id,
          option_price_adjustment1: priceAdjustmentStr,
        });

        console.log(`Product: ${dbProduct.name}`);
        console.log(`  Option values: ${optionValues.join(', ')}`);
        console.log(`  Price adjustments: ${priceAdjustmentStr}`);
        
        if (!allValuesFound) {
          productsWithIssues.push({
            id: dbProduct.id,
            name: dbProduct.name,
            issue: 'Some option values could not be matched to variants',
          });
        }
      } catch (err) {
        console.error(`Error processing product ${dbProduct.name}:`, err);
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Error: ${err.message}`,
        });
      }
    }

    // 5. Update products in the database (if not dry run)
    if (productsToUpdate.length > 0) {
      console.log(`\nReady to update ${productsToUpdate.length} products with price adjustments`);
      
      if (!DRY_RUN) {
        console.log('Updating products in database...');
        
        // Update products in batches to avoid hitting API limits
        const BATCH_SIZE = 50;
        for (let i = 0; i < productsToUpdate.length; i += BATCH_SIZE) {
          const batch = productsToUpdate.slice(i, i + BATCH_SIZE);
          
          // Update each product individually to avoid issues
          for (const product of batch) {
            const { error } = await supabase
              .from('products')
              .update({ option_price_adjustment1: product.option_price_adjustment1 })
              .eq('id', product.id);
              
            if (error) {
              console.error(`Error updating product ${product.id}:`, error);
              productsWithIssues.push({
                id: product.id,
                name: 'Unknown',
                issue: `Update error: ${error.message}`,
              });
            }
          }
          
          console.log(`Updated batch ${i / BATCH_SIZE + 1} of ${Math.ceil(productsToUpdate.length / BATCH_SIZE)}`);
        }
        
        console.log('Update completed successfully!');
      } else {
        console.log('DRY RUN: No changes were made to the database');
      }
    } else {
      console.log('No products to update');
    }

    // 6. Report issues
    if (productsWithIssues.length > 0) {
      console.log(`\n${productsWithIssues.length} products had issues:`);
      productsWithIssues.forEach(p => {
        console.log(`- ${p.name} (${p.id}): ${p.issue}`);
      });
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
updateProductOptionPricing().catch(console.error);
