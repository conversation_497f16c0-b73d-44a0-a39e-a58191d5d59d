/**
 * API endpoint for searching images related to blog topics
 */

// API keys for image services
const PIXABAY_API_KEY = import.meta.env.VITE_PIXABAY_API_KEY || '';
const FREEPIK_API_KEY = import.meta.env.VITE_FREEPIK_API_KEY || '';

// Mapping of topics to search terms for better image results
const topicSearchTerms: Record<string, string[]> = {
  'cbd oil': ['cbd oil', 'cannabis oil', 'hemp oil', 'dropper', 'wellness'],
  'anxiety': ['relaxation', 'meditation', 'calm', 'peaceful', 'stress relief'],
  'sleep': ['sleep', 'bedroom', 'night', 'rest', 'peaceful night'],
  'pain': ['massage', 'relief', 'therapy', 'wellness', 'relaxation'],
  'legalization': ['cannabis plant', 'hemp farm', 'cannabis legislation', 'medical cannabis'],
  'policy': ['cannabis research', 'medical cannabis', 'cannabis legislation', 'cannabis reform'],
  'general': ['cannabis', 'hemp', 'plant', 'natural', 'organic', 'wellness']
};

// Expanded collection of fallback images from reliable free sources
const fallbackImages: Record<string, string[]> = {
  'cbd oil': [
    'https://cdn.pixabay.com/photo/2019/09/12/15/21/cbd-4471876_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/08/08/01/39/cbd-4391511_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/06/08/17/32/massage-oil-2384729_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/12/15/47/oil-4621979_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/07/23/50/essential-oils-2609127_1280.jpg'
  ],
  'anxiety': [
    'https://cdn.pixabay.com/photo/2017/08/06/00/27/yoga-2587066_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/03/26/21/54/yoga-2176668_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/07/02/10/22/training-828726_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/18/14/05/brick-wall-1834784_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/04/08/22/26/buddhism-2214532_1280.jpg'
  ],
  'sleep': [
    'https://cdn.pixabay.com/photo/2016/11/18/17/20/living-room-1835923_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/18/17/46/bed-1835959_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/01/14/12/48/hotel-1979406_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/22/19/11/bed-1850024_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/29/04/49/wellness-1867410_1280.jpg'
  ],
  'pain': [
    'https://cdn.pixabay.com/photo/2017/03/14/11/21/massage-2142748_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/01/30/02/20/massage-2019877_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/01/05/22/29/massage-589770_1280.jpg',
    'https://cdn.pixabay.com/photo/2018/01/28/21/14/yoga-3114927_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/06/22/22/massage-2596619_1280.jpg'
  ],
  'legalization': [
    'https://cdn.pixabay.com/photo/2019/11/16/20/03/cannabis-4631153_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/04/14/10/27/cannabis-4126975_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/16/20/03/cannabis-4631142_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/16/20/03/cannabis-4631137_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/02/20/01/cannabis-4597301_1280.jpg'
  ],
  'policy': [
    'https://cdn.pixabay.com/photo/2018/07/12/21/32/book-3534606_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/01/19/01/42/library-1147815_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/06/22/01/books-2596809_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/02/01/13/53/analysis-2030265_1280.jpg',
    'https://cdn.pixabay.com/photo/2014/07/06/13/55/calculator-385506_1280.jpg'
  ],
  'general': [
    'https://cdn.pixabay.com/photo/2015/07/19/10/00/still-life-851328_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/03/27/07/12/apple-1282241_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/29/11/39/alternative-1869224_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/05/04/10/16/vegetables-752153_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/18/17/20/flower-1835619_1280.jpg'
  ]
};

// Keep track of recently used images to avoid repetition
let recentlyUsedImages: string[] = [];

// Track the last query and its results to ensure variety
let lastQuery = '';
let lastQueryResults: string[] = [];
const MAX_RECENT_IMAGES = 10; // How many recent images to remember

/**
 * Get a category for a topic based on content analysis
 * @param query The search query
 * @returns The most relevant category
 */
function getTopicCategory(query: string): string {
  const queryLower = query.toLowerCase();
  
  // Check for specific topics in the query
  if (queryLower.includes('oil') || queryLower.includes('tincture') || queryLower.includes('dropper')) {
    return 'cbd oil';
  } else if (queryLower.includes('anxiety') || queryLower.includes('stress') || queryLower.includes('relax')) {
    return 'anxiety';
  } else if (queryLower.includes('sleep') || queryLower.includes('insomnia') || queryLower.includes('rest')) {
    return 'sleep';
  } else if (queryLower.includes('pain') || queryLower.includes('relief') || queryLower.includes('ache')) {
    return 'pain';
  } else if (queryLower.includes('legal') || queryLower.includes('law') || queryLower.includes('legislation')) {
    return 'legalization';
  } else if (queryLower.includes('policy') || queryLower.includes('regulation') || queryLower.includes('government')) {
    return 'policy';
  }
  
  // Default to general category
  return 'general';
}

/**
 * Get fallback images for a topic
 * @param topic The topic to get fallback images for
 * @returns Array of image URLs
 */
export function getFallbackImages(topic: string): string[] {
  // Determine the most relevant category
  const category = getTopicCategory(topic);
  
  // Get images for this category
  const images = fallbackImages[category] || fallbackImages.general;
  
  // Shuffle the array to get random images each time
  return [...images].sort(() => 0.5 - Math.random());
}

/**
 * Search for images using the Freepik API (smart categorization)
 * @param query Search query
 * @returns Array of image URLs
 */
async function searchFreepik(query: string): Promise<string[]> {
  try {
    // Since we're encountering CORS issues with direct API access,
    // let's use a different approach and rely on our fallback images for now
    console.log(`Freepik API can't be accessed directly from the browser due to CORS restrictions`);
    console.log(`Using curated images for '${query}' instead`);
    
    // Determine the most relevant category based on the query
    const category = getTopicCategory(query);
    
    // Get images from our curated collection for this category
    const images = fallbackImages[category] || fallbackImages.general;
    
    // Shuffle the array to get random images each time
    const shuffled = [...images].sort(() => 0.5 - Math.random());
    
    // Take the first 3 images
    const selectedImages = shuffled.slice(0, 3);
    
    console.log(`Selected ${selectedImages.length} curated images for category: ${category}`);
    if (selectedImages.length > 0) {
      console.log('Sample image URL:', selectedImages[0]);
    }
    
    return selectedImages;
  } catch (error) {
    console.error('Error with Freepik alternative approach:', error);
    return [];
  }
}

/**
 * Search for images on Pixabay
 * @param query Search query
 * @returns Promise with array of image URLs
 */
async function searchPixabay(query: string): Promise<string[]> {
  try {
    // Clean up the query for API use
    const searchQuery = encodeURIComponent(query.trim());
    
    // Construct the API URL
    const apiUrl = `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=${searchQuery}&image_type=photo&per_page=3&safesearch=true`;
    
    console.log(`Searching Pixabay with query: ${searchQuery}`);
    
    // Make the API request
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      console.error(`Pixabay API error: ${response.status} ${response.statusText}`);
      return [];
    }
    
    const data = await response.json();
    console.log('Pixabay response:', data.totalHits, 'hits found');
    
    // If no hits, try a more generic search
    if (!data.hits || data.hits.length === 0) {
      console.log('No results found, trying generic search');
      const genericResponse = await fetch(
        `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=wellness&image_type=photo&per_page=3&safesearch=true&category=health`
      );
      
      if (!genericResponse.ok) {
        return [];
      }
      
      const genericData = await genericResponse.json();
      if (!genericData.hits || genericData.hits.length === 0) {
        return [];
      }
      
      // Extract image URLs from generic search - use CDN URLs that work in img tags
      return genericData.hits.map((hit: any) => {
        // Always use previewURL which has the CDN format, or construct a CDN URL from the ID and type
        if (hit.previewURL) {
          // Convert the preview URL to a larger format by modifying the size suffix
          // Example: from flower-195893_150.jpg to flower-195893_1280.jpg for better quality
          return hit.previewURL.replace('_150.jpg', '_1280.jpg');
        } else if (hit.id && hit.pageURL) {
          // Try to construct a URL from the page URL pattern
          // Extract the year/month/day/id from pageURL if possible
          const matches = hit.pageURL.match(/pixabay\.com\/[\w-]+\/(\d{4})\/(\d{2})\/(\d{2})\/(\d+)/);
          if (matches && matches.length >= 5) {
            const [_, year, month, day, id] = matches;
            return `https://cdn.pixabay.com/photo/${year}/${month}/${day}/${id}_1280.jpg`;
          }
        }
        
        // Use a random fallback from our collection to avoid always using the same image
        const fallbackCategory = Object.keys(fallbackImages)[Math.floor(Math.random() * Object.keys(fallbackImages).length)];
        const fallbackCollection = fallbackImages[fallbackCategory];
        return fallbackCollection[Math.floor(Math.random() * fallbackCollection.length)];
      });
    }
    
    // Extract image URLs - use CDN URLs that work in img tags
    const imageUrls = data.hits.map((hit: any) => {
      // Always use previewURL which has the CDN format, or construct a CDN URL from the ID and type
      if (hit.previewURL) {
        // Convert the preview URL to a larger format by modifying the size suffix
        // Example: from flower-195893_150.jpg to flower-195893_1280.jpg for better quality
        return hit.previewURL.replace('_150.jpg', '_1280.jpg');
      } else if (hit.id && hit.pageURL) {
        // Try to construct a URL from the page URL pattern
        // Extract the year/month/day/id from pageURL if possible
        const matches = hit.pageURL.match(/pixabay\.com\/[\w-]+\/(\d{4})\/(\d{2})\/(\d{2})\/(\d+)/);
        if (matches && matches.length >= 5) {
          const [_, year, month, day, id] = matches;
          return `https://cdn.pixabay.com/photo/${year}/${month}/${day}/${id}_1280.jpg`;
        }
      }
      
      // Use a random fallback from our collection to avoid always using the same image
      const fallbackCategory = Object.keys(fallbackImages)[Math.floor(Math.random() * Object.keys(fallbackImages).length)];
      const fallbackCollection = fallbackImages[fallbackCategory];
      return fallbackCollection[Math.floor(Math.random() * fallbackCollection.length)];
    });
    
    // Log the actual URL format we're returning
    console.log('Image URL format sample:', imageUrls[0]);
    
    return imageUrls.length > 0 ? imageUrls : [];
  } catch (error) {
    console.error('Error searching Pixabay:', error);
    return [];
  }
}

/**
 * Verify if an image URL is valid and accessible
 * @param url Image URL to verify
 * @returns Promise resolving to true if valid, false otherwise
 */
async function isValidImageUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error validating image URL:', error);
    return false;
  }
}

/**
 * Get a random image from the fallback list
 */
export function getRandomImage(): string {
  // Get a random category
  const categories = Object.keys(fallbackImages);
  const randomCategory = categories[Math.floor(Math.random() * categories.length)];
  
  // Get a random image from that category
  const images = fallbackImages[randomCategory];
  return images[Math.floor(Math.random() * images.length)];
}

/**
 * Search for images related to a blog topic
 * @param query Search query
 * @returns Array of image URLs
 */
export async function searchImages(query: string): Promise<string[]> {
  const normalizedQuery = query.toLowerCase();
  
  try {
    // Check if this is a new query or the same as the last one
    const isNewQuery = normalizedQuery !== lastQuery;
    
    // If it's the same query but we have previous results, return a different one
    // This ensures variety when clicking "New AI Image" multiple times for the same topic
    if (!isNewQuery && lastQueryResults.length > 1) {
      console.log('Using cached results for repeated query, but selecting a different image');
      
      // Filter out recently used images
      const unusedImages = lastQueryResults.filter(img => !recentlyUsedImages.includes(img));
      
      // If we have unused images, return one of those
      if (unusedImages.length > 0) {
        const randomIndex = Math.floor(Math.random() * unusedImages.length);
        const selectedImage = unusedImages[randomIndex];
        
        // Track this image as recently used
        recentlyUsedImages.push(selectedImage);
        if (recentlyUsedImages.length > MAX_RECENT_IMAGES) {
          recentlyUsedImages = recentlyUsedImages.slice(-MAX_RECENT_IMAGES);
        }
        
        return [selectedImage];
      }
    }
    
    // For a new query or if we need fresh results, search for images
    console.log(`Searching for images related to: ${normalizedQuery}`);
    
    // Get images from our smart categorization system (former Freepik function)
    let images = await searchFreepik(normalizedQuery);
    
    // If no images found, try Pixabay as fallback
    if (images.length === 0) {
      console.log('No results from primary source, trying Pixabay');
      images = await searchPixabay(normalizedQuery);
    }
    
    // If still no images, use general fallback images
    if (images.length === 0) {
      console.log('No images found from any service, using general fallback images');
      images = getFallbackImages('general');
    }
    
    // Update our tracking variables
    lastQuery = normalizedQuery;
    lastQueryResults = [...images]; // Make a copy to avoid reference issues
    
    // Filter out recently used images to avoid repetition
    const filteredImages = images.filter(img => !recentlyUsedImages.includes(img));
    
    // If all images have been recently used, reset the tracking
    if (filteredImages.length === 0 && images.length > 0) {
      console.log('All images have been recently used, resetting tracking');
      recentlyUsedImages = [];
      return [images[Math.floor(Math.random() * images.length)]];
    }
    
    // Get a random image from the filtered list
    const randomIndex = Math.floor(Math.random() * filteredImages.length);
    const selectedImage = filteredImages[randomIndex] || images[0];
    
    // Add the selected image to recently used
    if (selectedImage) {
      recentlyUsedImages.push(selectedImage);
      
      // Trim the recently used list if it gets too long
      if (recentlyUsedImages.length > MAX_RECENT_IMAGES) {
        recentlyUsedImages = recentlyUsedImages.slice(-MAX_RECENT_IMAGES);
      }
      
      return [selectedImage];
    }
    
    return images.slice(0, 1); // Return just one image
  } catch (error) {
    console.error('Error searching for images:', error);
    
    // In case of error, use fallback images
    const fallbackImages = getFallbackImages('general');
    return [fallbackImages[Math.floor(Math.random() * fallbackImages.length)]];
  }
}
