import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Package,
  Tags,
  ShoppingBag,
  Users,
  Settings,
  HelpCircle,
  Tag,
  Database
} from 'lucide-react';

interface SidebarLinkProps {
  to: string;
  icon: React.ReactNode;
  label: string;
}

function SidebarLink({ to, icon, label }: SidebarLinkProps) {
  return (
    <NavLink
      to={to}
      className={({ isActive }) =>
        cn(
          'flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all',
          isActive
            ? 'bg-accent text-accent-foreground'
            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
        )
      }
    >
      {icon}
      {label}
    </NavLink>
  );
}

export function AdminSidebar() {
  return (
    <div className="flex h-full w-full flex-col gap-2">
      <div className="px-3 py-2">
        <h2 className="mb-2 px-4 text-lg font-semibold">Admin</h2>
        <div className="space-y-1">
          <SidebarLink
            to="/admin/dashboard"
            icon={<LayoutDashboard className="h-4 w-4" />}
            label="Dashboard"
          />
          <SidebarLink
            to="/admin/products"
            icon={<Package className="h-4 w-4" />}
            label="Products"
          />
          <SidebarLink
            to="/admin/categories"
            icon={<Tags className="h-4 w-4" />}
            label="Categories"
          />
          <SidebarLink
            to="/admin/brands"
            icon={<Tag className="h-4 w-4" />}
            label="Brands"
          />
          <SidebarLink
            to="/admin/orders"
            icon={<ShoppingBag className="h-4 w-4" />}
            label="Orders"
          />
          <SidebarLink
            to="/admin/customers"
            icon={<Users className="h-4 w-4" />}
            label="Customers"
          />
          <SidebarLink
            to="/admin/epos-integration"
            icon={<Database className="h-4 w-4" />}
            label="EPOS Integration"
          />
          <SidebarLink
            to="/admin/faqs"
            icon={<HelpCircle className="h-4 w-4" />}
            label="FAQs"
          />
          <SidebarLink
            to="/admin/settings"
            icon={<Settings className="h-4 w-4" />}
            label="Settings"
          />
        </div>
      </div>
    </div>
  );
}