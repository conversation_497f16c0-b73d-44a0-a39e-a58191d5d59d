-- Fix the get_related_products function to resolve ambiguous column reference
CREATE OR REPLACE FUNCTION get_related_products(p_product_id UUID, p_limit INTEGER DEFAULT 4)
RETURNS SETOF products AS $$
DECLARE
    v_category_id UUID;
BEGIN
    -- Get the category_id of the current product
    SELECT category_id INTO v_category_id FROM products WHERE id = p_product_id;

    -- First, try to get user-selected related products
    RETURN QUERY
    SELECT p.*
    FROM products p
    JOIN related_products rp ON p.id = rp.related_product_id
    WHERE rp.product_id = p_product_id
      AND p.is_active = true
      AND p.in_stock = true
      AND p.image IS NOT NULL
    ORDER BY rp.display_order
    LIMIT p_limit;

    -- If no results, try products from the same category
    IF NOT FOUND AND v_category_id IS NOT NULL THEN
        RETURN QUERY
        SELECT p.*
        FROM products p
        WHERE p.id != p_product_id
          AND p.category_id = v_category_id
          AND p.is_active = true
          AND p.in_stock = true
          AND p.image IS NOT NULL
        ORDER BY RANDOM()
        LIMIT p_limit;
    END IF;

    -- If still no results, get random products
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT p.*
        FROM products p
        WHERE p.id != p_product_id
          AND p.is_active = true
          AND p.in_stock = true
          AND p.image IS NOT NULL
        ORDER BY RANDOM()
        LIMIT p_limit;
    END IF;

    -- If still no results, return empty set (handled by the RETURNS SETOF)
END;
$$ LANGUAGE plpgsql;
