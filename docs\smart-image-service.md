# Smart Image Service

## Overview

The Smart Image Service is an AI-powered image selection system that finds the most relevant images for social media campaigns based on product details. It uses multiple image APIs and AI-enhanced search queries to deliver high-quality, contextually relevant images.

## Features

- **AI-Enhanced Search Queries**: Uses Gemini/Deepseek to analyze products and generate optimal search terms
- **Multi-API Fallback System**: Tries multiple image sources in priority order
- **Relevance Scoring**: Ranks images by relevance to the product
- **Automatic Product Image Inclusion**: Always includes the actual product image
- **Robust Fallback Mechanism**: Provides mock images if all APIs fail

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Social Media   │────▶│  Smart Image    │────▶│  AI             │
│  Page           │     │  Service        │     │  Orchestration  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │  │  │
                               │  │  │
                               ▼  ▼  ▼
                        ┌─────────────────┐
                        │  Image APIs     │
                        │  - Google       │
                        │  - Freepik      │
                        │  - Pixabay      │
                        │  - Unsplash     │
                        └─────────────────┘
```

## API Keys Setup

To use all features of the Smart Image Service, you need to set up API keys for the following services:

1. **Google Custom Search API**
   - Visit: https://developers.google.com/custom-search/v1/overview
   - Create a new API key
   - Create a new search engine and get the search engine ID
   - Add to .env.local:
     ```
     NEXT_PUBLIC_GOOGLE_SEARCH_API_KEY=your_google_api_key_here
     NEXT_PUBLIC_GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
     ```

2. **Unsplash API**
   - Visit: https://unsplash.com/developers
   - Create a new application
   - Get your access key
   - Add to .env.local:
     ```
     NEXT_PUBLIC_UNSPLASH_ACCESS_KEY=your_unsplash_api_key_here
     ```

3. **Freepik API**
   - Visit: https://developers.freepik.com/
   - Register for an API key
   - Add to .env.local:
     ```
     NEXT_PUBLIC_FREEPIK_API_KEY=your_freepik_api_key_here
     ```

4. **Pixabay API**
   - Visit: https://pixabay.com/api/docs/
   - Register for an API key
   - Add to .env.local:
     ```
     NEXT_PUBLIC_PIXABAY_API_KEY=your_pixabay_api_key_here
     ```

## Usage

### Basic Usage

```typescript
import { fetchSmartImages } from '@/lib/services/smartImageService';

// In your component
const handleFindImages = async () => {
  const images = await fetchSmartImages(product, 'instagram');
  
  if (images.length > 0) {
    // Do something with the images
    console.log(`Found ${images.length} images`);
  }
};
```

### Advanced Usage with AI Orchestration

The Smart Image Service integrates with your existing AI orchestration system to generate intelligent search queries:

```typescript
// The service automatically uses your AI orchestration system
// to generate optimal search terms based on product details
const smartImages = await fetchSmartImages(product, platform);

// You can check image relevance
const highRelevanceImages = smartImages.filter(img => img.relevance === 'high');
```

## How It Works

1. **Query Generation**: The service uses your AI orchestration system to analyze the product and generate optimal search terms.

2. **API Priority**: The service tries multiple image APIs in this order:
   - Google Custom Search API (most relevant)
   - Freepik API
   - Pixabay API
   - Unsplash API
   - Alternative queries on Unsplash

3. **Fallback Mechanism**: If all APIs fail, the service generates mock images.

4. **Product Image Priority**: The product's own image is always included first in the results.

## Future Enhancements

- **Image Content Analysis**: Add computer vision to analyze image content
- **Performance Tracking**: Track which images perform best in campaigns
- **Learning System**: Improve image selection based on performance data
- **Custom Filters**: Add platform-specific filters for different social media platforms
- **Image Editing**: Automatically crop and resize images for different platforms

## Troubleshooting

If you're experiencing issues with the Smart Image Service:

1. **Check API Keys**: Ensure all API keys are correctly set in .env.local
2. **Check Console**: Look for error messages in the browser console
3. **API Limits**: Some APIs have rate limits that may be exceeded
4. **Fallback System**: The service should always provide at least mock images

## Contributing

To enhance the Smart Image Service:

1. Add support for additional image APIs
2. Improve the AI query generation
3. Add image content analysis
4. Implement feedback loops for learning
