import { useState, useEffect } from 'react';
import AgeVerification from '@/components/AgeVerification';
import HeroCarousel from '@/components/HeroCarousel';
import CategoryPreview from '@/components/CategoryPreview';
import FeaturedProducts from '@/components/products/FeaturedProducts';
import BlogPreview from '@/components/BlogPreview';
import FaqSection from '@/components/FaqSection';
import ParallaxTestimonials from '@/components/ParallaxTestimonials';
import Newsletter from '@/components/Newsletter';
import ChatBubble from '@/components/chat/ChatBubble';
import ChatInterface from '@/components/chat/ChatInterface';
import AnimatedOnScroll from '@/components/animations/AnimatedOnScroll';
import ParallaxFeatures from '@/components/ParallaxFeatures';
import ParallaxShowcase from '@/components/ParallaxShowcase';
import VideoHero from '@/components/VideoHero';
import { categories, blogPosts, faqs } from '@/data/sampleData';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

const Index = () => {
  // State to track if age verification has passed
  const [isVerified, setIsVerified] = useState<boolean>(false);
  // State to control chat visibility
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);

  useEffect(() => {
    const hasVerified = localStorage.getItem('ageVerified');
    setIsVerified(!!hasVerified);
  }, []);

  // Update verification state when local storage changes
  useEffect(() => {
    const handleStorageChange = () => {
      const hasVerified = localStorage.getItem('ageVerified');
      setIsVerified(!!hasVerified);
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Fetch latest 3 published blogs for homepage
  const { data: blogs, isLoading: blogsLoading } = useQuery({
    queryKey: ['homepage_blogs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blogs')
        .select('id, title, slug, summary, featured_image, author_id, category, published_at')
        .eq('is_published', true)
        .order('published_at', { ascending: false })
        .limit(3);
      if (error) return [];
      // Fetch author names
      const authorIds = data?.map(b => b.author_id).filter(Boolean);
      let profiles = [];
      if (authorIds && authorIds.length > 0) {
        const { data: profilesData } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', authorIds);
        profiles = profilesData || [];
      }
      return (data || []).map(blog => ({
        id: blog.id,
        title: blog.title,
        slug: blog.slug,
        excerpt: blog.summary || '',
        image: blog.featured_image || '/placeholder.svg',
        date: blog.published_at ? new Date(blog.published_at).toLocaleDateString('en-GB', { day: '2-digit', month: 'long', year: 'numeric' }) : '',
        author: (profiles.find(p => p.id === blog.author_id) ? `${profiles.find(p => p.id === blog.author_id).first_name} ${profiles.find(p => p.id === blog.author_id).last_name}` : 'Staff Writer'),
        category: blog.category || 'General',
      }));
    },
  });

  return (
    <div className="min-h-screen flex flex-col">
      <AgeVerification />
      
      <main className="flex-grow">
        {/* Video Hero Section (replace HeroCarousel, keep as backup below) */}
        <VideoHero
          title="Premium CBD, Seeds & Smoking Accessories"
          subtitle="Discover our curated collection of high-quality CBD products, specially selected seeds and artisanal smoking accessories for the modern enthusiast"
          videoUrl="/videos/herovid.webm"
          ctaText="Shop Products"
          ctaAction={() => window.location.href = '/shop'}
        />
        {/*
        <HeroCarousel />
        */}
        
        {/* Category Preview - wave divider must be outside animation wrapper for overlap */}
        <CategoryPreview withWaveDivider={true} />
        
        {/* New Parallax Showcase Section */}
        <ParallaxShowcase />
        
        {/* Featured Products */}
        <AnimatedOnScroll animation="fade-up">
          <FeaturedProducts 
            title="Featured Products" 
            subtitle="Our handpicked selection of premium products to enhance your experience."
            featured={true}
            limit={8}
            viewAllLink="/shop"
          />
        </AnimatedOnScroll>
        
        {/* New Parallax Features Section */}
        <ParallaxFeatures />
        
        {/* Best Sellers Products */}
        <AnimatedOnScroll animation="fade-up">
          <FeaturedProducts 
            title="Best Sellers" 
            subtitle="Our most popular products loved by our customers."
            bestSellers={true}
            featured={false}
            limit={8}
            viewAllLink="/shop?filter=best-sellers"
          />
        </AnimatedOnScroll>
        
        {/* New Parallax Testimonials Section (replacing standard Testimonials) */}
        <ParallaxTestimonials />
        
        {/* Newsletter */}
        <AnimatedOnScroll animation="fade-in">
          <Newsletter />
        </AnimatedOnScroll>
        
        {/* FAQ Section */}
        <AnimatedOnScroll animation="fade-up">
          <FaqSection faqs={faqs.slice(0, 4)} showAllLink={true} />
        </AnimatedOnScroll>
        
        {/* Blog Preview */}
        <AnimatedOnScroll animation="slide-left">
          <BlogPreview posts={blogsLoading ? [] : (blogs || [])} />
        </AnimatedOnScroll>
      </main>
      
      {/* Removed Footer component from here as it's already in ShopLayout */}
      
      {/* AI Chat Assistant */}
      <ChatBubble 
        isOpen={isChatOpen} 
        onClose={() => setIsChatOpen(false)} 
        onToggle={() => setIsChatOpen(prev => !prev)}
      >
        <ChatInterface />
      </ChatBubble>
    </div>
  );
};

export default Index;
