$files = Get-ChildItem -Path "d:\Projects\bitsnbongs\src" -Filter "*.tsx" -Recurse

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    
    # Replace auth imports
    $updatedContent = $content -replace "import \{ useAuth \} from '@/hooks/auth';", "import { useAuth } from '@/hooks/auth.basic';"
    $updatedContent = $updatedContent -replace "import \{ useAuth \} from '@/contexts/AuthContext';", "import { useAuth } from '@/hooks/auth.basic';"
    $updatedContent = $updatedContent -replace "import \{ useAuth \} from '@/hooks/auth/useAuth';", "import { useAuth } from '@/hooks/auth.basic';"
    $updatedContent = $updatedContent -replace "import \{ useAuth \} from '@/hooks/auth'", "import { useAuth } from '@/hooks/auth.basic'"
    $updatedContent = $updatedContent -replace "import \{ useAuth \} from '@/hooks/auth/useAuth'", "import { useAuth } from '@/hooks/auth.basic'"
    $updatedContent = $updatedContent -replace "import \{ useAuth \} from '@/contexts/AuthContext'", "import { useAuth } from '@/hooks/auth.basic'"
    
    # Also update clearAuthStorage imports
    $updatedContent = $updatedContent -replace "import \{ clearAuthStorage \} from '@/hooks/auth';", "import { clearAuthStorage } from '@/hooks/auth.basic';"
    $updatedContent = $updatedContent -replace "import \{ useAuth, clearAuthStorage \} from '@/hooks/auth';", "import { useAuth, clearAuthStorage } from '@/hooks/auth.basic';"
    
    # Save the updated content back to the file
    if ($content -ne $updatedContent) {
        Set-Content -Path $file.FullName -Value $updatedContent
        Write-Host "Updated auth imports in $($file.FullName)"
    }
}

Write-Host "Auth imports update completed!"
