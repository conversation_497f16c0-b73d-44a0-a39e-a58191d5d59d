/**
 * MCPConnectionManager.ts
 * 
 * Manages connections to the MCP Playwright server
 * Handles connection pooling, retries, and health checks
 */

import { PlaywrightMCPClient } from './PlaywrightMCPClient';

/**
 * Connection manager configuration
 */
interface ConnectionManagerConfig {
  maxConnections?: number;
  retryAttempts?: number;
  retryDelay?: number;
  connectionTimeout?: number;
  healthCheckInterval?: number;
}

/**
 * Connection status
 */
type ConnectionStatus = 'idle' | 'busy' | 'disconnected' | 'error';

/**
 * Connection information
 */
interface ConnectionInfo {
  client: PlaywrightMCPClient;
  status: ConnectionStatus;
  lastUsed: Date;
  id: string;
}

/**
 * Manages connections to MCP server
 */
export class MCPConnectionManager {
  private connections: Map<string, ConnectionInfo> = new Map();
  private maxConnections: number;
  private retryAttempts: number;
  private retryDelay: number;
  private connectionTimeout: number;
  private healthCheckInterval: number;
  private healthCheckTimer: NodeJS.Timeout | null = null;

  /**
   * Constructor
   * @param config - Connection manager configuration
   */
  constructor(config: ConnectionManagerConfig = {}) {
    this.maxConnections = config.maxConnections || 3;
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.connectionTimeout = config.connectionTimeout || 30000;
    this.healthCheckInterval = config.healthCheckInterval || 60000;
    
    // Start health check timer
    this.startHealthCheck();
  }

  /**
   * Get a client connection
   * @returns MCP client
   */
  async getConnection(): Promise<PlaywrightMCPClient> {
    // Try to find an idle connection
    const idleConnection = this.findIdleConnection();
    
    if (idleConnection) {
      // Mark connection as busy
      this.updateConnectionStatus(idleConnection.id, 'busy');
      return idleConnection.client;
    }
    
    // Check if we can create a new connection
    if (this.connections.size < this.maxConnections) {
      return this.createNewConnection();
    }
    
    // Wait for an idle connection
    return this.waitForIdleConnection();
  }

  /**
   * Release a connection
   * @param client - MCP client
   */
  releaseConnection(client: PlaywrightMCPClient): void {
    // Find connection for client
    for (const [id, info] of this.connections.entries()) {
      if (info.client === client) {
        // Mark connection as idle
        this.updateConnectionStatus(id, 'idle');
        break;
      }
    }
  }

  /**
   * Close all connections
   */
  async closeAll(): Promise<void> {
    // Stop health check timer
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
    
    // Close all connections
    const closePromises: Promise<void>[] = [];
    
    for (const [id, info] of this.connections.entries()) {
      closePromises.push(
        info.client.close().catch(error => {
          console.error(`Failed to close connection ${id}:`, error);
        })
      );
    }
    
    // Wait for all connections to close
    await Promise.all(closePromises);
    
    // Clear connections
    this.connections.clear();
  }

  /**
   * Find an idle connection
   * @returns Connection info or null
   */
  private findIdleConnection(): ConnectionInfo | null {
    for (const info of this.connections.values()) {
      if (info.status === 'idle') {
        return info;
      }
    }
    
    return null;
  }

  /**
   * Create a new connection
   * @returns MCP client
   */
  private async createNewConnection(): Promise<PlaywrightMCPClient> {
    const id = `connection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const client = new PlaywrightMCPClient();
    
    // Add connection to map
    this.connections.set(id, {
      client,
      status: 'busy',
      lastUsed: new Date(),
      id
    });
    
    // Connect to MCP server with retries
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        await client.connect();
        return client;
      } catch (error) {
        if (attempt === this.retryAttempts) {
          // Mark connection as error
          this.updateConnectionStatus(id, 'error');
          throw error;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      }
    }
    
    // This should never happen due to the throw above
    throw new Error('Failed to create connection after retries');
  }

  /**
   * Wait for an idle connection
   * @returns MCP client
   */
  private async waitForIdleConnection(): Promise<PlaywrightMCPClient> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkForIdleConnection = () => {
        // Check if we have an idle connection
        const idleConnection = this.findIdleConnection();
        
        if (idleConnection) {
          // Mark connection as busy
          this.updateConnectionStatus(idleConnection.id, 'busy');
          resolve(idleConnection.client);
          return;
        }
        
        // Check if we've timed out
        if (Date.now() - startTime > this.connectionTimeout) {
          reject(new Error('Timed out waiting for idle connection'));
          return;
        }
        
        // Check again in 100ms
        setTimeout(checkForIdleConnection, 100);
      };
      
      // Start checking
      checkForIdleConnection();
    });
  }

  /**
   * Update connection status
   * @param id - Connection ID
   * @param status - New status
   */
  private updateConnectionStatus(id: string, status: ConnectionStatus): void {
    const info = this.connections.get(id);
    
    if (info) {
      info.status = status;
      info.lastUsed = new Date();
    }
  }

  /**
   * Start health check timer
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckInterval);
  }

  /**
   * Perform health check on all connections
   */
  private async performHealthCheck(): Promise<void> {
    const now = new Date();
    const idleTimeout = 5 * 60 * 1000; // 5 minutes
    
    // Check each connection
    for (const [id, info] of this.connections.entries()) {
      // Close idle connections that haven't been used for a while
      if (info.status === 'idle' && now.getTime() - info.lastUsed.getTime() > idleTimeout) {
        try {
          await info.client.close();
          this.connections.delete(id);
        } catch (error) {
          console.error(`Failed to close idle connection ${id}:`, error);
        }
      }
      
      // Try to reconnect error connections
      if (info.status === 'error') {
        try {
          await info.client.connect();
          this.updateConnectionStatus(id, 'idle');
        } catch (error) {
          console.error(`Failed to reconnect error connection ${id}:`, error);
        }
      }
    }
  }
}
