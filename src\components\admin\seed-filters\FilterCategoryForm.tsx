import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface FilterCategory {
  id: string;
  name: string;
  display_name: string;
  display_order: number;
  category_id: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FilterCategoryFormProps {
  category: FilterCategory | null;
  onSubmit: (category: Partial<FilterCategory>) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export function FilterCategoryForm({
  category,
  onSubmit,
  onCancel,
  isLoading
}: FilterCategoryFormProps) {
  const [name, setName] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [displayOrder, setDisplayOrder] = useState(0);
  const [isActive, setIsActive] = useState(true);
  const [categoryId, setCategoryId] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Fetch product categories for the category_id dropdown
  const { data: productCategories = [] } = useQuery({
    queryKey: ["product-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .order("name");

      if (error) throw error;
      return data;
    }
  });

  // Initialize form with category data if editing
  useEffect(() => {
    if (category) {
      setName(category.name);
      setDisplayName(category.display_name);
      setDisplayOrder(category.display_order);
      setIsActive(category.is_active);
      setCategoryId(category.category_id);
    } else {
      // For new categories, set the display order to be the next in sequence
      const getNextDisplayOrder = async () => {
        const { data, error } = await supabase
          .from("filter_categories")
          .select("display_order")
          .order("display_order", { ascending: false })
          .limit(1);

        if (!error && data && data.length > 0) {
          setDisplayOrder(data[0].display_order + 10);
        } else {
          setDisplayOrder(10);
        }
      };

      getNextDisplayOrder();
    }
  }, [category]);

  // Generate a slug-like name from display name
  const handleDisplayNameChange = (value: string) => {
    setDisplayName(value);
    // Only auto-generate the name if it's a new category or the name hasn't been manually edited
    if (!category || category.name === name) {
      setName(value.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, ''));
    }
  };

  // Validate form before submission
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!name.trim()) {
      errors.name = "Name is required";
    } else if (!/^[a-z0-9_]+$/.test(name)) {
      errors.name = "Name can only contain lowercase letters, numbers, and underscores";
    }

    if (!displayName.trim()) {
      errors.displayName = "Display name is required";
    }

    if (displayOrder < 0) {
      errors.displayOrder = "Display order must be a positive number";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    onSubmit({
      name,
      display_name: displayName,
      display_order: displayOrder,
      is_active: isActive,
      category_id: categoryId
    });
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {category ? "Edit Filter Category" : "Add Filter Category"}
          </DialogTitle>
          <DialogDescription>
            {category 
              ? "Update the details for this filter category." 
              : "Create a new filter category for seed products."}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="display-name" className="text-right">
              Display Name
            </Label>
            <div className="col-span-3">
              <Input
                id="display-name"
                value={displayName}
                onChange={(e) => handleDisplayNameChange(e.target.value)}
                placeholder="e.g. Seed Type"
              />
              {formErrors.displayName && (
                <p className="text-sm text-red-500 mt-1">{formErrors.displayName}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Internal Name
            </Label>
            <div className="col-span-3">
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g. seed_type"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Used internally. Use lowercase letters, numbers, and underscores only.
              </p>
              {formErrors.name && (
                <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="display-order" className="text-right">
              Display Order
            </Label>
            <div className="col-span-3">
              <Input
                id="display-order"
                type="number"
                value={displayOrder}
                onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
                min={0}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Lower numbers appear first in the filter sidebar.
              </p>
              {formErrors.displayOrder && (
                <p className="text-sm text-red-500 mt-1">{formErrors.displayOrder}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">
              Product Category
            </Label>
            <div className="col-span-3">
              <select
                id="category"
                className="w-full p-2 border rounded"
                value={categoryId || ""}
                onChange={(e) => setCategoryId(e.target.value || null)}
              >
                <option value="">All Categories</option>
                {productCategories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground mt-1">
                Limit this filter to a specific product category, or leave blank for all categories.
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="is-active" className="text-right">
              Active
            </Label>
            <div className="flex items-center space-x-2 col-span-3">
              <Switch
                id="is-active"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="is-active">
                {isActive ? "Active" : "Inactive"}
              </Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : (category ? "Update" : "Create")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
