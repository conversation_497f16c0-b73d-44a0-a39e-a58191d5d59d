/**
 * Client-side proxy for image search APIs with fallback to Supabase Edge Function
 * This handles Pixabay and Freepik API calls with CORS workarounds
 */

import { supabase } from '@/lib/supabase';
import { getContextTerms, isImageRelevant, calculateRelevanceScore, IMAGE_SEARCH_CONFIG } from '@/config/imageSearchConfig';

// API Configuration
const PIXABAY_API_KEY = import.meta.env.VITE_PIXABAY_API_KEY;
const FREEPIK_API_KEY = import.meta.env.VITE_FREEPIK_API_KEY;

interface ImageResult {
  url: string;
  thumbnail: string;
  title: string;
  source: 'pixabay' | 'freepik';
}

interface SearchResponse {
  images: ImageResult[];
  totalHits: number;
  source: string;
}

/**
 * Search Pixabay for images
 */
/**
 * Clean and optimize search query for better relevance
 */
function optimizeSearchQuery(query: string): string {
  // Remove common stop words that don't help with image search
  const stopWords = ['the', 'of', 'in', 'and', 'or', 'but', 'a', 'an', 'is', 'are', 'was', 'were'];

  // Extract key terms and clean up
  let cleanQuery = query.toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Remove punctuation
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.includes(word))
    .slice(0, 4) // Limit to 4 most important words
    .join(' ');

  // Add relevant context terms based on the topic
  const contextTerms = getContextTerms(cleanQuery);
  if (contextTerms.length > 0) {
    // Add 2-3 most relevant context terms
    const selectedContext = contextTerms.slice(0, 3).join(' ');
    cleanQuery = `${cleanQuery} ${selectedContext}`;
  }

  return cleanQuery;
}

async function searchPixabayImages(query: string, count: number = 5): Promise<ImageResult[]> {
  if (!PIXABAY_API_KEY) {
    console.warn('Pixabay API key not configured');
    return [];
  }

  try {
    const optimizedQuery = optimizeSearchQuery(query);
    console.log(`Original query: "${query}" → Optimized: "${optimizedQuery}"`);

    const cleanQuery = encodeURIComponent(optimizedQuery);
    const apiUrl = `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=${cleanQuery}&image_type=photo&per_page=${Math.min(count * 2, 20)}&safesearch=true&category=health,nature,business,science,places&min_width=800&min_height=600&order=popular`;

    const response = await fetch(apiUrl);

    if (!response.ok) {
      throw new Error(`Pixabay API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.hits || data.hits.length === 0) {
      // Fallback with simpler query
      const fallbackQuery = query.split(' ').slice(0, 2).join(' ');
      console.log(`No results found, trying fallback query: "${fallbackQuery}"`);

      const fallbackUrl = `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=${encodeURIComponent(fallbackQuery)}&image_type=photo&per_page=${Math.min(count, 10)}&safesearch=true&min_width=600&min_height=400`;
      const fallbackResponse = await fetch(fallbackUrl);

      if (fallbackResponse.ok) {
        const fallbackData = await fallbackResponse.json();
        return fallbackData.hits?.slice(0, count).map((hit: any) => ({
          url: hit.webformatURL || hit.largeImageURL,
          thumbnail: hit.previewURL,
          title: hit.tags,
          source: 'pixabay' as const
        })) || [];
      }
    }

    // Filter and sort results by relevance
    const results = data.hits?.map((hit: any) => ({
      url: hit.webformatURL || hit.largeImageURL,
      thumbnail: hit.previewURL,
      title: hit.tags,
      source: 'pixabay' as const,
      views: hit.views,
      downloads: hit.downloads
    })) || [];

    // Filter and score results by relevance
    const scoredResults = results.map((result: any) => ({
      ...result,
      relevanceScore: calculateRelevanceScore(result.title, query),
      isRelevant: isImageRelevant(result.title, query)
    }));

    // Filter out irrelevant images
    const filteredResults = scoredResults.filter((result: any) => result.isRelevant);

    // Sort by relevance score first, then by popularity
    const sortedResults = (filteredResults.length > 0 ? filteredResults : scoredResults)
      .sort((a: any, b: any) => {
        // Primary sort: relevance score
        if (b.relevanceScore !== a.relevanceScore) {
          return b.relevanceScore - a.relevanceScore;
        }
        // Secondary sort: popularity
        return (b.views + b.downloads) - (a.views + a.downloads);
      })
      .slice(0, count);

    console.log(`Filtered ${results.length} results down to ${filteredResults.length}, returning ${sortedResults.length}`);
    console.log('Top result relevance scores:', sortedResults.slice(0, 3).map(r => ({ score: r.relevanceScore, tags: r.title.substring(0, 50) })));

    return sortedResults;

  } catch (error) {
    console.error('Pixabay search failed:', error);
    return [];
  }
}

/**
 * Search Freepik for images (if API access is available)
 */
async function searchFreepikImages(query: string, count: number = 5): Promise<ImageResult[]> {
  if (!FREEPIK_API_KEY) {
    console.warn('Freepik API key not configured');
    return [];
  }

  try {
    // Note: Freepik API might require different endpoint structure
    // This is a placeholder - adjust based on actual Freepik API documentation
    const cleanQuery = encodeURIComponent(query.trim());
    const apiUrl = `https://api.freepik.com/v1/resources?locale=en-US&page=1&limit=${count}&order=latest&filters[content_type][0]=photo&term=${cleanQuery}`;

    const response = await fetch(apiUrl, {
      headers: {
        'X-Freepik-API-Key': FREEPIK_API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Freepik API error: ${response.status}`);
    }

    const data = await response.json();

    return data.data?.map((item: any) => ({
      url: item.image?.source?.url || item.thumbnails?.large?.url,
      thumbnail: item.thumbnails?.small?.url,
      title: item.title,
      source: 'freepik' as const
    })) || [];

  } catch (error) {
    console.error('Freepik search failed:', error);
    return [];
  }
}

/**
 * Call Supabase Edge Function for server-side image search
 */
async function searchWithEdgeFunction(query: string, count: number = 5): Promise<ImageResult[]> {
  try {
    const { data, error } = await supabase.functions.invoke('image-search', {
      body: { query, count }
    });

    if (error) {
      console.warn('Edge function error:', error);
      return [];
    }

    return data?.images || [];
  } catch (error) {
    console.warn('Failed to call edge function:', error);
    return [];
  }
}

/**
 * Smart image search with fallbacks and caching
 */
export async function searchImagesWithProxy(query: string, count: number = 5): Promise<SearchResponse> {
  const results: ImageResult[] = [];

  try {
    // Try Pixabay first (most reliable for client-side)
    console.log(`Searching Pixabay for: ${query}`);
    const pixabayResults = await searchPixabayImages(query, count);
    results.push(...pixabayResults);

    // If we need more images, try Freepik
    if (results.length < count && FREEPIK_API_KEY) {
      console.log(`Searching Freepik for additional images: ${query}`);
      const freepikResults = await searchFreepikImages(query, count - results.length);
      results.push(...freepikResults);
    }

    // If still not enough, try the edge function as fallback
    if (results.length < count) {
      console.log(`Trying Supabase Edge Function as fallback for: ${query}`);
      const edgeFunctionResults = await searchWithEdgeFunction(query, count - results.length);
      results.push(...edgeFunctionResults);
    }

    return {
      images: results.slice(0, count),
      totalHits: results.length,
      source: results.length > 0 ? 'proxy-api' : 'none'
    };

  } catch (error) {
    console.error('Image search proxy failed:', error);
    return {
      images: [],
      totalHits: 0,
      source: 'error'
    };
  }
}

/**
 * Get a single random image for a topic
 */
export async function getRandomImageForTopic(topic: string): Promise<string | null> {
  try {
    const searchResult = await searchImagesWithProxy(topic, 10);

    if (searchResult.images.length > 0) {
      const randomIndex = Math.floor(Math.random() * searchResult.images.length);
      return searchResult.images[randomIndex].url;
    }

    return null;
  } catch (error) {
    console.error('Failed to get random image:', error);
    return null;
  }
}

/**
 * Create smart search variations based on topic content
 */
function createSearchVariations(originalTopic: string): string[] {
  const topic = originalTopic.toLowerCase();
  const variations: string[] = [];

  // Always start with the optimized original
  variations.push(originalTopic);

  // Create topic-specific variations for your product categories
  if (topic.includes('cannabis') || topic.includes('seeds')) {
    variations.push(
      'hemp seeds legal cultivation',
      'cannabis plant business legal',
      'hemp growing agriculture',
      'legal cannabis industry',
      'hemp products natural'
    );
  } else if (topic.includes('cbd')) {
    variations.push(
      'CBD oil wellness bottle',
      'hemp oil natural health',
      'CBD products lifestyle',
      'hemp extract organic',
      'CBD tincture wellness'
    );
  } else if (topic.includes('vaping') || topic.includes('vape') || topic.includes('vaporizer')) {
    variations.push(
      'vaporizer device modern electronic',
      'vape pen portable sleek',
      'electronic vaping technology',
      'vaporizer lifestyle modern',
      'vaping device premium'
    );
  } else if (topic.includes('bong') || topic.includes('water pipe')) {
    variations.push(
      'glass bong water pipe',
      'glass smoking accessories',
      'water pipe glass artistic',
      'bong glass crafted quality',
      'glass pipe smoking equipment'
    );
  } else if (topic.includes('smoking') || topic.includes('accessories')) {
    variations.push(
      'smoking accessories equipment',
      'grinder metal accessories',
      'smoking tools lifestyle',
      'pipe accessories glass',
      'smoking equipment modern'
    );
  } else if (topic.includes('grinder')) {
    variations.push(
      'herb grinder metal',
      'grinder accessories equipment',
      'metal grinder quality',
      'herb grinder modern',
      'grinder smoking accessories'
    );
  } else if (topic.includes('legal') || topic.includes('law')) {
    variations.push(
      'legal documents business',
      'law office professional',
      'legal consultation',
      'business law',
      'legal advice'
    );
  } else {
    // For non-product topics, use more general variations
    const firstWords = originalTopic.split(' ').slice(0, 2).join(' ');
    variations.push(
      `${firstWords} lifestyle`,
      `${originalTopic.split(' ')[0]} modern`,
      'business professional',
      'lifestyle contemporary',
      'modern design'
    );
  }

  // Remove duplicates and limit to 5 variations
  return [...new Set(variations)].slice(0, 5);
}

/**
 * Enhanced search with topic variations and smart fallbacks
 */
export async function searchImagesWithVariations(originalTopic: string): Promise<string | null> {
  const variations = createSearchVariations(originalTopic);
  console.log(`Trying ${variations.length} search variations for: "${originalTopic}"`);

  for (const variation of variations) {
    try {
      const result = await getRandomImageForTopic(variation);
      if (result) {
        console.log(`Found image using variation: "${variation}"`);
        return result;
      }
    } catch (error) {
      console.warn(`Search failed for variation "${variation}":`, error);
      continue;
    }
  }

  return null;
}
