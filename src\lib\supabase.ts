import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

// Initialize the Supabase client with the correct API key
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

// Log the Supabase configuration for debugging
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key length:', supabaseAnonKey.length);

// Create the client with the same options as the original configuration
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storageKey: 'supabase.auth.token',
  }
});
