import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Product, Category } from "@/types/database";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "react-hot-toast";
import { slugify } from "@/lib/utils";

interface FixedProductFormProps {
  product?: Product;
  onSuccess: () => void;
  onCancel: () => void;
}

export function FixedProductForm({ product, onSuccess, onCancel }: FixedProductFormProps) {
  // State for form data
  const [formData, setFormData] = useState<any>({
    name: "",
    description: "",
    price: 0,
    stock_quantity: 0,
    category_id: "",
    subcategory_id: "",
    brand_id: "",
    in_stock: true,
    ...product
  });

  // State for categories and subcategories
  const [categories, setCategories] = useState<any[]>([]);
  const [subcategories, setSubcategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch categories and subcategories
  useEffect(() => {
    const fetchCategories = async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .is("parent_id", null)
        .order("name");

      if (error) {
        console.error("Error fetching categories:", error);
        return;
      }

      setCategories(data || []);
    };

    const fetchSubcategories = async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .not("parent_id", "is", null)
        .order("name");

      if (error) {
        console.error("Error fetching subcategories:", error);
        return;
      }

      setSubcategories(data || []);
    };

    fetchCategories();
    fetchSubcategories();
  }, []);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare data for submission
      const productData: any = {
        name: formData.name,
        slug: formData.slug || slugify(formData.name),
        description: formData.description || '',
        price: Number(formData.price),
        stock_quantity: Number(formData.stock_quantity),
        category_id: formData.category_id,
        subcategory_id: formData.subcategory_id || null,
        brand_id: formData.brand_id || null,
        in_stock: true,
        updated_at: new Date().toISOString()
      };
      
      // If we're updating an existing product, include the ID
      if (product?.id) {
        productData.id = product.id;
      }

      let result;
      
      if (product?.id) {
        // Update existing product
        const { data, error } = await supabase
          .from("products")
          .update(productData)
          .eq("id", product.id)
          .select()
          .single();

        if (error) throw error;
        result = data;
      } else {
        // Create new product
        const { data, error } = await supabase
          .from("products")
          .insert(productData)
          .select()
          .single();

        if (error) throw error;
        result = data;
      }

      toast.success(`Product ${product?.id ? "updated" : "created"} successfully!`);
      onSuccess();
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error("Failed to save product. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Get filtered subcategories based on selected category
  const filteredSubcategories = subcategories.filter(
    (subcategory) => subcategory.parent_id === formData.category_id
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{product?.id ? "Edit" : "Add"} Product</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Product Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name || ""}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ""}
              onChange={handleChange}
              rows={4}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                value={formData.price || ""}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="stock_quantity">Stock Quantity</Label>
              <Input
                id="stock_quantity"
                name="stock_quantity"
                type="number"
                value={formData.stock_quantity || ""}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category_id">Category</Label>
            <select
              id="category_id"
              name="category_id"
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={formData.category_id || ""}
              onChange={(e) => {
                handleChange(e);
                // Reset subcategory when category changes
                setFormData(prev => ({ ...prev, subcategory_id: "" }));
              }}
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {formData.category_id && (
            <div className="space-y-2">
              <Label htmlFor="subcategory_id">Subcategory</Label>
              <select
                id="subcategory_id"
                name="subcategory_id"
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={formData.subcategory_id || ""}
                onChange={handleChange}
              >
                <option value="">None</option>
                {filteredSubcategories.map((subcategory) => (
                  <option key={subcategory.id} value={subcategory.id}>
                    {subcategory.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Save Product"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
