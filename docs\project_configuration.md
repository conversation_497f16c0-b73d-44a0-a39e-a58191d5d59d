# Buds N Bongs Boutique - Project Configuration

## Supabase Configuration

### Connection Details
- **Supabase URL**: `https://pkjyjuaiokrhgbutjhla.supabase.co`
- **Supabase Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ`

### Client Implementation
The working Supabase client is implemented in:
```typescript
// src/integrations/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

const SUPABASE_URL = "https://pkjyjuaiokrhgbutjhla.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storageKey: 'supabase.auth.token',
  }
});
```

### Important Notes
- **DO NOT** create alternative Supabase client implementations
- **ALWAYS** import the Supabase client from `@/integrations/supabase/client`
- If you need to modify the Supabase client, update the original file rather than creating new ones

## Environment Variables

The project uses the following environment variables:
```
VITE_SUPABASE_URL=https://pkjyjuaiokrhgbutjhla.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ
```

## Database Schema

### Products Table
- id (UUID, primary key)
- name (string)
- slug (string, unique)
- description (text)
- price (numeric)
- sale_price (numeric, nullable)
- image (string, URL to image)
- category_id (UUID, foreign key)
- in_stock (boolean)
- is_featured (boolean)
- is_new (boolean)
- is_best_seller (boolean)
- created_at (timestamp)

### Categories Table
- id (UUID, primary key)
- name (string)
- slug (string, unique)
- created_at (timestamp)

### Profiles Table
- id (UUID, primary key)
- user_id (UUID, foreign key to auth.users)
- first_name (string)
- last_name (string)
- is_admin (boolean)
- created_at (timestamp)

### Saved Items Table
- id (UUID, primary key)
- user_id (UUID, foreign key to auth.users)
- product_id (UUID, foreign key to products)
- created_at (timestamp)

## Authentication

- Login credentials for testing:
  - Email: <EMAIL>
  - Password: password

## Development Server

- The development server runs on port 8080 by default
- Run with `npm run dev`

## Known Issues and Solutions

1. **Supabase Authentication Issues**
   - Problem: "Invalid API key" errors
   - Solution: Ensure all components import the Supabase client from `@/integrations/supabase/client`

2. **Host Whitelist Issues**
   - Problem: "Host is not in insights whitelist" errors
   - Solution: This is a CORS-related issue. Ensure you're using the correct Supabase client configuration.
