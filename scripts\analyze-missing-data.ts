#!/usr/bin/env tsx
/**
 * Analyze missing data in seed products
 * This script identifies products with missing critical information
 * and prioritizes them for manual entry or Super Agent enrichment
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface ProductAnalysis {
  id: string;
  name: string;
  price: number;
  is_active: boolean;
  description: string | null;
  image: string | null;
  sku: string;
  missing_fields: string[];
  priority_score: number;
  category: string;
}

function calculatePriorityScore(product: any): number {
  let score = 0;

  // Higher price = higher priority
  if (product.price > 50) score += 30;
  else if (product.price > 30) score += 20;
  else if (product.price > 15) score += 10;

  // Active products get priority
  if (product.is_active) score += 25;

  // Missing description is critical
  if (!product.description || product.description.trim().length < 50) score += 20;

  // Missing image is important
  if (!product.image) score += 15;

  // Premium brands get priority
  if (product.name.toLowerCase().includes('420 fastbuds')) score += 10;
  if (product.name.toLowerCase().includes('barney')) score += 8;
  if (product.name.toLowerCase().includes('royal queen')) score += 8;

  return score;
}

function identifyMissingFields(product: any): string[] {
  const missing: string[] = [];

  if (!product.description || product.description.trim().length < 50) {
    missing.push('description');
  }

  if (!product.image) {
    missing.push('image');
  }

  if (!product.sku || product.sku.trim().length === 0) {
    missing.push('sku');
  }

  // Check if it's a seed product without seed attributes
  const name = product.name.toLowerCase();
  const isSeed = name.includes('seed') || name.includes('auto') ||
                 name.includes('feminised') || name.includes('feminized') ||
                 name.includes('female');

  if (isSeed && !product.seed_product_attributes?.id) {
    missing.push('seed_attributes');
  }

  return missing;
}

async function analyzeMissingData() {
  console.log('🔍 Analyzing missing data in seed products...\n');

  try {
    // Get all products with their seed attributes
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        is_active,
        description,
        image,
        sku,
        category_id,
        categories!fk_product_category(name),
        seed_product_attributes(id, seed_type, flowering_time, thc_level, effect)
      `);

    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }

    // Filter to seed products and analyze
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('seed') || name.includes('auto') ||
             name.includes('feminised') || name.includes('feminized') ||
             name.includes('female') || name.includes('strain');
    }) || [];

    console.log(`📄 Found ${seedProducts.length} seed products to analyze\n`);

    const analysis: ProductAnalysis[] = seedProducts.map(product => {
      const missingFields = identifyMissingFields(product);
      const priorityScore = calculatePriorityScore(product);

      return {
        id: product.id,
        name: product.name,
        price: product.price || 0,
        is_active: product.is_active,
        description: product.description,
        image: product.image,
        sku: product.sku,
        missing_fields: missingFields,
        priority_score: priorityScore,
        category: product.categories?.name || 'Uncategorized'
      };
    });

    // Sort by priority score (highest first)
    analysis.sort((a, b) => b.priority_score - a.priority_score);

    // Generate reports
    console.log('📊 Missing Data Analysis Results:\n');

    // Summary statistics
    const totalProducts = analysis.length;
    const missingDescription = analysis.filter(p => p.missing_fields.includes('description')).length;
    const missingImage = analysis.filter(p => p.missing_fields.includes('image')).length;
    const missingSeedAttributes = analysis.filter(p => p.missing_fields.includes('seed_attributes')).length;
    const activeProducts = analysis.filter(p => p.is_active).length;

    console.log('📈 Summary Statistics:');
    console.log(`   Total seed products: ${totalProducts}`);
    console.log(`   Active products: ${activeProducts}`);
    console.log(`   Missing descriptions: ${missingDescription} (${Math.round(missingDescription/totalProducts*100)}%)`);
    console.log(`   Missing images: ${missingImage} (${Math.round(missingImage/totalProducts*100)}%)`);
    console.log(`   Missing seed attributes: ${missingSeedAttributes} (${Math.round(missingSeedAttributes/totalProducts*100)}%)`);

    // Top priority products
    console.log('\n🚨 TOP 20 PRIORITY PRODUCTS FOR MANUAL ATTENTION:\n');
    analysis.slice(0, 20).forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   💰 Price: £${product.price} | Active: ${product.is_active ? '✅' : '❌'} | Score: ${product.priority_score}`);
      console.log(`   🔍 Missing: ${product.missing_fields.join(', ')}`);
      console.log('');
    });

    // Export CSV files for different priorities
    const highPriority = analysis.filter(p => p.priority_score >= 50);
    const mediumPriority = analysis.filter(p => p.priority_score >= 30 && p.priority_score < 50);
    const lowPriority = analysis.filter(p => p.priority_score < 30);

    // High priority CSV (for Super Agent)
    const highPriorityCSV = [
      'name,price,is_active,missing_fields,priority_score,description_length',
      ...highPriority.map(p =>
        `"${p.name}",${p.price},${p.is_active},"${p.missing_fields.join('; ')}",${p.priority_score},${p.description?.length || 0}`
      )
    ].join('\n');

    writeFileSync('docs/high-priority-missing-data.csv', highPriorityCSV);

    // Medium priority CSV (for manual entry)
    const mediumPriorityCSV = [
      'name,price,is_active,missing_fields,priority_score,current_description',
      ...mediumPriority.map(p =>
        `"${p.name}",${p.price},${p.is_active},"${p.missing_fields.join('; ')}",${p.priority_score},"${(p.description || '').replace(/"/g, '""')}"`
      )
    ].join('\n');

    writeFileSync('docs/medium-priority-missing-data.csv', mediumPriorityCSV);

    console.log('📁 Files Generated:');
    console.log(`   📄 docs/high-priority-missing-data.csv (${highPriority.length} products)`);
    console.log(`   📄 docs/medium-priority-missing-data.csv (${mediumPriority.length} products)`);

    console.log('\n💡 Recommendations:');
    console.log(`   🎯 Super Agent: Focus on ${highPriority.length} high-priority products`);
    console.log(`   ✏️  Manual Entry: ${mediumPriority.length} medium-priority products`);
    console.log(`   ⏳ Later: ${lowPriority.length} low-priority products`);

    console.log('\n🚀 Next Steps:');
    console.log('   1. Review high-priority-missing-data.csv');
    console.log('   2. Send top 10-20 to Super Agent for enrichment');
    console.log('   3. Manually fill medium-priority products');
    console.log('   4. Use basic enrichment for remaining products');

  } catch (err) {
    console.error('❌ Analysis failed:', err);
  }
}

// Run the analysis
analyzeMissingData().catch(console.error);
