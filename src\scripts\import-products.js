/**
 * Import Products and Variants from CSV
 * 
 * This script imports products and variants from CSV files into the database.
 * It reads the transformed CSV files and inserts the data into the products and product_variants tables.
 * 
 * Usage:
 * node src/scripts/import-products.js [products-csv-path] [variants-csv-path]
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import Papa from 'papaparse';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Get command line arguments
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Default paths
let productsPath = path.join(__dirname, '../../data/output/products-transformed.csv');
let variantsPath = path.join(__dirname, '../../data/output/product_variants.csv');

// Override with command line arguments if provided
if (process.argv.length > 2) {
  productsPath = process.argv[2];
}

if (process.argv.length > 3) {
  variantsPath = process.argv[3];
}

// Helper function to parse CSV file
async function parseCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          resolve(results.data);
        },
        error: (error) => {
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Import products
async function importProducts(productsData) {
  console.log(`Importing ${productsData.length} products...`);
  
  // Process products in batches to avoid hitting rate limits
  const batchSize = 50;
  const batches = [];
  
  for (let i = 0; i < productsData.length; i += batchSize) {
    batches.push(productsData.slice(i, i + batchSize));
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} products)`);
    
    // Process each product in the batch
    for (const product of batch) {
      try {
        // Parse option_definitions as JSON if it's a string
        if (typeof product.option_definitions === 'string') {
          product.option_definitions = JSON.parse(product.option_definitions);
        }
        
        // Parse additional_images as JSON if it's a string
        if (typeof product.additional_images === 'string') {
          product.additional_images = JSON.parse(product.additional_images);
        }
        
        // Convert boolean strings to actual booleans
        product.in_stock = product.in_stock === 'true';
        product.is_featured = product.is_featured === 'true';
        product.is_new = product.is_new === 'true';
        product.is_active = product.is_active === 'true';
        
        // Convert numeric strings to numbers
        product.price = parseFloat(product.price);
        product.sale_price = product.sale_price ? parseFloat(product.sale_price) : null;
        product.stock_quantity = parseInt(product.stock_quantity, 10);
        
        // Insert product into database
        const { data, error } = await supabase
          .from('products')
          .insert(product);
        
        if (error) {
          console.error(`Error inserting product ${product.id}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      } catch (error) {
        console.error(`Error processing product ${product.id}:`, error);
        errorCount++;
      }
    }
  }
  
  console.log(`Products import complete: ${successCount} succeeded, ${errorCount} failed`);
}

// Import variants
async function importVariants(variantsData) {
  console.log(`Importing ${variantsData.length} variants...`);
  
  // Process variants in batches
  const batchSize = 100;
  const batches = [];
  
  for (let i = 0; i < variantsData.length; i += batchSize) {
    batches.push(variantsData.slice(i, i + batchSize));
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} variants)`);
    
    // Process each variant in the batch
    for (const variant of batch) {
      try {
        // Parse option_combination as JSON if it's a string
        if (typeof variant.option_combination === 'string') {
          variant.option_combination = JSON.parse(variant.option_combination);
        }
        
        // Convert boolean strings to actual booleans
        variant.in_stock = variant.in_stock === 'true';
        variant.is_active = variant.is_active === 'true';
        
        // Convert numeric strings to numbers
        variant.price = parseFloat(variant.price);
        variant.sale_price = variant.sale_price ? parseFloat(variant.sale_price) : null;
        variant.stock_quantity = parseInt(variant.stock_quantity, 10);
        
        // Insert variant into database
        const { data, error } = await supabase
          .from('product_variants')
          .insert(variant);
        
        if (error) {
          console.error(`Error inserting variant ${variant.id}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      } catch (error) {
        console.error(`Error processing variant ${variant.id}:`, error);
        errorCount++;
      }
    }
  }
  
  console.log(`Variants import complete: ${successCount} succeeded, ${errorCount} failed`);
}

// Main function
async function main() {
  try {
    // Parse CSV files
    console.log(`Reading products from ${productsPath}`);
    const productsData = await parseCsvFile(productsPath);
    
    console.log(`Reading variants from ${variantsPath}`);
    const variantsData = await parseCsvFile(variantsPath);
    
    // Import products first
    await importProducts(productsData);
    
    // Then import variants
    await importVariants(variantsData);
    
    console.log('Import completed successfully');
  } catch (error) {
    console.error('Error during import:', error);
    process.exit(1);
  }
}

// Run the main function
main();
