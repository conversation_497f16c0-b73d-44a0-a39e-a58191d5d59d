
import React, { useEffect, useState } from 'react';
import { useAuthProvider } from './useAuthProvider';
import { AuthContext, defaultAuthContext } from './AuthContext';

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuthProvider();
  const [renderContent, setRenderContent] = useState(false);
  
  useEffect(() => {
    if (!auth.isInitializing) {
      setRenderContent(true);
    }
  }, [auth.isInitializing]);
  
  // If we're in the initial loading state, show a minimal loading indicator
  if (!renderContent) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span className="ml-2">Loading authentication...</span>
      </div>
    );
  }
  
  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};
