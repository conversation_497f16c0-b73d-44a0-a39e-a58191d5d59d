import React, { useEffect } from 'react';
import { toast as baseToast, useToast } from '@/hooks/use-toast';

// Create a wrapper around the toast function that forces auto-dismissal
export function useAutoDismissToast() {
  const { dismiss } = useToast();
  
  const toast = React.useCallback(
    ({ duration = 3000, ...props }: Parameters<typeof baseToast>[0] & { duration?: number }) => {
      // Call the base toast function
      const { id } = baseToast({
        ...props,
        duration,
      });
      
      // Set up our own timer as a backup to force dismissal
      const timer = setTimeout(() => {
        dismiss(id);
      }, duration + 500); // Add a small buffer to ensure it triggers after the animation
      
      // Clean up the timer if the component unmounts
      return {
        id,
        dismiss: () => {
          clearTimeout(timer);
          dismiss(id);
        },
      };
    },
    [dismiss]
  );
  
  return { toast, dismiss };
}

// Create a component that automatically dismisses itself
export function AutoDismissToast({
  title,
  description,
  variant = 'default',
  duration = 3000,
}: {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}) {
  const { toast } = useAutoDismissToast();
  
  useEffect(() => {
    toast({
      title,
      description,
      variant,
      duration,
    });
  }, [title, description, variant, duration, toast]);
  
  return null;
}
