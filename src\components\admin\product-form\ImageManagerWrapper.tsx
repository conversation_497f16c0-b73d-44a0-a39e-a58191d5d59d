import React, { useState, useEffect } from 'react';
import { SimpleImageManager } from './SimpleImageManager';

interface ImageManagerWrapperProps {
  mainImage: string;
  additionalImages: string[];
  onMainImageChange: (url: string) => void;
  onAdditionalImagesChange: (urls: string[]) => void;
  onFindImagesWithAI?: () => void;
  isFindingImages?: boolean;
}

export function ImageManagerWrapper({
  mainImage,
  additionalImages,
  onMainImageChange,
  onAdditionalImagesChange,
  onFindImagesWithAI,
  isFindingImages = false
}: ImageManagerWrapperProps) {
  // Local state to track images
  const [localMainImage, setLocalMainImage] = useState<string>(mainImage);
  const [localAdditionalImages, setLocalAdditionalImages] = useState<string[]>(additionalImages);

  // Update local state when props change
  useEffect(() => {
    setLocalMainImage(mainImage);
    setLocalAdditionalImages(additionalImages);
  }, [mainImage, additionalImages]);

  // Handler for setting a new main image
  const handleSetMainImage = (url: string) => {
    console.log('Setting main image to:', url);
    
    // If this image is in the additional images, remove it
    const newAdditionalImages = localAdditionalImages.filter(img => img !== url);
    
    // If there's a current main image, add it to additional images
    if (localMainImage && localMainImage.trim() !== '') {
      newAdditionalImages.push(localMainImage);
    }
    
    // Update local state
    setLocalMainImage(url);
    setLocalAdditionalImages(newAdditionalImages);
    
    // Notify parent components
    onMainImageChange(url);
    onAdditionalImagesChange(newAdditionalImages);
  };

  // Handler for adding a new additional image
  const handleAddImage = (url: string) => {
    console.log('Adding additional image:', url);
    const newAdditionalImages = [...localAdditionalImages, url];
    setLocalAdditionalImages(newAdditionalImages);
    onAdditionalImagesChange(newAdditionalImages);
  };

  // Handler for removing an additional image
  const handleRemoveImage = (url: string) => {
    console.log('Removing additional image:', url);
    const newAdditionalImages = localAdditionalImages.filter(img => img !== url);
    setLocalAdditionalImages(newAdditionalImages);
    onAdditionalImagesChange(newAdditionalImages);
  };

  // Handler for removing the main image
  const handleRemoveMainImage = () => {
    console.log('Removing main image');
    
    // If we have additional images, make the first one the main image
    if (localAdditionalImages.length > 0) {
      const newMainImage = localAdditionalImages[0];
      const newAdditionalImages = localAdditionalImages.slice(1);
      
      setLocalMainImage(newMainImage);
      setLocalAdditionalImages(newAdditionalImages);
      
      onMainImageChange(newMainImage);
      onAdditionalImagesChange(newAdditionalImages);
    } else {
      // Otherwise just clear the main image
      setLocalMainImage('');
      onMainImageChange('');
    }
  };

  // Handler for reordering additional images
  const handleReorderImages = (newOrder: string[]) => {
    console.log('Reordering additional images:', newOrder);
    setLocalAdditionalImages(newOrder);
    onAdditionalImagesChange(newOrder);
  };

  return (
    <SimpleImageManager
      mainImage={localMainImage}
      additionalImages={localAdditionalImages}
      onSetMainImage={handleSetMainImage}
      onAddImage={handleAddImage}
      onRemoveImage={handleRemoveImage}
      onRemoveMainImage={handleRemoveMainImage}
      onReorderImages={handleReorderImages}
      onFindImagesWithAI={onFindImagesWithAI}
      isFindingImages={isFindingImages}
    />
  );
}
