# AI Features Documentation

This document outlines the specialized AI features in the product management system that need to be preserved when implementing the variant-based product system.

## AI Description Generation

### Implementation
- Located in `useProductAI.ts` hook
- Uses Gemini API to generate product descriptions
- Falls back to client-side implementation if API call fails
- Incorporates web scraping to gather real product descriptions for context

### Key Functions
- `handleGenerateDescription()`: Main function that generates product descriptions
- Uses product name and category to create context-aware descriptions
- Formats descriptions with HTML for rich text display
- Handles error cases with appropriate fallbacks

### Integration Points
- Used in ProductFormFields.tsx via the ProductForm component
- Triggered by a "Generate Description" button in the product form
- Updates the product description field in the form data

## AI Image Finding

### Implementation
- Located in `useProductAI.ts` hook
- Searches for product images based on product name and category
- Saves images to Supabase storage
- Provides multiple fallback mechanisms for image retrieval

### Key Functions
- `handleFindImages()`: Main function that searches for product images
- `saveImageToStorage()`: Helper function to save images to Supabase storage
- Creates placeholder images if image retrieval fails

### Integration Points
- Used in ProductFormFields.tsx via the ProductForm component
- Triggered by a "Find Images" button in the product form
- Updates the product image fields in the form data

## Image Management

### Implementation
- Located in `ProductImageManager.tsx`
- Handles uploading, reordering, and deleting product images
- Manages main image and additional images separately

### Key Functions
- `addImage()`: Adds a new image to the product
- `removeImage()`: Removes an image from the product
- `handleSetAsMain()`: Sets an image as the main product image
- `handleMoveUp()` and `handleMoveDown()`: Reorder additional images

### Integration Points
- Used in ProductFormFields.tsx
- Manages both AI-generated and manually uploaded images

## Related Products Management

### Implementation
- Located in `useProductForm.ts` and `RelatedProductSelector.tsx`
- Allows adding, removing, and reordering related products
- Stores relationships in a separate `related_products` table

### Key Functions
- `handleAddRelatedProduct()`: Adds a related product
- `handleRemoveRelatedProduct()`: Removes a related product
- `handleReorderRelatedProducts()`: Reorders related products
- `fetchRelatedProducts()`: Fetches existing related products

### Integration Points
- Used in ProductForm.tsx
- Manages relationships between products

## Considerations for Variant Implementation

1. **Description Generation**:
   - Should AI-generated descriptions be at the base product level or variant level?
   - Consider generating variant-specific descriptions based on option values

2. **Image Management**:
   - Need to support both product-level and variant-level images
   - Update image finding to potentially search for variant-specific images

3. **Related Products**:
   - Should related products be at the product level or variant level?
   - Consider how to display related products with variants

4. **Form Structure**:
   - Need to update the form to include variant management
   - Preserve all AI features while adding variant capabilities
