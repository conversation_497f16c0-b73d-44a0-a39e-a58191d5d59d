// <PERSON><PERSON>t to force fix shipping methods in the database
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function forceFixShippingMethods() {
  console.log('🔧 Force fixing shipping methods...');
  
  try {
    // 1. Get all shipping methods
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} total shipping methods`);
    
    // 2. Find the Next Day Delivery method
    const nextDayMethod = methods.find(m => m.name === 'Next Day Delivery');
    if (!nextDayMethod) {
      console.log('Next Day Delivery method not found');
      return;
    }
    
    console.log('Next Day Delivery method found:');
    console.log(`- ID: ${nextDayMethod.id}`);
    console.log(`- Active: ${nextDayMethod.is_active}`);
    
    // 3. Force update the Next Day Delivery method to be inactive
    console.log('Forcing Next Day Delivery to be inactive...');
    
    const { error: updateError } = await supabase
      .from('shipping_methods')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', nextDayMethod.id);
      
    if (updateError) {
      console.error('Error updating Next Day Delivery:', updateError);
      return;
    }
    
    console.log('✅ Next Day Delivery has been forced to inactive');
    
    // 4. Clear any cached data in the settings table
    console.log('Clearing cached data...');
    
    // Add a timestamp to force cache invalidation
    const timestamp = new Date().toISOString();
    
    const { error: settingsError } = await supabase
      .from('settings')
      .upsert({ 
        key: 'shipping_cache_timestamp', 
        value: timestamp
      });
      
    if (settingsError) {
      console.error('Error updating settings:', settingsError);
    } else {
      console.log('✅ Cache timestamp updated');
    }
    
    // 5. Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('shipping_methods')
      .select('*')
      .eq('id', nextDayMethod.id)
      .single();
      
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('Verification:');
    console.log(`- Name: ${verifyData.name}`);
    console.log(`- Active: ${verifyData.is_active}`);
    
    if (verifyData.is_active === false) {
      console.log('✅ Next Day Delivery is confirmed inactive');
    } else {
      console.log('❌ Next Day Delivery is still active - database update failed');
    }
    
    console.log('\n🔄 To ensure changes take effect:');
    console.log('1. Restart the development server');
    console.log('2. Clear your browser cache or use incognito mode');
    console.log('3. Check the admin panel and checkout page');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
forceFixShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
  });
