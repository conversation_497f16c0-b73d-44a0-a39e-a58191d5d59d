// API route to fix shipping methods in the database
import { createClient } from '@supabase/supabase-js';

// Use the same credentials as the app
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // First, set all shipping methods to inactive
    const { error: updateAllError } = await supabase
      .from('shipping_methods')
      .update({ 
        is_active: false, 
        updated_at: new Date().toISOString() 
      });
      
    if (updateAllError) {
      console.error('Error updating all methods:', updateAllError);
      return res.status(500).json({ error: 'Error updating all methods' });
    }
    
    // Then set only Standard Shipping to active
    const { error: updateStandardError } = await supabase
      .from('shipping_methods')
      .update({ 
        is_active: true, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', '7e357480-9d1e-4b0c-bcc8-763d9120d7b6'); // Standard Shipping ID
      
    if (updateStandardError) {
      console.error('Error updating Standard Shipping:', updateStandardError);
      return res.status(500).json({ error: 'Error updating Standard Shipping' });
    }
    
    // Finally get all shipping methods to verify
    const { data: methods, error: getError } = await supabase
      .from('shipping_methods')
      .select('id, name, is_active');
      
    if (getError) {
      console.error('Error getting methods:', getError);
      return res.status(500).json({ error: 'Error getting methods' });
    }
    
    // Add a timestamp to bust client caches
    const timestamp = new Date().toISOString();
    
    return res.status(200).json({ 
      message: 'Shipping methods fixed successfully', 
      methods, 
      timestamp 
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'Unexpected error occurred' });
  }
} 