#!/usr/bin/env node

// Import required modules
const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration - using VITE environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key length:', supabaseKey ? supabaseKey.length : 0);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Check if file paths are provided
if (process.argv.length < 3) {
  console.error('Please provide a products CSV file path');
  process.exit(1);
}

// Get the products file path from command line arguments
const productsFilePath = process.argv[2];

// Get the variants file path from command line arguments (optional)
const variantsFilePath = process.argv.length > 3 ? process.argv[3] : null;

// Get the limit from command line arguments (default: 10)
const limit = process.argv.length > 4 ? parseInt(process.argv[4]) : 10;

// Process image URLs to match Supabase storage format
function processImageUrl(imageUrl) {
  if (!imageUrl) return null;

  // Extract just the filename from the URL
  const filename = imageUrl.split('/').pop();
  if (!filename) return null;

  // Remove the ~mv2 suffix and change extension to .webp
  const processedFilename = filename.replace(/~mv2\.(jpg|jpeg|png|webp|gif)$/i, '.webp');

  // Use the full Supabase storage URL format with hardcoded URL to ensure consistency
  const hardcodedUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';

  // Return the full URL
  return `${hardcodedUrl}/storage/v1/object/public/product-images/${processedFilename}`;
}

// Process multiple image URLs
function processImageUrls(imageUrlString) {
  if (!imageUrlString) return [];

  // Split by semicolons or commas
  const urls = imageUrlString.split(/[;,]/).map(url => url.trim()).filter(Boolean);

  // Process each URL
  return urls.map(processImageUrl).filter(Boolean);
}

// Extract option definitions from product
function extractOptionDefinitions(product) {
  // For testing, let's add some predefined options
  const optionDefinitions = {};

  // Add Pack Size option for seed products
  if (product.name.includes('Gorilla') || product.name.includes('Cheese') ||
      product.name.includes('Zkittlez') || product.name.includes('Banana')) {
    optionDefinitions["Pack Size"] = {
      name: "Pack Size",
      values: ["1 Pack", "3 Pack", "5 Pack", "10 Pack"],
      display_type: "dropdown"
    };
  }

  // Add Size option for storage products
  if (product.name.includes('Storage') || product.name.includes('Tightvac')) {
    optionDefinitions["Size"] = {
      name: "Size",
      values: ["Small", "Medium", "Large", "X-Large"],
      display_type: "dropdown"
    };
  }

  // Add Color option for all products
  optionDefinitions["Color"] = {
    name: "Color",
    values: ["Red", "Blue", "Green", "Gold", "Rasta"],
    display_type: "color"
  };

  // Also try to extract options from the product data
  for (let i = 1; i <= 6; i++) {
    const optionName = product[`productOptionName${i}`];
    const optionType = product[`productOptionType${i}`];
    const optionDescription = product[`productOptionDescription${i}`];

    if (optionName && optionType === 'DROP_DOWN' && optionDescription) {
      // Split the description by semicolons or commas to get individual values
      const values = optionDescription.split(/[;,]/).map(value => value.trim()).filter(Boolean);
      if (values.length > 0) {
        optionDefinitions[optionName] = {
          name: optionName,
          values: values,
          display_type: optionName.toLowerCase().includes('color') ? 'color' : 'dropdown'
        };
      }
    }
  }

  return optionDefinitions;
}

// Extract option combination from variant
function extractOptionCombination(variant, product) {
  const combination = {};

  // For Variant fieldType, the option value is directly in the productOptionDescription1 field
  if (variant.fieldType === 'Variant') {
    // Find the option name from the product
    for (let i = 1; i <= 6; i++) {
      const optionName = product[`productOptionName${i}`];
      if (optionName) {
        // The option value is in the same position in the variant
        const optionValue = variant[`productOptionDescription${i}`];
        if (optionValue) {
          combination[optionName] = optionValue.trim();
        }
      }
    }
  } else {
    // For Choice fieldType (legacy support)
    for (let i = 1; i <= 6; i++) {
      const optionName = variant[`productOptionName${i}`];
      const optionValue = variant[`productOptionDescription${i}`];

      if (optionName && optionValue) {
        combination[optionName] = optionValue.trim();
      }
    }
  }

  return combination;
}

// First, clear existing data
async function clearExistingData() {
  console.log('Clearing existing products and variants...');

  try {
    // First truncate product_variants (child table)
    const { error: variantsError } = await supabase
      .from('product_variants')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows

    if (variantsError) {
      console.error('Error clearing variants:', variantsError);
    }

    // Then truncate products
    const { error: productsError } = await supabase
      .from('products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows

    if (productsError) {
      console.error('Error clearing products:', productsError);
    }

    console.log('Existing data cleared.');
  } catch (error) {
    console.error('Error during data clearing:', error);
  }
}

// Main function to import products and variants
async function importProducts(productsData, variantsData = [], limit = 10) {
  console.log(`Starting import of up to ${limit} products...`);

  // First, clear existing data
  await clearExistingData();

  // Filter products to the limit
  const limitedProducts = productsData.slice(0, limit);

  console.log(`Processing ${limitedProducts.length} products (limited to ${limit})`);

  // Process and import each product
  for (const product of limitedProducts) {
    // Process the main product
    const id = uuidv4();
    const name = product.name || '';
    const uniqueId = uuidv4().substring(0, 8);
    const baseSlug = slugify(name, { lower: true, strict: true });
    const uniqueSlug = `${baseSlug}-${uniqueId}`;

    // Process the main image and additional images
    const mainImage = processImageUrl(product.productImageUrl ? product.productImageUrl.split(';')[0] : null);
    const additionalImages = processImageUrls(product.productImageUrl).slice(1);

    // Set is_active to false if there's no image
    const isActive = mainImage !== null;

    if (!isActive) {
      console.log(`Product "${name}" has no image and will be set to inactive`);
    }

    // Extract option definitions
    const optionDefinitions = extractOptionDefinitions(product);

    const processedProduct = {
      id,
      name,
      slug: uniqueSlug,
      description: product.description || '',
      price: parseFloat(product.price) || 0,
      sale_price: product.salePrice ? parseFloat(product.salePrice) : null,
      image: mainImage,
      additional_images: additionalImages,
      category_id: null,
      sku: product.sku || '',
      stock_quantity: parseInt(product.inventory) || 0,
      weight: parseFloat(product.weight) || 0,
      in_stock: true,
      is_featured: false,
      is_new: false,
      is_active: isActive, // Only set active if there's an image
      option_definitions: optionDefinitions,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    console.log(`Importing product: ${processedProduct.name} (${processedProduct.id})`);

    // Insert the product into Supabase
    const { data: insertedProduct, error: productError } = await supabase
      .from('products')
      .insert([processedProduct])
      .select();

    if (productError) {
      console.error(`Error inserting product ${processedProduct.name}:`, productError);
      continue;
    }

    console.log(`Successfully imported product: ${processedProduct.name}`);

    // Find variants for this product in the variants data
    const productVariants = variantsData.filter(v => v.product_id === product.id);

    if (productVariants.length > 0) {
      console.log(`Found ${productVariants.length} variants for product ${processedProduct.name}`);

      // Process and insert variants
      for (const variant of productVariants) {
        // Create a new variant with the correct product_id
        const processedVariant = {
          id: uuidv4(),
          product_id: processedProduct.id,
          variant_name: variant.variant_name || 'Default Variant',
          sku: variant.sku || `${processedProduct.id.substring(0, 8)}-${variant.variant_name}`,
          price: parseFloat(variant.price) || processedProduct.price,
          sale_price: variant.sale_price ? parseFloat(variant.sale_price) : null,
          stock_quantity: parseInt(variant.stock_quantity) || 30,
          in_stock: variant.in_stock === 'true' || variant.in_stock === true,
          image: variant.image || processedProduct.image,
          option_combination: variant.option_combination || '{}',
          is_active: variant.is_active === 'true' || variant.is_active === true,
          external_id: variant.external_id || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        console.log(`Importing variant: ${processedVariant.variant_name} for product ${processedProduct.name}`);

        const { data: insertedVariant, error: variantError } = await supabase
          .from('product_variants')
          .insert([processedVariant])
          .select();

        if (variantError) {
          console.error(`Error inserting variant for ${processedProduct.name}:`, variantError);
        } else {
          console.log(`Successfully imported variant for ${processedProduct.name}`);
        }
      }
    } else {
      console.log(`No variants found for product ${processedProduct.name}`);

      // Create a default variant if no variants exist
      const defaultVariant = {
        id: uuidv4(),
        product_id: processedProduct.id,
        variant_name: 'Default Variant',
        sku: `${processedProduct.id.substring(0, 8)}-default`,
        price: processedProduct.price,
        sale_price: processedProduct.sale_price,
        stock_quantity: processedProduct.stock_quantity || 30,
        in_stock: processedProduct.in_stock,
        image: processedProduct.image,
        option_combination: '{}',
        is_active: processedProduct.is_active,
        external_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log(`Creating default variant for product ${processedProduct.name}`);

      const { data: insertedVariant, error: variantError } = await supabase
        .from('product_variants')
        .insert([defaultVariant])
        .select();

      if (variantError) {
        console.error(`Error inserting default variant for ${processedProduct.name}:`, variantError);
      } else {
        console.log(`Successfully created default variant for ${processedProduct.name}`);
      }
    }
  }

  console.log('Import completed!');
}

// Function to read and parse a CSV file
function readCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    fs.readFile(filePath, 'utf8', (err, data) => {
      if (err) {
        reject(err);
        return;
      }

      Papa.parse(data, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          resolve(results.data);
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  });
}

// Main function to start the import process
async function startImport() {
  try {
    console.log(`Reading products from ${productsFilePath}`);
    const productsData = await readCsvFile(productsFilePath);

    let variantsData = [];
    if (variantsFilePath) {
      console.log(`Reading variants from ${variantsFilePath}`);
      variantsData = await readCsvFile(variantsFilePath);
    }

    // Import the products and variants
    await importProducts(productsData, variantsData, limit);

    console.log('Import process completed successfully!');
  } catch (error) {
    console.error('Error during import process:', error);
    process.exit(1);
  }
}

// Start the import process
startImport();
