import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Search, ShoppingBag, Menu, X, User, Settings, LogOut, Heart,
  Home, Store, Info, BookOpen, HelpCircle, Mail, ChevronDown
} from 'lucide-react';
import Brand<PERSON><PERSON> from './BrandLogo';
import CategoryNavigation from './CategoryNavigation';
import MobileCategories from './MobileCategories';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/auth.basic';
import { useCart } from '@/hooks/useCart';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Categories are now dynamically loaded from the database via CategoryNavigation component

const Navbar = ({ cartComponent }: { cartComponent?: React.ReactNode }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  
  // Wrap auth-related code in try/catch to handle possible initialization issues
  let user = null;
  let signOut = async () => { console.log('Auth not initialized yet'); };
  let isAdmin = false;
  
  try {
    const auth = useAuth();
    user = auth?.user;
    signOut = auth?.signOut || signOut;
    
    // Get the profile from auth context
    const profile = auth?.profile;
    
    // Log information to help debug admin status
    console.log('Auth data in Navbar:', {
      user: !!user,
      profile: profile,
      userMetadata: user?.user_metadata,
      appMetadata: user?.app_metadata
    });
    
    // TEMPORARY FIX: Hardcode admin access for specific user ID
    const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
    const isKnownAdmin = user && knownAdminUserIds.includes(user.id);
    
    if (isKnownAdmin) {
      // This user is a known admin, bypass the profile check
      isAdmin = true;
      console.log('Admin access granted in Navbar based on hardcoded user ID');
    } else {
      // Check for admin status in multiple possible locations
      // IMPORTANT: Check profile.is_admin first as this is the primary source
      isAdmin = (
        // Check profile table first (this is the most reliable source)
        profile?.is_admin === true ||
        // Fallback to checking user metadata
        (user && (
          user.user_metadata?.is_admin === true ||
          user.app_metadata?.is_admin === true ||
          user.app_metadata?.admin === true ||
          user.app_metadata?.role === 'admin'
        ))
      );
    }
    
    console.log('Admin status in Navbar:', isAdmin);
  } catch (error) {
    console.error('Auth context not available yet:', error);
  }
  
  // Safely access cart context
  let totalItems = 0;
  try {
    const cart = useCart();
    totalItems = cart?.totalItems || 0;
  } catch (error) {
    console.error('Cart context not available yet:', error);
  }

  console.log("Current user admin status:", isAdmin); // Debug log to check admin status

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex justify-between items-center h-24">
          {/* Logo */}
          <BrandLogo />

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link to="/" className={`nav-link font-medium flex items-center gap-1.5 ${isActive('/') ? 'active' : ''} hover:text-primary`}>
              <Home className="h-4 w-4" />
              <span>Home</span>
            </Link>
            <CategoryNavigation />
            <Link to="/about" className={`nav-link flex items-center gap-1.5 ${isActive('/about') ? 'active' : ''} hover:text-primary`}>
              <Info className="h-4 w-4" />
              <span>About Us</span>
            </Link>
            <Link to="/blog" className={`nav-link flex items-center gap-1.5 ${isActive('/blog') ? 'active' : ''} hover:text-primary`}>
              <BookOpen className="h-4 w-4" />
              <span>Blog</span>
            </Link>
            <Link to="/faq" className={`nav-link flex items-center gap-1.5 ${isActive('/faq') ? 'active' : ''} hover:text-primary`}>
              <HelpCircle className="h-4 w-4" />
              <span>FAQ</span>
            </Link>
            <Link to="/contact" className={`nav-link flex items-center gap-1.5 ${isActive('/contact') ? 'active' : ''} hover:text-primary`}>
              <Mail className="h-4 w-4" />
              <span>Contact</span>
            </Link>
            {isAdmin && (
              <Link to="/admin" className={`nav-link flex items-center gap-1.5 ${isActive('/admin') ? 'active' : ''} hover:text-primary`}>
                <Settings className="h-4 w-4" />
                <span>Admin</span>
              </Link>
            )}
          </div>

          {/* Icons */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="text-clay-800 hover:text-primary">
              <Search className="h-5 w-5" />
            </button>
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="text-clay-800 hover:text-primary">
                    <User className="h-5 w-5" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link to="/account" className="w-full">My Account</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/wishlist" className="w-full flex items-center gap-2">
                      <Heart className="h-4 w-4" />
                      My Wishlists
                    </Link>
                  </DropdownMenuItem>
                  {isAdmin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="w-full flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Admin Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()} className="flex items-center gap-2">
                    <LogOut className="h-4 w-4" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link to="/auth" className="text-clay-800 hover:text-primary">
                <User className="h-5 w-5" />
              </Link>
            )}
            {/* Add the cart component if provided */}
            {cartComponent}
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-clay-800 hover:text-primary p-2"
              aria-expanded={isMenuOpen}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t py-4">
          <div className="container-custom space-y-3">
            <Link
              to="/"
              className={`mobile-nav-link font-medium flex items-center gap-2 ${isActive('/') ? 'text-primary' : ''}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <Home className="h-5 w-5" />
              <span>Home</span>
            </Link>

            <div className="text-lg font-medium text-clay-900 mt-4 mb-2">Shop By Category</div>
            <MobileCategories onNavigate={() => setIsMenuOpen(false)} />
            <div className="border-t my-3"></div>
            <Link
              to="/about"
              className={`mobile-nav-link flex items-center gap-2 ${isActive('/about') ? 'text-primary font-medium' : ''}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <Info className="h-5 w-5" />
              <span>About Us</span>
            </Link>
            <Link
              to="/blog"
              className={`mobile-nav-link flex items-center gap-2 ${isActive('/blog') ? 'text-primary font-medium' : ''}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <BookOpen className="h-5 w-5" />
              <span>Blog</span>
            </Link>
            <Link
              to="/faq"
              className={`mobile-nav-link flex items-center gap-2 ${isActive('/faq') ? 'text-primary font-medium' : ''}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <HelpCircle className="h-5 w-5" />
              <span>FAQ</span>
            </Link>
            <Link
              to="/contact"
              className={`mobile-nav-link flex items-center gap-2 ${isActive('/contact') ? 'text-primary font-medium' : ''}`}
              onClick={() => setIsMenuOpen(false)}
            >
              <Mail className="h-5 w-5" />
              <span>Contact</span>
            </Link>
            {isAdmin && (
              <Link
                to="/admin"
                className={`mobile-nav-link flex items-center ${isActive('/admin') ? 'text-primary font-medium' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Admin Dashboard
              </Link>
            )}

            <div className="flex items-center space-x-4 mt-4 pt-3 border-t">
              {user ? (
                <>
                  <Link
                    to="/account"
                    className="flex items-center space-x-2 text-clay-800"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <User className="h-5 w-5" />
                    <span>My Account</span>
                  </Link>
                  <button
                    onClick={() => {
                      signOut();
                      setIsMenuOpen(false);
                    }}
                    className="flex items-center space-x-2 text-clay-800"
                  >
                    <LogOut className="h-5 w-5" />
                    <span>Sign Out</span>
                  </button>
                </>
              ) : (
                <Link
                  to="/auth"
                  className="flex items-center space-x-2 text-clay-800"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <User className="h-5 w-5" />
                  <span>Sign In</span>
                </Link>
              )}
              <Link
                to="/cart"
                className="flex items-center space-x-2 text-clay-800"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative">
                  <ShoppingBag className="h-5 w-5" />
                  {totalItems > 0 && (
                    <span className="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      {totalItems}
                    </span>
                  )}
                </div>
                <span>Cart {totalItems > 0 ? `(${totalItems})` : ''}</span>
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
