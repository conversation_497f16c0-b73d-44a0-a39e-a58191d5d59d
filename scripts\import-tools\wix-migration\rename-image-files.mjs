// <PERSON>ript to rename image files by removing the ~mv2 part
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path to local images
const localImagesPath = path.join(__dirname, 'public', 'images', 'products', 'wix-imports');

// Function to sanitize filenames by removing ~mv2
function sanitizeFilename(filename) {
  return filename.replace(/~mv2/g, '');
}

// Function to rename image files
async function renameImageFiles() {
  console.log('Starting image file renaming...');
  
  try {
    // Read all files in the directory
    const files = fs.readdirSync(localImagesPath);
    console.log(`Found ${files.length} files in ${localImagesPath}`);
    
    let renamed = 0;
    let skipped = 0;
    
    // Process each file
    for (const file of files) {
      // Check if the file contains ~mv2
      if (file.includes('~mv2')) {
        // Create the new filename
        const newFilename = sanitizeFilename(file);
        
        // Skip if the new filename already exists
        if (fs.existsSync(path.join(localImagesPath, newFilename))) {
          console.log(`Skipping ${file} - ${newFilename} already exists`);
          skipped++;
          continue;
        }
        
        // Rename the file
        fs.renameSync(
          path.join(localImagesPath, file),
          path.join(localImagesPath, newFilename)
        );
        
        console.log(`Renamed: ${file} -> ${newFilename}`);
        renamed++;
      } else {
        skipped++;
      }
      
      // Log progress every 50 files
      if ((renamed + skipped) % 50 === 0) {
        console.log(`Progress: ${renamed + skipped}/${files.length} (${renamed} renamed, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${renamed} files renamed, ${skipped} files skipped`);
  } catch (error) {
    console.error('Error renaming files:', error);
  }
}

// Run the script
renameImageFiles().catch(console.error);
