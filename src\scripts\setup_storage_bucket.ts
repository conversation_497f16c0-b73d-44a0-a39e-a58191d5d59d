import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupStorageBucket() {
  console.log('Setting up storage bucket for product images...');
  
  try {
    // Check if the bucket already exists
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      throw bucketsError;
    }
    
    const bucketExists = buckets.some(bucket => bucket.name === 'product-images');
    
    if (!bucketExists) {
      // Create the bucket
      const { data, error } = await supabase.storage.createBucket('product-images', {
        public: true, // Make the bucket public so images can be accessed without authentication
        fileSizeLimit: 5242880, // 5MB
      });
      
      if (error) {
        throw error;
      }
      
      console.log('Created product-images bucket successfully');
    } else {
      console.log('product-images bucket already exists');
      
      // Update bucket to be public if it's not already
      const { error } = await supabase.storage.updateBucket('product-images', {
        public: true,
        fileSizeLimit: 5242880, // 5MB
      });
      
      if (error) {
        throw error;
      }
      
      console.log('Updated product-images bucket settings');
    }
    
    // Set up CORS policy for the bucket
    const { error: corsError } = await supabase.storage.from('product-images').updateBucketCors({
      allowedOrigins: ['*'], // Allow from any origin
      allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['*'],
      maxAgeSeconds: 3600,
    });
    
    if (corsError) {
      throw corsError;
    }
    
    console.log('Set up CORS policy for product-images bucket');
    
  } catch (error) {
    console.error('Error setting up storage bucket:', error);
  }
}

// Run the setup
setupStorageBucket()
  .then(() => {
    console.log('Storage bucket setup completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Storage bucket setup failed:', error);
    process.exit(1);
  });
