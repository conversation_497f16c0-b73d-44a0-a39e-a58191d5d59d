import React, { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Edit, Trash, ArrowUpDown, Save, X, Check } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { FilterCategoryForm } from "@/components/admin/seed-filters/FilterCategoryForm";
import { FilterOptionForm } from "@/components/admin/seed-filters/FilterOptionForm";
import { ProductFilterManager } from "@/components/admin/seed-filters/ProductFilterManager";

// Define types for our filter system
interface FilterCategory {
  id: string;
  name: string;
  display_name: string;
  display_order: number;
  category_id: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FilterOption {
  id: string;
  category_id: string;
  name: string;
  display_name: string;
  display_order: number;
  product_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function SeedFiltersPage() {
  const [activeTab, setActiveTab] = useState("categories");
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isAddingOption, setIsAddingOption] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<FilterCategory | null>(null);
  const [selectedOption, setSelectedOption] = useState<FilterOption | null>(null);
  const [isEditingCategory, setIsEditingCategory] = useState(false);
  const [isEditingOption, setIsEditingOption] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'category' | 'option', id: string } | null>(null);
  
  const queryClient = useQueryClient();

  // Fetch filter categories
  const {
    data: categories = [],
    isLoading: isCategoriesLoading,
    error: categoriesError
  } = useQuery({
    queryKey: ["filter-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("filter_categories")
        .select("*")
        .order("display_order", { ascending: true });

      if (error) throw error;
      return data as FilterCategory[];
    }
  });

  // Fetch filter options for the selected category
  const {
    data: options = [],
    isLoading: isOptionsLoading,
    error: optionsError
  } = useQuery({
    queryKey: ["filter-options", selectedCategory?.id],
    queryFn: async () => {
      if (!selectedCategory) return [];

      const { data, error } = await supabase
        .from("filter_options")
        .select("*")
        .eq("category_id", selectedCategory.id)
        .order("display_order", { ascending: true });

      if (error) throw error;
      return data as FilterOption[];
    },
    enabled: !!selectedCategory
  });

  // Set first category as selected by default when categories load
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0]);
    }
  }, [categories, selectedCategory]);

  // Mutations for categories
  const addCategoryMutation = useMutation({
    mutationFn: async (newCategory: Omit<FilterCategory, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from("filter_categories")
        .insert([newCategory])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-categories"] });
      setIsAddingCategory(false);
      toast({
        title: "Success",
        description: "Filter category added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to add filter category: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const updateCategoryMutation = useMutation({
    mutationFn: async (category: Partial<FilterCategory> & { id: string }) => {
      const { data, error } = await supabase
        .from("filter_categories")
        .update(category)
        .eq("id", category.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-categories"] });
      setIsEditingCategory(false);
      toast({
        title: "Success",
        description: "Filter category updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update filter category: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("filter_categories")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-categories"] });
      setIsDeleteDialogOpen(false);
      setItemToDelete(null);
      if (selectedCategory && itemToDelete?.id === selectedCategory.id) {
        setSelectedCategory(categories.length > 1 ? categories[0] : null);
      }
      toast({
        title: "Success",
        description: "Filter category deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete filter category: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  // Mutations for options
  const addOptionMutation = useMutation({
    mutationFn: async (newOption: Omit<FilterOption, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from("filter_options")
        .insert([newOption])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-options", selectedCategory?.id] });
      setIsAddingOption(false);
      toast({
        title: "Success",
        description: "Filter option added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to add filter option: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const updateOptionMutation = useMutation({
    mutationFn: async (option: Partial<FilterOption> & { id: string }) => {
      const { data, error } = await supabase
        .from("filter_options")
        .update(option)
        .eq("id", option.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-options", selectedCategory?.id] });
      setIsEditingOption(false);
      toast({
        title: "Success",
        description: "Filter option updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update filter option: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  const deleteOptionMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("filter_options")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["filter-options", selectedCategory?.id] });
      setIsDeleteDialogOpen(false);
      setItemToDelete(null);
      toast({
        title: "Success",
        description: "Filter option deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete filter option: ${error.message}`,
        variant: "destructive",
      });
    }
  });

  // Handle delete confirmation
  const handleConfirmDelete = () => {
    if (!itemToDelete) return;
    
    if (itemToDelete.type === 'category') {
      deleteCategoryMutation.mutate(itemToDelete.id);
    } else {
      deleteOptionMutation.mutate(itemToDelete.id);
    }
  };

  // Handle category selection
  const handleCategorySelect = (category: FilterCategory) => {
    setSelectedCategory(category);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Seed Filters Management</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="categories">Filter Categories</TabsTrigger>
          <TabsTrigger value="options">Filter Options</TabsTrigger>
          <TabsTrigger value="products">Product Filters</TabsTrigger>
        </TabsList>

        {/* Filter Categories Tab */}
        <TabsContent value="categories">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Filter Categories</CardTitle>
                <Button onClick={() => setIsAddingCategory(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Category
                </Button>
              </div>
              <CardDescription>
                Manage filter categories for seed products. These categories will appear in the filter sidebar on the seeds page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isCategoriesLoading ? (
                <div className="flex justify-center py-4">
                  <p>Loading categories...</p>
                </div>
              ) : categoriesError ? (
                <div className="text-red-500 py-4">
                  Error loading categories: {(categoriesError as Error).message}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Display Name</TableHead>
                      <TableHead>Internal Name</TableHead>
                      <TableHead>Display Order</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4">
                          No filter categories found. Add your first category to get started.
                        </TableCell>
                      </TableRow>
                    ) : (
                      categories.map((category) => (
                        <TableRow 
                          key={category.id}
                          className={selectedCategory?.id === category.id ? "bg-muted/50" : ""}
                          onClick={() => handleCategorySelect(category)}
                        >
                          <TableCell>{category.display_name}</TableCell>
                          <TableCell>{category.name}</TableCell>
                          <TableCell>{category.display_order}</TableCell>
                          <TableCell>
                            {category.is_active ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                Inactive
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedCategory(category);
                                  setIsEditingCategory(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setItemToDelete({ type: 'category', id: category.id });
                                  setIsDeleteDialogOpen(true);
                                }}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Filter Options Tab */}
        <TabsContent value="options">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Filter Options</CardTitle>
                  {selectedCategory && (
                    <p className="text-sm text-muted-foreground mt-1">
                      For category: <span className="font-medium">{selectedCategory.display_name}</span>
                    </p>
                  )}
                </div>
                <div className="flex gap-4 items-center">
                  {categories.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Label htmlFor="category-select">Category:</Label>
                      <select
                        id="category-select"
                        className="p-2 border rounded"
                        value={selectedCategory?.id || ""}
                        onChange={(e) => {
                          const category = categories.find(c => c.id === e.target.value);
                          if (category) setSelectedCategory(category);
                        }}
                      >
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.display_name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                  <Button 
                    onClick={() => setIsAddingOption(true)}
                    disabled={!selectedCategory}
                  >
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Option
                  </Button>
                </div>
              </div>
              <CardDescription>
                Manage filter options for each category. These options will appear as checkboxes in the filter sidebar.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedCategory ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Please select a filter category to manage its options.
                  </p>
                </div>
              ) : isOptionsLoading ? (
                <div className="flex justify-center py-4">
                  <p>Loading options...</p>
                </div>
              ) : optionsError ? (
                <div className="text-red-500 py-4">
                  Error loading options: {(optionsError as Error).message}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Display Name</TableHead>
                      <TableHead>Internal Name</TableHead>
                      <TableHead>Display Order</TableHead>
                      <TableHead>Product Count</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {options.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          No filter options found for this category. Add your first option to get started.
                        </TableCell>
                      </TableRow>
                    ) : (
                      options.map((option) => (
                        <TableRow key={option.id}>
                          <TableCell>{option.display_name}</TableCell>
                          <TableCell>{option.name}</TableCell>
                          <TableCell>{option.display_order}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {option.product_count}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {option.is_active ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                Inactive
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedOption(option);
                                  setIsEditingOption(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setItemToDelete({ type: 'option', id: option.id });
                                  setIsDeleteDialogOpen(true);
                                }}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Product Filters Tab */}
        <TabsContent value="products">
          <ProductFilterManager />
        </TabsContent>
      </Tabs>

      {/* Add/Edit Category Dialog */}
      {(isAddingCategory || isEditingCategory) && (
        <FilterCategoryForm
          category={isEditingCategory ? selectedCategory : null}
          onSubmit={(category) => {
            if (isEditingCategory && selectedCategory) {
              updateCategoryMutation.mutate({
                id: selectedCategory.id,
                ...category
              });
            } else {
              addCategoryMutation.mutate(category as Omit<FilterCategory, 'id' | 'created_at' | 'updated_at'>);
            }
          }}
          onCancel={() => {
            setIsAddingCategory(false);
            setIsEditingCategory(false);
          }}
          isLoading={addCategoryMutation.isPending || updateCategoryMutation.isPending}
        />
      )}

      {/* Add/Edit Option Dialog */}
      {(isAddingOption || isEditingOption) && selectedCategory && (
        <FilterOptionForm
          option={isEditingOption ? selectedOption : null}
          categoryId={selectedCategory.id}
          onSubmit={(option) => {
            if (isEditingOption && selectedOption) {
              updateOptionMutation.mutate({
                id: selectedOption.id,
                ...option
              });
            } else {
              addOptionMutation.mutate(option as Omit<FilterOption, 'id' | 'created_at' | 'updated_at'>);
            }
          }}
          onCancel={() => {
            setIsAddingOption(false);
            setIsEditingOption(false);
          }}
          isLoading={addOptionMutation.isPending || updateOptionMutation.isPending}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              {itemToDelete?.type === 'category' 
                ? "Are you sure you want to delete this filter category? This will also delete all associated filter options and product relationships."
                : "Are you sure you want to delete this filter option? This will remove it from all products that have it assigned."}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
              disabled={deleteCategoryMutation.isPending || deleteOptionMutation.isPending}
            >
              {deleteCategoryMutation.isPending || deleteOptionMutation.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
