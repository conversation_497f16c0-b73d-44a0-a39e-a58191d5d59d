import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  slug: string;
  image?: string;
  description?: string;
  parent_id?: string | null;
}

interface CategoryPreviewProps {
  withWaveDivider?: boolean;
}

const CategoryPreview = ({ withWaveDivider = false }: CategoryPreviewProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        // Only fetch main categories (those without a parent_id)
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .is('parent_id', null)
          .order('display_order', { ascending: true });
        
        if (error) {
          throw error;
        }
        
        if (data) {
          setCategories(data);
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        setError(err.message || 'Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);
  if (isLoading) {
    return (
      <section className="py-8 bg-sage-50">
        {withWaveDivider && (
          <div className="-mt-16 drop-shadow-lg">
            <svg viewBox="0 0 1440 120" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-24 md:h-32 lg:h-40">
              <defs>
                <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#000000" stopOpacity="0"/> {/* Transparent at top */}
                  <stop offset="100%" stopColor="#FFFFFF" stopOpacity="1"/> {/* White at bottom */}
                </linearGradient>
              </defs>
              <path fill="url(#waveGradient)" d="M0,120 C360,30 1080,210 1440,120 L1440,0 L0,0 Z" />
            </svg>
          </div>
        )}
        <div className="container-custom text-center">
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>Loading categories...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-8 bg-sage-50">
        {withWaveDivider && (
          <div className="-mt-16 drop-shadow-lg">
            <svg viewBox="0 0 1440 120" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-24 md:h-32 lg:h-40">
              <defs>
                <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#000000" stopOpacity="0"/> {/* Transparent at top */}
                  <stop offset="100%" stopColor="#FFFFFF" stopOpacity="1"/> {/* White at bottom */}
                </linearGradient>
              </defs>
              <path fill="url(#waveGradient)" d="M0,120 C360,30 1080,210 1440,120 L1440,0 L0,0 Z" />
            </svg>
          </div>
        )}
        <div className="container-custom text-center">
          <p className="text-red-500">Failed to load categories</p>
        </div>
      </section>
    );
  }

  if (categories.length === 0) {
    return (
      <section className="py-8 bg-sage-50">
        {withWaveDivider && (
          <div className="-mt-16 drop-shadow-lg">
            <svg viewBox="0 0 1440 120" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-24 md:h-32 lg:h-40">
              <defs>
                <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#000000" stopOpacity="0"/> {/* Transparent at top */}
                  <stop offset="100%" stopColor="#FFFFFF" stopOpacity="1"/> {/* White at bottom */}
                </linearGradient>
              </defs>
              <path fill="url(#waveGradient)" d="M0,120 C360,30 1080,210 1440,120 L1440,0 L0,0 Z" />
            </svg>
          </div>
        )}
        <div className="container-custom text-center">
          <p>No categories found</p>
        </div>
      </section>
    );
  }

  // Function to scroll to top when clicking on a category (instant scroll)
  const handleCategoryClick = () => {
    window.scrollTo(0, 0);
  };
  
  return (
    <section className="py-8 bg-sage-50">
      {withWaveDivider && (
        <div className="-mt-24 relative overflow-hidden z-10">
          <svg viewBox="0 0 1440 60" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-12 md:h-16 relative z-10">
            <path 
              fill="rgba(247, 249, 243, 0.85)" 
              d="M0,0 L1440,0 L1440,60 C1080,40 720,30 360,40 C240,45 120,50 0,60 L0,0 Z" 
              className="transition-all duration-700 ease-in-out backdrop-blur-[2px]"
            />
          </svg>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-sage-50/70 mix-blend-overlay backdrop-blur-[1px] z-0"></div>
        </div>
      )}
      <div className="container-custom pt-8">
        <h2 className="section-heading text-center">Shop By Category</h2>
        <p className="text-clay-700 text-center mb-10 max-w-2xl mx-auto">
          Browse our collection of premium products across a variety of categories
        </p>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <Link 
              key={category.id} 
              to={`/shop?category=${category.slug}`}
              className="group block rounded-lg overflow-hidden shadow-md bg-white transition-all duration-300 hover:shadow-xl"
              onClick={handleCategoryClick}
            >
              <div className="relative">
                <AspectRatio ratio={1/1}>
                  {category.image ? (
                    <img 
                      src={category.image} 
                      alt={category.name}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-500">{category.name}</span>
                    </div>
                  )}
                </AspectRatio>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                  <div className="p-4 text-white">
                    <h3 className="text-xl font-semibold">{category.name}</h3>
                    <p className="text-sm text-white/80 mt-1 line-clamp-2">
                      {category.description || `Shop our ${category.name} collection`}
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategoryPreview;
