/**
 * TikTok Adapter - Viral Content AI
 *
 * "Welcome to the viral matrix!" 🎵
 *
 * Specialized AI for TikTok's algorithm-driven platform
 * Optimized for viral hooks, trending sounds, and maximum FYP potential
 */

import { socialMediaAI } from '../SocialMediaAI';

export interface TikTokContent {
  video_concept: string;
  hook: string;
  script: string[];
  trending_sounds: string[];
  hashtags: string[];
  effects_suggestions: string[];
  viral_elements: {
    trend_type: 'dance' | 'comedy' | 'educational' | 'transformation' | 'storytelling';
    hook_timing: number; // seconds to grab attention
    engagement_triggers: string[];
  };
  video_structure: {
    intro: string;
    main_content: string[];
    call_to_action: string;
    outro: string;
  };
}

export interface TikTokCampaign {
  primary_video: TikTokContent;
  follow_up_videos: TikTokContent[];
  trend_variations: TikTokContent[];
  hashtag_challenges: string[];
  viral_strategy: {
    posting_schedule: string[];
    engagement_tactics: string[];
    trend_adaptation: string[];
  };
}

export class TikTokAdapter {
  private static instance: TikTokAdapter;

  // TikTok viral optimization data
  private tiktokTrends = {
    viral_hooks: [
      'POV:', 'Tell me you... without telling me', 'This or that?', 'Plot twist:',
      'When you realize...', 'Nobody talks about...', 'Things that just hit different',
      'Red flag or green flag?', 'Unpopular opinion:', 'Day in my life as...'
    ],
    trending_formats: [
      'before_after', 'day_in_life', 'educational', 'comedy_skit', 'product_review',
      'behind_scenes', 'transformation', 'storytelling', 'trend_participation'
    ],
    engagement_boosters: [
      'duet_bait', 'comment_bait', 'stitch_worthy', 'trend_adaptation',
      'controversial_take', 'relatable_content', 'educational_value'
    ],
    optimal_times: ['06:00', '10:00', '19:00', '21:00'],
    video_length_sweet_spots: [15, 30, 60] // seconds
  };

  private constructor() {}

  static getInstance(): TikTokAdapter {
    if (!TikTokAdapter.instance) {
      TikTokAdapter.instance = new TikTokAdapter();
    }
    return TikTokAdapter.instance;
  }

  /**
   * Generate viral TikTok campaign for a product
   */
  async generateTikTokCampaign(productData: {
    name: string;
    category: string;
    description?: string;
    price?: number;
    features?: string[];
  }): Promise<TikTokCampaign> {

    try {
      console.log('🎵 Starting TikTok campaign generation for:', productData.name);

      // Generate only primary viral video to avoid infinite loops
      const primaryVideo = await this.generateViralVideo(productData, 'primary');

      // Generate simple follow-up videos without AI calls
      const followUpVideos = this.generateSimpleFollowUps(productData);

      // Generate simple trend variations without AI calls
      const trendVariations = this.generateSimpleTrendVariations(productData);

      // Create hashtag challenges
      const hashtagChallenges = this.createHashtagChallenges(productData);

      // Build viral strategy
      const viralStrategy = this.buildViralStrategy(productData);

      console.log('✅ TikTok campaign generation completed');

      return {
        primary_video: primaryVideo,
        follow_up_videos: followUpVideos,
        trend_variations: trendVariations,
        hashtag_challenges: hashtagChallenges,
        viral_strategy: viralStrategy
      };

    } catch (error) {
      console.error('TikTok campaign generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate viral TikTok video content
   */
  async generateViralVideo(
    productData: any,
    type: 'primary' | 'follow_up' | 'trend' = 'primary'
  ): Promise<TikTokContent> {

    // Use AI to generate the hook and script
    const aiContent = await this.generateAIContent(productData, type);
    const hashtags = await this.generateTikTokHashtags(productData.category);

    // Get product URL
    const productUrl = productData.product_url || productData.shop_link;

    // Ensure script includes product URL
    let script = [...aiContent.script];
    let includesLink = false;

    // Check if any part of the script already includes a link
    for (const line of script) {
      if (line.includes('http') || line.includes('shop now') || line.includes('buy now')) {
        includesLink = true;
        break;
      }
    }

    // Add product link if not already included
    if (productUrl && !includesLink) {
      script.push(`🛒 Shop now: ${productUrl}`);
    }

    // Create a call to action that includes the product URL
    const callToAction = productUrl
      ? `Shop ${productData.name} now! Link in bio: ${productUrl}`
      : this.generateTikTokCTA(productData);

    return {
      video_concept: this.generateVideoConcept(productData, type),
      hook: aiContent.hook,
      script: script,
      trending_sounds: this.getTrendingSounds(productData.category),
      hashtags,
      effects_suggestions: this.getEffectsSuggestions(productData.category),
      viral_elements: {
        trend_type: this.selectTrendType(productData),
        hook_timing: 3, // First 3 seconds are crucial
        engagement_triggers: this.getEngagementTriggers(productData)
      },
      video_structure: {
        intro: aiContent.hook,
        main_content: script,
        call_to_action: callToAction,
        outro: 'Follow for more premium finds! 🔥'
      },
      product_url: productUrl // Explicitly include product URL
    };
  }

  /**
   * Generate simple follow-up videos (no AI calls)
   */
  private generateSimpleFollowUps(productData: any): TikTokContent[] {
    const hashtags = ['#FYP', '#Viral', `#${productData.category}`, '#Quality'];

    return [
      {
        video_concept: `Behind the scenes: ${productData.name} quality process`,
        hook: `Behind the scenes of ${productData.name} 👀`,
        script: [
          `Behind the scenes of ${productData.name} 👀`,
          'Here\'s how we ensure quality...',
          'Every detail matters 💎',
          'This is why it\'s different ✨'
        ],
        trending_sounds: this.getTrendingSounds(productData.category),
        hashtags,
        effects_suggestions: this.getEffectsSuggestions(productData.category),
        viral_elements: {
          trend_type: 'educational',
          hook_timing: 3,
          engagement_triggers: ['Ask about quality process']
        },
        video_structure: {
          intro: `Behind the scenes of ${productData.name} 👀`,
          main_content: ['Quality process', 'Attention to detail'],
          call_to_action: 'Comment your questions! 💬',
          outro: 'Follow for more behind the scenes! 🔥'
        }
      }
    ];
  }

  /**
   * Generate simple trend variations (no AI calls)
   */
  private generateSimpleTrendVariations(productData: any): TikTokContent[] {
    const hashtags = ['#FYP', '#Trending', `#${productData.category}`, '#ThisOrThat'];

    return [
      {
        video_concept: `This or that: ${productData.name} vs alternatives`,
        hook: `This or that: ${productData.name} edition 🤔`,
        script: [
          `This or that: ${productData.name} edition 🤔`,
          'Quality vs price? Both! ✅',
          'Style vs function? Why not both! 💎',
          'The clear winner is... 🏆'
        ],
        trending_sounds: this.getTrendingSounds(productData.category),
        hashtags,
        effects_suggestions: this.getEffectsSuggestions(productData.category),
        viral_elements: {
          trend_type: 'comedy',
          hook_timing: 3,
          engagement_triggers: ['Ask viewers to choose']
        },
        video_structure: {
          intro: `This or that: ${productData.name} edition 🤔`,
          main_content: ['Comparison points', 'Clear winner'],
          call_to_action: 'What would you choose? 👇',
          outro: 'Follow for more comparisons! 🔥'
        }
      }
    ];
  }

  /**
   * Generate AI-powered TikTok content
   */
  private async generateAIContent(productData: any, type: string): Promise<{ hook: string; script: string[] }> {
    try {
      console.log('🎵 Generating AI TikTok content for:', productData.name);

      // Use the social media AI to generate platform content
      const platformContent = await socialMediaAI.generatePlatformContent(
        productData,
        'tiktok',
        'product_showcase'
      );

      if (platformContent && platformContent.content && platformContent.content.caption) {
        console.log('✅ AI-generated TikTok content received');

        // Parse the AI content into hook and script
        const content = platformContent.content.caption;
        const lines = content.split('\n').filter(line => line.trim());

        return {
          hook: lines[0] || this.generateViralHook(productData),
          script: lines.slice(1) || this.generateVideoScript(productData, lines[0])
        };
      }

      // Fallback to template if AI fails
      console.warn('⚠️ AI TikTok generation returned empty, using fallback');
      const fallbackHook = this.generateViralHook(productData);
      return {
        hook: fallbackHook,
        script: this.generateVideoScript(productData, fallbackHook)
      };

    } catch (error) {
      console.error('❌ AI TikTok generation failed, using fallback:', error);
      const fallbackHook = this.generateViralHook(productData);
      return {
        hook: fallbackHook,
        script: this.generateVideoScript(productData, fallbackHook)
      };
    }
  }

  /**
   * Generate viral hook for TikTok (fallback)
   */
  private generateViralHook(productData: any): string {
    const hooks = [
      `POV: You found the perfect ${productData.category} 😍`,
      `Tell me you love quality ${productData.category} without telling me...`,
      `This ${productData.name} hits different 🔥`,
      `When you realize ${productData.name} is exactly what you needed:`,
      `Plot twist: ${productData.name} exceeded all expectations`,
      `Nobody talks about how good ${productData.category} can be until...`,
      `Things that just hit different: ${productData.name} edition`,
      `Red flag or green flag? ${productData.name} quality ✅`
    ];

    return hooks[Math.floor(Math.random() * hooks.length)];
  }

  /**
   * Generate TikTok-optimized hashtags
   */
  async generateTikTokHashtags(category: string): Promise<string[]> {
    const viralHashtags = [
      '#FYP', '#Viral', '#Trending', '#ForYou', '#TikTokMadeMeBuyIt',
      '#ProductReview', '#Quality', '#MustHave', '#GameChanger'
    ];

    const categoryHashtags = {
      cbd: [
        '#CBD', '#CBDOil', '#Wellness', '#NaturalHealing', '#Hemp',
        '#CBDLife', '#PlantMedicine', '#HolisticHealth', '#CBDReview'
      ],
      seeds: [
        '#CannabisSeeds', '#GrowYourOwn', '#Seeds', '#Growing',
        '#Cultivation', '#GrowLife', '#Homegrown', '#GrowTips'
      ],
      accessories: [
        '#SmokeShop', '#GlassArt', '#Accessories', '#Quality',
        '#SmokeGear', '#GlassPipe', '#VapeLife', '#420Accessories'
      ]
    };

    const ukSpecific = ['#UK', '#Scotland', '#Glasgow', '#BitsNBongs'];

    const categoryTags = categoryHashtags[category.toLowerCase() as keyof typeof categoryHashtags] || categoryHashtags.cbd;

    return [...viralHashtags, ...categoryTags.slice(0, 10), ...ukSpecific].slice(0, 20);
  }

  /**
   * Generate video script
   */
  private generateVideoScript(productData: any, hook: string): string[] {
    return [
      hook,
      `Let me show you why ${productData.name} is different...`,
      `First, the quality is unmatched 💎`,
      `Second, it's lab tested and verified ✅`,
      `Third, the customer service is incredible 🙌`,
      `But here's what really sets it apart...`,
      `The attention to detail is *chef's kiss* 👌`,
      `And the results? Absolutely worth it!`,
      `Who else loves premium ${productData.category}? 🙋‍♀️`
    ];
  }

  /**
   * Generate video concept
   */
  private generateVideoConcept(productData: any, type: string): string {
    const concepts = {
      primary: `Product showcase of ${productData.name} with viral hook and quality highlights`,
      follow_up: `Behind-the-scenes look at ${productData.name} quality process`,
      trend: `Trending format adaptation featuring ${productData.name}`
    };

    return concepts[type as keyof typeof concepts] || concepts.primary;
  }

  /**
   * Get trending sounds for category
   */
  private getTrendingSounds(category: string): string[] {
    return [
      'Original Audio - Trending Beat 1',
      'Viral Sound - Chill Vibes',
      'Popular Audio - Upbeat Track',
      'Trending Music - Lo-Fi Hip Hop',
      'Viral Beat - Electronic Mix'
    ];
  }

  /**
   * Get effects suggestions
   */
  private getEffectsSuggestions(category: string): string[] {
    const effects = {
      cbd: ['Green Screen', 'Glow Effect', 'Nature Filter', 'Calm Vibes'],
      seeds: ['Growth Time-lapse', 'Nature Filter', 'Green Effect', 'Plant Overlay'],
      accessories: ['Sparkle Effect', 'Glow Filter', 'Artistic Filter', 'Color Pop'],
      vaporizers: ['Tech Effect', 'Neon Glow', 'Futuristic Filter', 'Smoke Effect'],
      bongs: ['Water Effect', 'Glass Reflection', 'Artistic Filter', 'Color Shift']
    };

    return effects[category.toLowerCase() as keyof typeof effects] || effects.cbd;
  }

  /**
   * Select trend type for product
   */
  private selectTrendType(productData: any): 'dance' | 'comedy' | 'educational' | 'transformation' | 'storytelling' {
    const trendTypes = {
      cbd: 'educational',
      seeds: 'transformation',
      accessories: 'storytelling',
      vaporizers: 'educational',
      bongs: 'storytelling'
    };

    return trendTypes[productData.category.toLowerCase() as keyof typeof trendTypes] || 'educational';
  }

  /**
   * Get engagement triggers
   */
  private getEngagementTriggers(productData: any): string[] {
    return [
      'Ask viewers to comment their favorite feature',
      'Create duet-worthy moment for responses',
      'Include controversial but safe opinion',
      'Ask for product suggestions in comments',
      'Create "this or that" comparison moment',
      'Include relatable struggle/solution',
      'Ask viewers to share their experiences'
    ];
  }

  /**
   * Generate TikTok call-to-action
   */
  private generateTikTokCTA(productData: any): string {
    const ctas = [
      'Link in bio for this quality! 🔗',
      'Who else needs this? Tag them! 👇',
      'Comment "NEED" if you want this! 💬',
      'Save this for later! You\'ll thank me 📌',
      'Follow for more premium finds! ✨',
      'Duet this with your reaction! 🎭'
    ];

    return ctas[Math.floor(Math.random() * ctas.length)];
  }

  /**
   * Create hashtag challenges
   */
  private createHashtagChallenges(productData: any): string[] {
    return [
      `#${productData.name.replace(/\s+/g, '')}Challenge`,
      `#Quality${productData.category}Check`,
      `#Premium${productData.category}Review`,
      `#BitsNBongs${productData.category}`,
      `#${productData.category}QualityTest`
    ];
  }

  /**
   * Build viral strategy
   */
  private buildViralStrategy(productData: any): any {
    return {
      posting_schedule: [
        'Post primary video at peak time (19:00)',
        'Follow up with behind-scenes (next day 10:00)',
        'Educational content (day 3, 21:00)',
        'Trend participation (day 5, 19:00)',
        'Customer reactions (day 7, 20:00)'
      ],
      engagement_tactics: [
        'Respond to all comments within 2 hours',
        'Create duets with customer videos',
        'Participate in trending sounds',
        'Cross-promote on other platforms',
        'Collaborate with micro-influencers'
      ],
      trend_adaptation: [
        'Monitor trending hashtags daily',
        'Adapt current trends to product',
        'Create original trend variations',
        'Participate in viral challenges',
        'Use trending sounds strategically'
      ]
    };
  }
}

// Export singleton instance
export const tiktokAdapter = TikTokAdapter.getInstance();
