import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { shippingService, ShippingZone, ShippingMethod, ShippingCalculation } from '@/services/shippingService';

export function useShippingZones() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: zones = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['shipping-zones'],
    queryFn: () => shippingService.getShippingZones(),
  });

  const createZoneMutation = useMutation({
    mutationFn: (zone: Omit<ShippingZone, 'id' | 'created_at' | 'updated_at'>) =>
      shippingService.createShippingZone(zone),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-zones'] });
      toast({
        title: 'Success',
        description: 'Shipping zone created successfully',
      });
    },
    onError: (error) => {
      console.error('Error creating zone:', error);
      toast({
        title: 'Error',
        description: 'Failed to create shipping zone',
        variant: 'destructive',
      });
    },
  });

  const updateZoneMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<ShippingZone> }) =>
      shippingService.updateShippingZone(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-zones'] });
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      toast({
        title: 'Success',
        description: 'Shipping zone updated successfully',
      });
    },
    onError: (error) => {
      console.error('Error updating zone:', error);
      toast({
        title: 'Error',
        description: 'Failed to update shipping zone',
        variant: 'destructive',
      });
    },
  });

  const deleteZoneMutation = useMutation({
    mutationFn: (id: string) => shippingService.deleteShippingZone(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-zones'] });
      queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
      toast({
        title: 'Success',
        description: 'Shipping zone deleted successfully',
      });
    },
    onError: (error) => {
      console.error('Error deleting zone:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete shipping zone',
        variant: 'destructive',
      });
    },
  });

  return {
    zones,
    isLoading,
    error,
    refetch,
    createZone: createZoneMutation.mutate,
    updateZone: updateZoneMutation.mutate,
    deleteZone: deleteZoneMutation.mutate,
    isCreating: createZoneMutation.isPending,
    isUpdating: updateZoneMutation.isPending,
    isDeleting: deleteZoneMutation.isPending,
  };
}

export function useShippingMethods() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: methods = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['shipping-methods'],
    queryFn: () => shippingService.getShippingMethods(),
  });

  const createMethodMutation = useMutation({
    mutationFn: (method: Omit<ShippingMethod, 'id' | 'created_at' | 'updated_at' | 'zone'>) =>
      shippingService.createShippingMethod(method),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      toast({
        title: 'Success',
        description: 'Shipping method created successfully',
      });
    },
    onError: (error) => {
      console.error('Error creating method:', error);
      toast({
        title: 'Error',
        description: 'Failed to create shipping method',
        variant: 'destructive',
      });
    },
  });

  const updateMethodMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<ShippingMethod> }) =>
      shippingService.updateShippingMethod(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      toast({
        title: 'Success',
        description: 'Shipping method updated successfully',
      });
    },
    onError: (error) => {
      console.error('Error updating method:', error);
      toast({
        title: 'Error',
        description: 'Failed to update shipping method',
        variant: 'destructive',
      });
    },
  });

  const deleteMethodMutation = useMutation({
    mutationFn: (id: string) => shippingService.deleteShippingMethod(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      toast({
        title: 'Success',
        description: 'Shipping method deleted successfully',
      });
    },
    onError: (error) => {
      console.error('Error deleting method:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete shipping method',
        variant: 'destructive',
      });
    },
  });

  return {
    methods,
    isLoading,
    error,
    refetch,
    createMethod: createMethodMutation.mutate,
    updateMethod: updateMethodMutation.mutate,
    deleteMethod: deleteMethodMutation.mutate,
    isCreating: createMethodMutation.isPending,
    isUpdating: updateMethodMutation.isPending,
    isDeleting: deleteMethodMutation.isPending,
  };
}

export function useShippingCalculation(country: string, cartTotal: number) {
  const [calculations, setCalculations] = useState<ShippingCalculation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!country) return;

    const calculateShipping = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const results = await shippingService.calculateShipping(country, cartTotal);
        setCalculations(results);
      } catch (err) {
        console.error('Error calculating shipping:', err);
        setError(err instanceof Error ? err : new Error('Failed to calculate shipping'));
      } finally {
        setIsLoading(false);
      }
    };

    calculateShipping();
  }, [country, cartTotal]);

  return {
    calculations,
    isLoading,
    error,
  };
}

export function useCheckoutShipping(country: string = 'United Kingdom') {
  const queryClient = useQueryClient();

  const {
    data: shippingMethods = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['checkout-shipping', country],
    queryFn: async () => {
      console.log('Fetching shipping methods for checkout');
      const methods = await shippingService.getCheckoutShippingMethods(country);
      console.log('Received shipping methods:', methods);
      // Extra safety filter
      return methods.filter(method => method && method.id && method.is_active !== false);
    },
    enabled: !!country,
    staleTime: 30000, // Cache for 30 seconds
    gcTime: 60000, // Keep unused data for 1 minute
    refetchOnMount: false, // Don't refetch on component mount
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
  });

  const refreshShippingMethods = () => {
    console.log('Manually refreshing shipping methods');
    // Force complete cache removal for shipping
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    refetch();
  };

  return {
    shippingMethods,
    isLoading,
    error,
    refreshShippingMethods,
  };
}

export default {
  useShippingZones,
  useShippingMethods,
  useShippingCalculation,
  useCheckoutShipping,
};
