#!/usr/bin/env tsx
/**
 * Import script for Super Agent enriched seed data
 * This script imports the professionally enriched seed data into our filtering system
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import <PERSON> from 'papa<PERSON><PERSON>';
import { join } from 'path';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials. Please check your .env file.');
  console.error('Expected: VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface EnrichedSeedData {
  product_name: string;
  seed_type: string;
  flowering_time: string;
  yield_indoor: string;
  yield_outdoor: string;
  thc_level: string;
  cbd_level: string;
  effect: string;
  seed_family: string;
  height: string;
  flavor_profile: string;
  special_notes: string;
}

async function findProductByName(productName: string) {
  // Try exact match first
  let { data: products } = await supabase
    .from('products')
    .select('id, name')
    .ilike('name', productName)
    .limit(1);

  if (products && products.length > 0) {
    return products[0];
  }

  // Try fuzzy matching by removing common variations
  const cleanName = productName
    .replace(/420 Fast ?Buds:?\s*/i, '')
    .replace(/\(Feminised?\s*x?\d*\)/i, '')
    .replace(/\(\d+\s*Feminised?\)/i, '')
    .replace(/\(Auto\)/i, '')
    .replace(/Auto\s*/i, '')
    .trim();

  ({ data: products } = await supabase
    .from('products')
    .select('id, name')
    .ilike('name', `%${cleanName}%`)
    .limit(5));

  if (products && products.length > 0) {
    console.log(`🔍 Fuzzy match for "${productName}":`);
    products.forEach((p, i) => console.log(`   ${i + 1}. ${p.name}`));
    return products[0]; // Return best match
  }

  return null;
}

async function getOrCreateFilterOption(categoryName: string, optionName: string, displayName?: string) {
  // Get the category
  const { data: categories } = await supabase
    .from('filter_categories')
    .select('id')
    .eq('name', categoryName)
    .single();

  if (!categories) {
    console.error(`❌ Filter category '${categoryName}' not found`);
    return null;
  }

  // Check if option exists
  let { data: options } = await supabase
    .from('filter_options')
    .select('id')
    .eq('category_id', categories.id)
    .eq('name', optionName)
    .single();

  if (options) {
    return options.id;
  }

  // Create new option
  const { data: newOption, error } = await supabase
    .from('filter_options')
    .insert({
      category_id: categories.id,
      name: optionName,
      display_name: displayName || optionName,
      display_order: 999 // Will be sorted later
    })
    .select('id')
    .single();

  if (error) {
    console.error(`❌ Error creating filter option '${optionName}':`, error);
    return null;
  }

  console.log(`✅ Created new filter option: ${displayName || optionName}`);
  return newOption.id;
}

async function importEnrichedData() {
  console.log('🌱 Importing Super Agent enriched seed data...\n');

  try {
    // Read the CSV file
    const csvPath = join(process.cwd(), 'docs', 'super_agent', 'enriched_seed_data.csv');
    const csvContent = readFileSync(csvPath, 'utf8');

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvContent, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.trim()
    });

    if (parseResult.errors.length > 0) {
      console.error('❌ CSV parsing errors:', parseResult.errors);
      process.exit(1);
    }

    const records: EnrichedSeedData[] = parseResult.data as EnrichedSeedData[];

    console.log(`📄 Found ${records.length} enriched products to import\n`);

    let imported = 0;
    let skipped = 0;
    let errors = 0;

    for (const record of records) {
      console.log(`🔍 Processing: ${record.product_name}`);

      try {
        // Find matching product
        const product = await findProductByName(record.product_name);

        if (!product) {
          console.log(`   ⚠️  No matching product found - skipping`);
          skipped++;
          continue;
        }

        console.log(`   ✅ Matched to: ${product.name}`);

        // Check if seed attributes already exist
        const { data: existingAttr } = await supabase
          .from('seed_product_attributes')
          .select('id')
          .eq('product_id', product.id)
          .single();

        // Use only basic columns that definitely exist
        const attributeData = {
          product_id: product.id,
          seed_type: record.seed_type,
          flowering_time: record.flowering_time,
          thc_level: record.thc_level,
          cbd_level: record.cbd_level,
          effect: record.effect
        };

        let attrError;
        if (existingAttr) {
          // Update existing
          const { error } = await supabase
            .from('seed_product_attributes')
            .update(attributeData)
            .eq('product_id', product.id);
          attrError = error;
        } else {
          // Insert new
          const { error } = await supabase
            .from('seed_product_attributes')
            .insert(attributeData);
          attrError = error;
        }

        if (attrError) {
          console.error(`   ❌ Error updating attributes:`, attrError);
          errors++;
          continue;
        }

        // Create filter associations
        const filterMappings = [
          { category: 'seed_type', value: 'autoflower', display: 'Autoflower' },
          { category: 'effect', value: record.effect.toLowerCase().replace(/\s+/g, '_'), display: record.effect },
          { category: 'thc_level', value: record.thc_level.replace('%', '').split('-')[0] + '%+', display: record.thc_level }
        ];

        for (const mapping of filterMappings) {
          const optionId = await getOrCreateFilterOption(mapping.category, mapping.value, mapping.display);

          if (optionId) {
            // Check if link already exists
            const { data: existingLink } = await supabase
              .from('product_filters')
              .select('*')
              .eq('product_id', product.id)
              .eq('filter_option_id', optionId)
              .single();

            if (!existingLink) {
              // Create new link
              await supabase
                .from('product_filters')
                .insert({
                  product_id: product.id,
                  filter_option_id: optionId
                });
            }
          }
        }

        console.log(`   ✅ Successfully imported enriched data`);
        imported++;

      } catch (err) {
        console.error(`   ❌ Error processing ${record.product_name}:`, err);
        errors++;
      }

      console.log(''); // Empty line for readability
    }

    // Summary
    console.log('📊 Import Summary:');
    console.log(`   ✅ Successfully imported: ${imported}`);
    console.log(`   ⚠️  Skipped (no match): ${skipped}`);
    console.log(`   ❌ Errors: ${errors}`);
    console.log(`   📄 Total processed: ${records.length}`);

    if (imported > 0) {
      console.log('\n🎉 Import completed! You can now test the filtering system.');
      console.log('💡 Next steps:');
      console.log('   1. Check the admin panel to see enriched products');
      console.log('   2. Test the seed filtering on the frontend');
      console.log('   3. Provide feedback to Super Agent for next batch');
    }

  } catch (err) {
    console.error('❌ Import failed:', err);
    process.exit(1);
  }
}

// Run the import
importEnrichedData().catch(console.error);
