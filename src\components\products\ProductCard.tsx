import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ShoppingCart, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/hooks/useCart';
import { Product as BaseProduct } from '@/types/database';
import WishlistButton from './WishlistButton';
import { formatPrice, ensureNumericPrice } from '@/lib/utils';

// Extend the base Product type to include brand information
interface Product extends BaseProduct {
  brands?: {
    id: string;
    name: string;
    slug: string;
  };
}

interface ProductCardProps {
  product: Product;
  showAddToCart?: boolean;
  showSaveForLater?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  showAddToCart = true,
  showSaveForLater = true,
}) => {
  const { addToCart } = useCart();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Make sure we're passing a product ID as string, not the whole product object
    const productId = typeof product === 'string' ? product : product.id;
    addToCart(productId, 1);
  };

  const [imageUrl, setImageUrl] = useState<string>('/placeholder-product.jpg');
  const [imageLoading, setImageLoading] = useState<boolean>(true);
  const [imageError, setImageError] = useState<boolean>(false);

  // Function to get image URL from Supabase storage or use a placeholder
  useEffect(() => {
    // Don't process if product is not ready
    if (!product) {
      return;
    }

    // Small delay to ensure component is fully mounted and data is stable
    const timeoutId = setTimeout(() => {
      // Start with loading state
      setImageLoading(true);
      setImageError(false);

    // Default to placeholder
    let finalImageUrl = '/placeholder-product.jpg';

    if (!product.image) {
      console.log('No image for product:', product.name);
      setImageUrl(finalImageUrl);
      setImageLoading(false);
      return;
    }

    try {
      // For data URLs, use them directly
      if (product.image.startsWith('data:')) {
        console.log('Using data URL from LocalImageUploader');
        finalImageUrl = product.image;
        setImageUrl(finalImageUrl);
        setImageLoading(false);
        return;
      }

      // Determine the URL to try
      let tryUrl = product.image;

      // If it's not a full URL, construct one
      if (!tryUrl.startsWith('http')) {
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';

        if (tryUrl.startsWith('/storage/')) {
          tryUrl = `${supabaseUrl}${tryUrl}`;
        } else if (tryUrl.startsWith('product-images/')) {
          tryUrl = `${supabaseUrl}/storage/v1/object/public/${tryUrl}`;
        } else {
          tryUrl = `${supabaseUrl}/storage/v1/object/public/product-images/${tryUrl}`;
        }
      }

      // console.log('Loading image for product:', product.name);
      // console.log('Image URL:', tryUrl);

      // Set the image URL immediately to attempt loading
      // The onLoad and onError handlers in the JSX will handle the loading states
      setImageUrl(tryUrl);

      // Pre-cache the image to ensure it's available for future renders
      const preloadImage = new Image();
      preloadImage.src = tryUrl;

    } catch (error) {
      console.error('Error in image processing:', error);
      setImageError(true);
      setImageLoading(false);
      setImageUrl('/placeholder-product.jpg');
    }
    }, 50); // Small delay to ensure stability

    return () => clearTimeout(timeoutId);
  }, [product?.image, product?.name]); // Include product.name to ensure re-run when product changes

  // Add a click handler to debug product navigation
  const handleProductClick = (e: React.MouseEvent) => {
    // Don't prevent default - we still want normal navigation
    // Use product ID as fallback if slug is empty
    const productPath = product.slug ? product.slug : product.id;
    console.log('ProductCard clicked for product:', {
      id: product.id,
      name: product.name,
      slug: product.slug,
      productPath: productPath,
      url: `/shop/${productPath}`
    });
  };

  return (
    <Link
      to={`/shop/${product.slug || product.id}`}
      className="group"
      onClick={handleProductClick}
      data-testid={`product-card-${product.id}`}
    >
      <div className="relative overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
        {/* Product badges */}
        <div className="absolute top-2 left-2 z-10 flex flex-col gap-1">
          {product.is_new && (
            <Badge variant="secondary" className="bg-sage-500 text-white">New</Badge>
          )}
          {product.is_best_seller && (
            <Badge variant="secondary" className="bg-amber-500 text-white">Best Seller</Badge>
          )}
          {product.sale_price && product.sale_price > 0 && (
            <Badge variant="secondary" className="bg-red-500 text-white">Sale</Badge>
          )}
          {!product.in_stock && (
            <Badge variant="secondary" className="bg-gray-500 text-white">Out of Stock</Badge>
          )}
        </div>

        {/* Wishlist button */}
        {showSaveForLater && (
          <div className="absolute top-2 right-2 z-10">
            <WishlistButton productId={product.id} />
          </div>
        )}

        {/* Product image */}
        <div className="aspect-square overflow-hidden relative">
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <Loader2 className="h-8 w-8 animate-spin text-sage-500" />
            </div>
          )}
          <img
            src={imageUrl}
            alt={product.name}
            className={`h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-105 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}
            onLoad={() => setImageLoading(false)}
            onError={(e) => {
              // console.error('Image failed to load for product:', product.name);
              // console.error('Failed image URL:', imageUrl);
              setImageError(true);
              setImageLoading(false);
              setImageUrl('/placeholder-product.jpg');
            }}
          />
        </div>

        {/* Product info */}
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2">{product.name}</h3>

          {/* Brand name - only show if available */}
          {product.brands && (
            <div
              className="mt-1 text-xs text-gray-500 hover:text-sage-600 transition-colors duration-200"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Navigate to brand page
                window.location.href = `/shop?brand=${product.brands?.slug || product.brand_id}`;
              }}
            >
              <span className="cursor-pointer">By {product.brands.name}</span>
            </div>
          )}

          <div className="mt-2 flex justify-between items-center">
            <div>
              {product.sale_price !== null && product.sale_price !== undefined && product.sale_price > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-lg font-bold text-red-600">{formatPrice(product.sale_price)}</span>
                  <span className="text-sm text-gray-500 line-through">{formatPrice(product.price)}</span>
                </div>
              ) : (
                <span className="text-lg font-bold text-gray-900">{formatPrice(product.price)}</span>
              )}
            </div>

            {/* Add to cart button */}
            {showAddToCart && product.in_stock && (
              <Button
                size="icon"
                variant="ghost"
                onClick={handleAddToCart}
                className="h-8 w-8 rounded-full hover:bg-sage-100 hover:text-sage-700"
                aria-label="Add to cart"
              >
                <ShoppingCart className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;
