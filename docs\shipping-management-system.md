# Shipping Management System

## Overview

The Shipping Management System replaces hardcoded shipping rates in your checkout with a flexible, database-driven solution. This allows you to easily manage shipping zones, methods, and rates through your admin interface.

## Key Features

### 🌍 **Shipping Zones**
- Define geographical regions (UK, EU, Rest of World, etc.)
- Assign multiple countries to each zone
- Enable/disable zones as needed
- Manage zone-specific shipping methods

### 🚚 **Shipping Methods**
- Create multiple shipping options per zone
- Set prices, delivery times, and descriptions
- Configure free shipping thresholds
- Choose appropriate icons (standard, express, next day, free)
- Control method ordering and availability

### 💰 **Dynamic Pricing**
- Base shipping rates per method
- Free shipping thresholds (e.g., free over £50)
- Automatic calculation based on customer location
- Support for promotional shipping rates

### 🎯 **Smart Calculation**
- Automatic zone detection based on customer country
- Real-time shipping cost calculation
- Fallback to default rates if no zone matches
- Integration with existing checkout flow

## Database Structure

### Shipping Zones Table
```sql
shipping_zones (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  countries JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Shipping Methods Table
```sql
shipping_methods (
  id UUID PRIMARY KEY,
  zone_id UUID REFERENCES shipping_zones(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  free_shipping_threshold DECIMAL(10,2),
  estimated_days_min INTEGER NOT NULL,
  estimated_days_max INTEGER NOT NULL,
  icon VARCHAR(50) DEFAULT 'standard',
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

## Setup Instructions

### 1. Database Setup

Run the database migration:
```bash
cd src/scripts
npx ts-node setup-shipping-tables.ts
```

Or manually execute the SQL:
```bash
psql -d your_database -f create-shipping-tables.sql
```

### 2. Add to Admin Interface

Add the ShippingManager component to your admin area:

```tsx
import ShippingManager from '@/components/admin/ShippingManager';

// In your admin routes or dashboard
<ShippingManager />
```

### 3. Update Checkout Integration

Replace hardcoded shipping methods in your checkout:

```tsx
// Before (hardcoded)
const SHIPPING_METHODS = [
  { id: 'standard', name: 'Standard', price: 5.99 }
];

// After (dynamic)
import { useCheckoutShipping } from '@/hooks/useShipping';

function CheckoutPage() {
  const { shippingMethods } = useCheckoutShipping(customerCountry);
  // Use shippingMethods in your checkout
}
```

## Default Configuration

The system comes with pre-configured zones and methods:

### UK Zone
- **Free Standard Shipping**: Free over £50 (3-5 days)
- **Standard Shipping**: £5.99 (3-5 days)
- **Express Shipping**: £9.99 (2-3 days)
- **Next Day Delivery**: £14.99 (1 day)

### EU Zone
- **EU Free Shipping**: Free over £100 (7-10 days)
- **EU Standard Shipping**: £12.99 (7-10 days)
- **EU Express Shipping**: £19.99 (5-7 days)

## Admin Interface Features

### Shipping Zones Management
- ✅ Create/edit/delete zones
- ✅ Select multiple countries per zone
- ✅ Enable/disable zones
- ✅ View zone coverage and method counts

### Shipping Methods Management
- ✅ Create/edit/delete methods
- ✅ Set prices and delivery times
- ✅ Configure free shipping thresholds
- ✅ Choose icons and descriptions
- ✅ Control sort order and availability

### Live Preview
- ✅ Test shipping rates by country
- ✅ Preview customer experience
- ✅ Verify free shipping thresholds
- ✅ Check delivery time displays

## Integration Points

### Checkout Flow
The system integrates seamlessly with your existing checkout:

```tsx
// Get shipping methods for customer's country
const { shippingMethods } = useCheckoutShipping(customerCountry);

// Calculate shipping for specific cart total
const { calculations } = useShippingCalculation(customerCountry, cartTotal);
```

### Order Processing
Shipping information is stored with orders for reference:

```tsx
// Store selected shipping method with order
const order = {
  shipping_method_id: selectedMethodId,
  shipping_cost: calculatedCost,
  // ... other order data
};
```

## API Reference

### ShippingService Methods

```typescript
// Get all zones
await shippingService.getShippingZones();

// Get methods for a zone
await shippingService.getShippingMethodsByZone(zoneId);

// Calculate shipping for country and cart total
await shippingService.calculateShipping(country, cartTotal);

// Get checkout-formatted methods
await shippingService.getCheckoutShippingMethods(country);
```

### React Hooks

```typescript
// Manage zones
const { zones, createZone, updateZone, deleteZone } = useShippingZones();

// Manage methods
const { methods, createMethod, updateMethod, deleteMethod } = useShippingMethods();

// Calculate shipping
const { calculations } = useShippingCalculation(country, cartTotal);

// Checkout integration
const { shippingMethods } = useCheckoutShipping(country);
```

## Benefits Over Hardcoded Rates

### ✅ **Flexibility**
- Change rates without code deployment
- Add new zones and methods easily
- Seasonal rate adjustments
- A/B testing different shipping strategies

### ✅ **Scalability**
- Support for unlimited zones and methods
- Easy expansion to new countries
- Complex pricing rules support
- Integration with shipping providers

### ✅ **User Experience**
- Accurate rates based on location
- Clear delivery time expectations
- Free shipping incentives
- Professional shipping options

### ✅ **Business Intelligence**
- Track shipping method popularity
- Analyze shipping costs vs. revenue
- Optimize rates based on data
- Monitor zone performance

## Migration from Hardcoded Rates

### Step 1: Backup Current Configuration
Document your current shipping methods and rates.

### Step 2: Set Up Database
Run the setup script to create tables and default data.

### Step 3: Configure Your Rates
Use the admin interface to match your current rates.

### Step 4: Update Checkout Code
Replace hardcoded arrays with dynamic service calls.

### Step 5: Test Thoroughly
Verify shipping calculation works for all countries.

### Step 6: Go Live
Deploy the new system and monitor for issues.

## Troubleshooting

### No Shipping Methods Available
- Check if zones are active
- Verify country is assigned to a zone
- Ensure methods are active for the zone

### Incorrect Shipping Costs
- Verify method pricing in admin
- Check free shipping threshold logic
- Confirm zone assignment for country

### Checkout Integration Issues
- Ensure hooks are properly imported
- Check country format consistency
- Verify API responses in browser console

## Future Enhancements

### Planned Features
- Weight-based shipping calculation
- Shipping provider API integration
- Bulk rate import/export
- Advanced pricing rules
- Shipping analytics dashboard

### Customization Options
- Custom shipping calculators
- Third-party rate providers
- Multi-currency support
- Tax-inclusive shipping rates

## Support

For issues or questions:
1. Check the browser console for errors
2. Verify database table structure
3. Test with known working countries
4. Review the admin interface for configuration issues

The shipping management system provides a solid foundation for professional e-commerce shipping while maintaining flexibility for future growth.

## ✅ Checkout Integration Complete!

The shipping management system is now **fully integrated** with your checkout process:

### 🔄 **How It Works:**
1. **Customer selects address** → System detects country
2. **System loads shipping methods** → Queries database for available methods in that zone
3. **Customer sees options** → Dynamic list with prices and delivery times
4. **Free shipping applied** → Automatically if cart total meets threshold
5. **Order placed** → Shipping method and cost stored with order

### 🌍 **Country-Based Shipping:**
- **UK customers** → See UK shipping methods (Free over £50, Standard £5.99, etc.)
- **EU customers** → See EU shipping methods (Free over £100, Standard £12.99, etc.)
- **Other countries** → See appropriate zone methods or default options

### 💰 **Dynamic Benefits:**
- ✅ Shipping costs update automatically when you change rates in admin
- ✅ Free shipping thresholds work in real-time
- ✅ No code deployment needed for rate changes
- ✅ Professional shipping options for customers
- ✅ Accurate delivery time estimates
