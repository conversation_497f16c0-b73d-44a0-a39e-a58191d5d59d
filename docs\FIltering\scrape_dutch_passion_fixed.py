"""
Dutch Passion Website Scraper (Requests + BeautifulSoup version) - Fixed Filter Extraction

This script scrapes product data and filter options from the Dutch Passion cannabis seeds website
using requests and BeautifulSoup with corrected selectors for filter extraction.
"""

import requests
import json
import re
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Base URL
BASE_URL = "https://dutch-passion.com"
SEEDS_URL = "https://dutch-passion.com/en/cannabis-seeds"

# Headers to mimic a browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Connection": "keep-alive",
    "Referer": "https://dutch-passion.com/",
    "DNT": "1",  # Do Not Track
}

# Function to get page content with age verification bypass
def get_page_content(url):
    print(f"Fetching content from: {url}")
    
    # First request to get cookies
    session = requests.Session()
    response = session.get(url, headers=HEADERS)
    
    # Check if we need to handle age verification
    if "ARE YOU AGED 18 OR OVER?" in response.text:
        print("Handling age verification...")
        
        # Extract any necessary tokens or form data
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Simulate clicking the "Enter" button by making a POST request
        # This is a simplified approach - actual implementation may vary based on site structure
        verification_url = urljoin(BASE_URL, "/en/age-verification")
        verification_data = {
            "age_verification": "true",
            "redirect": "/en/cannabis-seeds"
        }
        
        # Send POST request to handle age verification
        response = session.post(verification_url, data=verification_data, headers=HEADERS)
        
        # Get the main page again after verification
        response = session.get(url, headers=HEADERS)
    
    return response.text

# Function to extract filter categories and options - FIXED VERSION
def extract_filters(html_content):
    print("Extracting filter categories and options...")
    filter_data = {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all filter sections using the correct selector
    filter_sections = soup.select('section.facet.clearfix')
    
    print(f"Found {len(filter_sections)} filter sections")
    
    for section in filter_sections:
        try:
            # Get category name from the correct element
            category_element = section.select_one('div.facet-title.hidden-sm-down, div.h6.facet-title')
            if not category_element:
                continue
                
            category_name = category_element.text.strip()
            filter_data[category_name] = []
            
            # Get all options in this category
            options = section.select('li')
            
            for option in options:
                option_link = option.select_one('a.custom-control-label')
                if not option_link:
                    continue
                    
                option_text = option_link.text.strip()
                
                # Extract option name and count
                magnitude = option_link.select_one('span.magnitude')
                if magnitude:
                    count_text = magnitude.text.strip()
                    # Extract the number from format like "(25)"
                    count = re.search(r'\((\d+)\)', count_text)
                    count = count.group(1) if count else "0"
                    
                    # Remove the count from the option text to get clean name
                    name = option_text.replace(count_text, '').strip()
                else:
                    name = option_text
                    count = "0"
                
                # Try to get value from data attribute or href
                value = name
                if option_link.get('href'):
                    # Extract value from URL parameter if available
                    href = option_link.get('href')
                    param_match = re.search(r'tn_fk_\w+=([^&]+)', href)
                    if param_match:
                        value = param_match.group(1)
                
                filter_data[category_name].append({
                    "name": name,
                    "count": count,
                    "value": value
                })
        except Exception as e:
            print(f"Error extracting filter section: {e}")
    
    return filter_data

# Function to extract product data
def extract_products(html_content):
    print("Extracting product data...")
    products = []
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all product cards
    product_cards = soup.select('article.product-miniature')
    
    print(f"Found {len(product_cards)} product cards")
    
    for card in product_cards:
        try:
            product = {}
            
            # Extract product name
            name_elem = card.select_one('h3.product-title a')
            if name_elem:
                product["name"] = name_elem.text.strip()
            else:
                continue  # Skip if no name found
            
            # Extract price
            price_elem = card.select_one('span.price')
            if price_elem:
                product["price"] = price_elem.text.strip()
            else:
                product["price"] = "N/A"
            
            # Extract seed type
            seed_type_elem = card.select_one('.seed-type')
            if seed_type_elem:
                product["seed_type"] = seed_type_elem.text.strip()
            else:
                # Try to determine from product badges or other indicators
                if card.select_one('.product-flag.autoflower'):
                    product["seed_type"] = "Autoflower"
                elif card.select_one('.product-flag.feminized'):
                    product["seed_type"] = "Feminized"
                elif card.select_one('.product-flag.regular'):
                    product["seed_type"] = "Regular"
                else:
                    product["seed_type"] = "Unknown"
            
            # Extract product URL
            link_elem = card.select_one('h3.product-title a')
            if link_elem:
                product_url = link_elem.get('href')
                if not product_url.startswith('http'):
                    product_url = urljoin(BASE_URL, product_url)
                product["url"] = product_url
            
            # Extract image URL
            img_elem = card.select_one('.product-thumbnail img')
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url and not img_url.startswith('http'):
                    img_url = urljoin(BASE_URL, img_url)
                product["image_url"] = img_url
            
            # Extract any data attributes that might contain filter information
            for attr in card.attrs:
                if attr.startswith('data-'):
                    product[attr] = card[attr]
            
            products.append(product)
        except Exception as e:
            print(f"Error extracting product: {e}")
    
    return products

# Function to get detailed product information
def get_product_details(product_url):
    try:
        html_content = get_page_content(product_url)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        details = {}
        
        # Extract product specifications/attributes
        specs = {}
        spec_elements = soup.select('.product-features dl')
        
        for spec_group in spec_elements:
            try:
                dt_elements = spec_group.select('dt')
                dd_elements = spec_group.select('dd')
                
                for i in range(min(len(dt_elements), len(dd_elements))):
                    label = dt_elements[i].text.strip()
                    value = dd_elements[i].text.strip()
                    specs[label] = value
            except Exception as e:
                print(f"Error extracting specification group: {e}")
        
        details["specifications"] = specs
        
        # Extract product description
        desc_elem = soup.select_one('#description')
        if desc_elem:
            details["description"] = desc_elem.text.strip()
        else:
            details["description"] = ""
        
        return details
    except Exception as e:
        print(f"Error getting product details: {e}")
        return {"specifications": {}, "description": ""}

# Function to extract pagination information
def extract_pagination(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Try to find pagination elements
    pagination = soup.select('.pagination a')
    
    max_page = 1
    for page_link in pagination:
        try:
            page_num = int(page_link.text.strip())
            if page_num > max_page:
                max_page = page_num
        except ValueError:
            pass
    
    return max_page

# Main function to run the scraper
def main():
    print("Starting Dutch Passion website scraper (Requests + BeautifulSoup version - Fixed)...")
    
    try:
        # Get main page content
        main_page_html = get_page_content(SEEDS_URL)
        
        # Save the raw HTML for debugging
        with open('dutch_passion_raw_debug.html', 'w', encoding='utf-8') as f:
            f.write(main_page_html)
        
        # Extract filter data with fixed function
        filter_data = extract_filters(main_page_html)
        
        # Extract products from first page
        products = extract_products(main_page_html)
        
        # Check if there are more pages
        max_page = extract_pagination(main_page_html)
        print(f"Found {max_page} pages of products")
        
        # Get products from additional pages (limit to 3 pages)
        page_limit = min(max_page, 3)
        for page in range(2, page_limit + 1):
            page_url = f"{SEEDS_URL}?page={page}"
            page_html = get_page_content(page_url)
            page_products = extract_products(page_html)
            products.extend(page_products)
            time.sleep(1)  # Be nice to the server
        
        # Get detailed information for a sample of products
        sample_size = min(5, len(products))
        detailed_products = []
        
        for i, product in enumerate(products[:sample_size]):
            print(f"Getting details for product {i+1}/{sample_size}: {product['name']}")
            details = get_product_details(product['url'])
            detailed_product = {**product, **details}
            detailed_products.append(detailed_product)
            time.sleep(1)  # Be nice to the server
        
        # Save data to JSON files
        with open('dutch_passion_filters_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(filter_data, f, indent=2)
        
        with open('dutch_passion_products_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(products, f, indent=2)
        
        with open('dutch_passion_detailed_products_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_products, f, indent=2)
        
        print(f"Scraping completed. Extracted {len(filter_data)} filter categories and {len(products)} products.")
        print("Data saved to JSON files.")
        
    except Exception as e:
        print(f"Error during scraping: {e}")

if __name__ == "__main__":
    main()
