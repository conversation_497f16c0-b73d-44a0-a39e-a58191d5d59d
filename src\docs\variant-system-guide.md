# Product Variant System Guide

This guide explains how to use the product variant system in the admin UI.

## Overview

The product variant system allows you to create multiple variants of a product with different options, such as size, color, and pack size. Each variant can have its own price, stock quantity, and SKU.

## Components

The variant system consists of the following components:

1. **Option Definitions Manager**: Allows you to define the options for a product, such as size, color, and pack size.
2. **Variant Form**: Allows you to create and edit individual variants.
3. **Bulk Variant Generator**: Allows you to generate multiple variants at once based on option combinations.
4. **Variants Dialog**: Displays a list of all variants for a product and allows you to manage them.

## How to Use

### 1. Create a Product

First, create a product using the product form. Once the product is created, you can add variants to it.

### 2. Define Options

In the product form, scroll down to the "Product Variants" section. Use the Option Definitions Manager to define the options for your product:

1. Click "Add Option" to add a new option (e.g., Size, Color, Pack Size).
2. Enter the option name (e.g., "Size").
3. Add option values (e.g., "Small", "Medium", "Large", "XL").
4. Repeat for each option you want to add.
5. Click "Save" to save the product with the option definitions.

### 3. Create Variants

There are two ways to create variants:

#### Individual Variants

1. Click "Add Variant" to open the Variant Form.
2. Select the option values for the variant.
3. Enter the price, stock quantity, and other details.
4. Click "Save" to create the variant.

#### Bulk Generation

1. Click "Bulk Generate" to open the Bulk Variant Generator.
2. Set the global settings for all variants (price, stock quantity, etc.).
3. Adjust individual variant settings if needed.
4. Click "Generate Variants" to create all the variants at once.

### 4. Manage Variants

1. Click "Manage Variants" to open the Variants Dialog.
2. View all variants for the product.
3. Edit or delete individual variants.
4. Add new variants.

## Testing the Variant System

We've created several scripts to help you test the variant system:

1. `run-variant-count-migration.js`: Creates a database function to efficiently count variants per product.
2. `test-variant-components.js`: Adds test variants to a product.
3. `run-all-variant-tests.js`: Runs all the variant tests in sequence.

To run all the tests, use the following command:

```bash
node src/scripts/run-all-variant-tests.js
```

## Data Structure

### Option Definitions

Option definitions are stored in the `option_definitions` field of the `products` table as a JSON object:

```json
{
  "Size": ["Small", "Medium", "Large", "XL"],
  "Color": ["Red", "Blue", "Gold", "Rasta"],
  "Pack Size": ["3 Pack", "5 Pack", "10 Pack"]
}
```

### Variants

Variants are stored in the `product_variants` table with the following fields:

- `id`: Unique identifier for the variant
- `product_id`: ID of the parent product
- `variant_name`: Name of the variant
- `sku`: SKU of the variant
- `price`: Price of the variant
- `sale_price`: Sale price of the variant (if applicable)
- `stock_quantity`: Stock quantity of the variant
- `in_stock`: Whether the variant is in stock
- `image`: Image of the variant (if different from the product image)
- `option_combination`: JSON object containing the option values for the variant
- `is_active`: Whether the variant is active
- `external_id`: External ID for integration with other systems
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

Example option combination:

```json
{
  "Size": "Medium",
  "Color": "Blue",
  "Pack Size": "5 Pack"
}
```

## Best Practices

1. **Option Names**: Use clear, concise names for options.
2. **Option Values**: Keep option values simple and consistent.
3. **SKUs**: Use a consistent pattern for SKUs, such as `BASE-SIZE-COLOR-PACK`.
4. **Prices**: Set base prices for variants and adjust based on options.
5. **Images**: Add images for variants with different appearances.

## Troubleshooting

If you encounter issues with the variant system, try the following:

1. Check the browser console for errors.
2. Verify that the option definitions are correctly defined.
3. Make sure the product has been saved before adding variants.
4. Run the test scripts to verify that the variant system is working correctly.
5. Check the database to ensure that variants are being created correctly.

## Need Help?

If you need further assistance, please contact the development team.
