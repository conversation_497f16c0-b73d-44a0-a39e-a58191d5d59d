
export interface RelatedProduct {
  id: string;
  product_id: string;
  related_product_id: string;
  created_at: string;
  updated_at: string | null;
  sort_order: number;
}

export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  sale_price?: number;
  cost_price?: number; // Purchase cost (not shown to customers)
  image?: string;
  additional_images?: string[]; // For multiple product images
  images?: string[]; // Alias for UI components that use this property name
  category_id?: string;
  subcategory_id?: string;
  brand_id?: string;
  sku?: string;
  stock_quantity?: number;
  weight?: number;
  dimensions?: string;
  size?: string;
  additional_info?: string;
  in_stock: boolean;
  is_featured: boolean;
  is_new: boolean;
  is_best_seller: boolean;
  rating: number;
  review_count: number;
  pricing_model?: string; // standard, variant, bulk
  bulk_pricing_tiers?: string; // Format: quantity:discount;quantity:discount
  created_at: string;
  updated_at: string;
  // EPOS Integration fields
  external_id?: string; // ID from external EPOS system
  last_synced_at?: string; // Timestamp of last sync with EPOS
  sync_status?: string; // Status of sync (synced, pending, error, etc.)
  external_stock_quantity?: number; // Stock quantity from EPOS system

  // Product options and price adjustments
  options?: Record<string, string[]>; // Option name to array of option values
  price_adjustments?: Record<string, Record<string, any>>; // Option name to value to price adjustment (can be number, string, or object)

  // Variant-related properties
  parent_id?: string; // ID of parent product if this is a variant
  option_selection?: Record<string, string>; // Selected options for this variant
  variants?: Product[]; // Child variants of this product
  metadata?: string; // JSON string containing additional metadata like price_adjustment
                      // price_adjustment is used to override the calculated price difference
                      // This is useful when the variant price should not be a simple addition to the base price
                      // Example: { "price_adjustment": 37 } for a variant that costs £37 more than the base product

  // Product options
  option_name1?: string;
  option_type1?: string;
  option_description1?: string;
  option_price_adjustment1?: string; // Price adjustments for option values (e.g., 0;+2.00;+5.00)
  option_name2?: string;
  option_type2?: string;
  option_description2?: string;
  option_price_adjustment2?: string; // Price adjustments for option values
  option_name3?: string;
  option_type3?: string;
  option_description3?: string;
  option_price_adjustment3?: string; // Price adjustments for option values

  // Additional information
  additional_info_title1?: string;
  additional_info_description1?: string;
  additional_info_title2?: string;
  additional_info_description2?: string;
  additional_info_title3?: string;
  additional_info_description3?: string;

  // Related entities
  brand?: Brand;
  category?: Category;
  subcategory?: Category;
  related_products?: RelatedProduct[];
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent_id?: string | null;
  created_at: string;
  updated_at?: string;
  display_order: number;
  parent?: Category;
  subcategories?: Category[];
}

export interface Profile {
  id: string;
  email?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  address?: string | null;
  city?: string | null;
  postal_code?: string | null;
  country?: string | null;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  user_id: string;
  status: string;
  total: number;
  shipping_address?: any; // Changed from Record<string, any> to any to match Json type
  billing_address?: any; // Changed from Record<string, any> to any to match Json type
  created_at: string;
  updated_at: string;
  profiles?: Profile | null; // Updated to match the profile structure
  order_items?: OrderItem[]; // Added relationship to order items
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  price: number;
  quantity: number;
  product?: Product; // Added relationship to product
}

export interface CartItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  product?: Product;
}

export interface SavedItem {
  id: string;
  user_id: string;
  product_id: string;
  created_at: string;
  product?: Product;
}

export interface Wishlist {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  items?: WishlistItem[];
}

export interface WishlistItem {
  id: string;
  wishlist_id: string;
  product_id: string;
  created_at: string;
  product?: Product;
}

export interface Brand {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  created_at: string;
  updated_at?: string;
}

export interface Settings {
  id: string;
  key: string;
  value: any;
  created_at: string;
  updated_at: string;
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  order_index: number;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export interface Blog {
  id: string;
  title: string;
  slug: string;
  summary?: string;
  content: string;
  featured_image?: string;
  author_id?: string;
  category?: string;
  tags?: string[];
  is_published: boolean;
  is_featured: boolean;
  view_count: number;
  reading_time?: number;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  created_at: string;
  updated_at: string;
  published_at?: string;
  author?: Profile;
}

export interface BlogImage {
  id: string;
  blog_id: string;
  image_url: string;
  alt_text?: string;
  caption?: string;
  display_order: number;
  created_at: string;
  blog?: Blog;
}

export interface BlogAIMetadata {
  id: string;
  blog_id: string;
  prompt_used?: string;
  ai_model?: string;
  generation_params?: Record<string, any>;
  content_segments?: Record<string, any>;
  created_at: string;
  updated_at: string;
  blog?: Blog;
}

export interface BlogSocialShare {
  id: string;
  blog_id: string;
  platform: string;
  share_url?: string;
  share_content?: string;
  share_image?: string;
  scheduled_for?: string;
  posted_at?: string;
  post_status: string;
  engagement_metrics?: Record<string, any>;
  created_at: string;
  updated_at: string;
  blog?: Blog;
}

export interface BlogComment {
  id: string;
  blog_id: string;
  user_id?: string;
  parent_comment_id?: string;
  content: string;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  blog?: Blog;
  user?: Profile;
  parent_comment?: BlogComment;
  replies?: BlogComment[];
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  parent?: BlogCategory;
  subcategories?: BlogCategory[];
}

export interface RelatedProduct {
  id: string;
  product_id: string;
  related_product_id: string;
  display_order: number;
  created_at: string;
  updated_at: string;
}
