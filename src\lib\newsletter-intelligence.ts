/**
 * Newsletter Intelligence Service
 * Gathers store data to make newsletters smarter and more relevant
 */

import { supabase } from '@/integrations/supabase/client';

export interface ProductSummary {
  id: string;
  name: string;
  slug: string;
  price: number;
  sale_price?: number;
  image?: string;
  category?: string;
  brand?: string;
  created_at: string;
  is_featured: boolean;
  stock_quantity?: number;
}

export interface StoreIntelligence {
  newProducts: ProductSummary[];
  featuredProducts: ProductSummary[];
  popularProducts: ProductSummary[];
  lowStockProducts: ProductSummary[];
  recentlyRestocked: ProductSummary[];
  topCategories: { name: string; count: number }[];
  totalProducts: number;
  totalActiveProducts: number;
  lastNewsletterDate?: string;
}

/**
 * Get comprehensive store intelligence for newsletter generation
 */
export async function getStoreIntelligence(daysBack: number = 30): Promise<StoreIntelligence> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysBack);
  const cutoffDateString = cutoffDate.toISOString();

  try {
    // Get new products (created in the last X days)
    const { data: newProducts, error: newProductsError } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .eq('is_active', true)
      .gte('created_at', cutoffDateString)
      .order('created_at', { ascending: false })
      .limit(10);

    if (newProductsError) {
      console.warn('Error fetching new products:', newProductsError);
    }

    // Get featured products
    const { data: featuredProducts } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .eq('is_active', true)
      .eq('is_featured', true)
      .order('created_at', { ascending: false })
      .limit(8);

    // Get low stock products (stock_quantity < 10 and > 0)
    const { data: lowStockProducts } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .eq('is_active', true)
      .lt('stock_quantity', 10)
      .gt('stock_quantity', 0)
      .order('stock_quantity', { ascending: true })
      .limit(5);

    // Get recently restocked products (stock_quantity > 20, updated recently)
    const { data: recentlyRestocked } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .eq('is_active', true)
      .gte('stock_quantity', 20)
      .gte('updated_at', cutoffDateString)
      .order('updated_at', { ascending: false })
      .limit(5);

    // Get total product counts
    const { count: totalProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    const { count: totalActiveProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get top categories by product count
    const { data: categoryData } = await supabase
      .from('products')
      .select(`
        categories(name)
      `)
      .eq('is_active', true);

    // Process category data
    const categoryCount: { [key: string]: number } = {};
    categoryData?.forEach(item => {
      const categoryName = (item as any).categories?.name;
      if (categoryName) {
        categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;
      }
    });

    const topCategories = Object.entries(categoryCount)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Get last newsletter date (if any)
    const { data: lastNewsletter, error: newsletterError } = await supabase
      .from('newsletter_sends')
      .select('sent_at')
      .eq('status', 'sent')
      .order('sent_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    // Log any newsletter query errors but don't fail the whole function
    if (newsletterError) {
      console.warn('Could not fetch last newsletter date:', newsletterError);
    }

    // Transform data to consistent format
    const transformProduct = (product: any): ProductSummary => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      sale_price: product.sale_price,
      image: product.image,
      category: product.categories?.name,
      brand: product.brands?.name,
      created_at: product.created_at,
      is_featured: product.is_featured,
      stock_quantity: product.stock_quantity
    });

    return {
      newProducts: newProducts?.map(transformProduct) || [],
      featuredProducts: featuredProducts?.map(transformProduct) || [],
      popularProducts: [], // TODO: Implement based on order data
      lowStockProducts: lowStockProducts?.map(transformProduct) || [],
      recentlyRestocked: recentlyRestocked?.map(transformProduct) || [],
      topCategories,
      totalProducts: totalProducts || 0,
      totalActiveProducts: totalActiveProducts || 0,
      lastNewsletterDate: lastNewsletter?.sent_at
    };

  } catch (error) {
    console.error('Error gathering store intelligence:', error);
    // Return empty data structure on error
    return {
      newProducts: [],
      featuredProducts: [],
      popularProducts: [],
      lowStockProducts: [],
      recentlyRestocked: [],
      topCategories: [],
      totalProducts: 0,
      totalActiveProducts: 0
    };
  }
}

/**
 * Generate a smart newsletter prompt based on store intelligence
 */
export function generateSmartNewsletterPrompt(
  intelligence: StoreIntelligence,
  topic?: string,
  tone: string = 'informative'
): string {
  const {
    newProducts,
    featuredProducts,
    lowStockProducts,
    recentlyRestocked,
    topCategories,
    totalActiveProducts,
    lastNewsletterDate
  } = intelligence;

  const daysSinceLastNewsletter = lastNewsletterDate
    ? Math.floor((Date.now() - new Date(lastNewsletterDate).getTime()) / (1000 * 60 * 60 * 24))
    : null;

  let contextualInfo = `
Store Context:
- Total active products: ${totalActiveProducts}
- New products in last 30 days: ${newProducts.length}
- Featured products: ${featuredProducts.length}
- Low stock alerts: ${lowStockProducts.length}
- Recently restocked: ${recentlyRestocked.length}
${daysSinceLastNewsletter ? `- Days since last newsletter: ${daysSinceLastNewsletter}` : ''}

Top Categories: ${topCategories.map(c => `${c.name} (${c.count} products)`).join(', ')}
`;

  if (newProducts.length > 0) {
    contextualInfo += `
New Products to Highlight:
${newProducts.slice(0, 5).map(p => `- ${p.name} (£${p.price}${p.sale_price ? `, on sale for £${p.sale_price}` : ''}) - ${p.category || 'Uncategorized'}`).join('\n')}
`;
  }

  if (featuredProducts.length > 0) {
    contextualInfo += `
Featured Products:
${featuredProducts.slice(0, 3).map(p => `- ${p.name} (£${p.price}${p.sale_price ? `, on sale for £${p.sale_price}` : ''}) - ${p.category || 'Uncategorized'}`).join('\n')}
`;
  }

  if (lowStockProducts.length > 0) {
    contextualInfo += `
Low Stock Items (create urgency):
${lowStockProducts.slice(0, 3).map(p => `- ${p.name} (only ${p.stock_quantity} left!)`).join('\n')}
`;
  }

  if (recentlyRestocked.length > 0) {
    contextualInfo += `
Recently Restocked:
${recentlyRestocked.slice(0, 3).map(p => `- ${p.name} (back in stock!)`).join('\n')}
`;
  }

  return `Create an engaging email newsletter for a CBD/cannabis e-commerce store called "Bits N Bongs".

${contextualInfo}

Requirements:
- Tone: ${tone}
- Use British currency (£ GBP) for all pricing, never use dollars ($)
- Include a compelling subject line suggestion at the top
- Start with a friendly greeting to subscribers
- ${newProducts.length > 0 ? 'Feature the new products listed above with their actual names and prices' : 'Create a section about new arrivals (general)'}
- ${featuredProducts.length > 0 ? 'Highlight the featured products with their actual details' : 'Include a featured products section'}
- ${lowStockProducts.length > 0 ? 'Create urgency around the low stock items mentioned' : ''}
- ${recentlyRestocked.length > 0 ? 'Mention the recently restocked items' : ''}
- Include educational content about CBD/cannabis
- Add a special offer or promotion section
- End with a clear call-to-action to visit the store
- Use simple HTML formatting that works well in emails
- Include social media links section
- Add unsubscribe reminder at the bottom

${topic ? `Focus the newsletter around this theme: ${topic}` : 'Create a general monthly update newsletter'}

Make the newsletter feel personal and relevant to our actual store inventory. Use the real product names and prices provided above.`;
}

/**
 * Get newsletter-ready product data with images
 */
export async function getNewsletterProductData(productIds: string[]): Promise<ProductSummary[]> {
  if (productIds.length === 0) return [];

  try {
    const { data: products } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .in('id', productIds)
      .eq('is_active', true);

    return products?.map(product => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      sale_price: product.sale_price,
      image: product.image,
      category: (product as any).categories?.name,
      brand: (product as any).brands?.name,
      created_at: product.created_at,
      is_featured: product.is_featured,
      stock_quantity: product.stock_quantity
    })) || [];

  } catch (error) {
    console.error('Error fetching newsletter product data:', error);
    return [];
  }
}

/**
 * Process newsletter content to add real product images
 */
export async function processNewsletterImages(content: string, intelligence: StoreIntelligence): Promise<string> {
  let processedContent = content;

  // Get all products from intelligence
  const allProducts = [
    ...intelligence.newProducts,
    ...intelligence.featuredProducts,
    ...intelligence.recentlyRestocked
  ];

  // Also search for product mentions in the content and find matching products
  const additionalProducts = await findProductMentions(content);
  const combinedProducts = [...allProducts, ...additionalProducts];

  // Remove duplicates based on product ID
  const uniqueProducts = combinedProducts.filter((product, index, self) =>
    index === self.findIndex(p => p.id === product.id)
  );

  // Process content to add images
  uniqueProducts.forEach(product => {
    if (product.image && product.name) {
      // Create various patterns to match product mentions
      const patterns = [
        new RegExp(`\\b${escapeRegExp(product.name)}\\b`, 'gi'),
        new RegExp(`\\b${escapeRegExp(product.name.toLowerCase())}\\b`, 'gi'),
        // Handle partial matches for long product names
        ...createPartialNamePatterns(product.name)
      ];

      patterns.forEach(pattern => {
        if (processedContent.match(pattern) && !processedContent.includes(`![${product.name}]`)) {
          // Add image after the first mention of the product
          processedContent = processedContent.replace(
            pattern,
            (match) => `${match}\n\n![${product.name}](${getFullImageUrl(product.image!)})`
          );
        }
      });
    }
  });

  return processedContent;
}

/**
 * Find products mentioned in the content by searching the database
 */
async function findProductMentions(content: string): Promise<ProductSummary[]> {
  try {
    // Extract potential product names from content
    const words = content.toLowerCase().split(/\s+/);
    const potentialProductTerms = words.filter(word =>
      word.length > 3 &&
      !commonWords.includes(word) &&
      /^[a-zA-Z0-9]+$/.test(word)
    );

    if (potentialProductTerms.length === 0) return [];

    // Search for products that might match these terms
    const { data: products } = await supabase
      .from('products')
      .select(`
        id, name, slug, price, sale_price, image, created_at, is_featured, stock_quantity,
        categories(name),
        brands(name)
      `)
      .eq('is_active', true)
      .or(potentialProductTerms.map(term => `name.ilike.%${term}%`).join(','))
      .limit(20);

    return products?.map(product => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      sale_price: product.sale_price,
      image: product.image,
      category: (product as any).categories?.name,
      brand: (product as any).brands?.name,
      created_at: product.created_at,
      is_featured: product.is_featured,
      stock_quantity: product.stock_quantity
    })) || [];

  } catch (error) {
    console.error('Error finding product mentions:', error);
    return [];
  }
}

/**
 * Create partial name patterns for long product names
 */
function createPartialNamePatterns(productName: string): RegExp[] {
  const patterns: RegExp[] = [];
  const words = productName.split(/\s+/);

  if (words.length > 1) {
    // Create patterns for first few words
    for (let i = 2; i <= Math.min(words.length, 4); i++) {
      const partial = words.slice(0, i).join('\\s+');
      patterns.push(new RegExp(`\\b${escapeRegExp(partial)}\\b`, 'gi'));
    }
  }

  return patterns;
}

/**
 * Get full image URL for Supabase storage
 */
function getFullImageUrl(imagePath: string): string {
  if (imagePath.startsWith('http')) {
    return imagePath; // Already a full URL
  }

  // Construct Supabase storage URL
  const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
  const bucketName = 'product-images';

  return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${imagePath}`;
}

/**
 * Common words to exclude from product search
 */
const commonWords = [
  'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
  'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above',
  'below', 'between', 'among', 'this', 'that', 'these', 'those', 'our', 'your',
  'their', 'his', 'her', 'its', 'we', 'you', 'they', 'he', 'she', 'it', 'i',
  'me', 'my', 'mine', 'myself', 'you', 'your', 'yours', 'yourself', 'him',
  'his', 'himself', 'her', 'hers', 'herself', 'it', 'its', 'itself', 'we',
  'us', 'our', 'ours', 'ourselves', 'they', 'them', 'their', 'theirs',
  'themselves', 'what', 'which', 'who', 'whom', 'this', 'that', 'these',
  'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
  'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
  'may', 'might', 'must', 'can', 'shall', 'newsletter', 'email', 'store',
  'shop', 'buy', 'purchase', 'product', 'products', 'item', 'items', 'new',
  'latest', 'best', 'great', 'amazing', 'perfect', 'quality', 'premium'
];

/**
 * Generate HTML email template with real product data
 */
export async function generateNewsletterHTML(content: string, intelligence: StoreIntelligence): Promise<string> {
  const processedContent = await processNewsletterImages(content, intelligence);

  // Convert markdown to HTML with email-safe styling
  let html = processedContent
    .replace(/^# (.*$)/gim, '<h1 style="color: #2d3748; font-size: 24px; margin-bottom: 16px;">$1</h1>')
    .replace(/^## (.*$)/gim, '<h2 style="color: #4a5568; font-size: 20px; margin-bottom: 12px;">$1</h2>')
    .replace(/^### (.*$)/gim, '<h3 style="color: #718096; font-size: 18px; margin-bottom: 8px;">$1</h3>')
    .replace(/\*\*(.*?)\*\*/gim, '<strong style="font-weight: 600;">$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em style="font-style: italic;">$1</em>')
    .replace(/!\[(.*?)\]\((.*?)\)/gim, '<img src="$2" alt="$1" style="max-width: 100%; height: auto; margin: 16px 0; border-radius: 8px;" />')
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" style="color: #3182ce; text-decoration: none;">$1</a>')
    .replace(/\n\n/gim, '</p><p style="margin-bottom: 16px; line-height: 1.6;">')
    .replace(/\n/gim, '<br>')
    .replace(/^(.*)$/gim, '<p style="margin-bottom: 16px; line-height: 1.6;">$1</p>')
    .replace(/<p[^>]*><\/p>/gim, '')
    .replace(/<p[^>]*><h/gim, '<h')
    .replace(/<\/h([1-6])><\/p>/gim, '</h$1>');

  // Wrap in email template
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bits N Bongs Newsletter</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h1 style="color: #2d3748; text-align: center; margin-bottom: 0;">Bits N Bongs</h1>
        <p style="text-align: center; color: #718096; margin-top: 0;">Premium CBD Products & Smoking Accessories</p>
    </div>

    <div style="background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        ${html}
    </div>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px; text-align: center;">
        <p style="margin-bottom: 16px;"><strong>Follow us on social media:</strong></p>
        <p style="margin-bottom: 16px;">
            <a href="#" style="color: #3182ce; text-decoration: none; margin: 0 10px;">Facebook</a>
            <a href="#" style="color: #3182ce; text-decoration: none; margin: 0 10px;">Instagram</a>
            <a href="#" style="color: #3182ce; text-decoration: none; margin: 0 10px;">Twitter</a>
        </p>
        <p style="font-size: 12px; color: #718096;">
            You received this email because you subscribed to our newsletter.
            <a href="#" style="color: #3182ce; text-decoration: none;">Unsubscribe</a>
        </p>
    </div>
</body>
</html>`;
}

/**
 * Escape special regex characters
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
