const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Input file
const inputFile = path.join('..', '..', 'docs', 'catalog_products.csv');

// Create a custom CSV parser to handle the complex format
const customCsvParser = () => {
  return csv({
    separator: ',',
    escape: '"',
    quote: '"',
    headers: [
      'handleId', 'fieldType', 'name', 'description', 'productImageUrl', 'collection',
      'sku', 'ribbon', 'price', 'surcharge', 'visible', 'discountMode', 'discountValue',
      'inventory', 'weight', 'cost', 'productOptionName1', 'productOptionType1',
      'productOptionDescription1', 'productOptionName2', 'productOptionType2',
      'productOptionDescription2', 'productOptionName3', 'productOptionType3',
      'productOptionDescription3', 'productOptionName4', 'productOptionType4',
      'productOptionDescription4', 'productOptionName5', 'productOptionType5',
      'productOptionDescription5', 'productOptionName6', 'productOptionType6',
      'productOptionDescription6', 'additionalInfoTitle1', 'additionalInfoDescription1',
      'additionalInfoTitle2', 'additionalInfoDescription2', 'additionalInfoTitle3',
      'additionalInfoDescription3', 'additionalInfoTitle4', 'additionalInfoDescription4',
      'additionalInfoTitle5', 'additionalInfoDescription5', 'additionalInfoTitle6',
      'additionalInfoDescription6', 'customTextField1', 'customTextCharLimit1',
      'customTextMandatory1', 'customTextField2', 'customTextCharLimit2',
      'customTextMandatory2', 'brand', 'productOptionValue1'
    ]
  });
};

// Process the CSV file
console.log(`Reading products from ${inputFile}...`);

// Store products and their variants
const products = {};
const variants = {};

// First pass: collect all products with Pack Size options
fs.createReadStream(inputFile)
  .pipe(customCsvParser())
  .on('data', (row) => {
    if (row.fieldType === 'Product' && row.productOptionName1 === 'Pack Size') {
      console.log(`Found product with Pack Size: ${row.name} (${row.handleId})`);
      products[row.handleId] = {
        name: row.name,
        basePrice: parseFloat(row.price) || 0,
        options: row.productOptionDescription1 ? row.productOptionDescription1.split(';') : [],
        variants: []
      };
    }
  })
  .on('end', () => {
    console.log(`Found ${Object.keys(products).length} products with Pack Size options`);

    // Second pass: collect all variants
    fs.createReadStream(inputFile)
      .pipe(customCsvParser())
      .on('data', (row) => {
        if (row.fieldType === 'Variant' && products[row.handleId]) {
          const product = products[row.handleId];
          const optionValue = row.productOptionValue1;
          const surcharge = parseFloat(row.surcharge) || 0;

          console.log(`Found variant for ${product.name}: ${optionValue} with surcharge ${surcharge}`);

          if (optionValue && (surcharge > 0 || surcharge === 0)) {
            product.variants.push({
              option: optionValue,
              surcharge: surcharge,
              totalPrice: product.basePrice + surcharge
            });
          }
        }
      })
      .on('end', () => {
        // Filter products with price variations
        const productsWithVariations = Object.entries(products)
          .filter(([id, product]) => {
            // Check if any variant has a surcharge
            return product.variants.some(v => v.surcharge > 0);
          })
          .map(([id, product]) => {
            return {
              id,
              name: product.name,
              basePrice: product.basePrice,
              variants: product.variants
            };
          });

        console.log(`Found ${productsWithVariations.length} products with price variations out of ${Object.keys(products).length} products with Pack Size options`);

        // Output the results
        console.log('\nProducts with price variations:');
        productsWithVariations.forEach(product => {
          console.log(`\n${product.name} (${product.id}) - Base Price: £${product.basePrice}`);
          product.variants.forEach(variant => {
            console.log(`  ${variant.option}: £${variant.totalPrice} (surcharge: £${variant.surcharge})`);
          });
        });

        // Generate code for fix_variant_prices.js
        console.log('\n\nCode for fix_variant_prices.js:');
        console.log('const priceVariations = {');
        productsWithVariations.forEach(product => {
          console.log(`  // ${product.name}`);
          console.log(`  '${product.id}': {`);
          product.variants.forEach(variant => {
            console.log(`    '${variant.option}': ${variant.totalPrice},`);
          });
          console.log('  },');
        });
        console.log('};');
      });
  });
