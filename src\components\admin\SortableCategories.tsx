import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowUp, ArrowDown, Edit, Trash, Save } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';

// Import the Category type from the database types
import { Category } from '@/types/database';

// Extend the Category type for our component
type Category = DBCategory & {
  parent?: { id: string; name: string } | null;
};

interface SortableCategoryProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

const SortableItem: React.FC<SortableCategoryProps> = ({ category, onEdit, onDelete }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: category.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-2">
      <Card className="border shadow-sm">
        <CardContent className="p-3 flex items-center">
          <div 
            className="cursor-grab p-2 text-gray-400 hover:text-gray-600" 
            {...attributes} 
            {...listeners}
          >
            <GripVertical className="h-5 w-5" />
          </div>
          
          {category.image ? (
            <div className="w-10 h-10 mr-3 overflow-hidden rounded">
              <AspectRatio ratio={1}>
                <img 
                  src={category.image} 
                  alt={category.name} 
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    e.currentTarget.src = 'https://placehold.co/100x100?text=No+Image';
                  }}
                />
              </AspectRatio>
            </div>
          ) : (
            <div className="w-10 h-10 mr-3 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs text-gray-500">No img</span>
            </div>
          )}
          
          <div className="flex-grow">
            <h3 className="font-medium">{category.name}</h3>
            <p className="text-xs text-gray-500">
              {category.parent_id ? 'Subcategory' : 'Main Category'}
            </p>
          </div>
          
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(category)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(category)}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface SortableCategoriesProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
  parentId: string | null;
  title: string;
}

const SortableCategories: React.FC<SortableCategoriesProps> = ({ 
  categories, 
  onEdit, 
  onDelete,
  parentId,
  title
}) => {
  const queryClient = useQueryClient();
  const [localCategories, setLocalCategories] = useState<Category[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  
  // Filter and sort categories on component mount and when categories prop changes
  useEffect(() => {
    // Filter categories by parent_id
    const filtered = categories.filter(cat => cat.parent_id === parentId);
    
    // Sort by display_order
    const sorted = [...filtered].sort((a, b) => a.display_order - b.display_order);
    
    // Set local state
    setLocalCategories(sorted);
  }, [categories, parentId]);
  
  // Get category IDs for the sortable context
  const categoryIds = localCategories.map(cat => cat.id);
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // Update category order mutation
  const updateCategoryOrder = useMutation({
    mutationFn: async (updatedCategories: Category[]) => {
      console.log('Updating categories with display order:', updatedCategories);
      
      // Create a batch update with new display_order values
      const updates = updatedCategories.map((category, index) => ({
        id: category.id,
        display_order: index + 1 // 1-based indexing
      }));
      
      console.log('Category updates to apply:', updates);
      
      // First, fetch the current state to verify
      const { data: beforeUpdate } = await supabase
        .from('categories')
        .select('id, name, display_order')
        .in('id', updates.map(u => u.id));
      
      console.log('Before update:', beforeUpdate);
      
      // Perform a single batch update for all categories
      for (const update of updates) {
        const { error } = await supabase
          .from('categories')
          .update({ display_order: update.display_order } as any)
          .eq('id', update.id);
        
        if (error) {
          console.error('Error updating category order:', error);
          throw error;
        }
      }
      
      // Verify the update worked
      const { data: afterUpdate } = await supabase
        .from('categories')
        .select('id, name, display_order')
        .in('id', updates.map(u => u.id));
      
      console.log('After update:', afterUpdate);
      
      return updatedCategories;
    },
    onSuccess: () => {
      toast({
        title: "Category order updated",
        description: "The category order has been saved successfully.",
      });
      
      // Force an immediate refetch of the categories data
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update category order: ${error.message}`,
        variant: "destructive",
      });
    },
  });
  
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      setLocalCategories((items) => {
        // Find the indices of the items being reordered
        const oldIndex = items.findIndex(cat => cat.id === active.id);
        const newIndex = items.findIndex(cat => cat.id === over.id);
        
        // Create a new array with the updated order
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };
  
  const handleSaveOrder = async () => {
    setIsSaving(true);
    try {
      await updateCategoryOrder.mutateAsync(localCategories);
    } catch (error) {
      console.error('Error saving category order:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{title}</h2>
        <Button 
          onClick={handleSaveOrder}
          disabled={isSaving || updateCategoryOrder.isPending}
          size="sm"
          variant="outline"
        >
          {isSaving || updateCategoryOrder.isPending ? "Saving..." : "Save Order"}
        </Button>
      </div>
      
      {localCategories.length > 0 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={categoryIds}
            strategy={verticalListSortingStrategy}
          >
            {localCategories.map((category) => (
              <SortableItem
                key={category.id}
                category={category}
                onEdit={onEdit}
                onDelete={onDelete}
              />
            ))}
          </SortableContext>
        </DndContext>
      ) : (
        <p className="text-gray-500 italic">No categories found</p>
      )}
    </div>
  );
};

export default SortableCategories;
