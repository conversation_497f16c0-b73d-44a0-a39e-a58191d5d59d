
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus, Trash, Palette } from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Badge } from '@/components/ui/badge';

interface CartItemProps {
  item: {
    id: string;
    product_id?: string;
    quantity: number;
    selected_options?: Record<string, string>;
    price_adjustment?: number;
    calculated_price?: number;
    variant?: {
      id: string;
      sku: string;
      options: Record<string, string>;
      price_adjustment: number;
    };
    price?: number;
    product?: {
      id: string;
      name: string;
      price: number;
      sale_price?: number | null;
      image?: string | null;
      slug: string;
    };
  };
}

const CartItem = ({ item }: CartItemProps) => {
  const { removeFromCart, updateQuantity, isLoading } = useCart();
  const [quantity, setQuantity] = useState(item.quantity);

  if (!item.product) {
    return null;
  }

  const { name, price, sale_price, image, slug } = item.product;

  // Get the product base price (from sale_price, price, or item.price)
  const productBasePrice = typeof sale_price === 'number' ? sale_price : 
                         (typeof price === 'number' ? price : 
                         (typeof item.price === 'number' ? item.price : 0));
  
  // Calculate the base price for the sale badge
  const salePercentage = productBasePrice > 0 && sale_price !== undefined && sale_price < productBasePrice
    ? Math.round(((productBasePrice - sale_price) / productBasePrice) * 100)
    : 0;

  // Get price adjustment from either direct property or variant
  const priceAdjustment = item.price_adjustment !== undefined
    ? item.price_adjustment
    : (item.variant?.price_adjustment || 0);

  // Calculate the display price in order of priority:
  // 1. Use calculated_price if available (for special cases like pack sizes)
  // 2. Use base price + adjustment
  // 3. Fall back to base price
  let displayPrice = productBasePrice;
  
  // Log item details for debugging
  console.log('Cart Item:', {
    id: item.id,
    name: name,
    productBasePrice,
    priceAdjustment,
    calculated_price: item.calculated_price,
    variant_options: item.variant?.options,
    selected_options: item.selected_options
  });
  
  // Use the calculated_price if available, otherwise calculate from base price and adjustment
  if (item.calculated_price !== undefined) {
    // If calculated_price is provided directly, use it
    displayPrice = item.calculated_price;
    console.log(`Using calculated_price: ${displayPrice}`);
  } else if (item.variant?.options) {
    // Check if there are options that might affect the price
    console.log('Checking variant options for price info');
    const options = item.variant.options;
    
    // Look for calculated price in the options
    if (options.calculatedPrice !== undefined) {
      displayPrice = Number(options.calculatedPrice);
      console.log(`Found calculatedPrice in options: ${displayPrice}`);
    } else {
      // Apply the price adjustment to the base price
      displayPrice = productBasePrice + priceAdjustment;
      console.log(`Calculated from base + adjustment: ${displayPrice}`);
    }
  } else {
    // Otherwise, apply the price adjustment to the base price
    displayPrice = productBasePrice + priceAdjustment;
    console.log(`Using base + adjustment: ${displayPrice}`);
  }
                        
  const itemTotal = displayPrice * item.quantity;

  // Format selected options for display
  const hasOptions = (item.selected_options && Object.keys(item.selected_options).length > 0) ||
                    (item.variant && item.variant.options && Object.keys(item.variant.options).length > 0);

  // Get options from either selected_options or variant.options
  const rawOptions = item.selected_options || (item.variant?.options || {});
  
  // Clean up options for display - only show actual product options, not technical details
  const optionsToDisplay: Record<string, string> = {};
  Object.entries(rawOptions).forEach(([key, value]) => {
    // Skip internal properties and debug info
    if (key.startsWith('_') || 
        key === 'selectedOptions' || 
        key === 'priceAdjustment' || 
        key === 'calculatedPrice' ||
        key === 'calculated_price' ||
        key === 'price_adjustment') {
      return;
    }
    
    // Convert value to string if it's not already a string
    let stringValue = '';
    
    if (typeof value === 'string') {
      stringValue = value;
    } else if (value === null || value === undefined) {
      stringValue = '';
    } else if (typeof value === 'object' && value !== null) {
      // Try to extract meaningful value from object
      const objValue = value as Record<string, any>;
      if ('value' in objValue && objValue.value !== undefined) {
        stringValue = String(objValue.value);
      } else if ('name' in objValue && objValue.name !== undefined) {
        stringValue = String(objValue.name);
      } else {
        // Make JSON more readable
        stringValue = JSON.stringify(value)
          .replace(/[{}"'\[\]]/g, '')
          .replace(/,/g, ', ');
      }
    } else {
      stringValue = String(value);
    }
    
    // Clean up the string value
    stringValue = stringValue.trim();
    if (stringValue.includes(':')) {
      stringValue = stringValue.split(':').pop()?.trim() || stringValue;
    }
    
    // Clean up the key name for display
    const displayKey = key
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/_/g, ' ')         // Replace underscores with spaces
      .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
      
    optionsToDisplay[displayKey] = stringValue;
  });

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(e.target.value);
    if (newQuantity > 0) {
      setQuantity(newQuantity);
    }
  };

  const handleBlur = () => {
    if (quantity !== item.quantity) {
      updateQuantity(item.id, quantity);
    }
  };

  const incrementQuantity = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    updateQuantity(item.id, newQuantity);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      updateQuantity(item.id, newQuantity);
    }
  };

  const handleRemove = () => {
    removeFromCart(item.id);
  };

  return (
    <div className="flex gap-4 border-b pb-4">
      <div className="w-20 h-20 overflow-hidden rounded-md">
        <Link to={`/shop/${slug}`} className="block w-full h-full">
          <AspectRatio ratio={1/1} className="bg-gray-100 hover:opacity-90 transition-opacity">
            <img
              src={image || '/placeholder.svg'}
              alt={name}
              className="object-cover w-full h-full"
            />
          </AspectRatio>
        </Link>
      </div>

      <div className="flex-1">
        <div className="flex justify-between">
          <div className="flex flex-col text-base font-semibold">
            <Link to={`/product/${item.product.id}`} className="hover:underline">
              {name}
            </Link>

            {/* Show a compact summary of options in the title */}
            {hasOptions && (
              <div className="text-xs text-gray-500">
                {Object.entries(optionsToDisplay)
                  .filter(([_, val]) => val && val.trim() !== '')
                  .map(([key, val]) => `${key}: ${val}`)
                  .join(', ')}
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={handleRemove}
            disabled={isLoading}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex flex-col mt-1 text-sm text-gray-700">
          {/* Display selected options if available */}
          {hasOptions && (
            <div className="flex flex-wrap gap-1 mb-1">
              {Object.entries(optionsToDisplay).map(([key, value]) => {
                // Skip if value is empty
                if (!value || value.trim() === '') return null;
                
                const isColorOption = key.toLowerCase().includes('color') || key.toLowerCase().includes('colour');

                return isColorOption ? (
                  <Badge key={key} variant="outline" className="flex items-center gap-1 text-xs py-0 h-5">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        background: value.toLowerCase().includes('rasta')
                          ? 'linear-gradient(to right, #009900 33%, #ffff00 33%, #ffff00 66%, #ff0000 66%)'
                          : undefined,
                        backgroundColor: !value.toLowerCase().includes('rasta') ? value : undefined
                      }}
                    />
                    <span>{key}: {value}</span>
                  </Badge>
                ) : (
                  <Badge key={key} variant="secondary" className="text-xs py-0 h-5">
                    {key}: {value}
                  </Badge>
                );
              }).filter(Boolean)}
            </div>
          )}

          {/* Display unit price */}
          <div className="flex items-center">
            <span className="font-medium">£{typeof displayPrice === 'number' ? displayPrice.toFixed(2) : '0.00'}</span>
          </div>
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center border rounded-md">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 px-0"
              onClick={decrementQuantity}
              disabled={quantity <= 1 || isLoading}
            >
              <Minus className="h-3 w-3" />
            </Button>

            <Input
              type="number"
              value={quantity}
              onChange={handleQuantityChange}
              onBlur={handleBlur}
              className="w-12 h-8 text-center border-0 p-0"
              min={1}
              disabled={isLoading}
            />

            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 px-0"
              onClick={incrementQuantity}
              disabled={isLoading}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>

          <div className="font-bold text-right">
            £{typeof displayPrice === 'number' && typeof item.quantity === 'number' ? (displayPrice * item.quantity).toFixed(2) : '0.00'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartItem;
