
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow flex items-center justify-center bg-sage-50 py-16">
        <div className="container-custom text-center">
          <div className="max-w-md mx-auto">
            <h1 className="text-6xl font-bold text-sage-500 mb-4">404</h1>
            <h2 className="text-2xl font-semibold text-clay-900 mb-4">Page Not Found</h2>
            <p className="text-clay-700 mb-8">
              The page you are looking for doesn't exist or has been moved.
            </p>
            <Button asChild size="lg">
              <Link to="/">Return Home</Link>
            </Button>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default NotFound;
