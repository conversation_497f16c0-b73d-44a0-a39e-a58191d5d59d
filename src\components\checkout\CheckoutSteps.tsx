import { Truck, CreditCard, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export type CheckoutStep = 'shipping' | 'payment' | 'review';

interface CheckoutStepsProps {
  currentStep: CheckoutStep;
  completedSteps: CheckoutStep[];
}

export function CheckoutSteps({ currentStep, completedSteps }: CheckoutStepsProps) {
  const steps = [
    {
      id: 'shipping',
      name: 'Shipping',
      icon: Truck,
    },
    {
      id: 'payment',
      name: 'Payment',
      icon: CreditCard,
    },
    {
      id: 'review',
      name: 'Review',
      icon: CheckCircle,
    },
  ];

  return (
    <div className="flex items-center justify-center w-full mb-8">
      {steps.map((step, index) => {
        const isActive = step.id === currentStep;
        const isCompleted = completedSteps.includes(step.id as CheckoutStep);
        const StepIcon = step.icon;

        return (
          <div key={step.id} className="flex items-center">
            {/* Step indicator */}
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  'flex items-center justify-center w-10 h-10 rounded-full border-2',
                  isActive
                    ? 'border-primary bg-primary text-primary-foreground'
                    : isCompleted
                    ? 'border-primary bg-primary text-primary-foreground'
                    : 'border-gray-300 bg-gray-100 text-gray-400'
                )}
              >
                {isCompleted ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <StepIcon className="w-5 h-5" />
                )}
              </div>
              <span
                className={cn(
                  'mt-2 text-sm font-medium',
                  isActive
                    ? 'text-primary'
                    : isCompleted
                    ? 'text-primary'
                    : 'text-gray-500'
                )}
              >
                {step.name}
              </span>
            </div>

            {/* Connector line */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  'w-16 h-0.5 mx-2',
                  isCompleted && completedSteps.includes(steps[index + 1].id as CheckoutStep)
                    ? 'bg-primary'
                    : 'bg-gray-300'
                )}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
