#!/usr/bin/env tsx
/**
 * Export premium inactive seed products for Super Agent #2
 * Focus on high-value products worth activating
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function exportPremiumInactiveSeeds() {
  console.log('💎 Exporting premium inactive seed products for Super Agent #2...\n');
  
  try {
    // Get inactive seed products with premium pricing
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        is_active,
        description,
        image,
        sku,
        seed_product_attributes(id)
      `)
      .eq('is_active', false) // Only inactive products
      .gte('price', 40); // Premium pricing £40+
    
    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }
    
    // Filter to seed products
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('seed') || name.includes('auto') || 
             name.includes('feminised') || name.includes('feminized') ||
             name.includes('female') || name.includes('strain');
    }) || [];
    
    // Analyze and prioritize
    const analysis = seedProducts.map(product => {
      const missingFields = [];
      
      if (!product.description || product.description.trim().length < 50) {
        missingFields.push('description');
      }
      
      if (!product.image) {
        missingFields.push('image');
      }
      
      if (!product.seed_product_attributes?.id) {
        missingFields.push('seed_attributes');
      }
      
      // Calculate market appeal score
      let marketAppeal = 0;
      const name = product.name.toLowerCase();
      
      // Popular strain indicators
      const popularStrains = [
        'gorilla', 'cookies', 'gelato', 'zkittlez', 'runtz', 'wedding cake',
        'purple', 'og', 'kush', 'diesel', 'haze', 'amnesia', 'white widow',
        'northern lights', 'jack herer', 'blue dream', 'girl scout cookies'
      ];
      
      if (popularStrains.some(strain => name.includes(strain))) {
        marketAppeal += 20;
      }
      
      // Premium genetics indicators
      if (name.includes('auto')) marketAppeal += 10;
      if (name.includes('feminised') || name.includes('feminized')) marketAppeal += 10;
      if (product.price > 60) marketAppeal += 15;
      if (product.price > 80) marketAppeal += 10;
      
      return {
        name: product.name,
        price: product.price || 0,
        missing_fields: missingFields.join('; '),
        missing_count: missingFields.length,
        market_appeal_score: marketAppeal,
        description_length: product.description?.length || 0,
        has_image: !!product.image,
        has_seed_attributes: !!product.seed_product_attributes?.id,
        activation_potential: marketAppeal >= 30 ? 'HIGH' : marketAppeal >= 20 ? 'MEDIUM' : 'LOW'
      };
    });
    
    // Sort by market appeal and price
    analysis.sort((a, b) => {
      if (b.market_appeal_score !== a.market_appeal_score) {
        return b.market_appeal_score - a.market_appeal_score;
      }
      return b.price - a.price;
    });
    
    console.log(`📄 Found ${analysis.length} premium inactive seed products\n`);
    
    // Show top candidates
    console.log('🎯 TOP 20 CANDIDATES FOR SUPER AGENT #2:\n');
    analysis.slice(0, 20).forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   💰 £${product.price} | Appeal: ${product.market_appeal_score} | Potential: ${product.activation_potential}`);
      console.log(`   🔍 Missing: ${product.missing_fields || 'None'}`);
      console.log('');
    });
    
    // Create CSV for Super Agent #2
    const csvContent = [
      'product_name,price,missing_fields,missing_count,market_appeal_score,activation_potential,description_length,has_image,has_seed_attributes',
      ...analysis.map(p => 
        `"${p.name}",${p.price},"${p.missing_fields}",${p.missing_count},${p.market_appeal_score},"${p.activation_potential}",${p.description_length},${p.has_image},${p.has_seed_attributes}`
      )
    ].join('\n');
    
    writeFileSync('docs/premium-inactive-seeds-for-agent2.csv', csvContent);
    
    // Statistics
    const highPotential = analysis.filter(p => p.activation_potential === 'HIGH').length;
    const mediumPotential = analysis.filter(p => p.activation_potential === 'MEDIUM').length;
    const avgPrice = analysis.reduce((sum, p) => sum + p.price, 0) / analysis.length;
    
    console.log('📊 Summary for Super Agent #2:');
    console.log(`   Total premium inactive products: ${analysis.length}`);
    console.log(`   High activation potential: ${highPotential}`);
    console.log(`   Medium activation potential: ${mediumPotential}`);
    console.log(`   Average price: £${avgPrice.toFixed(2)}`);
    console.log(`   Recommended first batch: Top ${Math.min(10, highPotential)} high-potential products`);
    
    console.log(`\n📁 Generated: docs/premium-inactive-seeds-for-agent2.csv`);
    console.log('\n🚀 Ready to send to Super Agent #2!');
    
  } catch (err) {
    console.error('❌ Export failed:', err);
  }
}

// Run the export
exportPremiumInactiveSeeds().catch(console.error);
