/**
 * Gemini AI Provider
 * 
 * Wraps your existing Gemini implementation with the unified interface
 * Optimized for creative content generation
 */

import { BaseProvider, ProviderConfig, ProviderCapabilities } from './BaseProvider';
import { AIRequest, AIRequestOptions } from '../types/AIRequest';
import { AIResponse, AIProviderStatus } from '../types/AIResponse';

interface GeminiConfig extends ProviderConfig {
  api_url?: string;
  model?: string;
}

interface GeminiRequestBody {
  contents: Array<{
    parts: Array<{
      text: string;
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    topK?: number;
    topP?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export class GeminiProvider extends BaseProvider {
  private apiUrl: string;
  private model: string;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  
  constructor(config: GeminiConfig) {
    const capabilities: ProviderCapabilities = {
      supports_streaming: false, // Gemini streaming is more complex
      supports_images: true,
      supports_function_calling: true,
      supports_system_prompts: true,
      max_context_length: 30720, // Gemini 1.5 Flash context length
      supported_formats: ['text', 'markdown', 'html']
    };
    
    super('gemini', config, capabilities);
    
    this.apiUrl = config.api_url || `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${config.api_key}`;
    this.model = config.model || 'gemini-1.5-flash-latest';
  }
  
  async processRequest(request: AIRequest, options?: AIRequestOptions): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.valid) {
        throw new Error(`Invalid request: ${validation.errors.join(', ')}`);
      }
      
      // Handle rate limiting
      await this.handleRateLimit();
      
      // Prepare request body
      const requestBody = this.prepareRequestBody(request, options);
      
      // Make API call with retry logic
      const response = await this.retryWithBackoff(async () => {
        return this.makeAPICall(requestBody);
      });
      
      // Track usage
      this.requestCount++;
      this.lastRequestTime = Date.now();
      
      // Extract content
      const content = this.extractContent(response);
      
      // Calculate cost (Gemini Flash pricing: free tier, then ~$0.075 per 1M input tokens)
      const totalTokens = response.usageMetadata?.totalTokenCount || 0;
      const cost = (totalTokens / 1000000) * 0.075; // Rough estimate
      
      return {
        content: content,
        success: true,
        provider: this.name,
        model: this.model,
        tokens_used: totalTokens,
        cost: cost,
        processing_time: Date.now() - startTime,
        confidence_score: this.calculateConfidenceScore(response),
        request_id: request.request_id,
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('Gemini API error:', error);
      
      return {
        content: '',
        success: false,
        provider: this.name,
        processing_time: Date.now() - startTime,
        timestamp: new Date(),
        error: {
          code: 'GEMINI_API_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }
      };
    }
  }
  
  async processStreamingRequest(request: AIRequest, options?: AIRequestOptions): Promise<ReadableStream<string>> {
    // Gemini doesn't support streaming in the same way, so we'll simulate it
    return new ReadableStream({
      async start(controller) {
        try {
          const response = await this.processRequest(request, options);
          
          if (response.success && response.content) {
            // Simulate streaming by chunking the response
            const words = response.content.split(' ');
            for (let i = 0; i < words.length; i++) {
              const chunk = words[i] + (i < words.length - 1 ? ' ' : '');
              controller.enqueue(chunk);
              
              // Small delay to simulate streaming
              await new Promise(resolve => setTimeout(resolve, 50));
            }
          }
          
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }
  
  async checkHealth(): Promise<AIProviderStatus> {
    try {
      // Simple health check with minimal request
      const testRequest: GeminiRequestBody = {
        contents: [{
          parts: [{ text: 'Hello' }]
        }],
        generationConfig: {
          maxOutputTokens: 10
        }
      };
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testRequest)
      });
      
      return {
        provider: this.name,
        available: response.ok,
        rate_limited: response.status === 429,
        error_rate: 0, // Would track this over time
        average_response_time: 0, // Would track this over time
        last_check: new Date()
      };
      
    } catch (error) {
      return {
        provider: this.name,
        available: false,
        rate_limited: false,
        error_rate: 1,
        average_response_time: 0,
        last_check: new Date()
      };
    }
  }
  
  async getUsageStats(): Promise<{
    requests_today: number;
    tokens_today: number;
    cost_today: number;
    rate_limit_remaining: number;
  }> {
    // In production, this would query a usage tracking database
    return {
      requests_today: this.requestCount,
      tokens_today: 0, // Would track this
      cost_today: 0, // Would track this
      rate_limit_remaining: 15 // Gemini free tier: 15 requests per minute
    };
  }
  
  async estimateCost(request: AIRequest): Promise<number> {
    // Rough estimation based on content length
    const estimatedTokens = Math.ceil(request.content.length / 4); // ~4 chars per token
    return (estimatedTokens / 1000000) * 0.075; // Gemini Flash pricing
  }
  
  private prepareRequestBody(request: AIRequest, options?: AIRequestOptions): GeminiRequestBody {
    // Build the prompt with context
    const fullPrompt = this.buildPromptWithContext(request);
    
    const requestBody: GeminiRequestBody = {
      contents: [{
        parts: [{ text: fullPrompt }]
      }],
      generationConfig: {
        temperature: options?.temperature || this.config.temperature || 0.7,
        topK: 40,
        topP: options?.top_p || 0.95,
        maxOutputTokens: options?.max_tokens || this.config.max_tokens || 2048,
        stopSequences: options?.stop_sequences
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };
    
    return requestBody;
  }
  
  private buildPromptWithContext(request: AIRequest): string {
    const context = request.context;
    let prompt = '';
    
    // Add system-like instructions based on request type
    switch (request.type) {
      case 'blog_content':
        prompt = `Generate a compelling blog post about: ${request.content}\n\n`;
        if (context?.category) {
          prompt += `Category: ${context.category}\n`;
        }
        if (context?.target_audience) {
          prompt += `Target audience: ${context.target_audience}\n`;
        }
        prompt += `\nRequirements:
- Write in an engaging, informative style
- Include relevant keywords naturally
- Structure with clear headings and paragraphs
- Ensure content is accurate and helpful
- Make it SEO-friendly\n\n`;
        break;
        
      case 'product_description':
        prompt = `Generate a compelling product description for: ${request.content}\n\n`;
        if (context?.category) {
          prompt += `Product category: ${context.category}\n`;
        }
        prompt += `\nRequirements:
- Highlight key benefits and features
- Use persuasive, marketing-oriented language
- Include relevant keywords for SEO
- Keep it concise but informative
- Focus on what makes this product special\n\n`;
        break;
        
      case 'newsletter_content':
        prompt = `Create engaging newsletter content about: ${request.content}\n\n`;
        prompt += `\nRequirements:
- Write in a friendly, conversational tone
- Include a compelling subject line
- Structure with clear sections
- Add call-to-action elements
- Make it engaging for email readers\n\n`;
        break;
        
      default:
        prompt = request.content;
    }
    
    // Add business context
    if (context?.business_type === 'cannabis') {
      prompt += `\nNote: This is for a cannabis/CBD business. Ensure all content is compliant with UK cannabis laws and regulations. Focus on legal aspects like CBD products, hemp-derived products, and educational content.`;
    }
    
    // Add brand voice
    if (context?.brand_voice) {
      prompt += `\n\nBrand voice: Write in a ${context.brand_voice.tone} tone. ${context.brand_voice.personality}`;
    }
    
    // Add format requirements
    if (context?.format === 'html') {
      prompt += `\n\nFormat the response as clean HTML with appropriate tags (p, h2, h3, ul, li, etc.).`;
    } else if (context?.format === 'markdown') {
      prompt += `\n\nFormat the response in Markdown.`;
    }
    
    return prompt;
  }
  
  private async makeAPICall(requestBody: GeminiRequestBody): Promise<GeminiResponse> {
    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    return response.json();
  }
  
  private extractContent(response: GeminiResponse): string {
    if (!response.candidates || response.candidates.length === 0) {
      throw new Error('No candidates in Gemini response');
    }
    
    const candidate = response.candidates[0];
    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      throw new Error('No content in Gemini response');
    }
    
    let content = candidate.content.parts[0].text;
    
    // Clean up the content (remove any unwanted formatting)
    content = content.trim();
    
    // Ensure HTML formatting if the content looks like it should be HTML
    if (content.includes('<p>') || content.includes('<h')) {
      // Content is already HTML formatted
      return content;
    } else if (content.includes('\n\n')) {
      // Convert double newlines to paragraph tags
      content = content
        .split('\n\n')
        .map(para => para.trim() ? `<p>${para.trim()}</p>` : '')
        .join('');
    }
    
    return content;
  }
  
  private calculateConfidenceScore(response: GeminiResponse): number {
    if (!response.candidates || response.candidates.length === 0) {
      return 0;
    }
    
    const candidate = response.candidates[0];
    
    // Check finish reason
    if (candidate.finishReason !== 'STOP') {
      return 0.5; // Lower confidence for incomplete responses
    }
    
    // Check safety ratings
    const hasHighRiskRatings = candidate.safetyRatings?.some(
      rating => rating.probability === 'HIGH' || rating.probability === 'MEDIUM'
    );
    
    if (hasHighRiskRatings) {
      return 0.6; // Lower confidence for potentially risky content
    }
    
    // Check content length
    const contentLength = candidate.content?.parts?.[0]?.text?.length || 0;
    if (contentLength < 50) {
      return 0.7; // Lower confidence for very short responses
    }
    
    return 0.95; // High confidence for complete, safe, substantial responses
  }
  
  protected async handleRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    // Gemini free tier: 15 requests per minute
    const minInterval = 4000; // 4 seconds between requests to be safe
    
    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
