# Test Accounts for Social Media Publishing

## Overview
This document outlines the test accounts needed for developing and testing the Social Media Publishing System. Using dedicated test accounts is critical to avoid affecting production accounts and to ensure compliance with platform policies.

## Test Account Requirements

### General Requirements
- All test accounts should:
  - Be clearly labeled as test/development accounts
  - Not be connected to production business accounts
  - Have minimal followers/connections
  - Use dedicated email addresses for registration
  - Have appropriate age restrictions set (21+)

### Platform-Specific Requirements

#### Instagram Test Account
- **Account Type**: Business Account
- **Requirements**:
  - Must be connected to a Facebook Page
  - Must be connected to a Facebook Business Account
  - Content must comply with Instagram's policies on restricted products
- **Setup Steps**:
  1. Create a personal Instagram account
  2. Convert to Professional account (Business)
  3. Connect to Facebook Page
  4. Set up Instagram Graph API access

#### Facebook Test Account
- **Account Type**: Page
- **Requirements**:
  - Must be managed by a Facebook Business Account
  - Must have appropriate age restrictions (21+)
  - Must comply with Facebook's policies on restricted products
- **Setup Steps**:
  1. Create a Facebook Page for testing
  2. Set appropriate age restrictions
  3. Configure as a "Business" page
  4. Connect to Business Manager

#### Twitter Test Account
- **Account Type**: Standard Account
- **Requirements**:
  - Must have developer account access
  - Should be set to private during testing
  - Must comply with Twitter's policies
- **Setup Steps**:
  1. Create a Twitter account
  2. Apply for developer access
  3. Create a developer project and app
  4. Configure user authentication settings

#### TikTok Test Account
- **Account Type**: Business Account
- **Requirements**:
  - Must be registered as a Business account
  - Must comply with TikTok's strict policies on cannabis content
  - Must have age verification enabled
- **Setup Steps**:
  1. Create a TikTok account
  2. Switch to Business account
  3. Apply for TikTok for Business API access
  4. Create developer app

## Test Account Credentials

### Email Addresses
- Instagram Test: `<EMAIL>`
- Facebook Test: `<EMAIL>`
- Twitter Test: `<EMAIL>`
- TikTok Test: `<EMAIL>`

### Account Information
> **IMPORTANT**: Actual credentials should never be stored in code or documentation. Use environment variables or a secure credential manager.

```env
# Test Environment Only - DO NOT USE IN PRODUCTION
# These values should be stored in a .env.test file that is not committed to version control

# Instagram Test
VITE_INSTAGRAM_TEST_APP_ID=test_app_id
VITE_INSTAGRAM_TEST_APP_SECRET=test_app_secret
VITE_INSTAGRAM_TEST_ACCOUNT_NAME=BitsnBongs_Test

# Facebook Test
VITE_FACEBOOK_TEST_APP_ID=test_app_id
VITE_FACEBOOK_TEST_APP_SECRET=test_app_secret
VITE_FACEBOOK_TEST_PAGE_ID=test_page_id

# Twitter Test
VITE_TWITTER_TEST_API_KEY=test_api_key
VITE_TWITTER_TEST_API_SECRET=test_api_secret
VITE_TWITTER_TEST_BEARER_TOKEN=test_bearer_token
VITE_TWITTER_TEST_ACCESS_TOKEN=test_access_token
VITE_TWITTER_TEST_ACCESS_SECRET=test_access_secret

# TikTok Test
VITE_TIKTOK_TEST_CLIENT_KEY=test_client_key
VITE_TIKTOK_TEST_CLIENT_SECRET=test_client_secret
```

## Test Content Guidelines

### Approved Test Content
- Use placeholder images for cannabis products
- Use generic product names and descriptions
- Avoid explicit drug references
- Include appropriate disclaimers
- Use age-gating hashtags when applicable

### Sample Test Posts

#### Instagram Test Post
```
📱 Testing our new product showcase! #CBD #Wellness #21Plus #TestPost

This is a test post from our development environment. Please ignore.

[Placeholder Image]
```

#### Facebook Test Post
```
🧪 Testing our new social media system! This post is from our development environment and should be ignored.

Age-restricted content: This page is only visible to users 21+.

[Placeholder Image]
```

#### Twitter Test Post
```
🧪 Testing our new social publishing system! Please ignore this post. #TestPost #DevelopmentOnly

[Placeholder Image]
```

#### TikTok Test Post
```
🧪 Testing video upload capabilities. This is a development test. #TestVideo #DevelopmentOnly

[Test Video]
```

## Testing Scenarios

### Authentication Testing
- Test OAuth flow for each platform
- Test token refresh mechanism
- Test handling of expired tokens
- Test multi-account support

### Content Publishing Testing
- Test text-only posts
- Test image uploads (single and multiple)
- Test video uploads
- Test scheduling functionality
- Test error handling

### Compliance Testing
- Test content filtering for restricted terms
- Test age-gating functionality
- Test platform-specific content policies

## Test Account Management

### Account Creation Process
1. Request approval for test account creation
2. Register accounts using company email addresses
3. Document all account details securely
4. Configure developer applications
5. Set up appropriate permissions

### Account Security
- Use strong, unique passwords
- Enable two-factor authentication
- Restrict access to authorized developers only
- Regularly audit account access
- Rotate API credentials periodically

### Account Cleanup
- Delete test posts after testing
- Reset account state between test runs
- Document any persistent test data
- Regularly review account activity

## Next Steps
1. Request approval for test account creation
2. Set up developer accounts on each platform
3. Create necessary applications and obtain API credentials
4. Configure test environment with credentials
5. Verify API access for each platform
