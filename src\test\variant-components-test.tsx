import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { customSupabase } from '@/integrations/supabase/customClient';
import {
  VariantBadge,
  VariantsDialog,
  VariantForm,
  OptionDefinitionsManager,
  BulkVariantGenerator
} from '@/components/admin/product-variants';

/**
 * This is a test component to verify that all variant-related components
 * are working correctly. It provides a simple UI to test each component.
 *
 * To use this component:
 * 1. Import it into a page
 * 2. Pass a valid product ID
 * 3. Interact with the buttons to test each component
 */
export function VariantComponentsTest({ productId, productName }: { productId: string; productName: string }) {
  const [isVariantsDialogOpen, setIsVariantsDialogOpen] = useState(false);
  const [isVariantFormOpen, setIsVariantFormOpen] = useState(false);
  const [isBulkGeneratorOpen, setIsBulkGeneratorOpen] = useState(false);
  const [isOptionManagerOpen, setIsOptionManagerOpen] = useState(false);
  const [optionDefinitions, setOptionDefinitions] = useState<Record<string, string[]>>({});
  const [variantCount, setVariantCount] = useState<number>(0);

  // Fetch variant count
  const fetchVariantCount = async () => {
    try {
      const { count, error } = await customSupabase
        .from('product_variants')
        .select('*', { count: 'exact', head: true })
        .eq('product_id', productId);

      if (error) {
        console.error('Error fetching variant count:', error);
        return;
      }

      setVariantCount(count || 0);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  // Fetch option definitions
  const fetchOptionDefinitions = async () => {
    try {
      const { data, error } = await customSupabase
        .from('products')
        .select('option_definitions')
        .eq('id', productId)
        .single();

      if (error) {
        console.error('Error fetching option definitions:', error);
        return;
      }

      setOptionDefinitions(data?.option_definitions || {});
    } catch (error) {
      console.error('Error:', error);
    }
  };

  // Handle option definitions change
  const handleOptionDefinitionsChange = (newOptionDefinitions: Record<string, string[]>) => {
    setOptionDefinitions(newOptionDefinitions);

    // Save to database
    customSupabase
      .from('products')
      .update({ option_definitions: newOptionDefinitions })
      .eq('id', productId)
      .then(({ error }) => {
        if (error) {
          console.error('Error updating option definitions:', error);
        } else {
          console.log('Option definitions updated successfully');
        }
      });
  };

  // Handle success (refresh data)
  const handleSuccess = () => {
    fetchVariantCount();
    fetchOptionDefinitions();
  };

  // Initial data fetch
  React.useEffect(() => {
    if (productId) {
      fetchVariantCount();
      fetchOptionDefinitions();
    }
  }, [productId]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Variant Components Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                Product: <strong>{productName}</strong> (ID: {productId})
              </p>
              <p className="text-sm text-muted-foreground mb-4">
                Variant Count: <VariantBadge count={variantCount} />
              </p>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button
                variant="outline"
                onClick={() => setIsVariantsDialogOpen(true)}
              >
                Test Variants Dialog
              </Button>

              <Button
                variant="outline"
                onClick={() => setIsVariantFormOpen(true)}
              >
                Test Variant Form
              </Button>

              <Button
                variant="outline"
                onClick={() => setIsBulkGeneratorOpen(true)}
              >
                Test Bulk Generator
              </Button>

              <Button
                variant="outline"
                onClick={() => setIsOptionManagerOpen(true)}
              >
                Test Option Definitions Manager
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Option Definitions Manager */}
      {isOptionManagerOpen && (
        <Card>
          <CardHeader>
            <CardTitle>Option Definitions Manager</CardTitle>
          </CardHeader>
          <CardContent>
            <OptionDefinitionsManager
              optionDefinitions={optionDefinitions}
              onChange={handleOptionDefinitionsChange}
            />
            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setIsOptionManagerOpen(false)}
              >
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Variants Dialog */}
      <VariantsDialog
        isOpen={isVariantsDialogOpen}
        onClose={() => setIsVariantsDialogOpen(false)}
        productId={productId}
        productName={productName}
        onEdit={() => {
          setIsVariantsDialogOpen(false);
          setIsVariantFormOpen(true);
        }}
        onAdd={() => {
          setIsVariantsDialogOpen(false);
          setIsVariantFormOpen(true);
        }}
        onSuccess={handleSuccess}
      />

      {/* Variant Form */}
      <VariantForm
        isOpen={isVariantFormOpen}
        onClose={() => setIsVariantFormOpen(false)}
        productId={productId}
        productName={productName}
        productPrice={10.00} // Default test price
        variant={null}
        onSuccess={handleSuccess}
      />

      {/* Bulk Variant Generator */}
      <BulkVariantGenerator
        isOpen={isBulkGeneratorOpen}
        onClose={() => setIsBulkGeneratorOpen(false)}
        productId={productId}
        productName={productName}
        basePrice={0}
        onSuccess={handleSuccess}
      />
    </div>
  );
}
