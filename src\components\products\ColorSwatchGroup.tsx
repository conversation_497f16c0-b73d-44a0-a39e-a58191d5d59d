import React from 'react';
import { ColorSwatch } from './ColorSwatch';

interface ColorSwatchGroupProps {
  colors: string[];
  selectedColor: string | null;
  onSelectColor: (color: string) => void;
  disabledColors?: string[];
}

export const ColorSwatchGroup: React.FC<ColorSwatchGroupProps> = ({
  colors,
  selectedColor,
  onSelectColor,
  disabledColors = [],
}) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Color
      </label>
      <div className="flex flex-wrap gap-2 relative z-100">
        {colors.map((color) => (
          <ColorSwatch
            key={color}
            colorName={color}
            selected={selectedColor === color}
            disabled={disabledColors.includes(color)}
            onClick={() => onSelectColor(color)}
          />
        ))}
      </div>
      {selectedColor && (
        <p className="text-sm text-gray-600 mt-1">
          Selected: <span className="font-medium">{selectedColor}</span>
        </p>
      )}
    </div>
  );
};
