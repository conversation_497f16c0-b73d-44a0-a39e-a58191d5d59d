-- Migration: Add product variants table and related structures
-- Description: Creates tables and relationships for variant-based product system

-- Create extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create product_variants table
CREATE TABLE IF NOT EXISTS product_variants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  variant_name TEXT NOT NULL,
  sku TEXT,
  price DECIMAL(10, 2) NOT NULL,
  sale_price DECIMAL(10, 2),
  stock_quantity INTEGER DEFAULT 0,
  in_stock BOOLEAN DEFAULT TRUE,
  image TEXT,
  option_combination JSONB,
  external_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX IF NOT EXISTS idx_product_variants_sku ON product_variants(sku);
CREATE INDEX IF NOT EXISTS idx_product_variants_external_id ON product_variants(external_id);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp
BEFORE UPDATE ON product_variants
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add function to get variant price
CREATE OR REPLACE FUNCTION get_variant_price(product_id UUID, option_values TEXT[])
RETURNS DECIMAL(10, 2) AS $$
DECLARE
  variant_price DECIMAL(10, 2);
BEGIN
  SELECT price INTO variant_price
  FROM product_variants
  WHERE product_variants.product_id = get_variant_price.product_id
  AND product_variants.option_combination @> jsonb_build_object(
    'values', to_jsonb(option_values)
  );
  
  RETURN COALESCE(variant_price, (SELECT price FROM products WHERE id = product_id));
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE product_variants IS 'Stores variant-specific information for products';
COMMENT ON COLUMN product_variants.option_combination IS 'JSON object containing option values for this variant';
COMMENT ON FUNCTION get_variant_price IS 'Gets the price for a specific variant based on option values';
