/**
 * Mock MCPError<PERSON><PERSON><PERSON>
 */

export enum MCPErrorType {
  CONNECTION = 'connection',
  NAVIGATION = 'navigation',
  ELEMENT = 'element',
  TIMEOUT = 'timeout',
  EVALUATION = 'evaluation',
  UNKNOWN = 'unknown'
}

export class MCPError extends Error {
  type: MCPErrorType;
  originalError: Error | null;
  retryable: boolean;
  context: Record<string, any>;

  constructor(
    message: string,
    type: MCPErrorType = MCPErrorType.UNKNOWN,
    originalError: Error | null = null,
    retryable: boolean = false,
    context: Record<string, any> = {}
  ) {
    super(message);
    this.name = 'MCPError';
    this.type = type;
    this.originalError = originalError;
    this.retryable = retryable;
    this.context = context;
  }
}

export class MCPErrorHandler {
  constructor(config = {}) {
    // Mock constructor
  }

  classifyError(error: Error) {
    return new MCPError(error.message, MCPErrorType.UNKNOWN, error, false);
  }

  handleError(error: Error, context = {}) {
    return this.classifyError(error);
  }

  async withRetry<T>(fn: () => Promise<T>, context = {}): Promise<T> {
    // Just execute the function without retries in the mock
    return fn();
  }
}
