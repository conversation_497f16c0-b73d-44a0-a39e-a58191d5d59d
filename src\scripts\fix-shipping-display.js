// Script to ensure inactive shipping methods don't appear in checkout
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixShippingMethods() {
  console.log('🔍 Checking shipping methods...');
  
  // 1. Get all shipping methods
  const { data: methods, error } = await supabase
    .from('shipping_methods')
    .select('*');
    
  if (error) {
    console.error('Error fetching shipping methods:', error);
    return;
  }
  
  console.log(`Found ${methods.length} total shipping methods`);
  
  // 2. Log active vs inactive methods
  const activeMethods = methods.filter(m => m.is_active);
  const inactiveMethods = methods.filter(m => !m.is_active);
  
  console.log(`Active methods: ${activeMethods.length}`);
  console.log(`Inactive methods: ${inactiveMethods.length}`);
  
  // 3. Force update inactive methods to ensure they're properly marked
  if (inactiveMethods.length > 0) {
    console.log('Updating inactive methods to ensure they are properly marked as inactive...');
    
    for (const method of inactiveMethods) {
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', method.id);
        
      if (updateError) {
        console.error(`Error updating method ${method.id}:`, updateError);
      } else {
        console.log(`✅ Updated method: ${method.name} (ID: ${method.id})`);
      }
    }
  }
  
  // 4. Verify the database state after updates
  const { data: verifyMethods, error: verifyError } = await supabase
    .from('shipping_methods')
    .select('*');
    
  if (verifyError) {
    console.error('Error verifying shipping methods:', verifyError);
    return;
  }
  
  const activeAfterFix = verifyMethods.filter(m => m.is_active);
  console.log(`After fix: ${activeAfterFix.length} active methods`);
  console.log('Active methods:');
  activeAfterFix.forEach(m => {
    console.log(`- ${m.name} (ID: ${m.id})`);
  });
  
  console.log('\n✅ Shipping methods verification complete!');
  console.log('To ensure changes take effect in the checkout:');
  console.log('1. Clear your browser cache');
  console.log('2. Restart the development server');
}

// Run the function
fixShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
    process.exit(1);
  });
