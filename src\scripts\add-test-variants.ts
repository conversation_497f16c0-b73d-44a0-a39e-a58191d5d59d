import { customSupabase } from '../integrations/supabase/customClient';

/**
 * This script adds test option definitions and variants to an existing product.
 * It can be used to quickly create test data for the variant system.
 *
 * To use:
 * 1. Update the productId with a valid product ID from your database
 * 2. Modify the option definitions and variants as needed
 * 3. Run the script with: npx ts-node src/scripts/add-test-variants.ts
 */

// Configuration - Update these values
const productId = 'a85e3cab-0345-4de1-a29d-f411d065dc37'; // Santa Cruz Shredder -4pc Medium
const productName = 'Santa Cruz Shredder -4pc Medium'; // For reference only

// Example option definitions with special colors
const optionDefinitions = {
  'Colour': {
    name: 'Colour',
    display_type: 'swatch',
    values: ['Black', 'Purple', 'Green', 'Blue', 'Grey', 'Gold', 'Rasta']
  },
  'Size': {
    name: 'Size',
    display_type: 'dropdown',
    values: ['Small', 'Medium', 'Large']
  }
};

// Example variants to create with special colors
const variants = [
  // Size: Small with various colors
  {
    variant_name: `${productName} - Small/Black`,
    option_combination: { 'Size': 'Small', 'Colour': 'Black' },
    price: 75.00,
    stock_quantity: 10
  },
  {
    variant_name: `${productName} - Small/Purple`,
    option_combination: { 'Size': 'Small', 'Colour': 'Purple' },
    price: 80.00,
    stock_quantity: 8
  },
  {
    variant_name: `${productName} - Small/Green`,
    option_combination: { 'Size': 'Small', 'Colour': 'Green' },
    price: 85.00,
    stock_quantity: 5
  },
  {
    variant_name: `${productName} - Small/Blue`,
    option_combination: { 'Size': 'Small', 'Colour': 'Blue' },
    price: 75.00,
    stock_quantity: 10
  },
  {
    variant_name: `${productName} - Small/Grey`,
    option_combination: { 'Size': 'Small', 'Colour': 'Grey' },
    price: 80.00,
    stock_quantity: 8
  },
  {
    variant_name: `${productName} - Small/Gold`,
    option_combination: { 'Size': 'Small', 'Colour': 'Gold' },
    price: 90.00,
    stock_quantity: 5
  },
  {
    variant_name: `${productName} - Small/Rasta`,
    option_combination: { 'Size': 'Small', 'Colour': 'Rasta' },
    price: 85.00,
    stock_quantity: 5
  },

  // Size: Medium with various colors
  {
    variant_name: `${productName} - Medium/Black`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Black' },
    price: 80.00,
    stock_quantity: 15
  },
  {
    variant_name: `${productName} - Medium/Purple`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Purple' },
    price: 85.00,
    stock_quantity: 7
  },
  {
    variant_name: `${productName} - Medium/Green`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Green' },
    price: 90.00,
    stock_quantity: 3
  },
  {
    variant_name: `${productName} - Medium/Blue`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Blue' },
    price: 80.00,
    stock_quantity: 15
  },
  {
    variant_name: `${productName} - Medium/Grey`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Grey' },
    price: 85.00,
    stock_quantity: 7
  },
  {
    variant_name: `${productName} - Medium/Gold`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Gold' },
    price: 95.00,
    stock_quantity: 3
  },
  {
    variant_name: `${productName} - Medium/Rasta`,
    option_combination: { 'Size': 'Medium', 'Colour': 'Rasta' },
    price: 90.00,
    stock_quantity: 3
  },

  // Size: Large with various colors
  {
    variant_name: `${productName} - Large/Black`,
    option_combination: { 'Size': 'Large', 'Colour': 'Black' },
    price: 85.00,
    stock_quantity: 12
  },
  {
    variant_name: `${productName} - Large/Purple`,
    option_combination: { 'Size': 'Large', 'Colour': 'Purple' },
    price: 90.00,
    stock_quantity: 6
  },
  {
    variant_name: `${productName} - Large/Green`,
    option_combination: { 'Size': 'Large', 'Colour': 'Green' },
    price: 95.00,
    stock_quantity: 4
  },
  {
    variant_name: `${productName} - Large/Blue`,
    option_combination: { 'Size': 'Large', 'Colour': 'Blue' },
    price: 85.00,
    stock_quantity: 12
  },
  {
    variant_name: `${productName} - Large/Grey`,
    option_combination: { 'Size': 'Large', 'Colour': 'Grey' },
    price: 90.00,
    stock_quantity: 6
  },
  {
    variant_name: `${productName} - Large/Gold`,
    option_combination: { 'Size': 'Large', 'Colour': 'Gold' },
    price: 100.00,
    stock_quantity: 4
  },
  {
    variant_name: `${productName} - Large/Rasta`,
    option_combination: { 'Size': 'Large', 'Colour': 'Rasta' },
    price: 95.00,
    stock_quantity: 4
  }
];

/**
 * Adds option definitions to a product
 */
async function addOptionDefinitions() {
  try {
    const { data, error } = await customSupabase
      .from('products')
      .update({ option_definitions: optionDefinitions })
      .eq('id', productId)
      .select();

    if (error) {
      throw error;
    }

    console.log('✅ Added option definitions to product:', data);
    return true;
  } catch (error) {
    console.error('❌ Error adding option definitions:', error);
    return false;
  }
}

/**
 * Adds variants to a product
 */
async function addVariants() {
  try {
    // Prepare variants data
    const variantsToCreate = variants.map(variant => ({
      product_id: productId,
      variant_name: variant.variant_name,
      sku: `${productId.substring(0, 6)}-${variant.option_combination.Size?.substring(0, 1)}${variant.option_combination.Color?.substring(0, 1)}${variant.option_combination['Pack Size']?.replace(/\D/g, '')}`,
      price: variant.price,
      sale_price: null,
      stock_quantity: variant.stock_quantity,
      in_stock: true,
      image: null,
      option_combination: variant.option_combination,
      is_active: true,
      external_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data, error } = await customSupabase
      .from('product_variants')
      .insert(variantsToCreate)
      .select();

    if (error) {
      throw error;
    }

    console.log(`✅ Added ${variantsToCreate.length} variants to product:`, data);
    return true;
  } catch (error) {
    console.error('❌ Error adding variants:', error);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 Adding test variants to product:', productId);

  // Step 1: Add option definitions
  const optionsAdded = await addOptionDefinitions();
  if (!optionsAdded) {
    console.error('❌ Failed to add option definitions. Aborting.');
    return;
  }

  // Step 2: Add variants
  const variantsAdded = await addVariants();
  if (!variantsAdded) {
    console.error('❌ Failed to add variants.');
    return;
  }

  console.log('✅ Successfully added test variants to product!');
}

// Run the script
main().catch(console.error);
