
import { useState, useEffect } from "react";
import { Product } from "@/types/database";

export function useProductFormState(initialProduct: Product | null) {
  // Clear localStorage on mount to prevent stale data
  useEffect(() => {
    if (!initialProduct) {
      console.log('Clearing localStorage product form data');
      localStorage.removeItem('productFormData');
    }
  }, [initialProduct]);

  // Initialize form data from initialProduct or default values
  const [formData, setFormData] = useState<Partial<Product>>(() => {
    // If we're editing an existing product, use that data
    if (initialProduct) {
      console.log('Using existing product data');
      return initialProduct;
    }

    console.log('Using default values for new product');

    // Default values for a new product
    return {
      name: "",
      slug: "",
      description: "",
      price: 0,
      sale_price: null,
      category_id: null,
      image: "",
      additional_images: [],
      in_stock: true,
      is_featured: false,
      is_new: true,
      is_best_seller: false,

      // Product options
      option_name1: "",
      option_type1: "",
      option_description1: "",
      option_name2: "",
      option_type2: "",
      option_description2: "",
      option_name3: "",
      option_type3: "",
      option_description3: "",

      // Additional information
      additional_info_title1: "",
      additional_info_description1: "",
      additional_info_title2: "",
      additional_info_description2: "",
      additional_info_title3: "",
      additional_info_description3: "",
    };
  });

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    // Don't save if we're editing an existing product
    if (!initialProduct) {
      localStorage.setItem('productFormData', JSON.stringify(formData));
    }
  }, [formData, initialProduct]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    console.log("handleChange called:", { name, value, type });

    // Handle numeric fields
    if (type === "number") {
      setFormData({
        ...formData,
        [name]: value === "" ? null : parseFloat(value),
      });
    } else {
      setFormData({ ...formData, [name]: value });
    }

    console.log("formData after update:", formData);
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    console.log("handleSwitchChange called:", { name, checked });
    setFormData({ ...formData, [name]: checked });
    console.log("formData after switch update:", { ...formData, [name]: checked });
  };

  const handleSelectChange = (name: string, value: any) => {
    // Log the value being set for debugging
    console.log(`handleSelectChange called: Setting ${name} to:`, value);

    // Skip update if the value hasn't changed to prevent loops
    if (formData[name] === value) {
      console.log(`Value for ${name} hasn't changed, skipping update`);
      return;
    }

    // Create a new form data object with the updated value
    const updatedFormData = { ...formData, [name]: value };

    // Special handling for option_definitions to ensure they're saved correctly
    if (name === 'option_definitions') {
      console.log('Updating option_definitions to:', value);
      // Make sure we're storing an object, not an array
      if (typeof value === 'object' && !Array.isArray(value)) {
        updatedFormData.option_definitions = value;
      } else {
        console.error('Invalid option_definitions format:', value);
      }
    }

    // Special handling for image fields to ensure they're updated correctly
    else if (name === 'image') {
      console.log('Updating main image to:', value, 'from previous:', formData.image);

      // If there was a previous main image and it's valid, add it to additional_images
      if (formData.image && formData.image !== value && formData.image.trim() !== '') {
        // Initialize additional_images array if it doesn't exist
        if (!updatedFormData.additional_images) {
          updatedFormData.additional_images = [];
        }

        // Only add the previous main image if it's not already in additional_images
        if (!updatedFormData.additional_images.includes(formData.image)) {
          updatedFormData.additional_images = [...updatedFormData.additional_images, formData.image];
          console.log('Added previous main image to additional_images:', formData.image);
        }
      }

      // If the new main image is also in additional_images, remove it to prevent duplication
      if (updatedFormData.additional_images && Array.isArray(updatedFormData.additional_images) &&
          updatedFormData.additional_images.includes(value)) {
        updatedFormData.additional_images = updatedFormData.additional_images.filter(img => img !== value);
        console.log('Removed new main image from additional_images to prevent duplication');
      }
    }

    // Update the form data state
    setFormData(updatedFormData);

    console.log("formData after select update:", updatedFormData);
  };

  return {
    formData,
    setFormData,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
  };
}
