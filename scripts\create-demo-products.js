// Simple script to create a demo products CSV file
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const csv = require('csv-parser');

// Input and output file paths
const inputFile = path.join(__dirname, '../data/catalog_products.csv');
const outputFile = path.join(__dirname, '../data/demo_products.csv');

// Number of products to extract
const numProducts = 10;

// Function to create a slug from a product name
function createSlug(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-|-$/g, '');
}

// Read the input CSV file
const products = [];
fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    // Only process rows with a name and price
    if (row.name && row.price) {
      products.push(row);
    }
  })
  .on('end', () => {
    console.log(`Read ${products.length} products from CSV`);
    
    // Select a subset of products
    const selectedProducts = products.slice(0, numProducts);
    
    // Create the output CSV header
    const header = 'id,name,slug,description,price,image,is_active,in_stock,sku,stock_quantity\n';
    
    // Create the output CSV content
    const rows = selectedProducts.map(product => {
      const id = uuidv4();
      const name = product.name.replace(/,/g, ''); // Remove commas from name
      const slug = createSlug(name);
      const description = (product.description || '').replace(/,/g, ''); // Remove commas from description
      const price = parseFloat(product.price) || 9.99;
      const image = ''; // Leave blank for manual upload
      const isActive = 'true';
      const inStock = 'true';
      const sku = product.sku || '';
      const stockQuantity = '100';
      
      return `${id},${name},${slug},${description},${price},${image},${isActive},${inStock},${sku},${stockQuantity}`;
    }).join('\n');
    
    // Write the output CSV file
    fs.writeFileSync(outputFile, header + rows);
    
    console.log(`Created demo products CSV with ${selectedProducts.length} products`);
    console.log(`Output file: ${outputFile}`);
  });
