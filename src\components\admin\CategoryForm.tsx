import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Category } from "@/types/database";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { X, Upload, Loader2 } from "lucide-react";
import { nanoid } from "nanoid";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CategoryFormProps {
  category?: Category | null;
  onSuccess: () => void;
  onCancel: () => void;
}

interface CategoryFormValues {
  name: string;
  slug: string;
  description?: string;
  parent_id?: string | null;
}

export function CategoryForm({ category, onSuccess, onCancel }: CategoryFormProps) {
  const queryClient = useQueryClient();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(
    category?.image || null
  );
  const [isImageUploading, setIsImageUploading] = useState(false);
  
  // Fetch all categories for parent selection
  const { data: categories } = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");

      if (error) throw error;
      return data as Category[];
    },
  });

  const { register, handleSubmit, formState: { errors, isSubmitting }, setValue, watch } = useForm<CategoryFormValues>({
    defaultValues: {
      name: category?.name || "",
      slug: category?.slug || "",
      description: category?.description || "",
      parent_id: category?.parent_id || null,
    }
  });
  
  const parentId = watch('parent_id');

  const createOrUpdateCategory = useMutation({
    mutationFn: async (formData: CategoryFormValues & { image?: string }) => {
      if (category) {
        // Update existing category
        const { error } = await supabase
          .from("categories")
          .update({
            name: formData.name,
            slug: formData.slug,
            description: formData.description,
            image: formData.image !== undefined ? formData.image : category.image,
            parent_id: formData.parent_id,
            updated_at: new Date().toISOString(),
          })
          .eq("id", category.id);

        if (error) throw error;
        return { ...category, ...formData };
      } else {
        // Create new category
        const { data, error } = await supabase
          .from("categories")
          .insert({
            name: formData.name,
            slug: formData.slug,
            description: formData.description,
            image: formData.image,
            parent_id: formData.parent_id,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      toast({
        title: category ? "Category updated" : "Category created",
        description: category
          ? "The category has been updated successfully."
          : "The category has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      onSuccess();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to ${category ? "update" : "create"} category: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setImagePreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleImageRemove = () => {
    setImageFile(null);
    setImagePreviewUrl(null);
  };

  const uploadImage = async (): Promise<string | null> => {
    if (!imageFile) {
      // If image was removed or no new image selected
      return imagePreviewUrl === null ? null : undefined;
    }

    setIsImageUploading(true);
    try {
      const fileExt = imageFile.name.split('.').pop();
      const filePath = `categories/${nanoid()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from("category-images")
        .upload(filePath, imageFile);

      if (uploadError) throw uploadError;
      
      // Add a small delay to ensure the image is processed and available
      await new Promise(resolve => setTimeout(resolve, 500));

      // Make sure to get the public URL after the upload is complete
      const { data: urlData } = supabase.storage
        .from("category-images")
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast({
        title: "Error uploading image",
        description: "There was a problem uploading your image.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsImageUploading(false);
    }
  };

  const onSubmit = async (formData: CategoryFormValues) => {
    // Generate slug if empty
    if (!formData.slug) {
      formData.slug = formData.name
        .toLowerCase()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "-");
    }

    const imageUrl = await uploadImage();
    if (imageUrl === null && imagePreviewUrl === null) {
      // Image was removed and no new image was selected
      createOrUpdateCategory.mutate({ ...formData, image: null });
    } else if (imageUrl) {
      // New image was uploaded
      createOrUpdateCategory.mutate({ ...formData, image: imageUrl });
    } else if (imageUrl === undefined) {
      // No change to image
      createOrUpdateCategory.mutate(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          {...register("name", { required: "Category name is required" })}
          placeholder="Category name"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="slug">Slug</Label>
        <Input
          id="slug"
          {...register("slug")}
          placeholder="category-slug (leave empty to auto-generate)"
        />
        <p className="text-xs text-muted-foreground">
          Used in URLs. Leave empty to auto-generate from name.
        </p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="parent_id">Parent Category (Optional)</Label>
        <Select
          value={parentId || 'none'}
          onValueChange={(value) => setValue('parent_id', value === 'none' ? null : value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a parent category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">None (Top-level Category)</SelectItem>
            {categories?.filter(c => c.id !== category?.id).map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>
                {cat.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Make this a subcategory by selecting a parent category.
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description (Optional)</Label>
        <Textarea
          id="description"
          {...register("description")}
          placeholder="Category description"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>Image (Optional)</Label>
        {imagePreviewUrl ? (
          <div className="relative w-40">
            <AspectRatio ratio={1 / 1}>
              <img
                src={imagePreviewUrl}
                alt="Category preview"
                className="rounded-md object-cover"
              />
            </AspectRatio>
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-0 right-0 h-6 w-6 rounded-full -mt-2 -mr-2"
              onClick={handleImageRemove}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center">
            <Label
              htmlFor="image-upload"
              className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-md border border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
            >
              <Upload className="h-6 w-6 text-gray-400" />
              <span className="mt-2 text-sm text-gray-500">Upload image</span>
              <Input
                id="image-upload"
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={handleFileChange}
              />
            </Label>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isImageUploading}
          className="min-w-[100px]"
        >
          {isSubmitting || isImageUploading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : category ? (
            "Update Category"
          ) : (
            "Create Category"
          )}
        </Button>
      </div>
    </form>
  );
}
