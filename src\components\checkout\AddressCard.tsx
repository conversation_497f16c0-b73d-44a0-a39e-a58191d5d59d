import { Address } from '@/hooks/useAddresses';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Home, MapPin, Phone, User, Edit, Check } from 'lucide-react';

interface AddressCardProps {
  address: Address;
  isSelected?: boolean;
  onSelect?: (address: Address) => void;
  onEdit?: (address: Address) => void;
}

export function AddressCard({ 
  address, 
  isSelected = false, 
  onSelect, 
  onEdit 
}: AddressCardProps) {
  return (
    <Card 
      className={`relative border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
        isSelected 
          ? 'border-sage-500 bg-sage-50' 
          : 'border-gray-200 hover:border-sage-300'
      }`}
      onClick={() => onSelect && onSelect(address)}
    >
      {address.is_default && (
        <Badge 
          className="absolute top-2 right-2 bg-green-100 text-green-800 border-green-200"
        >
          Default
        </Badge>
      )}
      
      {isSelected && (
        <div className="absolute top-2 left-2 bg-sage-500 text-white rounded-full p-1">
          <Check className="h-4 w-4" />
        </div>
      )}
      
      <CardContent className="pt-6 pb-4">
        <div className="space-y-3">
          <div className="flex items-start">
            <User className="h-4 w-4 text-gray-500 mr-2 mt-1" />
            <span className="font-medium">{address.full_name}</span>
          </div>
          
          <div className="flex items-start">
            <Home className="h-4 w-4 text-gray-500 mr-2 mt-1" />
            <div>
              <p>{address.street}</p>
              <p>
                {address.city}
                {address.state ? `, ${address.state}` : ''}
              </p>
              <p>{address.postal_code}</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <MapPin className="h-4 w-4 text-gray-500 mr-2" />
            <span>{address.country}</span>
          </div>
          
          <div className="flex items-center">
            <Phone className="h-4 w-4 text-gray-500 mr-2" />
            <span>{address.phone}</span>
          </div>
        </div>
        
        {onEdit && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="mt-4 text-sage-600 hover:text-sage-800 hover:bg-sage-50 p-0 h-auto"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(address);
            }}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
