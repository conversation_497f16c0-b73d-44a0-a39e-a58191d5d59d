import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateProductsTable() {
  console.log('Updating products table...');
  
  // Add new columns to the products table
  const { error: updateError } = await supabase.rpc('exec_sql', {
    sql: `
      -- Add columns for multiple images
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'additional_images'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN additional_images TEXT[];
        END IF;
      END
      $$;
      
      -- Add columns for product options
      DO $$
      BEGIN
        -- Option 1
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'option_name1'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN option_name1 TEXT,
          ADD COLUMN option_type1 TEXT,
          ADD COLUMN option_description1 TEXT;
        END IF;
        
        -- Option 2
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'option_name2'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN option_name2 TEXT,
          ADD COLUMN option_type2 TEXT,
          ADD COLUMN option_description2 TEXT;
        END IF;
        
        -- Option 3
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'option_name3'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN option_name3 TEXT,
          ADD COLUMN option_type3 TEXT,
          ADD COLUMN option_description3 TEXT;
        END IF;
      END
      $$;
      
      -- Add columns for additional information
      DO $$
      BEGIN
        -- Additional Info 1
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'additional_info_title1'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN additional_info_title1 TEXT,
          ADD COLUMN additional_info_description1 TEXT;
        END IF;
        
        -- Additional Info 2
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'additional_info_title2'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN additional_info_title2 TEXT,
          ADD COLUMN additional_info_description2 TEXT;
        END IF;
        
        -- Additional Info 3
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'additional_info_title3'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN additional_info_title3 TEXT,
          ADD COLUMN additional_info_description3 TEXT;
        END IF;
      END
      $$;
    `
  });
  
  if (updateError) {
    console.error('Error updating products table:', updateError);
    return;
  }
  
  console.log('Products table updated successfully');
}

// Run the migration
updateProductsTable()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
