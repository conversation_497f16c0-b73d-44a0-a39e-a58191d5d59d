import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';
import { customSupabase } from '@/integrations/supabase/customClient';
import { toast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ProductVariant, Product } from '@/types/database-with-variants';

// Using the imported customSupabase client

interface OptionDefinition {
  name: string;
  values: string[];
}

interface VariantFormProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  productPrice: number;
  variant?: ProductVariant;
  onSuccess: () => void;
}

export function VariantForm({
  isOpen,
  onClose,
  productId,
  productName,
  productPrice,
  variant,
  onSuccess
}: VariantFormProps) {
  const isEditing = !!variant?.id;

  const [formData, setFormData] = useState<Partial<ProductVariant>>({
    product_id: productId,
    variant_name: '',
    sku: '',
    price: productPrice || 0,
    price_adjustment: 0,
    sale_price: null,
    stock_quantity: 0,
    in_stock: true,
    image: null,
    option_combination: {},
    is_active: true,
    external_id: null,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [optionDefinitions, setOptionDefinitions] = useState<OptionDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load option definitions and set initial form data
  useEffect(() => {
    if (isOpen) {
      fetchOptionDefinitions();

      if (variant) {
        setFormData({
          ...variant,
          product_id: productId,
        });
      } else {
        // Reset form for new variant
        setFormData({
          product_id: productId,
          sku: '',
          price: productPrice || 0,
          price_adjustment: 0,
          sale_price: null,
          stock_quantity: 0,
          in_stock: true,
          image: null,
          option_combination: {},
          is_active: true,
        });
      }
    }
  }, [isOpen, productId, variant]);

  const fetchOptionDefinitions = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await customSupabase
        .from('products')
        .select('option_definitions')
        .eq('id', productId)
        .single();

      if (error) {
        throw error;
      }

      if (data && data.option_definitions) {
        // Convert from object to array format for easier use in the form
        const optionsArray = Object.entries(data.option_definitions).map(
          ([name, values]) => ({
            name,
            values: values as string[],
          })
        );
        setOptionDefinitions(optionsArray);

        // Initialize option_combination with first values if creating new variant
        if (!isEditing) {
          const initialCombination: Record<string, string> = {};
          optionsArray.forEach(option => {
            if (option.values.length > 0) {
              initialCombination[option.name] = option.values[0];
            }
          });
          setFormData(prev => ({
            ...prev,
            option_combination: initialCombination
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching option definitions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product options',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    // Handle numeric inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? null : parseFloat(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const handleOptionChange = (optionName: string, value: string) => {
    setFormData({
      ...formData,
      option_combination: {
        ...formData.option_combination,
        [optionName]: value,
      },
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Generate variant name from option combination if not provided
      const variantName = formData.variant_name ||
        Object.entries(formData.option_combination)
          .map(([key, value]) => `${value}`)
          .join(' / ');

      // Add required fields for new variants and remove UI-only fields
      const { price_adjustment, ...formDataWithoutPriceAdjustment } = formData;

      const dataToSubmit = {
        ...formDataWithoutPriceAdjustment,
        variant_name: variantName,
        // Add default values for required fields when creating a new variant
        external_id: formData.external_id || null,
        created_at: formData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      let result;

      if (isEditing && variant?.id) {
        // Update existing variant
        result = await customSupabase
          .from('product_variants')
          .update(dataToSubmit)
          .eq('id', variant.id);
      } else {
        // Create new variant
        result = await customSupabase
          .from('product_variants')
          .insert(dataToSubmit);
      }

      if (result.error) {
        throw result.error;
      }

      toast({
        title: 'Success',
        description: isEditing ? 'Variant updated successfully' : 'Variant created successfully',
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving variant:', error);
      toast({
        title: 'Error',
        description: 'Failed to save variant',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit' : 'Add'} Variant for {productName}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the variant details below.'
              : 'Create a new variant with specific options, price, and inventory.'}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Option Selections */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Options</h3>
              {optionDefinitions.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No options defined for this product. Add options in the product settings first.
                </p>
              ) : (
                optionDefinitions.map((option) => (
                  <div key={option.name} className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>{option.name}</Label>
                    </div>
                    <div>
                      <Select
                        value={formData.option_combination[option.name] || ''}
                        onValueChange={(value) => handleOptionChange(option.name, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={`Select ${option.name}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {option.values.map((value) => (
                            <SelectItem key={value} value={value}>
                              {value}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* SKU */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sku">SKU</Label>
              </div>
              <div>
                <Input
                  id="sku"
                  name="sku"
                  value={formData.sku || ''}
                  onChange={handleInputChange}
                  placeholder="SKU-VAR-001"
                />
              </div>
            </div>

            {/* Simple Price Adjustment */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="base_price_display">Base Product Price</Label>
              </div>
              <div>
                <input
                  id="base_price_display"
                  type="text"
                  value={`£${productPrice.toFixed(2)}`}
                  readOnly
                  className="flex h-10 w-full rounded-md border border-input bg-gray-50 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="price_adjustment">Price Adjustment (£)</Label>
                <p className="text-xs text-gray-500">Amount to add to base price</p>
              </div>
              <div>
                <input
                  id="price_adjustment"
                  name="price_adjustment"
                  type="number"
                  step="0.01"
                  value={formData.price_adjustment || 0}
                  onChange={(e) => {
                    try {
                      // Get the adjustment value
                      const value = e.target.value;
                      const adjustment = value === '' ? 0 : Number(value);

                      // Calculate the final price
                      const finalPrice = productPrice + adjustment;

                      // Update form data
                      setFormData({
                        ...formData,
                        price_adjustment: adjustment,
                        price: finalPrice
                      });
                    } catch (error) {
                      console.error("Error updating price adjustment:", error);
                    }
                  }}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="price">Final Price (£)</Label>
                <p className="text-xs text-gray-500">Base price + adjustment</p>
              </div>
              <div>
                <input
                  id="price"
                  name="price"
                  type="text"
                  value={`£${(productPrice + (formData.price_adjustment || 0)).toFixed(2)}`}
                  readOnly
                  className="flex h-10 w-full rounded-md border border-input bg-gray-50 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            {/* Sale Price */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sale_price">Sale Price (£) (Optional)</Label>
              </div>
              <div>
                <Input
                  id="sale_price"
                  name="sale_price"
                  type="number"
                  step="0.01"
                  value={formData.sale_price !== null ? formData.sale_price : ''}
                  onChange={handleInputChange}
                  placeholder="Leave blank if not on sale"
                />
              </div>
            </div>

            {/* Stock Quantity */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stock_quantity">Stock Quantity</Label>
              </div>
              <div>
                <Input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  value={formData.stock_quantity !== null ? formData.stock_quantity : ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* In Stock Switch */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="in_stock">In Stock</Label>
              </div>
              <div className="flex items-center">
                <Switch
                  id="in_stock"
                  checked={formData.in_stock}
                  onCheckedChange={(checked) => handleSwitchChange('in_stock', checked)}
                />
              </div>
            </div>

            {/* Is Active Switch */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="is_active">Active</Label>
              </div>
              <div className="flex items-center">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => handleSwitchChange('is_active', checked)}
                />
              </div>
            </div>

            {/* Image URL */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="image">Image URL (Optional)</Label>
              </div>
              <div>
                <Input
                  id="image"
                  name="image"
                  value={formData.image || ''}
                  onChange={handleInputChange}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEditing ? 'Update Variant' : 'Create Variant'
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
