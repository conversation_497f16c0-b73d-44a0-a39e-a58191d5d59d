import React from 'react';
import { EPOSIntegration } from '@/components/admin/EPOSIntegration';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

export default function EPOSPage() {
  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link to="/admin/dashboard">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Dashboard
          </Link>
        </Button>
      </div>
      
      <div>
        <h1 className="text-2xl font-bold mb-2">EPOS Integration</h1>
        <p className="text-gray-500 mb-6">
          Manage your Electronic Point of Sale system integration
        </p>
      </div>
      
      <EPOSIntegration />
    </div>
  );
}
