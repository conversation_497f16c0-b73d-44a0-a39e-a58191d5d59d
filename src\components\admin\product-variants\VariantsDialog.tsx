import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash, Plus, Loader2 } from 'lucide-react';
import { customSupabase } from '@/integrations/supabase/customClient';
import { toast } from '@/components/ui/use-toast';
import { ProductVariant } from '@/types/database-with-variants';

interface VariantsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  onEdit?: (variant: ProductVariant) => void;
  onAdd?: () => void;
  onSuccess?: () => void;
}

export function VariantsDialog({
  isOpen,
  onClose,
  productId,
  productName,
  onEdit,
  onAdd,
  onSuccess
}: VariantsDialogProps) {
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Fetch variants when dialog opens or productId changes
  useEffect(() => {
    if (isOpen && productId) {
      fetchVariants();
    }
  }, [isOpen, productId]);

  // Keep dialog state when it's closed but still mounted
  // This prevents the dialog from disappearing when changing tabs

  const fetchVariants = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await customSupabase
        .from('product_variants')
        .select('*')
        .eq('product_id', productId)
        .order('created_at');

      if (error) {
        throw error;
      }

      setVariants(data || []);
    } catch (error) {
      console.error('Error fetching variants:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product variants',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (variantId: string) => {
    setIsDeleting(variantId);
    try {
      const { error } = await customSupabase
        .from('product_variants')
        .delete()
        .eq('id', variantId);

      if (error) {
        throw error;
      }

      setVariants(variants.filter(v => v.id !== variantId));
      toast({
        title: 'Success',
        description: 'Variant deleted successfully',
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error deleting variant:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete variant',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Format option combination for display
  const formatOptionCombination = (optionCombination: Record<string, string>) => {
    return Object.entries(optionCombination)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Variants for {productName}</DialogTitle>
          <DialogDescription>
            Manage product variants with different options, prices, and inventory.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {variants.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No variants found for this product.</p>
                <p className="text-sm text-muted-foreground mt-1">Add variants to offer different options to customers.</p>
              </div>
            ) : (
              <div className="max-h-[400px] overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Options</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead className="text-right">Adjustment</TableHead>
                      <TableHead className="text-right">Final Price</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {variants.map((variant) => (
                      <TableRow key={variant.id}>
                        <TableCell className="font-medium">
                          {formatOptionCombination(variant.option_combination)}
                        </TableCell>
                        <TableCell>{variant.sku || '-'}</TableCell>
                        <TableCell className="text-right">
                          {variant.price_adjustment !== undefined ? (
                            <span className={variant.price_adjustment > 0 ? 'text-green-600' : variant.price_adjustment < 0 ? 'text-red-600' : ''}>
                              {variant.price_adjustment > 0 ? '+' : ''}£{variant.price_adjustment.toFixed(2)}
                            </span>
                          ) : (
                            '—'
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          £{variant.price.toFixed(2)}
                          {variant.sale_price && (
                            <span className="ml-2 text-sm text-muted-foreground line-through">
                              £{variant.sale_price.toFixed(2)}
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-center">
                          {variant.stock_quantity !== null ? variant.stock_quantity : '-'}
                        </TableCell>
                        <TableCell className="text-center">
                          <Badge variant={variant.is_active ? "default" : "secondary"}>
                            {variant.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => onEdit && onEdit(variant)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(variant.id)}
                              disabled={isDeleting === variant.id}
                            >
                              {isDeleting === variant.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}

        <DialogFooter className="flex justify-between items-center">
          <div>
            <span className="text-sm text-muted-foreground">
              {variants.length} {variants.length === 1 ? 'variant' : 'variants'}
            </span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={() => onAdd && onAdd()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Variant
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
