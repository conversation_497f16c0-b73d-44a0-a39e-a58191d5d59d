
import React, { useState, useEffect } from "react";
import { Product, Category } from "@/types/database";
import { ProductFormFields } from "./product-form/ProductFormFields";
import { ProductFormToggles } from "./product-form/ProductFormToggles";
import { ProductFormActions } from "./product-form/ProductFormActions";
import { useProductForm } from "./product-form/useProductForm";
import { useCategoriesQuery } from "./product-form/useCategoriesQuery";
import { useBrandsQuery } from "./product-form/hooks/useBrandsQuery";
import { supabase } from "@/integrations/supabase/client";

interface ProductFormProps {
  product: Product | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const { data: categories, isLoading: categoriesLoading } = useCategoriesQuery();
  const { data: brands, isLoading: brandsLoading } = useBrandsQuery();

  const {
    formData,
    setFormData,
    isSubmitting,
    isGeneratingDescription,
    isFindingImages,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
    handleSubmit,
    handleGenerateDescription,
    handleFindImages,
    // Related products
    relatedProducts,
    handleAddRelatedProduct,
    handleRemoveRelatedProduct,
    handleReorderRelatedProducts,
  } = useProductForm({ product, onSuccess });

  // State for subcategories
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [filteredSubcategories, setFilteredSubcategories] = useState<Category[]>([]);
  const [isLoadingSubcategories, setIsLoadingSubcategories] = useState(false);

  // Fetch all subcategories once when component mounts
  useEffect(() => {
    const fetchSubcategories = async () => {
      setIsLoadingSubcategories(true);
      try {
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .not('parent_id', 'is', null) // Only get subcategories
          .order('display_order', { ascending: true });

        if (error) {
          console.error('Error fetching subcategories:', error);
          return;
        }

        console.log('Fetched subcategories:', data?.length || 0);
        setSubcategories(data || []);
      } catch (error) {
        console.error('Error in subcategories fetch:', error);
      } finally {
        setIsLoadingSubcategories(false);
      }
    };

    fetchSubcategories();
  }, []);

  // Filter subcategories when category changes - using a ref to prevent loops
  const previousCategoryIdRef = React.useRef<string | null>(null);

  useEffect(() => {
    // Only update if the category has actually changed
    if (formData?.category_id !== previousCategoryIdRef.current) {
      console.log(`Category changed from ${previousCategoryIdRef.current} to ${formData?.category_id}`);
      previousCategoryIdRef.current = formData?.category_id || null;

      if (formData?.category_id) {
        console.log(`Filtering subcategories for category: ${formData.category_id}`);
        const filtered = subcategories.filter(subcat => subcat.parent_id === formData.category_id);
        console.log(`Found ${filtered.length} subcategories for category ${formData.category_id}`);
        setFilteredSubcategories(filtered);
      } else {
        setFilteredSubcategories([]);
      }
    }
  }, [formData?.category_id, subcategories]);

  // Simplified function to handle swapping main image with an additional image
  const swapMainImage = (newMainImageUrl: string) => {
    console.log('===== SWAP MAIN IMAGE FUNCTION =====');
    console.log('New main image URL:', newMainImageUrl);

    // Get current state
    const currentMainImage = formData.image;
    const currentAdditionalImages = [...(formData.additional_images || [])];

    // Create new state
    let newAdditionalImages = [...currentAdditionalImages];

    // 1. Remove the new main image from additional images
    newAdditionalImages = newAdditionalImages.filter(img => img !== newMainImageUrl);

    // 2. If we have a current main image, add it to additional images
    if (currentMainImage && currentMainImage.trim() !== '' && currentMainImage !== newMainImageUrl) {
      newAdditionalImages.push(currentMainImage);
    }

    // 3. Create a completely new form data object to avoid reference issues
    const newFormData = {
      ...formData,
      image: newMainImageUrl,
      additional_images: newAdditionalImages
    };

    // 4. Update the form data directly
    setFormData(newFormData);

    console.log('===== END SWAP MAIN IMAGE FUNCTION =====');
  };

  console.log("Rendering ProductForm with data:", {
    productId: product?.id,
    hasCategories: !!categories?.length,
    formDataKeys: Object.keys(formData)
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <ProductFormFields
        formData={formData}
        categories={categories || []}
        brands={brands || []}
        subcategories={subcategories || []}
        filteredSubcategories={filteredSubcategories || []}
        isLoadingSubcategories={isLoadingSubcategories}
        handleChange={handleChange}
        handleSwitchChange={handleSwitchChange}
        handleSelectChange={handleSelectChange}
        handleGenerateDescription={handleGenerateDescription}
        handleFindImages={handleFindImages}
        isGeneratingDescription={isGeneratingDescription}
        isFindingImages={isFindingImages}
        swapMainImage={swapMainImage}
        // Related products
        relatedProducts={relatedProducts}
        onAddRelatedProduct={handleAddRelatedProduct}
        onRemoveRelatedProduct={handleRemoveRelatedProduct}
        onReorderRelatedProducts={handleReorderRelatedProducts}
      />

      <ProductFormToggles
        formData={formData}
        handleSwitchChange={handleSwitchChange}
      />

      <ProductFormActions
        isSubmitting={isSubmitting}
        isEditing={!!product}
        onCancel={onCancel}
      />
    </form>
  );
}
