-- Create shipping zones table
CREATE TABLE IF NOT EXISTS shipping_zones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    countries JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create shipping methods table
CREATE TABLE IF NOT EXISTS shipping_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zone_id UUID NOT NULL REFERENCES shipping_zones(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    free_shipping_threshold DECIMAL(10,2),
    estimated_days_min INTEGER NOT NULL DEFAULT 1,
    estimated_days_max INTEGER NOT NULL DEFAULT 7,
    icon VARCHAR(50) NOT NULL DEFAULT 'standard',
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shipping_zones_active ON shipping_zones(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_zones_countries ON shipping_zones USING GIN(countries);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_zone_id ON shipping_methods(zone_id);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_active ON shipping_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_sort_order ON shipping_methods(sort_order);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
DROP TRIGGER IF EXISTS update_shipping_zones_updated_at ON shipping_zones;
CREATE TRIGGER update_shipping_zones_updated_at
    BEFORE UPDATE ON shipping_zones
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_shipping_methods_updated_at ON shipping_methods;
CREATE TRIGGER update_shipping_methods_updated_at
    BEFORE UPDATE ON shipping_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default shipping zones
INSERT INTO shipping_zones (name, description, countries, is_active) VALUES
(
    'United Kingdom',
    'Domestic shipping within the UK',
    '["United Kingdom"]'::jsonb,
    true
),
(
    'European Union',
    'Shipping to EU countries',
    '["Ireland", "France", "Germany", "Spain", "Italy", "Netherlands", "Belgium", "Portugal", "Austria", "Denmark", "Sweden", "Finland", "Poland", "Czech Republic", "Hungary", "Slovakia", "Slovenia", "Croatia", "Estonia", "Latvia", "Lithuania", "Luxembourg", "Malta", "Cyprus", "Bulgaria", "Romania", "Greece"]'::jsonb,
    true
),
(
    'Rest of Europe',
    'Shipping to non-EU European countries',
    '["Norway", "Switzerland"]'::jsonb,
    true
);

-- Insert default shipping methods for UK
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Free Standard Shipping',
    'Free delivery within 3-5 business days for orders over £50',
    0.00,
    50.00,
    3,
    5,
    'free',
    1
FROM shipping_zones z WHERE z.name = 'United Kingdom';

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Standard Shipping',
    'Delivery within 3-5 business days',
    5.99,
    NULL,
    3,
    5,
    'standard',
    2
FROM shipping_zones z WHERE z.name = 'United Kingdom';

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Express Shipping',
    'Delivery within 2-3 business days',
    9.99,
    NULL,
    2,
    3,
    'express',
    3
FROM shipping_zones z WHERE z.name = 'United Kingdom';

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Next Day Delivery',
    'Order before 2pm for next day delivery',
    14.99,
    NULL,
    1,
    1,
    'nextDay',
    4
FROM shipping_zones z WHERE z.name = 'United Kingdom';

-- Insert default shipping methods for EU
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Free Shipping',
    'Free delivery within 7-10 business days for orders over £100',
    0.00,
    100.00,
    7,
    10,
    'free',
    1
FROM shipping_zones z WHERE z.name = 'European Union';

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Standard Shipping',
    'Delivery within 7-10 business days',
    12.99,
    NULL,
    7,
    10,
    'standard',
    2
FROM shipping_zones z WHERE z.name = 'European Union';

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Express Shipping',
    'Delivery within 5-7 business days',
    19.99,
    NULL,
    5,
    7,
    'express',
    3
FROM shipping_zones z WHERE z.name = 'European Union';

-- Insert default shipping methods for Rest of Europe
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Europe Standard Shipping',
    'Delivery within 10-14 business days',
    15.99,
    NULL,
    10,
    14,
    'standard',
    1
FROM shipping_zones z WHERE z.name = 'Rest of Europe';

-- Enable RLS (Row Level Security) if needed
ALTER TABLE shipping_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_methods ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access (adjust as needed for your auth system)
CREATE POLICY "Admin can manage shipping zones" ON shipping_zones
    FOR ALL USING (true);

CREATE POLICY "Admin can manage shipping methods" ON shipping_methods
    FOR ALL USING (true);

-- Create policies for public read access
CREATE POLICY "Public can read active shipping zones" ON shipping_zones
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public can read active shipping methods" ON shipping_methods
    FOR SELECT USING (is_active = true);
