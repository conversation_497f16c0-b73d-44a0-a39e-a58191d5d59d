import { supabase } from './client';
import { Database } from './types';

export type NewsletterSubscriber = Database['public']['Tables']['newsletter_subscribers']['Row'];

/**
 * Check if an email already exists in the newsletter subscribers
 */
export async function checkEmailExists(email: string): Promise<boolean> {
  try {
    // Use a direct query to check if the email exists
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .select('email')
      .eq('email', email)
      .maybeSingle();

    if (error) {
      console.error('Error checking email existence:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Unexpected error checking email:', error);
    return false;
  }
}

/**
 * Subscribe to the newsletter using direct Supabase client
 */
export async function subscribeToNewsletter(email: string, firstName?: string, lastName?: string) {
  try {
    // First check if the email already exists
    const emailExists = await checkEmailExists(email);
    if (emailExists) {
      return { error: 'Email already subscribed', alreadySubscribed: true };
    }

    // Prepare subscriber data
    const subscriberData = {
      email,
      first_name: firstName || null,
      last_name: lastName || null,
      subscribed_at: new Date().toISOString(),
      is_active: true,
      metadata: {
        source: 'website_footer',
        utm_source: window.location.pathname
      }
    };

    // Insert the subscriber directly
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .insert(subscriberData)
      .select()
      .single();

    if (error) {
      console.error('Error subscribing to newsletter:', error);

      // Check if it's a duplicate email error
      if (error.code === '23505' || error.message?.includes('duplicate')) {
        return {
          error: 'Email already subscribed',
          alreadySubscribed: true
        };
      }

      return { error: error.message || 'Failed to subscribe' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Unexpected error during subscription:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Get all newsletter subscribers
 * @returns Array of subscribers or error object
 */
export async function getNewsletterSubscribers(isActive?: boolean) {
  try {
    let query = supabase
      .from('newsletter_subscribers')
      .select('*')
      .order('subscribed_at', { ascending: false });

    if (typeof isActive === 'boolean') {
      query = query.eq('is_active', isActive);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching newsletter subscribers:', error);
      return { error: 'Failed to fetch subscribers' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Update a subscriber's status
 * @param id Subscriber ID
 * @param isActive New active status
 * @returns Object with success/error information
 */
export async function updateSubscriberStatus(id: string, isActive: boolean) {
  try {
    const { error } = await supabase
      .from('newsletter_subscribers')
      .update({ is_active: isActive, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error updating subscriber status:', error);
      return { error: error.message || 'Failed to update subscriber status' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating subscriber status:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Send a newsletter to subscribers
 * @param subject Newsletter subject
 * @param content Plain text content of the newsletter
 * @param htmlContent HTML content of the newsletter
 * @param recipientIds Optional array of subscriber IDs (if not provided, sends to all active subscribers)
 * @returns Object with success/error information and recipient count
 */
export async function sendNewsletter(
  subject: string,
  content: string,
  htmlContent: string,
  recipientIds?: string[]
) {
  try {
    // Get subscribers to send to
    let query = supabase
      .from('newsletter_subscribers')
      .select('id, email, first_name')
      .eq('is_active', true);

    // If specific recipients are provided, filter by those IDs
    if (recipientIds && recipientIds.length > 0) {
      query = query.in('id', recipientIds);
    }

    const { data: subscribers, error } = await query;

    if (error) {
      console.error('Error fetching subscribers:', error);
      return { error: 'Failed to fetch subscribers' };
    }

    if (!subscribers || subscribers.length === 0) {
      return { error: 'No active subscribers found' };
    }

    // For now, we'll just log the newsletter send in the database
    // In a real implementation, you would integrate with an email service like SendGrid
    const { data: newsletterSend, error: insertError } = await supabase
      .from('newsletter_sends')
      .insert({
        subject,
        content,
        html_content: htmlContent,
        recipient_count: subscribers.length,
        sent_at: new Date().toISOString(),
        status: 'sent' // In real implementation, this would be 'sending' initially
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error logging newsletter send:', insertError);
      return { error: 'Failed to log newsletter send' };
    }

    // TODO: Integrate with actual email service (SendGrid, Mailchimp, etc.)
    // For now, we'll simulate successful sending
    console.log(`Newsletter "${subject}" would be sent to ${subscribers.length} subscribers`);
    console.log('Recipients:', subscribers.map(sub => sub.email));

    return {
      success: true,
      recipientCount: subscribers.length,
      newsletterId: newsletterSend.id
    };
  } catch (error) {
    console.error('Error sending newsletter:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Get all newsletter sends
 * @returns Array of newsletter sends or error object
 */
export async function getNewsletterSends() {
  try {
    const { data, error } = await supabase
      .from('newsletter_sends')
      .select('*')
      .order('sent_at', { ascending: false });

    if (error) {
      console.error('Error fetching newsletter sends:', error);
      return { error: 'Failed to fetch newsletter sends' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error:', error);
    return { error: 'An unexpected error occurred' };
  }
}
