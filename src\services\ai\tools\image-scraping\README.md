# 🖼️ Image Scraping System

## 🎯 **Objective**
Replace expensive Google Image Search with free, targeted scraping to activate 1000+ inactive products.

## 📁 **File Structure**
```
src/services/ai/tools/image-scraping/
├── PlaywrightImageScraper.ts      // Main MCP-based scraper
├── ImageQualityAssessor.ts        // Quality ranking & filtering  
├── SourceManager.ts               // Retailer source management
├── BulkImageProcessor.ts          // Batch processing for 1000+ products
├── ImageValidator.ts              // Image validation & verification
├── types/
│   ├── ImageScrapingTypes.ts      // Type definitions (CREATED)
│   └── SourceTypes.ts             // Source configuration types
└── README.md                      // This file
```

## 🚀 **Implementation Priority**

### **Phase 1: Core Scraper (Week 1)**
1. **PlaywrightImageScraper.ts** - Main scraping engine using MCP
2. **ImageQualityAssessor.ts** - Rank images by quality and relevance
3. **SourceManager.ts** - Manage cannabis/CBD retailer sources
4. **BulkImageProcessor.ts** - Process 1000+ products efficiently

### **Phase 2: Enhancement (Week 2)**
1. **ImageValidator.ts** - Validate images before saving
2. **Error handling and retry logic**
3. **Performance optimization**
4. **Integration with existing product form**

## 🎯 **Target Retailers for Cannabis/CBD Products**

### **Seeds**
- seedsman.com
- royalqueenseeds.com
- barneysfarm.com

### **CBD Products**
- cbdoil.co.uk
- lovecbd.org
- cbdlife.co.uk

### **Vaporizers**
- vaporizerchief.com
- planetofthevapes.co.uk
- vapefiend.co.uk

### **Smoking Accessories**
- everyonedoesit.com
- grasscity.com
- shivaonline.co.uk

## 🛠️ **Technical Approach**

### **1. MCP Playwright Integration**
```typescript
// Use Playwright MCP Server for web automation
const playwright = new PlaywrightMCPClient();
await playwright.navigate(searchUrl);
const images = await playwright.extractElements({
  selector: '.product-image img',
  attributes: ['src', 'alt', 'data-src']
});
```

### **2. Quality Assessment**
```typescript
// Rank images by multiple factors
const qualityScore = assessImageQuality(image, productName);
// Factors: dimensions, alt text relevance, source reliability, etc.
```

### **3. Bulk Processing**
```typescript
// Process products in batches with respectful delays
const batches = chunkArray(products, 10);
for (const batch of batches) {
  await processBatch(batch);
  await delay(2000); // Respectful delay
}
```

## 📊 **Success Metrics**

### **Performance Targets**
- ✅ **90%+ success rate** finding relevant images
- ✅ **£0 API costs** (vs current £5+ Google costs)
- ✅ **Process 1000+ products** in 2-3 hours
- ✅ **5+ quality images** per successful product

### **Quality Targets**
- ✅ **Images match product names** (high relevance)
- ✅ **Minimum 400x400 pixels** (good quality)
- ✅ **From actual product pages** (not generic stock photos)
- ✅ **Valid image URLs** (accessible and working)

## 🔗 **Integration Points**

### **Current Integration**
Replace calls in: `src/components/admin/product-form/hooks/useProductAI.ts`

```typescript
// Current (expensive Google API)
const images = await searchGoogleImages(searchQuery, 5);

// New (free targeted scraping)
const images = await imageScrapingService.findProductImages({
  name: formData.name,
  category: formData.category
});
```

### **Bulk Processing Integration**
Add to admin products page for processing inactive products:

```typescript
// New bulk processing capability
const report = await imageScrapingService.bulkProcessProducts(
  inactiveProducts
);
```

## 🤝 **Coordination with Main AI System**

### **Interface Contract**
The main AI system will use the `ImageScrapingService` interface defined in `types/ImageScrapingTypes.ts`.

### **No Conflicts**
- ✅ **Separate file structure** (tools/ vs core/)
- ✅ **Clear interfaces** (defined types)
- ✅ **Independent development** (can work in parallel)

## 🚀 **Getting Started**

### **Step 1: Setup MCP Playwright**
```bash
npm install -g @executeautomation/playwright-mcp-server
```

### **Step 2: Create Core Files**
1. Start with `PlaywrightImageScraper.ts`
2. Implement basic scraping for one retailer
3. Add quality assessment
4. Test with a few products

### **Step 3: Expand & Optimize**
1. Add more retailer sources
2. Implement bulk processing
3. Add error handling and retries
4. Integrate with existing product form

## 📞 **Questions or Issues?**

If you need any clarification on:
- MCP Playwright integration
- Specific retailer scraping approaches
- Integration with existing systems
- Performance optimization

Just ask! The main AI system development is happening in parallel, so we can coordinate interfaces as needed.

## 🎯 **Ready to Solve the 1000+ Product Problem!**

This image scraping system will be a game-changer for activating all those inactive products. Let's build something amazing! 🚀
