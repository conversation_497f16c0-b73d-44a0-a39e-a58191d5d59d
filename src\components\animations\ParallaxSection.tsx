
import { ReactNode } from 'react';
import ParallaxBackground from './ParallaxBackground';

interface ParallaxSectionProps {
  children: ReactNode;
  imageUrl: string;
  overlayColor?: string;
  speed?: number;
  className?: string;
  contentClassName?: string;
}

const ParallaxSection = ({ 
  children, 
  imageUrl,
  overlayColor = 'from-sage-900/70 to-clay-900/70',
  speed = 0.2,
  className = '',
  contentClassName = ''
}: ParallaxSectionProps) => {
  return (
    <section className={`relative overflow-hidden ${className}`}>
      <ParallaxBackground imageUrl={imageUrl} speed={speed} opacity={0.6} />
      <div className={`absolute inset-0 bg-gradient-to-br ${overlayColor}`} />
      <div className={`relative z-10 ${contentClassName}`}>
        {children}
      </div>
    </section>
  );
};

export default ParallaxSection;
