/**
 * Test utility for image search functionality
 * Use this to test if your API keys are working correctly
 */

const PIXABAY_API_KEY = import.meta.env.VITE_PIXABAY_API_KEY;

export async function testPixabayAPI(): Promise<boolean> {
  if (!PIXABAY_API_KEY) {
    console.error('❌ Pixabay API key not found in environment variables');
    return false;
  }

  try {
    console.log('🔍 Testing Pixabay API...');

    const testQuery = 'wellness';
    const apiUrl = `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=${encodeURIComponent(testQuery)}&image_type=photo&per_page=3&safesearch=true`;

    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error(`❌ Pixabay API returned status: ${response.status}`);
      return false;
    }

    const data = await response.json();

    if (data.hits && data.hits.length > 0) {
      console.log(`✅ Pixabay API working! Found ${data.hits.length} images for "${testQuery}"`);
      console.log('Sample image:', data.hits[0].webformatURL);
      return true;
    } else {
      console.warn('⚠️ Pixabay API responded but no images found');
      return false;
    }

  } catch (error) {
    console.error('❌ Pixabay API test failed:', error);
    return false;
  }
}

export async function testImageSearchSystem(): Promise<void> {
  console.log('🧪 Testing Image Search System...');
  console.log('================================');

  // Test environment variables
  console.log('📋 Environment Variables:');
  console.log(`VITE_PIXABAY_API_KEY: ${PIXABAY_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`VITE_FREEPIK_API_KEY: ${import.meta.env.VITE_FREEPIK_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`VITE_GOOGLE_API_KEY: ${import.meta.env.VITE_GOOGLE_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`VITE_GOOGLE_CX_ID: ${import.meta.env.VITE_GOOGLE_CX_ID ? '✅ Set' : '❌ Missing'}`);
  console.log('');

  // Test Pixabay API
  const pixabayWorking = await testPixabayAPI();

  console.log('');
  console.log('🎯 Product Category Examples:');
  console.log('Your system is optimized for:');
  console.log('• Cannabis Seeds & Hemp Products');
  console.log('• CBD Oils & Wellness Products');
  console.log('• Vaporizers & Vaping Devices');
  console.log('• Bongs & Water Pipes');
  console.log('• Smoking Accessories & Grinders');
  console.log('• Legal & Business Topics');
  console.log('• General Lifestyle & Wellness');
  console.log('');

  console.log('📊 Summary:');
  console.log(`Pixabay API: ${pixabayWorking ? '✅ Working' : '❌ Failed'}`);

  if (pixabayWorking) {
    console.log('🎉 Your image search system should work well!');
    console.log('💡 Try these example topics:');
    console.log('   - "CBD Oil Benefits"');
    console.log('   - "Cannabis Seeds Legal UK"');
    console.log('   - "Best Vaporizers 2024"');
    console.log('   - "Glass Bongs Collection"');
    console.log('   - "Smoking Accessories Guide"');
    console.log('   - "Business Strategy" (non-product example)');
  } else {
    console.log('⚠️ Please check your API keys and try again');
    console.log('📖 See docs/IMAGE_SEARCH_SETUP.md for setup instructions');
  }
}

// Auto-run test in development
if (import.meta.env.DEV) {
  // Run test after a short delay to avoid blocking app startup
  setTimeout(() => {
    testImageSearchSystem();
  }, 2000);
}
