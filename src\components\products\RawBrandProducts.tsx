import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import ProductCard from './ProductCard';
import { Button } from '@/components/ui/button';

interface RawBrandProductsProps {
  initialPageSize?: number;
}

const RawBrandProducts: React.FC<RawBrandProductsProps> = ({
  initialPageSize = 50,
}) => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = initialPageSize;

  // Function to fetch RAW brand products only
  const fetchRawProducts = async (page = 0, append = false) => {
    try {
      console.log('Fetching RAW brand products...');
      
      if (page === 0 && !append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // Calculate range for pagination
      const from = page * pageSize;
      const to = from + pageSize - 1;
      
      // RAW brand ID from your console output
      const RAW_BRAND_ID = '9628468d-6311-4a3f-b4bb-6b2821f5d50a';
      
      // Direct query for RAW brand products only
      const { data, error, count } = await supabase
        .from('products')
        .select('*', { count: 'exact' })
        .eq('is_active', true)
        .eq('brand_id', RAW_BRAND_ID)
        .order('name', { ascending: true })
        .range(from, to);
        
      if (error) {
        console.error('Error fetching RAW products:', error);
        setError(`Error: ${error.message}`);
        setLoading(false);
        setLoadingMore(false);
        return;
      }
      
      console.log(`Fetched ${data?.length || 0} RAW products`);
      
      // Update products state
      if (append && page > 0) {
        setProducts(prev => [...prev, ...(data || [])]);
      } else {
        setProducts(data || []);
      }
      
      // Check if there are more products to load
      if (count !== null) {
        setHasMore(from + pageSize < count);
      } else {
        setHasMore(data && data.length === pageSize);
      }
      
      setLoading(false);
      setLoadingMore(false);
    } catch (err) {
      console.error('Exception in RAW product grid:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more products
  const handleLoadMore = () => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchRawProducts(nextPage, true);
  };

  // Fetch products on component mount
  useEffect(() => {
    setCurrentPage(0);
    setHasMore(true);
    fetchRawProducts(0, false);
  }, []);

  return (
    <div className="py-4 min-h-[300px] relative">
      {/* Loading State */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
          <p className="mt-2">Loading RAW products...</p>
        </div>
      )}

      {/* Error Message */}
      {error && !loading && (
        <div className="text-center py-10 bg-red-50 rounded-lg">
          <h3 className="text-xl font-medium text-red-900">Error loading products</h3>
          <p className="mt-2 text-red-500">{error}</p>
        </div>
      )}

      {/* No Products Message */}
      {products.length === 0 && !loading && !error && (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p>No RAW products found</p>
        </div>
      )}

      {/* Products Grid */}
      {!loading && !error && products.length > 0 && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {products.map(product => (
              <ProductCard
                key={product.id}
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  description: product.description,
                  category_id: product.category_id,
                  subcategory_id: product.subcategory_id,
                  brand_id: product.brand_id,
                  slug: product.slug,
                  in_stock: product.in_stock ?? true,
                  is_featured: product.is_featured ?? false,
                  is_new: product.is_new ?? false,
                  is_best_seller: product.is_best_seller ?? false,
                  sale_price: product.sale_price,
                  image: product.image,
                  created_at: product.created_at || new Date().toISOString(),
                  updated_at: product.updated_at || new Date().toISOString(),
                  rating: product.rating ?? 0,
                  review_count: product.review_count ?? 0
                }}
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={handleLoadMore}
                disabled={loadingMore}
                className="min-w-[200px]"
                variant="outline"
              >
                {loadingMore ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More Products'
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RawBrandProducts;
