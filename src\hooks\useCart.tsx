'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Product } from '@/types/database';

// Define ProductVariant interface directly to avoid import issues
export interface ProductVariant {
  id: string;
  sku: string;
  options: Record<string, string>;
  price_adjustment?: number;
}

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  variant?: {
    id: string;
    sku: string;
    options: Record<string, any>; // Use any to allow for nested properties
    price_adjustment: number;
  };
  price: number;
  calculated_price?: number; // Add calculated_price property
  price_adjustment?: number; // Add price_adjustment property
  selected_options?: Record<string, string>; // Add selected_options property
}

interface CartContextType {
  cartItems: CartItem[];
  isLoading: boolean;
  addToCart: (product: Product, quantity: number, variant?: ProductVariant) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  subtotal: number;
}

const CartContext = createContext<CartContextType | null>(null);

export const CartProvider = ({ children }: { children: ReactNode }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  // Make auth optional to avoid circular dependencies
  const user = null; // We'll handle user-specific cart logic elsewhere

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Failed to parse cart from localStorage:', error);
        localStorage.removeItem('cart');
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(items));
  }, [items]);

  // Calculate total items and subtotal
  const itemCount = items.reduce((total, item) => total + item.quantity, 0);
  
  // Calculate subtotal using the calculated_price if available, otherwise fall back to price + adjustment
  const subtotal = items.reduce((total, item) => {
    // Get the display price for this item
    let itemPrice;
    
    // First try to use calculated_price if available
    if (item.calculated_price !== undefined) {
      itemPrice = item.calculated_price;
    } 
    // Then try variant.options.calculatedPrice
    else if (item.variant?.options?.calculatedPrice !== undefined) {
      itemPrice = Number(item.variant.options.calculatedPrice);
    }
    // Then apply price adjustment if available
    else {
      const basePrice = item.price || 0;
      const adjustment = item.price_adjustment !== undefined ? 
                        item.price_adjustment : 
                        (item.variant?.price_adjustment || 0);
      itemPrice = basePrice + adjustment;
    }
    
    // Multiply by quantity and add to total
    return total + (itemPrice * item.quantity);
  }, 0);

  // Add item to cart
  const addToCart = (product: Product, quantity: number, variant?: ProductVariant) => {
    try {
      setIsLoading(true);
      
      // Check if the item already exists in the cart
      const existingItemIndex = items.findIndex(item => 
        item.product.id === product.id && 
        (!variant ? !item.variant : item.variant?.id === variant.id)
      );

      if (existingItemIndex >= 0) {
        // Update quantity of existing item
        const updatedItems = [...items];
        updatedItems[existingItemIndex].quantity += quantity;
        setItems(updatedItems);

        // Format the options in a cleaner way for existing item
        let optionsDisplay = '';
        const existingItem = items[existingItemIndex];
        if (existingItem.variant && existingItem.variant.options) {
          // Get only the values without the keys
          const optionValues = Object.entries(existingItem.variant.options)
            .filter(([key]) => 
              !key.startsWith('_') && 
              key !== 'calculatedPrice' && 
              key !== 'priceAdjustment'
            )
            .map(([_, value]) => value);
            
          if (optionValues.length > 0) {
            // Filter out any empty values or objects
            const validOptions = optionValues.filter(val => 
              typeof val === 'string' && val.trim() !== ''
            );
            if (validOptions.length > 0) {
              optionsDisplay = ` (${validOptions.join(', ')})`;
            }
          }
        }
        
        toast({
          title: 'Added to Cart',
          description: `Added ${quantity} more ${product.name}${optionsDisplay} to your cart!`,
        });
      } else {
        // Extract calculated price and price adjustment from variant if available
        // Use type assertion to handle custom properties that might not be in the type definition
        const variantAny = variant as any;
        
        // Log the variant for debugging
        console.log('Adding to cart with variant:', variantAny);
        
        // Check for calculated price in different possible locations
        let calculatedPrice;
        if (variantAny?.calculated_price !== undefined) {
          calculatedPrice = variantAny.calculated_price;
          console.log('Using variant.calculated_price:', calculatedPrice);
        } else if (variantAny?.options?.calculatedPrice !== undefined) {
          calculatedPrice = variantAny.options.calculatedPrice;
          console.log('Using variant.options.calculatedPrice:', calculatedPrice);
        } else {
          console.log('No calculated price found in variant');
        }
        
        // Check for price adjustment in different possible locations
        let priceAdjustment;
        if (variantAny?.price_adjustment !== undefined) {
          priceAdjustment = variantAny.price_adjustment;
          console.log('Using variant.price_adjustment:', priceAdjustment);
        } else if (variantAny?.options?.priceAdjustment !== undefined) {
          priceAdjustment = variantAny.options.priceAdjustment;
          console.log('Using variant.options.priceAdjustment:', priceAdjustment);
        } else {
          priceAdjustment = 0;
          console.log('No price adjustment found, using 0');
        }
        
        // Get selected options from variant if available
        const selectedOptions = {};
        if (variant?.options) {
          Object.entries(variant.options).forEach(([key, value]) => {
            // Skip internal properties used for price calculations
            if (key !== 'calculatedPrice' && key !== 'priceAdjustment' && !key.startsWith('_')) {
              selectedOptions[key] = value;
            }
          });
        }
        
        // Add new item to cart with all necessary information
        const newItem: CartItem = {
          id: variant ? `${product.id}-${variant.id}` : `${product.id}-${Date.now()}`,
          product,
          quantity,
          price: product.price,
          // Add calculated price if available
          ...(calculatedPrice !== undefined && {
            calculated_price: calculatedPrice
          }),
          // Add selected options if available
          ...(Object.keys(selectedOptions).length > 0 && {
            selected_options: selectedOptions
          }),
          // Add price adjustment if available
          ...(priceAdjustment !== undefined && {
            price_adjustment: priceAdjustment
          }),
          // Add variant information if available
          ...(variant && {
            variant: {
              id: variant.id,
              sku: variant.sku,
              options: variant.options,
              price_adjustment: variant.price_adjustment || 0,
            }
          })
        };

        setItems([...items, newItem]);

        // Format the options in a cleaner way
        let optionsDisplay = '';
        if (variant && variant.options) {
          // Get only the values without the keys
          const optionValues = Object.values(variant.options);
          if (optionValues.length > 0) {
            // Filter out any empty values or objects
            const validOptions = optionValues.filter(val => 
              typeof val === 'string' && val.trim() !== ''
            );
            if (validOptions.length > 0) {
              optionsDisplay = ` (${validOptions.join(', ')})`;
            }
          }
        }
        
        toast({
          title: 'Added to Cart',
          description: `Added ${quantity} ${product.name}${optionsDisplay} to your cart!`,
        });
      }
    } catch (error) {
      console.error('Error adding item to cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to add item to cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Remove item from cart
  const removeFromCart = (itemId: string) => {
    setIsLoading(true);
    try {
      setItems(items.filter(item => item.id !== itemId));

      toast({
        title: 'Removed from Cart',
        description: 'Item removed from your cart',
      });
    } catch (error) {
      console.error('Error removing item from cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove item from cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update item quantity
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity < 1) return;

    setIsLoading(true);
    try {
      setItems(items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      ));
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      toast({
        title: 'Error',
        description: 'Failed to update quantity',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Clear entire cart
  const clearCart = () => {
    setIsLoading(true);
    try {
      setItems([]);
      localStorage.removeItem('cart');

      toast({
        title: 'Cart Cleared',
        description: 'Your cart has been cleared',
      });
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to clear cart',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems: items,
        isLoading,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        totalItems: itemCount,
        subtotal
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
