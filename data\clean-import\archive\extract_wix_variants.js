const fs = require('fs');
const path = require('path');

// Input file
const inputFile = path.join('..', '..', 'docs', 'catalog_products.csv');

// Read the file
const fileContent = fs.readFileSync(inputFile, 'utf8');
const lines = fileContent.split('\n');

// Store products and their variants
const products = {};
const variants = {};

// Process each line
console.log('Processing Wix CSV file...');
for (let i = 1; i < lines.length; i++) {
  const line = lines[i];
  if (!line.trim()) continue;
  
  // Split the line by commas, but respect quoted values
  const parts = [];
  let currentPart = '';
  let inQuotes = false;
  
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    
    if (char === '"') {
      inQuotes = !inQuotes;
      currentPart += char;
    } else if (char === ',' && !inQuotes) {
      parts.push(currentPart);
      currentPart = '';
    } else {
      currentPart += char;
    }
  }
  
  // Add the last part
  parts.push(currentPart);
  
  // Extract relevant fields
  const handleId = parts[0];
  const fieldType = parts[1];
  const name = parts[2];
  const price = parseFloat(parts[8]) || 0;
  const surcharge = parseFloat(parts[9]) || 0;
  const productOptionName1 = parts[16];
  const productOptionDescription1 = parts[18];
  const productOptionValue1 = parts[parts.length - 1]; // Last column
  
  // Process products with Pack Size options
  if (fieldType === 'Product' && productOptionName1 === 'Pack Size') {
    products[handleId] = {
      name,
      basePrice: price,
      options: productOptionDescription1 ? productOptionDescription1.split(';') : [],
      variants: []
    };
  }
  
  // Process variants
  if (fieldType === 'Variant' && products[handleId]) {
    const product = products[handleId];
    
    // Add to variants array
    product.variants.push({
      option: productOptionValue1,
      surcharge,
      totalPrice: product.basePrice + surcharge
    });
    
    // Add to variants object
    if (!variants[handleId]) {
      variants[handleId] = [];
    }
    
    variants[handleId].push({
      option: productOptionValue1,
      surcharge,
      totalPrice: product.basePrice + surcharge
    });
  }
}

// Output the results
console.log(`Found ${Object.keys(products).length} products with Pack Size options`);

// Count products with price variations
const productsWithVariations = Object.entries(products)
  .filter(([id, product]) => {
    return product.variants.some(v => v.surcharge > 0);
  });

console.log(`Found ${productsWithVariations.length} products with price variations`);

// Output the price variations
console.log('\nProducts with price variations:');
productsWithVariations.forEach(([id, product]) => {
  console.log(`\n${product.name} (${id}) - Base Price: £${product.basePrice}`);
  product.variants.forEach(variant => {
    console.log(`  ${variant.option}: £${variant.totalPrice} (surcharge: £${variant.surcharge})`);
  });
});

// Generate code for fix_variant_prices.js
console.log('\n\nCode for fix_variant_prices.js:');
console.log('const priceVariations = {');
productsWithVariations.forEach(([id, product]) => {
  console.log(`  // ${product.name}`);
  console.log(`  '${id}': {`);
  product.variants.forEach(variant => {
    if (variant.option) {
      console.log(`    '${variant.option}': ${variant.totalPrice},`);
    }
  });
  console.log('  },');
});
console.log('};');
