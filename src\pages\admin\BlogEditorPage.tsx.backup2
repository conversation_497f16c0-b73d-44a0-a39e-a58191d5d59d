// React and Hooks
import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// State Management
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/auth';
import { useToast } from '@/components/ui/use-toast';

// Supabase
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { Blog } from '@/types/database';

// AI and Utilities
import { generateContent, AIProvider } from '@/lib/ai';
import { getImageForTopicAsync } from '@/api/directImageUrl';
import { v4 as uuidv4 } from 'uuid';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Types
type BlogInsert = Database['public']['Tables']['blogs']['Insert'];
type BlogUpdate = Database['public']['Tables']['blogs']['Update'];

type BlogFormData = Omit<BlogInsert, 'id' | 'created_at' | 'updated_at' | 'published_at'> & {
  updated_at: string;
  published_at?: string;
  is_published: boolean;
  is_featured: boolean;
  reading_time: number;
};

type BlogCategoryType = 'wellness' | 'education' | 'lifestyle' | 'research' | 'news';

interface BlogCategoryItem {
  id: BlogCategoryType;
  name: string;
  slug: string;
}

// Constants
const AVAILABLE_CATEGORIES: BlogCategoryItem[] = [
  { id: 'wellness', name: 'Wellness', slug: 'wellness' },
  { id: 'education', name: 'Education', slug: 'education' },
  { id: 'lifestyle', name: 'Lifestyle', slug: 'lifestyle' },
  { id: 'research', name: 'Research', slug: 'research' },
  { id: 'news', name: 'News', slug: 'news' },
];

// Helper function to get category name
const getSafeCategoryName = (category: BlogCategoryType | null | string): string => {
  if (!category) return 'Uncategorized';
  const categoryId = typeof category === 'string' ? category as BlogCategoryType : category;
  const found = AVAILABLE_CATEGORIES.find(cat => cat.id === categoryId);
  return found ? found.name : 'Uncategorized';
};

const BlogEditorPage = () => {
  // 1. Router and auth hooks
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const isEditMode = !!id;

  // 2. Form state
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [content, setContent] = useState('');
  const [summary, setSummary] = useState('');
  const [category, setCategory] = useState<BlogCategoryType | null>(null);
  const [featuredImage, setFeaturedImage] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isPublished, setIsPublished] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [readingTime, setReadingTime] = useState(5);
  
  // 3. UI State
  const [isLoading, setIsLoading] = useState(false);
  const [showAiDialog, setShowAiDialog] = useState(false);
  const [aiProvider, setAiProvider] = useState<AIProvider>('gemini');
  const [aiPrompt, setAiPrompt] = useState('');
  const [generationType, setGenerationType] = useState<'title' | 'content' | 'both'>('both');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [aiImagePrompt, setAiImagePrompt] = useState('');
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  
  // 4. Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 5. Helper functions
  const getCategoryById = useCallback((categoryId: BlogCategoryType | null) => {
    if (!categoryId) return null;
    return AVAILABLE_CATEGORIES.find(cat => cat.id === categoryId) || null;
  }, []);

  // Upload image to Supabase Storage
  const uploadImage = async (file: File): Promise<string> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `blog-images/${fileName}`;
      
      const { error: uploadError } = await supabase.storage
        .from('blog-images')
        .upload(filePath, file);
        
      if (uploadError) throw uploadError;
      
      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('blog-images')
        .getPublicUrl(filePath);
        
      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  
  // Generate and save AI image to Supabase storage using Google Search API
  const refreshAiImage = useCallback(async () => {
    console.log('refreshAiImage called', { title, aiPrompt });
    if (!title && !aiPrompt) {
      toast({
        title: 'Error',
        description: 'Please enter a topic or title for image generation',
        variant: 'destructive',
      });
      return;
    }

    setIsImageLoading(true);
    try {
      const topic = title || aiPrompt;
      // Get image URL from the Google Search API
      console.log('Getting image for topic:', topic);
      const imageUrl = await getImageForTopicAsync(topic);
      console.log('Image URL received:', imageUrl);
      
      if (imageUrl) {
        // Fetch the image to save it to Supabase
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        
        // Check if the response is an image by examining Content-Type
        const contentType = response.headers.get('Content-Type');
        if (!contentType || !contentType.startsWith('image/')) {
          throw new Error('The URL did not return a valid image');
        }
        
        // Convert to blob and then to File
        const blob = await response.blob();
        const file = new File([blob], `${uuidv4()}.${contentType.split('/')[1] || 'jpg'}`, { type: contentType });
        
        // Upload to Supabase storage
        const savedUrl = await uploadImage(file);
        console.log('Image saved to Supabase:', savedUrl);
        setFeaturedImage(savedUrl);
        
        toast({
          title: 'Success',
          description: 'Image generated and saved successfully',
        });
      } else {
        throw new Error('No image URL was returned');
      }
    } catch (error) {
      console.error('Error generating and saving image:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsImageLoading(false);
    }
  }, [title, aiPrompt, toast]);

  // Generate content using AI
  const generateWithAI = useCallback(async () => {
    if (!aiPrompt.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a prompt',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    try {
      const contentType = generationType === 'content' ? 'full' : 
                         generationType === 'title' ? 'topic' : 'full';
      
      const generatedText = await generateContent({
        provider: aiProvider,
        contentType,
        topic: aiPrompt,
        title: generationType === 'content' ? title : undefined,
        existingContent: generationType === 'content' ? content : undefined,
        tone: 'informative',
        length: 'medium'
      });

      if (generationType === 'title' || generationType === 'both') {
        // Extract title from the generated text (first line)
        const titleMatch = generatedText.match(/^#\s*(.+)$/m);
        if (titleMatch) {
          setTitle(titleMatch[1]);
        } else {
          // Fallback: use the first non-empty line as title
          const firstLine = generatedText.split('\n').find(line => line.trim().length > 0);
          if (firstLine) {
            setTitle(firstLine.replace(/^#+\s*/, '').trim());
          }
        }
      }
      
      if ((generationType === 'content' || generationType === 'both') && generatedText) {
        setContent(prevContent => 
          prevContent ? `${prevContent}\n\n${generatedText}` : generatedText
        );
      }
      
      setShowAiDialog(false);
      setAiPrompt('');
    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate content. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  }, [aiPrompt, generationType, aiProvider, title, content, toast]);

  // 6. Data fetching - Fetch blog data if in edit mode
  const { data: blog, isLoading: isLoadingBlog } = useQuery({
    queryKey: ['blog', id],
    queryFn: async () => {
      if (!id) return null;
      const { data, error } = await supabase
        .from('blogs')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data as Blog;
    },
    enabled: isEditMode,
    onSuccess: (data) => {
      if (data) {
        setTitle(data.title);
        setSlug(data.slug);
        setContent(data.content);
        setSummary(data.summary || '');
        setCategory(data.category as BlogCategoryType);
        setFeaturedImage(data.featured_image || '');
        setTags(data.tags || []);
        setIsPublished(data.is_published || false);
        setIsFeatured(data.is_featured || false);
        setReadingTime(data.reading_time || 5);
      }
    },
  });

  // 7. Mutations
  const saveBlogMutation = useMutation<Blog, Error, BlogFormData>({
    mutationFn: async (blogData) => {
      try {
        const { published_at, ...dataToSave } = blogData;
        const dataToSaveWithAuthor = {
          ...dataToSave,
          author_id: user?.id || null,
        };

        if (id) {
          // Update existing blog
          const { data, error } = await supabase
            .from('blogs')
            .update(dataToSaveWithAuthor)
            .eq('id', id)
            .select()
            .single();
          
          if (error) throw error;
          return data as Blog;
        } else {
          // Create new blog
          const { data, error } = await supabase
            .from('blogs')
            .insert([{
              ...dataToSaveWithAuthor,
              published_at: published_at || null
            }])
            .select()
            .single();
          
          if (error) throw error;
          return data as Blog;
        }
      } catch (error) {
        console.error('Error saving blog:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      if (id) {
        queryClient.invalidateQueries({ queryKey: ['blog', id] });
      }
      toast({
        title: 'Success',
        description: id ? 'Blog post updated successfully' : 'Blog post created successfully',
      });
      navigate('/admin/blogs');
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save blog post',
        variant: 'destructive',
      });
    },
  });

  // 8. Event handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim() || !content.trim()) {
      toast({
        title: 'Error',
        description: 'Title and content are required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);
      let imageUrl = featuredImage;
      
      // Upload new image if a file was selected
      if (imageFile) {
        try {
          const fileExt = imageFile.name.split('.').pop();
          const fileName = `${uuidv4()}.${fileExt}`;
          const filePath = `blog-images/${fileName}`;
          
          const { error: uploadError } = await supabase.storage
            .from('blog-images')
            .upload(filePath, imageFile);
            
          if (uploadError) throw uploadError;
          
          // Get the public URL
          const { data: { publicUrl } } = supabase.storage
            .from('blog-images')
            .getPublicUrl(filePath);
            
          imageUrl = publicUrl;
        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast({
            title: 'Error',
            description: 'Failed to upload image',
            variant: 'destructive',
          });
          return;
        }
      }

      // Calculate reading time (words per minute)
      const words = content.trim().split(/\s+/).length;
      const readingTime = Math.ceil(words / 200); // Average reading speed: 200 words per minute

      const blogData: BlogFormData = {
        title: title.trim(),
        slug: slug || title.trim().toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
        content: content.trim(),
        summary: summary.trim() || content.trim().substring(0, 200) + '...',
        category: category || null,
        featured_image: imageUrl || null,
        tags: tags,
        is_published: isPublished,
        is_featured: isFeatured,
        reading_time: readingTime,
        updated_at: new Date().toISOString(),
      };

      await saveBlogMutation.mutateAsync(blogData);
    } catch (error) {
      console.error('Error saving blog:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while saving the blog post',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 9. Render
  if (isLoadingBlog) {
    return (
      <div className="container mx-auto py-8">
        <Skeleton className="h-12 w-64 mb-8" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">
            {isEditMode ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h1>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowAiDialog(true)}
              className="flex items-center space-x-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span>AI Writer</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/admin/blogs')}
            >
              Back to Blog Posts
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Blog Content</CardTitle>
              <CardDescription>
                Create and edit your blog post content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter blog post title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug (URL)</Label>
                <div className="flex space-x-2">
                  <Input
                    id="slug"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder="blog-post-url"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const generatedSlug = title
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/(^-|-$)/g, '');
                      setSlug(generatedSlug);
                    }}
                  >
                    Generate
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Featured Image</Label>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={refreshAiImage}
                    disabled={!title.trim() || isUploading || isImageLoading}
                  >
                    {isImageLoading ? 'Generating...' : 'Generate AI Image'}
                  </Button>
                </div>
                <div className="flex items-center space-x-4">
                  {featuredImage ? (
                    <div className="relative group">
                      <img
                        src={featuredImage}
                        alt="Featured"
                        className="h-32 w-32 object-cover rounded-md"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          setFeaturedImage('');
                          setImageFile(null);
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed rounded-md p-4 text-center">
                      <p className="text-sm text-gray-500 mb-2">
                        No image selected
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        Upload Image
                      </Button>
                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setImageFile(file);
                            setFeaturedImage(URL.createObjectURL(file));
                          }
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your blog post content here..."
                  rows={12}
                  className="font-mono text-sm"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="summary">Summary (Optional)</Label>
                <Textarea
                  id="summary"
                  value={summary}
                  onChange={(e) => setSummary(e.target.value)}
                  placeholder="A short summary of your blog post"
                  rows={3}
                />
                <p className="text-xs text-gray-500">
                  If left empty, a summary will be generated from the first 200 characters of your content.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>
                Additional information about your blog post
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={category || ''}
                    onValueChange={(value: BlogCategoryType) => setCategory(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {AVAILABLE_CATEGORIES.map((cat) => (
                        <SelectItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          className="ml-1.5 inline-flex items-center justify-center h-4 w-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white"
                          onClick={() =>
                            setTags(tags.filter((t) => t !== tag))
                          }
                        >
                          <span className="sr-only">Remove tag</span>
                          <svg
                            className="h-2 w-2"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 8 8"
                          >
                            <path
                              strokeLinecap="round"
                              strokeWidth="1.5"
                              d="M1 1l6 6m0-6L1 7"
                            />
                          </svg>
                        </button>
                      </span>
                    ))}
                    <div className="flex">
                      <Input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && tagInput.trim()) {
                            e.preventDefault();
                            if (!tags.includes(tagInput.trim())) {
                              setTags([...tags, tagInput.trim()]);
                            }
                            setTagInput('');
                          }
                        }}
                        placeholder="Add a tag..."
                        className="w-32"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="ml-2"
                        onClick={() => {
                          if (tagInput.trim() && !tags.includes(tagInput.trim())) {
                            setTags([...tags, tagInput.trim()]);
                            setTagInput('');
                          }
                        }}
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-published"
                    checked={isPublished}
                    onCheckedChange={setIsPublished}
                  />
                  <Label htmlFor="is-published">Publish</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-featured"
                    checked={isFeatured}
                    onCheckedChange={setIsFeatured}
                  />
                  <Label htmlFor="is-featured">Featured</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/blogs')}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUploading}>
              {isUploading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Saving...
                </>
              ) : isEditMode ? (
                'Update Post'
              ) : (
                'Create Post'
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* AI Generation Dialog */}
      <Dialog open={showAiDialog} onOpenChange={setShowAiDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate Content with AI</DialogTitle>
            <DialogDescription>
              Enter a prompt to generate {generationType === 'both' ? 'title and content' : generationType} for your blog post.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="ai-prompt">Prompt</Label>
              <Textarea
                id="ai-prompt"
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="Enter a detailed description of what you want to write about..."
                className="min-h-[100px]"
              />
            </div>
            
            <div className="space-y-2">
              <Label>AI Provider</Label>
              <Select
                value={aiProvider}
                onValueChange={(value: AIProvider) => setAiProvider(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini">Gemini</SelectItem>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                  <SelectItem value="claude">Claude</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Generation Type</Label>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="generate-both"
                    checked={generationType === 'both'}
                    onChange={() => setGenerationType('both')}
                    className="h-4 w-4 text-primary"
                  />
                  <Label htmlFor="generate-both">Title & Content</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="generate-title"
                    checked={generationType === 'title'}
                    onChange={() => setGenerationType('title')}
                    className="h-4 w-4 text-primary"
                  />
                  <Label htmlFor="generate-title">Title Only</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="generate-content"
                    checked={generationType === 'content'}
                    onChange={() => setGenerationType('content')}
                    className="h-4 w-4 text-primary"
                  />
                  <Label htmlFor="generate-content">Content Only</Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setShowAiDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              type="button" 
              onClick={generateWithAI}
              disabled={isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Generate'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BlogEditorPage;
