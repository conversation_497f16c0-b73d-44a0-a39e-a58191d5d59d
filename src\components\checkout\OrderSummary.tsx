import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CartItem } from '@/hooks/useCart';
import { formatPrice } from '@/lib/utils';

interface OrderSummaryProps {
  items: CartItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  discountAmount?: number;
  discountCode?: string;
}

export function OrderSummary({
  items,
  subtotal,
  shipping,
  tax,
  total,
  discountAmount = 0,
  discountCode,
}: OrderSummaryProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Order Summary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Items list */}
        <div className="space-y-3">
          {items.map((item) => (
            <div key={item.id} className="flex justify-between">
              <div className="flex-1">
                <p className="font-medium">{item.product.name}</p>
                <div className="text-sm text-gray-500">
                  {item.variant && (
                    <p>
                      {Object.entries(item.variant.options).map(([key, value], index, arr) => (
                        <span key={key}>
                          {key}={value}
                          {index < arr.length - 1 ? ', ' : ''}
                        </span>
                      ))}
                    </p>
                  )}
                  <p>Quantity: {item.quantity}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">{formatPrice(item.price * item.quantity)}</p>
                {item.variant && item.variant.price_adjustment !== 0 && (
                  <p className="text-xs text-gray-500">
                    {item.variant.price_adjustment > 0 ? '+' : ''}
                    {formatPrice(item.variant.price_adjustment)}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Price breakdown */}
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Subtotal</span>
            <span>{formatPrice(subtotal)}</span>
          </div>

          {discountAmount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>Discount {discountCode ? `(${discountCode})` : ''}</span>
              <span>-{formatPrice(discountAmount)}</span>
            </div>
          )}

          <div className="flex justify-between">
            <span>Shipping</span>
            <span>{shipping === 0 ? 'FREE' : formatPrice(shipping)}</span>
          </div>

          <div className="flex justify-between">
            <span>Tax</span>
            <span>{formatPrice(tax)}</span>
          </div>
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between font-medium text-lg">
          <span>Total</span>
          <span>{formatPrice(total)}</span>
        </div>

        <div className="text-xs text-gray-500 mt-4">
          By placing your order, you agree to our Terms of Service and Privacy Policy
        </div>
      </CardContent>
    </Card>
  );
}
