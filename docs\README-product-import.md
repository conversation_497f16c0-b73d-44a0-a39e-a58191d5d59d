# Product Import Guide

This guide outlines the steps to properly import products with variants into the BITS N BONGS e-commerce platform.

## Prerequisites

- Node.js 16+ installed
- Access to the Supabase database
- Environment variables set up (see .env file)

## Step 1: Clear the Database

Before importing new products, it's recommended to clear the existing products and variants from the database to avoid conflicts.

```bash
# Run the import-test-products.cjs script with the --clear-only flag
node src/scripts/import-test-products.cjs --clear-only
```

## Step 2: Update the Schema

Ensure the database schema is up to date by applying the SQL migration to add the image field to the product_variants table.

```bash
# Apply the SQL migration using the Supabase CLI or directly in the Supabase dashboard
psql -U postgres -d your_database_name -f src/migrations/update_product_variants_schema.sql
```

## Step 3: Import Test Products

Import a small set of test products with variants to verify that the import process works correctly.

```bash
# Run the import-test-products.cjs script
node src/scripts/import-test-products.cjs
```

This script will:
- Clear existing products and variants
- Import test products with predefined variants
- Set up proper relationships between products and variants

## Step 4: Fix Image URLs

Run the fix-image-urls.cjs script to check and fix any invalid image URLs.

```bash
# Run the fix-image-urls.cjs script
node src/scripts/fix-image-urls.cjs
```

This script will:
- Check if each product's image URL is valid
- Set products with missing or invalid images to inactive
- Update variants to use their parent product's image if they don't have one

## Step 5: Import Real Products

Once you've verified that the test products are working correctly, you can import real products from a CSV file.

```bash
# Run the import-variants.cjs script with the path to your CSV file
node src/scripts/import-variants.cjs data/catalog_products.csv
```

### Important Notes for CSV Import

1. **Image URLs**: Ensure that all image URLs in the CSV file are valid and accessible. The import script will process these URLs to match the Supabase storage format.

2. **Option Definitions**: The import script will extract option definitions from the product data in the CSV file. Make sure that option names and values are properly formatted.

3. **Variants**: The import script will create variants based on the option combinations in the CSV file. Make sure that each variant has a unique SKU.

4. **Active Status**: Products without images will be set to inactive by default. You can manually activate them later if needed.

## Troubleshooting

### Images Not Displaying

If images are not displaying correctly, check the following:

1. **Image URLs**: Verify that the image URLs in the database are correct and accessible. You can use the fix-image-urls.cjs script to check and fix invalid URLs.

2. **Supabase Storage**: Ensure that the images are properly uploaded to the Supabase storage bucket. You can check this in the Supabase dashboard.

3. **Frontend Code**: Check the frontend code to ensure that it's correctly handling image URLs. The ProductCard component should handle both full URLs and relative paths.

### Variants Not Displaying

If variants are not displaying correctly, check the following:

1. **Option Definitions**: Verify that the option definitions in the product data are correctly formatted as JSON.

2. **Option Combinations**: Verify that each variant has a valid option combination that matches the option definitions of its parent product.

3. **Frontend Code**: Check the frontend code to ensure that it's correctly handling variant data. The ProductDetailPage component should display variant options and allow selection.

## Best Practices

1. **Test with a Small Dataset**: Always test the import process with a small dataset before importing a large number of products.

2. **Backup the Database**: Before importing a large number of products, make sure to backup the database to avoid data loss.

3. **Check Image URLs**: Ensure that all image URLs are valid and accessible before importing products.

4. **Validate CSV Data**: Validate the CSV data before importing to ensure that it's properly formatted and contains all required fields.

5. **Monitor the Import Process**: Monitor the import process to ensure that it's running smoothly and to catch any errors early.

## Additional Resources

- [Supabase Documentation](https://supabase.io/docs)
- [Node.js Documentation](https://nodejs.org/en/docs/)
- [CSV Parsing with Papa Parse](https://www.papaparse.com/docs)
