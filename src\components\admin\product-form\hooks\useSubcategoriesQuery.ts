import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Category } from '@/types/database';

/**
 * Custom hook to fetch subcategories based on the selected category
 * @param categoryId The ID of the selected category
 * @returns An object containing all subcategories, filtered subcategories, and loading state
 */
export function useSubcategoriesQuery(categoryId?: string | null) {
  // Fetch all subcategories (those with a parent_id)
  const { data: allSubcategories, isLoading } = useQuery({
    queryKey: ['subcategories'],
    queryFn: async () => {
      console.log('Fetching all subcategories');
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .not('parent_id', 'is', null) // Only get subcategories
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching subcategories:', error);
        return [];
      }

      console.log('Fetched subcategories:', data?.length || 0);
      return data as Category[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Filter subcategories based on the selected category
  const filteredSubcategories = allSubcategories && categoryId
    ? allSubcategories.filter(subcat => {
        console.log(`Checking subcategory ${subcat.id} (${subcat.name}) - parent_id: ${subcat.parent_id}, comparing to categoryId: ${categoryId}`);
        return subcat.parent_id === categoryId;
      })
    : [];

  console.log(`Selected category: ${categoryId}, Found ${filteredSubcategories.length} subcategories`);
  if (filteredSubcategories.length > 0) {
    console.log('Filtered subcategories:', filteredSubcategories.map(s => `${s.id}: ${s.name}`).join(', '));
  }

  return {
    subcategories: allSubcategories || [],
    filteredSubcategories,
    isLoading,
  };
}
