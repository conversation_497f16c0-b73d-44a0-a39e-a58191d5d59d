// Debug script to check the exact state of shipping methods in the database
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugShippingMethods() {
  console.log('🔍 Debugging shipping methods database state...');
  
  try {
    // 1. Get all shipping methods
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} total shipping methods:`);
    console.log('-------------------------------------------');
    
    // Sort methods by ID for easier comparison
    methods.sort((a, b) => a.id.localeCompare(b.id));
    
    // Print detailed info for each method
    methods.forEach(method => {
      console.log(`ID: ${method.id}`);
      console.log(`Name: ${method.name}`);
      console.log(`Zone ID: ${method.zone_id}`);
      console.log(`Active: ${method.is_active}`);
      console.log(`Data type of is_active: ${typeof method.is_active}`);
      console.log(`Price: ${method.price}`);
      console.log(`Updated at: ${method.updated_at}`);
      console.log('-------------------------------------------');
    });
    
    // 2. Get all shipping zones
    const { data: zones, error: zonesError } = await supabase
      .from('shipping_zones')
      .select('*');
      
    if (zonesError) {
      console.error('Error fetching shipping zones:', zonesError);
      return;
    }
    
    console.log(`\nFound ${zones.length} shipping zones:`);
    console.log('-------------------------------------------');
    
    zones.forEach(zone => {
      console.log(`ID: ${zone.id}`);
      console.log(`Name: ${zone.name}`);
      console.log(`Active: ${zone.is_active}`);
      console.log(`Countries: ${zone.countries.join(', ')}`);
      console.log('-------------------------------------------');
    });
    
    // 3. Test the query that's used in the checkout page
    console.log('\n🧪 Testing checkout query:');
    
    const testCountry = 'United Kingdom';
    const zone = zones.find(z => z.is_active && z.countries.includes(testCountry));
    
    if (!zone) {
      console.log(`No active zone found for ${testCountry}`);
      return;
    }
    
    console.log(`Found zone for ${testCountry}: ${zone.name} (${zone.id})`);
    
    const { data: activeMethods, error: activeError } = await supabase
      .from('shipping_methods')
      .select('*')
      .eq('zone_id', zone.id)
      .eq('is_active', true);
      
    if (activeError) {
      console.error('Error fetching active methods:', activeError);
      return;
    }
    
    console.log(`Found ${activeMethods.length} active methods for ${testCountry}:`);
    activeMethods.forEach(method => {
      console.log(`- ${method.name} (${method.id})`);
    });
    
    // 4. Test direct SQL query to check for type issues
    console.log('\n🔬 Testing direct SQL query:');
    
    const { data: sqlData, error: sqlError } = await supabase
      .rpc('get_shipping_methods_debug', { zone_id_param: zone.id });
      
    if (sqlError) {
      console.error('Error with SQL query:', sqlError);
    } else {
      console.log('SQL query results:');
      console.log(sqlData);
    }
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
debugShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
  });
