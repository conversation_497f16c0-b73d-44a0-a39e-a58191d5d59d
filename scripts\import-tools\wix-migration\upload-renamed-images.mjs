// <PERSON>ript to upload renamed images to Supabase storage
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client with admin token for higher permissions
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAccessToken = process.env.VITE_SUPABASE_ACCESS_TOKEN || '';

if (!supabaseAccessToken) {
  console.error('Error: VITE_SUPABASE_ACCESS_TOKEN is not set in .env file');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedToken = supabaseAccessToken ? 
  `${supabaseAccessToken.substring(0, 5)}...${supabaseAccessToken.substring(supabaseAccessToken.length - 5)}` : 
  'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with admin token: ${maskedToken}`);

// Create Supabase client with admin token
const supabase = createClient(supabaseUrl, supabaseAccessToken);

// Path to local images
const localImagesPath = path.join(__dirname, 'public', 'images', 'products', 'wix-imports');

// Function to get all image files in the directory
async function getImageFiles() {
  try {
    // Read all files in the directory
    const files = fs.readdirSync(localImagesPath);
    
    // Filter for .webp files
    const imageFiles = files.filter(file => file.toLowerCase().endsWith('.webp'));
    
    console.log(`Found ${imageFiles.length} image files`);
    return imageFiles;
  } catch (error) {
    console.error('Error reading image files:', error);
    return [];
  }
}

// Function to upload images in batches
async function uploadImages(batchSize = 5) {
  console.log('Starting image upload...');
  
  // Get all image files
  const imageFiles = await getImageFiles();
  
  if (imageFiles.length === 0) {
    console.log('No image files found');
    return;
  }
  
  // Create a readline interface for user input
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  // Ask for confirmation
  rl.question(`Do you want to upload ${imageFiles.length} images to Supabase storage? (y/n) `, async (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Upload cancelled');
      rl.close();
      return;
    }
    
    // Upload images in batches
    let uploaded = 0;
    let failed = 0;
    
    for (let i = 0; i < imageFiles.length; i += batchSize) {
      const batch = imageFiles.slice(i, i + batchSize);
      
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(imageFiles.length / batchSize)}`);
      
      // Process batch in parallel
      const results = await Promise.allSettled(
        batch.map(async (filename) => {
          try {
            const filePath = path.join(localImagesPath, filename);
            
            // Check if file exists
            if (!fs.existsSync(filePath)) {
              console.error(`File not found: ${filePath}`);
              return { success: false, error: 'File not found' };
            }
            
            // Read the file
            const fileBuffer = fs.readFileSync(filePath);
            
            // Upload to Supabase storage
            const { data, error } = await supabase.storage
              .from('product-images')
              .upload(filename, fileBuffer, {
                contentType: 'image/webp',
                cacheControl: '3600',
                upsert: true
              });
            
            if (error) {
              console.error(`Error uploading ${filename}:`, error);
              return { success: false, error };
            }
            
            console.log(`Uploaded: ${filename}`);
            return { success: true, data };
          } catch (error) {
            console.error(`Error processing ${filename}:`, error);
            return { success: false, error };
          }
        })
      );
      
      // Count successes and failures
      results.forEach(result => {
        if (result.value && result.value.success) {
          uploaded++;
        } else {
          failed++;
        }
      });
      
      console.log(`Batch progress: ${uploaded + failed}/${imageFiles.length} (${uploaded} uploaded, ${failed} failed)`);
      
      // Wait a bit between batches to avoid rate limiting
      if (i + batchSize < imageFiles.length) {
        console.log('Waiting 2 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    console.log(`Finished! ${uploaded} images uploaded, ${failed} failed`);
    rl.close();
  });
}

// Run the script
uploadImages().catch(console.error);
