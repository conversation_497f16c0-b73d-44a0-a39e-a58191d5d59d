const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const inputFile = 'transformed_variants_clean.csv';
const outputFile = 'transformed_variants_all_prices.csv';

// Products with known price variations
const priceVariations = {
  // DAMN SOUR
  'ed21ef40-6df2-4966-8318-f3d07f3fafcd': {
    '3 Pack': 27.00,
    '5 Pack': 37.00,
    '10 Pack': 70.00
  },
  // Auto Gorilla Glue
  '7b8a11bc-d7df-4db8-b41c-54a1898a983f': {
    '5 Pack': 44.00,
    '10 Pack': 81.00
  },
  // Barneys Farm Blueberry Cheese/Blue Cheese
  '2054873e-da93-4b17-ba64-a43f2b846bf9': {
    '3 Pack': 26.00,
    '5 Pack': 41.00,
    '10 Pack': 77.00
  },
  // Tropicanna Banana
  '8b1b845e-5e2f-4332-84e3-31c9a7c7f201': {
    '3 Pack': 49.00,
    '5 Pack': 93.00,
    '10 Pack': 32.00
  },
  // Barneys Farm Watermelon Zkittlez
  '96d1f702-593a-4ee9-a759-fe2c9030e70c': {
    '3 Pack': 25.00,
    '5 Pack': 50.00,
    '10 Pack': 93.00
  },
  // Greenhouse Seeds Deep Candy
  'c0f708cd-9e2d-43c1-8c61-a329c28283f3': {
    '3 Pack': 26.00,
    '5 Pack': 30.00
  },
  // Barneys Farm Dos Si Dos 33
  '8834e073-1d05-444e-bb3b-cd3078f527f9': {
    '3 Pack': 31.00,
    '5 Pack': 49.00,
    '10 Pack': 86.00
  },
  // Barney's Farm :Banana Punch
  'a4bee8ed-b32c-42fc-a46a-dd71e31a8bf5': {
    '3 Pack': 51.00,
    '5 Pack': 102.00,
    '10 Pack': 34.00
  },
  // 420 Fast Buds: Original Northern Lights
  '42c2033f-181f-4030-acdb-04460dd78d38': {
    '5 Pack': 43.00,
    '10 Pack': 75.00
  },
  // 420 Fast Buds: Blue Dream`matic
  '8fa596e0-07f2-40fa-a452-c0d940f0a7f2': {
    '5 Pack': 48.00,
    '10 Pack': 86.00
  },
  // 420 Fast Buds: Green Crack
  '733d66e9-bed0-4985-b107-734696172739': {
    '5 Pack': 48.00,
    '10 Pack': 86.00
  }
};

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Process the CSV file
console.log(`Reading variants from ${inputFile}...`);
const variants = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    // Check if this product has price variations
    if (priceVariations[row.product_id]) {
      // Get the variant name (e.g., "3 Pack", "5 Pack", "10 Pack")
      const variantName = row.variant_name;
      
      // Check if we have a specific price for this variant
      if (priceVariations[row.product_id][variantName] !== undefined) {
        // Update the price
        row.price = priceVariations[row.product_id][variantName];
        console.log(`Updated price for ${row.product_id} - ${variantName} to £${row.price}`);
      }
    }
    
    variants.push(row);
  })
  .on('end', async () => {
    console.log(`Read ${variants.length} variants from CSV`);

    // Write variants to CSV
    await csvWriter.writeRecords(variants);
    console.log(`Wrote ${variants.length} variants to ${outputFile}`);

    console.log('Price update complete!');
  });
