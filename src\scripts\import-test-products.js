import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase connection details
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test products data
const testProducts = [
  {
    id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    name: 'T-Shirt',
    slug: 't-shirt',
    description: 'Premium cotton t-shirt available in different sizes and colors.',
    price: 10.00,
    sale_price: null,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    additional_images: [],
    sku: 'TS-BASE',
    stock_quantity: 100,
    in_stock: true,
    is_active: true,
    option_definitions: {
      "Size": ["Small", "Medium", "Large"],
      "Color": ["Red", "Blue", "Black"]
    }
  },
  {
    id: '30557c1e-0077-4d83-8974-ccc371193033',
    name: 'Hoodie',
    slug: 'hoodie',
    description: 'Warm hoodie with multiple options.',
    price: 25.00,
    sale_price: null,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_88b20b67b685474ba1e88ca0a89c67c2.webp',
    additional_images: [],
    sku: 'HD-BASE',
    stock_quantity: 50,
    in_stock: true,
    is_active: true,
    option_definitions: {
      "Size": ["Small", "Medium", "Large"],
      "Material": ["Cotton", "Polyester"],
      "Style": ["Zip", "Pullover"]
    }
  },
  {
    id: '15183fba-bb12-4641-931f-7dbeb6384c7c',
    name: 'Hat',
    slug: 'hat',
    description: 'Simple hat with single size option.',
    price: 8.00,
    sale_price: null,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_cfa398ef8e8642a3a2736ed844654439.webp',
    additional_images: [],
    sku: 'HAT-BASE',
    stock_quantity: 100,
    in_stock: true,
    is_active: true,
    option_definitions: {
      "Color": ["White", "Black", "Gray"]
    }
  }
];

// T-Shirt variants
const tShirtVariants = [
  {
    id: '09f8ecac-99c4-4ddc-a2d2-92be1797b47d',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Small / Red',
    sku: 'TS-SM-RED',
    price: 10.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Small", "Color": "Red" },
    is_active: true,
    external_id: 'Product_Test01_v1'
  },
  {
    id: 'e8a81eae-0621-4742-b3d1-56faa41af3ab',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Small / Blue',
    sku: 'TS-SM-BLUE',
    price: 10.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Small", "Color": "Blue" },
    is_active: true,
    external_id: 'Product_Test01_v2'
  },
  {
    id: '6e34a56e-4366-48b2-8168-72d336b33998',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Small / Black',
    sku: 'TS-SM-BLACK',
    price: 10.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Small", "Color": "Black" },
    is_active: true,
    external_id: 'Product_Test01_v3'
  },
  {
    id: '779f2329-aa0a-4cc0-bbc7-a965a3556915',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Medium / Red',
    sku: 'TS-MD-RED',
    price: 12.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Medium", "Color": "Red" },
    is_active: true,
    external_id: 'Product_Test01_v4'
  },
  {
    id: '3e677504-c2a0-4f5b-bb46-94cab95f712d',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Medium / Blue',
    sku: 'TS-MD-BLUE',
    price: 12.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Medium", "Color": "Blue" },
    is_active: true,
    external_id: 'Product_Test01_v5'
  },
  {
    id: 'b4a04549-83ee-4e59-bfd7-957a7fbd28aa',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Medium / Black',
    sku: 'TS-MD-BLACK',
    price: 12.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Medium", "Color": "Black" },
    is_active: true,
    external_id: 'Product_Test01_v6'
  },
  {
    id: 'a0d10cb2-cb8b-44ec-8231-8d30349fac01',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Large / Red',
    sku: 'TS-LG-RED',
    price: 15.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Large", "Color": "Red" },
    is_active: true,
    external_id: 'Product_Test01_v7'
  },
  {
    id: 'd6aa2556-5311-4242-aaac-0251b1a77bf4',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Large / Blue',
    sku: 'TS-LG-BLUE',
    price: 15.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Large", "Color": "Blue" },
    is_active: true,
    external_id: 'Product_Test01_v8'
  },
  {
    id: 'f56db42d-70e4-42c4-9bb8-2802a19a8495',
    product_id: 'ea6edd61-ad21-4eae-82a4-44a939544786',
    variant_name: 'T-Shirt - Large / Black',
    sku: 'TS-LG-BLACK',
    price: 15.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_6a5c1b6181f543109d047a419a70a6aa.webp',
    option_combination: { "Size": "Large", "Color": "Black" },
    is_active: true,
    external_id: 'Product_Test01_v9'
  }
];

// Hat variants
const hatVariants = [
  {
    id: '3f7e2a5c-9b8d-4f1e-8c7a-6b5d4e3f2c1a',
    product_id: '15183fba-bb12-4641-931f-7dbeb6384c7c',
    variant_name: 'Hat - White',
    sku: 'HAT-WHITE',
    price: 8.00,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_cfa398ef8e8642a3a2736ed844654439.webp',
    option_combination: { "Color": "White" },
    is_active: true,
    external_id: 'Product_Test03_v1'
  },
  {
    id: '2e1d9b8c-7a6f-5e4d-3c2b-1a9b8c7d6e5f',
    product_id: '15183fba-bb12-4641-931f-7dbeb6384c7c',
    variant_name: 'Hat - Black',
    sku: 'HAT-BLACK',
    price: 9.50,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_cfa398ef8e8642a3a2736ed844654439.webp',
    option_combination: { "Color": "Black" },
    is_active: true,
    external_id: 'Product_Test03_v2'
  },
  {
    id: '1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p',
    product_id: '15183fba-bb12-4641-931f-7dbeb6384c7c',
    variant_name: 'Hat - Gray',
    sku: 'HAT-GRAY',
    price: 8.50,
    sale_price: null,
    stock_quantity: 30,
    in_stock: true,
    image: 'https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/7caa35_cfa398ef8e8642a3a2736ed844654439.webp',
    option_combination: { "Color": "Gray" },
    is_active: true,
    external_id: 'Product_Test03_v3'
  }
];

// Combine all variants
const allVariants = [...tShirtVariants, ...hatVariants];

// Import function
async function importTestData() {
  try {
    console.log('Importing test products and variants...');
    
    // First, clear existing data
    console.log('Clearing existing products and variants...');
    const { error: truncateError } = await supabase.rpc('truncate_products_and_variants');
    if (truncateError) {
      console.error('Error truncating tables:', truncateError);
      // Continue anyway, as the tables might be empty
    }
    
    // Import products
    console.log(`Importing ${testProducts.length} products...`);
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .insert(testProducts);
    
    if (productsError) {
      console.error('Error importing products:', productsError);
      return;
    }
    
    // Import variants
    console.log(`Importing ${allVariants.length} variants...`);
    const { data: variantsData, error: variantsError } = await supabase
      .from('product_variants')
      .insert(allVariants);
    
    if (variantsError) {
      console.error('Error importing variants:', variantsError);
      return;
    }
    
    console.log('Import completed successfully!');
    console.log(`Imported ${testProducts.length} products and ${allVariants.length} variants.`);
  } catch (error) {
    console.error('Unexpected error during import:', error);
  }
}

// Run the import
importTestData();
