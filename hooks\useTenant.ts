// React Hook for Tenant Management
// Provides tenant context and operations for React components

import { useState, useEffect, useCallback, useContext, createContext } from 'react';
import { User } from '@supabase/supabase-js';
import {
  Tenant,
  UserTenant,
  TenantContext,
  UseTenantResult,
  TenantError,
  CreateTenantForm,
  UpdateTenantForm,
  InviteTenantUserForm
} from '../lib/types/tenant';
import { tenantClient } from '../lib/utils/tenant';

/**
 * Tenant Context for React
 */
const TenantContextReact = createContext<TenantContext | null>(null);

/**
 * Tenant Provider Component
 */
export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [userTenants, setUserTenants] = useState<UserTenant[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<TenantError | null>(null);

  const contextValue: TenantContext = {
    currentTenant,
    userTenants,
    loading,
    error,
    setCurrentTenant,
    setUserTenants,
    setLoading,
    setError
  };

  return (
    <TenantContextReact.Provider value={contextValue}>
      {children}
    </TenantContextReact.Provider>
  );
}

/**
 * Main tenant management hook
 */
export function useTenant(user?: User): UseTenantResult {
  const context = useContext(TenantContextReact);
  
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }

  const {
    currentTenant,
    userTenants,
    loading,
    error,
    setCurrentTenant,
    setUserTenants,
    setLoading,
    setError
  } = context;

  /**
   * Load user's tenants
   */
  const loadUserTenants = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      const tenants = await tenantClient.getUserTenants(user.id);
      setUserTenants(tenants);
      
      // Set first tenant as current if none selected
      if (!currentTenant && tenants.length > 0) {
        await switchTenant(tenants[0].tenant_id);
      }
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to load user tenants',
        'LOAD_TENANTS_ERROR'
      );
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [user?.id, currentTenant]);

  /**
   * Switch to a different tenant
   */
  const switchTenant = useCallback(async (tenantId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Set tenant context in Supabase
      await tenantClient.setTenantContext(tenantId);
      
      // Get tenant details
      const tenant = await tenantClient.getTenant(tenantId);
      setCurrentTenant(tenant);
      
      // Store in localStorage for persistence
      localStorage.setItem('currentTenantId', tenantId);
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to switch tenant',
        'SWITCH_TENANT_ERROR',
        tenantId
      );
      setError(error);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new tenant
   */
  const createTenant = useCallback(async (tenantData: CreateTenantForm) => {
    if (!user?.id) throw new TenantError('User not authenticated', 'AUTH_ERROR');

    try {
      setLoading(true);
      setError(null);
      
      const newTenant = await tenantClient.createTenant(tenantData, user.id);
      
      // Reload user tenants
      await loadUserTenants();
      
      // Switch to new tenant
      await switchTenant(newTenant.id);
      
      return newTenant;
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to create tenant',
        'CREATE_TENANT_ERROR'
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user?.id, loadUserTenants, switchTenant]);

  /**
   * Update current tenant
   */
  const updateTenant = useCallback(async (updates: UpdateTenantForm) => {
    if (!currentTenant) throw new TenantError('No current tenant', 'NO_TENANT_ERROR');

    try {
      setLoading(true);
      setError(null);
      
      const updatedTenant = await tenantClient.updateTenant(currentTenant.id, updates);
      setCurrentTenant(updatedTenant);
      
      // Update in user tenants list
      setUserTenants(prev => prev.map(ut => 
        ut.tenant_id === updatedTenant.id 
          ? { ...ut, tenant_name: updatedTenant.name }
          : ut
      ));
      
      return updatedTenant;
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to update tenant',
        'UPDATE_TENANT_ERROR',
        currentTenant.id
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  /**
   * Invite user to current tenant
   */
  const inviteUser = useCallback(async (inviteData: InviteTenantUserForm) => {
    if (!currentTenant) throw new TenantError('No current tenant', 'NO_TENANT_ERROR');

    try {
      setLoading(true);
      setError(null);
      
      await tenantClient.inviteUserToTenant(currentTenant.id, inviteData);
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to invite user',
        'INVITE_USER_ERROR',
        currentTenant.id
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [currentTenant]);

  /**
   * Check if user has required role
   */
  const hasRole = useCallback((requiredRole: string): boolean => {
    if (!currentTenant || !user?.id) return false;
    
    const userTenant = userTenants.find(ut => ut.tenant_id === currentTenant.id);
    if (!userTenant) return false;
    
    const roleHierarchy = { owner: 3, admin: 2, member: 1 };
    const userLevel = roleHierarchy[userTenant.role as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return userLevel >= requiredLevel;
  }, [currentTenant, user?.id, userTenants]);

  /**
   * Check if user can perform action
   */
  const canPerformAction = useCallback((action: string): boolean => {
    switch (action) {
      case 'create_product':
      case 'edit_product':
      case 'delete_product':
        return hasRole('admin');
      case 'manage_users':
      case 'manage_settings':
        return hasRole('owner');
      case 'view_analytics':
        return hasRole('admin');
      case 'create_order':
      case 'view_orders':
        return hasRole('member');
      default:
        return false;
    }
  }, [hasRole]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Initialize tenant context from localStorage
   */
  useEffect(() => {
    const initializeTenant = async () => {
      if (!user?.id) return;
      
      const savedTenantId = localStorage.getItem('currentTenantId');
      if (savedTenantId) {
        try {
          // Check if user has access to saved tenant
          const hasAccess = await tenantClient.checkTenantAccess(user.id, savedTenantId);
          if (hasAccess) {
            await switchTenant(savedTenantId);
          } else {
            localStorage.removeItem('currentTenantId');
          }
        } catch (err) {
          localStorage.removeItem('currentTenantId');
        }
      }
      
      // Load user tenants
      await loadUserTenants();
    };

    initializeTenant();
  }, [user?.id]);

  return {
    // State
    currentTenant,
    userTenants,
    loading,
    error,
    
    // Actions
    switchTenant,
    createTenant,
    updateTenant,
    inviteUser,
    loadUserTenants,
    clearError,
    
    // Utilities
    hasRole,
    canPerformAction,
    
    // Computed
    isOwner: hasRole('owner'),
    isAdmin: hasRole('admin'),
    isMember: hasRole('member'),
    hasTenants: userTenants.length > 0,
    canCreateTenant: true, // All authenticated users can create tenants
    canManageUsers: hasRole('owner'),
    canManageSettings: hasRole('owner'),
    canViewAnalytics: hasRole('admin')
  };
}

/**
 * Hook for tenant-aware data fetching
 */
export function useTenantData() {
  const { currentTenant, loading: tenantLoading } = useTenant();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<TenantError | null>(null);

  /**
   * Fetch tenant-aware data with automatic retry
   */
  const fetchData = useCallback(async <T>(
    fetchFn: () => Promise<{ data: T | null; error: any }>,
    retries = 3
  ): Promise<T | null> => {
    if (!currentTenant) {
      throw new TenantError('No tenant context', 'NO_TENANT_CONTEXT');
    }

    let lastError: any;
    
    for (let i = 0; i < retries; i++) {
      try {
        setLoading(true);
        setError(null);
        
        const { data, error } = await fetchFn();
        
        if (error) {
          lastError = error;
          continue;
        }
        
        return data;
      } catch (err) {
        lastError = err;
      }
    }
    
    const tenantError = lastError instanceof TenantError ? lastError : new TenantError(
      `Failed to fetch data: ${lastError?.message || 'Unknown error'}`,
      'FETCH_ERROR',
      currentTenant.id
    );
    
    setError(tenantError);
    throw tenantError;
  }, [currentTenant]);

  /**
   * Get products for current tenant
   */
  const getProducts = useCallback(() => {
    return fetchData(() => tenantClient.getProducts());
  }, [fetchData]);

  /**
   * Get categories for current tenant
   */
  const getCategories = useCallback(() => {
    return fetchData(() => tenantClient.getCategories());
  }, [fetchData]);

  /**
   * Get brands for current tenant
   */
  const getBrands = useCallback(() => {
    return fetchData(() => tenantClient.getBrands());
  }, [fetchData]);

  /**
   * Get orders for current tenant
   */
  const getOrders = useCallback(() => {
    return fetchData(() => tenantClient.getOrders());
  }, [fetchData]);

  /**
   * Get blogs for current tenant
   */
  const getBlogs = useCallback(() => {
    return fetchData(() => tenantClient.getBlogs());
  }, [fetchData]);

  return {
    loading: loading || tenantLoading,
    error,
    fetchData,
    getProducts,
    getCategories,
    getBrands,
    getOrders,
    getBlogs,
    clearError: () => setError(null)
  };
}

/**
 * Hook for tenant testing and validation
 */
export function useTenantTesting() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<TenantError | null>(null);

  const runIsolationTests = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      return await tenantClient.testTenantIsolation();
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to run isolation tests',
        'TEST_ERROR'
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const runPerformanceTests = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      return await tenantClient.testTenantPerformance();
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to run performance tests',
        'TEST_ERROR'
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const validateRlsPolicies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      return await tenantClient.validateRlsPolicies();
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to validate RLS policies',
        'VALIDATION_ERROR'
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const checkDataDistribution = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      return await tenantClient.checkTenantDataDistribution();
    } catch (err) {
      const error = err instanceof TenantError ? err : new TenantError(
        'Failed to check data distribution',
        'DATA_CHECK_ERROR'
      );
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    runIsolationTests,
    runPerformanceTests,
    validateRlsPolicies,
    checkDataDistribution,
    clearError: () => setError(null)
  };
}