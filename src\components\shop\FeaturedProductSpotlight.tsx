import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import type { Product as BaseProduct } from '@/types/database';

// Extended Product interface with additional fields
interface Product extends Omit<BaseProduct, 'is_featured'> {
  image_url?: string;
  compare_at_price?: string;
  is_featured: boolean;
}
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sparkles, ArrowRight, Star } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface FeaturedProductSpotlightProps {
  limit?: number;
}

const FeaturedProductSpotlight: React.FC<FeaturedProductSpotlightProps> = ({ limit = 5 }) => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Helper function to shuffle array (Fisher-Yates algorithm)
  const shuffleArray = <T,>(array: T[]): T[] => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // Fetch featured products
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        console.log('Fetching featured products...');
        
        // First, get all categories to ensure diversity
        const { data: categories } = await supabase
          .from('categories')
          .select('id');
          
        const categoryIds = categories?.map(cat => cat.id) || [];
        
        // Get products from all categories
        let { data, error } = await supabase
          .from('products')
          .select('*, category_id')
          .eq('is_active', true)
          .order('created_at', { ascending: false })
          .limit(limit * 5); // Fetch more to ensure we have enough diversity

        if (error) throw error;
        
        console.log(`Found ${data?.length || 0} products`);
        
        if (!data || data.length === 0) {
          console.log('No products found at all');
          setLoading(false);
          return;
        }
        
        // First try to get featured products
        let featuredProducts = data.filter(product => product.is_featured === true);
        
        // If we have featured products, ensure category diversity
        if (featuredProducts.length > 0) {
          console.log(`Found ${featuredProducts.length} featured products`);
          
          // Group by category
          const productsByCategory: Record<string, Product[]> = {};
          
          featuredProducts.forEach(product => {
            const catId = product.category_id || 'unknown';
            if (!productsByCategory[catId]) {
              productsByCategory[catId] = [];
            }
            productsByCategory[catId].push(product as Product);
          });
          
          // Take one product from each category first (if possible)
          let selectedProducts: Product[] = [];
          
          // Get one product from each category first
          Object.values(productsByCategory).forEach(products => {
            if (products.length > 0 && selectedProducts.length < limit) {
              // Randomly select one product from this category
              const randomIndex = Math.floor(Math.random() * products.length);
              selectedProducts.push(products[randomIndex]);
            }
          });
          
          // If we still need more products, add random ones from the pool
          if (selectedProducts.length < limit) {
            // Shuffle all featured products and add until we reach the limit
            const remainingNeeded = limit - selectedProducts.length;
            const remainingProducts = shuffleArray(featuredProducts)
              .filter(p => !selectedProducts.includes(p))
              .slice(0, remainingNeeded);
              
            selectedProducts = [...selectedProducts, ...remainingProducts];
          }
          
          // Final shuffle to randomize the order
          setFeaturedProducts(shuffleArray(selectedProducts));
        } else {
          // If no featured products, use a diverse set of regular products
          console.log('No featured products found, using diverse product set');
          
          // Group regular products by category
          const productsByCategory: Record<string, Product[]> = {};
          
          data.forEach(product => {
            const catId = product.category_id || 'unknown';
            if (!productsByCategory[catId]) {
              productsByCategory[catId] = [];
            }
            productsByCategory[catId].push(product as Product);
          });
          
          // Take one product from each category first
          let selectedProducts: Product[] = [];
          
          // Get one product from each category first
          Object.values(productsByCategory).forEach(products => {
            if (products.length > 0 && selectedProducts.length < limit) {
              // Randomly select one product from this category
              const randomIndex = Math.floor(Math.random() * products.length);
              selectedProducts.push(products[randomIndex]);
            }
          });
          
          // If we still need more products, add random ones from the pool
          if (selectedProducts.length < limit) {
            // Shuffle all products and add until we reach the limit
            const remainingNeeded = limit - selectedProducts.length;
            const remainingProducts = shuffleArray(data as Product[])
              .filter(p => !selectedProducts.includes(p))
              .slice(0, remainingNeeded);
              
            selectedProducts = [...selectedProducts, ...remainingProducts];
          }
          
          // Final shuffle to randomize the order
          setFeaturedProducts(shuffleArray(selectedProducts));
        }
      } catch (error) {
        console.error('Error fetching featured products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, [limit]);

  // Auto-rotate products every 5 seconds
  useEffect(() => {
    if (featuredProducts.length <= 1) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % featuredProducts.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [featuredProducts]);

  // Handle manual navigation
  const goToProduct = (index: number) => {
    setCurrentIndex(index);
  };

  const viewProductDetails = (product: Product) => {
    navigate(`/shop/product/${product.id}`);
  };

  if (loading) {
    return (
      <div className="w-full h-[300px] bg-gray-100 animate-pulse rounded-lg flex items-center justify-center">
        <Sparkles className="w-8 h-8 text-gray-300 animate-spin" />
      </div>
    );
  }

  if (featuredProducts.length === 0) {
    return null;
  }

  const currentProduct = featuredProducts[currentIndex];
  
  // Helper function to get product image URL
  const getProductImageUrl = (product: Product) => {
    // Check for image field in different possible formats
    const imageField = product.image_url || product.image || '';
    
    if (!imageField) return 'https://placehold.co/600x400?text=No+Image';
    
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    
    if (imageField.startsWith('http')) {
      return imageField;
    } else if (imageField.startsWith('/storage/')) {
      return `${supabaseUrl}${imageField}`;
    } else if (imageField.startsWith('product-images/')) {
      return `${supabaseUrl}/storage/v1/object/public/${imageField}`;
    } else {
      return `${supabaseUrl}/storage/v1/object/public/product-images/${imageField}`;
    }
  };

  return (
    <div className="w-full mb-12 overflow-hidden">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold flex items-center">
          <Sparkles className="w-5 h-5 mr-2 text-amber-400" />
          Featured Products
        </h2>
        <div className="flex space-x-1">
          {featuredProducts.map((_, index) => (
            <button
              key={index}
              onClick={() => goToProduct(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex ? 'bg-primary w-4' : 'bg-gray-300'
              }`}
              aria-label={`Go to product ${index + 1}`}
            />
          ))}
        </div>
      </div>

      <div className="relative w-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="w-full"
          >
            <Card className="overflow-hidden border-0 shadow-lg">
              <div className="flex flex-col md:flex-row">
                <div className="w-full md:w-1/2 relative">
                  <div className="absolute top-4 left-4 z-10">
                    <Badge className="bg-amber-500 hover:bg-amber-600">
                      <Star className="w-3 h-3 mr-1" /> Featured
                    </Badge>
                  </div>
                  <div className="h-[300px] overflow-hidden">
                    <img
                      src={getProductImageUrl(currentProduct)}
                      alt={currentProduct.name}
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-700"
                    />
                  </div>
                </div>
                <div className="w-full md:w-1/2 p-6 flex flex-col justify-between bg-gradient-to-br from-white to-gray-50">
                  <div>
                    <h3 className="text-xl font-bold mb-2">{currentProduct.name}</h3>
                    <div 
                      className="text-gray-600 mb-4 line-clamp-3"
                      dangerouslySetInnerHTML={{ 
                        __html: currentProduct.description?.replace(/<\/?p>/g, '') || ''
                      }}
                    />
                    <div className="flex items-center mb-4">
                      <span className="text-2xl font-bold text-primary">
                        £{typeof currentProduct.price === 'string' 
                          ? parseFloat(currentProduct.price).toFixed(2) 
                          : currentProduct.price.toFixed(2)}
                      </span>
                      {currentProduct.compare_at_price && (
                        <span className="ml-2 text-gray-500 line-through">
                          £{typeof currentProduct.compare_at_price === 'string' 
                            ? parseFloat(currentProduct.compare_at_price).toFixed(2) 
                            : parseFloat(String(currentProduct.compare_at_price)).toFixed(2)}
                        </span>
                      )}
                    </div>
                  </div>
                  <Button 
                    onClick={() => viewProductDetails(currentProduct)}
                    className="mt-4 w-full md:w-auto group"
                  >
                    View Details
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default FeaturedProductSpotlight;
