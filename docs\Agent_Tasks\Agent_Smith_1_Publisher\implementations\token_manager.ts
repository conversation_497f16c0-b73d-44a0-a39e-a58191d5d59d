/**
 * Token Manager Implementation
 * 
 * This file implements the TokenManager interface for the Social Media Publishing System.
 * It handles secure storage, retrieval, and refresh of OAuth tokens for social media platforms.
 */

import axios from 'axios';
import { TokenData, SocialPlatform } from './social_media_types';
import { TokenManager } from './social_media_publisher';

/**
 * Interface for database operations related to token management
 */
interface TokenDatabase {
  getTokenData(accountId: string): Promise<TokenData | null>;
  storeTokenData(accountId: string, tokenData: TokenData): Promise<boolean>;
  updateTokenData(accountId: string, tokenData: Partial<TokenData>): Promise<boolean>;
  getAccountPlatform(accountId: string): Promise<SocialPlatform>;
}

/**
 * Configuration for OAuth token refresh
 */
interface OAuthConfig {
  instagram: {
    clientId: string;
    clientSecret: string;
    tokenUrl: string;
  };
  facebook: {
    clientId: string;
    clientSecret: string;
    tokenUrl: string;
  };
  twitter: {
    clientId: string;
    clientSecret: string;
    tokenUrl: string;
  };
  tiktok: {
    clientKey: string;
    clientSecret: string;
    tokenUrl: string;
  };
}

/**
 * Implementation of the TokenManager interface
 */
export class OAuthTokenManager implements TokenManager {
  private database: TokenDatabase;
  private config: OAuthConfig;
  private tokenCache: Map<string, { token: string; expiresAt: Date }> = new Map();
  
  /**
   * Create a new OAuthTokenManager
   * @param database The token database
   * @param config OAuth configuration
   */
  constructor(database: TokenDatabase, config: OAuthConfig) {
    this.database = database;
    this.config = config;
  }
  
  /**
   * Get a valid access token for an account
   * @param accountId The account ID
   * @returns Promise resolving to access token
   */
  async getAccessToken(accountId: string): Promise<string> {
    // Check cache first
    const cachedToken = this.tokenCache.get(accountId);
    if (cachedToken && cachedToken.expiresAt > new Date()) {
      return cachedToken.token;
    }
    
    // Get token from database
    const tokenData = await this.database.getTokenData(accountId);
    if (!tokenData) {
      throw new Error(`No token data found for account: ${accountId}`);
    }
    
    // Check if token is expired or about to expire (within 5 minutes)
    const now = new Date();
    const expiresAt = tokenData.expiresAt;
    const isExpired = expiresAt && expiresAt < new Date(now.getTime() + 5 * 60 * 1000);
    
    if (isExpired && tokenData.refreshToken) {
      // Token is expired or about to expire, refresh it
      return this.refreshToken(accountId).then(() => this.getAccessToken(accountId));
    }
    
    // Cache the token
    if (tokenData.expiresAt) {
      this.tokenCache.set(accountId, {
        token: tokenData.accessToken,
        expiresAt: tokenData.expiresAt
      });
    }
    
    return tokenData.accessToken;
  }
  
  /**
   * Store token data for an account
   * @param accountId The account ID
   * @param tokenData The token data to store
   * @returns Promise resolving to boolean indicating success
   */
  async storeTokenData(accountId: string, tokenData: TokenData): Promise<boolean> {
    // Store token in database
    const result = await this.database.storeTokenData(accountId, tokenData);
    
    // Update cache if successful
    if (result && tokenData.expiresAt) {
      this.tokenCache.set(accountId, {
        token: tokenData.accessToken,
        expiresAt: tokenData.expiresAt
      });
    }
    
    return result;
  }
  
  /**
   * Refresh token for an account
   * @param accountId The account ID
   * @returns Promise resolving to boolean indicating success
   */
  async refreshToken(accountId: string): Promise<boolean> {
    // Get current token data
    const tokenData = await this.database.getTokenData(accountId);
    if (!tokenData || !tokenData.refreshToken) {
      throw new Error(`No refresh token found for account: ${accountId}`);
    }
    
    // Get platform for this account
    const platform = await this.database.getAccountPlatform(accountId);
    
    // Refresh token based on platform
    try {
      let newTokenData: Partial<TokenData>;
      
      switch (platform) {
        case 'instagram':
        case 'facebook':
          newTokenData = await this.refreshFacebookToken(tokenData.refreshToken);
          break;
        case 'twitter':
          newTokenData = await this.refreshTwitterToken(tokenData.refreshToken);
          break;
        case 'tiktok':
          newTokenData = await this.refreshTikTokToken(tokenData.refreshToken);
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }
      
      // Update token in database
      await this.database.updateTokenData(accountId, newTokenData);
      
      // Update cache
      if (newTokenData.accessToken && newTokenData.expiresAt) {
        this.tokenCache.set(accountId, {
          token: newTokenData.accessToken,
          expiresAt: newTokenData.expiresAt
        });
      }
      
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }
  
  /**
   * Check if token is valid
   * @param accountId The account ID
   * @returns Promise resolving to boolean indicating if token is valid
   */
  async isTokenValid(accountId: string): Promise<boolean> {
    // Check cache first
    const cachedToken = this.tokenCache.get(accountId);
    if (cachedToken && cachedToken.expiresAt > new Date()) {
      return true;
    }
    
    // Get token from database
    const tokenData = await this.database.getTokenData(accountId);
    if (!tokenData) {
      return false;
    }
    
    // Check if token is expired
    const now = new Date();
    const expiresAt = tokenData.expiresAt;
    
    if (!expiresAt) {
      // If no expiration time, assume it's valid (some platforms use long-lived tokens)
      return true;
    }
    
    return expiresAt > now;
  }
  
  /**
   * Refresh a Facebook/Instagram token
   * @param refreshToken The refresh token
   * @returns Promise resolving to new token data
   */
  private async refreshFacebookToken(refreshToken: string): Promise<Partial<TokenData>> {
    const response = await axios.get(
      this.config.facebook.tokenUrl,
      {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: this.config.facebook.clientId,
          client_secret: this.config.facebook.clientSecret,
          fb_exchange_token: refreshToken
        }
      }
    );
    
    const data = response.data;
    
    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + data.expires_in);
    
    return {
      accessToken: data.access_token,
      refreshToken: data.access_token, // For Facebook, the new access token is also the refresh token
      expiresAt
    };
  }
  
  /**
   * Refresh a Twitter token
   * @param refreshToken The refresh token
   * @returns Promise resolving to new token data
   */
  private async refreshTwitterToken(refreshToken: string): Promise<Partial<TokenData>> {
    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('refresh_token', refreshToken);
    
    const response = await axios.post(
      this.config.twitter.tokenUrl,
      params,
      {
        auth: {
          username: this.config.twitter.clientId,
          password: this.config.twitter.clientSecret
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    const data = response.data;
    
    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + data.expires_in);
    
    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt,
      scopes: data.scope.split(' ')
    };
  }
  
  /**
   * Refresh a TikTok token
   * @param refreshToken The refresh token
   * @returns Promise resolving to new token data
   */
  private async refreshTikTokToken(refreshToken: string): Promise<Partial<TokenData>> {
    const params = new URLSearchParams();
    params.append('client_key', this.config.tiktok.clientKey);
    params.append('client_secret', this.config.tiktok.clientSecret);
    params.append('grant_type', 'refresh_token');
    params.append('refresh_token', refreshToken);
    
    const response = await axios.post(
      this.config.tiktok.tokenUrl,
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    const data = response.data.data;
    
    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + data.expires_in);
    
    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt,
      scopes: data.scope.split(',')
    };
  }
  
  /**
   * Clear token cache for an account
   * @param accountId The account ID
   */
  clearCache(accountId: string): void {
    this.tokenCache.delete(accountId);
  }
  
  /**
   * Clear all token cache
   */
  clearAllCache(): void {
    this.tokenCache.clear();
  }
}
