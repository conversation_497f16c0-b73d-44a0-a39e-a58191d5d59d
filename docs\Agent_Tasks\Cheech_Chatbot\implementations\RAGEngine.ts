/**
 * RAG Engine Implementation
 * 
 * This file implements the Retrieval Augmented Generation (RAG) engine for the Cheech chatbot.
 * It handles document retrieval, embedding generation, and query processing.
 */

import { Document, QueryResult, RAGService } from './types';
import { OpenAI } from 'openai';
import { 
  VectorStore, 
  MemoryVectorStore,
  Document as LangchainDocument,
  OpenAIEmbeddings
} from 'langchain/vectorstores';

/**
 * Implementation of the RAG service using LangChain and OpenAI
 */
export class RAGEngine implements RAGService {
  private vectorStore: VectorStore;
  private openai: OpenAI;
  private embeddings: OpenAIEmbeddings;
  
  /**
   * Create a new RAGEngine
   * @param apiKey OpenAI API key
   * @param initialDocuments Optional initial documents to add to the vector store
   */
  constructor(apiKey: string, initialDocuments?: Document[]) {
    // Initialize OpenAI client
    this.openai = new OpenAI({
      apiKey: apiKey
    });
    
    // Initialize embeddings model
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey,
      modelName: 'text-embedding-ada-002'
    });
    
    // Initialize vector store
    this.vectorStore = new MemoryVectorStore(this.embeddings);
    
    // Add initial documents if provided
    if (initialDocuments && initialDocuments.length > 0) {
      this.addDocuments(initialDocuments);
    }
  }
  
  /**
   * Query the RAG engine for relevant documents
   * @param text The query text
   * @param filters Optional filters for the query
   * @returns Promise resolving to query result
   */
  async query(text: string, filters?: Record<string, any>): Promise<QueryResult> {
    try {
      // Convert filters to LangChain format if provided
      const metadataFilters = filters ? this.convertFiltersToMetadataFilters(filters) : undefined;
      
      // Perform similarity search with metadata filtering
      const searchResults = await this.vectorStore.similaritySearchWithScore(
        text,
        5, // Get top 5 results
        metadataFilters
      );
      
      // Extract documents and scores
      const documents: Document[] = [];
      const relevanceScores: number[] = [];
      
      for (const [doc, score] of searchResults) {
        documents.push({
          id: doc.metadata.id || '',
          content: doc.pageContent,
          metadata: {
            source: doc.metadata.source || 'unknown',
            category: doc.metadata.category,
            productId: doc.metadata.productId,
            createdAt: new Date(doc.metadata.createdAt || Date.now()),
            updatedAt: new Date(doc.metadata.updatedAt || Date.now())
          }
        });
        
        // Convert similarity score to relevance score (0-1)
        // Note: LangChain similarity scores are typically cosine similarity
        relevanceScores.push(score);
      }
      
      return {
        query: text,
        documents,
        relevanceScores
      };
    } catch (error) {
      console.error('Error querying RAG engine:', error);
      throw new Error(`RAG query failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Add documents to the vector store
   * @param documents The documents to add
   * @returns Promise resolving when documents are added
   */
  async addDocuments(documents: Document[]): Promise<void> {
    try {
      // Convert documents to LangChain format
      const langchainDocs: LangchainDocument[] = documents.map(doc => ({
        pageContent: doc.content,
        metadata: {
          id: doc.id,
          source: doc.metadata.source,
          category: doc.metadata.category,
          productId: doc.metadata.productId,
          createdAt: doc.metadata.createdAt.toISOString(),
          updatedAt: doc.metadata.updatedAt.toISOString()
        }
      }));
      
      // Add documents to vector store
      await this.vectorStore.addDocuments(langchainDocs);
    } catch (error) {
      console.error('Error adding documents to RAG engine:', error);
      throw new Error(`Failed to add documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Delete documents from the vector store
   * @param ids The document IDs to delete
   * @returns Promise resolving when documents are deleted
   */
  async deleteDocuments(ids: string[]): Promise<void> {
    try {
      // Delete documents from vector store
      // Note: This implementation depends on the specific vector store being used
      // For MemoryVectorStore, we would need to rebuild the store without these documents
      // For a production implementation, use a vector store with proper delete support
      
      // This is a placeholder implementation
      console.warn('Document deletion not fully implemented for MemoryVectorStore');
      
      // In a real implementation with a database-backed vector store:
      // await this.vectorStore.delete({ ids });
    } catch (error) {
      console.error('Error deleting documents from RAG engine:', error);
      throw new Error(`Failed to delete documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Convert generic filters to metadata filters for LangChain
   * @param filters The filters to convert
   * @returns Metadata filters for LangChain
   */
  private convertFiltersToMetadataFilters(filters: Record<string, any>): any {
    // Convert filters to LangChain format
    const metadataFilters: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null) {
        metadataFilters[key] = value;
      }
    }
    
    return metadataFilters;
  }
  
  /**
   * Generate embedding for a text
   * @param text The text to generate embedding for
   * @returns Promise resolving to embedding
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text
      });
      
      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

/**
 * Document processor for preparing documents for the RAG engine
 */
export class DocumentProcessor {
  /**
   * Split a document into chunks
   * @param document The document to split
   * @param chunkSize The maximum chunk size
   * @param chunkOverlap The overlap between chunks
   * @returns Array of document chunks
   */
  static splitDocument(
    document: Document,
    chunkSize: number = 1000,
    chunkOverlap: number = 200
  ): Document[] {
    // Simple text splitting implementation
    const text = document.content;
    const chunks: Document[] = [];
    
    // If text is shorter than chunk size, return as is
    if (text.length <= chunkSize) {
      return [document];
    }
    
    // Split text into chunks
    let startIndex = 0;
    while (startIndex < text.length) {
      // Calculate end index for this chunk
      const endIndex = Math.min(startIndex + chunkSize, text.length);
      
      // Extract chunk text
      const chunkText = text.substring(startIndex, endIndex);
      
      // Create new document for this chunk
      chunks.push({
        id: `${document.id}-chunk-${chunks.length}`,
        content: chunkText,
        metadata: {
          ...document.metadata,
          chunkIndex: chunks.length,
          parentDocumentId: document.id
        }
      });
      
      // Move to next chunk, accounting for overlap
      startIndex = endIndex - chunkOverlap;
      
      // Ensure we make progress even with large overlap
      if (startIndex <= 0 || startIndex >= text.length - 10) {
        break;
      }
    }
    
    return chunks;
  }
  
  /**
   * Process product data into documents for the RAG engine
   * @param product The product data
   * @returns Array of documents
   */
  static processProductData(product: any): Document[] {
    const documents: Document[] = [];
    
    // Main product document
    const mainDocument: Document = {
      id: `product-${product.id}`,
      content: `
        Product Name: ${product.name}
        Category: ${product.category}
        ${product.subcategory ? `Subcategory: ${product.subcategory}` : ''}
        Price: $${product.price}
        Description: ${product.description}
        Tags: ${product.tags.join(', ')}
      `,
      metadata: {
        source: 'product_catalog',
        category: product.category,
        productId: product.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };
    
    documents.push(mainDocument);
    
    // Additional documents for detailed information if available
    if (product.detailedDescription) {
      documents.push({
        id: `product-${product.id}-details`,
        content: product.detailedDescription,
        metadata: {
          source: 'product_details',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    if (product.usageInstructions) {
      documents.push({
        id: `product-${product.id}-usage`,
        content: `
          Usage Instructions for ${product.name}:
          ${product.usageInstructions}
        `,
        metadata: {
          source: 'product_usage',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    if (product.reviews && product.reviews.length > 0) {
      const reviewsText = product.reviews
        .map((review: any) => `
          Rating: ${review.rating}/5
          Review: ${review.text}
        `)
        .join('\n\n');
      
      documents.push({
        id: `product-${product.id}-reviews`,
        content: `
          Reviews for ${product.name}:
          ${reviewsText}
        `,
        metadata: {
          source: 'product_reviews',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    return documents;
  }
  
  /**
   * Process FAQ data into documents for the RAG engine
   * @param faq The FAQ data
   * @returns Array of documents
   */
  static processFAQData(faq: any): Document[] {
    return [{
      id: `faq-${faq.id}`,
      content: `
        Question: ${faq.question}
        Answer: ${faq.answer}
        ${faq.category ? `Category: ${faq.category}` : ''}
        ${faq.tags ? `Tags: ${faq.tags.join(', ')}` : ''}
      `,
      metadata: {
        source: 'faq',
        category: faq.category || 'general',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }];
  }
}
