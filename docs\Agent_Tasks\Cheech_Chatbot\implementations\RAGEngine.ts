/**
 * RAG Engine Implementation
 *
 * This file implements the Retrieval Augmented Generation (RAG) engine for the Cheech chatbot.
 * It handles document retrieval, embedding generation, and query processing.
 * Uses the existing AI orchestration system with Gemini and Deepseek.
 */

import { Document, QueryResult, RAGService } from './types';
import { UnifiedAIService } from '../../../services/ai/core/UnifiedAIService';
import { AIRequest, AIRequestType } from '../../../services/ai/types/AIRequest';

/**
 * Implementation of the RAG service using the AI orchestration system
 */
export class RAGEngine implements RAGService {
  private aiService: UnifiedAIService;
  private documents: Map<string, Document> = new Map();
  private documentEmbeddings: Map<string, number[]> = new Map();

  /**
   * Create a new RAGEngine
   * @param aiService The unified AI service
   * @param initialDocuments Optional initial documents to add to the vector store
   */
  constructor(aiService: UnifiedAIService, initialDocuments?: Document[]) {
    this.aiService = aiService;

    // Add initial documents if provided
    if (initialDocuments && initialDocuments.length > 0) {
      this.addDocuments(initialDocuments);
    }
  }

  /**
   * Query the RAG engine for relevant documents
   * @param text The query text
   * @param filters Optional filters for the query
   * @returns Promise resolving to query result
   */
  async query(text: string, filters?: Record<string, any>): Promise<QueryResult> {
    try {
      // For now, use a simplified text-based search
      // In a full implementation, this would use vector embeddings
      const queryLower = text.toLowerCase();
      const matchingDocuments: { document: Document; score: number }[] = [];

      // Search through all documents
      for (const [id, document] of this.documents) {
        // Apply filters if provided
        if (filters) {
          let passesFilter = true;
          for (const [key, value] of Object.entries(filters)) {
            if (document.metadata[key] !== value) {
              passesFilter = false;
              break;
            }
          }
          if (!passesFilter) continue;
        }

        // Calculate relevance score based on text matching
        const contentLower = document.content.toLowerCase();
        let score = 0;

        // Exact phrase match gets highest score
        if (contentLower.includes(queryLower)) {
          score += 1.0;
        }

        // Individual word matches
        const queryWords = queryLower.split(/\s+/);
        const contentWords = contentLower.split(/\s+/);

        for (const queryWord of queryWords) {
          if (queryWord.length > 2) { // Skip very short words
            for (const contentWord of contentWords) {
              if (contentWord.includes(queryWord) || queryWord.includes(contentWord)) {
                score += 0.1;
              }
            }
          }
        }

        // Category/metadata matching
        if (document.metadata.category) {
          const categoryLower = document.metadata.category.toLowerCase();
          if (queryLower.includes(categoryLower) || categoryLower.includes(queryLower)) {
            score += 0.5;
          }
        }

        if (score > 0) {
          matchingDocuments.push({ document, score });
        }
      }

      // Sort by relevance score (highest first) and take top 5
      matchingDocuments.sort((a, b) => b.score - a.score);
      const topResults = matchingDocuments.slice(0, 5);

      return {
        query: text,
        documents: topResults.map(result => result.document),
        relevanceScores: topResults.map(result => result.score)
      };
    } catch (error) {
      console.error('Error querying RAG engine:', error);
      throw new Error(`RAG query failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Add documents to the document store
   * @param documents The documents to add
   * @returns Promise resolving when documents are added
   */
  async addDocuments(documents: Document[]): Promise<void> {
    try {
      // Store documents in memory
      for (const document of documents) {
        this.documents.set(document.id, document);
      }

      console.log(`Added ${documents.length} documents to RAG engine`);
    } catch (error) {
      console.error('Error adding documents to RAG engine:', error);
      throw new Error(`Failed to add documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete documents from the document store
   * @param ids The document IDs to delete
   * @returns Promise resolving when documents are deleted
   */
  async deleteDocuments(ids: string[]): Promise<void> {
    try {
      // Delete documents from memory store
      for (const id of ids) {
        this.documents.delete(id);
        this.documentEmbeddings.delete(id);
      }

      console.log(`Deleted ${ids.length} documents from RAG engine`);
    } catch (error) {
      console.error('Error deleting documents from RAG engine:', error);
      throw new Error(`Failed to delete documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get document count
   * @returns Number of documents in the store
   */
  getDocumentCount(): number {
    return this.documents.size;
  }

  /**
   * Get all document IDs
   * @returns Array of document IDs
   */
  getDocumentIds(): string[] {
    return Array.from(this.documents.keys());
  }
}

/**
 * Document processor for preparing documents for the RAG engine
 */
export class DocumentProcessor {
  /**
   * Split a document into chunks
   * @param document The document to split
   * @param chunkSize The maximum chunk size
   * @param chunkOverlap The overlap between chunks
   * @returns Array of document chunks
   */
  static splitDocument(
    document: Document,
    chunkSize: number = 1000,
    chunkOverlap: number = 200
  ): Document[] {
    // Simple text splitting implementation
    const text = document.content;
    const chunks: Document[] = [];

    // If text is shorter than chunk size, return as is
    if (text.length <= chunkSize) {
      return [document];
    }

    // Split text into chunks
    let startIndex = 0;
    while (startIndex < text.length) {
      // Calculate end index for this chunk
      const endIndex = Math.min(startIndex + chunkSize, text.length);

      // Extract chunk text
      const chunkText = text.substring(startIndex, endIndex);

      // Create new document for this chunk
      chunks.push({
        id: `${document.id}-chunk-${chunks.length}`,
        content: chunkText,
        metadata: {
          ...document.metadata,
          chunkIndex: chunks.length,
          parentDocumentId: document.id
        }
      });

      // Move to next chunk, accounting for overlap
      startIndex = endIndex - chunkOverlap;

      // Ensure we make progress even with large overlap
      if (startIndex <= 0 || startIndex >= text.length - 10) {
        break;
      }
    }

    return chunks;
  }

  /**
   * Process product data into documents for the RAG engine
   * @param product The product data
   * @returns Array of documents
   */
  static processProductData(product: any): Document[] {
    const documents: Document[] = [];

    // Main product document
    const mainDocument: Document = {
      id: `product-${product.id}`,
      content: `
        Product Name: ${product.name}
        Category: ${product.category}
        ${product.subcategory ? `Subcategory: ${product.subcategory}` : ''}
        Price: $${product.price}
        Description: ${product.description}
        Tags: ${product.tags.join(', ')}
      `,
      metadata: {
        source: 'product_catalog',
        category: product.category,
        productId: product.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };

    documents.push(mainDocument);

    // Additional documents for detailed information if available
    if (product.detailedDescription) {
      documents.push({
        id: `product-${product.id}-details`,
        content: product.detailedDescription,
        metadata: {
          source: 'product_details',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    if (product.usageInstructions) {
      documents.push({
        id: `product-${product.id}-usage`,
        content: `
          Usage Instructions for ${product.name}:
          ${product.usageInstructions}
        `,
        metadata: {
          source: 'product_usage',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    if (product.reviews && product.reviews.length > 0) {
      const reviewsText = product.reviews
        .map((review: any) => `
          Rating: ${review.rating}/5
          Review: ${review.text}
        `)
        .join('\n\n');

      documents.push({
        id: `product-${product.id}-reviews`,
        content: `
          Reviews for ${product.name}:
          ${reviewsText}
        `,
        metadata: {
          source: 'product_reviews',
          category: product.category,
          productId: product.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    return documents;
  }

  /**
   * Process FAQ data into documents for the RAG engine
   * @param faq The FAQ data
   * @returns Array of documents
   */
  static processFAQData(faq: any): Document[] {
    return [{
      id: `faq-${faq.id}`,
      content: `
        Question: ${faq.question}
        Answer: ${faq.answer}
        ${faq.category ? `Category: ${faq.category}` : ''}
        ${faq.tags ? `Tags: ${faq.tags.join(', ')}` : ''}
      `,
      metadata: {
        source: 'faq',
        category: faq.category || 'general',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }];
  }
}
