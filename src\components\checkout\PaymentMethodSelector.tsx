import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Paypal, CreditCardIcon } from 'lucide-react';

export interface PaymentMethod {
  id: string;
  name: string;
  description?: string;
  icon: 'card' | 'paypal' | 'stripe';
}

interface PaymentMethodSelectorProps {
  methods: PaymentMethod[];
  selectedMethodId: string;
  onSelect: (methodId: string) => void;
  disabled?: boolean;
}

export function PaymentMethodSelector({
  methods,
  selectedMethodId,
  onSelect,
  disabled = false,
}: PaymentMethodSelectorProps) {
  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'card':
        return <CreditCard className="h-5 w-5" />;
      case 'paypal':
        return <Paypal className="h-5 w-5 text-blue-600" />;
      case 'stripe':
        return <CreditCardIcon className="h-5 w-5 text-purple-600" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  return (
    <RadioGroup
      value={selectedMethodId}
      onValueChange={onSelect}
      className="space-y-4"
      disabled={disabled}
    >
      {methods.map((method) => (
        <div key={method.id} className="relative">
          <Card className={`overflow-hidden transition-all ${
            selectedMethodId === method.id ? 'ring-2 ring-primary' : ''
          } ${disabled ? 'opacity-60' : ''}`}>
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <RadioGroupItem value={method.id} id={method.id} className="mt-1" disabled={disabled} />
                <div className="flex-1">
                  <Label htmlFor={method.id} className="flex items-center cursor-pointer">
                    <div className="flex-1">
                      <div className="font-medium flex items-center gap-2">
                        {getIcon(method.icon)}
                        {method.name}
                      </div>
                      {method.description && (
                        <p className="text-sm text-gray-500 mt-1">{method.description}</p>
                      )}
                    </div>
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </RadioGroup>
  );
}
