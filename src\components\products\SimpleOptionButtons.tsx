import React from 'react';

interface SimpleOptionButtonsProps {
  optionName: string;
  values: string[];
  selectedValue: string;
  onChange: (value: string) => void;
  disabledValues?: string[];
}

export const SimpleOptionButtons: React.FC<SimpleOptionButtonsProps> = ({
  optionName,
  values,
  selectedValue,
  onChange,
  disabledValues = [],
}) => {
  // Filter out DROP_DOWN values
  const filteredValues = values.filter(v => v !== 'DROP_DOWN');

  // Handle button click with extra debugging
  const handleClick = (value: string) => {
    console.log(`SimpleOptionButtons: ${optionName} - ${value} clicked`);

    // Show an alert for debugging
    alert(`Button clicked: ${optionName} = ${value}`);

    // Call the onChange handler
    onChange(value);
  };

  // Add a wrapper div with position: relative to create a new stacking context
  return (
    <div className="mb-6 p-4 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 shadow-md relative" style={{ zIndex: 1000 }}>
      <h4 className="text-sm font-medium mb-3 text-gray-700 flex items-center gap-1.5">
        <span className="inline-block w-1.5 h-1.5 rounded-full bg-primary"></span>
        {optionName}
      </h4>
      <div className="flex flex-wrap gap-2">
        {filteredValues.map((value) => {
          const isDisabled = disabledValues.includes(value);
          const isSelected = selectedValue === value;

          return (
            <button
              key={value}
              type="button"
              disabled={isDisabled}
              onClick={() => handleClick(value)}
              style={{
                padding: '12px 24px',
                backgroundColor: isSelected ? '#4CAF50' : 'rgba(255, 255, 255, 0.9)',
                color: isSelected ? 'white' : '#333',
                border: isSelected ? 'none' : '2px solid rgba(0, 0, 0, 0.1)',
                borderRadius: '8px',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                position: 'relative',
                zIndex: 9999,
                fontWeight: '600',
                boxShadow: isSelected ? '0 4px 12px rgba(76, 175, 80, 0.4)' : '0 2px 6px rgba(0, 0, 0, 0.15)',
                transition: 'all 0.2s ease',
                opacity: isDisabled ? 0.5 : 1,
                transform: isSelected ? 'scale(1.05)' : 'scale(1)'
              }}
            >
              {value}
            </button>
          );
        })}
      </div>
    </div>
  );
};
