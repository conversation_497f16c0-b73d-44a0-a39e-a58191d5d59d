const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const productsFile = 'transformed_products.csv';
const variantsFile = 'transformed_variants_final.csv';
const batchSize = 10;
const batchNumber = 10;

// Output files
const batchProductsFile = `batch_${batchNumber}_products.csv`;
const batchVariantsFile = `batch_${batchNumber}_variants.csv`;

// Store products and their variants
const products = [];
const variants = [];
const productIds = new Set();

// Process the products file
console.log(`Reading products from ${productsFile}...`);
fs.createReadStream(productsFile)
  .pipe(csv())
  .on('data', (row) => {
    products.push(row);
  })
  .on('end', () => {
    console.log(`Read ${products.length} products from CSV`);

    // Select a batch of products
    const startIndex = (batchNumber - 1) * batchSize;
    const batchProducts = products.slice(startIndex, startIndex + batchSize);

    // Store the product IDs
    batchProducts.forEach(product => {
      productIds.add(product.id);
    });

    console.log(`Selected ${batchProducts.length} products for batch ${batchNumber}`);

    // Create a CSV writer for products
    const productsCsvWriter = createObjectCsvWriter({
      path: batchProductsFile,
      header: Object.keys(products[0]).map(key => ({ id: key, title: key }))
    });

    // Write batch products to CSV
    productsCsvWriter.writeRecords(batchProducts)
      .then(() => {
        console.log(`Created batch products file: ${batchProductsFile}`);

        // Process the variants file
        console.log(`Reading variants from ${variantsFile}...`);
        fs.createReadStream(variantsFile)
          .pipe(csv())
          .on('data', (row) => {
            variants.push(row);
          })
          .on('end', async () => {
            console.log(`Read ${variants.length} variants from CSV`);

            // Filter variants for the batch products
            const batchVariants = variants.filter(variant => productIds.has(variant.product_id));

            console.log(`Found ${batchVariants.length} variants for the batch products`);

            // Create a CSV writer for variants
            const variantsCsvWriter = createObjectCsvWriter({
              path: batchVariantsFile,
              header: Object.keys(variants[0]).map(key => ({ id: key, title: key }))
            });

            // Write batch variants to CSV
            await variantsCsvWriter.writeRecords(batchVariants);
            console.log(`Created batch variants file: ${batchVariantsFile}`);

            // Print the batch products and variants
            console.log('\nBatch products:');
            batchProducts.forEach(product => {
              console.log(`- id: ${product.id}, name: ${product.name}`);
            });

            console.log('\nBatch variants:');
            batchVariants.forEach(variant => {
              console.log(`- product_id: ${variant.product_id}, variant_name: ${variant.variant_name}`);
            });
          });
      });
  });
