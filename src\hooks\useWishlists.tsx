import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from '@/components/ui/use-toast';
import { Wishlist, WishlistItem } from '@/types/database';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/auth.basic';

interface WishlistsContextType {
  wishlists: Wishlist[];
  activeWishlist: Wishlist | null;
  isLoading: boolean;
  createWishlist: (name: string, description?: string) => Promise<void>;
  updateWishlist: (id: string, data: Partial<Wishlist>) => Promise<void>;
  deleteWishlist: (id: string) => Promise<void>;
  setActiveWishlist: (wishlistId: string) => void;
  addToWishlist: (productId: string, wishlistId?: string) => Promise<void>;
  removeFromWishlist: (productId: string, wishlistId?: string) => Promise<void>;
  isInWishlist: (productId: string, wishlistId?: string) => boolean;
  getDefaultWishlist: () => Wishlist | null;
  refreshWishlists: () => Promise<void>;
  wishlistItems: Record<string, WishlistItem[]>;
}

const WishlistsContext = createContext<WishlistsContextType | undefined>(undefined);

export const WishlistsProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const [wishlists, setWishlists] = useState<Wishlist[]>([]);
  const [activeWishlist, setActiveWishlist] = useState<Wishlist | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [wishlistItems, setWishlistItems] = useState<Record<string, WishlistItem[]>>({});

  // Fetch wishlists when user changes
  useEffect(() => {
    // Add a small delay to avoid race conditions with auth state
    const timer = setTimeout(() => {
      try {
        if (user) {
          console.log('WishlistsProvider: User detected, fetching wishlists');
          fetchWishlists();
        } else {
          console.log('WishlistsProvider: No user, clearing wishlists');
          // Clear wishlists when user logs out
          setWishlists([]);
          setActiveWishlist(null);
          setWishlistItems({});
        }
      } catch (error) {
        console.error('Error in WishlistsProvider useEffect:', error);
      }
    }, 1000); // 1000ms delay to ensure auth is fully initialized

    return () => clearTimeout(timer);
  }, [user]);

  // Fetch all wishlists for the current user
  const fetchWishlists = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Limit to 10 most recent wishlists to avoid performance issues
      const { data, error } = await supabase
        .from('wishlists')
        .select('*')
        .eq('user_id', user.id) // Make sure we only get this user's wishlists
        .order('created_at', { ascending: false })
        .limit(10); // Limit to 10 records

      if (error) {
        throw error;
      }

      if (data) {
        setWishlists(data);

        // Set default wishlist as active if no active wishlist
        if (!activeWishlist) {
          const defaultWishlist = data.find(w => w.is_default);
          if (defaultWishlist) {
            setActiveWishlist(defaultWishlist);
          } else if (data.length > 0) {
            setActiveWishlist(data[0]);
          }
        }

        // Fetch wishlist items for each wishlist
        await Promise.all(data.map(wishlist => fetchWishlistItems(wishlist.id)));
      }
    } catch (error) {
      console.error('Error fetching wishlists:', error);
      toast({
        title: 'Error',
        description: 'Failed to load wishlists',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch items for a specific wishlist
  const fetchWishlistItems = async (wishlistId: string) => {
    try {
      // Limit to 20 most recent items to avoid performance issues
      const { data, error } = await supabase
        .from('wishlist_items')
        .select('*, products(*)')
        .eq('wishlist_id', wishlistId)
        .order('created_at', { ascending: false })
        .limit(20); // Limit to 20 items

      if (error) {
        throw error;
      }

      if (data) {
        setWishlistItems(prev => ({
          ...prev,
          [wishlistId]: data
        }));
      }
    } catch (error) {
      console.error(`Error fetching items for wishlist ${wishlistId}:`, error);
    }
  };

  // Create a new wishlist
  const createWishlist = async (name: string, description?: string) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to create a wishlist',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Check if this is the first wishlist (make it default)
      const isDefault = wishlists.length === 0;

      const { data, error } = await supabase
        .from('wishlists')
        .insert([
          {
            name,
            description,
            user_id: user.id,
            is_default: isDefault
          }
        ])
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        setWishlists(prev => [data, ...prev]);

        // Set as active if it's the first wishlist
        if (isDefault) {
          setActiveWishlist(data);
        }

        toast({
          title: 'Success',
          description: `Wishlist "${name}" created successfully`,
        });
      }
    } catch (error) {
      console.error('Error creating wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to create wishlist',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update an existing wishlist
  const updateWishlist = async (id: string, data: Partial<Wishlist>) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data: updatedWishlist, error } = await supabase
        .from('wishlists')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (updatedWishlist) {
        setWishlists(prev =>
          prev.map(w => w.id === id ? updatedWishlist : w)
        );

        // Update active wishlist if it's the one being updated
        if (activeWishlist?.id === id) {
          setActiveWishlist(updatedWishlist);
        }

        toast({
          title: 'Success',
          description: `Wishlist updated successfully`,
        });
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to update wishlist',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a wishlist
  const deleteWishlist = async (id: string) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('wishlists')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Remove from state
      setWishlists(prev => prev.filter(w => w.id !== id));

      // If active wishlist was deleted, set a new active wishlist
      if (activeWishlist?.id === id) {
        const newActiveWishlist = wishlists.find(w => w.id !== id);
        setActiveWishlist(newActiveWishlist || null);
      }

      // Remove wishlist items from state
      setWishlistItems(prev => {
        const newItems = {...prev};
        delete newItems[id];
        return newItems;
      });

      toast({
        title: 'Success',
        description: 'Wishlist deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete wishlist',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Set active wishlist by ID
  const setActiveWishlistById = (wishlistId: string) => {
    const wishlist = wishlists.find(w => w.id === wishlistId);
    if (wishlist) {
      setActiveWishlist(wishlist);
    }
  };

  // Add a product to a wishlist
  const addToWishlist = async (productId: string, wishlistId?: string) => {
    if (!user) {
      console.log('Cannot add to wishlist: No user found');
      toast({
        title: 'Sign in required',
        description: 'Please sign in to use the wishlist feature',
        variant: 'destructive',
      });
      return;
    }

    // Log user information for debugging
    console.log('Adding to wishlist with user:', {
      userId: user.id,
      email: user.email,
      isHardcodedAdmin: user.id === 'a0627f38-06d9-48c2-86fc-f0fbca331e18'
    });

    // Determine which wishlist to use
    let targetWishlistId = wishlistId || activeWishlist?.id;

    // If no target wishlist, create a default one or use the first available
    if (!targetWishlistId) {
      if (wishlists.length === 0) {
        // Create a default wishlist
        try {
          console.log('Creating default wishlist for user:', user.id);
          await createWishlist('My Wishlist', 'Default wishlist');

          // Refresh wishlists to get the newly created one
          await fetchWishlists();

          const defaultWishlist = getDefaultWishlist();
          if (!defaultWishlist) {
            throw new Error('Failed to create default wishlist');
          }
          targetWishlistId = defaultWishlist.id;
          console.log('Created default wishlist with ID:', targetWishlistId);
        } catch (error) {
          console.error('Error creating default wishlist:', error);
          return;
        }
      } else {
        // Use the first wishlist
        targetWishlistId = wishlists[0].id;
        console.log('Using existing wishlist with ID:', targetWishlistId);
      }
    }

    setIsLoading(true);
    try {
      // Check if item is already in the wishlist
      if (isInWishlist(productId, targetWishlistId)) {
        console.log('Product already in wishlist:', productId);
        toast({
          title: 'Already in Wishlist',
          description: 'This item is already in your wishlist',
        });
        setIsLoading(false);
        return;
      }

      console.log('Inserting wishlist item:', {
        wishlist_id: targetWishlistId,
        product_id: productId,
        user_id: user.id
      });

      // The error shows that user_id column doesn't exist in the wishlist_items table
      // Instead, the user_id is likely associated with the wishlist itself, not individual items
      const { data, error } = await supabase
        .from('wishlist_items')
        .insert([
          {
            wishlist_id: targetWishlistId,
            product_id: productId
            // Removed user_id as it doesn't exist in the schema
          }
        ])
        .select(`
          id,
          product_id,
          created_at,
          products (
            id,
            name,
            slug,
            price,
            sale_price,
            image,
            description
          )
        `)
        .single();

      if (error) {
        console.error('Supabase error adding to wishlist:', error);
        throw error;
      }

      if (data) {
        console.log('Successfully added to wishlist:', data);
        // Update state
        setWishlistItems(prev => ({
          ...prev,
          [targetWishlistId]: [...(prev[targetWishlistId] || []), data]
        }));

        toast({
          title: 'Added to Wishlist',
          description: 'Item added to your wishlist successfully',
        });

        // Force refresh to ensure UI is updated
        await fetchWishlists();
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to add item to wishlist',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Remove a product from a wishlist
  const removeFromWishlist = async (productId: string, wishlistId?: string) => {
    if (!user) {
      console.log('Cannot remove from wishlist: No user found');
      toast({
        title: 'Sign in required',
        description: 'Please sign in to use the wishlist feature',
        variant: 'destructive',
      });
      return;
    }

    // Log user information for debugging
    console.log('Removing from wishlist with user:', {
      userId: user.id,
      email: user.email,
      isHardcodedAdmin: user.id === 'a0627f38-06d9-48c2-86fc-f0fbca331e18'
    });

    // Determine which wishlist to use
    const targetWishlistId = wishlistId || activeWishlist?.id;
    if (!targetWishlistId) {
      console.log('No target wishlist found for removal');
      return;
    }

    console.log('Removing product from wishlist:', {
      wishlist_id: targetWishlistId,
      product_id: productId,
      user_id: user.id
    });

    setIsLoading(true);
    try {
      // Remove the user_id filter since that column doesn't exist in the wishlist_items table
      const { error } = await supabase
        .from('wishlist_items')
        .delete()
        .eq('wishlist_id', targetWishlistId)
        .eq('product_id', productId);
      // Note: Security is maintained because we're filtering by wishlist_id,
      // and wishlists are already filtered by user_id

      if (error) {
        console.error('Supabase error removing from wishlist:', error);
        throw error;
      }

      console.log('Successfully removed from wishlist');

      // Update state
      setWishlistItems(prev => {
        const items = prev[targetWishlistId] || [];
        return {
          ...prev,
          [targetWishlistId]: items.filter(item => item.product_id !== productId)
        };
      });

      toast({
        title: 'Removed from Wishlist',
        description: 'Item removed from your wishlist',
      });

      // Force refresh to ensure UI is updated
      await fetchWishlists();
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove item from wishlist',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if a product is in a wishlist
  const isInWishlist = (productId: string, wishlistId?: string) => {
    const targetWishlistId = wishlistId || activeWishlist?.id;
    if (!targetWishlistId) return false;

    const items = wishlistItems[targetWishlistId] || [];
    return items.some(item => item.product_id === productId);
  };

  // Get the default wishlist or the first available one
  const getDefaultWishlist = () => {
    const defaultWishlist = wishlists.find(w => w.is_default);
    return defaultWishlist || (wishlists.length > 0 ? wishlists[0] : null);
  };

  // Refresh wishlists data
  const refreshWishlists = async () => {
    // Use a direct implementation instead of calling fetchWishlists to avoid deep type instantiation
    if (!user) return;

    setIsLoading(true);
    try {
      // Limit to 10 most recent wishlists to avoid performance issues
      const { data, error } = await supabase
        .from('wishlists')
        .select('*')
        .eq('user_id', user.id) // Make sure we only get this user's wishlists
        .order('created_at', { ascending: false })
        .limit(10); // Limit to 10 records

      if (error) {
        throw error;
      }

      if (data) {
        setWishlists(data);

        // Set default wishlist as active if no active wishlist
        if (!activeWishlist) {
          const defaultWishlist = data.find(w => w.is_default);
          if (defaultWishlist) {
            setActiveWishlist(defaultWishlist);
          } else if (data.length > 0) {
            setActiveWishlist(data[0]);
          }
        }

        // Fetch wishlist items for each wishlist
        for (const wishlist of data) {
          try {
            // Limit to 20 most recent items to avoid performance issues
            const { data: itemsData, error: itemsError } = await supabase
              .from('wishlist_items')
              .select(`
                id,
                product_id,
                created_at,
                products (
                  id,
                  name,
                  slug,
                  price,
                  sale_price,
                  image,
                  description
                )
              `)
              .eq('wishlist_id', wishlist.id)
              .order('created_at', { ascending: false })
              .limit(20); // Limit to 20 items

            if (!itemsError && itemsData) {
              setWishlistItems(prev => ({
                ...prev,
                [wishlist.id]: itemsData
              }));
            }
          } catch (itemError) {
            console.error(`Error fetching items for wishlist ${wishlist.id}:`, itemError);
          }
        }
      }
    } catch (error) {
      console.error('Error refreshing wishlists:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh wishlists',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <WishlistsContext.Provider
      value={{
        wishlists,
        activeWishlist,
        isLoading,
        createWishlist,
        updateWishlist,
        deleteWishlist,
        setActiveWishlist: setActiveWishlistById,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        getDefaultWishlist,
        refreshWishlists,
        wishlistItems // Expose wishlistItems to consumers
      }}
    >
      {children}
    </WishlistsContext.Provider>
  );
};

export const useWishlists = () => {
  const context = useContext(WishlistsContext);
  if (context === undefined) {
    throw new Error('useWishlists must be used within a WishlistsProvider');
  }
  return context;
};
