
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { checkEmailExists, subscribeToNewsletter } from '@/integrations/supabase/newsletter';

const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showNameField, setShowNameField] = useState(false);

  // Submit the form to subscribe to the newsletter
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic email validation
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast({
        title: "Error", 
        description: "Please enter a valid email address",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // If we're showing the name field, submit the full form
      if (showNameField) {
        // Use the Edge Function to subscribe
        const result = await subscribeToNewsletter(email, firstName);
        
        if (result.error) {
          if (result.alreadySubscribed) {
            toast({
              title: "Already Subscribed",
              description: "This email is already subscribed to our newsletter."
            });
          } else {
            throw new Error(result.error);
          }
          return;
        }
        
        toast({
          title: "Success",
          description: "Thank you for subscribing! Check your email for your 10% discount code."
        });
        
        // Reset form
        setEmail('');
        setFirstName('');
        setShowNameField(false);
      } else {
        // Check if email already exists
        const emailExists = await checkEmailExists(email);
        
        if (emailExists) {
          toast({
            title: "Already Subscribed",
            description: "This email is already subscribed to our newsletter."
          });
          return;
        }
        
        // Show the name field for the second step
        setShowNameField(true);
      }
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      toast({
        title: "Error",
        description: "There was a problem subscribing you to our newsletter. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-16 bg-clay-100">
      <div className="container-custom">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="section-heading">Join Our Newsletter</h2>
          <p className="text-clay-700 mb-6">
            Sign up for our newsletter to receive 10% off your first order, plus exclusive offers, educational content, and early access to new products.
          </p>
          
          <form onSubmit={handleSubmit} className="max-w-md mx-auto">
            {!showNameField ? (
              <div className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  placeholder="Your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-grow"
                  required
                  disabled={isSubmitting}
                />
                <Button 
                  type="submit" 
                  className="bg-primary hover:bg-primary/90"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    'Subscribe'
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-left">
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                    First Name (Optional)
                  </label>
                  <Input
                    id="firstName"
                    type="text"
                    placeholder="Your first name"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full"
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="flex justify-between gap-3">
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => setShowNameField(false)}
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    Back
                  </Button>
                  <Button 
                    type="submit" 
                    className="bg-primary hover:bg-primary/90 flex-1"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      'Complete Signup'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </section>
  );
};

export default Newsletter;
