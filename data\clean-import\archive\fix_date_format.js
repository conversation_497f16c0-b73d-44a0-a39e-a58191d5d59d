const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const inputFile = 'transformed_variants_fixed_ids.csv';
const outputFile = 'transformed_variants_final.csv';

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Process the CSV file
console.log(`Reading variants from ${inputFile}...`);
const variants = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    // Fix date format
    if (row.created_at) {
      // Convert to ISO format without the 'Z' at the end
      const createdDate = new Date(row.created_at);
      row.created_at = createdDate.toISOString().replace('Z', '');
    }
    
    if (row.updated_at) {
      // Convert to ISO format without the 'Z' at the end
      const updatedDate = new Date(row.updated_at);
      row.updated_at = updatedDate.toISOString().replace('Z', '');
    }
    
    // Fix boolean format
    if (row.in_stock) {
      row.in_stock = row.in_stock.toLowerCase();
    }
    
    if (row.is_active) {
      row.is_active = row.is_active.toLowerCase();
    }
    
    variants.push(row);
  })
  .on('end', async () => {
    console.log(`Read ${variants.length} variants from CSV`);

    // Write variants to CSV
    await csvWriter.writeRecords(variants);
    console.log(`Wrote ${variants.length} variants to ${outputFile}`);

    console.log('Date format fix complete!');
  });
