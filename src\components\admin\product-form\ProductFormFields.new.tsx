import React, { useState } from "react";
import { Product, Category, Brand } from "@/types/database";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Wand2, Plus, Image, Loader2 } from "lucide-react";
import { SimpleImageGallery } from "./SimpleImageGallery";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { BrandForm } from "../BrandForm";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";

interface ProductFormFieldsProps {
  formData: Partial<Product>;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (name: string, value: any) => void;
  handleCheckboxChange: (name: string, checked: boolean) => void;
  brands: Brand[];
  categories: Category[];
  handleFindImages?: (productName: string) => void;
  handleGenerateDescription?: () => void;
  isFindingImages?: boolean;
  isGeneratingDescription?: boolean;
}

export function ProductFormFields({
  formData,
  handleChange,
  handleSelectChange,
  handleCheckboxChange,
  brands,
  categories,
  handleFindImages,
  handleGenerateDescription,
  isFindingImages = false,
  isGeneratingDescription = false,
}: ProductFormFieldsProps) {
  const [isBrandDialogOpen, setIsBrandDialogOpen] = useState(false);

  const handleBrandDialogClose = () => {
    setIsBrandDialogOpen(false);
  };

  return (
    <>
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <DialogDescription>
              Enter the basic details of your product.
            </DialogDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name ?? ""}
                  onChange={handleChange}
                  placeholder="Enter product name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  name="sku"
                  value={formData.sku ?? ""}
                  onChange={handleChange}
                  placeholder="Enter product SKU"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug ?? ""}
                  onChange={handleChange}
                  placeholder="auto-generated-if-empty"
                />
                <p className="text-xs text-gray-500">
                  Leave empty to auto-generate from name
                </p>
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="images">
                  <span className="flex items-center gap-1">
                    <Image className="h-4 w-4" /> Product Images
                  </span>
                </Label>

                <SimpleImageGallery
                  images={formData.additional_images || []}
                  onChange={(images) => {
                    console.log('SimpleImageGallery onChange called with:', images);
                    handleSelectChange('additional_images', images);
                  }}
                  mainImage={formData.image}
                  onMainImageChange={(url) => handleSelectChange("image", url)}
                  onFindImagesWithAI={handleFindImages ? () => handleFindImages(formData.name || '') : undefined}
                  isFindingImages={isFindingImages}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">
                  Description
                </Label>
                <div className="relative">
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description ?? ""}
                    onChange={handleChange}
                    rows={6}
                    placeholder="Enter product description"
                    className="min-h-[150px]"
                  />
                  {handleGenerateDescription && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={handleGenerateDescription}
                      disabled={isGeneratingDescription}
                    >
                      {isGeneratingDescription ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Wand2 className="mr-2 h-4 w-4" />
                          Generate with AI
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pricing & Inventory</CardTitle>
            <DialogDescription>
              Set the pricing and inventory details.
            </DialogDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="price">Price (£)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sale_price">Sale Price (£)</Label>
                <Input
                  id="sale_price"
                  name="sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.sale_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500">
                  Leave empty if there's no sale
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost_price">Cost (£)</Label>
                <Input
                  id="cost_price"
                  name="cost_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cost_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock_quantity">Quantity</Label>
                <Input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.stock_quantity ?? ""}
                  onChange={handleChange}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="track_inventory"
                  checked={formData.track_inventory ?? false}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange("track_inventory", checked)
                  }
                />
                <Label htmlFor="track_inventory">Track Inventory</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active ?? true}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange("is_active", checked)
                  }
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Organization</CardTitle>
            <DialogDescription>
              Categorize and organize your product.
            </DialogDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="brand_id">Brand</Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.brand_id?.toString() ?? ""}
                    onValueChange={(value) => handleSelectChange("brand_id", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id.toString()}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setIsBrandDialogOpen(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category_id">Category</Label>
                <Select
                  value={formData.category_id?.toString() ?? ""}
                  onValueChange={(value) => handleSelectChange("category_id", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.tags ?? ""}
                  onChange={handleChange}
                  placeholder="Enter tags separated by commas"
                />
                <p className="text-xs text-gray-500">
                  Separate tags with commas
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Brand Form Dialog */}
      <Dialog open={isBrandDialogOpen} onOpenChange={setIsBrandDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Add New Brand</DialogTitle>
            <DialogDescription>
              Enter the brand details below to create a new brand.
            </DialogDescription>
          </DialogHeader>
          <BrandForm
            brand={null}
            onSuccess={handleBrandDialogClose}
            onCancel={handleBrandDialogClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
