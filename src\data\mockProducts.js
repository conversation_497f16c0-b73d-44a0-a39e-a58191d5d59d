// Mock product data to use while Supabase connection is being fixed
export const mockProducts = [
  {
    id: '1',
    name: 'Premium CBD Oil',
    slug: 'premium-cbd-oil',
    description: 'High-quality CBD oil for relaxation and wellness.',
    price: 49.99,
    sale_price: null,
    image: '/product-images/cbd-oil.jpg',
    category_id: '1',
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: true,
  },
  {
    id: '2',
    name: 'Ceramic Water Pipe',
    slug: 'ceramic-water-pipe',
    description: 'Handcrafted ceramic water pipe with artistic design.',
    price: 89.99,
    sale_price: 79.99,
    image: '/product-images/water-pipe.jpg',
    category_id: '2',
    in_stock: true,
    is_featured: true,
    is_new: false,
    is_best_seller: true,
  },
  {
    id: '3',
    name: 'Hemp Rolling Papers',
    slug: 'hemp-rolling-papers',
    description: 'Organic hemp rolling papers, pack of 50.',
    price: 5.99,
    sale_price: null,
    image: '/product-images/rolling-papers.jpg',
    category_id: '3',
    in_stock: true,
    is_featured: false,
    is_new: false,
    is_best_seller: true,
  },
  {
    id: '4',
    name: 'Glass Dab Rig',
    slug: 'glass-dab-rig',
    description: 'Premium glass dab rig for concentrates.',
    price: 129.99,
    sale_price: null,
    image: '/product-images/dab-rig.jpg',
    category_id: '2',
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: false,
  },
  {
    id: '5',
    name: 'CBD Gummies',
    slug: 'cbd-gummies',
    description: 'Delicious fruit-flavored CBD gummies, 25mg each.',
    price: 24.99,
    sale_price: 19.99,
    image: '/product-images/cbd-gummies.jpg',
    category_id: '1',
    in_stock: true,
    is_featured: true,
    is_new: false,
    is_best_seller: true,
  },
  {
    id: '6',
    name: 'Portable Vaporizer',
    slug: 'portable-vaporizer',
    description: 'Compact and discreet vaporizer with temperature control.',
    price: 149.99,
    sale_price: null,
    image: '/product-images/vaporizer.jpg',
    category_id: '4',
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: false,
  },
  {
    id: '7',
    name: 'Grinder Card',
    slug: 'grinder-card',
    description: 'Credit card sized grinder that fits in your wallet.',
    price: 12.99,
    sale_price: null,
    image: '/product-images/grinder-card.jpg',
    category_id: '3',
    in_stock: true,
    is_featured: false,
    is_new: true,
    is_best_seller: false,
  },
  {
    id: '8',
    name: 'CBD Bath Bombs',
    slug: 'cbd-bath-bombs',
    description: 'Relaxing CBD-infused bath bombs, set of 3.',
    price: 29.99,
    sale_price: null,
    image: '/product-images/bath-bombs.jpg',
    category_id: '1',
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: false,
  }
];

// Helper functions to filter products
export const getFeaturedProducts = (limit = 8) => {
  return mockProducts
    .filter(product => product.is_featured)
    .slice(0, limit);
};

export const getBestSellerProducts = (limit = 8) => {
  return mockProducts
    .filter(product => product.is_best_seller)
    .slice(0, limit);
};

export const getNewProducts = (limit = 8) => {
  return mockProducts
    .filter(product => product.is_new)
    .slice(0, limit);
};

export const getProductsByCategory = (categoryId, limit = 0) => {
  const filtered = mockProducts.filter(product => 
    categoryId ? product.category_id === categoryId : true
  );
  return limit > 0 ? filtered.slice(0, limit) : filtered;
};

export const getProductBySlug = (slug) => {
  return mockProducts.find(product => product.slug === slug);
};

// Mock categories
export const mockCategories = [
  { id: '1', name: 'CBD Products', slug: 'cbd-products' },
  { id: '2', name: 'Glassware', slug: 'glassware' },
  { id: '3', name: 'Accessories', slug: 'accessories' },
  { id: '4', name: 'Vaporizers', slug: 'vaporizers' }
];
