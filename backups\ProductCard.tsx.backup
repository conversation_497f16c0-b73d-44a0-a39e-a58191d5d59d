
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ShoppingBag, Star, Heart } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useCart } from '@/hooks/useCart';
import { useSavedItems } from '@/hooks/useSavedItems';
import { toast } from '@/components/ui/use-toast';

export interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  salePrice?: number;
  image: string;
  category: string;
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  isFeatured?: boolean;
  isBestSeller?: boolean;
}

interface ProductCardProps {
  product: Product;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const { id, name, slug, price, salePrice, image, rating, reviewCount, isNew, isBestSeller } = product;
  const { addToCart } = useCart();
  const { saveForLater, isSaved } = useSavedItems();
  const isProductSaved = isSaved(id);
  
  const discount = salePrice ? Math.round(((price - salePrice) / price) * 100) : 0;

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    addToCart(id, 1);
  };

  const handleSaveForLater = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    saveForLater(id);
  };

  return (
    <div className="product-card group">
      <div className="relative overflow-hidden">
        <Link to={`/product/${slug}`}>
          <AspectRatio ratio={1/1}>
            <img src={image} alt={name} className="product-image" />
          </AspectRatio>
        </Link>
        
        {/* Product badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-2">
          {isNew && (
            <span className="bg-primary px-2 py-1 text-xs font-medium text-white rounded">
              New
            </span>
          )}
          {isBestSeller && (
            <span className="bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground rounded">
              Best Seller
            </span>
          )}
          {discount > 0 && (
            <span className="bg-destructive px-2 py-1 text-xs font-medium text-destructive-foreground rounded">
              {discount}% OFF
            </span>
          )}
        </div>
        
        {/* Quick action buttons */}
        <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
          <Button 
            className="flex-1 bg-white text-clay-900 hover:bg-clay-100"
            onClick={handleAddToCart}
          >
            <ShoppingBag className="mr-2 h-4 w-4" /> Add
          </Button>
          <Button 
            variant="outline"
            className={`w-10 p-0 ${isProductSaved ? 'bg-red-50 border-red-200' : 'bg-white'}`}
            onClick={handleSaveForLater}
          >
            <Heart className={`h-4 w-4 ${isProductSaved ? 'fill-red-500 text-red-500' : ''}`} />
          </Button>
        </div>
      </div>
      
      <div className="p-4 bg-white">
        <Link to={`/product/${slug}`} className="hover:text-primary">
          <h3 className="font-medium text-clay-900 mb-1 truncate">{name}</h3>
        </Link>
        
        <div className="flex items-center space-x-1 mb-2">
          <div className="flex items-center text-amber-400">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star 
                key={i} 
                className={`h-4 w-4 ${i < Math.round(rating) ? 'fill-current' : ''}`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500">({reviewCount})</span>
        </div>
        
        <div className="flex items-center">
          {salePrice ? (
            <>
              <span className="text-destructive font-medium mr-2">£{salePrice.toFixed(2)}</span>
              <span className="text-gray-500 text-sm line-through">£{price.toFixed(2)}</span>
            </>
          ) : (
            <span className="font-medium">£{price.toFixed(2)}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
