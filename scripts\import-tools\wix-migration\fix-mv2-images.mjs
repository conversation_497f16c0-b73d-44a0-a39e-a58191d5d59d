// Script to specifically fix image URLs with ~mv2 in them
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('Error: Supabase key is missing. Please check your .env file.');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to fix image URLs with ~mv2 in them
async function fixMv2Images() {
  console.log('Starting to fix image URLs with ~mv2...');
  
  try {
    // Fetch all products with ~mv2 in the image URL
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images')
      .or('image.ilike.%~mv2%,additional_images.cs.{%~mv2%}');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products with ~mv2 in image URLs`);
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Function to fix image URL
      const fixImageUrl = (imageUrl) => {
        if (!imageUrl) return null;
        
        // Skip if the URL doesn't contain ~mv2
        if (!imageUrl.includes('~mv2')) {
          return imageUrl;
        }
        
        // Extract parts of the URL
        const urlParts = imageUrl.split('/');
        const filename = urlParts.pop();
        
        // Remove ~mv2 and change extension to .webp
        const filenameWithoutExtension = filename.split('.')[0].replace(/~mv2$/, '');
        const newFilename = `${filenameWithoutExtension}.webp`;
        
        // Reconstruct the URL
        urlParts.push(newFilename);
        return urlParts.join('/');
      };
      
      // Fix main image URL
      if (product.image && product.image.includes('~mv2')) {
        const fixedImageUrl = fixImageUrl(product.image);
        if (fixedImageUrl && fixedImageUrl !== product.image) {
          updates.image = fixedImageUrl;
          needsUpdate = true;
        }
      }
      
      // Fix additional image URLs
      if (product.additional_images && Array.isArray(product.additional_images) && 
          product.additional_images.length > 0) {
        const fixedAdditionalImages = product.additional_images.map(url => {
          if (url && url.includes('~mv2')) {
            return fixImageUrl(url);
          }
          return url;
        }).filter(Boolean);
        
        // Check if any URLs were changed
        const hasChanges = JSON.stringify(fixedAdditionalImages) !== 
                          JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = fixedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          console.log(`  Old image: ${product.image}`);
          console.log(`  New image: ${updates.image}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error fixing ~mv2 images:', error);
  }
}

// Run the script
fixMv2Images().catch(console.error);
