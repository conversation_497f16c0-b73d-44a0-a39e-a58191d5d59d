/**
 * AI Management Dashboard
 * 
 * Central control panel for monitoring and managing the unified AI system
 * Provides insights, controls, and optimization recommendations
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  DollarSign, 
  Zap, 
  TrendingUp, 
  Settings, 
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';
import { aiServiceManager } from '@/services/ai/AIServiceManager';
import { productAI } from '@/services/ai/integrations/ProductAIIntegration';
import { blogAI } from '@/services/ai/integrations/BlogAIIntegration';

interface AISystemStatus {
  unified_ai_available: boolean;
  active_features: string[];
  cost_savings: number;
  recommendations: string[];
}

interface ProviderStatus {
  provider: string;
  available: boolean;
  rate_limited: boolean;
  error_rate: number;
  average_response_time: number;
  last_check: Date;
}

interface UsageStats {
  total_requests: number;
  total_tokens: number;
  total_cost: number;
  cost_breakdown: { provider: string; cost: number; percentage: number }[];
  recommendations: string[];
}

const AIManagementPage: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<AISystemStatus | null>(null);
  const [providerStatuses, setProviderStatuses] = useState<ProviderStatus[]>([]);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [featureFlags, setFeatureFlags] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [status, flags] = await Promise.all([
        aiServiceManager.getSystemStatus(),
        aiServiceManager.getFeatureFlags()
      ]);
      
      setSystemStatus(status);
      setFeatureFlags(flags);
      
      // Mock provider statuses and usage stats for now
      setProviderStatuses([
        {
          provider: 'DeepSeek',
          available: true,
          rate_limited: false,
          error_rate: 0.02,
          average_response_time: 1200,
          last_check: new Date()
        },
        {
          provider: 'Gemini',
          available: true,
          rate_limited: false,
          error_rate: 0.01,
          average_response_time: 2800,
          last_check: new Date()
        },
        {
          provider: 'OpenRouter',
          available: true,
          rate_limited: false,
          error_rate: 0.005,
          average_response_time: 1800,
          last_check: new Date()
        }
      ]);
      
      setUsageStats({
        total_requests: 156,
        total_tokens: 45000,
        total_cost: 2.34,
        cost_breakdown: [
          { provider: 'DeepSeek', cost: 1.20, percentage: 51 },
          { provider: 'Gemini', cost: 0.89, percentage: 38 },
          { provider: 'OpenRouter', cost: 0.25, percentage: 11 }
        ],
        recommendations: [
          'Consider routing more simple tasks to DeepSeek for cost savings',
          'Gemini usage is optimal for creative content'
        ]
      });
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFeatureFlagChange = async (flag: string, enabled: boolean) => {
    try {
      const newFlags = { ...featureFlags, [flag]: enabled };
      aiServiceManager.updateFeatureFlags(newFlags);
      setFeatureFlags(newFlags);
      
      // Reload system status to reflect changes
      const status = await aiServiceManager.getSystemStatus();
      setSystemStatus(status);
      
    } catch (error) {
      console.error('Failed to update feature flag:', error);
    }
  };

  const getStatusColor = (available: boolean, rateLimited: boolean) => {
    if (!available) return 'destructive';
    if (rateLimited) return 'warning';
    return 'success';
  };

  const getStatusText = (available: boolean, rateLimited: boolean) => {
    if (!available) return 'Offline';
    if (rateLimited) return 'Rate Limited';
    return 'Online';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading AI dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI Management Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and control your unified AI system
          </p>
        </div>
        <Button onClick={loadDashboardData} variant="outline">
          <Activity className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemStatus?.unified_ai_available ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-muted-foreground">
              {systemStatus?.active_features.length || 0} features active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              £{systemStatus?.cost_savings.toFixed(2) || '0.00'}
            </div>
            <p className="text-xs text-muted-foreground">
              vs legacy AI costs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats?.total_requests || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Response</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1.8s</div>
            <p className="text-xs text-muted-foreground">
              Across all providers
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="features">Feature Flags</TabsTrigger>
          <TabsTrigger value="usage">Usage & Costs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>System Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {systemStatus?.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <TrendingUp className="w-4 h-4 text-blue-500 mt-0.5" />
                    <p className="text-sm">{rec}</p>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Active Features</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {systemStatus?.active_features.map((feature, index) => (
                    <Badge key={index} variant="secondary">
                      {feature}
                    </Badge>
                  ))}
                  {(!systemStatus?.active_features || systemStatus.active_features.length === 0) && (
                    <p className="text-sm text-muted-foreground">
                      No features currently active. Enable features in the Feature Flags tab.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="providers" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {providerStatuses.map((provider) => (
              <Card key={provider.provider}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {provider.provider}
                    <Badge variant={getStatusColor(provider.available, provider.rate_limited) as any}>
                      {getStatusText(provider.available, provider.rate_limited)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Response Time:</span>
                    <span>{provider.average_response_time}ms</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Error Rate:</span>
                    <span>{(provider.error_rate * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Last Check:</span>
                    <span>{provider.last_check.toLocaleTimeString()}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Flag Controls</CardTitle>
              <p className="text-sm text-muted-foreground">
                Enable or disable AI features for gradual rollout
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(featureFlags).map(([flag, enabled]) => (
                <div key={flag} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{flag.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</p>
                    <p className="text-sm text-muted-foreground">
                      {flag.includes('Product') && 'Replace existing product AI with unified system'}
                      {flag.includes('Blog') && 'Enhance blog generation with unified AI'}
                      {flag.includes('Social') && 'Enable new social media AI features'}
                      {flag.includes('Newsletter') && 'Improve newsletter generation'}
                      {flag.includes('Optimization') && 'Enable automatic cost optimization'}
                      {flag.includes('Fallback') && 'Enable automatic provider fallbacks'}
                    </p>
                  </div>
                  <Switch
                    checked={enabled as boolean}
                    onCheckedChange={(checked) => handleFeatureFlagChange(flag, checked)}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cost Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {usageStats?.cost_breakdown.map((item) => (
                  <div key={item.provider} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{item.provider}</span>
                      <span>£{item.cost.toFixed(2)} ({item.percentage}%)</span>
                    </div>
                    <Progress value={item.percentage} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Requests</p>
                    <p className="text-2xl font-bold">{usageStats?.total_requests}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Tokens</p>
                    <p className="text-2xl font-bold">{usageStats?.total_tokens.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Cost</p>
                    <p className="text-2xl font-bold">£{usageStats?.total_cost.toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Cost/Request</p>
                    <p className="text-2xl font-bold">
                      £{((usageStats?.total_cost || 0) / Math.max(usageStats?.total_requests || 1, 1)).toFixed(3)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIManagementPage;
