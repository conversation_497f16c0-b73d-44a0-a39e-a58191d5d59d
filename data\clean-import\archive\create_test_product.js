const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const inputFile = 'transformed_products.csv';
const outputFile = 'test_product.csv';

// Process the CSV file
console.log(`Reading products from ${inputFile}...`);
const products = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    products.push(row);
  })
  .on('end', async () => {
    console.log(`Read ${products.length} products from CSV`);
    
    // Select the first product
    const product = products[0];
    console.log(`\nSelected product:`);
    console.log(`- id: ${product.id}`);
    console.log(`- name: ${product.name}`);
    console.log(`- price: ${product.price}`);
    
    // Create a CSV writer
    const csvWriter = createObjectCsvWriter({
      path: outputFile,
      header: Object.keys(product).map(key => ({ id: key, title: key }))
    });
    
    // Write the product to CSV
    await csvWriter.writeRecords([product]);
    console.log(`\nCreated test product file: ${outputFile}`);
  });
