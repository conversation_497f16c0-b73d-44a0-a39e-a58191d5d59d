import { Link } from 'react-router-dom';

interface BrandLogoProps {
  className?: string;
}

const BrandLogo = ({ className = '' }: BrandLogoProps) => {
  // Instructions for the user:
  // 1. Save the animated logo GIF to: /public/images/animated-logo.gif
  // 2. Save the text logo image to: /public/images/text-logo.png
  
  return (
    <Link to="/" className={`flex items-center ${className}`}>
      <div className="flex items-center gap-3">
        {/* Animated Logo - using the actual GIF file */}
        <div className="flex items-center justify-center">
          <img 
            src="/images/animated-logo.gif" 
            alt="Bits N Bongs Animated Logo" 
            className="h-20 w-auto"
            style={{ marginBottom: '2px' }} // Fine-tune vertical position
          />
        </div>
        
        {/* Text Logo - using the actual image file */}
        <div className="flex items-center">
          <img 
            src="/images/text-logo.png" 
            alt="Bits N Bongs" 
            className="h-7 w-auto"
            style={{ marginTop: '2px' }} // Fine-tune vertical position
          />
        </div>
      </div>
    </Link>
  );
};

export default BrandLogo;
