@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 42 33% 96%;
    --foreground: 170 9% 14%;

    --card: 0 0% 100%;
    --card-foreground: 170 9% 14%;

    --popover: 0 0% 100%;
    --popover-foreground: 170 9% 14%;

    --primary: 126 54% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 41 46% 71%;
    --secondary-foreground: 170 9% 14%;

    --muted: 60 10% 95%;
    --muted-foreground: 170 4% 45%;

    --accent: 41 46% 90%;
    --accent-foreground: 170 9% 14%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 126 12% 87%;
    --input: 126 12% 87%;
    --ring: 126 54% 41%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 170 9% 14%;
    --foreground: 0 0% 95%;

    --card: 170 9% 16%;
    --card-foreground: 0 0% 95%;

    --popover: 170 9% 16%;
    --popover-foreground: 0 0% 95%;

    --primary: 126 54% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 41 46% 35%;
    --secondary-foreground: 0 0% 95%;

    --muted: 170 9% 22%;
    --muted-foreground: 170 5% 65%;

    --accent: 41 46% 25%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 95%;

    --border: 170 9% 24%;
    --input: 170 9% 24%;
    --ring: 126 54% 41%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

/* Custom animations */
@keyframes progressbar {
  from { width: 0%; }
  to { width: 100%; }
}

.animate-progressbar {
  animation: progressbar 5s linear;
}

.glass-card {
  @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-lg;
}

.product-card {
  @apply rounded-lg overflow-hidden transition-all duration-300 hover:shadow-xl;
}

.product-card:hover img {
  @apply scale-105;
}

.product-image {
  @apply aspect-square object-cover w-full transition-all duration-500;
}

.section-heading {
  @apply text-3xl md:text-4xl font-bold mb-6 text-clay-900;
}

.subsection-heading {
  @apply text-xl font-semibold mb-4 text-clay-800;
}

.nav-link {
  @apply px-3 py-2 text-clay-800 hover:text-primary hover:bg-sage-50 rounded-md transition-all duration-200;
}

.nav-link.active {
  @apply bg-primary text-white hover:bg-primary/90 hover:text-white;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary/90 px-4 py-2 rounded-md transition-all duration-200;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 px-4 py-2 rounded-md transition-all duration-200;
}

.btn-outline {
  @apply border border-primary text-primary hover:bg-primary hover:text-white px-4 py-2 rounded-md transition-all duration-200;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes hero-glow {
  0%, 100% {
    text-shadow: 0 0 24px #fff, 0 0 48px #a3e635, 0 0 8px #fff;
  }
  50% {
    text-shadow: 0 0 48px #a3e635, 0 0 96px #fff, 0 0 16px #fff;
  }
}

.animate-hero-glow {
  animation: hero-glow 2.5s ease-in-out infinite alternate;
}

@keyframes fade-in-delayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  60% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-delayed {
  animation: fade-in-delayed 1.6s 0.6s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes hero-bounce-in {
  0% {
    opacity: 0;
    transform: translateY(80px) scale(0.8);
  }
  60% {
    opacity: 1;
    transform: translateY(-16px) scale(1.08);
  }
  80% {
    transform: translateY(8px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-hero-bounce-in {
  animation: hero-bounce-in 1.2s cubic-bezier(0.23, 1.2, 0.32, 1) both;
}
