import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

// Lazy load admin pages for better performance
const Dashboard = lazy(() => import('@/pages/admin/DashboardPage'));
const Products = lazy(() => import('@/pages/admin/ProductsPage'));
const Categories = lazy(() => import('@/pages/admin/CategoriesPage'));
const Brands = lazy(() => import('@/pages/admin/BrandsPage'));
const Orders = lazy(() => import('@/pages/admin/OrdersPage'));
const Customers = lazy(() => import('@/pages/admin/CustomersPage'));
const Settings = lazy(() => import('@/pages/admin/SettingsPage'));
const FAQs = lazy(() => import('@/pages/admin/FAQsPage'));
const EPOSIntegration = lazy(() => import('@/pages/admin/EPOSIntegrationPage'));
const VariantTest = lazy(() => import('@/pages/admin/VariantTestPage'));

export const adminRoutes = [
  {
    path: '',
    element: <Navigate to="dashboard" replace />,
  },
  {
    path: 'dashboard',
    element: <Dashboard />,
  },
  {
    path: 'products',
    element: <Products />,
  },
  {
    path: 'categories',
    element: <Categories />,
  },
  {
    path: 'brands',
    element: <Brands />,
  },
  {
    path: 'orders',
    element: <Orders />,
  },
  {
    path: 'customers',
    element: <Customers />,
  },
  {
    path: 'settings',
    element: <Settings />,
  },
  {
    path: 'faqs',
    element: <FAQs />,
  },
  {
    path: 'epos-integration',
    element: <EPOSIntegration />,
  },
  {
    path: 'variant-test',
    element: <VariantTest />,
  },
];