/**
 * Image Scraping Admin Dashboard
 * 
 * "Free your mind... and your product images!" 🔴
 * 
 * Admin interface for Agent 2's MCP Playwright image scraping system
 * Process 1000+ inactive products with a single click
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>bs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  Image, 
  Search, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  BarChart3,
  Clock,
  DollarSign,
  <PERSON>rkles,
  Loader,
  Filter,
  Settings,
  Database
} from 'lucide-react';
import { imageScrapingIntegration } from '@/services/ai/integrations/ImageScrapingIntegration';
import { useToast } from '@/components/ui/use-toast';

interface Product {
  id: string;
  name: string;
  category: string;
  has_images: boolean;
}

interface ProcessingResult {
  product_id: string;
  product_name: string;
  success: boolean;
  image_count: number;
  processing_time: number;
  error?: string;
}

const ImageScrapingPage: React.FC = () => {
  const { toast } = useToast();
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [onlyWithoutImages, setOnlyWithoutImages] = useState(true);
  const [processingResults, setProcessingResults] = useState<ProcessingResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [bulkProcessingStats, setBulkProcessingStats] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [batchSize, setBatchSize] = useState(5);
  const [maxImagesPerProduct, setMaxImagesPerProduct] = useState(10);
  const [minQualityThreshold, setMinQualityThreshold] = useState(0.7);

  // Mock product data - in real app, this would come from your API
  const mockProducts: Product[] = [
    { id: '1', name: 'Premium CBD Oil 1000mg', category: 'cbd', has_images: false },
    { id: '2', name: 'Glass Water Pipe - Artistic', category: 'bongs', has_images: false },
    { id: '3', name: 'Portable Dry Herb Vaporizer', category: 'vaporizers', has_images: false },
    { id: '4', name: 'Hemp Seeds - Organic', category: 'seeds', has_images: false },
    { id: '5', name: 'CBD Gummies - Mixed Fruit', category: 'cbd', has_images: false },
    { id: '6', name: 'Rolling Papers - Hemp', category: 'accessories', has_images: true },
    { id: '7', name: 'CBD Tincture - Full Spectrum', category: 'cbd', has_images: false },
    { id: '8', name: 'Beaker Bong - 12 inch', category: 'bongs', has_images: false },
    { id: '9', name: 'Dab Rig Kit - Complete', category: 'accessories', has_images: false },
    { id: '10', name: 'CBD Balm - Muscle Relief', category: 'cbd', has_images: false },
  ];

  useEffect(() => {
    loadSystemStatus();
    loadProducts();
  }, []);

  const loadSystemStatus = async () => {
    try {
      const status = await imageScrapingIntegration.getSystemStatus();
      setSystemStatus(status);
    } catch (error) {
      console.error('Failed to load system status:', error);
      toast({
        title: 'Error',
        description: 'Failed to load system status',
        variant: 'destructive'
      });
    }
  };

  const loadProducts = () => {
    // In a real app, this would fetch from your API
    setProducts(mockProducts);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSelectedProducts([]);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSelectAll = () => {
    const filteredProducts = getFilteredProducts();
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const getFilteredProducts = () => {
    return products.filter(product => {
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesImageFilter = !onlyWithoutImages || !product.has_images;
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesCategory && matchesImageFilter && matchesSearch;
    });
  };

  const handleProcessProducts = async () => {
    if (selectedProducts.length === 0) {
      toast({
        title: 'No products selected',
        description: 'Please select at least one product to process',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setProcessingResults([]);
    setBulkProcessingStats(null);

    try {
      // Get selected product details
      const productsToProcess = products
        .filter(p => selectedProducts.includes(p.id))
        .map(p => ({
          id: p.id,
          name: p.name,
          category: p.category
        }));

      // Process in batches to show progress
      const totalProducts = productsToProcess.length;
      const progressStep = 100 / totalProducts;
      const results: ProcessingResult[] = [];

      // Simulate processing with progress updates
      for (let i = 0; i < totalProducts; i++) {
        const product = productsToProcess[i];
        setProcessingProgress((i + 0.5) * progressStep);

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // In a real app, this would call the actual API
        const success = Math.random() > 0.1; // 90% success rate
        const imageCount = success ? Math.floor(Math.random() * 5) + 5 : 0;
        
        results.push({
          product_id: product.id,
          product_name: product.name,
          success,
          image_count: imageCount,
          processing_time: Math.floor(Math.random() * 2000) + 1000,
          error: success ? undefined : 'Failed to find suitable images'
        });

        setProcessingResults([...results]);
        setProcessingProgress((i + 1) * progressStep);
      }

      // Calculate statistics
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);
      const totalImages = successful.reduce((sum, r) => sum + r.image_count, 0);
      const totalProcessingTime = results.reduce((sum, r) => sum + r.processing_time, 0);
      const costSavings = totalImages * 0.005; // £0.005 per image saved vs Google

      setBulkProcessingStats({
        total_products: totalProducts,
        successful_products: successful.length,
        failed_products: failed.length,
        total_images_found: totalImages,
        average_images_per_product: successful.length > 0 ? totalImages / successful.length : 0,
        total_processing_time: totalProcessingTime,
        total_cost_savings: costSavings,
        success_rate: (successful.length / totalProducts) * 100
      });

      toast({
        title: 'Processing complete',
        description: `Successfully processed ${successful.length} of ${totalProducts} products`,
        variant: successful.length === totalProducts ? 'default' : 'destructive'
      });

    } catch (error) {
      console.error('Processing failed:', error);
      toast({
        title: 'Processing failed',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Image className="w-8 h-8 text-blue-500" />
            Image Scraping System
          </h1>
          <p className="text-muted-foreground">
            Process inactive products with AI-powered image scraping
          </p>
        </div>
        <Badge variant={systemStatus?.service_available ? 'success' : 'destructive'} className="px-3 py-1">
          {systemStatus?.service_available ? 'System Online' : 'System Offline'}
        </Badge>
      </div>

      {/* System Status Cards */}
      {systemStatus && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Database className={`w-5 h-5 ${systemStatus.mcp_connection_status === 'connected' ? 'text-green-500' : 'text-red-500'}`} />
                <div>
                  <div className="text-sm text-muted-foreground">MCP Connection</div>
                  <div className="font-semibold">{systemStatus.mcp_connection_status}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Search className="w-5 h-5 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Available Sources</div>
                  <div className="font-semibold">{systemStatus.available_sources}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-purple-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Processing Speed</div>
                  <div className="font-semibold">{systemStatus.estimated_processing_speed}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Cost Savings</div>
                  <div className="font-semibold">{systemStatus.cost_comparison.savings_per_product} per product</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Selection Panel */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                Product Selection
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Category Filter */}
              <div>
                <Label>Category</Label>
                <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="cbd">CBD Products</SelectItem>
                    <SelectItem value="bongs">Bongs & Water Pipes</SelectItem>
                    <SelectItem value="vaporizers">Vaporizers</SelectItem>
                    <SelectItem value="accessories">Accessories</SelectItem>
                    <SelectItem value="seeds">Seeds</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Search */}
              <div>
                <Label>Search Products</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                </div>
              </div>

              {/* Image Filter */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="without-images"
                  checked={onlyWithoutImages}
                  onCheckedChange={(checked) => setOnlyWithoutImages(checked as boolean)}
                />
                <Label htmlFor="without-images">Only show products without images</Label>
              </div>

              {/* Processing Settings */}
              <div className="space-y-2">
                <Label>Batch Size</Label>
                <Select value={batchSize.toString()} onValueChange={(value) => setBatchSize(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 product per batch</SelectItem>
                    <SelectItem value="5">5 products per batch</SelectItem>
                    <SelectItem value="10">10 products per batch</SelectItem>
                    <SelectItem value="20">20 products per batch</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Max Images Per Product</Label>
                <Select value={maxImagesPerProduct.toString()} onValueChange={(value) => setMaxImagesPerProduct(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 images</SelectItem>
                    <SelectItem value="10">10 images</SelectItem>
                    <SelectItem value="15">15 images</SelectItem>
                    <SelectItem value="20">20 images</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Minimum Quality Threshold</Label>
                <Select value={minQualityThreshold.toString()} onValueChange={(value) => setMinQualityThreshold(parseFloat(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.5">0.5 (Lower quality, more images)</SelectItem>
                    <SelectItem value="0.7">0.7 (Balanced)</SelectItem>
                    <SelectItem value="0.8">0.8 (Higher quality, fewer images)</SelectItem>
                    <SelectItem value="0.9">0.9 (Premium quality only)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Process Button */}
              <Button 
                onClick={handleProcessProducts}
                disabled={selectedProducts.length === 0 || isProcessing}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {isProcessing ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    Processing {selectedProducts.length} Products...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    Process {selectedProducts.length} Products
                  </>
                )}
              </Button>

              {isProcessing && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Processing Progress</span>
                    <span>{Math.round(processingProgress)}%</span>
                  </div>
                  <Progress value={processingProgress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Product List & Results Panel */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="products" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="products">Product Selection</TabsTrigger>
              <TabsTrigger value="results">Processing Results</TabsTrigger>
            </TabsList>

            <TabsContent value="products" className="space-y-4">
              <Card>
                <CardHeader className="py-4">
                  <div className="flex items-center justify-between">
                    <CardTitle>Available Products</CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" onClick={handleSelectAll}>
                        {selectedProducts.length === getFilteredProducts().length ? 'Deselect All' : 'Select All'}
                      </Button>
                      <Badge>{getFilteredProducts().length} products</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="max-h-[500px] overflow-y-auto">
                    <table className="w-full">
                      <thead className="bg-muted/50 sticky top-0">
                        <tr>
                          <th className="p-2 text-left w-10"></th>
                          <th className="p-2 text-left">Product Name</th>
                          <th className="p-2 text-left">Category</th>
                          <th className="p-2 text-left w-24">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getFilteredProducts().map(product => (
                          <tr key={product.id} className="border-t hover:bg-muted/50">
                            <td className="p-2">
                              <Checkbox
                                checked={selectedProducts.includes(product.id)}
                                onCheckedChange={() => handleSelectProduct(product.id)}
                              />
                            </td>
                            <td className="p-2">{product.name}</td>
                            <td className="p-2">
                              <Badge variant="outline">{product.category}</Badge>
                            </td>
                            <td className="p-2">
                              {product.has_images ? (
                                <Badge variant="success" className="bg-green-100 text-green-800">Has Images</Badge>
                              ) : (
                                <Badge variant="destructive" className="bg-red-100 text-red-800">No Images</Badge>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              {bulkProcessingStats ? (
                <>
                  {/* Results Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Processing Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">Success Rate</div>
                          <div className="text-2xl font-bold">{bulkProcessingStats.success_rate.toFixed(1)}%</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">Total Images</div>
                          <div className="text-2xl font-bold">{bulkProcessingStats.total_images_found}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">Avg. Images/Product</div>
                          <div className="text-2xl font-bold">{bulkProcessingStats.average_images_per_product.toFixed(1)}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">Cost Savings</div>
                          <div className="text-2xl font-bold text-green-600">£{bulkProcessingStats.total_cost_savings.toFixed(2)}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Detailed Results */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Detailed Results</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="max-h-[400px] overflow-y-auto">
                        <table className="w-full">
                          <thead className="bg-muted/50 sticky top-0">
                            <tr>
                              <th className="p-2 text-left">Product</th>
                              <th className="p-2 text-left w-24">Status</th>
                              <th className="p-2 text-left w-24">Images</th>
                              <th className="p-2 text-left w-24">Time (ms)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {processingResults.map(result => (
                              <tr key={result.product_id} className="border-t hover:bg-muted/50">
                                <td className="p-2">{result.product_name}</td>
                                <td className="p-2">
                                  {result.success ? (
                                    <Badge variant="success" className="bg-green-100 text-green-800">Success</Badge>
                                  ) : (
                                    <Badge variant="destructive" className="bg-red-100 text-red-800">Failed</Badge>
                                  )}
                                </td>
                                <td className="p-2">{result.image_count}</td>
                                <td className="p-2">{result.processing_time}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Card className="h-96 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Sparkles className="w-16 h-16 mx-auto text-gray-400" />
                    <div>
                      <h3 className="text-lg font-semibold">No Processing Results Yet</h3>
                      <p className="text-muted-foreground">Select products and click "Process Products" to get started</p>
                    </div>
                  </div>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ImageScrapingPage;
