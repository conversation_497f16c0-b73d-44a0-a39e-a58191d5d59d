const fs = require('fs');
const readline = require('readline');

const inputFile = 'transformed_variants.csv';
const outputFile = 'transformed_variants_clean.csv';

// Create read and write streams
const readStream = fs.createReadStream(inputFile);
const writeStream = fs.createWriteStream(outputFile);

// Create readline interface
const rl = readline.createInterface({
  input: readStream,
  crlfDelay: Infinity
});

// Process the file line by line
let isFirstLine = true;
let lineCount = 0;

rl.on('line', (line) => {
  lineCount++;
  
  if (isFirstLine) {
    // Write header without metadata column
    writeStream.write('id,product_id,variant_name,sku,price,sale_price,stock_quantity,in_stock,image,option_combination,is_active,created_at,updated_at\n');
    isFirstLine = false;
    return;
  }
  
  // Split the line by commas, but respect quoted values
  const parts = [];
  let currentPart = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
      currentPart += char;
    } else if (char === ',' && !inQuotes) {
      parts.push(currentPart);
      currentPart = '';
    } else {
      currentPart += char;
    }
  }
  
  // Add the last part
  parts.push(currentPart);
  
  // Remove the metadata column (index 11)
  if (parts.length > 11) {
    parts.splice(11, 1);
  }
  
  // Join the parts back together
  const newLine = parts.join(',');
  
  // Write the line to the output file
  writeStream.write(newLine + '\n');
});

rl.on('close', () => {
  console.log(`Processed ${lineCount} lines`);
  console.log(`Created clean variants file: ${outputFile}`);
});
