import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Define CORS headers directly in this file
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

interface RequestData {
  action: 'generate-blog' | 'generate-outline' | 'generate-intro' | 'generate-conclusion' | 'generate-topics';
  topic?: string;
  title?: string;
  existingContent?: string;
  tone?: 'informative' | 'casual' | 'professional' | 'persuasive';
  length?: 'short' | 'medium' | 'long';
}

serve(async (req) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { action, topic, title, existingContent, tone = 'informative', length = 'medium' } = await req.json() as RequestData;
    
    // Validate required fields
    if (!topic && action !== 'generate-topics') {
      return new Response(
        JSON.stringify({ error: "Topic is required for this action" }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Get the API key from the environment
    const apiKey = Deno.env.get("GEMINI_API_KEY");
    if (!apiKey) {
      return new Response(
        JSON.stringify({ error: "API key not configured" }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Prepare the prompt based on the action
    let prompt = "";
    const wordCount = length === 'short' ? '300-500' : length === 'medium' ? '500-800' : '800-1200';
    
    switch (action) {
      case 'generate-blog':
        prompt = `Write a comprehensive blog post about "${topic}" with the title "${title || topic}".
        
        Requirements:
        - Word count: ${wordCount} words
        - Tone: ${tone}
        - Include an engaging introduction, main content with subsections, and a conclusion
        - Use markdown formatting (headings, lists, bold/italic)
        - Include 3-5 relevant subheadings
        - Add bullet points for key information
        - End with 2-3 discussion questions for readers
        
        Focus on providing valuable, accurate information about ${topic} in the cannabis/CBD industry.`;
        break;
        
      case 'generate-outline':
        prompt = `Create a detailed outline for a blog post about "${topic}" with the title "${title || topic}".
        
        The outline should include:
        1. Introduction (hook, background, thesis)
        2. 3-5 main sections with 2-3 subsections each
        3. Key points to cover in each section
        4. Conclusion and call-to-action
        5. 5 potential discussion questions
        
        Format as a hierarchical markdown list.`;
        break;
        
      case 'generate-intro':
        prompt = `Write an engaging introduction for a blog post about "${topic}" with the title "${title || topic}".
        
        The introduction should:
        - Hook the reader with an interesting fact or question
        - Provide context about why this topic matters
        - Clearly state what the article will cover
        - Be ${length === 'short' ? '1-2 paragraphs' : '2-3 paragraphs'} long
        - Use a ${tone} tone
        - End with a smooth transition to the main content`;
        break;
        
      case 'generate-conclusion':
        prompt = `Write a compelling conclusion for a blog post about "${topic}".
        
        The conclusion should:
        - Summarize the key points from the article
        - Reinforce the main message
        - Provide a clear takeaway for the reader
        - Include a call-to-action ${existingContent ? 'that relates to the following content: ' + existingContent.substring(0, 500) + '...' : ''}
        - Be ${length === 'short' ? '1 paragraph' : '1-2 paragraphs'} long
        - Use a ${tone} tone`;
        break;
        
      case 'generate-topics':
        const topicPrompt = topic || 'cannabis and CBD';
        prompt = `Generate 5 engaging blog post topics about ${topicPrompt} that would appeal to cannabis and CBD enthusiasts.
        
        For each topic, provide:
        1. A catchy title (title case, max 10 words)
        2. A 1-2 sentence description
        3. 3-5 key points to cover
        
        Format the response in markdown with clear headings for each topic.`;
        break;
    }

    // Call the Gemini API
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.0-pro:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2048,
            topP: 0.8,
            topK: 40,
          },
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API error:", errorText);
      return new Response(
        JSON.stringify({ error: "Error calling AI API", details: errorText }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    const data = await response.json();
    const result = data.candidates?.[0]?.content?.parts?.[0]?.text || "Failed to generate content";

    return new Response(
      JSON.stringify({ result }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Function error:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error", details: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});
