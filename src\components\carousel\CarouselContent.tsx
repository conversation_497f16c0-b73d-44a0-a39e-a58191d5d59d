
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

const CarouselContent: React.FC = () => {
  return (
    <div className="relative h-full flex flex-col items-center justify-center text-center px-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="max-w-3xl mx-auto"
      >
        <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight drop-shadow-xl">
          Premium CBD &amp; Smoking Accessories
        </h1>
        <p className="text-xl md:text-2xl mb-10 text-white/90 max-w-2xl mx-auto leading-relaxed drop-shadow-lg">
          Discover our curated collection of high-quality CBD products and artisanal smoking accessories for the modern enthusiast.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            asChild 
            size="lg" 
            className="bg-sage-500 hover:bg-sage-600 text-white px-8 py-6 text-lg transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl hover:shadow-sage-500/20"
          >
            <Link to="/shop">Explore Products</Link>
          </Button>
          <Button 
            asChild 
            variant="outline" 
            size="lg" 
            className="bg-transparent border-2 border-white text-white hover:bg-white/10 px-8 py-6 text-lg backdrop-blur-sm transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <Link to="/blog">Read Our Blog</Link>
          </Button>
        </div>
      </motion.div>
    </div>
  );
};

export default CarouselContent;
