import { useState, useEffect } from 'react';
import { getProxiedImageUrl, generateFallbackImage, getSafeImageUrl, mightHaveCorsIssues } from '../api/imageProxy';
import { getImageForTopic, getImageForTopicAsync } from '../api/directImageUrl';

/**
 * Component to test the Canvas API image handling functionality
 */
export default function ImageProxyTest() {
  const [directImage, setDirectImage] = useState<string>('');
  const [safeImage, setSafeImage] = useState<string>('');
  const [asyncImage, setAsyncImage] = useState<string>('');
  const [topic, setTopic] = useState<string>('CBD oil benefits');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [conversionStatus, setConversionStatus] = useState<'idle' | 'converting' | 'success' | 'failed'>('idle');

  // Test direct image loading
  const testDirectImage = () => {
    try {
      // This is a URL that would normally have CORS issues
      const imageUrl = 'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg';
      setDirectImage(imageUrl);
      
      // Check if this URL might have CORS issues
      const corsIssues = mightHaveCorsIssues(imageUrl);
      console.log(`URL might have CORS issues: ${corsIssues ? 'Yes' : 'No'}`);
    } catch (err) {
      setError(`Direct image error: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  // Test Canvas API image conversion
  const testCanvasConversion = async () => {
    try {
      setConversionStatus('converting');
      
      // Same URL but converted using Canvas API
      const imageUrl = 'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg';
      
      // Use our safe image URL function
      const safeUrl = await getSafeImageUrl(imageUrl);
      setSafeImage(safeUrl);
      setConversionStatus('success');
      console.log('Image converted successfully:', safeUrl.substring(0, 50) + '...');
    } catch (err) {
      setConversionStatus('failed');
      setError(`Canvas conversion error: ${err instanceof Error ? err.message : String(err)}`);
      // If conversion fails, use a fallback image
      setSafeImage(generateFallbackImage('Canvas Conversion Failed'));
    }
  };

  // Test topic-based image loading
  const testTopicImage = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Get a topic-based image asynchronously
      const image = await getImageForTopicAsync(topic);
      setAsyncImage(image);
    } catch (err) {
      setError(`Topic image error: ${err instanceof Error ? err.message : String(err)}`);
      setAsyncImage(generateFallbackImage(`Failed to load image for: ${topic}`));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Run tests on component mount
    const initTests = async () => {
      testDirectImage();
      await testCanvasConversion();
    };
    
    initTests();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Image Handling Test</h1>
      
      {/* Canvas API Status */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Canvas API Image Conversion</h2>
        <div className="flex items-center gap-2">
          <div 
            className={`w-3 h-3 rounded-full ${conversionStatus === 'idle' 
              ? 'bg-gray-400' 
              : conversionStatus === 'converting'
                ? 'bg-yellow-500'
                : conversionStatus === 'success'
                  ? 'bg-green-500' 
                  : 'bg-red-500'}`}
          />
          <span>
            {conversionStatus === 'idle' 
              ? 'Not started' 
              : conversionStatus === 'converting'
                ? 'Converting image...'
                : conversionStatus === 'success'
                  ? 'Image converted successfully' 
                  : 'Image conversion failed'}
          </span>
        </div>
        
        <div className="mt-3">
          <button 
            onClick={testCanvasConversion}
            className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
          >
            Try Again
          </button>
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Topic-Based Image Test</h2>
        <div className="flex gap-4 mb-4">
          <input
            type="text"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            className="flex-1 p-2 border rounded"
            placeholder="Enter a blog topic"
          />
          <button
            onClick={testTopicImage}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Get Image'}
          </button>
        </div>
        
        {asyncImage && (
          <div className="mb-4">
            <p className="mb-2 font-medium">Topic-based image result:</p>
            <div className="border rounded overflow-hidden">
              <img
                src={asyncImage}
                alt="Topic-based"
                className="max-w-full h-auto max-h-64 w-full object-cover"
                onError={() => setError('Failed to load topic-based image')}
              />
            </div>
            <p className="mt-2 text-sm text-gray-500 break-all">
              <span className="font-medium">Image URL:</span> {asyncImage.substring(0, 100)}{asyncImage.length > 100 ? '...' : ''}
            </p>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-2">Direct Image</h2>
          <p className="text-sm text-gray-600 mb-4">This might fail due to CORS issues</p>
          {directImage && (
            <div>
              <div className="border rounded overflow-hidden">
                <img
                  src={directImage}
                  alt="Direct"
                  className="max-w-full h-auto max-h-64 w-full object-cover"
                  onError={() => setError('Failed to load direct image (likely CORS issue)')}
                />
              </div>
              <p className="mt-2 text-sm text-gray-500 break-all">
                <span className="font-medium">Image URL:</span> {directImage}
              </p>
            </div>
          )}
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-2">Canvas-Converted Image</h2>
          <p className="text-sm text-gray-600 mb-4">This should work without CORS issues</p>
          {safeImage && (
            <div>
              <div className="border rounded overflow-hidden">
                <img
                  src={safeImage}
                  alt="Canvas Converted"
                  className="max-w-full h-auto max-h-64 w-full object-cover"
                  onError={() => setError('Failed to load converted image')}
                />
              </div>
              <p className="mt-2 text-sm text-gray-500 break-all">
                <span className="font-medium">Image URL:</span> {safeImage.substring(0, 100)}{safeImage.length > 100 ? '...' : ''}
              </p>
            </div>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mt-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <p className="font-medium">Error:</p>
          <p>{error}</p>
        </div>
      )}
    </div>
  );
}
