#!/usr/bin/env node

// Import required modules
const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key length:', supabaseKey.length);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Check if a file path is provided
if (process.argv.length < 3) {
  console.error('Please provide a CSV file path');
  process.exit(1);
}

// Get the file path from command line arguments
const filePath = process.argv[2];

// Process image URLs to match Supabase storage format
function processImageUrl(imageUrl) {
  if (!imageUrl) return null;
  
  // Extract just the filename from the URL
  const filename = imageUrl.split('/').pop();
  if (!filename) return null;
  
  // Remove the ~mv2 suffix and change extension to .webp
  const processedFilename = filename.replace(/~mv2\.(jpg|jpeg|png|webp|gif)$/i, '.webp');
  
  // Return the full URL
  return `${supabaseUrl}/storage/v1/object/public/product-images/${processedFilename}`;
}

// Process multiple image URLs
function processImageUrls(imageUrlString) {
  if (!imageUrlString) return [];
  
  // Split by semicolons or commas
  const urls = imageUrlString.split(/[;,]/).map(url => url.trim()).filter(Boolean);
  
  // Process each URL
  return urls.map(processImageUrl).filter(Boolean);
}

// Extract option definitions from product
function extractOptionDefinitions(product) {
  const optionDefinitions = {};
  
  // Look for option names and descriptions in the product
  for (let i = 1; i <= 6; i++) {
    const optionName = product[`productOptionName${i}`];
    const optionType = product[`productOptionType${i}`];
    const optionDescription = product[`productOptionDescription${i}`];
    
    if (optionName && optionType === 'DROP_DOWN' && optionDescription) {
      // Split the description by semicolons or commas to get individual values
      const values = optionDescription.split(/[;,]/).map(value => value.trim()).filter(Boolean);
      if (values.length > 0) {
        optionDefinitions[optionName] = {
          name: optionName,
          values: values,
          display_type: optionName.toLowerCase().includes('color') ? 'color' : 'dropdown'
        };
      }
    }
  }
  
  return optionDefinitions;
}

// Extract option combination from variant
function extractOptionCombination(variant, product) {
  const combination = {};
  
  // For Variant fieldType, the option value is directly in the productOptionDescription1 field
  if (variant.fieldType === 'Variant') {
    // Find the option name from the product
    for (let i = 1; i <= 6; i++) {
      const optionName = product[`productOptionName${i}`];
      if (optionName) {
        // The option value is in the same position in the variant
        const optionValue = variant[`productOptionDescription${i}`];
        if (optionValue) {
          combination[optionName] = optionValue.trim();
        }
      }
    }
  } else {
    // For Choice fieldType (legacy support)
    for (let i = 1; i <= 6; i++) {
      const optionName = variant[`productOptionName${i}`];
      const optionValue = variant[`productOptionDescription${i}`];
      
      if (optionName && optionValue) {
        combination[optionName] = optionValue.trim();
      }
    }
  }
  
  return combination;
}

// Process product data
function processProduct(product) {
  const {
    handleId,
    name,
    description,
    productImageUrl,
    price,
    salePrice,
    inventory,
    weight,
  } = product;

  // Extract option definitions
  const optionDefinitions = extractOptionDefinitions(product);

  // Generate a unique slug by adding a short unique ID at the end
  const uniqueId = uuidv4().substring(0, 8);
  const baseSlug = slugify(name || '', { lower: true, strict: true });
  const uniqueSlug = `${baseSlug}-${uniqueId}`;
  
  // Process the main image and additional images
  const mainImage = processImageUrl(productImageUrl ? productImageUrl.split(';')[0] : null);
  const additionalImages = processImageUrls(productImageUrl).slice(1);
  
  // Set is_active to false if there's no image
  const isActive = mainImage !== null;
  
  if (!isActive) {
    console.log(`Product "${name}" has no image and will be set to inactive`);
  }
  
  return {
    id: uuidv4(),
    name: name || '',
    slug: uniqueSlug,
    description: description || '',
    price: parseFloat(price) || 0,
    sale_price: salePrice ? parseFloat(salePrice) : null,
    image: mainImage,
    additional_images: additionalImages,
    category_id: null,
    brand_id: null,
    sku: product.sku || '',
    stock_quantity: parseInt(inventory) || 0,
    weight: parseFloat(weight) || 0,
    in_stock: true,
    is_featured: false,
    is_new: false,
    is_active: isActive, // Only set active if there's an image
    option_definitions: optionDefinitions,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

// Process variant data
function processVariant(variant, productId, productPrice, productImage, product) {
  // Extract option combination
  const optionCombination = extractOptionCombination(variant, product);
  
  // Create variant name from option values
  const variantName = Object.values(optionCombination).join(' - ');
  
  // Handle variant pricing
  let priceAdjustment = 0;
  
  // If variant has a price, it's a surcharge to be added to the base price
  if (variant.price && parseFloat(variant.price) !== 0) {
    priceAdjustment = parseFloat(variant.price);
  } else if (variant.surcharge && parseFloat(variant.surcharge) !== 0) {
    // Some variants use 'surcharge' field instead of 'price'
    priceAdjustment = parseFloat(variant.surcharge);
  }

  return {
    id: uuidv4(),
    product_id: productId,
    sku: variant.sku || `${productId.substring(0, 8)}-${Object.values(optionCombination).join('-')}`,
    price_adjustment: priceAdjustment,
    stock_quantity: parseInt(variant.inventory) || 30,
    options: optionCombination,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

// First, clear existing data
async function clearExistingData() {
  console.log('Clearing existing products and variants...');
  
  try {
    // First truncate product_variants (child table)
    const { error: variantsError } = await supabase
      .from('product_variants')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
    
    if (variantsError) {
      console.error('Error clearing variants:', variantsError);
    }
    
    // Then truncate products
    const { error: productsError } = await supabase
      .from('products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
    
    if (productsError) {
      console.error('Error clearing products:', productsError);
    }
    
    console.log('Existing data cleared.');
  } catch (error) {
    console.error('Error during data clearing:', error);
  }
}

// Main function to import products and variants
async function importProducts(data, limit = 10) {
  console.log(`Starting import of up to ${limit} products with variants...`);
  
  // First, clear existing data
  await clearExistingData();
  
  // Group products and variants
  const productGroups = {};
  
  data.forEach(item => {
    if (item.fieldType === 'Product') {
      const productId = item.handleId;
      productGroups[productId] = {
        product: item,
        variants: []
      };
    } else if (item.fieldType === 'Variant' || item.fieldType === 'Choice') {
      // Handle both Variant and Choice fieldTypes
      const productId = item.handleId;
      if (productGroups[productId]) {
        productGroups[productId].variants.push(item);
      }
    }
  });
  
  // Filter to only products with variants and images
  const productsWithVariants = Object.values(productGroups)
    .filter(group => 
      group.variants.length > 0 && 
      group.product.productImageUrl && 
      group.product.productImageUrl.trim() !== ''
    )
    .slice(0, limit);
  
  console.log(`Found ${productsWithVariants.length} products with variants and images`);
  
  // Process and import each product
  for (const group of productsWithVariants) {
    const { product, variants } = group;
    
    // Process the main product
    const processedProduct = processProduct(product);
    console.log(`Importing product: ${processedProduct.name} (${processedProduct.id})`);
    
    // Insert the product into Supabase
    const { data: insertedProduct, error: productError } = await supabase
      .from('products')
      .insert([processedProduct])
      .select();
    
    if (productError) {
      console.error(`Error inserting product ${processedProduct.name}:`, productError);
      continue;
    }
    
    console.log(`Successfully imported product: ${processedProduct.name}`);
    
    // Process and insert variants
    for (const variant of variants) {
      const processedVariant = processVariant(
        variant, 
        processedProduct.id, 
        processedProduct.price,
        processedProduct.image,
        product
      );
      
      console.log(`Importing variant: ${JSON.stringify(processedVariant.options)} for product ${processedProduct.name}`);
      
      const { data: insertedVariant, error: variantError } = await supabase
        .from('product_variants')
        .insert([processedVariant])
        .select();
      
      if (variantError) {
        console.error(`Error inserting variant for ${processedProduct.name}:`, variantError);
      } else {
        console.log(`Successfully imported variant for ${processedProduct.name}`);
      }
    }
  }
  
  console.log('Import completed!');
}

// Read and parse the CSV file
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    process.exit(1);
  }

  // Parse the CSV data
  Papa.parse(data, {
    header: true,
    skipEmptyLines: true,
    complete: (results) => {
      console.log(`Reading products and variants from ${filePath}`);
      
      // Import the products and variants
      importProducts(results.data)
        .catch(error => {
          console.error('Error during import:', error);
          process.exit(1);
        });
    },
    error: (error) => {
      console.error('Error parsing CSV:', error);
      process.exit(1);
    }
  });
});
