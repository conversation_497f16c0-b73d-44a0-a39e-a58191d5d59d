import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/auth.basic';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Loader2, Download, Plus, Search, Trash2, Send, Edit } from 'lucide-react';
import { getNewsletterSubscribers, updateSubscriberStatus, NewsletterSubscriber } from '@/integrations/supabase/newsletter';
import { format } from 'date-fns';

const NewsletterPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [subscribers, setSubscribers] = useState<NewsletterSubscriber[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubscribers, setSelectedSubscribers] = useState<string[]>([]);
  const [selectedSubscriber, setSelectedSubscriber] = useState<NewsletterSubscriber | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Add debugging for authentication
  useEffect(() => {
    console.log('Newsletter Page Auth Check:', {
      user: !!user,
      metadata: user?.app_metadata,
      isAdmin: user?.app_metadata?.admin
    });

    // No redirect here - the ProtectedRoute component handles this
  }, [user]);

  // Fetch subscribers
  useEffect(() => {
    fetchSubscribers();
  }, []);

  const fetchSubscribers = async (filterActive?: boolean) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await getNewsletterSubscribers(filterActive);

      if (result.error) {
        throw new Error(result.error);
      }

      // Set subscribers from the data property of the result
      setSubscribers(result.data || []);
    } catch (error) {
      console.error('Error fetching subscribers:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch subscribers',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const { toast } = useToast();

  // Filter subscribers based on search query
  const filteredSubscribers = subscribers.filter((subscriber) => {
    const query = searchQuery.toLowerCase();
    return (
      subscriber.email.toLowerCase().includes(query) ||
      (subscriber.first_name && subscriber.first_name.toLowerCase().includes(query)) ||
      (subscriber.last_name && subscriber.last_name.toLowerCase().includes(query))
    );
  });

  // Toggle selection of all subscribers
  const toggleSelectAll = () => {
    if (selectedSubscribers.length === filteredSubscribers.length) {
      setSelectedSubscribers([]);
    } else {
      setSelectedSubscribers(filteredSubscribers.map((sub) => sub.id));
    }
  };

  // Toggle selection of a single subscriber
  const toggleSelectSubscriber = (id: string) => {
    if (selectedSubscribers.includes(id)) {
      setSelectedSubscribers(selectedSubscribers.filter((subId) => subId !== id));
    } else {
      setSelectedSubscribers([...selectedSubscribers, id]);
    }
  };

  // Update subscriber status
  const handleUpdateStatus = async (id: string, isActive: boolean) => {
    setIsProcessing(true);
    try {
      const result = await updateSubscriberStatus(id, isActive);

      if (result.error) {
        throw new Error(result.error);
      }

      // Update was successful
      setSubscribers(
        subscribers.map((sub) =>
          sub.id === id ? { ...sub, is_active: isActive } : sub
        )
      );

      toast({
        title: 'Success',
        description: `Subscriber ${isActive ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error('Error updating subscriber status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update subscriber status',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Export subscribers to CSV
  const exportSubscribers = () => {
    const selectedData = selectedSubscribers.length > 0
      ? subscribers.filter(sub => selectedSubscribers.includes(sub.id))
      : subscribers;

    const csvContent = [
      // CSV header
      ['Email', 'First Name', 'Last Name', 'Subscribed At', 'Status', 'Tags'].join(','),
      // CSV rows
      ...selectedData.map(sub => [
        sub.email,
        sub.first_name || '',
        sub.last_name || '',
        sub.subscribed_at,
        sub.is_active ? 'Active' : 'Inactive',
        (sub.tags || []).join(';')
      ].join(','))
    ].join('\n');

    // Create a blob and download
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Newsletter Subscribers</CardTitle>
              <CardDescription>Manage your newsletter subscribers</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => navigate('/admin/newsletter/create')}
              >
                <Edit className="mr-2 h-4 w-4" />
                Create Newsletter
              </Button>
              <Button
                variant="outline"
                onClick={exportSubscribers}
                disabled={isLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                onClick={() => fetchSubscribers()}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  'Refresh'
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search subscribers..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchSubscribers(true)}
              >
                Active
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchSubscribers(false)}
              >
                Inactive
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchSubscribers()}
              >
                All
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={
                        filteredSubscribers.length > 0 &&
                        selectedSubscribers.length === filteredSubscribers.length
                      }
                      onCheckedChange={toggleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Subscribed At</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                      <p className="mt-2 text-sm text-muted-foreground">
                        Loading subscribers...
                      </p>
                    </TableCell>
                  </TableRow>
                ) : filteredSubscribers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <p className="text-muted-foreground">
                        No subscribers found
                      </p>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSubscribers.map((subscriber) => (
                    <TableRow key={subscriber.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedSubscribers.includes(subscriber.id)}
                          onCheckedChange={() =>
                            toggleSelectSubscriber(subscriber.id)
                          }
                        />
                      </TableCell>
                      <TableCell>{subscriber.email}</TableCell>
                      <TableCell>
                        {subscriber.first_name || subscriber.last_name
                          ? `${subscriber.first_name || ''} ${
                              subscriber.last_name || ''
                            }`
                          : '-'}
                      </TableCell>
                      <TableCell>
                        {format(
                          new Date(subscriber.subscribed_at),
                          'MMM dd, yyyy'
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={subscriber.is_active ? 'default' : 'outline'}
                        >
                          {subscriber.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleUpdateStatus(
                              subscriber.id,
                              !subscriber.is_active
                            )
                          }
                          disabled={isProcessing}
                        >
                          {subscriber.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            {filteredSubscribers.length} subscribers
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setSelectedSubscribers([]);
              }}
              disabled={selectedSubscribers.length === 0}
            >
              Clear Selection
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/admin/newsletter/create')}
            >
              <Send className="mr-2 h-4 w-4" />
              Create & Send Newsletter
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default NewsletterPage;

