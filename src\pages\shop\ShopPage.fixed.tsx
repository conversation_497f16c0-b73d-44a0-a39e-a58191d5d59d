import { useState, useEffect, useRef, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import type { Product } from '@/types/database';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import CategoryBanner from '@/components/shop/CategoryBanner';
import { categoryBannerImages, categoryDescriptions } from '@/data/categoryBanners';
import TransitionLayout from '@/components/layout/TransitionLayout';
import SearchInput from '@/components/shop/SearchInput';
import SubcategoryImageGrid from '@/components/shop/SubcategoryImageGrid';
import SimpleSeedFilters from '@/components/shop/SimpleSeedFilters';
// Featured spotlight removed to fix loading issues
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
// Import the SimpleProductGrid component
import SimpleProductGrid from '@/components/products/SimpleProductGrid';
import SeedProductGrid from '@/components/products/SeedProductGrid';

// Database category type from Supabase
interface DatabaseCategory {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string | null;
  display_order?: number; // Make display_order optional in base type
}

// Extended Category interface for UI
interface Category extends DatabaseCategory {
  subcategories?: Category[];
}

// SubCategory interface with parent reference
interface SubCategory extends DatabaseCategory {
  parent?: Category;
}

// Union type for functions that accept either Category or SubCategory
type CategoryOrSubCategory = Category | SubCategory;

const ShopPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
  const [selectedBrand, setSelectedBrand] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [seedFilters, setSeedFilters] = useState<Record<string, string[]>>({});
  const [isSeedCategory, setIsSeedCategory] = useState(false);
  
  // Handle seed filter changes - memoized to prevent infinite loops
  const handleSeedFilterChange = useCallback((filters: Record<string, string[]>) => {
    console.log('Seed filters changed:', filters);
    // Only update if the filters have actually changed
    if (JSON.stringify(filters) !== JSON.stringify(seedFilters)) {
      setSeedFilters(filters);
    }
  }, [seedFilters]);

  // Refs for debouncing
  const searchTimeoutRef = useRef<number>();
  const urlUpdateTimeoutRef = useRef<number>();

  // Flag to prevent URL updates from triggering state changes
  const isUpdatingFromUrlRef = useRef(false);

  // Check if the current category is the Seeds category
  const checkSeedCategory = () => {
    // Get the category from URL parameters
    const categoryParam = searchParams.get('category');
    
    // For testing purposes, if the URL parameter is 'seeds', always treat as seed category
    if (categoryParam && categoryParam.toLowerCase() === 'seeds') {
      console.log('Found seeds in URL param, setting as seed category');
      setIsSeedCategory(true);
      // Force select the seed category ID
      setSelectedCategory('cat-2');
      return;
    }
    
    // Find the category by slug if we have categories loaded
    if (categories.length > 0 && categoryParam) {
      const category = categories.find(cat => cat.slug === categoryParam);
      
      if (category) {
        // Check if this is the Seeds category
        const isSeedCat = 
          category.id === 'cat-2' || 
          category.id === '9606c3d0-e84e-450f-a09a-53f6420bfd58' || 
          category.name.toLowerCase().includes('seed');
        
        console.log('Category check result:', { 
          categoryId: category.id, 
          categoryName: category.name, 
          isSeedCategory: isSeedCat 
        });
        
        setIsSeedCategory(isSeedCat);
        return;
      }
    }
    
    // If URL explicitly contains 'seeds' anywhere, treat as seed category
    if (window.location.href.toLowerCase().includes('seeds')) {
      console.log('Found seeds in URL, setting as seed category');
      setIsSeedCategory(true);
      setSelectedCategory('cat-2');
      return;
    }
    
    // Default case
    console.log('Not a seed category');
    setIsSeedCategory(false);
  };

  // Effect to scroll to top when URL parameters change and check location state
  useEffect(() => {
    // Use a small timeout to ensure the scroll happens after the transition starts
    const timer = setTimeout(() => {
      window.scrollTo(0, 0);
      
      // Check for location state from React Router navigation
      const locationState = window.history.state?.usr;
      if (locationState?.categoryId && locationState?.directCategoryId) {
        console.log('Found direct category ID in location state:', locationState.categoryId);
        setSelectedCategory(locationState.categoryId);
        
        // Clear the state to prevent it from being used again
        window.history.replaceState(
          { ...window.history.state, usr: { ...locationState, directCategoryId: false } },
          ''
        );
        return;
      }

      // Force reload of products when URL changes
      if (!isUpdatingFromUrlRef.current) {
        // This will re-initialize from URL parameters
        const categoryParam = searchParams.get('category');
        console.log('URL changed, current category param:', categoryParam);
        
        // Force the effect that processes URL parameters to run again
        if (categoryParam) {
          // Find the category by slug
          const category = categories.find(cat => cat.slug === categoryParam);
          if (category) {
            setSelectedCategory(category.id);
          }
        } else {
          setSelectedCategory('all');
        }
        
        // Check for subcategory parameter
        const subcategoryParam = searchParams.get('subcategory');
        if (subcategoryParam) {
          // Find the subcategory by slug
          const subcategory = subcategories.find(sub => sub.slug === subcategoryParam);
          if (subcategory) {
            setSelectedSubcategory(subcategory.id);
          }
        } else {
          setSelectedSubcategory('all');
        }
        
        // Check for brand parameter
        const brandParam = searchParams.get('brand');
        if (brandParam && brandParam !== 'all') {
          setSelectedBrand(brandParam);
        } else {
          setSelectedBrand('all');
        }
        
        // Check for search parameter
        const searchParam = searchParams.get('search');
        if (searchParam) {
          setSearchQuery(searchParam);
        } else {
          setSearchQuery('');
        }
      }
      
      // Check if the current category is the Seeds category
      checkSeedCategory();
    }, 50);
    
    return () => clearTimeout(timer);
  }, [searchParams, categories, subcategories]);

  // Effect to fetch categories from the database
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        
        // Fetch categories from the database
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .order('display_order', { ascending: true });
        
        if (categoriesError) {
          console.error('Error fetching categories:', categoriesError);
          return;
        }
        
        console.log('Raw categories data:', categoriesData);
        
        // Process categories and subcategories
        if (categoriesData) {
          // Filter subcategories (categories with a parent_id)
          const subCats = categoriesData
            .filter((cat: DatabaseCategory) => cat.parent_id !== null)
            .sort((a: DatabaseCategory, b: DatabaseCategory) => {
              const aOrder = a.display_order || 0;
              const bOrder = b.display_order || 0;
              return aOrder - bOrder;
            });
          
          console.log('Subcategories with display_order:', subCats);
          
          // Filter main categories (categories without a parent_id)
          const mainCats = categoriesData
            .filter((cat: DatabaseCategory) => cat.parent_id === null)
            .sort((a: DatabaseCategory, b: DatabaseCategory) => {
              const aOrder = a.display_order || 0;
              const bOrder = b.display_order || 0;
              return aOrder - bOrder;
            });
          
          console.log('Main categories:', mainCats);
          console.log('Subcategories:', subCats);
          
          // Create a hierarchical structure
          const catsWithSubs = mainCats.map((cat: DatabaseCategory) => {
            const catSubcategories = subCats.filter(
              (subCat: DatabaseCategory) => subCat.parent_id === cat.id
            );
            return { ...cat, subcategories: catSubcategories };
          });
          
          console.log('Categories with subs:', catsWithSubs);
          
          // Update state with the processed data
          setCategories(catsWithSubs as Category[]);
          setSubcategories(subCats as SubCategory[]);
          
          // Initialize from URL parameters
          const categoryParam = searchParams.get('category');
          if (categoryParam) {
            // Find the category by slug
            const category = catsWithSubs.find((cat: Category) => cat.slug === categoryParam);
            if (category) {
              setSelectedCategory(category.id);
            }
          }
          
          // Check for subcategory parameter
          const subcategoryParam = searchParams.get('subcategory');
          if (subcategoryParam) {
            // Find the subcategory by slug
            const subcategory = subCats.find((sub: SubCategory) => sub.slug === subcategoryParam);
            if (subcategory) {
              setSelectedSubcategory(subcategory.id);
            }
          }
          
          // Check for brand parameter
          const brandParam = searchParams.get('brand');
          if (brandParam && brandParam !== 'all') {
            setSelectedBrand(brandParam);
          }
          
          // Check for search parameter
          const searchParam = searchParams.get('search');
          if (searchParam) {
            setSearchQuery(searchParam);
          }
          
          // Check if the current category is the Seeds category
          checkSeedCategory();
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);

  // Update URL based on current state without triggering state changes
  const updateURL = () => {
    if (isUpdatingFromUrlRef.current) return;
    
    // Set the flag to prevent URL updates from triggering state changes
    isUpdatingFromUrlRef.current = true;
    
    // Clear any existing timeout
    if (urlUpdateTimeoutRef.current) {
      window.clearTimeout(urlUpdateTimeoutRef.current);
    }
    
    // Create a new URL parameters object
    const newParams = new URLSearchParams();
    
    // Add category parameter if not 'all'
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        newParams.set('category', category.slug);
      }
    }
    
    // Add subcategory parameter if not 'all'
    if (selectedSubcategory !== 'all') {
      const subcategory = subcategories.find(sub => sub.id === selectedSubcategory);
      if (subcategory) {
        newParams.set('subcategory', subcategory.slug);
      }
    }
    
    // Add brand parameter if not 'all'
    if (selectedBrand !== 'all') {
      newParams.set('brand', selectedBrand);
    }
    
    // Add search parameter if not empty
    if (searchQuery) {
      newParams.set('search', searchQuery);
    }
    
    // Update the URL
    setSearchParams(newParams, { replace: true });
    
    // Reset the flag after a delay
    urlUpdateTimeoutRef.current = window.setTimeout(() => {
      isUpdatingFromUrlRef.current = false;
    }, 100);
  };

  // Helper function to get category image URL
  const getCategoryImageUrl = (category: CategoryOrSubCategory) => {
    // First check if the category has an image in the database
    if (category.image) {
      return category.image;
    }
    
    // Then check if we have a predefined image for this category
    const slug = category.slug || '';
    if (categoryBannerImages[slug]) {
      return categoryBannerImages[slug];
    }
    
    // If it's a subcategory, try to use the parent category's image
    if ('parent_id' in category && category.parent_id) {
      const parentCategory = categories.find(cat => cat.id === category.parent_id);
      if (parentCategory && parentCategory.image) {
        return parentCategory.image;
      }
      
      // Try predefined image for parent
      if (parentCategory && categoryBannerImages[parentCategory.slug || '']) {
        return categoryBannerImages[parentCategory.slug || ''];
      }
    }
    
    // Default fallback image
    return '/images/banners/default-category.jpg';
  };

  // Effect to update URL when state changes
  useEffect(() => {
    // Skip the first render
    if (loading) return;
    
    updateURL();
    
    // Check if the current category is the Seeds category
    checkSeedCategory();
  }, [selectedCategory, selectedSubcategory, selectedBrand, searchQuery]);

  // Get the current category data for the banner
  const getCategoryData = () => {
    let bannerImage = '/images/banners/default-category.jpg';
    let description = 'Browse our selection of premium products.';
    let isDefault = true;
    let categorySlug = '';
    
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        categorySlug = category.slug || '';
        
        // Use the category image if available
        if (category.image) {
          bannerImage = category.image;
          isDefault = false;
        } else if (categoryBannerImages[categorySlug]) {
          bannerImage = categoryBannerImages[categorySlug];
          isDefault = false;
        }
        
        // Use the category description if available
        if (category.description) {
          description = category.description;
        } else if (categoryDescriptions[categorySlug]) {
          description = categoryDescriptions[categorySlug];
        }
      }
    }
    
    if (selectedSubcategory !== 'all') {
      const subcategory = subcategories.find(sub => sub.id === selectedSubcategory);
      if (subcategory) {
        categorySlug = subcategory.slug || '';
        
        // Use the subcategory image if available
        if (subcategory.image) {
          bannerImage = subcategory.image;
          isDefault = false;
        } else if (categoryBannerImages[categorySlug]) {
          bannerImage = categoryBannerImages[categorySlug];
          isDefault = false;
        }
        
        // Use the subcategory description if available
        if (subcategory.description) {
          description = subcategory.description;
        } else if (categoryDescriptions[categorySlug]) {
          description = categoryDescriptions[categorySlug];
        }
      }
    }
    
    return { bannerImage, description, isDefault, categorySlug };
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId: string) => {
    setSelectedSubcategory(subcategoryId);
    
    // Update the URL
    updateURL();
  };

  // Get filtered subcategories for the selected category
  const filteredSubcategories = selectedCategory !== 'all'
    ? subcategories.filter(sub => sub.parent_id === selectedCategory)
    : [];
  
  // Get category name for display
  const categoryName = selectedCategory !== 'all'
    ? categories.find(cat => cat.id === selectedCategory)?.name || ''
    : '';
  
  // Get page title based on selected category and subcategory
  let pageTitle = 'Shop';
  
  if (selectedCategory !== 'all') {
    const category = categories.find(cat => cat.id === selectedCategory);
    if (category) {
      pageTitle = category.name;
      
      if (selectedSubcategory !== 'all') {
        const subcategory = subcategories.find(sub => sub.id === selectedSubcategory);
        if (subcategory) {
          pageTitle = `${subcategory.name}`;
        }
      }
    }
  }

  if (loading) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">Shop</h1>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
        </div>
      </div>
    );
  }

  const { bannerImage, description, isDefault, categorySlug } = getCategoryData();

  return (
    <div className="container-custom py-8">
      {/* Category Banner */}
      <CategoryBanner
        title={pageTitle}
        image={bannerImage}
        description={description}
        isDefault={isDefault}
      />
      
      {/* Featured Product Spotlight removed to fix loading issues */}
      
      {/* Bold, eye-catching search area with solid colors */}
      <div className="mb-8 overflow-hidden rounded-lg shadow-lg border-2 border-sage-600">
        <div className="bg-gradient-to-r from-sage-600 to-sage-700 p-4 text-white">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h2 className="text-xl font-semibold">Find Your Perfect Product</h2>
          </div>
        </div>
        
        <div className="bg-white p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search Input */}
            <div className="col-span-2">
              <SearchInput
                initialValue={searchQuery}
                onSearch={(value) => {
                  setSearchQuery(value);
                  
                  // Clear timeout if it exists
                  if (searchTimeoutRef.current) {
                    window.clearTimeout(searchTimeoutRef.current);
                  }
                  
                  // Update URL after a short delay
                  searchTimeoutRef.current = window.setTimeout(() => {
                    updateURL();
                  }, 500);
                }}
                placeholder="Search products..."
              />
            </div>
            
            {/* Category Dropdown */}
            <div>
              <Select
                value={selectedCategory}
                onValueChange={(value) => {
                  isUpdatingFromUrlRef.current = true;
                  
                  // First reset subcategory to ensure products load correctly
                  setSelectedSubcategory('all');
                  
                  // Then update category state
                  setTimeout(() => {
                    console.log('Setting category with delay to ensure subcategory reset is processed');
                    setSelectedCategory(value);
                  }, 50);

                  // Create a new URL parameters object
                  const newParams = new URLSearchParams();
                  
                  // Only add category parameter if not 'all'
                  if (value !== 'all') {
                    const category = categories.find(cat => cat.id === value);
                    if (category) {
                      newParams.set('category', category.slug);
                    }
                  }
                  
                  // Keep brand parameter if it exists
                  if (selectedBrand !== 'all') {
                    newParams.set('brand', selectedBrand);
                  }
                  
                  // Keep search parameter if it exists
                  if (searchQuery) {
                    newParams.set('search', searchQuery);
                  }
                  
                  // Update the URL immediately
                  setSearchParams(newParams, { replace: true });
                  
                  // Reset the flag after a delay
                  setTimeout(() => {
                    isUpdatingFromUrlRef.current = false;
                  }, 200);
                }}
              >
                <SelectTrigger className="w-[200px] border-sage-200 hover:border-sage-400 transition-colors duration-300 shadow-sm">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Subcategory Image Grid - only show if a category is selected and it has subcategories */}
      {selectedCategory !== 'all' && filteredSubcategories.length > 0 && (
        <SubcategoryImageGrid
          subcategories={filteredSubcategories as any[]}
          selectedSubcategoryId={selectedSubcategory}
          onSelectSubcategory={handleSubcategorySelect}
          categoryName={categoryName}
          getCategoryImageUrl={(category) => getCategoryImageUrl(category as CategoryOrSubCategory)}
        />
      )}
      
      {/* Main content area with sidebar layout for filters */}
      <div className="flex flex-col md:flex-row gap-6 mt-6">
        {/* Sidebar for filters - only show when Seeds category is selected */}
        {isSeedCategory && (
          <div className="w-full md:w-64 flex-shrink-0">
            <SimpleSeedFilters
              onFiltersChange={handleSeedFilterChange}
            />
          </div>
        )}
        
        {/* Main product grid area */}
        <div className="flex-grow">
          <SimpleProductGrid
            categoryId={selectedCategory !== 'all' ? selectedCategory : undefined}
            subcategoryId={selectedSubcategory !== 'all' ? selectedSubcategory : undefined}
            brandId={selectedBrand !== 'all' ? selectedBrand : undefined}
            searchQuery={searchQuery}
            seedFilters={isSeedCategory ? seedFilters : undefined}
          />
        </div>
      </div>
    </div>
  );
};

export default ShopPage;
