/**
 * Import Products and Variants from CSV
 * 
 * This script imports products and variants from CSV files into the database.
 * It reads the transformed CSV files and inserts the data into the products and product_variants tables.
 * 
 * Usage:
 * node src/scripts/import-products.cjs [products-csv-path] [variants-csv-path]
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const Papa = require('papaparse');
const dotenv = require('dotenv');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
let supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  // If environment variable is not set, try to read from .env file
  try {
    const envContent = fs.readFileSync(path.join(__dirname, '../../.env'), 'utf8');
    const envLines = envContent.split('\n');
    for (const line of envLines) {
      if (line.startsWith('VITE_SUPABASE_ANON_KEY=')) {
        supabaseKey = line.split('=')[1].trim();
        console.log('Found SUPABASE_ANON_KEY in .env file');
        break;
      }
    }
  } catch (error) {
    console.error('Error reading .env file:', error);
  }

  if (!supabaseKey) {
    console.error('VITE_SUPABASE_ANON_KEY not found in environment or .env file');
    process.exit(1);
  }
}

// Process image URLs to match Supabase storage format
function processImageUrl(imageUrl) {
  if (!imageUrl) return null;
  
  // Extract just the filename from the URL
  const filename = imageUrl.split('/').pop();
  if (!filename) return null;
  
  // Remove the ~mv2 suffix and change extension to .webp
  const processedFilename = filename.replace(/~mv2\.(jpg|jpeg|png|webp|gif)$/i, '.webp');
  
  // Use the full Supabase storage URL format with the product-images bucket
  const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
  
  // Use the correct format for product images
  return `${supabaseUrl}/storage/v1/object/public/product-images/${processedFilename}`;
}

// Process multiple image URLs
function processImageUrls(imageUrls) {
  if (!imageUrls || !Array.isArray(imageUrls)) return [];
  return imageUrls.map(url => processImageUrl(url));
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Default paths
let productsPath = path.join(__dirname, '../../data/output/products-transformed.csv');
let variantsPath = path.join(__dirname, '../../data/output/product_variants.csv');

// Override with command line arguments if provided
if (process.argv.length > 2) {
  productsPath = process.argv[2];
}

if (process.argv.length > 3) {
  variantsPath = process.argv[3];
}

// Helper function to parse CSV file
async function parseCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          resolve(results.data);
        },
        error: (error) => {
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Process Wix CSV format to extract option definitions
function extractOptionDefinitions(product) {
  const optionDefinitions = {};
  
  // Look for option names and descriptions in the product
  for (let i = 1; i <= 6; i++) {
    const optionName = product[`productOptionName${i}`];
    const optionType = product[`productOptionType${i}`];
    const optionDescription = product[`productOptionDescription${i}`];
    
    if (optionName && optionType === 'DROP_DOWN' && optionDescription) {
      // Split the description by semicolons or commas to get individual values
      const values = optionDescription.split(/[;,]/).map(value => value.trim()).filter(Boolean);
      if (values.length > 0) {
        optionDefinitions[optionName] = values;
      }
    }
  }
  
  return optionDefinitions;
}

// Import products
async function importProducts(productsData) {
  console.log(`Importing ${productsData.length} products...`);
  
  // Process products in batches to avoid hitting rate limits
  const batchSize = 5;
  const batches = [];
  
  for (let i = 0; i < productsData.length; i += batchSize) {
    batches.push(productsData.slice(i, i + batchSize));
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  // Create a map of product IDs for reference when importing variants
  const productIdMap = new Map();
  const productHandleMap = new Map(); // Map handleId to product ID for variant linking
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} products)`);
    
    // Process each product in the batch
    for (const rawProduct of batch) {
      try {
        // Only process items with fieldType = 'Product'
        if (rawProduct.fieldType !== 'Product') {
          continue;
        }
        
        // Create a product object
        const product = {
          id: uuidv4(), // Generate a new UUID for the product
          name: rawProduct.name,
          slug: rawProduct.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, ''),
          description: rawProduct.description || '',
          price: parseFloat(rawProduct.price) || 0,
          sale_price: null,
          image: '',
          additional_images: JSON.stringify([]),
          category_id: null, // Will be set later if categories are implemented
          brand_id: null, // Will be set later if brands are implemented
          sku: rawProduct.sku || null,
          stock_quantity: parseInt(rawProduct.inventory) || 0,
          weight: parseFloat(rawProduct.weight) || null,
          in_stock: rawProduct.inventory === 'InStock' || rawProduct.inventory > 0,
          is_featured: false,
          is_new: false,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        
        // Process the main image and additional images
        if (rawProduct.productImageUrl) {
          const imageUrls = rawProduct.productImageUrl.split(';').map(img => img.trim()).filter(Boolean);
          if (imageUrls.length > 0) {
            // Set the main image (first in the list)
            product.image = processImageUrl(imageUrls[0]);
            
            // Set additional images (rest of the list)
            if (imageUrls.length > 1) {
              product.additional_images = JSON.stringify(processImageUrls(imageUrls.slice(1)));
            }
          }
        }
        
        // Calculate sale price if discount is available
        if (rawProduct.discountMode === 'PERCENT' && rawProduct.discountValue) {
          const discountValue = parseFloat(rawProduct.discountValue);
          const originalPrice = parseFloat(rawProduct.price);
          if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
            product.sale_price = originalPrice * (1 - discountValue / 100);
          }
        }
        
        // Extract option definitions
        const optionDefinitions = extractOptionDefinitions(rawProduct);
        product.option_definitions = JSON.stringify(optionDefinitions);
        
        // Insert product into database
        const { data, error } = await supabase
          .from('products')
          .insert(product);
        
        if (error) {
          console.error(`Error inserting product ${product.name}:`, error);
          errorCount++;
        } else {
          // Store the mapping of handleId to new ID for variant linking
          productHandleMap.set(rawProduct.handleId, product.id);
          // Also store in productIdMap for backward compatibility
          productIdMap.set(rawProduct.handleId, product.id);
          successCount++;
          console.log(`Imported product: ${product.name} (ID: ${product.id})`);
        }
      } catch (error) {
        console.error(`Error processing product ${rawProduct.name || rawProduct.handleId}:`, error);
        errorCount++;
      }
    }
  }
  
  console.log(`Products import complete: ${successCount} succeeded, ${errorCount} failed`);
  return { productIdMap, productHandleMap };
}

// Extract option combination from variant data
function extractOptionCombination(variant) {
  const optionCombination = {};
  
  // Process up to 3 options
  for (let i = 1; i <= 3; i++) {
    // For the CSV format we're using, the option names/values are in these fields
    const nameField = `productOptionName${i}`;
    const valueField = `productOptionValue${i}`;
    
    // For the Wix format, they're in these fields
    const wixNameField = `optionName${i}`;
    const wixValueField = `optionValue${i}`;
    
    // Try both formats
    if (variant[nameField] && variant[valueField]) {
      const optionName = variant[nameField].trim();
      const optionValue = variant[valueField].trim();
      optionCombination[optionName] = optionValue;
    } else if (variant[wixNameField] && variant[wixValueField]) {
      const optionName = variant[wixNameField].trim();
      const optionValue = variant[wixValueField].trim();
      optionCombination[optionName] = optionValue;
    }
  }
  
  // If we didn't find any options, try to extract from the Wix format using Strength and Size
  if (Object.keys(optionCombination).length === 0) {
    if (variant.Strength || variant.Size) {
      if (variant.Strength) optionCombination['Strength'] = variant.Strength;
      if (variant.Size) optionCombination['Size'] = variant.Size;
    }
  }
  
  // If we still didn't find any options, try to extract from the fieldType=Choice format
  if (Object.keys(optionCombination).length === 0 && variant.fieldType === 'Choice') {
    // For our test data, the options are directly in these fields
    if (variant.productOptionName1 && variant.productOptionDescription1) {
      optionCombination[variant.productOptionName1.trim()] = variant.productOptionDescription1.trim();
    }
    if (variant.productOptionName2 && variant.productOptionDescription2) {
      optionCombination[variant.productOptionName2.trim()] = variant.productOptionDescription2.trim();
    }
  }
  
  return optionCombination;
}

// Import variants
async function importVariants(variantsData, productHandleMap) {
  console.log(`Importing ${variantsData.length} variants...`);
  
  // Process variants in batches
  const batchSize = 10;
  const batches = [];
  
  for (let i = 0; i < variantsData.length; i += batchSize) {
    batches.push(variantsData.slice(i, i + batchSize));
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} variants)`);
    
    // Process each variant in the batch
    for (const rawVariant of batch) {
      try {
        // Only process items with fieldType = 'Choice'
        if (rawVariant.fieldType !== 'Choice') {
          continue;
        }
        
        // Get the product ID from the handle map
        // The handleId of the variant should be in the format: productHandleId_v1, productHandleId_v2, etc.
        const productHandleId = rawVariant.handleId.split('_v')[0];
        const productId = productHandleMap.get(productHandleId);
        
        // Skip variants with invalid product_id
        if (!productId) {
          console.warn(`Skipping variant ${rawVariant.handleId} with missing product_id. Product handle: ${productHandleId}`);
          errorCount++;
          continue;
        }
        
        // Extract option combination
        const optionCombination = extractOptionCombination(rawVariant);
        
        // Create variant name from option values
        let variantName = Object.values(optionCombination).join(' ');
        if (!variantName) {
          variantName = `Variant ${rawVariant.handleId.split('_v')[1] || ''}`;
        }
        
        // Create a variant object with ABSOLUTE pricing (not price adjustments)
        const variant = {
          id: uuidv4(), // Generate a new UUID for the variant
          product_id: productId,
          variant_name: variantName,
          sku: rawVariant.sku || null,
          price: parseFloat(rawVariant.price) || 0, // Use absolute price from CSV
          sale_price: null,
          stock_quantity: parseInt(rawVariant.inventory) || 0,
          in_stock: rawVariant.inventory === 'InStock' || parseInt(rawVariant.inventory) > 0,
          image: null,
          option_combination: JSON.stringify(optionCombination), // Store as JSON string
          is_active: rawVariant.visible === 'TRUE' || rawVariant.visible === true,
          external_id: rawVariant.handleId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        
        // Process variant image if available
        if (rawVariant.productImageUrl) {
          const imageUrls = rawVariant.productImageUrl.split(';').map(img => img.trim()).filter(Boolean);
          if (imageUrls.length > 0) {
            variant.image = processImageUrl(imageUrls[0]);
          }
        }
        
        // Calculate sale price if discount is available
        if (rawVariant.discountMode === 'PERCENT' && rawVariant.discountValue) {
          const discountValue = parseFloat(rawVariant.discountValue);
          const originalPrice = parseFloat(rawVariant.price);
          if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
            variant.sale_price = originalPrice * (1 - discountValue / 100);
          }
        }
        
        // Insert variant into database
        const { data, error } = await supabase
          .from('product_variants')
          .insert(variant);
        
        if (error) {
          console.error(`Error inserting variant ${variant.variant_name}:`, error);
          errorCount++;
        } else {
          successCount++;
          console.log(`Imported variant: ${variant.variant_name} (ID: ${variant.id}) for product ${productId}`);
        }
      } catch (error) {
        console.error(`Error processing variant ${rawVariant.handleId}:`, error);
        errorCount++;
      }
    }
  }
  
  console.log(`Variants import complete: ${successCount} succeeded, ${errorCount} failed`);
}

// Generate CSV files for products and variants
async function generateCsvFiles(csvData) {
  // Separate products and variants
  const productsData = csvData.filter(item => item.fieldType === 'Product');
  const variantsData = csvData.filter(item => item.fieldType === 'Choice');
  
  console.log(`Found ${productsData.length} products and ${variantsData.length} variants in the CSV`);
  
  // Process products
  const processedProducts = [];
  const productHandleMap = new Map();
  
  for (const rawProduct of productsData) {
    try {
      // Generate a UUID for the product
      const productId = uuidv4();
      productHandleMap.set(rawProduct.handleId, productId);
      
      // Process the main image and additional images
      let mainImage = '';
      let additionalImages = [];
      
      if (rawProduct.productImageUrl) {
        const imageUrls = rawProduct.productImageUrl.split(';').map(img => img.trim()).filter(Boolean);
        if (imageUrls.length > 0) {
          mainImage = processImageUrl(imageUrls[0]);
          if (imageUrls.length > 1) {
            additionalImages = processImageUrls(imageUrls.slice(1));
          }
        }
      }
      
      // Extract option definitions
      const optionDefinitions = extractOptionDefinitions(rawProduct);
      
      // Create a product object
      const product = {
        id: productId,
        name: rawProduct.name,
        slug: rawProduct.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, ''),
        description: rawProduct.description || '',
        price: parseFloat(rawProduct.price) || 0,
        sale_price: null,
        image: mainImage,
        additional_images: JSON.stringify(additionalImages),
        category_id: null,
        brand_id: null,
        sku: rawProduct.sku || null,
        stock_quantity: parseInt(rawProduct.inventory) || 0,
        weight: parseFloat(rawProduct.weight) || null,
        in_stock: rawProduct.inventory === 'InStock' || rawProduct.inventory > 0,
        is_featured: false,
        is_new: false,
        is_active: true,
        option_definitions: JSON.stringify(optionDefinitions),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      // Calculate sale price if discount is available
      if (rawProduct.discountMode === 'PERCENT' && rawProduct.discountValue) {
        const discountValue = parseFloat(rawProduct.discountValue);
        const originalPrice = parseFloat(rawProduct.price);
        if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
          product.sale_price = originalPrice * (1 - discountValue / 100);
        }
      }
      
      processedProducts.push(product);
      console.log(`Processed product: ${product.name} (ID: ${product.id})`);
    } catch (error) {
      console.error(`Error processing product ${rawProduct.name || rawProduct.handleId}:`, error);
    }
  }
  
  // Process variants
  const processedVariants = [];
  
  for (const rawVariant of variantsData) {
    try {
      // Get the product ID from the handle map
      const productHandleId = rawVariant.handleId.split('_v')[0];
      const productId = productHandleMap.get(productHandleId);
      
      // Skip variants with invalid product_id
      if (!productId) {
        console.warn(`Skipping variant ${rawVariant.handleId} with missing product_id. Product handle: ${productHandleId}`);
        continue;
      }
      
      // Extract option combination
      const optionCombination = extractOptionCombination(rawVariant);
      
      // Create variant name from option values
      let variantName = Object.values(optionCombination).join(' ');
      if (!variantName) {
        variantName = `Variant ${rawVariant.handleId.split('_v')[1] || ''}`;
      }
      
      // Process variant image if available
      let variantImage = null;
      if (rawVariant.productImageUrl) {
        const imageUrls = rawVariant.productImageUrl.split(';').map(img => img.trim()).filter(Boolean);
        if (imageUrls.length > 0) {
          variantImage = processImageUrl(imageUrls[0]);
        }
      }
      
      // Create a variant object with ABSOLUTE pricing (not price adjustments)
      const variant = {
        id: uuidv4(),
        product_id: productId,
        variant_name: variantName,
        sku: rawVariant.sku || null,
        price: parseFloat(rawVariant.price) || 0, // Use absolute price from CSV
        sale_price: null,
        stock_quantity: parseInt(rawVariant.inventory) || 0,
        in_stock: rawVariant.inventory === 'InStock' || parseInt(rawVariant.inventory) > 0,
        image: variantImage,
        option_combination: JSON.stringify(optionCombination),
        is_active: rawVariant.visible === 'TRUE' || rawVariant.visible === true,
        external_id: rawVariant.handleId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      // Calculate sale price if discount is available
      if (rawVariant.discountMode === 'PERCENT' && rawVariant.discountValue) {
        const discountValue = parseFloat(rawVariant.discountValue);
        const originalPrice = parseFloat(rawVariant.price);
        if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
          variant.sale_price = originalPrice * (1 - discountValue / 100);
        }
      }
      
      processedVariants.push(variant);
      console.log(`Processed variant: ${variant.variant_name} (ID: ${variant.id}) for product ${productId}`);
    } catch (error) {
      console.error(`Error processing variant ${rawVariant.handleId}:`, error);
    }
  }
  
  // Write products to CSV
  const productsOutputPath = path.join(__dirname, '../../data/output/products-transformed.csv');
  const productsOutput = Papa.unparse(processedProducts);
  fs.mkdirSync(path.dirname(productsOutputPath), { recursive: true });
  fs.writeFileSync(productsOutputPath, productsOutput);
  console.log(`Wrote ${processedProducts.length} products to ${productsOutputPath}`);
  
  // Write variants to CSV
  const variantsOutputPath = path.join(__dirname, '../../data/output/product_variants.csv');
  const variantsOutput = Papa.unparse(processedVariants);
  fs.writeFileSync(variantsOutputPath, variantsOutput);
  console.log(`Wrote ${processedVariants.length} variants to ${variantsOutputPath}`);
  
  return {
    productsOutputPath,
    variantsOutputPath,
    productCount: processedProducts.length,
    variantCount: processedVariants.length
  };
}

// Main function
async function main() {
  try {
    // Parse CSV files
    console.log(`Reading products and variants from ${productsPath}`);
    const csvData = await parseCsvFile(productsPath);
    
    // Generate CSV files for products and variants
    const { productsOutputPath, variantsOutputPath, productCount, variantCount } = await generateCsvFiles(csvData);
    
    console.log('\nCSV files generated successfully:');
    console.log(`- Products: ${productsOutputPath} (${productCount} products)`);
    console.log(`- Variants: ${variantsOutputPath} (${variantCount} variants)`);
    console.log('\nYou can now import these CSV files into your database.');
    
    // Uncomment these lines to also import directly to the database
    /*
    // Import products first and get the product ID maps
    const { productIdMap, productHandleMap } = await importProducts(csvData);
    
    // Then import variants with the product handle map
    await importVariants(csvData, productHandleMap);
    
    console.log('Import completed successfully');
    */
  } catch (error) {
    console.error('Error during import:', error);
  }
}

// Run the main function
main();
