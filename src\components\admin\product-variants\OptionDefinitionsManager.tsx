import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { customSupabase } from '@/integrations/supabase/customClient';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, X, ArrowUp, ArrowDown, Save } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';

interface OptionDefinition {
  name: string;
  values: string[];
  display_type?: string; // Optional display type (color, dropdown, etc.)
}

// Define the complex option definition format
interface ComplexOptionValue {
  name?: string;
  values: string[];
  display_type?: string;
}

// Define the possible formats for option definitions
type OptionDefinitionsFormat =
  | Record<string, string[]> // Simple format: {"Size": ["Small", "Medium"]}
  | Record<string, ComplexOptionValue>; // Complex format: {"Size": { values: ["Small", "Medium"], display_type: "dropdown" }}

interface OptionDefinitionsManagerProps {
  optionDefinitions: OptionDefinitionsFormat;
  onChange: (optionDefinitions: Record<string, string[]>) => void;
  productId?: string; // Optional product ID for direct saving
  showSaveButton?: boolean; // Whether to show the save button
  saveInstructions?: string; // Optional instructions to show when save button is hidden
}

export function OptionDefinitionsManager({
  optionDefinitions,
  onChange,
  productId,
  showSaveButton = true,
  saveInstructions
}: OptionDefinitionsManagerProps) {
  // State for saving
  const [isSaving, setIsSaving] = useState(false);

  // Convert from object to array format for easier manipulation in the UI
  const [options, setOptions] = useState<OptionDefinition[]>(() => {
    // Handle both simple and complex option definition formats
    return Object.entries(optionDefinitions || {}).map(([name, value]) => {
      // Check if the value is an array (simple format) or an object (complex format)
      if (Array.isArray(value)) {
        // Simple format: { "Size": ["Small", "Medium"] }
        return {
          name,
          values: value || [],
          display_type: name.toLowerCase().includes('color') ? 'color' : 'dropdown', // Default display type based on name
        };
      } else if (value && typeof value === 'object' && 'values' in value) {
        // Complex format: { "Size": { values: ["Small", "Medium"], display_type: "dropdown" } }
        return {
          name,
          values: value.values || [],
          display_type: value.display_type || (name.toLowerCase().includes('color') ? 'color' : 'dropdown'),
        };
      } else {
        // Fallback for unexpected format
        console.warn(`Unexpected option definition format for ${name}:`, value);
        return {
          name,
          values: [],
          display_type: 'dropdown',
        };
      }
    });
  });

  // Update parent component when options change
  useEffect(() => {
    // Convert from array back to object format
    const optionsObject = options.reduce((acc, option) => {
      if (option.name.trim() !== '') {
        acc[option.name] = option.values.filter(v => v.trim() !== '');
      }
      return acc;
    }, {} as Record<string, string[]>);

    console.log('Updating option definitions:', optionsObject);
    onChange(optionsObject);
  }, [options, onChange]);

  // Function to directly save option definitions to the database
  const saveOptionDefinitionsToDatabase = async () => {
    if (!productId) {
      toast({
        title: "Error",
        description: "Cannot save options: Product ID is missing",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    try {
      // Convert from array back to object format
      const optionsObject = options.reduce((acc, option) => {
        if (option.name.trim() !== '') {
          acc[option.name] = option.values.filter(v => v.trim() !== '');
        }
        return acc;
      }, {} as Record<string, string[]>);

      console.log('Saving option definitions to database:', optionsObject);

      const { data, error } = await customSupabase
        .from('products')
        .update({ option_definitions: optionsObject })
        .eq('id', productId)
        .select();

      if (error) {
        throw error;
      }

      toast({
        title: "Success",
        description: "Product options saved successfully",
      });

      console.log('Option definitions saved successfully:', data);
    } catch (error) {
      console.error('Error saving option definitions:', error);
      toast({
        title: "Error",
        description: "Failed to save product options",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const addOption = () => {
    // Add new options at the top of the list for better UX
    setOptions([{ name: '', values: [] }, ...options]);
  };

  const removeOption = (index: number) => {
    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  const updateOptionName = (index: number, name: string) => {
    const newOptions = [...options];
    newOptions[index].name = name;
    setOptions(newOptions);
  };

  const addOptionValue = (optionIndex: number) => {
    const newOptions = [...options];
    // Add new values at the top of the list for better UX
    newOptions[optionIndex].values.unshift('');
    setOptions(newOptions);
  };

  const updateOptionValue = (optionIndex: number, valueIndex: number, value: string) => {
    const newOptions = [...options];
    newOptions[optionIndex].values[valueIndex] = value;
    setOptions(newOptions);
  };

  const removeOptionValue = (optionIndex: number, valueIndex: number) => {
    const newOptions = [...options];
    newOptions[optionIndex].values.splice(valueIndex, 1);
    setOptions(newOptions);
  };

  const moveOptionValueUp = (optionIndex: number, valueIndex: number) => {
    if (valueIndex === 0) return;

    const newOptions = [...options];
    const values = [...newOptions[optionIndex].values];
    const temp = values[valueIndex];
    values[valueIndex] = values[valueIndex - 1];
    values[valueIndex - 1] = temp;

    newOptions[optionIndex].values = values;
    setOptions(newOptions);
  };

  const moveOptionValueDown = (optionIndex: number, valueIndex: number) => {
    const newOptions = [...options];
    const values = newOptions[optionIndex].values;

    if (valueIndex === values.length - 1) return;

    const temp = values[valueIndex];
    values[valueIndex] = values[valueIndex + 1];
    values[valueIndex + 1] = temp;

    setOptions(newOptions);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Product Options</CardTitle>
          <p className="text-sm text-muted-foreground">
            Define the types of options (Size, Color, etc.) for your product.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.preventDefault(); // Prevent form submission
              addOption();
            }}
            disabled={options.length >= 3}
            type="button" // Explicitly set button type to prevent form submission
            title="Add a new option type like Size, Color, Material, etc. (max 3)"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Option
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {options.length === 0 ? (
          <div className="text-center py-6 px-4 bg-gray-50 rounded-md border border-gray-200">
            <h3 className="font-medium mb-2">Getting Started with Product Options</h3>
            <p className="text-muted-foreground mb-3">Click "Add Option" to define your first option type (like Size, Color, etc.)</p>
            <div className="flex justify-center">
              <Button
                variant="default"
                onClick={(e) => {
                  e.preventDefault();
                  addOption();
                }}
                disabled={options.length >= 3}
                type="button"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Your First Option
              </Button>
            </div>
          </div>
        ) : (
          options.map((option, optionIndex) => (
            <div key={optionIndex} className="space-y-4 border rounded-md p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Label htmlFor={`option-name-${optionIndex}`}>Option Name</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      id={`option-name-${optionIndex}`}
                      value={option.name}
                      onChange={(e) => updateOptionName(optionIndex, e.target.value)}
                      placeholder="e.g. Size, Color, Material"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.preventDefault(); // Prevent form submission
                        removeOption(optionIndex);
                      }}
                      type="button" // Explicitly set button type to prevent form submission
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Option Values</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent form submission
                      addOptionValue(optionIndex);
                    }}
                    type="button" // Explicitly set button type to prevent form submission
                    title={`Add a value for the "${option.name}" option (e.g., Small, Medium, Large)`}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Value
                  </Button>
                </div>

                {option.values.length === 0 ? (
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200 text-center">
                    <p className="text-sm text-muted-foreground mb-2">
                      Add values for the <strong>{option.name}</strong> option (e.g., Small, Medium, Large)
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        addOptionValue(optionIndex);
                      }}
                      type="button"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add First Value
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {option.values.map((value, valueIndex) => (
                      <div key={valueIndex} className="flex items-center gap-2">
                        <div className="flex-1">
                          <Input
                            value={value}
                            onChange={(e) =>
                              updateOptionValue(optionIndex, valueIndex, e.target.value)
                            }
                            placeholder={`Value ${valueIndex + 1}`}
                          />
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault(); // Prevent form submission
                              moveOptionValueUp(optionIndex, valueIndex);
                            }}
                            disabled={valueIndex === 0}
                            type="button" // Explicitly set button type to prevent form submission
                          >
                            <ArrowUp className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault(); // Prevent form submission
                              moveOptionValueDown(optionIndex, valueIndex);
                            }}
                            disabled={valueIndex === option.values.length - 1}
                            type="button" // Explicitly set button type to prevent form submission
                          >
                            <ArrowDown className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault(); // Prevent form submission
                              removeOptionValue(optionIndex, valueIndex);
                            }}
                            type="button" // Explicitly set button type to prevent form submission
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Preview of option values */}
              {option.values.length > 0 && option.name && (
                <div className="mt-2">
                  <Label className="text-xs text-muted-foreground">Preview:</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {option.values
                      .filter(v => v.trim() !== '')
                      .map((value, i) => (
                        <Badge key={i} variant="outline">
                          {value}
                        </Badge>
                      ))}
                  </div>
                </div>
              )}
            </div>
          ))
        )}

        {options.length > 0 && (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>
                These options will be used to generate variants. You can create up to 3 options
                (e.g., Size, Color, Material).
              </p>
              <p className="mt-1">
                Total possible variants: {options.reduce((acc, option) => {
                  const validValues = option.values.filter(v => v.trim() !== '').length;
                  return acc * (validValues || 1);
                }, 1)}
              </p>
            </div>

            {/* Save Options Button or Instructions */}
            {showSaveButton && productId ? (
              <div className="flex justify-end">
                <Button
                  variant="secondary"
                  onClick={(e) => {
                    e.preventDefault(); // Prevent form submission
                    saveOptionDefinitionsToDatabase();
                  }}
                  disabled={isSaving}
                  type="button"
                  title="Save options directly to the database"
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving Options...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-1" />
                      Save Options
                    </>
                  )}
                </Button>
              </div>
            ) : saveInstructions ? (
              <div className="bg-blue-50 p-3 rounded-md border border-blue-200 text-center">
                <p className="text-sm text-blue-700">{saveInstructions}</p>
              </div>
            ) : null}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
