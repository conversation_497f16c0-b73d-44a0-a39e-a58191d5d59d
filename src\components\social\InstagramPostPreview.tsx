import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface InstagramPostPreviewProps {
  caption: string;
  hashtags: string[];
  images: string[];
  productName: string;
  storeName?: string;
  storeAvatar?: string;
}

const InstagramPostPreview: React.FC<InstagramPostPreviewProps> = ({
  caption,
  hashtags,
  images,
  productName,
  storeName = 'bitsnbongs',
  storeAvatar = '/logo.png',
}) => {
  // Format caption with line breaks
  const formattedCaption = caption.split('\n').map((line, i) => (
    // Use a span instead of React.Fragment to avoid issues with data attributes
    <span key={i} className="inline">
      {line}
      {i < caption.split('\n').length - 1 && <br />}
    </span>
  ));

  return (
    <Card className="max-w-md mx-auto shadow-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center p-3 border-b">
        <Avatar className="h-8 w-8 mr-2">
          <AvatarImage src={storeAvatar} alt={storeName} />
          <AvatarFallback>{storeName.substring(0, 2).toUpperCase()}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="font-semibold text-sm">{storeName}</div>
        </div>
        <div className="text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="1" />
            <circle cx="19" cy="12" r="1" />
            <circle cx="5" cy="12" r="1" />
          </svg>
        </div>
      </div>

      {/* Image Carousel */}
      <div className="relative">
        <AspectRatio ratio={1/1}>
          {images && images.length > 0 ? (
            <img
              src={images[0]}
              alt={productName}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">{productName}</span>
            </div>
          )}
        </AspectRatio>

        {/* Image Counter */}
        {images && images.length > 1 && (
          <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
            1/{images.length}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between p-3">
        <div className="flex space-x-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="22" y1="2" x2="11" y2="13" />
            <polygon points="22 2 15 22 11 13 2 9 22 2" />
          </svg>
        </div>
        <div>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" />
          </svg>
        </div>
      </div>

      {/* Likes */}
      <div className="px-3 pb-1">
        <p className="font-semibold text-sm">1,234 likes</p>
      </div>

      {/* Caption */}
      <CardContent className="pt-0">
        <div className="text-sm mb-1">
          <span className="font-semibold mr-1">{storeName}</span>
          {formattedCaption}
        </div>

        {/* Hashtags */}
        <div className="flex flex-wrap gap-1 mt-2">
          {hashtags.map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag.startsWith('#') ? tag : `#${tag}`}
            </Badge>
          ))}
        </div>

        {/* Comments */}
        <div className="text-gray-500 text-xs mt-2">
          View all 42 comments
        </div>

        {/* Time */}
        <div className="text-gray-400 text-xs mt-1">
          2 HOURS AGO
        </div>
      </CardContent>
    </Card>
  );
};

export default InstagramPostPreview;
