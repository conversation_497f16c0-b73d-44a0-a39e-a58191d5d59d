-- Add option_definitions column to products table if it doesn't exist
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS option_definitions JSONB,
ADD COLUMN IF NOT EXISTS sku TEXT,
ADD COLUMN IF NOT EXISTS stock_quantity INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS images TEXT[];

-- Create product_variants table
CREATE TABLE IF NOT EXISTS public.product_variants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  sku TEXT NOT NULL,
  price_adjustment DECIMAL NOT NULL DEFAULT 0,
  stock_quantity INTEGER NOT NULL DEFAULT 0,
  options JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(product_id, sku)
);

-- Add RLS policies
ALTER TABLE public.product_variants ENABLE ROW LEVEL SECURITY;

-- Policy: Everyone can view product variants
CREATE POLICY "Everyone can view product variants"
  ON public.product_variants
  FOR SELECT
  USING (true);

-- Policy: Only admins can insert product variants
CREATE POLICY "Only admins can insert product variants"
  ON public.product_variants
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    )
  );

-- Policy: Only admins can update product variants
CREATE POLICY "Only admins can update product variants"
  ON public.product_variants
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    )
  );

-- Policy: Only admins can delete product variants
CREATE POLICY "Only admins can delete product variants"
  ON public.product_variants
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    )
  );

-- Add trigger to update updated_at timestamp
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.product_variants
FOR EACH ROW
EXECUTE FUNCTION public.handle_updated_at();
