
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Order } from '@/types/database';
import { useAuth } from '@/hooks/auth.basic';
import { toast } from '@/components/ui/use-toast';

export function useOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    async function fetchOrders() {
      if (!user) {
        setOrders([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        // Use a simple query that doesn't join with profiles
        const { data, error } = await supabase
          .from('orders')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setOrders(data || []);
      } catch (error) {
        console.error('Error fetching orders:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your orders',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchOrders();
  }, [user]);

  return { orders, isLoading };
}

export function useOrderDetails(orderId: string | null) {
  const [orderDetails, setOrderDetails] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    async function fetchOrderDetails() {
      if (!orderId) {
        setOrderDetails(null);
        return;
      }
      
      try {
        setIsLoading(true);
        
        // Get order details without joins
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', orderId)
          .single();
          
        if (orderError) throw orderError;
        
        // Then get the order items separately
        const { data: orderItems, error: itemsError } = await supabase
          .from('order_items')
          .select('*, product:product_id(*)')
          .eq('order_id', orderId);
          
        if (itemsError) throw itemsError;
        
        // Manually combine the data
        const fullOrderDetails = {
          ...orderData,
          order_items: orderItems || []
        };
        
        setOrderDetails(fullOrderDetails);
      } catch (error) {
        console.error('Error fetching order details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load order details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchOrderDetails();
  }, [orderId]);
  
  return { orderDetails, isLoading };
}

