'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { CheckoutSteps, CheckoutStep } from '@/components/checkout/CheckoutSteps';
import { ShippingStep } from '@/components/checkout/ShippingStep';
import { PaymentStep } from '@/components/checkout/PaymentStep';
import { ReviewStep } from '@/components/checkout/ReviewStep';
import { OrderSummary } from '@/components/checkout/OrderSummary';
import { useCart } from '@/hooks/useCart';
import { useToast } from '@/components/ui/use-toast';
import { Address } from '@/hooks/useAddresses';
import { ShippingMethod } from '@/components/checkout/ShippingMethodSelector';
import { PaymentMethod } from '@/components/checkout/PaymentMethodSelector';
import { useCheckoutShipping } from '@/hooks/useShipping';

// Shipping methods are now loaded dynamically from the database

// Define payment methods
const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'card',
    name: 'Credit Card',
    description: 'Pay with credit or debit card',
    icon: 'card',
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Fast and secure checkout with PayPal',
    icon: 'paypal',
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Secure payment processing with Stripe',
    icon: 'stripe',
  },
];

export function CheckoutContainer() {
  const router = useRouter();
  const { toast } = useToast();
  const { items, subtotal, clearCart } = useCart();

  // State for checkout steps
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping');
  const [completedSteps, setCompletedSteps] = useState<CheckoutStep[]>([]);

  // State for checkout data
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [selectedShippingMethod, setSelectedShippingMethod] = useState<string>('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>(PAYMENT_METHODS[0].id);

  // Get customer country from selected address (default to UK)
  const customerCountry = selectedAddress?.country || 'United Kingdom';

  // Load dynamic shipping methods
  const { shippingMethods, isLoading: isShippingLoading } = useCheckoutShipping(customerCountry);

  // Calculate order totals
  const shippingMethod = shippingMethods.find(method => method.id === selectedShippingMethod);
  const shippingCost = shippingMethod?.price || 0;
  const taxRate = 0.2; // 20% VAT
  const taxAmount = subtotal * taxRate;
  const orderTotal = subtotal + shippingCost + taxAmount;

  // Check if cart is empty and redirect if needed
  useEffect(() => {
    if (items.length === 0) {
      toast({
        title: 'Your cart is empty',
        description: 'Please add items to your cart before checking out',
        variant: 'destructive',
      });
      router.push('/shop');
    }
  }, [items, router]);

  // Set default shipping method when shipping methods load
  useEffect(() => {
    if (shippingMethods.length > 0 && !selectedShippingMethod) {
      setSelectedShippingMethod(shippingMethods[0].id);
    }
  }, [shippingMethods, selectedShippingMethod]);

  // Handle step navigation
  const goToNextStep = () => {
    if (currentStep === 'shipping') {
      if (!selectedAddress) {
        toast({
          title: 'Shipping address required',
          description: 'Please select or add a shipping address to continue',
          variant: 'destructive',
        });
        return;
      }
      setCompletedSteps(prev => [...prev.filter(step => step !== 'shipping'), 'shipping']);
      setCurrentStep('payment');
    } else if (currentStep === 'payment') {
      setCompletedSteps(prev => [...prev.filter(step => step !== 'payment'), 'payment']);
      setCurrentStep('review');
    }
  };

  const goToPreviousStep = () => {
    if (currentStep === 'payment') {
      setCurrentStep('shipping');
    } else if (currentStep === 'review') {
      setCurrentStep('payment');
    }
  };

  // Handle order submission
  const handlePlaceOrder = async () => {
    try {
      // In a real implementation, this would call an API to create the order
      // For now, we'll just simulate a successful order

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Clear the cart
      clearCart();

      // Show success message
      toast({
        title: 'Order placed successfully!',
        description: 'Thank you for your purchase',
      });

      // Redirect to order confirmation page
      router.push('/checkout/confirmation');
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Error',
        description: 'There was a problem placing your order. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container max-w-7xl py-8">
      <h1 className="text-3xl font-bold text-center mb-6">Checkout</h1>

      <CheckoutSteps
        currentStep={currentStep}
        completedSteps={completedSteps}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          {currentStep === 'shipping' && (
            <ShippingStep
              selectedAddressId={selectedAddress?.id}
              onAddressSelect={setSelectedAddress}
              selectedShippingMethodId={selectedShippingMethod}
              shippingMethods={shippingMethods}
              onShippingMethodSelect={setSelectedShippingMethod}
              onContinue={goToNextStep}
              isShippingLoading={isShippingLoading}
            />
          )}

          {currentStep === 'payment' && (
            <PaymentStep
              paymentMethods={PAYMENT_METHODS}
              selectedPaymentMethodId={selectedPaymentMethod}
              onPaymentMethodSelect={setSelectedPaymentMethod}
              onContinue={goToNextStep}
              onBack={goToPreviousStep}
              shippingCost={shippingCost}
              taxAmount={taxAmount}
            />
          )}

          {currentStep === 'review' && (
            <ReviewStep
              address={selectedAddress}
              shippingMethod={shippingMethods.find(method => method.id === selectedShippingMethod)}
              paymentMethod={PAYMENT_METHODS.find(method => method.id === selectedPaymentMethod)}
              onPlaceOrder={handlePlaceOrder}
              onBack={goToPreviousStep}
            />
          )}
        </div>

        <div className="lg:col-span-1">
          <OrderSummary
            items={items}
            subtotal={subtotal}
            shipping={shippingCost}
            tax={taxAmount}
            total={orderTotal}
          />
        </div>
      </div>
    </div>
  );
}
