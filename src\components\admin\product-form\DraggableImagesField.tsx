import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, X, GripVertical, Trash2, ArrowUp, ArrowDown } from 'lucide-react';
import { ImageUploader } from './ImageUploader';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { cn } from '@/lib/utils';

interface DraggableImagesFieldProps {
  images: string[];
  onChange: (images: string[]) => void;
  mainImage?: string;
  onMainImageChange?: (url: string) => void;
}

interface SortableImageProps {
  url: string;
  index: number;
  isMainImage?: boolean;
  onRemove: (index: number) => void;
  onSetAsMain?: (url: string) => void;
  onMoveUp?: (index: number) => void;
  onMoveDown?: (index: number) => void;
  total: number;
}

function SortableImage({ 
  url, 
  index, 
  isMainImage, 
  onRemove, 
  onSetAsMain,
  onMoveUp,
  onMoveDown,
  total
}: SortableImageProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: url });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
  };

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className={cn(
        "relative rounded-md overflow-hidden border group",
        isMainImage ? "border-primary" : "border-gray-200",
        isDragging ? "opacity-50" : "opacity-100"
      )}
    >
      <div className="aspect-square">
        <img 
          src={url} 
          alt={`Product image ${index + 1}`} 
          className="w-full h-full object-cover"
          onError={(e) => {
            // Show a placeholder on error
            e.currentTarget.src = "https://placehold.co/200x200/e5e7eb/a1a1aa?text=Image+Error";
          }}
        />
      </div>
      
      {/* Drag handle */}
      <div 
        {...attributes} 
        {...listeners}
        className="absolute top-1 left-1 p-1 bg-white/80 rounded-md cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="h-4 w-4 text-gray-500" />
      </div>
      
      {/* Image controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-between items-center">
        <div className="flex gap-1">
          {/* Move up button */}
          {index > 0 && onMoveUp && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-6 w-6 bg-white/20 hover:bg-white/40"
              onClick={() => onMoveUp(index)}
            >
              <ArrowUp className="h-3 w-3 text-white" />
            </Button>
          )}
          
          {/* Move down button */}
          {index < total - 1 && onMoveDown && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-6 w-6 bg-white/20 hover:bg-white/40"
              onClick={() => onMoveDown(index)}
            >
              <ArrowDown className="h-3 w-3 text-white" />
            </Button>
          )}
        </div>
        
        <div className="flex gap-1">
          {/* Set as main image button */}
          {!isMainImage && onSetAsMain && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs bg-white/20 hover:bg-white/40 text-white"
              onClick={() => onSetAsMain(url)}
            >
              Set as Main
            </Button>
          )}
          
          {/* Delete button */}
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
            onClick={() => onRemove(index)}
          >
            <Trash2 className="h-3 w-3 text-white" />
          </Button>
        </div>
      </div>
      
      {/* Main image indicator */}
      {isMainImage && (
        <div className="absolute top-1 right-1 bg-primary text-white text-xs px-2 py-0.5 rounded-sm">
          Main
        </div>
      )}
    </div>
  );
}

export function DraggableImagesField({ 
  images, 
  onChange, 
  mainImage,
  onMainImageChange 
}: DraggableImagesFieldProps) {
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addImage = (url: string) => {
    const newImages = [...images, url];
    onChange(newImages);
    
    // If this is the first image and there's no main image set, make it the main image
    if (images.length === 0 && onMainImageChange && !mainImage) {
      onMainImageChange(url);
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    const removedUrl = newImages[index];
    newImages.splice(index, 1);
    onChange(newImages);
    
    // If we're removing the main image, set the first available image as main
    if (mainImage === removedUrl && onMainImageChange && newImages.length > 0) {
      onMainImageChange(newImages[0]);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = images.indexOf(active.id as string);
      const newIndex = images.indexOf(over.id as string);
      
      const newImages = arrayMove(images, oldIndex, newIndex);
      onChange(newImages);
    }
  };

  const handleAddImageUrl = () => {
    if (imageUrl.trim()) {
      addImage(imageUrl.trim());
      setImageUrl('');
      setShowUrlInput(false);
    }
  };

  const handleMoveUp = (index: number) => {
    if (index > 0) {
      const newImages = [...images];
      [newImages[index - 1], newImages[index]] = [newImages[index], newImages[index - 1]];
      onChange(newImages);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index < images.length - 1) {
      const newImages = [...images];
      [newImages[index], newImages[index + 1]] = [newImages[index + 1], newImages[index]];
      onChange(newImages);
    }
  };

  return (
    <div className="space-y-2 md:col-span-2">
      <Label htmlFor="additional_images">Product Images</Label>
      <div className="space-y-4">
        {/* Image grid */}
        {images.length > 0 && (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={images}
              strategy={verticalListSortingStrategy}
            >
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                {images.map((img, index) => (
                  <SortableImage
                    key={img}
                    url={img}
                    index={index}
                    isMainImage={mainImage === img}
                    onRemove={removeImage}
                    onSetAsMain={onMainImageChange}
                    onMoveUp={handleMoveUp}
                    onMoveDown={handleMoveDown}
                    total={images.length}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
        
        {/* Upload controls */}
        <div className="flex flex-wrap gap-2">
          <ImageUploader 
            onImageUploaded={addImage} 
            buttonText="Upload New Image"
          />
          
          {showUrlInput ? (
            <div className="flex gap-2 items-center">
              <input
                type="text"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="Enter image URL"
                className="px-3 py-1 border rounded-md text-sm"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddImageUrl}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowUrlInput(false)}
              >
                Cancel
              </Button>
            </div>
          ) : (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowUrlInput(true)}
            >
              <Plus className="mr-1 h-4 w-4" />
              Add Image URL
            </Button>
          )}
        </div>
      </div>
      <p className="text-xs text-gray-500">
        Drag images to reorder. The main image will be displayed as the product thumbnail.
      </p>
    </div>
  );
}
