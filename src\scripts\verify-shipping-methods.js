// <PERSON>ript to verify and fix shipping methods in the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyShippingMethods() {
  console.log('🔍 Verifying shipping methods...');
  
  // 1. Get all shipping methods
  const { data: methods, error } = await supabase
    .from('shipping_methods')
    .select('*');
    
  if (error) {
    console.error('Error fetching shipping methods:', error);
    return;
  }
  
  console.log(`Found ${methods.length} total shipping methods`);
  
  // 2. Log active vs inactive methods
  const activeMethods = methods.filter(m => m.is_active);
  const inactiveMethods = methods.filter(m => !m.is_active);
  
  console.log('\nActive methods:');
  activeMethods.forEach(m => {
    console.log(`- ${m.name} (ID: ${m.id})`);
  });
  
  console.log('\nInactive methods:');
  inactiveMethods.forEach(m => {
    console.log(`- ${m.name} (ID: ${m.id})`);
  });
  
  // 3. Verify the Next Day Delivery method
  const nextDayMethod = methods.find(m => m.name === 'Next Day Delivery');
  if (nextDayMethod) {
    console.log('\nNext Day Delivery method:');
    console.log(`- ID: ${nextDayMethod.id}`);
    console.log(`- Active: ${nextDayMethod.is_active}`);
    console.log(`- Zone ID: ${nextDayMethod.zone_id}`);
    
    if (!nextDayMethod.is_active) {
      console.log('✅ Next Day Delivery is correctly marked as inactive');
    } else {
      console.log('⚠️ Next Day Delivery is marked as active');
      
      // Ask if we should deactivate it
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      readline.question('Do you want to deactivate the Next Day Delivery method? (y/n) ', async (answer) => {
        if (answer.toLowerCase() === 'y') {
          const { error: updateError } = await supabase
            .from('shipping_methods')
            .update({ is_active: false })
            .eq('id', nextDayMethod.id);
            
          if (updateError) {
            console.error('Error updating Next Day Delivery:', updateError);
          } else {
            console.log('✅ Next Day Delivery has been deactivated');
          }
        }
        
        readline.close();
      });
    }
  } else {
    console.log('\n❌ Next Day Delivery method not found');
  }
  
  // 4. Check for any caching issues
  console.log('\nChecking for potential caching issues...');
  console.log('- Make sure to clear your browser cache');
  console.log('- Check if there are any hardcoded shipping methods in the code');
  console.log('- Verify that the checkout page is using the latest data from the database');
}

// Run the verification
verifyShippingMethods()
  .catch(err => {
    console.error('Error running verification:', err);
  });
