import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";

// Define the EPOS product interface based on expected EPOS system structure
interface EPOSProduct {
  id: string;          // EPOS system's internal ID
  sku: string;         // Stock Keeping Unit
  name: string;        // Product name
  price: number;       // Current price
  stock_quantity: number; // Available stock
  // Add other fields as needed based on the EPOS API
}

// Configuration for EPOS API
interface EPOSConfig {
  apiUrl: string;
  apiKey: string;
  refreshInterval: number; // in milliseconds
}

/**
 * Service to handle integration with external EPOS (Electronic Point of Sale) system
 */
export class EPOSIntegrationService {
  private config: EPOSConfig;
  private syncInterval: NodeJS.Timeout | null = null;
  
  constructor(config: EPOSConfig) {
    this.config = config;
  }

  /**
   * Initialize the EPOS integration service
   */
  public initialize(): void {
    console.log("Initializing EPOS integration service");
    // Start periodic sync if configured
    if (this.config.refreshInterval > 0) {
      this.startPeriodicSync();
    }
  }

  /**
   * Start periodic synchronization with EPOS system
   */
  public startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.syncInterval = setInterval(
      () => this.syncProductsFromEPOS(),
      this.config.refreshInterval
    );
    
    console.log(`EPOS sync scheduled every ${this.config.refreshInterval / 1000} seconds`);
  }

  /**
   * Stop periodic synchronization
   */
  public stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log("EPOS sync stopped");
    }
  }

  /**
   * Fetch products from EPOS system
   * This is a placeholder that should be implemented based on the actual EPOS API
   */
  private async fetchProductsFromEPOS(): Promise<EPOSProduct[]> {
    try {
      // This is a placeholder - replace with actual API call to EPOS system
      // Example:
      // const response = await fetch(`${this.config.apiUrl}/products`, {
      //   headers: {
      //     'Authorization': `Bearer ${this.config.apiKey}`,
      //     'Content-Type': 'application/json'
      //   }
      // });
      // 
      // if (!response.ok) {
      //   throw new Error(`EPOS API error: ${response.status} ${response.statusText}`);
      // }
      // 
      // return await response.json();
      
      console.log("Fetching products from EPOS system (placeholder)");
      return [];
    } catch (error) {
      console.error("Error fetching products from EPOS:", error);
      throw error;
    }
  }

  /**
   * Synchronize products from EPOS to our database
   */
  public async syncProductsFromEPOS(): Promise<void> {
    try {
      console.log("Starting EPOS product synchronization");
      
      // 1. Fetch products from EPOS
      const eposProducts = await this.fetchProductsFromEPOS();
      console.log(`Retrieved ${eposProducts.length} products from EPOS`);
      
      // 2. Update each product in our database
      for (const eposProduct of eposProducts) {
        await this.updateProductFromEPOS(eposProduct);
      }
      
      console.log("EPOS synchronization completed successfully");
    } catch (error) {
      console.error("EPOS synchronization failed:", error);
    }
  }

  /**
   * Update a single product from EPOS data
   */
  private async updateProductFromEPOS(eposProduct: EPOSProduct): Promise<void> {
    try {
      // First try to find product by external_id
      let { data: existingProduct, error: lookupError } = await supabase
        .from("products")
        .select("*")
        .eq("external_id", eposProduct.id)
        .maybeSingle();
      
      if (lookupError) {
        throw lookupError;
      }
      
      // If not found by external_id, try by SKU
      if (!existingProduct && eposProduct.sku) {
        const { data: productBySku, error: skuLookupError } = await supabase
          .from("products")
          .select("*")
          .eq("sku", eposProduct.sku)
          .maybeSingle();
          
        if (skuLookupError) {
          throw skuLookupError;
        }
        
        existingProduct = productBySku;
      }
      
      const now = new Date().toISOString();
      
      if (existingProduct) {
        // Update existing product
        const { error: updateError } = await supabase
          .from("products")
          .update({
            // Only update fields that should be managed by EPOS
            price: eposProduct.price,
            stock_quantity: eposProduct.stock_quantity,
            in_stock: eposProduct.stock_quantity > 0,
            external_id: eposProduct.id,
            sku: eposProduct.sku || existingProduct.sku, // Keep existing SKU if EPOS doesn't provide one
            last_synced_at: now,
            sync_status: "synced",
            external_stock_quantity: eposProduct.stock_quantity
          })
          .eq("id", existingProduct.id);
          
        if (updateError) {
          throw updateError;
        }
        
        console.log(`Updated product ${existingProduct.name} from EPOS data`);
      } else {
        // Product doesn't exist in our system yet
        // You may want to create it or log it for manual review
        console.log(`Product with EPOS ID ${eposProduct.id} not found in database`);
      }
    } catch (error) {
      console.error(`Error updating product from EPOS (${eposProduct.id}):`, error);
    }
  }

  /**
   * Check if a product's stock needs to be updated based on EPOS data
   */
  public async checkProductStock(productId: string): Promise<number> {
    try {
      // Get the product from our database
      const { data: product, error } = await supabase
        .from("products")
        .select("*")
        .eq("id", productId)
        .single();
        
      if (error) throw error;
      
      // If the product has an external_id, we could fetch the latest stock
      // from the EPOS system here for real-time accuracy
      // For now, we'll just return the stored value
      
      return product.stock_quantity || 0;
    } catch (error) {
      console.error("Error checking product stock:", error);
      return 0; // Default to 0 if there's an error
    }
  }

  /**
   * Map products between our system and EPOS system
   * This is useful for initial setup or remapping
   */
  public async mapProductsToEPOS(mappings: { productId: string, eposId: string, sku: string }[]): Promise<void> {
    try {
      for (const mapping of mappings) {
        const { error } = await supabase
          .from("products")
          .update({
            external_id: mapping.eposId,
            sku: mapping.sku,
            sync_status: "mapped",
            last_synced_at: new Date().toISOString()
          })
          .eq("id", mapping.productId);
          
        if (error) throw error;
      }
      
      console.log(`Successfully mapped ${mappings.length} products to EPOS system`);
    } catch (error) {
      console.error("Error mapping products to EPOS:", error);
      throw error;
    }
  }
}

// Create a singleton instance with default config
// This will be replaced with actual config when available
let eposService: EPOSIntegrationService | null = null;

/**
 * Get the EPOS integration service instance
 */
export function getEPOSService(): EPOSIntegrationService {
  if (!eposService) {
    // Default config - should be loaded from environment variables in production
    const config: EPOSConfig = {
      apiUrl: import.meta.env.VITE_EPOS_API_URL || '',
      apiKey: import.meta.env.VITE_EPOS_API_KEY || '',
      refreshInterval: parseInt(import.meta.env.VITE_EPOS_REFRESH_INTERVAL || '300000') // Default 5 minutes
    };
    
    eposService = new EPOSIntegrationService(config);
  }
  
  return eposService;
}