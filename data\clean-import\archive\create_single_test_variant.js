const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');
const { v4: uuidv4 } = require('uuid');

// Input files
const productsFile = 'transformed_products.csv';
const outputFile = 'single_test_variant.csv';

// Store products
const products = [];

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Read products file
console.log(`Reading products from ${productsFile}...`);
fs.createReadStream(productsFile)
  .pipe(csv())
  .on('data', (row) => {
    products.push(row);
  })
  .on('end', async () => {
    console.log(`Found ${products.length} products`);
    
    // Select a product
    const product = products[0];
    console.log(`Selected product: ${product.name} (${product.id})`);
    
    // Create a test variant
    const testVariant = {
      id: uuidv4(),
      product_id: product.id,
      variant_name: 'Test Variant',
      sku: '',
      price: product.price,
      sale_price: '',
      stock_quantity: '100',
      in_stock: 'true',
      image: '',
      option_combination: JSON.stringify({ 'Test Option': 'Test Value' }),
      is_active: 'true',
      created_at: new Date().toISOString().replace('Z', ''),
      updated_at: new Date().toISOString().replace('Z', '')
    };
    
    // Write test variant to CSV
    console.log(`Writing test variant to ${outputFile}...`);
    await csvWriter.writeRecords([testVariant]);
    console.log(`Created test variant file: ${outputFile}`);
    
    // Print the test variant
    console.log('\nTest variant:');
    console.log(`- id: ${testVariant.id}, product_id: ${testVariant.product_id}, variant_name: ${testVariant.variant_name}, price: ${testVariant.price}`);
  });
