import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import ProductCard from './ProductCard';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ProductGridSimpleProps {
  categoryId?: string;
  subcategoryId?: string;
  brandId?: string;
  searchQuery?: string;
  initialPageSize?: number;
  showAdminFilters?: boolean;
  seedFilters?: Record<string, string[]>;
}

const ProductGridSimple: React.FC<ProductGridSimpleProps> = ({
  categoryId,
  subcategoryId,
  brandId,
  searchQuery,
  initialPageSize = 50,
  showAdminFilters = false,
  seedFilters
}) => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [hideAssigned, setHideAssigned] = useState(false);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const pageSize = initialPageSize;

  // Function to verify if a category exists
  const verifyCategoryExists = async (categoryId: string) => {
    if (!categoryId || categoryId === 'all') return false;

    try {
      // Check if the category exists in the database
      const { data, error } = await supabase
        .from('categories')
        .select('id')
        .eq('id', categoryId)
        .single();

      if (error) {
        console.error('Error verifying category:', error);
        return false;
      }

      return !!data; // Return true if data exists, false otherwise
    } catch (error) {
      console.error('Exception verifying category:', error);
      return false;
    }
  };

  // Function to fetch products
  const fetchProducts = async (page = 0, append = false, overrideCategoryId?: string) => {
    // NOTE: Seed filtering integration approach
    // The seed filtering system uses a sophisticated backend with:
    // - filter_categories table for filter types (seed_type, flowering_time, etc.)
    // - filter_options table for specific options within each category
    // - product_filters table for product-to-filter relationships
    // - get_seed_products RPC function for backend filtering
    //
    // This ensures consistency with the admin filtering system and avoids
    // bypassing important category validation logic.
    try {
      console.log('Fetching products with params:', {
        categoryId,
        subcategoryId,
        brandId,
        searchQuery,
        seedFilters,
        page,
        append
      });

      if (page === 0 && !append) {
        setLoading(true);
        // Clear existing products to prevent showing incorrect items
        setProducts([]);
        setFilteredProducts([]);
      } else {
        setLoadingMore(true);
      }

      // Calculate range for pagination
      const from = page * pageSize;
      const to = from + pageSize - 1;

      // Build a simple query to avoid relationship issues - ONLY show active products
      let query = supabase
        .from('products')
        .select('*', { count: 'exact' })
        .eq('is_active', true)  // Always filter to active products only
        .order('name', { ascending: true })
        .range(from, to);

      // Apply filters - use override if provided
      const effectiveCategoryId = overrideCategoryId || categoryId;

      console.log('Effective category ID for filtering:', effectiveCategoryId);

      // If we have a specific category ID, verify it exists before filtering
      if (effectiveCategoryId && effectiveCategoryId !== 'all') {
        console.log(`Checking if category ID exists: ${effectiveCategoryId}`);

        // First check if the category exists
        const categoryExists = await verifyCategoryExists(effectiveCategoryId);

        if (categoryExists) {
          console.log(`Category ID ${effectiveCategoryId} exists, applying filter`);
          // Make sure we're using the exact category ID for filtering
          query = query.eq('category_id', effectiveCategoryId);

          // Log the exact query being used
          console.log('Using exact category ID for filtering:', effectiveCategoryId);

          // Add a debug query to verify products exist for this category
          const debugQuery = async () => {
            const { data, error } = await supabase
              .from('products')
              .select('id, name, category_id')
              .eq('category_id', effectiveCategoryId)
              .limit(5);

            console.log(`Debug: Found ${data?.length || 0} products for category ID ${effectiveCategoryId}:`, data, error);
          };

          // Execute the debug query
          debugQuery();
        } else {
          console.log(`Category ID ${effectiveCategoryId} does not exist, showing all products`);
          // Don't apply any category filter if the category doesn't exist
          // This will show all products instead of no products

          // Find products with invalid category ID and include them in results
          const handleInvalidCategoryProducts = async () => {
            // First, get products with the invalid category ID
            const { data, error } = await supabase
              .from('products')
              .select('*')
              .eq('category_id', effectiveCategoryId)
              .order('name', { ascending: true })
              .range(from, to);

            if (data && data.length > 0) {
              console.log(`Found ${data.length} products with invalid category ID ${effectiveCategoryId}:`, data);
              console.log('Including these products in the results');

              // Add these products to the results
              const count = data.length;
              setTotalCount(count);

              if (append) {
                setProducts(prevProducts => [...prevProducts, ...data]);
              } else {
                setProducts(data);
              }

              setFilteredProducts(data);
              setLoading(false);
              setLoadingMore(false);

              // Return true to indicate we've handled the products
              return true;
            }

            // Return false to indicate no products were found
            return false;
          };

          // Execute the handler for invalid categories
          const handled = await handleInvalidCategoryProducts();

          // If products were handled, return early
          if (handled) return;
        }
      } else {
        // If no category filter, log this
        console.log('No category filter applied - showing all products');
      }

      if (subcategoryId && subcategoryId !== 'all') {
        console.log(`Filtering by subcategory ID: ${subcategoryId}`);
        query = query.eq('subcategory_id', subcategoryId);
      }

      if (brandId && brandId !== 'all') {
        if (brandId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          // If it's a UUID, use it directly
          query = query.eq('brand_id', brandId);
        } else {
          // Handle non-UUID brand IDs (legacy support)
          console.log(`Filtering by brand name/slug: ${brandId}`);
        }
      }

      if (searchQuery) {
        query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      // Execute the query
      const { data: initialData, error, count } = await query;

      if (error) {
        console.error('Error fetching products:', error);
        setError(error.message);
        setLoading(false);
        setLoadingMore(false);
        return;
      }

      // Create a mutable copy of the data
      let filteredData = [...(initialData || [])];
      console.log(`Fetched ${filteredData?.length || 0} products from database (page ${page}, pageSize: ${pageSize})`);

      // Apply seed filtering if we're in the seed category and have filters
      const isSeedsCategory =
        categoryId === '9606c3d0-e84e-450f-a09a-53f6420bfd58' ||
        categoryId === 'cat-2'; // Keep legacy support

      // Check if there are any actual filter values (not just empty arrays)
      const hasActiveFilters = seedFilters && Object.values(seedFilters).some(filterArray => filterArray && filterArray.length > 0);

      if (isSeedsCategory && hasActiveFilters) {
        console.log('Seed category detected with filters - using backend filtering');
        console.log('Available filters:', seedFilters);
        console.log('Products before filtering:', filteredData.length);

        // Use backend filtering instead of client-side filtering
        // This ensures we use the proper product_filters relationship table
        try {
          // Build filter options array for the backend
          const filterOptionNames: string[] = [];
          Object.entries(seedFilters).forEach(([filterKey, filterValues]) => {
            if (filterValues && filterValues.length > 0) {
              filterOptionNames.push(...filterValues);
            }
          });

          if (filterOptionNames.length > 0) {
            console.log('Applying backend filters with option names:', filterOptionNames);

            // First, resolve filter option names to IDs for the current category only
            // This prevents duplicate filter options from other categories
            const { data: filterOptionsData, error: optionsError } = await supabase
              .from('filter_options')
              .select('id, name, category_id')
              .in('name', filterOptionNames)
              .gt('product_count', 0); // Only get filter options that actually have products

            if (optionsError) {
              console.error('Error fetching filter option IDs:', optionsError);
            } else if (filterOptionsData && filterOptionsData.length > 0) {
              const filterOptionIds = filterOptionsData.map(option => option.id);
              console.log('Resolved filter option IDs:', filterOptionIds);

              // Use the backend RPC function for proper filtering with UUIDs
              const { data: backendFilteredData, error: filterError } = await supabase.rpc('get_filtered_seed_products', {
                p_category_id: categoryId,
                p_filter_options: filterOptionIds,
                p_active_only: true
              });

              if (filterError) {
                console.error('Backend filtering error:', filterError);
                // Fall back to showing all products if filtering fails
              } else if (backendFilteredData) {
                console.log(`Backend filtering returned ${backendFilteredData.length} products`);
                filteredData = backendFilteredData;
              }
            } else {
              console.log('No matching filter options found for:', filterOptionNames);
            }
          }
        } catch (filterError) {
          console.error('Exception in backend filtering:', filterError);
          // Continue with unfiltered data if backend filtering fails
        }

        console.log(`Applied seed filters: ${initialData?.length || 0} -> ${filteredData.length} products`);
      }

      // Special handling for RAW brand
      if (brandId && (brandId === '4b2c5e4d-5c6e-4718-a4d4-7a25e0ca0f16' ||
                      brandId === '9628468d-6311-4a3f-b4bb-6b2821f5d50a' ||
                      brandId === 'raw' ||
                      brandId === 'r' ||
                      brandId === 'Raw')) {
        console.log('DEBUG: Query results for RAW brand:', filteredData.length);
        console.log('DEBUG: First 5 products:', filteredData.slice(0, 5).map(p => ({ id: p.id, name: p.name, brand_id: p.brand_id })));

        // Check how many products actually have the RAW brand_id
        const rawBrandId = '9628468d-6311-4a3f-b4bb-6b2821f5d50a'; // Use the correct UUID from the console output
        const rawBrandProducts = filteredData.filter(p => p.brand_id === rawBrandId);
        console.log(`DEBUG: Products with RAW brand_id: ${rawBrandProducts.length} out of ${filteredData.length}`);

        // Filter the data to only include products with the RAW brand_id
        if (rawBrandProducts.length > 0) {
          console.log('Filtering to only show RAW brand products');
          filteredData = rawBrandProducts;
        } else {
          console.log('No RAW brand products found in the results');
        }
      }

      // Update products state with a small delay to ensure stability
      setTimeout(() => {
        if (append && page > 0) {
          setProducts(prev => [...prev, ...filteredData]);
        } else {
          setProducts(filteredData);
        }
      }, 10);

      // Set total count
      if (count !== null) {
        setTotalCount(count);
      }

      // Check if there are more products to load
      if (count !== null) {
        setHasMore(from + pageSize < count);
      } else {
        setHasMore(filteredData && filteredData.length === pageSize);
      }

      setLoading(false);
      setLoadingMore(false);
    } catch (err) {
      console.error('Exception in product grid:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more products
  const handleLoadMore = () => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchProducts(nextPage, true);
  };

  // Fetch products when filters change
  useEffect(() => {
    // Apply any additional filtering
    if (products.length > 0) {
      let filtered = [...products];

      // Apply admin filters if enabled
      if (showAdminFilters && hideAssigned) {
        filtered = filtered.filter(product => !product.category_id);
      }

      // Log the first few products to help debug
      console.log('First few products:', filtered.slice(0, 3));

      setFilteredProducts(filtered);
    }
  }, [products, hideAssigned, showAdminFilters]);

  // Initial data fetch
  useEffect(() => {
    fetchProducts();
  }, []);

  // Check URL parameters directly to handle direct navigation
  useEffect(() => {
    // Get URL parameters directly
    const urlParams = new URLSearchParams(window.location.search);
    const categoryIdParam = urlParams.get('category_id');
    const categoryParam = urlParams.get('category');

    // If we have a direct category_id parameter, use it immediately
    if (categoryIdParam && categoryIdParam !== categoryId) {
      console.log('Direct category_id parameter found:', categoryIdParam);
      console.log('Current categoryId state:', categoryId);

      // Force a product refresh with the category ID from URL
      setProducts([]);
      setFilteredProducts([]);
      setLoading(true);
      fetchProducts(0, false, categoryIdParam);
    }
    // If we only have a category slug, try to find the matching category ID
    else if (categoryParam && !categoryIdParam) {
      console.log('Only category slug found, trying to find matching category ID');

      // Make a direct query to get the category ID
      const fetchCategoryId = async () => {
        // Try multiple approaches to find the category
        console.log('Searching for category with slug:', categoryParam);

        // First try exact match
        const { data: exactMatch, error: exactError } = await supabase
          .from('categories')
          .select('id, slug, name')
          .eq('slug', categoryParam);

        if (exactMatch && exactMatch.length > 0) {
          console.log('Found exact match:', exactMatch[0]);
          setProducts([]);
          setFilteredProducts([]);
          setLoading(true);
          fetchProducts(0, false, exactMatch[0].id);
          return;
        }

        // Try case-insensitive match
        const { data: caseMatch, error: caseError } = await supabase
          .from('categories')
          .select('id, slug, name')
          .ilike('slug', categoryParam);

        if (caseMatch && caseMatch.length > 0) {
          console.log('Found case-insensitive match:', caseMatch[0]);
          setProducts([]);
          setFilteredProducts([]);
          setLoading(true);
          fetchProducts(0, false, caseMatch[0].id);
          return;
        }

        // Try name match
        const { data: nameMatch, error: nameError } = await supabase
          .from('categories')
          .select('id, slug, name')
          .ilike('name', categoryParam);

        if (nameMatch && nameMatch.length > 0) {
          console.log('Found name match:', nameMatch[0]);
          setProducts([]);
          setFilteredProducts([]);
          setLoading(true);
          fetchProducts(0, false, nameMatch[0].id);
          return;
        }

        console.log('No matches found for category:', categoryParam);
      };

      fetchCategoryId();
    }
  }, []);

  // Listen for force-product-refresh event
  useEffect(() => {
    const handleForceRefresh = (event: any) => {
      console.log('Received force-product-refresh event with data:', event.detail);

      // If we have a categoryId in the event, use it
      if (event.detail?.categoryId && event.detail.categoryId !== categoryId) {
        console.log('Forcing refresh with new category ID:', event.detail.categoryId);
        // Directly fetch products with the new category ID
        setProducts([]);
        setFilteredProducts([]);
        setLoading(true);
        fetchProducts(0, false, event.detail.categoryId);
      } else {
        // Otherwise just refresh with current parameters
        console.log('Forcing refresh with current parameters');
        setProducts([]);
        setFilteredProducts([]);
        setLoading(true);
        fetchProducts(0, false);
      }
    };

    // Add event listener
    window.addEventListener('force-product-refresh', handleForceRefresh);

    // Clean up
    return () => {
      window.removeEventListener('force-product-refresh', handleForceRefresh);
    };
  }, [categoryId, subcategoryId, brandId]);

  // Fetch products when filters change (consolidated effect)
  useEffect(() => {
    // Add a longer delay to prevent rapid successive calls and infinite loops
    const timeoutId = setTimeout(() => {
      console.log('Filters changed - reloading products', {
        categoryId,
        subcategoryId,
        brandId,
        searchQuery,
        seedFilters: seedFilters ? Object.keys(seedFilters).length : 'none'
      });

      // Reset to first page and clear existing products
      setCurrentPage(0);
      setProducts([]);
      setFilteredProducts([]);
      setHasMore(true);
      setLoading(true);

      // Fetch products with current parameters
      fetchProducts(0, false);
    }, 100); // Reduced delay for faster category switching

    return () => clearTimeout(timeoutId);
  }, [categoryId, subcategoryId, brandId, searchQuery, JSON.stringify(seedFilters)]);

  return (
    <div className="py-4 min-h-[300px] relative">
      {/* Admin Filters */}
      {showAdminFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={hideAssigned}
                onChange={() => setHideAssigned(!hideAssigned)}
                className="mr-2 h-4 w-4 rounded border-gray-300 text-sage-600 focus:ring-sage-500"
              />
              <span>Hide products already assigned to categories</span>
            </label>
            {hideAssigned && (
              <span className="ml-4 text-sm text-gray-500">
                Showing {filteredProducts.length} of {products.length} products
              </span>
            )}
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
          <p className="mt-2">Loading products...</p>
        </div>
      )}

      {/* Error Message */}
      {error && !loading && (
        <div className="text-center py-10 bg-red-50 rounded-lg">
          <h3 className="text-xl font-medium text-red-900">Error loading products</h3>
          <p className="mt-2 text-red-500">{error}</p>
        </div>
      )}

      {/* No Products Message */}
      {(hideAssigned ? filteredProducts.length === 0 : products.length === 0) && !loading && !error && (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p>No products found</p>
        </div>
      )}

      {/* Products Grid */}
      {!loading && !error && (hideAssigned ? filteredProducts.length > 0 : products.length > 0) && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {(hideAssigned ? filteredProducts : products).map((product, index) => (
              <ProductCard
                key={`${product.id}-${product.name}-${index}`}
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  description: product.description,
                  category_id: product.category_id,
                  subcategory_id: product.subcategory_id,
                  brand_id: product.brand_id,
                  slug: product.slug,
                  in_stock: product.in_stock ?? true,
                  is_featured: product.is_featured ?? false,
                  is_new: product.is_new ?? false,
                  is_best_seller: product.is_best_seller ?? false,
                  sale_price: product.sale_price,
                  image: product.image,
                  created_at: product.created_at || new Date().toISOString(),
                  updated_at: product.updated_at || new Date().toISOString(),
                  rating: product.rating ?? 0,
                  review_count: product.review_count ?? 0
                }}
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={handleLoadMore}
                disabled={loadingMore}
                className="min-w-[200px]"
                variant="outline"
              >
                {loadingMore ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More Products'
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProductGridSimple;
