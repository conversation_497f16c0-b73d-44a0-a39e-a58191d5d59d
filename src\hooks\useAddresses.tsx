import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/auth.basic';

export interface Address {
  id: string;
  user_id: string;
  full_name: string;
  street: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
  phone: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface AddressInput {
  full_name: string;
  street: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
  phone: string;
  is_default?: boolean;
}

export function useAddresses() {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [defaultAddress, setDefaultAddress] = useState<Address | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchAddresses = async () => {
    if (!user) {
      setAddresses([]);
      setDefaultAddress(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('addresses')
        .select('*')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      const addressList = data || [];
      setAddresses(addressList);

      // Set default address
      const defaultAddr = addressList.find(addr => addr.is_default);
      setDefaultAddress(defaultAddr || null);
    } catch (err) {
      console.error('Error fetching addresses:', err);
      setError('Failed to load addresses');
      toast({
        title: 'Error',
        description: 'Failed to load your addresses',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new address
  const addAddress = async (addressData: AddressInput): Promise<Address | null> => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add an address',
        variant: 'destructive',
      });
      return null;
    }

    setIsLoading(true);
    try {
      console.log('Current user ID:', user.id);
      console.log('Address data to save:', { ...addressData, user_id: user.id });
      
      // If this is the default address, update all other addresses to not be default
      if (addressData.is_default) {
        try {
          const { error: updateError } = await supabase
            .from('addresses')
            .update({ is_default: false })
            .eq('user_id', user.id);
            
          if (updateError) {
            console.error('Error updating existing addresses:', updateError);
          }
        } catch (updateErr) {
          console.error('Exception updating addresses:', updateErr);
        }
      }

      // Add the new address with a direct approach
      const { data, error } = await supabase
        .from('addresses')
        .insert({
          user_id: user.id,
          full_name: addressData.full_name,
          street: addressData.street,
          city: addressData.city,
          state: addressData.state || null,
          postal_code: addressData.postal_code,
          country: addressData.country,
          phone: addressData.phone,
          is_default: addressData.is_default || false
        })
        .select()
        .single();

      if (error) {
        console.error('Error inserting address:', error);
        throw error;
      }

      // Update the local state
      const newAddress = data as Address;

      if (newAddress.is_default) {
        // If this is the new default, update local state
        setAddresses(prev =>
          prev.map(addr => ({ ...addr, is_default: false })).concat(newAddress)
        );
        setDefaultAddress(newAddress);
      } else {
        setAddresses(prev => [newAddress, ...prev]);
      }

      toast({
        title: 'Success',
        description: 'Address added successfully',
      });

      return newAddress;
    } catch (err) {
      console.error('Error adding address:', err);
      setError('Failed to add address');
      toast({
        title: 'Error',
        description: 'Failed to add your address',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update an existing address
  const updateAddress = async (id: string, addressData: Partial<AddressInput>): Promise<boolean> => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to update an address',
        variant: 'destructive',
      });
      return false;
    }

    setIsLoading(true);
    try {
      // If setting as default, update all other addresses to not be default
      if (addressData.is_default) {
        await supabase
          .from('addresses')
          .update({ is_default: false })
          .eq('user_id', user.id);
      }

      // Update the address
      const { data, error } = await supabase
        .from('addresses')
        .update(addressData)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update the local state
      const updatedAddress = data as Address;

      if (updatedAddress.is_default) {
        // If this is the new default, update all addresses
        setAddresses(prev =>
          prev.map(addr =>
            addr.id === id
              ? updatedAddress
              : { ...addr, is_default: false }
          )
        );
        setDefaultAddress(updatedAddress);
      } else {
        setAddresses(prev =>
          prev.map(addr => addr.id === id ? updatedAddress : addr)
        );

        // If this was the default and is no longer, clear default
        if (defaultAddress?.id === id) {
          setDefaultAddress(null);
        }
      }

      toast({
        title: 'Success',
        description: 'Address updated successfully',
      });

      return true;
    } catch (err) {
      console.error('Error updating address:', err);
      setError('Failed to update address');
      toast({
        title: 'Error',
        description: 'Failed to update your address',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete an address
  const deleteAddress = async (id: string) => {
    if (!user) return false;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('addresses')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update local state
      const isRemovingDefault = defaultAddress?.id === id;
      const filteredAddresses = addresses.filter(addr => addr.id !== id);

      setAddresses(filteredAddresses);

      // If we removed the default address, set a new default if available
      if (isRemovingDefault && filteredAddresses.length > 0) {
        const newDefault = filteredAddresses[0];
        await supabase
          .from('addresses')
          .update({ is_default: true })
          .eq('id', newDefault.id);

        setDefaultAddress({ ...newDefault, is_default: true });
        setAddresses(filteredAddresses.map((addr, idx) =>
          idx === 0 ? { ...addr, is_default: true } : addr
        ));
      } else if (isRemovingDefault) {
        setDefaultAddress(null);
      }

      toast({
        title: 'Address Deleted',
        description: 'Your address has been removed',
      });

      return true;
    } catch (error) {
      console.error('Error deleting address:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete your address',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Set an address as default
  const setAsDefault = async (id: string) => {
    return updateAddress(id, { is_default: true });
  };

  // Fetch addresses when user changes
  useEffect(() => {
    fetchAddresses();
  }, [user]);

  return {
    addresses,
    defaultAddress,
    isLoading,
    fetchAddresses,
    addAddress,
    updateAddress,
    deleteAddress,
    setAsDefault
  };
}

