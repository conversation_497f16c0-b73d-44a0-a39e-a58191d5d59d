# Variant-Based Product Import Implementation Guide

This guide outlines the necessary changes to update the product import process to support our new variant-based product system.

## Overview

The current import process needs to be updated to:
1. Import base products with option definitions
2. Import variants with option combinations
3. Maintain relationships between products and their variants
4. Preserve all AI image and content generation features

## Required Changes

### 1. Update Data Types

```typescript
// Base product with option definitions
interface DBProduct {
  // Existing fields...
  
  // Replace individual option fields with a structured option_definitions field
  option_definitions: {
    [key: string]: {
      values: string[];
      display_type: string;
    }
  };
}

// New variant type
interface ProductVariant {
  id?: string;
  product_id: string;
  variant_name: string;
  sku: string | null;
  price: number;
  sale_price: number | null;
  stock_quantity: number;
  in_stock: boolean;
  image: string | null;
  option_combination: {
    [key: string]: string;
  };
  is_active: boolean;
  external_id: string | null;
}
```

### 2. Update Import Process

#### 2.1 File Handling

The import process should now handle two types of CSV files:
- Products CSV: Contains base product information with option definitions
- Variants CSV: Contains variant information with option combinations

#### 2.2 Import Flow

1. First, import all base products and store their IDs
2. Then, import all variants, linking them to their parent products
3. Ensure proper error handling if a variant references a non-existent product

### 3. Update mapProductToDBFormat Function

```typescript
const mapProductToDBFormat = (product: CSVProduct, categoryId: string | null, brandId: string | null): DBProduct => {
  // Extract images from semicolon-separated list (Wix format)
  const images = product.productImageUrl ? product.productImageUrl.split(';') : [];
  const firstImage = images.length > 0 ? images[0].trim() : '';
  
  // Extract additional images (all images except the first one)
  const additionalImages = images.length > 1 ? images.slice(1).map(img => img.trim()) : [];
  
  // Determine if product is in stock
  const inStock = product.inventory === 'InStock';
  
  // Calculate sale price if discount is available
  let salePrice = null;
  if (product.discountMode === 'PERCENT' && product.discountValue) {
    const discountValue = parseFloat(product.discountValue);
    const originalPrice = parseFloat(product.price);
    if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
      salePrice = originalPrice * (1 - discountValue / 100);
    }
  }
  
  // Extract option definitions
  const optionDefinitions: Record<string, { values: string[], display_type: string }> = {};
  
  if (product.productOptionName1 && product.productOptionDescription1) {
    optionDefinitions[product.productOptionName1] = {
      values: product.productOptionDescription1.split(/[;,|]/).map(v => v.trim()).filter(Boolean),
      display_type: determineDisplayType(product.productOptionName1, product.productOptionType1)
    };
  }
  
  if (product.productOptionName2 && product.productOptionDescription2) {
    optionDefinitions[product.productOptionName2] = {
      values: product.productOptionDescription2.split(/[;,|]/).map(v => v.trim()).filter(Boolean),
      display_type: determineDisplayType(product.productOptionName2, product.productOptionType2)
    };
  }
  
  if (product.productOptionName3 && product.productOptionDescription3) {
    optionDefinitions[product.productOptionName3] = {
      values: product.productOptionDescription3.split(/[;,|]/).map(v => v.trim()).filter(Boolean),
      display_type: determineDisplayType(product.productOptionName3, product.productOptionType3)
    };
  }
  
  return {
    name: product.name,
    slug: slugify(product.name, { lower: true, strict: true }),
    description: product.description || '',
    price: parseFloat(product.price) || 0,
    sale_price: salePrice,
    image: firstImage, // This will be the Wix filename, which will be processed in processImage
    additional_images: additionalImages,
    category_id: categoryId,
    brand_id: brandId,
    sku: product.sku || null,
    stock_quantity: parseInt(product.inventory) || 0,
    weight: parseFloat(product.weight) || null,
    in_stock: inStock,
    is_featured: false, // Default values, can be updated later
    is_new: false,
    is_active: !!firstImage, // Flag products without images as inactive
    option_definitions: JSON.stringify(optionDefinitions),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
};
```

### 4. Add mapVariantToDBFormat Function

```typescript
const mapVariantToDBFormat = (variant: CSVVariant, productId: string): ProductVariant => {
  // Extract option combination
  const optionCombination: Record<string, string> = {};
  
  if (variant.optionName1 && variant.optionValue1) {
    optionCombination[variant.optionName1] = variant.optionValue1;
  }
  
  if (variant.optionName2 && variant.optionValue2) {
    optionCombination[variant.optionName2] = variant.optionValue2;
  }
  
  if (variant.optionName3 && variant.optionValue3) {
    optionCombination[variant.optionName3] = variant.optionValue3;
  }
  
  return {
    product_id: productId,
    variant_name: variant.variantName || `${variant.optionValue1 || ''} ${variant.optionValue2 || ''} ${variant.optionValue3 || ''}`.trim(),
    sku: variant.sku || null,
    price: parseFloat(variant.price) || 0,
    sale_price: variant.salePrice ? parseFloat(variant.salePrice) : null,
    stock_quantity: parseInt(variant.stockQuantity) || 0,
    in_stock: (parseInt(variant.stockQuantity) || 0) > 0,
    image: variant.image || null,
    option_combination: JSON.stringify(optionCombination),
    is_active: variant.isActive === 'true',
    external_id: variant.externalId || null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
};
```

### 5. Update Image Processing

The image processing function should be updated to handle both product and variant images, with the same options for:
- Using Google Image Search (primary method)
- Using external URLs
- Using local images
- Skipping images

### 6. Update UI Components

The import UI should be updated to:
- Allow uploading both product and variant CSV files
- Show progress for both product and variant imports
- Display appropriate error messages for both types of imports

## Implementation Steps

1. Create a backup of the original ProductImport.tsx file
2. Create a new ProductImportWithVariants.tsx file with the updated functionality
3. Update the import UI to handle both product and variant CSV files
4. Test the import process with sample data
5. Verify that variants are correctly linked to their parent products
6. Ensure all AI features are preserved

## Notes

- Always preserve the Google Image Search functionality as the primary method for generating images
- Ensure proper error handling for cases where a variant references a non-existent product
- Maintain backward compatibility with the existing CSV format for a smooth transition

---

**IMPLEMENTATION STATUS: ✅ COMPLETED**
- Import system successfully updated for variants
- CSV processing handles both products and variants
- Image processing preserved and working
- All AI features maintained
