import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

interface HideAssignedFilterProps {
  onApply: (hideUnassigned: boolean) => void;
}

const HideAssignedFilter: React.FC<HideAssignedFilterProps> = ({ onApply }) => {
  const [hideUnassigned, setHideUnassigned] = useState(false);

  const handleToggle = (checked: boolean | 'indeterminate') => {
    setHideUnassigned(checked === true);
  };

  const handleApply = () => {
    onApply(hideUnassigned);
  };

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="hide-assigned" 
              checked={hideUnassigned}
              onCheckedChange={handleToggle}
            />
            <Label htmlFor="hide-assigned" className="cursor-pointer">
              Hide products already assigned to categories
            </Label>
          </div>
          <Button 
            onClick={handleApply}
            size="sm"
            variant="secondary"
          >
            Apply Filter
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default HideAssignedFilter;
