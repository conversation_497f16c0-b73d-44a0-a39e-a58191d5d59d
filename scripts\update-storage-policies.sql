-- SQL script to update storage policies for product-images bucket
-- Run this in the Supabase SQL Editor

-- First, let's drop the existing restrictive policies
DROP POLICY IF EXISTS "Admins can upload product images 16wiy3a_1" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload product images 16wiy3a_2" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload product images 16wiy3a_3" ON storage.objects;

-- Now create new policies that allow anyone to upload, update, and delete images

-- Policy for INSERT (uploading new files)
CREATE POLICY "Anyone can upload product images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'product-images'
  -- No auth check, allowing anyone to upload
);

-- Policy for UPDATE (updating existing files)
CREATE POLICY "Anyone can update product images"
ON storage.objects FOR UPDATE
USING (bucket_id = 'product-images')
WITH CHECK (bucket_id = 'product-images');

-- Policy for DELETE (deleting files)
CREATE POLICY "Anyone can delete product images"
ON storage.objects FOR DELETE
USING (bucket_id = 'product-images');

-- Make sure the SELECT policy remains to allow public access to images
-- If the existing policy is working fine, you can keep it
-- Otherwise, create or update it:
DROP POLICY IF EXISTS "Product images are publicly accessible" ON storage.objects;
CREATE POLICY "Product images are publicly accessible"
ON storage.objects FOR SELECT
USING (bucket_id = 'product-images');

-- Verify the policies
SELECT
  policyname,
  permissive,
  cmd AS operation,
  qual AS using_expression,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'objects'
  AND schemaname = 'storage'
  AND policyname LIKE '%product%';
