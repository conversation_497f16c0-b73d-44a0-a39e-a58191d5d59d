// Script to upload WebP images to Supabase storage
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('Error: Supabase key is missing. Please check your .env file.');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to local images
const localImagesPath = path.join(__dirname, 'public', 'images', 'products', 'wix-imports');

// Function to get all available image files
async function getAvailableImages() {
  try {
    const files = fs.readdirSync(localImagesPath);
    return files.filter(file => file.endsWith('.webp'));
  } catch (error) {
    console.error('Error reading image directory:', error);
    return [];
  }
}

// Function to upload images to Supabase storage
async function uploadImagesToStorage() {
  console.log('Starting image upload to Supabase storage...');
  
  try {
    // Get all available image files
    const availableImages = await getAvailableImages();
    console.log(`Found ${availableImages.length} available .webp image files locally`);
    
    // Get the list of files in the Supabase storage
    const { data: storageFiles, error: storageError } = await supabase
      .storage
      .from('product-images')
      .list();
    
    if (storageError) {
      console.error('Error listing storage files:', storageError);
      return;
    }
    
    console.log(`Found ${storageFiles.length} files in Supabase storage`);
    
    // Create a map of files in Supabase storage
    const storageFileMap = {};
    if (storageFiles) {
      storageFiles.forEach(file => {
        storageFileMap[file.name] = true;
      });
    }
    
    let uploaded = 0;
    let skipped = 0;
    let failed = 0;
    
    // Upload each image that doesn't exist in storage
    for (const filename of availableImages) {
      if (!storageFileMap[filename]) {
        try {
          const filePath = path.join(localImagesPath, filename);
          const fileContent = fs.readFileSync(filePath);
          
          const { data, error } = await supabase
            .storage
            .from('product-images')
            .upload(filename, fileContent, {
              contentType: 'image/webp',
              upsert: true
            });
          
          if (error) {
            console.error(`Error uploading ${filename}:`, error);
            failed++;
          } else {
            console.log(`Uploaded: ${filename}`);
            uploaded++;
          }
        } catch (error) {
          console.error(`Error processing ${filename}:`, error);
          failed++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 images
      if ((uploaded + skipped + failed) % 10 === 0) {
        console.log(`Progress: ${uploaded + skipped + failed}/${availableImages.length} (${uploaded} uploaded, ${skipped} skipped, ${failed} failed)`);
      }
    }
    
    console.log(`Finished! ${uploaded} images uploaded, ${skipped} skipped, ${failed} failed`);
    
    // Now update the database to ensure all product images use the correct URLs
    await updateProductImageUrls();
    
  } catch (error) {
    console.error('Error uploading images:', error);
  }
}

// Function to update product image URLs in the database
async function updateProductImageUrls() {
  console.log('Updating product image URLs in the database...');
  
  try {
    // Get all products
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products in the database`);
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Function to fix image URL
      const fixImageUrl = (imageUrl) => {
        if (!imageUrl) return null;
        
        // If the URL is already a Supabase URL with .webp extension, it's correct
        if (imageUrl.includes('/storage/v1/object/public/product-images/') && 
            imageUrl.endsWith('.webp')) {
          return imageUrl;
        }
        
        // Extract the filename from the URL
        let filename = '';
        
        if (imageUrl.includes('/storage/v1/object/public/product-images/')) {
          filename = imageUrl.split('/').pop();
        } else if (imageUrl.startsWith('/')) {
          filename = imageUrl.split('/').pop();
        } else {
          filename = imageUrl;
        }
        
        // Remove any query parameters
        filename = filename.split('?')[0];
        
        // Get the base filename without extension
        const baseFilename = filename.split('.')[0].replace(/~mv2/g, '');
        
        // Create the correct URL with .webp extension
        return `${supabaseUrl}/storage/v1/object/public/product-images/${baseFilename}.webp`;
      };
      
      // Fix main image URL
      if (product.image) {
        const fixedImageUrl = fixImageUrl(product.image);
        if (fixedImageUrl && fixedImageUrl !== product.image) {
          updates.image = fixedImageUrl;
          needsUpdate = true;
        }
      }
      
      // Fix additional image URLs
      if (product.additional_images && Array.isArray(product.additional_images) && 
          product.additional_images.length > 0) {
        const fixedAdditionalImages = product.additional_images
          .map(fixImageUrl)
          .filter(Boolean);
        
        // Check if any URLs were changed
        const hasChanges = JSON.stringify(fixedAdditionalImages) !== 
                          JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = fixedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished updating URLs! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error updating product image URLs:', error);
  }
}

// Run the script
uploadImagesToStorage().catch(console.error);
