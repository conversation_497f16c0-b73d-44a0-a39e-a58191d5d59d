-- Create newsletter_sends table to track newsletter campaigns
CREATE TABLE public.newsletter_sends (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  html_content TEXT,
  recipient_count INTEGER NOT NULL DEFAULT 0,
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Add check constraint for status
ALTER TABLE public.newsletter_sends
ADD CONSTRAINT newsletter_sends_status_check
CHECK (status IN ('draft', 'sending', 'sent', 'failed'));

-- Create indexes for better performance
CREATE INDEX idx_newsletter_sends_created_by ON public.newsletter_sends(created_by);
CREATE INDEX idx_newsletter_sends_sent_at ON public.newsletter_sends(sent_at);
CREATE INDEX idx_newsletter_sends_status ON public.newsletter_sends(status);

-- Enable RLS
ALTER TABLE public.newsletter_sends ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allow authenticated users to view all newsletter sends (for admin purposes)
CREATE POLICY "Authenticated users can view newsletter sends"
  ON public.newsletter_sends
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert newsletter sends
CREATE POLICY "Authenticated users can create newsletter sends"
  ON public.newsletter_sends
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- Allow users to update their own newsletter sends
CREATE POLICY "Users can update their own newsletter sends"
  ON public.newsletter_sends
  FOR UPDATE
  USING (auth.uid() = created_by);

-- Allow users to delete their own newsletter sends
CREATE POLICY "Users can delete their own newsletter sends"
  ON public.newsletter_sends
  FOR DELETE
  USING (auth.uid() = created_by);
