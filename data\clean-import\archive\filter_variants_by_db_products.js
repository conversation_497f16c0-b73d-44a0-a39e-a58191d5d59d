const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const dbProductIdsFile = 'db_product_ids.csv';
const variantsFile = 'transformed_variants_final.csv';
const outputFile = 'variants_for_db_products.csv';

// Store product IDs from database
const dbProductIds = new Set();

// Process the database product IDs file
console.log(`Reading product IDs from database...`);
fs.createReadStream(dbProductIdsFile)
  .pipe(csv())
  .on('data', (row) => {
    dbProductIds.add(row.id);
  })
  .on('end', () => {
    console.log(`Found ${dbProductIds.size} product IDs in database`);
    
    // Process the variants file
    console.log(`Reading variants from ${variantsFile}...`);
    const variants = [];
    const validVariants = [];
    const invalidVariants = [];
    
    fs.createReadStream(variantsFile)
      .pipe(csv())
      .on('data', (row) => {
        variants.push(row);
        
        // Check if product ID exists in database
        if (dbProductIds.has(row.product_id)) {
          validVariants.push(row);
        } else {
          invalidVariants.push(row);
        }
      })
      .on('end', async () => {
        console.log(`Read ${variants.length} variants from CSV`);
        console.log(`Found ${validVariants.length} variants with valid product IDs`);
        console.log(`Found ${invalidVariants.length} variants with invalid product IDs`);
        
        if (invalidVariants.length > 0) {
          console.log('\nSample invalid variants:');
          invalidVariants.slice(0, 5).forEach(variant => {
            console.log(`- product_id: ${variant.product_id}, variant_name: ${variant.variant_name}`);
          });
        }
        
        // Create a CSV writer
        const csvWriter = createObjectCsvWriter({
          path: outputFile,
          header: Object.keys(variants[0]).map(key => ({ id: key, title: key }))
        });
        
        // Write valid variants to CSV
        await csvWriter.writeRecords(validVariants);
        console.log(`\nCreated filtered variants file: ${outputFile}`);
        
        // Create a file with invalid product IDs
        const invalidProductIds = [...new Set(invalidVariants.map(v => v.product_id))];
        fs.writeFileSync('invalid_product_ids.txt', invalidProductIds.join('\n'));
        console.log(`Created invalid product IDs file: invalid_product_ids.txt`);
      });
  });
