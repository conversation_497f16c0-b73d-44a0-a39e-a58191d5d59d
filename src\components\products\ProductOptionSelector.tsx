import React from 'react';
import { VisualOptionCard } from './VisualOptionCard';
import { ColorSwatch } from './ColorSwatch';
import { Check, Plus, Minus } from 'lucide-react';

interface ProductOptionSelectorProps {
  optionName: string;
  values: string[];
  selectedValue: string | null;
  priceAdjustments?: Record<string, number>;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export const ProductOptionSelector: React.FC<ProductOptionSelectorProps> = ({
  optionName,
  values,
  selectedValue,
  priceAdjustments = {},
  onChange,
  disabled = false,
}) => {
  // Normalize option name for display and checks
  const displayName = optionName.charAt(0).toUpperCase() + optionName.slice(1);
  const normalizedName = optionName.toLowerCase();

  // Check if this is a color option
  const isColorOption = normalizedName.includes('color') || normalizedName.includes('colour');

  // Get price adjustment for selected value
  const selectedAdjustment = selectedValue && priceAdjustments[selectedValue] !== undefined
    ? Number(priceAdjustments[selectedValue])
    : null;

  // Format price adjustment for display
  const formatPriceAdjustment = (adjustment: number | null) => {
    if (adjustment === null || adjustment === 0) return null;
    return adjustment > 0
      ? `+£${adjustment.toFixed(2)}`
      : `-£${Math.abs(adjustment).toFixed(2)}`;
  };

  const formattedAdjustment = formatPriceAdjustment(selectedAdjustment);

  // Get appropriate icon based on option name
  const getOptionIcon = () => {
    if (normalizedName.includes('size') || normalizedName.includes('dimension')) {
      return <span className="mr-2 p-1 rounded-full bg-primary/10 text-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
          <polyline points="3.29 7 12 12 20.71 7"></polyline>
          <line x1="12" y1="22" x2="12" y2="12"></line>
        </svg>
      </span>;
    }

    if (isColorOption) {
      return <span className="mr-2 p-1 rounded-full bg-primary/10 text-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="13.5" cy="6.5" r="2.5"></circle>
          <circle cx="19" cy="13" r="2.5"></circle>
          <circle cx="6" cy="12" r="2.5"></circle>
          <circle cx="10" cy="20" r="2.5"></circle>
        </svg>
      </span>;
    }

    if (normalizedName.includes('pack') || normalizedName.includes('quantity')) {
      return <span className="mr-2 p-1 rounded-full bg-primary/10 text-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
        </svg>
      </span>;
    }

    // Default icon
    return <span className="inline-block w-2 h-2 rounded-full bg-primary mr-2"></span>;
  };

  return (
    <div className="backdrop-blur-sm bg-white/20 p-4 rounded-xl border border-white/30 shadow-md transition-all hover:shadow-lg">
      {/* Option header with selected value and price adjustment */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          {getOptionIcon()}
          {displayName}
        </h3>

        {selectedValue && (
          <div className="flex items-center gap-2 animate-fadeIn">
            {isColorOption ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 rounded-full overflow-hidden border border-gray-200 shadow-sm"
                     style={{
                       background: selectedValue.toLowerCase().includes('rasta')
                         ? 'linear-gradient(to right, #009900 33%, #ffff00 33%, #ffff00 66%, #ff0000 66%)'
                         : undefined,
                       backgroundColor: !selectedValue.toLowerCase().includes('rasta') ? selectedValue : undefined
                     }}>
                </div>
                <span className="text-sm font-medium">{selectedValue}</span>
              </div>
            ) : (
              <span className="text-sm font-medium bg-white/50 px-2 py-1 rounded-full flex items-center gap-1">
                <Check className="h-3.5 w-3.5 text-primary" />
                {selectedValue}
              </span>
            )}

            {formattedAdjustment && (
              <span className={`text-sm font-medium px-2 py-1 rounded-full flex items-center gap-1 ${
                selectedAdjustment && selectedAdjustment > 0
                  ? 'bg-red-50 text-red-600'
                  : 'bg-green-50 text-green-600'
              }`}>
                {selectedAdjustment && selectedAdjustment > 0
                  ? <Plus className="h-3 w-3" />
                  : <Minus className="h-3 w-3" />}
                {formattedAdjustment.replace(/[+-]/, '')}
              </span>
            )}
          </div>
        )}
      </div>

      {isColorOption ? (
        // Color swatches for color options
        <div className="flex flex-wrap gap-3 p-2 bg-white/30 rounded-lg">
          <div className="w-full mb-2 text-sm text-gray-600 font-medium">
            Select a color:
          </div>
          {values.filter(value => value !== 'DROP_DOWN').map((value) => (
            <ColorSwatch
              key={value}
              colorName={value}
              selected={selectedValue === value}
              disabled={disabled}
              onClick={() => onChange(value)}
            />
          ))}
        </div>
      ) : (
        // Visual cards for other options
        <div className="p-2 bg-white/30 rounded-lg">
          <div className="w-full mb-2 text-sm text-gray-600 font-medium">
            Select {normalizedName.includes('size') ? 'a size' : normalizedName.includes('pack') ? 'a pack size' : 'an option'}:
          </div>
          <div className="grid grid-cols-2 gap-3">
            {values.filter(value => value !== 'DROP_DOWN').map((value) => {
              // Get price adjustment for this option
              const adjustment = priceAdjustments[value] !== undefined
                ? Number(priceAdjustments[value])
                : null;

              return (
                <VisualOptionCard
                  key={value}
                  optionName={optionName}
                  value={value}
                  isSelected={selectedValue === value}
                  priceAdjustment={adjustment}
                  disabled={disabled}
                  onClick={() => onChange(value)}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
