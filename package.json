{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"proxy": "node scripts/proxy/server.js", "start-proxy": "node scripts/proxy/start-proxy.js", "dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "export-seeds-for-agent": "tsx src/scripts/export-seeds-for-agent.ts", "import-enriched-seeds": "tsx src/scripts/import-enriched-seeds.ts", "download-product-images": "tsx src/scripts/download-product-images.ts", "setup-seed-filtering": "tsx scripts/setup-seed-filtering.ts", "test-seed-filtering": "tsx scripts/test-seed-filtering.ts", "import-super-agent-data": "tsx scripts/import-super-agent-data.ts", "basic-seed-enrichment": "tsx scripts/basic-seed-enrichment.ts", "analyze-missing-data": "tsx scripts/analyze-missing-data.ts", "analyze-active-seeds": "tsx scripts/analyze-active-seeds-only.ts", "export-premium-inactive": "tsx scripts/export-premium-inactive-seeds.ts", "organize-super-agent-files": "tsx scripts/organize-super-agent-files.ts", "export-remaining-active": "tsx scripts/export-remaining-active-seeds.ts", "export-top-50-missing": "tsx scripts/export-top-50-missing-descriptions.ts", "import-super-agent-batches": "tsx scripts/import-super-agent-batches.ts", "import-all-super-agent-data": "tsx scripts/import-all-super-agent-data.ts", "fix-duplicate-filters": "tsx scripts/fix-duplicate-filter-categories.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "axios": "^1.9.0", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "esm": "^3.2.25", "express": "^4.21.2", "framer-motion": "^12.12.1", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "nanoid": "^5.1.5", "next-themes": "^0.3.0", "papaparse": "^5.5.2", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-parallax": "^3.5.2", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.12.7", "slugify": "^1.6.6", "sonner": "^1.5.0", "string-similarity": "^4.0.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/jest": "^29.5.14", "@types/node": "^22.15.18", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.7", "next": "^15.3.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}