"""
Full Dutch Passion Rich Media Extractor

This script extracts rich media elements from all 136 Dutch Passion product pages:
- Terpene profile charts and percentages
- Cannabinoid content indicators
- Product icons and badges
- Product images
"""

import requests
import json
import re
import os
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# Base URL
BASE_URL = "https://dutch-passion.com"
SEEDS_URL = "https://dutch-passion.com/en/cannabis-seeds"

# Headers to mimic a browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Connection": "keep-alive",
    "Referer": "https://dutch-passion.com/",
    "DNT": "1",  # Do Not Track
}

# Create directories for media
def create_directories():
    os.makedirs("dutch_passion_media/icons", exist_ok=True)
    os.makedirs("dutch_passion_media/product_images", exist_ok=True)
    os.makedirs("dutch_passion_media/terpene_charts", exist_ok=True)
    os.makedirs("dutch_passion_media/cannabinoid_charts", exist_ok=True)

# Function to get page content with age verification bypass
def get_page_content(url):
    print(f"Fetching content from: {url}")
    
    # First request to get cookies
    session = requests.Session()
    response = session.get(url, headers=HEADERS)
    
    # Check if we need to handle age verification
    if "ARE YOU AGED 18 OR OVER?" in response.text:
        print("Handling age verification...")
        
        # Extract any necessary tokens or form data
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Simulate clicking the "Enter" button by making a POST request
        verification_url = urljoin(BASE_URL, "/en/age-verification")
        verification_data = {
            "age_verification": "true",
            "redirect": "/en/cannabis-seeds"
        }
        
        # Send POST request to handle age verification
        response = session.post(verification_url, data=verification_data, headers=HEADERS)
        
        # Get the main page again after verification
        response = session.get(url, headers=HEADERS)
    
    return response.text, session

# Function to download an image
def download_image(session, url, save_path):
    try:
        response = session.get(url, headers=HEADERS)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            print(f"Downloaded image to {save_path}")
            return True
        else:
            print(f"Failed to download image: {url}, status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading image {url}: {e}")
        return False

# Function to extract filename from URL
def get_filename_from_url(url):
    parsed_url = urlparse(url)
    path = parsed_url.path
    filename = os.path.basename(path)
    return filename

# Function to extract terpene profiles
def extract_terpene_profiles(soup, product_id, session):
    terpene_data = {}
    
    # Look for terpene chart - using more specific selectors based on the screenshots
    terpene_sections = soup.select('.terpene-chart, .cannabinoid-profile, .terpenes-section, .product-info img[src*="terpene"], .product-info img[src*="chart"], .product-description img[src*="terpene"], img[alt*="terpene"], img[alt*="profile"]')
    
    # Also look for the circular terpene profile chart shown in the screenshot
    circular_charts = soup.select('.product-info img[src*="circle"], .product-info img[src*="wheel"], .product-info img[src*="profile"], img[alt*="terpene profile"]')
    if circular_charts:
        terpene_sections.extend(circular_charts)
    
    if terpene_sections:
        print(f"Found terpene sections for product {product_id}")
        
        # Extract terpene percentages from text
        product_text = soup.get_text()
        
        # Common terpenes to look for
        terpenes = ["Myrcene", "Limonene", "Caryophyllene", "Pinene", "Linalool", "Terpinolene", "Humulene", "Beta-Pinene", "Beta-Caryophyllene", "Beta-Myrcene"]
        
        for terpene in terpenes:
            # Look for percentage patterns like "Myrcene: 0.5%" or "Myrcene (0.5%)" or "37.6%" (from screenshot)
            pattern = rf"{terpene}[:\s]*(\d+(?:\.\d+)?)%"
            match = re.search(pattern, product_text, re.IGNORECASE)
            if match:
                terpene_data[terpene] = match.group(1) + "%"
        
        # Try to find and download terpene chart image
        for section in terpene_sections:
            if section.name == 'img':
                img_elem = section
            else:
                img_elem = section.select_one('img')
                
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    filename = f"terpene_chart_{product_id}.jpg"
                    save_path = os.path.join("dutch_passion_media/terpene_charts", filename)
                    
                    if download_image(session, img_url, save_path):
                        terpene_data['chart_image'] = save_path
                        break
    
    # Look specifically for the terpene wheel from the screenshot
    terpene_wheel = soup.select('.terpene-wheel, .terpene-profile, .terpene-chart, div[class*="terpene"]')
    if terpene_wheel:
        print(f"Found terpene wheel for product {product_id}")
        
        # Try to extract percentages from the wheel
        for wheel in terpene_wheel:
            # Look for percentage elements
            percentage_elements = wheel.select('.percentage, .value, .terpene-value')
            name_elements = wheel.select('.name, .terpene-name')
            
            if len(percentage_elements) == len(name_elements) and len(percentage_elements) > 0:
                for i in range(len(percentage_elements)):
                    name = name_elements[i].text.strip()
                    value = percentage_elements[i].text.strip()
                    
                    # Extract percentage
                    percentage_match = re.search(r'(\d+(?:\.\d+)?)%', value)
                    if percentage_match:
                        value = percentage_match.group(1) + "%"
                    
                    terpene_data[name] = value
    
    # If no specific terpene data found, look for it in the product description
    if not terpene_data:
        description = soup.select_one('#description, .product-description, .product-info')
        if description:
            text = description.get_text()
            
            # Look for terpene mentions
            terpene_section = re.search(r'(?:terpene|aroma).*?profile', text, re.IGNORECASE)
            if terpene_section:
                section_text = text[terpene_section.start():terpene_section.start() + 500]  # Get some context
                
                # Extract terpene percentages
                percentages = re.findall(r'(\w+)[:\s]*(\d+(?:\.\d+)?)%', section_text)
                for name, value in percentages:
                    if len(name) > 3:  # Avoid short abbreviations
                        terpene_data[name] = value + "%"
    
    # Look for the specific terpene layout shown in the screenshot
    terpene_icons = soup.select('.terpene-icon, .terpene-item, div[class*="terpene"] img')
    if terpene_icons:
        print(f"Found terpene icons for product {product_id}")
        
        for i, icon in enumerate(terpene_icons):
            try:
                # Try to get the image
                if icon.name == 'img':
                    img_elem = icon
                else:
                    img_elem = icon.select_one('img')
                
                if img_elem:
                    img_url = img_elem.get('src') or img_elem.get('data-src')
                    if img_url:
                        if not img_url.startswith('http'):
                            img_url = urljoin(BASE_URL, img_url)
                        
                        filename = f"terpene_icon_{product_id}_{i}.png"
                        save_path = os.path.join("dutch_passion_media/icons", filename)
                        
                        if download_image(session, img_url, save_path):
                            # Try to get the name and percentage
                            parent = img_elem.parent
                            text = parent.get_text().strip()
                            
                            # Extract name and percentage
                            name_match = re.search(r'([A-Za-z\-]+)', text)
                            percentage_match = re.search(r'(\d+(?:\.\d+)?)%', text)
                            
                            if name_match and percentage_match:
                                name = name_match.group(1).strip()
                                value = percentage_match.group(1) + "%"
                                
                                terpene_data[name] = value
                                terpene_data[f"{name}_icon"] = save_path
            except Exception as e:
                print(f"Error extracting terpene icon: {e}")
    
    return terpene_data

# Function to extract cannabinoid profiles
def extract_cannabinoid_profiles(soup, product_id, session):
    cannabinoid_data = {}
    
    # Look for cannabinoid information - using more specific selectors based on the screenshots
    cannabinoid_sections = soup.select('.cannabinoid-section, .thc-section, .cbd-section, .product-info img[src*="thc"], .product-info img[src*="cbd"], .product-info img[src*="cannabinoid"], .thc-meter, .cannabinoid-meter, div[class*="cannabinoid"], div[class*="thc"]')
    
    if cannabinoid_sections:
        print(f"Found cannabinoid sections for product {product_id}")
        
        # Extract cannabinoid percentages from text
        product_text = soup.get_text()
        
        # Look for THC percentage
        thc_match = re.search(r'THC[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if thc_match:
            cannabinoid_data['THC'] = thc_match.group(1) + "%"
        
        # Look for CBD percentage
        cbd_match = re.search(r'CBD[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if cbd_match:
            cannabinoid_data['CBD'] = cbd_match.group(1) + "%"
        
        # Look for CBG percentage
        cbg_match = re.search(r'CBG[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if cbg_match:
            cannabinoid_data['CBG'] = cbg_match.group(1) + "%"
        
        # Look for THCV percentage
        thcv_match = re.search(r'THCV[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if thcv_match:
            cannabinoid_data['THCV'] = thcv_match.group(1) + "%"
        
        # Try to find and download cannabinoid chart image
        for section in cannabinoid_sections:
            if section.name == 'img':
                img_elem = section
            else:
                img_elem = section.select_one('img')
                
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    filename = f"cannabinoid_chart_{product_id}.jpg"
                    save_path = os.path.join("dutch_passion_media/cannabinoid_charts", filename)
                    
                    if download_image(session, img_url, save_path):
                        cannabinoid_data['chart_image'] = save_path
                        break
    
    # Look specifically for the THC meter from the screenshot
    thc_meter = soup.select('.thc-meter, .thc-gauge, .thc-level, div[class*="thc"]')
    if thc_meter:
        print(f"Found THC meter for product {product_id}")
        
        # Try to extract THC level from the meter
        for meter in thc_meter:
            # Look for THC level text
            level_text = meter.get_text().strip()
            
            # Extract THC level
            thc_match = re.search(r'THC[:\s]*(\d+(?:\.\d+)?)%', level_text, re.IGNORECASE)
            if thc_match:
                cannabinoid_data['THC'] = thc_match.group(1) + "%"
            
            # Look for THC level image
            img_elem = meter.select_one('img')
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    filename = f"thc_meter_{product_id}.jpg"
                    save_path = os.path.join("dutch_passion_media/cannabinoid_charts", filename)
                    
                    if download_image(session, img_url, save_path):
                        cannabinoid_data['thc_meter_image'] = save_path
    
    # If no specific cannabinoid data found, try to extract from text
    if not cannabinoid_data:
        # Look for THC/CBD mentions in product description
        description = soup.select_one('#description, .product-description, .product-info')
        if description:
            text = description.get_text().lower()
            
            # Extract THC percentage with more flexible pattern
            thc_patterns = [
                r'thc[^\d]*(\d+(?:\.\d+)?)%',
                r'thc[^\d]*content[^\d]*(\d+(?:\.\d+)?)%',
                r'thc[^\d]*level[^\d]*(\d+(?:\.\d+)?)%',
                r'(\d+(?:\.\d+)?)%[^\d]*thc'
            ]
            
            for pattern in thc_patterns:
                thc_match = re.search(pattern, text)
                if thc_match:
                    cannabinoid_data['THC'] = thc_match.group(1) + "%"
                    break
            
            # Extract CBD percentage with more flexible pattern
            cbd_patterns = [
                r'cbd[^\d]*(\d+(?:\.\d+)?)%',
                r'cbd[^\d]*content[^\d]*(\d+(?:\.\d+)?)%',
                r'cbd[^\d]*level[^\d]*(\d+(?:\.\d+)?)%',
                r'(\d+(?:\.\d+)?)%[^\d]*cbd'
            ]
            
            for pattern in cbd_patterns:
                cbd_match = re.search(pattern, text)
                if cbd_match:
                    cannabinoid_data['CBD'] = cbd_match.group(1) + "%"
                    break
    
    # Look for the specific THC: VERY HIGH text from the screenshot
    thc_level_text = soup.select('.thc-level-text, .thc-text, .cannabinoid-text, div[class*="thc"] strong, div[class*="cannabinoid"] strong')
    if thc_level_text:
        for level_elem in thc_level_text:
            level_text = level_elem.get_text().strip()
            
            # Check for descriptive THC levels
            if "very high" in level_text.lower():
                cannabinoid_data['THC_level'] = "VERY HIGH"
            elif "high" in level_text.lower():
                cannabinoid_data['THC_level'] = "HIGH"
            elif "medium" in level_text.lower():
                cannabinoid_data['THC_level'] = "MEDIUM"
            elif "low" in level_text.lower():
                cannabinoid_data['THC_level'] = "LOW"
    
    return cannabinoid_data

# Function to extract product icons
def extract_product_icons(soup, product_id, session):
    icons_data = {}
    
    # Look for product characteristic icons - using more specific selectors based on the screenshots
    icon_sections = soup.select('.product-characteristics, .product-icons, .product-features, .product-info
(Content truncated due to size limit. Use line ranges to read in chunks)