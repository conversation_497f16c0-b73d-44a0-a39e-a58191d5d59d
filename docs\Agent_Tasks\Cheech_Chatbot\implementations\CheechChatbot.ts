/**
 * Cheech Chatbot Implementation
 *
 * This file implements the main Cheech chatbot service that integrates
 * all components for a comprehensive product knowledge assistant.
 * Uses the existing AI orchestration system with Gemini and Deepseek.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  Message,
  ChatResponse,
  Product,
  ChatSession,
  KnowledgeEntry
} from './types';
import { RAGEngine } from './RAGEngine';
import { ContextManager } from './ContextManager';
import { WebNavigationAssistant } from './WebNavigationAssistant';
import { UnifiedAIService } from '../../../services/ai/core/UnifiedAIService';
import { AIRequest, AIRequestType } from '../../../services/ai/types/AIRequest';

/**
 * Configuration for the Cheech chatbot
 */
interface CheechConfig {
  aiService: UnifiedAIService;
  baseUrl: string;
  productUrlPattern?: string;
  categoryUrlPattern?: string;
  maxResponseTokens?: number;
  temperature?: number;
  knowledgeBasePath?: string;
  productDatabasePath?: string;
}

/**
 * Main Cheech chatbot service
 */
export class CheechChatbot {
  private ragEngine: RAGEngine;
  private contextManager: ContextManager;
  private navigationAssistant: WebNavigationAssistant;
  private aiService: UnifiedAIService;
  private sessions: Map<string, ChatSession> = new Map();
  private config: CheechConfig;
  private knowledgeBase: KnowledgeEntry[] = [];
  private products: Product[] = [];

  /**
   * Create a new Cheech chatbot
   * @param config The chatbot configuration
   */
  constructor(config: CheechConfig) {
    this.config = {
      maxResponseTokens: 1024,
      temperature: 0.7,
      ...config
    };

    // Store AI service reference
    this.aiService = config.aiService;

    // Initialize components with AI service
    this.ragEngine = new RAGEngine(config.aiService);
    this.contextManager = new ContextManager(config.aiService);
    this.navigationAssistant = new WebNavigationAssistant(
      config.baseUrl,
      config.productUrlPattern,
      config.categoryUrlPattern
    );

    // Load knowledge base and products if paths provided
    if (config.knowledgeBasePath) {
      this.loadKnowledgeBase(config.knowledgeBasePath);
    }

    if (config.productDatabasePath) {
      this.loadProductDatabase(config.productDatabasePath);
    }
  }

  /**
   * Load knowledge base from file
   * @param path The path to the knowledge base file
   */
  private async loadKnowledgeBase(path: string): Promise<void> {
    try {
      // In a real implementation, this would load from a file or database
      // For this prototype, we'll use a placeholder
      console.log(`Loading knowledge base from ${path}...`);

      // Placeholder knowledge base
      this.knowledgeBase = [
        {
          id: 'kb-1',
          question: 'How do I clean my bong?',
          answer: 'To clean your bong, you\'ll need isopropyl alcohol (91% or higher), coarse salt, and warm water. Empty the bong, rinse with warm water, add alcohol and salt, cover openings, shake vigorously, rinse thoroughly, and let dry. For best results, clean your bong at least once a week.',
          category: 'cleaning',
          tags: ['bong', 'cleaning', 'maintenance']
        },
        {
          id: 'kb-2',
          question: 'What\'s the difference between CBD and THC?',
          answer: 'CBD (cannabidiol) and THC (tetrahydrocannabinol) are both cannabinoids found in cannabis plants. The key difference is that THC produces psychoactive effects ("high"), while CBD does not. CBD is often used for its potential therapeutic benefits without the intoxicating effects of THC.',
          category: 'education',
          tags: ['cbd', 'thc', 'cannabinoids']
        },
        {
          id: 'kb-3',
          question: 'How do I choose the right vaporizer?',
          answer: 'When choosing a vaporizer, consider: 1) Material (dry herb, concentrate, or both), 2) Portability needs, 3) Battery life, 4) Temperature control, 5) Budget, and 6) Vapor quality. Desktop vaporizers offer better vapor quality but aren\'t portable, while pen vaporizers are discreet but may have less battery life.',
          category: 'products',
          tags: ['vaporizer', 'vape', 'buying guide']
        }
      ];

      // Add knowledge base to RAG engine
      for (const entry of this.knowledgeBase) {
        await this.ragEngine.addDocuments([{
          id: entry.id,
          content: `Question: ${entry.question}\nAnswer: ${entry.answer}`,
          metadata: {
            source: 'knowledge_base',
            category: entry.category,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }]);
      }

      console.log(`Loaded ${this.knowledgeBase.length} knowledge base entries`);
    } catch (error) {
      console.error('Error loading knowledge base:', error);
    }
  }

  /**
   * Load product database from file
   * @param path The path to the product database file
   */
  private async loadProductDatabase(path: string): Promise<void> {
    try {
      // In a real implementation, this would load from a file or database
      // For this prototype, we'll use a placeholder
      console.log(`Loading product database from ${path}...`);

      // Placeholder product database
      this.products = [
        {
          id: 'p-1',
          name: 'Crystal Clear Cleaning Solution',
          description: 'Premium cleaning solution specifically formulated for glass water pipes and bongs. Removes resin, tar, and buildup without harmful chemicals.',
          price: 14.99,
          imageUrl: '/images/products/crystal-clear.jpg',
          category: 'cleaning',
          tags: ['cleaning', 'solution', 'maintenance'],
          rating: 4.8,
          reviewCount: 156,
          inStock: true
        },
        {
          id: 'p-2',
          name: 'Herbal Essence Vaporizer',
          description: 'Advanced dry herb vaporizer with precision temperature control, extended battery life, and pure vapor path for the cleanest flavor.',
          price: 149.99,
          imageUrl: '/images/products/herbal-essence.jpg',
          category: 'vaporizers',
          subcategory: 'dry-herb',
          tags: ['vaporizer', 'dry herb', 'portable'],
          rating: 4.6,
          reviewCount: 89,
          inStock: true
        },
        {
          id: 'p-3',
          name: 'Full Spectrum CBD Oil (1000mg)',
          description: 'High-quality full spectrum CBD oil containing multiple cannabinoids for enhanced effectiveness. Lab tested for purity and potency.',
          price: 79.99,
          imageUrl: '/images/products/cbd-oil.jpg',
          category: 'cbd',
          subcategory: 'tinctures',
          tags: ['cbd', 'oil', 'tincture', 'full spectrum'],
          rating: 4.9,
          reviewCount: 212,
          inStock: true
        }
      ];

      // Add products to RAG engine
      for (const product of this.products) {
        const documents = [
          {
            id: `product-${product.id}`,
            content: `
              Product Name: ${product.name}
              Category: ${product.category}
              ${product.subcategory ? `Subcategory: ${product.subcategory}` : ''}
              Price: $${product.price}
              Description: ${product.description}
              Tags: ${product.tags.join(', ')}
            `,
            metadata: {
              source: 'product_catalog',
              category: product.category,
              productId: product.id,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }
        ];

        await this.ragEngine.addDocuments(documents);
      }

      console.log(`Loaded ${this.products.length} products`);
    } catch (error) {
      console.error('Error loading product database:', error);
    }
  }

  /**
   * Create a new chat session
   * @returns The new session ID
   */
  createSession(): string {
    const sessionId = uuidv4();

    this.sessions.set(sessionId, {
      id: sessionId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      context: {}
    });

    return sessionId;
  }

  /**
   * Get a chat session
   * @param sessionId The session ID
   * @returns The chat session or null if not found
   */
  getSession(sessionId: string): ChatSession | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Process a user message
   * @param sessionId The session ID
   * @param userMessage The user message
   * @returns Promise resolving to chat response
   */
  async processMessage(sessionId: string, userMessage: string): Promise<ChatResponse> {
    try {
      // Get or create session
      let session = this.getSession(sessionId);

      if (!session) {
        sessionId = this.createSession();
        session = this.getSession(sessionId)!;
      }

      // Create message object
      const message: Message = {
        id: uuidv4(),
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      };

      // Add message to session
      session.messages.push(message);
      session.updatedAt = new Date();

      // Update context with recent messages
      await this.contextManager.updateContext(sessionId, {
        recentMessages: session.messages
      });

      // Extract user preferences from conversation
      const userPreferences = this.contextManager.extractUserPreferences(session.messages);

      // Update context with user preferences
      if (Object.keys(userPreferences).length > 0) {
        await this.contextManager.updateContext(sessionId, {
          userPreferences
        });
      }

      // Generate response
      const response = await this.generateResponse(sessionId, userMessage);

      // Add response to session
      const assistantMessage: Message = {
        id: uuidv4(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date()
      };

      session.messages.push(assistantMessage);

      // Update context with new message
      await this.contextManager.updateContext(sessionId, {
        recentMessages: session.messages
      });

      return response;
    } catch (error) {
      console.error('Error processing message:', error);

      return {
        message: 'I apologize, but I encountered an error processing your message. Please try again.',
        suggestions: ['Try a different question', 'Contact support']
      };
    }
  }

  /**
   * Generate a response to a user message
   * @param sessionId The session ID
   * @param userMessage The user message
   * @returns Promise resolving to chat response
   */
  private async generateResponse(sessionId: string, userMessage: string): Promise<ChatResponse> {
    // Analyze user message to determine intent
    const intent = await this.analyzeIntent(userMessage);

    // Handle different intents
    switch (intent.type) {
      case 'product_search':
        return this.handleProductSearch(sessionId, userMessage, intent.entities);

      case 'product_comparison':
        return this.handleProductComparison(sessionId, userMessage, intent.entities);

      case 'navigation':
        return this.handleNavigation(sessionId, userMessage, intent.entities);

      case 'knowledge_query':
        return this.handleKnowledgeQuery(sessionId, userMessage);

      case 'greeting':
        return this.handleGreeting(sessionId);

      case 'farewell':
        return this.handleFarewell(sessionId);

      default:
        return this.handleGeneralQuery(sessionId, userMessage);
    }
  }

  /**
   * Analyze user message to determine intent
   * @param userMessage The user message
   * @returns Promise resolving to intent analysis
   */
  private async analyzeIntent(userMessage: string): Promise<{
    type: string;
    confidence: number;
    entities: Record<string, any>;
  }> {
    // This is a simplified implementation
    // In a real system, this would use NLU or a more sophisticated approach

    const lowercaseMessage = userMessage.toLowerCase();

    // Check for greetings
    if (
      lowercaseMessage.includes('hello') ||
      lowercaseMessage.includes('hi') ||
      lowercaseMessage.includes('hey') ||
      lowercaseMessage.match(/^(good|morning|afternoon|evening)/)
    ) {
      return {
        type: 'greeting',
        confidence: 0.9,
        entities: {}
      };
    }

    // Check for farewells
    if (
      lowercaseMessage.includes('bye') ||
      lowercaseMessage.includes('goodbye') ||
      lowercaseMessage.includes('see you') ||
      lowercaseMessage.includes('thank you')
    ) {
      return {
        type: 'farewell',
        confidence: 0.9,
        entities: {}
      };
    }

    // Check for product search
    if (
      lowercaseMessage.includes('looking for') ||
      lowercaseMessage.includes('find') ||
      lowercaseMessage.includes('search') ||
      lowercaseMessage.includes('show me')
    ) {
      // Extract product category
      let category = '';

      if (lowercaseMessage.includes('bong')) category = 'bongs';
      else if (lowercaseMessage.includes('vape') || lowercaseMessage.includes('vaporizer')) category = 'vaporizers';
      else if (lowercaseMessage.includes('cbd')) category = 'cbd';
      else if (lowercaseMessage.includes('clean')) category = 'cleaning';

      return {
        type: 'product_search',
        confidence: 0.8,
        entities: { category }
      };
    }

    // Check for product comparison
    if (
      lowercaseMessage.includes('compare') ||
      lowercaseMessage.includes('difference between') ||
      lowercaseMessage.includes('better') ||
      lowercaseMessage.includes('vs')
    ) {
      return {
        type: 'product_comparison',
        confidence: 0.8,
        entities: {}
      };
    }

    // Check for navigation
    if (
      lowercaseMessage.includes('go to') ||
      lowercaseMessage.includes('navigate') ||
      lowercaseMessage.includes('take me to') ||
      lowercaseMessage.includes('show me the page')
    ) {
      return {
        type: 'navigation',
        confidence: 0.8,
        entities: {}
      };
    }

    // Check for knowledge query
    if (
      lowercaseMessage.includes('how do i') ||
      lowercaseMessage.includes('what is') ||
      lowercaseMessage.includes('why') ||
      lowercaseMessage.includes('explain')
    ) {
      return {
        type: 'knowledge_query',
        confidence: 0.7,
        entities: {}
      };
    }

    // Default to general query
    return {
      type: 'general_query',
      confidence: 0.5,
      entities: {}
    };
  }

  /**
   * Handle product search intent
   * @param sessionId The session ID
   * @param userMessage The user message
   * @param entities The extracted entities
   * @returns Promise resolving to chat response
   */
  private async handleProductSearch(
    sessionId: string,
    userMessage: string,
    entities: Record<string, any>
  ): Promise<ChatResponse> {
    // Query RAG engine for relevant products
    const queryResult = await this.ragEngine.query(userMessage, {
      source: 'product_catalog'
    });

    // Extract product IDs from query results
    const productIds = queryResult.documents
      .filter(doc => doc.metadata.productId)
      .map(doc => doc.metadata.productId);

    // Find matching products
    const matchingProducts = this.products.filter(product =>
      productIds.includes(product.id) ||
      (entities.category && product.category === entities.category)
    );

    if (matchingProducts.length === 0) {
      return {
        message: "I couldn't find any products matching your search. Could you provide more details about what you're looking for?",
        suggestions: ['Show all vaporizers', 'Show cleaning products', 'Show CBD products']
      };
    }

    // Generate response
    const productList = matchingProducts
      .slice(0, 3) // Limit to 3 products
      .map(product => `- **${product.name}** ($${product.price}) - ${product.description.substring(0, 100)}...`)
      .join('\n\n');

    return {
      message: `Here are some products that might interest you:\n\n${productList}\n\nWould you like more information about any of these products?`,
      suggestions: ['Show more products', 'Compare these products', 'Filter by price'],
      products: matchingProducts.slice(0, 3)
    };
  }

  /**
   * Handle product comparison intent
   * @param sessionId The session ID
   * @param userMessage The user message
   * @param entities The extracted entities
   * @returns Promise resolving to chat response
   */
  private async handleProductComparison(
    sessionId: string,
    userMessage: string,
    entities: Record<string, any>
  ): Promise<ChatResponse> {
    // This would be more sophisticated in a real implementation

    return {
      message: "I'd be happy to help you compare products. Could you specify which products you'd like to compare?",
      suggestions: ['Compare vaporizers', 'Compare CBD oils', 'Compare cleaning solutions']
    };
  }

  /**
   * Handle navigation intent
   * @param sessionId The session ID
   * @param userMessage The user message
   * @param entities The extracted entities
   * @returns Promise resolving to chat response
   */
  private async handleNavigation(
    sessionId: string,
    userMessage: string,
    entities: Record<string, any>
  ): Promise<ChatResponse> {
    try {
      // Find relevant page
      const navigationResult = await this.navigationAssistant.findPageForQuery(userMessage);

      // Generate response
      return {
        message: `${navigationResult.instructions}\n\nI can take you to this page if you'd like.`,
        suggestions: ['Go to this page', 'Show me something else'],
        actions: [
          {
            type: 'navigate',
            payload: {
              url: navigationResult.url,
              title: navigationResult.title
            }
          }
        ]
      };
    } catch (error) {
      console.error('Error handling navigation:', error);

      return {
        message: "I'm sorry, I couldn't navigate to the page you requested. Would you like to try something else?",
        suggestions: ['Go to home page', 'Show me products']
      };
    }
  }

  /**
   * Handle knowledge query intent
   * @param sessionId The session ID
   * @param userMessage The user message
   * @returns Promise resolving to chat response
   */
  private async handleKnowledgeQuery(
    sessionId: string,
    userMessage: string
  ): Promise<ChatResponse> {
    // Query RAG engine for relevant knowledge
    const queryResult = await this.ragEngine.query(userMessage, {
      source: 'knowledge_base'
    });

    if (queryResult.documents.length === 0) {
      return this.handleGeneralQuery(sessionId, userMessage);
    }

    // Get most relevant document
    const mostRelevantDoc = queryResult.documents[0];
    const content = mostRelevantDoc.content;

    // Extract answer from content
    const answerMatch = content.match(/Answer: ([\s\S]+)/);
    const answer = answerMatch ? answerMatch[1].trim() : content;

    // Generate suggestions based on document category
    const suggestions: string[] = [];

    if (mostRelevantDoc.metadata.category === 'cleaning') {
      suggestions.push('Show cleaning products', 'More cleaning tips');
    } else if (mostRelevantDoc.metadata.category === 'education') {
      suggestions.push('Learn more about CBD', 'CBD vs THC');
    } else if (mostRelevantDoc.metadata.category === 'products') {
      suggestions.push('Show recommended products', 'Compare options');
    }

    return {
      message: answer,
      suggestions
    };
  }

  /**
   * Handle greeting intent
   * @param sessionId The session ID
   * @returns Promise resolving to chat response
   */
  private async handleGreeting(sessionId: string): Promise<ChatResponse> {
    const session = this.getSession(sessionId);
    const isReturningUser = session && session.messages.length > 2;

    if (isReturningUser) {
      return {
        message: "Welcome back! How can I help you today?",
        suggestions: ['Show new products', 'Continue our conversation', 'I have a question']
      };
    } else {
      return {
        message: "Hey there! I'm Cheech, your BitsnBongs product expert. I can help you find the perfect products, answer questions about our items, or guide you through our website. What can I help you with today?",
        suggestions: ['Show popular products', 'Help me find something', 'I have a question']
      };
    }
  }

  /**
   * Handle farewell intent
   * @param sessionId The session ID
   * @returns Promise resolving to chat response
   */
  private async handleFarewell(sessionId: string): Promise<ChatResponse> {
    return {
      message: "Thanks for chatting! If you have any more questions about our products, feel free to ask anytime. Have a great day!",
      suggestions: ['Start new conversation', 'Browse products']
    };
  }

  /**
   * Handle general query intent
   * @param sessionId The session ID
   * @param userMessage The user message
   * @returns Promise resolving to chat response
   */
  private async handleGeneralQuery(
    sessionId: string,
    userMessage: string
  ): Promise<ChatResponse> {
    try {
      // Query RAG engine for relevant information
      const queryResult = await this.ragEngine.query(userMessage);

      // Get conversation context
      const contextPrompt = await this.contextManager.generateContextPrompt(sessionId);

      // Combine user message, context, and retrieved information
      const prompt = `You are Cheech, a knowledgeable and friendly chatbot for BitsnBongs, a cannabis and CBD product retailer.
Your goal is to help users find products, answer their questions, and provide guidance.

${contextPrompt}

User query: ${userMessage}

Retrieved information:
${queryResult.documents.map(doc => doc.content).join('\n\n')}

Respond in a friendly, conversational tone. Be concise but informative. If you don't know something, admit it and offer alternatives.
Do not mention that you are an AI or that you're using retrieved information.
Focus on being helpful and guiding users to relevant products or information.`;

      // Create AI request using the unified AI system
      const aiRequest: AIRequest = {
        type: 'chat_response' as AIRequestType,
        content: prompt,
        context: {
          business_type: 'cannabis',
          brand_voice: {
            tone: 'friendly',
            personality: 'knowledgeable and helpful product expert',
            compliance_requirements: ['age_verification', 'cannabis_regulations']
          },
          max_length: this.config.maxResponseTokens || 1024,
          format: 'plain'
        },
        provider: 'auto', // Let the AI orchestration system choose the best provider
        complexity: 'medium',
        urgency: 'medium',
        session_id: sessionId
      };

      // Get response from AI orchestration system
      const aiResponse = await this.aiService.processRequest(aiRequest);

      if (aiResponse.success && aiResponse.content) {
        // Generate suggestions
        const suggestions = this.generateSuggestions(userMessage, queryResult);

        return {
          message: aiResponse.content,
          suggestions
        };
      } else {
        // Fallback to simple response if AI fails
        return this.generateFallbackResponse(userMessage, queryResult);
      }
    } catch (error) {
      console.error('Error in handleGeneralQuery:', error);
      return this.generateFallbackResponse(userMessage, null);
    }
  }

  /**
   * Generate a fallback response when AI service fails
   * @param userMessage The user message
   * @param queryResult The query result (if available)
   * @returns Chat response
   */
  private generateFallbackResponse(userMessage: string, queryResult: any): ChatResponse {
    let responseMessage = '';

    if (queryResult && queryResult.documents.length > 0) {
      // Use the most relevant document as the basis for the response
      const mostRelevantDoc = queryResult.documents[0];

      if (mostRelevantDoc.metadata.source === 'product_catalog') {
        // Product-related response
        const productId = mostRelevantDoc.metadata.productId;
        const product = this.products.find(p => p.id === productId);

        if (product) {
          responseMessage = `I'd recommend checking out the ${product.name}. It's priced at $${product.price} and ${product.description.substring(0, 100)}... Would you like more details about this product?`;
        } else {
          responseMessage = "Based on what you're looking for, we have several products that might interest you. Would you like me to show you some options?";
        }
      } else if (mostRelevantDoc.metadata.source === 'knowledge_base') {
        // Knowledge-related response
        const contentLines = mostRelevantDoc.content.split('\n');
        const answerLine = contentLines.find(line => line.startsWith('Answer:'));

        if (answerLine) {
          responseMessage = answerLine.substring('Answer:'.length).trim();
        } else {
          responseMessage = mostRelevantDoc.content;
        }
      } else {
        // Generic response
        responseMessage = "I found some information that might help. What specific details are you looking for?";
      }
    } else {
      // No relevant information found
      responseMessage = "I don't have specific information about that, but I'd be happy to help you find what you're looking for. Could you provide more details about your question?";
    }

    // Generate suggestions
    const suggestions = this.generateSuggestions(userMessage, queryResult);

    return {
      message: responseMessage,
      suggestions
    };
  }

  /**
   * Generate suggestions based on user message and query results
   * @param userMessage The user message
   * @param queryResult The query result
   * @returns Array of suggestions
   */
  private generateSuggestions(userMessage: string, queryResult: any): string[] {
    const suggestions: string[] = [];

    // Add suggestions based on query results
    if (queryResult.documents.length > 0) {
      const categories = new Set<string>();

      for (const doc of queryResult.documents) {
        if (doc.metadata.category) {
          categories.add(doc.metadata.category);
        }
      }

      // Add category-based suggestions
      for (const category of categories) {
        if (category === 'vaporizers' || category === 'vapes') {
          suggestions.push('Show vaporizers');
        } else if (category === 'cbd') {
          suggestions.push('Learn about CBD');
        } else if (category === 'cleaning') {
          suggestions.push('Cleaning tips');
        } else if (category === 'bongs') {
          suggestions.push('Show bongs');
        }
      }
    }

    // Add generic suggestions
    if (suggestions.length < 3) {
      const genericSuggestions = [
        'Show popular products',
        'How to clean a bong',
        'CBD vs THC',
        'Vaporizer buying guide',
        'Contact support'
      ];

      // Add generic suggestions until we have 3
      for (const suggestion of genericSuggestions) {
        if (!suggestions.includes(suggestion)) {
          suggestions.push(suggestion);

          if (suggestions.length >= 3) {
            break;
          }
        }
      }
    }

    return suggestions;
  }
}
