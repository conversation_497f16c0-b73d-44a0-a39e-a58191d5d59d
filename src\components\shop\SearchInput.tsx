import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';

interface SearchInputProps {
  initialValue: string;
  onSearch: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const SearchInput: React.FC<SearchInputProps> = ({
  initialValue = '',
  onSearch,
  placeholder = 'Search products...',
  className = '',
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const debounceTimerRef = useRef<number>();
  
  // Update local state if the initial value changes
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Update the input value immediately for responsive UI
    setInputValue(value);
    
    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    // Set a new timer to trigger the search after typing stops
    debounceTimerRef.current = window.setTimeout(() => {
      onSearch(value);
    }, 500); // 500ms debounce
  };
  
  return (
    <Input
      type="text"
      placeholder={placeholder}
      value={inputValue}
      onChange={handleInputChange}
      className={className}
    />
  );
};

export default SearchInput;
