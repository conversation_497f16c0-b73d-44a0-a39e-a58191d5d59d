/**
 * SourceTypes.ts
 * 
 * Type definitions for retailer source configurations
 * Used by the SourceManager to manage scraping sources
 */

/**
 * Source rate limit configuration
 */
export interface SourceRateLimit {
  /**
   * Maximum requests per minute
   */
  requests_per_minute: number;
  
  /**
   * Delay between requests in milliseconds
   */
  delay_between_requests: number;
}

/**
 * Source selectors for scraping
 */
export interface SourceSelectors {
  /**
   * CSS selectors for product images
   * Multiple selectors can be provided for fallback
   */
  product_images: string[];
  
  /**
   * CSS selectors for product titles
   * Multiple selectors can be provided for fallback
   */
  product_titles: string[];
  
  /**
   * CSS selectors for product links
   * Multiple selectors can be provided for fallback
   */
  product_links: string[];
}

/**
 * Source category
 */
export enum SourceCategory {
  SEEDS = 'seeds',
  CBD = 'cbd',
  VAPORIZERS = 'vaporizers',
  ACCESSORIES = 'accessories',
  GENERAL = 'general'
}

/**
 * Source configuration for a retailer
 */
export interface SourceConfig {
  /**
   * Unique name of the source
   */
  name: string;
  
  /**
   * Base URL of the retailer website
   */
  base_url: string;
  
  /**
   * Path for search functionality
   * Can include placeholders like {query} and {category}
   */
  search_path: string;
  
  /**
   * CSS selectors for scraping
   */
  selectors: SourceSelectors;
  
  /**
   * Rate limit configuration
   */
  rate_limit: SourceRateLimit;
  
  /**
   * Categories that this source is relevant for
   */
  categories: string[];
  
  /**
   * Reliability score (0-10)
   * Higher scores indicate more reliable sources
   */
  reliability_score: number;
  
  /**
   * Optional authentication configuration
   */
  auth?: {
    /**
     * Whether authentication is required
     */
    required: boolean;
    
    /**
     * Authentication type
     */
    type?: 'none' | 'basic' | 'cookie' | 'token';
    
    /**
     * Authentication credentials
     */
    credentials?: {
      username?: string;
      password?: string;
      token?: string;
    };
  };
  
  /**
   * Optional proxy configuration
   */
  proxy?: {
    /**
     * Whether to use a proxy
     */
    use: boolean;
    
    /**
     * Proxy URL
     */
    url?: string;
  };
  
  /**
   * Optional pagination configuration
   */
  pagination?: {
    /**
     * Whether pagination is supported
     */
    supported: boolean;
    
    /**
     * Pagination type
     */
    type?: 'page' | 'infinite' | 'load_more';
    
    /**
     * CSS selector for next page button
     */
    next_selector?: string;
    
    /**
     * Maximum pages to scrape
     */
    max_pages?: number;
  };
}
