// Script to verify shipping methods filtering in the UI
import { createClient } from '@supabase/supabase-js';

// Use the same credentials as the app
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testShippingMethodFiltering() {
  console.log('🧪 Testing shipping method filtering...');
  
  try {
    // 1. First, log all shipping methods with their active status
    console.log('Fetching all shipping methods...');
    const { data: allMethods, error: allMethodsError } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (allMethodsError) {
      console.error('Error fetching all methods:', allMethodsError);
      return;
    }
    
    console.log(`Found ${allMethods.length} total shipping methods`);
    
    // Log active statuses for all methods
    console.log('\nShipping Method Active Status:');
    allMethods.forEach(method => {
      console.log(`- ${method.name} (ID: ${method.id}): is_active = ${method.is_active} (${typeof method.is_active})`);
    });
    
    // 2. Test different filtering scenarios
    console.log('\n🔎 Testing Different Filtering Scenarios:');
    
    // Our fixed filtering should only exclude methods where is_active is explicitly false
    const filteredMethods1 = allMethods.filter(method => method.is_active !== false);
    console.log(`\nFiltering with 'is_active !== false' includes ${filteredMethods1.length} methods:`);
    filteredMethods1.forEach(method => {
      console.log(`- ${method.name} (is_active=${method.is_active})`);
    });
    
    // The strict filter that was causing problems
    const filteredMethods2 = allMethods.filter(method => method.is_active === true);
    console.log(`\nFiltering with 'is_active === true' includes ${filteredMethods2.length} methods:`);
    filteredMethods2.forEach(method => {
      console.log(`- ${method.name} (is_active=${method.is_active})`);
    });
    
    // 3. Compare the two filtering methods to see which methods are being excluded
    if (filteredMethods1.length !== filteredMethods2.length) {
      console.log('\n⚠️ Differences between filtering methods:');
      const excludedMethods = filteredMethods1.filter(
        method1 => !filteredMethods2.some(method2 => method2.id === method1.id)
      );
      
      console.log(`Methods included with 'is_active !== false' but excluded with 'is_active === true':`);
      excludedMethods.forEach(method => {
        console.log(`- ${method.name} (is_active=${method.is_active}, type=${typeof method.is_active})`);
      });
    } else {
      console.log('\n✅ Both filtering methods yield the same results');
    }
    
    console.log('\n🔄 Testing Complete');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
testShippingMethodFiltering()
  .catch(err => {
    console.error('Error running script:', err);
  }); 