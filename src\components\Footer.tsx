
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin, ArrowRight, Heart, Store, Loader2 } from 'lucide-react';
import { checkEmailExists, subscribeToNewsletter } from '@/integrations/supabase/newsletter';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic email validation
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Check if email already exists
      const emailExists = await checkEmailExists(email);
      
      if (emailExists) {
        toast.info('This email is already subscribed to our newsletter.');
        setEmail('');
        return;
      }
      
      // Subscribe to the newsletter
      const result = await subscribeToNewsletter(email, undefined, undefined);
      
      if (result.error) {
        if (result.alreadySubscribed) {
          toast.info('This email is already subscribed to our newsletter.');
        } else {
          throw new Error(result.error);
        }
      } else {
        toast.success('Thank you for subscribing! Check your email for 10% off your first order.');
        setEmail('');
      }
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      toast.error('There was a problem subscribing you to our newsletter. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-b from-clay-800 to-clay-900 text-white pt-16 pb-8 relative overflow-hidden">
      {/* Animated cannabis leaf background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large background leaves */}
        {[...Array(20)].map((_, i) => {
          // Create different sizes for variety - larger sizes
          const size = 30 + Math.floor(Math.random() * 40);
          // Create different rotation angles
          const rotation = Math.floor(Math.random() * 360);
          // Different animation speeds
          const duration = 10 + Math.random() * 15;
          // Different starting positions
          const top = Math.random() * 100;
          const left = Math.random() * 100;
          
          return (
            <motion.img
              key={`large-${i}`}
              src="/images/Cannabis_leaf.svg"
              alt=""
              className="absolute"
              style={{
                top: `${top}%`,
                left: `${left}%`,
                width: `${size}px`,
                height: `${size}px`,
                transform: `rotate(${rotation}deg)`,
                opacity: 0.15,
                filter: 'blur(1px)',
              }}
              animate={{
                y: [0, 20, 0],
                x: [-8, 8, -8],
                rotate: [rotation, rotation + 15, rotation],
                opacity: [0.15, 0.25, 0.15],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: duration,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 0.5,
              }}
            />
          );
        })}
        
        {/* Medium leaves with higher contrast */}
        {[...Array(15)].map((_, i) => {
          const size = 20 + Math.floor(Math.random() * 25);
          const rotation = Math.floor(Math.random() * 360);
          const duration = 8 + Math.random() * 12;
          const top = Math.random() * 100;
          const left = Math.random() * 100;
          
          return (
            <motion.img
              key={`medium-${i}`}
              src="/images/Cannabis_leaf.svg"
              alt=""
              className="absolute"
              style={{
                top: `${top}%`,
                left: `${left}%`,
                width: `${size}px`,
                height: `${size}px`,
                transform: `rotate(${rotation}deg)`,
                opacity: 0.25,
              }}
              animate={{
                y: [0, 15, 0],
                x: [-5, 5, -5],
                rotate: [rotation, rotation + 10, rotation],
                opacity: [0.25, 0.35, 0.25],
                scale: [1, 1.08, 1],
              }}
              transition={{
                duration: duration,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 0.3 + 0.2,
              }}
            />
          );
        })}
        
        {/* Small foreground leaves with highest contrast */}
        {[...Array(10)].map((_, i) => {
          const size = 10 + Math.floor(Math.random() * 15);
          const rotation = Math.floor(Math.random() * 360);
          const duration = 6 + Math.random() * 8;
          const top = Math.random() * 100;
          const left = Math.random() * 100;
          
          return (
            <motion.img
              key={`small-${i}`}
              src="/images/Cannabis_leaf.svg"
              alt=""
              className="absolute z-10"
              style={{
                top: `${top}%`,
                left: `${left}%`,
                width: `${size}px`,
                height: `${size}px`,
                transform: `rotate(${rotation}deg)`,
                opacity: 0.35,
              }}
              animate={{
                y: [0, 10, 0],
                x: [-3, 3, -3],
                rotate: [rotation, rotation + 20, rotation],
                opacity: [0.35, 0.5, 0.35],
                scale: [1, 1.15, 1],
              }}
              transition={{
                duration: duration,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 0.2,
              }}
            />
          );
        })}
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-12 gap-y-10 mb-16">
          {/* Logo and About */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link to="/" className="flex flex-col items-center md:items-start space-y-3 mb-6">
              <img 
                src="/images/logo1.png" 
                alt="Bits N Bongs Logo" 
                className="h-16 w-auto"
              />
              <img 
                src="/images/text-logo.png" 
                alt="Bits N Bongs" 
                className="h-8 w-auto"
              />
            </Link>
            <p className="text-gray-300 mb-4 text-center md:text-left">
              Premium CBD products and smoking accessories for the discerning enthusiast.
            </p>
            <p className="text-sm text-gray-400 text-center md:text-left">
              Must be 18+ to purchase. Always consume responsibly.
            </p>
          </motion.div>

          {/* Shop Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h3 className="text-xl font-semibold mb-5 flex items-center">
              <span className="w-1.5 h-6 bg-sage-500 rounded-full mr-2"></span>
              Shop
            </h3>
            <ul className="space-y-3">
              {[
                { path: "/category/cbd", label: "CBD Products" },
                { path: "/category/bongs", label: "Bongs & Water Pipes" },
                { path: "/category/pipes", label: "Hand Pipes" },
                { path: "/category/papers", label: "Rolling Papers" },
                { path: "/category/vaporizers", label: "Vaporizers" },
                { path: "/category/accessories", label: "Accessories" }
              ].map((item, index) => (
                <li key={index}>
                  <Link 
                    to={item.path} 
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transform -translate-x-2 group-hover:translate-x-0 transition-all duration-200" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-xl font-semibold mb-5 flex items-center">
              <span className="w-1.5 h-6 bg-sage-500 rounded-full mr-2"></span>
              Information
            </h3>
            <ul className="space-y-3 grid grid-cols-1">
              {[
                { path: "/blog", label: "Blog" },
                { path: "/faq", label: "FAQ" },
                { path: "/about", label: "About Us" },
                { path: "/contact", label: "Contact Us" },
                { path: "/shipping-policy", label: "Shipping Policy" },
                { path: "/returns-policy", label: "Returns & Refunds" },
                { path: "/terms-of-service", label: "Terms of Service" },
                { path: "/privacy-policy", label: "Privacy Policy" }
              ].map((item, index) => (
                <li key={index}>
                  <Link 
                    to={item.path} 
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                  >
                    <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transform -translate-x-2 group-hover:translate-x-0 transition-all duration-200" />
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Newsletter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h3 className="text-xl font-semibold mb-5 flex items-center">
              <span className="w-1.5 h-6 bg-sage-500 rounded-full mr-2"></span>
              Newsletter
            </h3>
            <p className="text-gray-300 mb-4">
              Subscribe for product updates, educational content, and discounts.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-3">
              <div className="relative">
                <Input
                  type="email"
                  placeholder="Your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-clay-800/50 border-clay-700/50 text-white pl-4 pr-10 py-2 rounded-lg focus:ring-1 focus:ring-sage-500 focus:border-sage-500 transition-all duration-200"
                  disabled={isSubmitting}
                  required
                />
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>
              <Button 
                type="submit" 
                className="w-full bg-sage-500 hover:bg-sage-600 text-white font-medium py-2 rounded-lg transition-all duration-200 relative overflow-hidden group"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Subscribing...
                  </span>
                ) : (
                  <>
                    <span className="relative z-10">Subscribe for 10% Off</span>
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:animate-shimmer"></span>
                  </>
                )}
              </Button>
            </form>
          </motion.div>
        </div>

        {/* Contact & Social Section */}
        <div className="bg-clay-800/40 backdrop-blur-sm rounded-xl p-6 mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
            <div className="flex flex-col items-center md:items-start space-y-3">
              <h4 className="text-lg font-medium text-white flex items-center">
                <Phone className="h-4 w-4 mr-2 text-sage-400" />
                Contact Us
              </h4>
              <a 
                href="tel:01417373717" 
                className="text-gray-300 hover:text-white transition-colors duration-200"
              >
                0141 737 3717
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="text-gray-300 hover:text-white transition-colors duration-200"
              >
                <EMAIL>
              </a>
            </div>
            
            <div className="flex flex-col items-center space-y-3">
              <h4 className="text-lg font-medium text-white flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-sage-400" />
                Location
              </h4>
              <p className="text-gray-300 text-center">
                78 Stanley St<br />
                Kinning Park, Glasgow G41 1JH
              </p>
            </div>
            
            <div className="flex flex-col items-center md:items-end space-y-3">
              <h4 className="text-lg font-medium text-white">Follow Us</h4>
              <div className="flex space-x-4">
                <motion.a 
                  href="https://www.facebook.com/Bitsnbongsuk/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="bg-blue-600 p-2.5 rounded-full text-white hover:bg-blue-700 transition-all duration-200"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="sr-only">Facebook</span>
                  <Facebook className="h-5 w-5" />
                </motion.a>
                <motion.a 
                  href="https://www.instagram.com/bitsandbongs/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="bg-gradient-to-r from-pink-500 via-purple-500 to-orange-500 p-2.5 rounded-full text-white transition-all duration-200"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="sr-only">Instagram</span>
                  <Instagram className="h-5 w-5" />
                </motion.a>
                <motion.a 
                  href="https://www.etsy.com/uk/shop/BitsNBongsUK" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="bg-orange-600 p-2.5 rounded-full text-white hover:bg-orange-700 transition-all duration-200"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="sr-only">Etsy</span>
                  <Store className="h-5 w-5" />
                </motion.a>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-clay-700/30 pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <motion.p 
            className="text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            &copy; {currentYear} BITS N BONGS. All rights reserved.
          </motion.p>

          <motion.div 
            className="flex space-x-6 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <Link to="/privacy-policy" className="text-gray-400 hover:text-white transition-colors duration-200">Privacy</Link>
            <Link to="/terms-of-service" className="text-gray-400 hover:text-white transition-colors duration-200">Terms</Link>
            <Link to="/shipping-policy" className="text-gray-400 hover:text-white transition-colors duration-200">Shipping</Link>
            <Link to="/returns-policy" className="text-gray-400 hover:text-white transition-colors duration-200">Returns</Link>
          </motion.div>
        </div>

        <motion.div 
          className="mt-8 text-xs text-gray-500 text-center max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <p>Disclaimer: These products are not intended to diagnose, treat, cure or prevent any disease. All information presented here is for informational purposes only.</p>
          <p className="mt-2">BITS N BONGS CBD products contain less than 0.2% THC in accordance with UK law. Products are not for use by persons under the age of 18.</p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
