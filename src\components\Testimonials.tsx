
import { useState } from 'react';
import { Star, MessageSquare, ThumbsUp } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { motion } from 'framer-motion';

interface Testimonial {
  id: string;
  name: string;
  location?: string;
  rating: number;
  text: string;
  image?: string;
  date?: string;
  reviewCount?: number;
  photoCount?: number;
}

interface TestimonialsProps {
  testimonials?: Testimonial[];
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON><PERSON> <PERSON>',
    rating: 5,
    date: 'a month ago',
    reviewCount: 4,
    text: "Decent shop! Boy in there is brand new! Helped me out with getting a Puffco Pivot and the Puffco hot knife! Even gave me a freebie 👌 👌"
  },
  {
    id: '2',
    name: '<PERSON>',
    rating: 5,
    date: '4 months ago',
    reviewCount: 8,
    photoCount: 4,
    text: "First class. Dont go anywhere else for ur smoking essentials. They have everything. Well worth a visit."
  },
  {
    id: '3',
    name: '<PERSON>',
    rating: 5,
    date: '6 months ago',
    reviewCount: 67,
    photoCount: 126,
    text: "Fair impressed with this shop. All your smoking accessories under 1 roof 👌 👌"
  },
  {
    id: '4',
    name: '<PERSON>',
    rating: 5,
    date: '3 years ago',
    reviewCount: 1,
    text: "Such a great shop. Went in today and got everything I needed. The guys were very welcoming and kind, helping me find exactly what I wanted. Accommodating my pickiness isn't easy as well haha. I will be back soon for more! 😊"
  },
  {
    id: '5',
    name: 'Andrew Brennan',
    rating: 5,
    date: '3 years ago',
    reviewCount: 3,
    photoCount: 1,
    text: "Great shop, good vibes throughout visit, made to feel welcome. Staff also were very helpful and had extensive knowledge of products. Looking forward to my next visit."
  },
  {
    id: '6',
    name: 'Collective Studios',
    rating: 5,
    date: '11 months ago',
    reviewCount: 2,
    photoCount: 1,
    text: "Very honest, very helpful. Thank you for being so helpful for all my needs at an unimaginable price. Honest man"
  },
  {
    id: '7',
    name: 'Ad Bc',
    rating: 5,
    date: 'a year ago',
    reviewCount: 3,
    text: "The best in Glasgow for all essentials, good selection of Roor glass and top brands like puffco and pax stocked here. Class"
  },
  {
    id: '8',
    name: 'Lauren',
    rating: 5,
    date: 'a year ago',
    reviewCount: 2,
    photoCount: 2,
    text: "Super helpful staff, great selection and good quality prices. I got a few new bongs, a bubbler and a grinder from here and the staff were great at helping. The owner also makes their own cool drip art and trays too"
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12
    }
  }
};

const Testimonials = ({ testimonials = defaultTestimonials }: TestimonialsProps) => {
  // Changed initial visible count from 3 to 6
  const [visibleCount, setVisibleCount] = useState(6);
  
  const handleLoadMore = () => {
    setVisibleCount(prevCount => Math.min(prevCount + 3, testimonials.length));
  };

  const visibleTestimonials = testimonials.slice(0, visibleCount);

  return (
    <section className="py-16 bg-white">
      <div className="container-custom">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-10"
        >
          <h2 className="section-heading">Customer Reviews</h2>
          <div className="flex items-center justify-center mb-4">
            <div className="text-4xl font-bold text-clay-900 mr-2">4.9</div>
            <div className="flex items-center text-amber-400">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star key={i} className="h-6 w-6 fill-current" />
              ))}
            </div>
          </div>
          <p className="text-clay-700 max-w-2xl mx-auto">
            Based on 70+ reviews from our satisfied customers
          </p>
        </motion.div>
        
        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {visibleTestimonials.map((testimonial) => (
            <motion.div 
              key={testimonial.id}
              variants={itemVariants}
              whileHover={{ 
                scale: 1.02, 
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
              }}
              className="p-6 rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300"
            >
              <div className="flex items-center mb-4">
                <Avatar className="h-12 w-12 mr-4 border-2 border-primary">
                  <AvatarFallback className="bg-sage-200 text-sage-600 font-bold text-lg">
                    {testimonial.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-medium text-clay-900">{testimonial.name}</div>
                  <div className="flex items-center text-sm text-gray-500">
                    {testimonial.date}
                    {(testimonial.reviewCount || testimonial.photoCount) && (
                      <span className="mx-1">•</span>
                    )}
                    {testimonial.reviewCount && (
                      <span className="mr-2">{testimonial.reviewCount} reviews</span>
                    )}
                    {testimonial.photoCount && (
                      <span>{testimonial.photoCount} photos</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center text-amber-400 mb-3">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star 
                    key={i}
                    className={`h-4 w-4 ${i < testimonial.rating ? 'fill-current' : ''}`}
                  />
                ))}
              </div>
              
              <p className="text-gray-700 mb-4">{testimonial.text}</p>
              
              <div className="flex items-center text-gray-500 text-sm">
                <div className="flex items-center mr-4">
                  <ThumbsUp className="h-4 w-4 mr-1" />
                  <span>Helpful</span>
                </div>
                <div className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  <span>Comment</span>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        {visibleCount < testimonials.length && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center mt-10"
          >
            <button 
              onClick={handleLoadMore}
              className="btn-outline"
            >
              Load More Reviews
            </button>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default Testimonials;
