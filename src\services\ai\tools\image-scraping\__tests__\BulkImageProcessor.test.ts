/**
 * BulkImageProcessor.test.ts
 * 
 * Tests for the BulkImageProcessor component
 */

import { BulkImageProcessor } from '../BulkImageProcessor';
import { PlaywrightImageScraper } from '../PlaywrightImageScraper';
import { ImageQualityAssessor } from '../ImageQualityAssessor';
import { SourceManager } from '../SourceManager';
import { ProductImage } from '../types/ImageScrapingTypes';

// Mock dependencies
jest.mock('../PlaywrightImageScraper');
jest.mock('../ImageQualityAssessor');
jest.mock('../SourceManager');

describe('BulkImageProcessor', () => {
  let bulkProcessor: BulkImageProcessor;
  let mockScraper: jest.Mocked<PlaywrightImageScraper>;
  let mockQualityAssessor: jest.Mocked<ImageQualityAssessor>;
  let mockSourceManager: jest.Mocked<SourceManager>;

  beforeEach(() => {
    // Create mocks
    mockScraper = new PlaywrightImageScraper(null, []) as jest.Mocked<PlaywrightImageScraper>;
    mockQualityAssessor = new ImageQualityAssessor() as jest.Mocked<ImageQualityAssessor>;
    mockSourceManager = new SourceManager() as jest.Mocked<SourceManager>;
    
    // Setup mock implementations
    mockScraper.findProductImages = jest.fn();
    
    // Create bulk processor with small batch size for testing
    bulkProcessor = new BulkImageProcessor(
      mockScraper,
      mockQualityAssessor,
      mockSourceManager,
      {
        batchSize: 2,
        delayBetweenBatches: 100,
        retryAttempts: 1
      }
    );
  });

  describe('processProducts', () => {
    it('should process products in batches', async () => {
      // Setup mock to return images for each product
      mockScraper.findProductImages.mockImplementation((product) => {
        const images: ProductImage[] = [
          {
            url: `https://example.com/${product.id}-image1.jpg`,
            alt: product.name,
            quality_score: 80,
            source: 'Test Source'
          },
          {
            url: `https://example.com/${product.id}-image2.jpg`,
            alt: product.name,
            quality_score: 70,
            source: 'Test Source'
          }
        ];
        
        return Promise.resolve(images);
      });
      
      // Define test products
      const products = [
        { name: 'Product 1', category: 'test', id: '1' },
        { name: 'Product 2', category: 'test', id: '2' },
        { name: 'Product 3', category: 'test', id: '3' }
      ];
      
      // Process products
      const report = await bulkProcessor.processProducts(products);
      
      // Check report
      expect(report).toBeDefined();
      expect(report.total_products).toBe(3);
      expect(report.successful_products).toBe(3);
      expect(report.failed_products).toBe(0);
      expect(report.total_images_found).toBe(6); // 2 images per product
      expect(report.average_images_per_product).toBe(2);
      expect(report.processing_time).toBeGreaterThan(0);
      expect(report.cost_savings).toBeGreaterThan(0);
      expect(report.success_details.length).toBe(3);
      expect(report.errors.length).toBe(0);
      
      // Check that scraper was called for each product
      expect(mockScraper.findProductImages).toHaveBeenCalledTimes(3);
    });

    it('should handle failed products', async () => {
      // Setup mock to succeed for first product and fail for others
      mockScraper.findProductImages.mockImplementation((product) => {
        if (product.id === '1') {
          const images: ProductImage[] = [
            {
              url: `https://example.com/${product.id}-image1.jpg`,
              alt: product.name,
              quality_score: 80,
              source: 'Test Source'
            }
          ];
          
          return Promise.resolve(images);
        } else {
          return Promise.reject(new Error(`Failed to find images for ${product.name}`));
        }
      });
      
      // Define test products
      const products = [
        { name: 'Product 1', category: 'test', id: '1' },
        { name: 'Product 2', category: 'test', id: '2' },
        { name: 'Product 3', category: 'test', id: '3' }
      ];
      
      // Process products
      const report = await bulkProcessor.processProducts(products);
      
      // Check report
      expect(report).toBeDefined();
      expect(report.total_products).toBe(3);
      expect(report.successful_products).toBe(1);
      expect(report.failed_products).toBe(2);
      expect(report.total_images_found).toBe(1);
      expect(report.average_images_per_product).toBe(1);
      expect(report.errors.length).toBe(2);
      expect(report.success_details.length).toBe(1);
      
      // Check error details
      expect(report.errors[0].product_id).toBe('2');
      expect(report.errors[0].product_name).toBe('Product 2');
      expect(report.errors[0].error).toContain('Failed to find images for Product 2');
      
      // Check success details
      expect(report.success_details[0].product_id).toBe('1');
      expect(report.success_details[0].product_name).toBe('Product 1');
      expect(report.success_details[0].images_found).toBe(1);
    });

    it('should handle empty product list', async () => {
      // Process empty product list
      const report = await bulkProcessor.processProducts([]);
      
      // Check report
      expect(report).toBeDefined();
      expect(report.total_products).toBe(0);
      expect(report.successful_products).toBe(0);
      expect(report.failed_products).toBe(0);
      expect(report.total_images_found).toBe(0);
      expect(report.average_images_per_product).toBe(0);
      expect(report.errors.length).toBe(0);
      expect(report.success_details.length).toBe(0);
      
      // Check that scraper was not called
      expect(mockScraper.findProductImages).not.toHaveBeenCalled();
    });

    it('should respect search options', async () => {
      // Setup mock to return images
      mockScraper.findProductImages.mockResolvedValue([
        {
          url: 'https://example.com/test-image.jpg',
          alt: 'Test Image',
          quality_score: 80,
          source: 'Test Source'
        }
      ]);
      
      // Define test products
      const products = [
        { name: 'Product 1', category: 'test', id: '1' }
      ];
      
      // Define search options
      const options = {
        max_images: 3,
        min_quality_score: 70,
        preferred_sources: ['Preferred Source']
      };
      
      // Process products with options
      await bulkProcessor.processProducts(products, options);
      
      // Check that scraper was called with options
      expect(mockScraper.findProductImages).toHaveBeenCalledWith(
        products[0],
        expect.objectContaining(options)
      );
    });
  });
});
