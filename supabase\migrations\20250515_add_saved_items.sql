-- Create the saved_items table
CREATE TABLE IF NOT EXISTS public.saved_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Ensure a user can only save a product once
    CONSTRAINT saved_items_user_product_unique UNIQUE (user_id, product_id)
);

-- Add RLS policies
ALTER TABLE public.saved_items ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to view their own saved items
CREATE POLICY "Users can view their own saved items" 
    ON public.saved_items 
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Policy to allow users to insert their own saved items
CREATE POLICY "Users can add their own saved items" 
    ON public.saved_items 
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to delete their own saved items
CREATE POLICY "Users can delete their own saved items" 
    ON public.saved_items 
    FOR DELETE 
    USING (auth.uid() = user_id);

-- Add saved_items to public schema
GRANT ALL ON public.saved_items TO postgres, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.saved_items TO authenticated;
GRANT SELECT ON public.saved_items TO anon;
