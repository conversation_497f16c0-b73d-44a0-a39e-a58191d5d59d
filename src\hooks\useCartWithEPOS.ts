import { useState, useEffect } from 'react';
import { useCart } from '@/hooks/useCart';
import { useEPOSStockCheck } from '@/hooks/useEPOSIntegration';
import { toast } from '@/components/ui/use-toast';

/**
 * Enhanced cart hook that integrates with EPOS for real-time stock checking
 */
export function useCartWithEPOS() {
  // Get the base cart functionality
  const cart = useCart();
  
  // Get EPOS stock checking functionality
  const { checkProductStock } = useEPOSStockCheck();
  
  // State to track items with insufficient stock
  const [outOfStockItems, setOutOfStockItems] = useState<string[]>([]);
  const [isCheckingStock, setIsCheckingStock] = useState(false);
  
  // Check stock levels when cart items change
  useEffect(() => {
    const validateStock = async () => {
      if (!cart.items || cart.items.length === 0) {
        setOutOfStockItems([]);
        return;
      }
      
      setIsCheckingStock(true);
      try {
        const stockChecks = await Promise.all(
          cart.items.map(async (item) => {
            const isInStock = await checkProductStock(item.id, item.quantity);
            return { id: item.id, isInStock };
          })
        );
        
        const outOfStock = stockChecks
          .filter(item => !item.isInStock)
          .map(item => item.id);
          
        setOutOfStockItems(outOfStock);
        
        // Notify user if items are out of stock
        if (outOfStock.length > 0) {
          const outOfStockNames = cart.items
            .filter(item => outOfStock.includes(item.id))
            .map(item => item.name);
            
          toast({
            title: "Stock Alert",
            description: `Some items have insufficient stock: ${outOfStockNames.join(', ')}`,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error validating stock:', error);
      } finally {
        setIsCheckingStock(false);
      }
    };
    
    validateStock();
  }, [cart.items]);
  
  // Enhanced checkout function that validates stock before proceeding
  const checkoutWithStockValidation = async () => {
    setIsCheckingStock(true);
    try {
      // Perform a final stock check before checkout
      const stockChecks = await Promise.all(
        cart.items.map(async (item) => {
          const isInStock = await checkProductStock(item.id, item.quantity);
          return { id: item.id, isInStock };
        })
      );
      
      const outOfStock = stockChecks
        .filter(item => !item.isInStock)
        .map(item => item.id);
        
      setOutOfStockItems(outOfStock);
      
      if (outOfStock.length > 0) {
        const outOfStockNames = cart.items
          .filter(item => outOfStock.includes(item.id))
          .map(item => item.name);
          
        toast({
          title: "Cannot Checkout",
          description: `Some items have insufficient stock: ${outOfStockNames.join(', ')}`,
          variant: "destructive",
        });
        
        return false;
      }
      
      // If all items are in stock, proceed with checkout
      return cart.checkout();
    } catch (error) {
      console.error('Error during checkout stock validation:', error);
      toast({
        title: "Checkout Error",
        description: "There was an error validating stock. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsCheckingStock(false);
    }
  };
  
  return {
    ...cart,
    outOfStockItems,
    isCheckingStock,
    isItemOutOfStock: (id: string) => outOfStockItems.includes(id),
    checkout: checkoutWithStockValidation,
  };
}