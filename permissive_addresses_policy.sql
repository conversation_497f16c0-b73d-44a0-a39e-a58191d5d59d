-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own addresses" ON public.addresses;
DROP POLICY IF EXISTS "Users can insert their own addresses" ON public.addresses;
DROP POLICY IF EXISTS "Users can update their own addresses" ON public.addresses;
DROP POLICY IF EXISTS "Users can delete their own addresses" ON public.addresses;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON public.addresses;

-- Make sure RLS is enabled
ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;

-- Create a more permissive policy for testing
CREATE POLICY "Enable all operations for authenticated users"
    ON public.addresses
    FOR ALL
    USING (auth.role() = 'authenticated');

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.addresses TO authenticated;
