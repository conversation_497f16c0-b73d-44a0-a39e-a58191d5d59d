import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { supabaseAdmin } from '@/utils/supabaseAdmin';
import { Profile } from '@/types/database';
import { useAuth } from '@/hooks/auth.basic';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { UserCheck, UserX, Trash2, PlusCircle, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

type ProfileWithEmail = Profile & {
  email?: string;
  is_pending?: boolean;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
};

const UsersPage = () => {
  const [users, setUsers] = useState<ProfileWithEmail[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newUserData, setNewUserData] = useState({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    is_admin: false
  });
  const { toast } = useToast();
  const { user, profile } = useAuth(); // Get current user and profile from auth context

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('Fetching users...');
      
      // Get all users from auth.users (requires admin privileges)
      // Using supabaseAdmin client which has the service role key
      const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
      
      if (authError) {
        console.error('Error fetching auth users:', authError);
        // Fall back to just profiles if admin API fails
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('*');
        
        if (profilesError) {
          throw profilesError;
        }
        
        if (profilesData) {
          console.log('Profiles data (fallback):', profilesData);
          setUsers(profilesData as ProfileWithEmail[]);
        }
        return;
      }
      
      // Get profiles directly from the profiles table
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*');
      
      console.log('Profiles data:', profilesData);
      
      if (profilesError) {
        throw profilesError;
      }
      
      // Combine auth users with profiles
      const combinedUsers: ProfileWithEmail[] = [];
      
      // First add all profiles
      if (profilesData) {
        combinedUsers.push(...(profilesData as ProfileWithEmail[]));
      }
      
      // Add any missing users from auth
      if (authUsers?.users) {
        for (const authUser of authUsers.users) {
          // Check if this user is already in the profiles
          const existingProfile = combinedUsers.find(p => p.id === authUser.id);
          if (!existingProfile) {
            // Add a basic profile for this user
            combinedUsers.push({
              id: authUser.id,
              email: authUser.email,
              first_name: authUser.user_metadata?.first_name || '',
              last_name: authUser.user_metadata?.last_name || '',
              is_admin: false,
              is_active: true,
              created_at: authUser.created_at,
              updated_at: authUser.updated_at || authUser.created_at
            });
          } else if (!existingProfile.email) {
            // Update the email if it's missing
            existingProfile.email = authUser.email;
          }
        }
      }
      
      console.log('Combined users:', combinedUsers);
      setUsers(combinedUsers);
    } catch (error: any) {
      console.error('Error fetching users:', error.message);
      toast({
        title: 'Error',
        description: `Failed to fetch users: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleAdminStatus = async (userId: string, isAdmin: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_admin: !isAdmin })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(users.map(user => 
        user.id === userId ? { ...user, is_admin: !isAdmin } : user
      ));

      toast({
        title: 'Success',
        description: `User is now ${!isAdmin ? 'an admin' : 'a regular user'}.`,
      });
    } catch (error: any) {
      console.error('Error toggling admin status:', error.message);
      toast({
        title: 'Error',
        description: `Failed to update user: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const deleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      // Since we can't directly delete users without admin privileges,
      // we'll mark the user as inactive in the profiles table
      const { error } = await supabase
        .from('profiles')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(users.filter(user => user.id !== userId));

      toast({
        title: 'Success',
        description: 'User deactivated successfully.',
      });
    } catch (error: any) {
      console.error('Error deactivating user:', error.message);
      toast({
        title: 'Error',
        description: `Failed to deactivate user: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setNewUserData({
      ...newUserData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleAddUser = async () => {
    if (!newUserData.email || !newUserData.password) {
      toast({
        title: 'Error',
        description: 'Email and password are required.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('Creating new user with data:', newUserData);

      // Make sure the password is at least 6 characters
      if (newUserData.password.length < 6) {
        toast({
          title: 'Error',
          description: 'Password must be at least 6 characters.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Create a unique email with timestamp to avoid conflicts
      const timestamp = new Date().getTime();
      const email = newUserData.email.includes('@') 
        ? newUserData.email 
        : `${newUserData.email.replace(/[^a-zA-Z0-9]/g, '')}_${timestamp}@example.com`;

      console.log('Using email:', email);

      // Create a temporary user in the UI
      const tempUser = {
        id: `pending_${timestamp}`,
        email: email,
        first_name: newUserData.first_name || '',
        last_name: newUserData.last_name || '',
        is_admin: newUserData.is_admin || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_pending: true
      };
      
      setUsers([...users, tempUser]);
      
      // Show instructions to the admin
      toast({
        title: 'Admin Note Added',
        description: `Added ${email} to the admin list. The user needs to sign up with this email, then you can grant them admin privileges.`,
        duration: 8000,
      });
      
      // Reset form and close dialog
      setNewUserData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        is_admin: false
      });
      setIsAddUserDialogOpen(false);
    } catch (error: any) {
      console.error('Error creating user:', error.message);
      toast({
        title: 'Error',
        description: `Failed to create user: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setIsAddUserDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((userProfile) => {
                // Skip rendering the current user (admin)
                if (user && userProfile.id === user.id) return null;
                
                return (
                  <TableRow key={userProfile.id}>
                    <TableCell>{userProfile.email || 'No Email'}</TableCell>
                    <TableCell>
                      {userProfile.first_name || ''} {userProfile.last_name || ''}
                    </TableCell>
                    <TableCell>
                      {userProfile.is_pending ? (
                        <span className="flex items-center text-amber-500">
                          <UserX className="mr-1 h-4 w-4" />
                          Pending
                        </span>
                      ) : userProfile.is_active !== false ? (
                        <span className="flex items-center text-green-500">
                          <UserCheck className="mr-1 h-4 w-4" />
                          Active
                        </span>
                      ) : (
                        <span className="flex items-center text-red-500">
                          <UserX className="mr-1 h-4 w-4" />
                          Inactive
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={!!userProfile.is_admin}
                        onCheckedChange={() => toggleAdminStatus(userProfile.id, !!userProfile.is_admin)}
                        disabled={userProfile.is_pending}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleAdminStatus(userProfile.id, !!userProfile.is_admin)}
                          disabled={userProfile.is_pending}
                        >
                          {userProfile.is_admin ? 'Remove Admin' : 'Make Admin'}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => userProfile.is_pending 
                            ? setUsers(users.filter(u => u.id !== userProfile.id))
                            : deleteUser(userProfile.id)
                          }
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add User Dialog */}
      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account with optional admin privileges.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email <span className="text-red-500">*</span></Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={newUserData.email}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password <span className="text-red-500">*</span></Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="••••••••"
                value={newUserData.password}
                onChange={handleInputChange}
                required
              />
              <p className="text-xs text-muted-foreground">Password must be at least 6 characters long.</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  name="first_name"
                  value={newUserData.first_name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  name="last_name"
                  value={newUserData.last_name}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_admin"
                name="is_admin"
                checked={newUserData.is_admin}
                onCheckedChange={(checked) => setNewUserData({...newUserData, is_admin: checked})}
              />
              <Label htmlFor="is_admin">Admin User</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAddUser} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create User'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UsersPage;
