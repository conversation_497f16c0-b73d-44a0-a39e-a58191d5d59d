import { useState, useEffect } from "react";
import { Product, Category, Brand } from "@/types/database";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { Loader2, Wand2 } from "lucide-react";

interface BasicProductFormProps {
  product: Product | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function BasicProductForm({ product, onSuccess, onCancel }: BasicProductFormProps) {
  // Form state
  const [formData, setFormData] = useState<Partial<Product>>(product || {
    name: "",
    description: "",
    price: 0,
    stock_quantity: 0,
    category_id: "",
    subcategory_id: "",
    brand_id: "",
    in_stock: true,
    is_active: true
  });
  
  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  
  // Data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [filteredSubcategories, setFilteredSubcategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  
  // Fetch categories and brands on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from("categories")
          .select("*")
          .is("parent_id", null) // Only get main categories
          .order("display_order", { ascending: true });
          
        if (categoriesError) throw categoriesError;
        setCategories(categoriesData || []);
        
        // Fetch subcategories
        const { data: subcategoriesData, error: subcategoriesError } = await supabase
          .from("categories")
          .select("*")
          .not("parent_id", "is", null) // Only get subcategories
          .order("display_order", { ascending: true });
          
        if (subcategoriesError) throw subcategoriesError;
        setSubcategories(subcategoriesData || []);
        
        // Filter subcategories if we have a category selected
        if (formData.category_id) {
          const filtered = subcategoriesData?.filter(
            subcat => subcat.parent_id === formData.category_id
          ) || [];
          setFilteredSubcategories(filtered);
        }
        
        // Fetch brands
        const { data: brandsData, error: brandsError } = await supabase
          .from("brands")
          .select("*")
          .order("name", { ascending: true });
          
        if (brandsError) throw brandsError;
        setBrands(brandsData || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load categories and brands",
          variant: "destructive"
        });
      }
    };
    
    fetchData();
  }, [formData.category_id]);
  
  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    // Handle numeric fields
    if (type === "number") {
      setFormData({
        ...formData,
        [name]: value === "" ? null : parseFloat(value)
      });
    } else if (name === "category_id") {
      // When category changes, reset subcategory and filter subcategories
      setFormData({
        ...formData,
        [name]: value,
        subcategory_id: ""
      });
      
      // Filter subcategories for the selected category
      const filtered = subcategories.filter(
        subcat => subcat.parent_id === value
      );
      setFilteredSubcategories(filtered);
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate required fields
      if (!formData.name || !formData.price) {
        toast({
          title: "Validation Error",
          description: "Name and price are required",
          variant: "destructive"
        });
        setIsSubmitting(false);
        return;
      }
      
      // Generate slug if not provided
      if (!formData.slug && formData.name) {
        formData.slug = formData.name
          .toLowerCase()
          .replace(/[^\w\s-]/g, "")
          .replace(/[\s_-]+/g, "-")
          .replace(/^-+|-+$/g, "");
      }
      
      // Prepare data for saving
      const productData = {
        ...formData,
        subcategory_id: formData.subcategory_id || null,
        updated_at: new Date().toISOString()
      };
      
      if (!product) {
        productData.created_at = new Date().toISOString();
      }
      
      // Save to database
      let result;
      if (product?.id) {
        // Update existing product
        result = await supabase
          .from("products")
          .update(productData)
          .eq("id", product.id)
          .select()
          .single();
      } else {
        // Create new product
        result = await supabase
          .from("products")
          .insert(productData)
          .select()
          .single();
      }
      
      if (result.error) throw result.error;
      
      toast({
        title: "Success",
        description: product ? "Product updated successfully" : "Product created successfully"
      });
      
      onSuccess();
    } catch (error) {
      console.error("Error saving product:", error);
      toast({
        title: "Error",
        description: "Failed to save product",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle AI description generation
  const handleGenerateDescription = async () => {
    if (!formData.name) {
      toast({
        title: "Error",
        description: "Please enter a product name first",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingDescription(true);
    
    try {
      // Call the Edge Function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/product-ai`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          action: "generate_description",
          productName: formData.name,
          categoryName: categories.find(c => c.id === formData.category_id)?.name || ""
        })
      });
      
      if (!response.ok) {
        throw new Error(`API error: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.description) {
        setFormData({
          ...formData,
          description: data.description
        });
        
        toast({
          title: "Success",
          description: "Description generated successfully"
        });
      } else {
        throw new Error("No description returned");
      }
    } catch (error) {
      console.error("Error generating description:", error);
      toast({
        title: "Error",
        description: "Failed to generate description",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingDescription(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="name">Product Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name || ""}
                onChange={handleChange}
                placeholder="Enter product name"
              />
            </div>
            
            <div className="space-y-2 md:col-span-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="description">Description</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateDescription}
                  disabled={isGeneratingDescription}
                >
                  {isGeneratingDescription ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="mr-2 h-4 w-4" />
                      Generate with AI
                    </>
                  )}
                </Button>
              </div>
              <Textarea
                id="description"
                name="description"
                value={formData.description || ""}
                onChange={handleChange}
                placeholder="Enter product description"
                rows={5}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">Price (£)</Label>
              <Input
                id="price"
                name="price"
                type="number"
                min="0"
                step="0.01"
                value={formData.price || ""}
                onChange={handleChange}
                placeholder="0.00"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="stock_quantity">Stock Quantity</Label>
              <Input
                id="stock_quantity"
                name="stock_quantity"
                type="number"
                min="0"
                step="1"
                value={formData.stock_quantity || ""}
                onChange={handleChange}
                placeholder="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Organization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="category_id">Category</Label>
              <select
                id="category_id"
                name="category_id"
                value={formData.category_id || ""}
                onChange={handleChange}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            
            {formData.category_id && (
              <div className="space-y-2">
                <Label htmlFor="subcategory_id">Subcategory</Label>
                <select
                  id="subcategory_id"
                  name="subcategory_id"
                  value={formData.subcategory_id || ""}
                  onChange={handleChange}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">None</option>
                  {filteredSubcategories.map((subcategory) => (
                    <option key={subcategory.id} value={subcategory.id}>
                      {subcategory.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="brand_id">Brand</Label>
              <select
                id="brand_id"
                name="brand_id"
                value={formData.brand_id || ""}
                onChange={handleChange}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="in_stock"
                  name="in_stock"
                  checked={formData.in_stock || false}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="in_stock">In Stock</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active || false}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            product ? "Update Product" : "Create Product"
          )}
        </Button>
      </div>
    </form>
  );
}
