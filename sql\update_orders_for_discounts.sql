-- Add discount_code and discount_amount columns to orders table
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS discount_code TEXT,
ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0;

-- Create a function to apply discount to an order
CREATE OR REPLACE FUNCTION apply_discount_to_order(
  order_id UUID,
  discount_code_param TEXT,
  discount_amount_param DECIMAL(10, 2)
) RETURNS VOID AS $$
BEGIN
  -- Update the order with the discount information
  UPDATE orders
  SET 
    discount_code = discount_code_param,
    discount_amount = discount_amount_param,
    total_amount = total_amount - discount_amount_param
  WHERE id = order_id;
  
  -- Ensure total_amount doesn't go below 0
  UPDATE orders
  SET total_amount = 0
  WHERE id = order_id AND total_amount < 0;
END;
$$ LANGUAGE plpgsql;

-- Create a function to validate and apply a discount code
CREATE OR REPLACE FUNCTION validate_and_apply_discount(
  order_id UUID,
  discount_code_param TEXT
) RETURNS JSONB AS $$
DECLARE
  discount_record RECORD;
  order_total DECIMAL(10, 2);
  discount_amount DECIMAL(10, 2) := 0;
  result JSONB;
BEGIN
  -- Get the order total
  SELECT total_amount INTO order_total
  FROM orders
  WHERE id = order_id;
  
  IF order_total IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Order not found'
    );
  END IF;
  
  -- Get the discount code
  SELECT * INTO discount_record
  FROM discount_codes
  WHERE code = discount_code_param
    AND is_active = true;
  
  IF discount_record IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Invalid discount code'
    );
  END IF;
  
  -- Check if the code is expired
  IF discount_record.start_date IS NOT NULL AND discount_record.start_date > NOW() THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'This discount code is not active yet'
    );
  END IF;
  
  IF discount_record.end_date IS NOT NULL AND discount_record.end_date < NOW() THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'This discount code has expired'
    );
  END IF;
  
  -- Check usage limit
  IF discount_record.usage_limit IS NOT NULL AND discount_record.usage_count >= discount_record.usage_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'This discount code has reached its usage limit'
    );
  END IF;
  
  -- Check minimum order amount
  IF order_total < discount_record.minimum_order_amount THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'This discount code requires a minimum order of £' || discount_record.minimum_order_amount
    );
  END IF;
  
  -- Calculate the discount amount
  IF discount_record.discount_type = 'percentage' THEN
    discount_amount := (order_total * discount_record.discount_value) / 100;
  ELSE
    discount_amount := LEAST(discount_record.discount_value, order_total);
  END IF;
  
  -- Apply the discount to the order
  PERFORM apply_discount_to_order(order_id, discount_code_param, discount_amount);
  
  -- Increment usage count
  UPDATE discount_codes
  SET usage_count = usage_count + 1
  WHERE code = discount_code_param;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Discount applied successfully',
    'discount_code', discount_code_param,
    'discount_amount', discount_amount,
    'new_total', order_total - discount_amount
  );
END;
$$ LANGUAGE plpgsql;
