/**
 * DeepSeek AI Provider
 * 
 * Ported from automation project - robust, production-ready implementation
 * with retry logic, error handling, and multi-modal support
 */

import { BaseProvider, ProviderConfig, ProviderCapabilities } from './BaseProvider';
import { AIRequest, AIRequestOptions } from '../types/AIRequest';
import { AIResponse, AIProviderStatus } from '../types/AIResponse';

interface DeepSeekConfig extends ProviderConfig {
  base_url?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
}

interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: { url: string };
  }>;
}

interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepSeekProvider extends BaseProvider {
  private baseUrl: string;
  private model: string;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  
  constructor(config: DeepSeekConfig) {
    const capabilities: ProviderCapabilities = {
      supports_streaming: true,
      supports_images: true,
      supports_function_calling: true,
      supports_system_prompts: true,
      max_context_length: 32000,
      supported_formats: ['json', 'text', 'markdown']
    };
    
    super('deepseek', config, capabilities);
    
    this.baseUrl = config.base_url || 'https://api.deepseek.com/v1';
    this.model = config.model || 'deepseek-chat';
  }
  
  async processRequest(request: AIRequest, options?: AIRequestOptions): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.valid) {
        throw new Error(`Invalid request: ${validation.errors.join(', ')}`);
      }
      
      // Handle rate limiting
      await this.handleRateLimit();
      
      // Prepare messages
      const messages = this.prepareMessages(request);
      
      // Make API call with retry logic
      const response = await this.retryWithBackoff(async () => {
        return this.makeAPICall(messages, options);
      });
      
      // Track usage
      this.requestCount++;
      this.lastRequestTime = Date.now();
      
      // Calculate cost (DeepSeek pricing: ~$0.14 per 1M input tokens, ~$0.28 per 1M output tokens)
      const inputCost = (response.usage.prompt_tokens / 1000000) * 0.14;
      const outputCost = (response.usage.completion_tokens / 1000000) * 0.28;
      const totalCost = inputCost + outputCost;
      
      return {
        content: response.choices[0].message.content,
        success: true,
        provider: this.name,
        model: response.model,
        tokens_used: response.usage.total_tokens,
        cost: totalCost,
        processing_time: Date.now() - startTime,
        confidence_score: this.calculateConfidenceScore(response),
        request_id: request.request_id,
        response_id: response.id,
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('DeepSeek API error:', error);
      
      return {
        content: '',
        success: false,
        provider: this.name,
        processing_time: Date.now() - startTime,
        timestamp: new Date(),
        error: {
          code: 'DEEPSEEK_API_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }
      };
    }
  }
  
  async processStreamingRequest(request: AIRequest, options?: AIRequestOptions): Promise<ReadableStream<string>> {
    // Streaming implementation
    const messages = this.prepareMessages(request);
    
    return new ReadableStream({
      async start(controller) {
        try {
          const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.api_key}`
            },
            body: JSON.stringify({
              model: this.model,
              messages: messages,
              stream: true,
              max_tokens: options?.max_tokens || this.config.max_tokens || 2000,
              temperature: options?.temperature || this.config.temperature || 0.7
            })
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No response body');
          }
          
          const decoder = new TextDecoder();
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(content);
                  }
                } catch (e) {
                  // Skip invalid JSON
                }
              }
            }
          }
          
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }
  
  async checkHealth(): Promise<AIProviderStatus> {
    try {
      const testResponse = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.api_key}`
        }
      });
      
      return {
        provider: this.name,
        available: testResponse.ok,
        rate_limited: testResponse.status === 429,
        error_rate: 0, // Would track this over time
        average_response_time: 0, // Would track this over time
        last_check: new Date()
      };
      
    } catch (error) {
      return {
        provider: this.name,
        available: false,
        rate_limited: false,
        error_rate: 1,
        average_response_time: 0,
        last_check: new Date()
      };
    }
  }
  
  async getUsageStats(): Promise<{
    requests_today: number;
    tokens_today: number;
    cost_today: number;
    rate_limit_remaining: number;
  }> {
    // In production, this would query a usage tracking database
    return {
      requests_today: this.requestCount,
      tokens_today: 0, // Would track this
      cost_today: 0, // Would track this
      rate_limit_remaining: 1000 // DeepSeek rate limits
    };
  }
  
  async estimateCost(request: AIRequest): Promise<number> {
    // Rough estimation based on content length
    const estimatedInputTokens = Math.ceil(request.content.length / 4); // ~4 chars per token
    const estimatedOutputTokens = 500; // Average response length
    
    const inputCost = (estimatedInputTokens / 1000000) * 0.14;
    const outputCost = (estimatedOutputTokens / 1000000) * 0.28;
    
    return inputCost + outputCost;
  }
  
  private prepareMessages(request: AIRequest): DeepSeekMessage[] {
    const messages: DeepSeekMessage[] = [];
    
    // Add system prompt based on request type and context
    const systemPrompt = this.buildSystemPrompt(request);
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user message
    messages.push({
      role: 'user',
      content: request.content
    });
    
    return messages;
  }
  
  private buildSystemPrompt(request: AIRequest): string {
    const context = request.context;
    let prompt = '';
    
    // Base prompt based on request type
    switch (request.type) {
      case 'product_description':
        prompt = 'You are an expert e-commerce copywriter specializing in cannabis and CBD products. Create compelling, accurate product descriptions that highlight benefits and comply with regulations.';
        break;
      case 'blog_content':
        prompt = 'You are a professional content writer specializing in cannabis, CBD, and wellness topics. Create engaging, informative content that educates readers while maintaining compliance.';
        break;
      case 'social_media_post':
        prompt = 'You are a social media expert for cannabis and CBD brands. Create engaging, compliant social media content that drives engagement while following platform guidelines.';
        break;
      default:
        prompt = 'You are a helpful AI assistant specializing in cannabis and CBD e-commerce.';
    }
    
    // Add business context
    if (context?.business_type === 'cannabis') {
      prompt += ' Focus on cannabis products, ensuring all content is compliant with UK cannabis laws and regulations.';
    }
    
    // Add brand voice
    if (context?.brand_voice) {
      prompt += ` Write in a ${context.brand_voice.tone} tone with the following personality: ${context.brand_voice.personality}.`;
    }
    
    // Add compliance requirements
    if (context?.brand_voice?.compliance_requirements?.length) {
      prompt += ` Ensure compliance with: ${context.brand_voice.compliance_requirements.join(', ')}.`;
    }
    
    return prompt;
  }
  
  private async makeAPICall(messages: DeepSeekMessage[], options?: AIRequestOptions): Promise<DeepSeekResponse> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.api_key}`
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        max_tokens: options?.max_tokens || this.config.max_tokens || 2000,
        temperature: options?.temperature || this.config.temperature || 0.7,
        top_p: options?.top_p || 0.9,
        frequency_penalty: options?.frequency_penalty || 0,
        presence_penalty: options?.presence_penalty || 0,
        stop: options?.stop_sequences
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    return response.json();
  }
  
  private calculateConfidenceScore(response: DeepSeekResponse): number {
    // Simple confidence calculation based on response characteristics
    const choice = response.choices[0];
    if (!choice || choice.finish_reason !== 'stop') {
      return 0.5; // Lower confidence for incomplete responses
    }
    
    const contentLength = choice.message.content.length;
    if (contentLength < 50) {
      return 0.6; // Lower confidence for very short responses
    }
    
    return 0.9; // High confidence for complete, substantial responses
  }
  
  protected async handleRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    // DeepSeek rate limit: ~60 requests per minute
    const minInterval = 1000; // 1 second between requests
    
    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
