import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/hooks/auth.basic';
import { EPOSIntegration } from '@/components/admin/EPOSIntegration';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const DashboardPage = () => {
  const { user, profile } = useAuth();
  const [productCount, setProductCount] = useState<number>(0);
  const [categoryCount, setCategoryCount] = useState<number>(0);
  const [userCount, setUserCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Add a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        setError('Loading timed out. Please refresh the page to try again.');
      }
    }, 15000); // 15 seconds timeout

    const fetchCounts = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log('Fetching dashboard data...');

        // Fetch product count
        const { count: prodCount, error: prodError } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });

        if (prodError) {
          console.error('Error fetching product count:', prodError);
          setProductCount(0);
        } else {
          setProductCount(prodCount || 0);
        }

        // Fetch category count
        const { count: catCount, error: catError } = await supabase
          .from('categories')
          .select('*', { count: 'exact', head: true });

        if (catError) {
          console.error('Error fetching category count:', catError);
          setCategoryCount(0);
        } else {
          setCategoryCount(catCount || 0);
        }

        // Fetch user count
        const { count: usrCount, error: usrError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        if (usrError) {
          console.error('Error fetching user count:', usrError);
          setUserCount(0);
        } else {
          setUserCount(usrCount || 0);
        }

        console.log('Dashboard data fetched successfully');
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
        clearTimeout(loadingTimeout);
      }
    };

    fetchCounts();

    return () => clearTimeout(loadingTimeout);
  }, []);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <p className="text-gray-500">
            Welcome back, {profile?.first_name || 'Admin'}
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-4xl">{productCount}</CardTitle>
                <CardDescription>Products</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="ghost" className="w-full" asChild>
                  <Link to="/admin/products">Manage Products</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-4xl">{categoryCount}</CardTitle>
                <CardDescription>Categories</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="ghost" className="w-full" asChild>
                  <Link to="/admin/categories">Manage Categories</Link>
                </Button>
              </CardContent>
            </Card>

            {/* Orders card removed temporarily until database schema is updated */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-4xl">-</CardTitle>
                <CardDescription>Orders</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="ghost" className="w-full" asChild>
                  <Link to="/admin/orders">Manage Orders</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-4xl">{userCount}</CardTitle>
                <CardDescription>Users</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="ghost" className="w-full" asChild>
                  <Link to="/admin/users">Manage Users</Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Links</CardTitle>
                <CardDescription>Frequently used admin actions</CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <Button asChild>
                  <Link to="/admin/products">Add Product</Link>
                </Button>
                <Button asChild>
                  <Link to="/admin/categories">Add Category</Link>
                </Button>
                <Button asChild>
                  <Link to="/admin/orders">View Orders</Link>
                </Button>
                <Button asChild>
                  <Link to="/admin/discount-codes">Discount Codes</Link>
                </Button>
                <Button asChild>
                  <Link to="/admin/settings">Settings</Link>
                </Button>
                <Button asChild variant="outline">
                  <Link to="/admin/epos">EPOS Integration</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest updates and actions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-500 text-center py-4">
                    No recent activity available
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* EPOS Integration */}
            <Card>
              <CardHeader>
                <CardTitle>EPOS Integration</CardTitle>
                <CardDescription>Manage your EPOS system integration</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Connect your online store with your Electronic Point of Sale system to synchronize inventory, prices, and orders.
                </p>
                <Button asChild className="w-full">
                  <Link to="/admin/epos">Manage EPOS Integration</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardPage;
