/**
 * Base AI Provider Interface
 * 
 * Defines the contract that all AI providers must implement
 */

import { AIRequest, AIRequestOptions } from '../types/AIRequest';
import { AIResponse, AIProviderStatus } from '../types/AIResponse';

export interface ProviderConfig {
  api_key: string;
  api_url?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  timeout?: number;
  max_retries?: number;
  rate_limit?: {
    requests_per_minute: number;
    tokens_per_minute: number;
  };
}

export interface ProviderCapabilities {
  supports_streaming: boolean;
  supports_images: boolean;
  supports_function_calling: boolean;
  supports_system_prompts: boolean;
  max_context_length: number;
  supported_formats: string[];
}

export abstract class BaseProvider {
  protected config: ProviderConfig;
  protected capabilities: ProviderCapabilities;
  protected name: string;
  
  constructor(name: string, config: ProviderConfig, capabilities: ProviderCapabilities) {
    this.name = name;
    this.config = config;
    this.capabilities = capabilities;
  }
  
  /**
   * Process an AI request
   */
  abstract processRequest(
    request: AIRequest, 
    options?: AIRequestOptions
  ): Promise<AIResponse>;
  
  /**
   * Process a streaming AI request
   */
  abstract processStreamingRequest(
    request: AIRequest, 
    options?: AIRequestOptions
  ): Promise<ReadableStream<string>>;
  
  /**
   * Check if the provider is available and healthy
   */
  abstract checkHealth(): Promise<AIProviderStatus>;
  
  /**
   * Get current usage statistics
   */
  abstract getUsageStats(): Promise<{
    requests_today: number;
    tokens_today: number;
    cost_today: number;
    rate_limit_remaining: number;
  }>;
  
  /**
   * Estimate cost for a request
   */
  abstract estimateCost(request: AIRequest): Promise<number>;
  
  /**
   * Get provider capabilities
   */
  getCapabilities(): ProviderCapabilities {
    return this.capabilities;
  }
  
  /**
   * Get provider name
   */
  getName(): string {
    return this.name;
  }
  
  /**
   * Check if provider supports a specific feature
   */
  supports(feature: keyof ProviderCapabilities): boolean {
    return this.capabilities[feature] as boolean;
  }
  
  /**
   * Validate request against provider capabilities
   */
  protected validateRequest(request: AIRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check if streaming is requested but not supported
    if (request.stream && !this.capabilities.supports_streaming) {
      errors.push('Provider does not support streaming');
    }
    
    // Check context length
    if (request.content.length > this.capabilities.max_context_length) {
      errors.push(`Content exceeds maximum context length of ${this.capabilities.max_context_length}`);
    }
    
    // Check format support
    if (request.context?.format && !this.capabilities.supported_formats.includes(request.context.format)) {
      errors.push(`Provider does not support format: ${request.context.format}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Handle rate limiting
   */
  protected async handleRateLimit(): Promise<void> {
    // Implementation will vary by provider
    // Base implementation just waits 1 second
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  /**
   * Retry logic with exponential backoff
   */
  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff: 1s, 2s, 4s, 8s...
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
}
