/**
 * Multi-Tenant Testing Environment Setup Script
 * 
 * This script helps set up and validate the multi-tenant testing environment
 * by connecting to Supabase and running the migration scripts.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration - to be updated with actual values after Supabase project creation
const config = {
  supabaseUrl: process.env.SUPABASE_URL || 'https://your-project-id.supabase.co',
  supabaseKey: process.env.SUPABASE_KEY || 'your-supabase-key',
  migrationFiles: [
    '20250127000001_create_tenant_system.sql',
    '20250127000002_add_tenant_columns.sql',
    '20250127000003_implement_rls_policies.sql',
    '20250127000004_test_tenant_isolation.sql'
  ]
};

// Initialize Supabase client
const supabase = createClient(config.supabaseUrl, config.supabaseKey);

/**
 * Runs a SQL migration file
 * @param {string} filename - The name of the migration file
 * @returns {Promise<void>}
 */
async function runMigration(filename) {
  console.log(`Running migration: ${filename}`);
  
  try {
    const filePath = path.join(__dirname, filename);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split the SQL file into separate statements
    const statements = sql
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim().length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      const { data, error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`Error executing statement: ${error.message}`);
        console.error(`Statement: ${statement}`);
        throw error;
      }
    }
    
    console.log(`✅ Migration ${filename} completed successfully`);
  } catch (error) {
    console.error(`❌ Migration ${filename} failed: ${error.message}`);
    throw error;
  }
}

/**
 * Creates test data for the tenants
 * @returns {Promise<void>}
 */
async function createTestData() {
  console.log('Creating test data...');
  
  try {
    const { data, error } = await supabase.rpc('tenant_management.create_test_data');
    
    if (error) {
      console.error(`Error creating test data: ${error.message}`);
      throw error;
    }
    
    console.log('✅ Test data created successfully');
  } catch (error) {
    console.error(`❌ Failed to create test data: ${error.message}`);
    throw error;
  }
}

/**
 * Runs tenant isolation tests
 * @returns {Promise<void>}
 */
async function runIsolationTests() {
  console.log('Running tenant isolation tests...');
  
  try {
    const { data, error } = await supabase.rpc('tenant_management.test_tenant_isolation');
    
    if (error) {
      console.error(`Error running isolation tests: ${error.message}`);
      throw error;
    }
    
    console.log('Isolation Test Results:');
    console.table(data);
    
    // Check if all tests passed
    const allPassed = data.every(test => test.test_passed);
    if (allPassed) {
      console.log('✅ All tenant isolation tests passed');
    } else {
      console.error('❌ Some tenant isolation tests failed');
    }
  } catch (error) {
    console.error(`❌ Failed to run isolation tests: ${error.message}`);
    throw error;
  }
}

/**
 * Runs performance tests
 * @returns {Promise<void>}
 */
async function runPerformanceTests() {
  console.log('Running performance tests...');
  
  try {
    const { data, error } = await supabase.rpc('tenant_management.test_query_performance');
    
    if (error) {
      console.error(`Error running performance tests: ${error.message}`);
      throw error;
    }
    
    console.log('Performance Test Results:');
    console.table(data);
    
    // Check if all queries use indexes
    const allUseIndexes = data.every(test => test.uses_index);
    if (allUseIndexes) {
      console.log('✅ All queries are using indexes');
    } else {
      console.error('❌ Some queries are not using indexes');
    }
  } catch (error) {
    console.error(`❌ Failed to run performance tests: ${error.message}`);
    throw error;
  }
}

/**
 * Main function to run the setup process
 */
async function main() {
  console.log('Starting multi-tenant testing environment setup...');
  
  try {
    // Run migrations in order
    for (const migrationFile of config.migrationFiles) {
      await runMigration(migrationFile);
    }
    
    // Create test data
    await createTestData();
    
    // Run isolation tests
    await runIsolationTests();
    
    // Run performance tests
    await runPerformanceTests();
    
    console.log('✅ Multi-tenant testing environment setup completed successfully');
  } catch (error) {
    console.error(`❌ Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  runMigration,
  createTestData,
  runIsolationTests,
  runPerformanceTests
};
