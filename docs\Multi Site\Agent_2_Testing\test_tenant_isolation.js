/**
 * Test Tenant Isolation Script
 * 
 * This script simulates requests from different tenants to validate
 * that tenant isolation is working correctly.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const config = {
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseKey: process.env.SUPABASE_KEY
};

// Initialize Supabase client
const supabase = createClient(config.supabaseUrl, config.supabaseKey);

/**
 * Get tenant IDs
 * @returns {Promise<Object>} Object containing tenant IDs
 */
async function getTenantIds() {
  const { data, error } = await supabase
    .from('tenant_management.tenants')
    .select('id, name, subdomain');
  
  if (error) {
    throw new Error(`Failed to get tenant IDs: ${error.message}`);
  }
  
  const tenants = {};
  data.forEach(tenant => {
    tenants[tenant.subdomain] = {
      id: tenant.id,
      name: tenant.name
    };
  });
  
  return tenants;
}

/**
 * Test cross-tenant access
 * @param {Object} tenants - Object containing tenant IDs
 * @returns {Promise<void>}
 */
async function testCrossTenantAccess(tenants) {
  console.log('Testing cross-tenant access...');
  
  // Test cases to verify tenant isolation
  const testCases = [
    {
      name: 'Tenant A cannot see Tenant B\'s products',
      actingTenant: 'fashion',
      targetTenant: 'techhub',
      table: 'products',
      expectedResult: 0
    },
    {
      name: 'Tenant B cannot see Tenant C\'s orders',
      actingTenant: 'techhub',
      targetTenant: 'booknook',
      table: 'orders',
      expectedResult: 0
    },
    {
      name: 'Tenant C cannot access Tenant A\'s customers',
      actingTenant: 'booknook',
      targetTenant: 'fashion',
      table: 'customers',
      expectedResult: 0
    }
  ];
  
  // Run each test case
  for (const test of testCases) {
    console.log(`Running test: ${test.name}`);
    
    // Create a client with the JWT claim for the acting tenant
    const tenantClient = createClient(
      config.supabaseUrl,
      config.supabaseKey,
      {
        global: {
          headers: {
            Authorization: `Bearer ${generateJWT(tenants[test.actingTenant].id)}`
          }
        }
      }
    );
    
    // Try to access data from the target tenant
    const { data, error } = await tenantClient
      .from(test.table)
      .select('*')
      .eq('tenant_id', tenants[test.targetTenant].id);
    
    if (error) {
      console.log(`✅ Test passed with error: ${error.message}`);
    } else {
      const rowCount = data?.length || 0;
      if (rowCount === test.expectedResult) {
        console.log(`✅ Test passed: ${rowCount} rows returned as expected`);
      } else {
        console.error(`❌ Test failed: Expected ${test.expectedResult} rows, got ${rowCount}`);
        console.error('Data returned:', data);
      }
    }
    
    console.log('---');
  }
}

/**
 * Test CRUD operations with tenant isolation
 * @param {Object} tenants - Object containing tenant IDs
 * @returns {Promise<void>}
 */
async function testCRUDOperations(tenants) {
  console.log('Testing CRUD operations with tenant isolation...');
  
  // Test cases for CRUD operations
  const crudTests = [
    {
      name: 'Create: Tenant A can create a product',
      tenant: 'fashion',
      operation: 'create',
      table: 'products',
      data: {
        name: 'Test Product A',
        description: 'Test product for Tenant A',
        price: 19.99
      }
    },
    {
      name: 'Create: Tenant A cannot create a product for Tenant B',
      tenant: 'fashion',
      operation: 'create',
      table: 'products',
      data: {
        name: 'Test Product for B',
        description: 'This should fail',
        price: 29.99,
        tenant_id: null // Will be set to Tenant B's ID in the test
      },
      shouldFail: true
    },
    {
      name: 'Read: Tenant B can read its own products',
      tenant: 'techhub',
      operation: 'read',
      table: 'products',
      expectedMinCount: 1
    },
    {
      name: 'Update: Tenant C can update its own product',
      tenant: 'booknook',
      operation: 'update',
      table: 'products',
      updateData: {
        description: 'Updated description'
      }
    },
    {
      name: 'Update: Tenant C cannot update Tenant A\'s product',
      tenant: 'booknook',
      operation: 'update',
      table: 'products',
      targetTenant: 'fashion',
      updateData: {
        description: 'This should fail'
      },
      shouldFail: true
    },
    {
      name: 'Delete: Tenant A can delete its own product',
      tenant: 'fashion',
      operation: 'delete',
      table: 'products'
    },
    {
      name: 'Delete: Tenant A cannot delete Tenant B\'s product',
      tenant: 'fashion',
      operation: 'delete',
      table: 'products',
      targetTenant: 'techhub',
      shouldFail: true
    }
  ];
  
  // Run each CRUD test
  for (const test of crudTests) {
    console.log(`Running test: ${test.name}`);
    
    // Create a client with the JWT claim for the acting tenant
    const tenantClient = createClient(
      config.supabaseUrl,
      config.supabaseKey,
      {
        global: {
          headers: {
            Authorization: `Bearer ${generateJWT(tenants[test.tenant].id)}`
          }
        }
      }
    );
    
    try {
      let result;
      
      // Handle different operations
      switch (test.operation) {
        case 'create':
          // If this is a test that should fail, set the tenant_id to another tenant
          if (test.shouldFail && test.data.tenant_id === null) {
            test.data.tenant_id = tenants[test.targetTenant || 'corner'].id;
          }
          
          result = await tenantClient
            .from(test.table)
            .insert(test.data)
            .select();
          
          // Store the created item's ID for later tests if needed
          if (result.data && result.data.length > 0) {
            test.createdId = result.data[0].id;
          }
          break;
          
        case 'read':
          result = await tenantClient
            .from(test.table)
            .select('*');
          
          if (!test.shouldFail && result.data) {
            if (result.data.length >= test.expectedMinCount) {
              console.log(`✅ Test passed: Found ${result.data.length} rows (expected at least ${test.expectedMinCount})`);
            } else {
              console.error(`❌ Test failed: Expected at least ${test.expectedMinCount} rows, got ${result.data.length}`);
            }
          }
          break;
          
        case 'update':
          // Get an item to update
          let itemToUpdate;
          
          if (test.targetTenant) {
            // This should fail - trying to update another tenant's item
            const targetTenantId = tenants[test.targetTenant].id;
            itemToUpdate = { id: null, tenant_id: targetTenantId };
            
            // Get the first product from the target tenant (directly, not through the API)
            const { data } = await supabase
              .from(test.table)
              .select('id')
              .eq('tenant_id', targetTenantId)
              .limit(1);
            
            if (data && data.length > 0) {
              itemToUpdate.id = data[0].id;
            }
          } else {
            // Get the first item for this tenant
            const { data } = await tenantClient
              .from(test.table)
              .select('id')
              .limit(1);
            
            if (data && data.length > 0) {
              itemToUpdate = { id: data[0].id };
            }
          }
          
          if (itemToUpdate && itemToUpdate.id) {
            result = await tenantClient
              .from(test.table)
              .update(test.updateData)
              .eq('id', itemToUpdate.id)
              .select();
          } else {
            throw new Error('No item found to update');
          }
          break;
          
        case 'delete':
          // Get an item to delete
          let itemToDelete;
          
          if (test.targetTenant) {
            // This should fail - trying to delete another tenant's item
            const targetTenantId = tenants[test.targetTenant].id;
            
            // Get the first product from the target tenant (directly, not through the API)
            const { data } = await supabase
              .from(test.table)
              .select('id')
              .eq('tenant_id', targetTenantId)
              .limit(1);
            
            if (data && data.length > 0) {
              itemToDelete = { id: data[0].id };
            }
          } else {
            // Create a new item to delete
            const { data } = await tenantClient
              .from(test.table)
              .insert({
                name: 'Item to delete',
                description: 'This item will be deleted',
                price: 9.99
              })
              .select();
            
            if (data && data.length > 0) {
              itemToDelete = { id: data[0].id };
            }
          }
          
          if (itemToDelete && itemToDelete.id) {
            result = await tenantClient
              .from(test.table)
              .delete()
              .eq('id', itemToDelete.id);
          } else {
            throw new Error('No item found to delete');
          }
          break;
      }
      
      // Check if the test passed or failed
      if (test.shouldFail) {
        console.error(`❌ Test failed: Expected operation to fail but it succeeded`);
        console.error('Result:', result);
      } else {
        if (result.error) {
          console.error(`❌ Test failed with error: ${result.error.message}`);
        } else {
          console.log(`✅ Test passed: Operation completed successfully`);
        }
      }
    } catch (error) {
      if (test.shouldFail) {
        console.log(`✅ Test passed with expected error: ${error.message}`);
      } else {
        console.error(`❌ Test failed with unexpected error: ${error.message}`);
      }
    }
    
    console.log('---');
  }
}

/**
 * Generate a mock JWT with tenant_id claim
 * @param {string} tenantId - The tenant ID to include in the JWT
 * @returns {string} A mock JWT token
 */
function generateJWT(tenantId) {
  // This is a mock JWT for testing purposes
  // In a real application, you would use a proper JWT library
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
  const payload = Buffer.from(JSON.stringify({ tenant_id: tenantId })).toString('base64');
  const signature = 'mock_signature';
  
  return `${header}.${payload}.${signature}`;
}

/**
 * Main function to run all tests
 */
async function main() {
  try {
    console.log('Starting tenant isolation tests...');
    
    // Get tenant IDs
    const tenants = await getTenantIds();
    console.log('Found tenants:', Object.keys(tenants).map(k => tenants[k].name));
    
    // Test cross-tenant access
    await testCrossTenantAccess(tenants);
    
    // Test CRUD operations
    await testCRUDOperations(tenants);
    
    console.log('✅ All tests completed');
  } catch (error) {
    console.error(`❌ Tests failed: ${error.message}`);
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  testCrossTenantAccess,
  testCRUDOperations
};
