# Cheech Chatbot

## Overview

Cheech is an all-knowledgeable chatbot designed for BitsnBongs to provide product information, recommendations, and usage guidance. The chatbot features a floating button interface, RAG (Retrieval Augmented Generation) capabilities for accurate product knowledge, and web navigation assistance.

![Cheech Chatbot Interface](./assets/cheech-preview.png)

## Key Features

- **Intelligent Product Knowledge**: Comprehensive understanding of cannabis/CBD products and accessories
- **Floating Chat Interface**: Accessible from any page on the website
- **RAG Implementation**: Retrieves relevant product information dynamically
- **Context-Aware Conversations**: Maintains conversation history and user preferences
- **Product Recommendations**: Suggests products based on user needs
- **Web Navigation Assistance**: Guides users to relevant pages using Puppeteer

## Technical Architecture

The Cheech Chatbot is built with a modular architecture consisting of several key components:

1. **RAG Engine**: Retrieves relevant information using vector embeddings
2. **Context Manager**: Maintains conversation state using LangChain
3. **Web Navigation Assistant**: Provides page guidance using Puppeteer
4. **Chat Interface**: React-based UI with floating button and chat window
5. **Main Chatbot Service**: Orchestrates all components

## Implementation Guide

### Prerequisites

- Node.js (v14+)
- npm or yarn
- OpenAI API key

### Installation

1. Clone the repository or navigate to the project directory:
   ```
   cd docs/Agent_Tasks/Cheech_Chatbot
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file with the following variables:
   ```
   OPENAI_API_KEY=your_openai_api_key
   BASE_URL=https://bitsnbongs.com
   ```

### Development

1. Start the development server:
   ```
   npm run dev
   ```

2. The chatbot interface will be available at `http://localhost:3000`

### Building for Production

1. Build the project:
   ```
   npm run build
   ```

2. The build artifacts will be in the `dist` directory

## Component Documentation

### RAG Engine

The RAG Engine handles document retrieval and embedding generation. It uses OpenAI embeddings to find relevant information based on user queries.

Key files:
- `implementations/RAGEngine.ts`

### Context Manager

The Context Manager maintains conversation state and user preferences. It uses LangChain for memory management and context tracking.

Key files:
- `implementations/ContextManager.ts`

### Web Navigation Assistant

The Web Navigation Assistant uses Puppeteer to guide users through the website. It can navigate to relevant pages and provide visual guidance.

Key files:
- `implementations/WebNavigationAssistant.ts`

### Chat Interface

The Chat Interface provides the user-facing components, including the floating chat button and chat window.

Key files:
- `implementations/ChatInterface.tsx`
- `assets/chat-interface.css`

### Main Chatbot Service

The main service orchestrates all components and handles user interactions.

Key files:
- `implementations/CheechChatbot.ts`
- `implementations/index.ts`

## Data Sources

The chatbot uses several data sources:

1. **Product Database**: Contains detailed information about all products
2. **Knowledge Base**: FAQs and common questions with answers
3. **Website Content**: Accessed via the Web Navigation Assistant

## Integration Guide

To integrate the Cheech Chatbot into the BitsnBongs website:

1. Add the following script to your website's HTML:
   ```html
   <script src="https://bitsnbongs.com/chatbot/cheech.js"></script>
   <link rel="stylesheet" href="https://bitsnbongs.com/chatbot/cheech.css">
   ```

2. Initialize the chatbot:
   ```html
   <script>
     document.addEventListener('DOMContentLoaded', function() {
       CheechChatbot.init({
         apiKey: 'your_api_key',
         baseUrl: 'https://bitsnbongs.com'
       });
     });
   </script>
   ```

## Future Enhancements

- **Voice Interface**: Add speech recognition and synthesis
- **Personalization**: Improve user preference tracking
- **Multi-language Support**: Add support for additional languages
- **Analytics Dashboard**: Track common questions and user satisfaction

## Testing

Run the test suite:
```
npm test
```

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For questions or support, contact the development <NAME_EMAIL>
