# Cheech Chatbot

## Overview

Cheech is an all-knowledgeable chatbot designed for BitsnBongs to provide product information, recommendations, and usage guidance. The chatbot features a floating button interface, RAG (Retrieval Augmented Generation) capabilities for accurate product knowledge, and web navigation assistance.

![Cheech Chatbot Interface](./assets/cheech-preview.png)

## Key Features

- **Intelligent Product Knowledge**: Comprehensive understanding of cannabis/CBD products and accessories
- **Floating Chat Interface**: Accessible from any page on the website
- **RAG Implementation**: Retrieves relevant product information dynamically
- **Context-Aware Conversations**: Maintains conversation history and user preferences
- **Product Recommendations**: Suggests products based on user needs
- **Web Navigation Assistance**: Guides users to relevant pages using Puppeteer

## Technical Architecture

The Cheech Chatbot is built with a modular architecture consisting of several key components:

1. **RAG Engine**: Retrieves relevant information using vector embeddings
2. **Context Manager**: Maintains conversation state using LangChain
3. **Web Navigation Assistant**: Provides page guidance using Puppeteer
4. **Chat Interface**: React-based UI with floating button and chat window
5. **Main Chatbot Service**: Orchestrates all components

## Implementation Guide

### Prerequisites

- Node.js (v14+)
- npm or yarn
- Existing AI orchestration system (Gemini + Deepseek + OpenRouter)

### Installation

1. Copy the chatbot files to your main project:
   ```bash
   cp -r docs/Agent_Tasks/Cheech_Chatbot/implementations/* src/components/chatbot/
   cp docs/Agent_Tasks/Cheech_Chatbot/assets/chat-interface.css src/styles/
   ```

2. The chatbot uses your existing AI orchestration system, so no additional API keys are needed.

### Development

1. Start the development server:
   ```
   npm run dev
   ```

2. The chatbot interface will be available at `http://localhost:3000`

### Building for Production

1. Build the project:
   ```
   npm run build
   ```

2. The build artifacts will be in the `dist` directory

## Component Documentation

### RAG Engine

The RAG Engine handles document retrieval and embedding generation. It uses OpenAI embeddings to find relevant information based on user queries.

Key files:
- `implementations/RAGEngine.ts`

### Context Manager

The Context Manager maintains conversation state and user preferences. It uses LangChain for memory management and context tracking.

Key files:
- `implementations/ContextManager.ts`

### Web Navigation Assistant

The Web Navigation Assistant uses Puppeteer to guide users through the website. It can navigate to relevant pages and provide visual guidance.

Key files:
- `implementations/WebNavigationAssistant.ts`

### Chat Interface

The Chat Interface provides the user-facing components, including the floating chat button and chat window.

Key files:
- `implementations/ChatInterface.tsx`
- `assets/chat-interface.css`

### Main Chatbot Service

The main service orchestrates all components and handles user interactions.

Key files:
- `implementations/CheechChatbot.ts`
- `implementations/index.ts`

## Data Sources

The chatbot uses several data sources:

1. **Product Database**: Contains detailed information about all products
2. **Knowledge Base**: FAQs and common questions with answers
3. **Website Content**: Accessed via the Web Navigation Assistant

## Integration Guide

To integrate the Cheech Chatbot into the BitsnBongs website:

1. Import the necessary components in your main layout:
   ```tsx
   import ChatInterface from './components/chatbot/ChatInterface';
   import { CheechChatbot } from './components/chatbot/CheechChatbot';
   import { AIServiceManager } from './services/ai/AIServiceManager';
   import './styles/chat-interface.css';
   ```

2. Initialize the chatbot with your AI orchestration system:
   ```tsx
   // Initialize AI service manager
   const aiManager = new AIServiceManager();
   await aiManager.initialize({
     deepseek_key: process.env.VITE_DEEPSEEK_API_KEY,
     gemini_key: process.env.VITE_GEMINI_API_KEY,
     openrouter_key: process.env.VITE_OPENROUTER_API_KEY
   });

   // Create chatbot instance
   const chatbot = new CheechChatbot({
     aiService: aiManager.getUnifiedAI(),
     baseUrl: window.location.origin,
     knowledgeBasePath: './data/knowledge-base.json',
     productDatabasePath: './data/products.json'
   });

   // Generate session ID
   const sessionId = chatbot.createSession();
   ```

3. Add the chat interface to your JSX:
   ```tsx
   <ChatInterface
     sessionId={sessionId}
     onSendMessage={(message) => chatbot.processMessage(sessionId, message)}
   />
   ```

## Future Enhancements

- **Voice Interface**: Add speech recognition and synthesis
- **Personalization**: Improve user preference tracking
- **Multi-language Support**: Add support for additional languages
- **Analytics Dashboard**: Track common questions and user satisfaction

## Testing

Run the test suite:
```
npm test
```

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For questions or support, contact the development <NAME_EMAIL>
