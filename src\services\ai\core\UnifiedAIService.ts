/**
 * Unified AI Service - The Brain of the Operation
 * 
 * Orchestrates multiple AI providers with smart routing, cost optimization,
 * and automatic fallbacks. This is the main interface for all AI operations.
 */

import { AIRequest, AIRequestType, AIProvider } from '../types/AIRequest';
import { AIResponse, AIProviderStatus, AIUsageStats } from '../types/AIResponse';
import { BaseProvider } from '../providers/BaseProvider';
import { DeepSeekProvider } from '../providers/DeepSeekProvider';
import { GeminiProvider } from '../providers/GeminiProvider';
import { OpenRouterProvider } from '../providers/OpenRouterProvider';

interface UnifiedAIConfig {
  deepseek?: {
    api_key: string;
    enabled: boolean;
  };
  gemini?: {
    api_key: string;
    enabled: boolean;
  };
  openrouter?: {
    api_key: string;
    enabled: boolean;
  };
  default_provider?: AIProvider;
  cost_optimization?: boolean;
  auto_fallback?: boolean;
}

interface ProviderRoutingRule {
  request_types: AIRequestType[];
  preferred_provider: AIProvider;
  fallback_providers: AIProvider[];
  cost_threshold?: number;
  urgency_override?: boolean;
}

export class UnifiedAIService {
  private providers: Map<AIProvider, BaseProvider> = new Map();
  private config: UnifiedAIConfig;
  private routingRules: ProviderRoutingRule[];
  private usageStats: Map<AIProvider, AIUsageStats> = new Map();
  private healthStatus: Map<AIProvider, AIProviderStatus> = new Map();
  
  constructor(config: UnifiedAIConfig) {
    this.config = config;
    this.initializeProviders();
    this.setupRoutingRules();
    this.startHealthMonitoring();
  }
  
  /**
   * Main entry point for all AI requests
   */
  async processRequest(request: AIRequest): Promise<AIResponse> {
    try {
      // Add request metadata
      request.request_id = request.request_id || this.generateRequestId();
      request.timestamp = request.timestamp || new Date();
      
      // Select optimal provider
      const provider = await this.selectOptimalProvider(request);
      
      // Process request with automatic fallback
      const response = await this.processWithFallback(request, provider);
      
      // Track usage and costs
      await this.trackUsage(provider, request, response);
      
      return response;
      
    } catch (error) {
      console.error('UnifiedAIService error:', error);
      
      return {
        content: '',
        success: false,
        provider: 'unknown',
        processing_time: 0,
        timestamp: new Date(),
        error: {
          code: 'UNIFIED_AI_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }
      };
    }
  }
  
  /**
   * Process streaming request
   */
  async processStreamingRequest(request: AIRequest): Promise<ReadableStream<string>> {
    const provider = await this.selectOptimalProvider(request);
    const providerInstance = this.providers.get(provider);
    
    if (!providerInstance) {
      throw new Error(`Provider ${provider} not available`);
    }
    
    return providerInstance.processStreamingRequest(request);
  }
  
  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<{
    overall_status: 'healthy' | 'degraded' | 'down';
    providers: AIProviderStatus[];
    recommendations: string[];
  }> {
    const providerStatuses: AIProviderStatus[] = [];
    let healthyProviders = 0;
    
    for (const [providerName, provider] of this.providers) {
      const status = await provider.checkHealth();
      providerStatuses.push(status);
      this.healthStatus.set(providerName, status);
      
      if (status.available && !status.rate_limited) {
        healthyProviders++;
      }
    }
    
    const totalProviders = this.providers.size;
    let overallStatus: 'healthy' | 'degraded' | 'down';
    const recommendations: string[] = [];
    
    if (healthyProviders === totalProviders) {
      overallStatus = 'healthy';
    } else if (healthyProviders > 0) {
      overallStatus = 'degraded';
      recommendations.push('Some AI providers are experiencing issues. Requests will be routed to healthy providers.');
    } else {
      overallStatus = 'down';
      recommendations.push('All AI providers are currently unavailable. Please check API keys and network connectivity.');
    }
    
    return {
      overall_status: overallStatus,
      providers: providerStatuses,
      recommendations
    };
  }
  
  /**
   * Get usage statistics
   */
  async getUsageStats(timeframe: 'today' | 'week' | 'month' = 'today'): Promise<{
    total_requests: number;
    total_tokens: number;
    total_cost: number;
    cost_breakdown: { provider: string; cost: number; percentage: number }[];
    recommendations: string[];
  }> {
    // In production, this would query a usage tracking database
    // For now, return mock data based on current session
    
    let totalRequests = 0;
    let totalTokens = 0;
    let totalCost = 0;
    const costBreakdown: { provider: string; cost: number; percentage: number }[] = [];
    
    for (const [providerName, stats] of this.usageStats) {
      const requests = timeframe === 'today' ? stats.requests_today : stats.requests_this_month;
      const tokens = timeframe === 'today' ? stats.tokens_today : stats.tokens_this_month;
      const cost = timeframe === 'today' ? stats.cost_today : stats.cost_this_month;
      
      totalRequests += requests;
      totalTokens += tokens;
      totalCost += cost;
      
      costBreakdown.push({
        provider: providerName,
        cost: cost,
        percentage: 0 // Will calculate after totals
      });
    }
    
    // Calculate percentages
    costBreakdown.forEach(item => {
      item.percentage = totalCost > 0 ? (item.cost / totalCost) * 100 : 0;
    });
    
    // Generate recommendations
    const recommendations: string[] = [];
    
    if (totalCost > 50) { // Arbitrary threshold
      recommendations.push('Consider using DeepSeek for more requests to reduce costs.');
    }
    
    const geminiUsage = costBreakdown.find(item => item.provider === 'gemini');
    if (geminiUsage && geminiUsage.percentage > 60) {
      recommendations.push('High Gemini usage detected. Consider routing simple tasks to DeepSeek.');
    }
    
    return {
      total_requests: totalRequests,
      total_tokens: totalTokens,
      total_cost: totalCost,
      cost_breakdown: costBreakdown,
      recommendations
    };
  }
  
  /**
   * Estimate cost for a request
   */
  async estimateCost(request: AIRequest, provider?: AIProvider): Promise<{
    provider: AIProvider;
    estimated_cost: number;
    alternatives: { provider: AIProvider; cost: number }[];
  }> {
    const selectedProvider = provider || await this.selectOptimalProvider(request);
    const providerInstance = this.providers.get(selectedProvider);
    
    if (!providerInstance) {
      throw new Error(`Provider ${selectedProvider} not available`);
    }
    
    const estimatedCost = await providerInstance.estimateCost(request);
    
    // Get alternatives
    const alternatives: { provider: AIProvider; cost: number }[] = [];
    for (const [providerName, providerInst] of this.providers) {
      if (providerName !== selectedProvider) {
        const altCost = await providerInst.estimateCost(request);
        alternatives.push({ provider: providerName, cost: altCost });
      }
    }
    
    // Sort alternatives by cost
    alternatives.sort((a, b) => a.cost - b.cost);
    
    return {
      provider: selectedProvider,
      estimated_cost: estimatedCost,
      alternatives
    };
  }
  
  private initializeProviders(): void {
    // Initialize DeepSeek (primary provider)
    if (this.config.deepseek?.enabled && this.config.deepseek.api_key) {
      const deepseek = new DeepSeekProvider({
        api_key: this.config.deepseek.api_key,
        max_tokens: 2000,
        temperature: 0.7
      });
      this.providers.set('deepseek', deepseek);
    }
    
    // Initialize Gemini (creative provider)
    if (this.config.gemini?.enabled && this.config.gemini.api_key) {
      const gemini = new GeminiProvider({
        api_key: this.config.gemini.api_key,
        max_tokens: 2048,
        temperature: 0.7
      });
      this.providers.set('gemini', gemini);
    }
    
    // Initialize OpenRouter (premium provider)
    if (this.config.openrouter?.enabled && this.config.openrouter.api_key) {
      const openrouter = new OpenRouterProvider({
        api_key: this.config.openrouter.api_key,
        max_tokens: 2000,
        temperature: 0.7
      });
      this.providers.set('openrouter', openrouter);
    }
  }
  
  private setupRoutingRules(): void {
    this.routingRules = [
      // DeepSeek for fast, simple tasks
      {
        request_types: ['product_description', 'seo_optimization', 'social_media_post', 'hashtag_generation'],
        preferred_provider: 'deepseek',
        fallback_providers: ['gemini', 'openrouter']
      },
      
      // Gemini for creative content
      {
        request_types: ['blog_content', 'newsletter_content', 'creative_campaigns'],
        preferred_provider: 'gemini',
        fallback_providers: ['deepseek', 'openrouter']
      },
      
      // OpenRouter for critical/complex tasks
      {
        request_types: ['fraud_detection', 'customer_analysis', 'complex_analysis'],
        preferred_provider: 'openrouter',
        fallback_providers: ['gemini', 'deepseek']
      }
    ];
  }
  
  private async selectOptimalProvider(request: AIRequest): Promise<AIProvider> {
    // Check if provider is explicitly requested
    if (request.provider && request.provider !== 'auto' && this.providers.has(request.provider)) {
      return request.provider;
    }
    
    // Find routing rule for request type
    const rule = this.routingRules.find(r => r.request_types.includes(request.type));
    
    if (rule) {
      // Check if preferred provider is available
      const preferredStatus = this.healthStatus.get(rule.preferred_provider);
      if (preferredStatus?.available && !preferredStatus.rate_limited) {
        return rule.preferred_provider;
      }
      
      // Try fallback providers
      for (const fallbackProvider of rule.fallback_providers) {
        const fallbackStatus = this.healthStatus.get(fallbackProvider);
        if (fallbackStatus?.available && !fallbackStatus.rate_limited) {
          return fallbackProvider;
        }
      }
    }
    
    // Default fallback: use any available provider
    for (const [providerName, status] of this.healthStatus) {
      if (status.available && !status.rate_limited) {
        return providerName;
      }
    }
    
    // Last resort: use default provider or first available
    return this.config.default_provider || 'deepseek';
  }
  
  private async processWithFallback(request: AIRequest, primaryProvider: AIProvider): Promise<AIResponse> {
    const providers = [primaryProvider];
    
    // Add fallback providers
    const rule = this.routingRules.find(r => r.request_types.includes(request.type));
    if (rule && this.config.auto_fallback) {
      providers.push(...rule.fallback_providers.filter(p => p !== primaryProvider));
    }
    
    let lastError: Error | null = null;
    
    for (const providerName of providers) {
      const provider = this.providers.get(providerName);
      if (!provider) continue;
      
      try {
        const response = await provider.processRequest(request);
        if (response.success) {
          return response;
        }
        lastError = new Error(response.error?.message || 'Provider returned unsuccessful response');
      } catch (error) {
        lastError = error as Error;
        console.warn(`Provider ${providerName} failed:`, error);
      }
    }
    
    throw lastError || new Error('All providers failed');
  }
  
  private async trackUsage(provider: AIProvider, request: AIRequest, response: AIResponse): Promise<void> {
    // In production, this would save to database
    // For now, update in-memory stats
    
    const currentStats = this.usageStats.get(provider) || {
      provider: provider,
      requests_today: 0,
      tokens_today: 0,
      cost_today: 0,
      requests_this_month: 0,
      tokens_this_month: 0,
      cost_this_month: 0,
      success_rate: 1
    };
    
    currentStats.requests_today++;
    currentStats.requests_this_month++;
    
    if (response.tokens_used) {
      currentStats.tokens_today += response.tokens_used;
      currentStats.tokens_this_month += response.tokens_used;
    }
    
    if (response.cost) {
      currentStats.cost_today += response.cost;
      currentStats.cost_this_month += response.cost;
    }
    
    this.usageStats.set(provider, currentStats);
  }
  
  private startHealthMonitoring(): void {
    // Check provider health every 5 minutes
    setInterval(async () => {
      for (const [providerName, provider] of this.providers) {
        try {
          const status = await provider.checkHealth();
          this.healthStatus.set(providerName, status);
        } catch (error) {
          console.error(`Health check failed for ${providerName}:`, error);
        }
      }
    }, 5 * 60 * 1000); // 5 minutes
    
    // Initial health check
    this.getSystemHealth();
  }
  
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
