-- Migration: Add tenant_id columns to existing tables
-- Description: Adds tenant_id foreign key columns to all tables requiring tenant isolation
-- Author: AI Assistant
-- Date: 2025-01-27
-- Dependencies: 20250127000001_create_tenant_system.sql

-- Add tenant_id to products table (Priority 1)
ALTER TABLE products ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_products_tenant_id ON products(tenant_id);
CREATE INDEX IF NOT EXISTS idx_products_tenant_active ON products(tenant_id, is_active) WHERE is_active = true;

-- Add tenant_id to categories table
ALTER TABLE categories ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_categories_tenant_id ON categories(tenant_id);

-- Add tenant_id to brands table
ALTER TABLE brands ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_brands_tenant_id ON brands(tenant_id);

-- Add tenant_id to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_orders_tenant_id ON orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_orders_tenant_status ON orders(tenant_id, status);

-- Add tenant_id to order_items table
ALTER TABLE order_items ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_order_items_tenant_id ON order_items(tenant_id);

-- Add tenant_id to blogs table
ALTER TABLE blogs ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_blogs_tenant_id ON blogs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_blogs_tenant_published ON blogs(tenant_id, published_at) WHERE published_at IS NOT NULL;

-- Add tenant_id to blog_categories table
ALTER TABLE blog_categories ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_blog_categories_tenant_id ON blog_categories(tenant_id);

-- Add tenant_id to blog_comments table (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_comments') THEN
    ALTER TABLE blog_comments ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
    CREATE INDEX IF NOT EXISTS idx_blog_comments_tenant_id ON blog_comments(tenant_id);
  END IF;
END $$;

-- Add tenant_id to blog_images table (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_images') THEN
    ALTER TABLE blog_images ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
    CREATE INDEX IF NOT EXISTS idx_blog_images_tenant_id ON blog_images(tenant_id);
  END IF;
END $$;

-- Add tenant_id to discount_codes table
ALTER TABLE discount_codes ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_discount_codes_tenant_id ON discount_codes(tenant_id);

-- Add tenant_id to settings table
ALTER TABLE settings ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_settings_tenant_id ON settings(tenant_id);

-- Add tenant_id to faqs table
ALTER TABLE faqs ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_faqs_tenant_id ON faqs(tenant_id);

-- Add tenant_id to related_products table
ALTER TABLE related_products ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_related_products_tenant_id ON related_products(tenant_id);

-- Add tenant_id to product_variants table (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_variants') THEN
    ALTER TABLE product_variants ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
    CREATE INDEX IF NOT EXISTS idx_product_variants_tenant_id ON product_variants(tenant_id);
  END IF;
END $$;

-- User-specific tables (Priority 2)
-- Add current_tenant_id to profiles table (for user's current active tenant)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS current_tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_profiles_current_tenant_id ON profiles(current_tenant_id);

-- Add tenant_id to addresses table
ALTER TABLE addresses ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_addresses_tenant_id ON addresses(tenant_id);

-- Add tenant_id to cart_items table
ALTER TABLE cart_items ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_cart_items_tenant_id ON cart_items(tenant_id);

-- Add tenant_id to wishlists table
ALTER TABLE wishlists ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_wishlists_tenant_id ON wishlists(tenant_id);

-- Add tenant_id to wishlist_items table
ALTER TABLE wishlist_items ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_tenant_id ON wishlist_items(tenant_id);

-- Add tenant_id to saved_items table
ALTER TABLE saved_items ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_saved_items_tenant_id ON saved_items(tenant_id);

-- Add tenant_id to newsletter_subscribers table
ALTER TABLE newsletter_subscribers ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_tenant_id ON newsletter_subscribers(tenant_id);

-- Populate tenant_id with default tenant for existing data
DO $$
DECLARE
  default_tenant_id UUID := '00000000-0000-0000-0000-000000000001'::UUID;
BEGIN
  -- Update core tables
  UPDATE products SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE categories SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE brands SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE orders SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE order_items SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE blogs SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE blog_categories SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE discount_codes SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE settings SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE faqs SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE related_products SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  
  -- Update user-specific tables
  UPDATE addresses SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE cart_items SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE wishlists SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE wishlist_items SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE saved_items SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE newsletter_subscribers SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  UPDATE profiles SET current_tenant_id = default_tenant_id WHERE current_tenant_id IS NULL;
  
  -- Update conditional tables if they exist
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_comments') THEN
    UPDATE blog_comments SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_images') THEN
    UPDATE blog_images SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_variants') THEN
    UPDATE product_variants SET tenant_id = default_tenant_id WHERE tenant_id IS NULL;
  END IF;
  
  RAISE NOTICE 'Successfully populated tenant_id columns with default tenant';
END $$;

-- Add comments for documentation
COMMENT ON COLUMN products.tenant_id IS 'Multi-tenant: Associates product with tenant';
COMMENT ON COLUMN categories.tenant_id IS 'Multi-tenant: Associates category with tenant';
COMMENT ON COLUMN brands.tenant_id IS 'Multi-tenant: Associates brand with tenant';
COMMENT ON COLUMN orders.tenant_id IS 'Multi-tenant: Associates order with tenant';
COMMENT ON COLUMN order_items.tenant_id IS 'Multi-tenant: Associates order item with tenant';
COMMENT ON COLUMN blogs.tenant_id IS 'Multi-tenant: Associates blog post with tenant';
COMMENT ON COLUMN blog_categories.tenant_id IS 'Multi-tenant: Associates blog category with tenant';
COMMENT ON COLUMN discount_codes.tenant_id IS 'Multi-tenant: Associates discount code with tenant';
COMMENT ON COLUMN settings.tenant_id IS 'Multi-tenant: Associates setting with tenant';
COMMENT ON COLUMN faqs.tenant_id IS 'Multi-tenant: Associates FAQ with tenant';
COMMENT ON COLUMN related_products.tenant_id IS 'Multi-tenant: Associates related product with tenant';
COMMENT ON COLUMN profiles.current_tenant_id IS 'Multi-tenant: Users current active tenant';
COMMENT ON COLUMN addresses.tenant_id IS 'Multi-tenant: Associates address with tenant';
COMMENT ON COLUMN cart_items.tenant_id IS 'Multi-tenant: Associates cart item with tenant';
COMMENT ON COLUMN wishlists.tenant_id IS 'Multi-tenant: Associates wishlist with tenant';
COMMENT ON COLUMN wishlist_items.tenant_id IS 'Multi-tenant: Associates wishlist item with tenant';
COMMENT ON COLUMN saved_items.tenant_id IS 'Multi-tenant: Associates saved item with tenant';
COMMENT ON COLUMN newsletter_subscribers.tenant_id IS 'Multi-tenant: Associates newsletter subscriber with tenant';