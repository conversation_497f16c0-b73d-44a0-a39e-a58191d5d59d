# Frontend Theming Strategy for Multi-Tenant Architecture

This document outlines how our frontend can be adapted for different tenants while maintaining a single codebase.

## Theming Approach

Our multi-tenant frontend uses a dynamic theming system that allows each tenant to have a unique look and feel without requiring separate codebases. This is achieved through:

1. **Theme Configuration**: Stored in the database per tenant
2. **Dynamic CSS Variables**: Applied at runtime based on tenant
3. **Component Customization**: Configurable layouts and components

## Implementation Details

### 1. Theme Configuration Storage

Themes are stored in the `settings` table with the tenant's ID:

```sql
-- Example theme settings in the database
INSERT INTO settings (key, value, tenant_id)
VALUES (
  'theme', 
  '{
    "colors": {
      "primary": "#FF5733",
      "secondary": "#33FF57",
      "accent": "#3357FF",
      "background": "#FFFFFF",
      "text": "#333333"
    },
    "typography": {
      "fontFamily": "Roboto, sans-serif",
      "headingFont": "Montserrat, sans-serif"
    },
    "layout": {
      "maxWidth": "1200px",
      "navbarStyle": "side",
      "footerStyle": "minimal"
    },
    "components": {
      "buttonStyle": "rounded",
      "cardStyle": "elevated",
      "imageStyle": "rounded"
    },
    "logo": {
      "url": "https://storage.example.com/logos/tenant-logo.png",
      "width": 200,
      "height": 60
    }
  }',
  'tenant-id-here'
);
```

### 2. Theme Provider Component

A React context provider applies the theme at the application root:

```jsx
// ThemeProvider.jsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

const ThemeContext = createContext();

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function loadTheme() {
      // Get tenant ID from subdomain or JWT
      const tenantId = getCurrentTenantId();
      
      // Fetch theme from settings
      const { data, error } = await supabase
        .from('settings')
        .select('value')
        .eq('key', 'theme')
        .eq('tenant_id', tenantId)
        .single();
        
      if (data) {
        setTheme(data.value);
        applyThemeToDOM(data.value);
      } else {
        // Apply default theme
        setTheme(defaultTheme);
        applyThemeToDOM(defaultTheme);
      }
      
      setLoading(false);
    }
    
    loadTheme();
  }, []);
  
  // Apply theme to DOM as CSS variables
  function applyThemeToDOM(theme) {
    const root = document.documentElement;
    
    // Apply colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    
    // Apply typography
    root.style.setProperty('--font-family', theme.typography.fontFamily);
    root.style.setProperty('--heading-font', theme.typography.headingFont);
    
    // Apply other theme properties
    // ...
  }
  
  return (
    <ThemeContext.Provider value={{ theme, loading }}>
      {children}
    </ThemeContext.Provider>
  );
}

export const useTheme = () => useContext(ThemeContext);
```

### 3. CSS Implementation

Our CSS uses variables that reference the theme:

```css
/* global.css */
:root {
  /* Default theme variables (fallback) */
  --color-primary: #4A90E2;
  --color-secondary: #50E3C2;
  --color-accent: #B8E986;
  --color-background: #FFFFFF;
  --color-text: #333333;
  
  --font-family: 'Inter', sans-serif;
  --heading-font: 'Inter', sans-serif;
  
  /* Other default variables */
}

/* These styles will use the tenant-specific variables */
.button-primary {
  background-color: var(--color-primary);
  color: white;
  font-family: var(--font-family);
  border-radius: var(--button-radius, 4px);
}

.navbar {
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

/* Component variations based on theme settings */
.card[data-style="elevated"] {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card[data-style="flat"] {
  border: 1px solid var(--color-border);
}
```

### 4. Component Customization

Components can adapt based on theme settings:

```jsx
// Button.jsx
import { useTheme } from '../context/ThemeProvider';

export function Button({ children, variant = 'primary', ...props }) {
  const { theme } = useTheme();
  
  // Apply button style from theme
  const buttonStyle = theme?.components?.buttonStyle || 'default';
  
  return (
    <button 
      className={`button-${variant}`} 
      data-style={buttonStyle}
      {...props}
    >
      {children}
    </button>
  );
}
```

### 5. Layout Customization

Layouts can be configured per tenant:

```jsx
// MainLayout.jsx
import { useTheme } from '../context/ThemeProvider';
import { SideNav, TopNav } from '../components/Navigation';
import { StandardFooter, MinimalFooter } from '../components/Footer';

export function MainLayout({ children }) {
  const { theme } = useTheme();
  
  // Determine layout components based on theme
  const navbarStyle = theme?.layout?.navbarStyle || 'top';
  const footerStyle = theme?.layout?.footerStyle || 'standard';
  
  return (
    <div className="main-layout">
      {navbarStyle === 'side' ? <SideNav /> : <TopNav />}
      
      <main style={{ maxWidth: theme?.layout?.maxWidth || '1200px' }}>
        {children}
      </main>
      
      {footerStyle === 'minimal' ? <MinimalFooter /> : <StandardFooter />}
    </div>
  );
}
```

## Theme Administration

Each tenant can customize their theme through an admin interface:

1. **Theme Editor**: Visual interface for selecting colors, fonts, etc.
2. **Layout Options**: Configure layout preferences
3. **Component Styles**: Choose styles for buttons, cards, etc.
4. **Logo Upload**: Upload and position tenant logo
5. **Preview Mode**: See changes in real-time before publishing

## Advanced Customization

For tenants requiring deeper customization:

1. **Custom CSS Injection**: Allow tenants to add custom CSS
2. **Component Overrides**: Enable specific component replacements
3. **Page Templates**: Offer industry-specific page templates
4. **Custom Pages**: Allow tenants to create unique pages

## Implementation Considerations

1. **Performance**: Theme settings are cached for performance
2. **Fallbacks**: Default values ensure the UI works even with incomplete themes
3. **Validation**: Theme settings are validated to prevent CSS injection attacks
4. **Versioning**: Theme schema is versioned to allow safe updates

This approach gives tenants significant control over their brand experience while maintaining a single, manageable codebase.
