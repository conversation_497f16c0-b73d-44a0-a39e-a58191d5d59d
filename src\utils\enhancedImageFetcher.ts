/**
 * Enhanced image fetcher with robust CORS handling and fallbacks
 */

// List of CORS proxies to try in order
const CORS_PROXIES = [
  (url: string) => `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`,
  (url: string) => `https://corsproxy.io/?${encodeURIComponent(url)}`,
  (url: string) => `https://api.codetabs.com/v1/proxy?quest=${encodeURIComponent(url)}`,
  (url: string) => `https://cors-anywhere.herokuapp.com/${url}`
];

// Cache for successful image URLs to avoid refetching
const imageCache = new Map<string, string>();

/**
 * Fetches an image with CORS handling and fallbacks
 * @param url The image URL to fetch
 * @returns Promise resolving to a data URL of the image
 */
export async function fetchImageWithCors(url: string): Promise<string> {
  // Check cache first
  if (imageCache.has(url)) {
    return imageCache.get(url)!;
  }

  // Try direct fetch first
  try {
    console.log(`Attempting direct fetch of: ${url}`);
    const response = await fetch(url, {
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/*,*/*;q=0.8',
        'Referer': window.location.origin,
        'Origin': window.location.origin
      },
      referrerPolicy: 'no-referrer-when-downgrade'
    });
    
    if (response.ok) {
      const blob = await response.blob();
      if (blob.type.startsWith('image/')) {
        const dataUrl = await blobToDataUrl(blob);
        imageCache.set(url, dataUrl);
        return dataUrl;
      }
    }
  } catch (error) {
    console.log(`Direct fetch failed, trying CORS proxies...`, error);
  }

  // Try each CORS proxy
  for (const proxy of CORS_PROXIES) {
    try {
      const proxyUrl = proxy(url);
      console.log(`Trying proxy: ${proxyUrl.substring(0, 60)}...`);
      
      const response = await fetch(proxyUrl, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'image/*,*/*;q=0.8',
          'Referer': window.location.origin,
          'Origin': window.location.origin
        },
        referrerPolicy: 'no-referrer-when-downgrade'
      });
      
      if (response.ok) {
        const blob = await response.blob();
        if (blob.type.startsWith('image/')) {
          const dataUrl = await blobToDataUrl(blob);
          imageCache.set(url, dataUrl);
          return dataUrl;
        }
      }
    } catch (error) {
      console.warn(`Proxy fetch failed:`, error);
      continue;
    }
  }

  throw new Error('All image fetch attempts failed');
}

/**
 * Converts a blob to a data URL
 */
async function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Fetches and processes an image, returning a data URL
 * @param url The image URL to process
 * @returns Promise resolving to a data URL
 */
export async function processImageUrl(url: string): Promise<string> {
  try {
    // If it's already a data URL, return it
    if (url.startsWith('data:')) {
      return url;
    }
    
    // Try to fetch with CORS handling
    return await fetchImageWithCors(url);
  } catch (error) {
    console.error('Error processing image URL:', error);
    throw error;
  }
}

/**
 * Generates a unique cache key for a URL to avoid collisions
 */
function getCacheKey(url: string): string {
  try {
    const urlObj = new URL(url);
    // Include hostname and pathname in the key, but not query params which might contain timestamps
    return `${urlObj.hostname}${urlObj.pathname}`;
  } catch (e) {
    // If URL parsing fails, use the full URL as key
    return url;
  }
}

/**
 * Fetches multiple images in parallel with concurrency control
 * @param urls Array of image URLs to fetch
 * @param concurrency Maximum number of concurrent fetches (default: 3)
 * @returns Promise resolving to an array of data URLs in the same order as input
 */
export async function fetchMultipleImages(
  urls: string[],
  concurrency: number = 3
): Promise<string[]> {
  const results: string[] = [];
  const pending = new Set<Promise<void>>();
  
  // Process URLs with concurrency control
  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    
    // Skip empty URLs
    if (!url) {
      results[i] = '';
      continue;
    }
    
    // If we've reached concurrency limit, wait for one to finish
    if (pending.size >= concurrency) {
      await Promise.race(pending);
    }
    
    // Process this URL
    const promise = (async () => {
      try {
        results[i] = await processImageUrl(url);
      } catch (error) {
        console.warn(`Failed to fetch image ${i + 1}/${urls.length}:`, error);
        results[i] = ''; // Use empty string for failed fetches
      } finally {
        pending.delete(promise);
      }
    })();
    
    pending.add(promise);
  }
  
  // Wait for all remaining promises to complete
  await Promise.all(pending);
  
  return results;
}
