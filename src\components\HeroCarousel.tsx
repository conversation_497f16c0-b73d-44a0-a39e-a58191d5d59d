
import React, { useEffect, useRef } from 'react';
import { AnimatePresence } from 'framer-motion';
import CarouselNavigation from './carousel/CarouselNavigation';
import CarouselContent from './carousel/CarouselContent';
import CarouselSlide from './carousel/CarouselSlide';
import { useCarousel } from './carousel/useCarousel';

// Define the image paths using the uploaded images
const heroImages = [
  "/lovable-uploads/8d0fe3ba-d6c9-42b3-9666-bcc8ab973b17.png", // CBD oil drop image
  "/lovable-uploads/f32dd051-8e0c-4728-b048-e7c116f409a3.png", // RAW smoking accessories image
  "/lovable-uploads/cd204b16-527a-4916-8199-c490d75d34ba.png"  // CBD oils and cannabis leaf image
];

const HeroCarousel: React.FC = () => {
  const {
    currentIndex,
    direction,
    isAutoPlaying,
    handlePrevious,
    handleNext,
    handleDotClick,
  } = useCarousel({ images: heroImages });

  // Parallax effect references
  const parallaxRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const heroRef = useRef<HTMLDivElement>(null);
  
  // Enhanced logging to debug image paths
  useEffect(() => {
    console.log("Current slide index:", currentIndex);
    console.log("Current image path:", heroImages[currentIndex]);
  }, [currentIndex]);
  
  // Strong parallax effect with smooth transitions
  useEffect(() => {
    const handleScroll = () => {
      if (!parallaxRef.current || !contentRef.current || !heroRef.current) return;
      
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      
      // If we're in the viewport range where the parallax should happen
      if (scrollPosition < windowHeight * 1.2) {
        // Stronger parallax effect for the content - moves faster than the background
        contentRef.current.style.transform = `translateY(${scrollPosition * 0.5}px)`;
        contentRef.current.style.opacity = `${1 - (scrollPosition * 0.0015)}`;
        
        // Background parallax effect - slower movement for depth perception
        heroRef.current.style.transform = `translateY(${scrollPosition * 0.3}px)`;
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section ref={parallaxRef} className="relative h-screen overflow-hidden">
      {/* Image Carousel with Smooth Dissolve Animations */}
      <div ref={heroRef} className="absolute inset-0 bg-gray-200 transition-transform duration-200 ease-out">
        <AnimatePresence initial={false} custom={direction} mode="sync">
          {heroImages.map((image, index) => (
            <CarouselSlide
              key={`slide-${index}`}
              imagePath={image}
              index={index}
              currentIndex={currentIndex}
              direction={direction}
              totalImages={heroImages.length}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Content with Parallax Effect */}
      <div ref={contentRef} className="relative h-full z-10 transition-transform duration-200 ease-out">
        <CarouselContent />
      </div>
      
      {/* Custom Navigation Controls */}
      <CarouselNavigation
        onPrevious={handlePrevious}
        onNext={handleNext}
        currentIndex={currentIndex}
        totalImages={heroImages.length}
        isAutoPlaying={isAutoPlaying}
        onDotClick={handleDotClick}
      />
      
      {/* Scroll indicator animation */}
      <div className="absolute bottom-8 left-0 right-0 flex justify-center animate-bounce">
        <div className="w-8 h-12 rounded-full border-2 border-white flex items-start justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroCarousel;
