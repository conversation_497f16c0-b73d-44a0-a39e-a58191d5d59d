import { useEffect } from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Truck, Clock, Zap } from "lucide-react";
import { useCheckoutShipping } from "@/hooks/useShipping";
import { useAuth } from '@/hooks/auth.basic';

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
  icon: 'standard' | 'express' | 'nextDay';
  is_active?: boolean;
}

interface ShippingMethodSelectorProps {
  methods: ShippingMethod[];
  selectedMethodId: string;
  onSelect: (methodId: string) => void;
  disabled?: boolean;
  forceStrictFilter?: boolean;
}

export function ShippingMethodSelector({
  methods,
  selectedMethodId,
  onSelect,
  disabled = false,
  forceStrictFilter = false
}: ShippingMethodSelectorProps) {
  // Get refresh function but don't call it automatically on each mount
  const { refreshShippingMethods } = useCheckoutShipping();
  const { isAdmin } = useAuth();
  
  // Log all methods for debugging
  console.log('ShippingMethodSelector - ALL methods received:', methods);
  
  // EXTREMELY STRICT filter with extra logging
  const filteredMethods = methods.filter(method => {
    // Enhanced logging for each method
    console.log(`Filtering method: ${method.name} (${method.id})`);
    console.log(`- is_active value: ${method.is_active}`);
    console.log(`- is_active type: ${typeof method.is_active}`);
    console.log(`- method.is_active === true: ${method.is_active === true}`);
    
    // For admin users, show all shipping methods regardless of active status
    if (isAdmin && !forceStrictFilter) {
      return method && method.id; // Just basic validation for admins
    }
    
    // For regular users or when forceStrictFilter is true, only show active methods
    return method && 
      method.id && 
      method.is_active === true; // Strict boolean check for true only
  });
  
  // Log methods for debugging but don't trigger automatic refreshes
  useEffect(() => {
    console.log('[ShippingMethodSelector] Available methods:', methods.length);
    console.log('[ShippingMethodSelector] Filtered active methods:', filteredMethods.length);
    console.log('[ShippingMethodSelector] Active methods:', filteredMethods);
    
    // If we have no shipping methods after filtering, log a warning
    if (methods.length > 0 && filteredMethods.length === 0) {
      console.warn('All shipping methods were filtered out - something might be wrong with the data');
      console.log('Original methods with is_active values:', methods.map(m => ({
        id: m.id,
        name: m.name,
        is_active: m.is_active,
        type: typeof m.is_active
      })));
    }
  }, [methods, filteredMethods]);
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(price);
  };

  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'standard':
        return <Truck className="h-5 w-5 text-primary" />;
      case 'express':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'nextDay':
        return <Zap className="h-5 w-5 text-green-500" />;
      default:
        return <Truck className="h-5 w-5 text-primary" />;
    }
  };

  // If the selected method is no longer active, reset it
  useEffect(() => {
    if (selectedMethodId && !filteredMethods.some(m => m.id === selectedMethodId) && filteredMethods.length > 0) {
      onSelect(filteredMethods[0].id);
    }
  }, [selectedMethodId, filteredMethods, onSelect]);

  // If there are no methods at all, show a message
  if (filteredMethods.length === 0) {
    return (
      <div className="text-center p-4 border rounded-md bg-gray-50">
        <p className="text-gray-500">No shipping methods available at this time.</p>
      </div>
    );
  }

  return (
    <RadioGroup
      value={selectedMethodId}
      onValueChange={onSelect}
      className="space-y-3"
      disabled={disabled}
    >
      {filteredMethods.map((method) => (
        <Card
          key={method.id}
          className={`cursor-pointer transition-all ${
            selectedMethodId === method.id
              ? 'border-primary'
              : 'hover:border-gray-300'
          }`}
        >
          <CardContent className="p-4">
            <RadioGroupItem
              value={method.id}
              id={`shipping-${method.id}`}
              className="peer sr-only"
            />
            <Label
              htmlFor={`shipping-${method.id}`}
              className="flex items-start cursor-pointer"
            >
              <div className="flex-shrink-0 mt-0.5 mr-3">
                {getIcon(method.icon)}
              </div>
              <div className="flex-grow">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{method.name}</span>
                  <span className="font-medium">
                    {method.price === 0
                      ? 'FREE'
                      : formatPrice(method.price)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {method.description}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Estimated delivery: {method.estimatedDays}
                </p>
              </div>
            </Label>
          </CardContent>
        </Card>
      ))}
    </RadioGroup>
  );
}
