import { useEffect } from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Truck, Clock, Zap } from "lucide-react";

export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  price: number;
  estimatedDays: string;
  icon: 'standard' | 'express' | 'nextDay';
  is_active?: boolean;
}

interface ShippingMethodSelectorProps {
  methods: ShippingMethod[];
  selectedMethodId: string;
  onSelect: (methodId: string) => void;
  disabled?: boolean;
}

export function ShippingMethodSelector({
  methods,
  selectedMethodId,
  onSelect,
  disabled = false
}: ShippingMethodSelectorProps) {
  // More aggressive filtering to ensure only active methods appear
  const filteredMethods = methods.filter(method => 
    method && 
    method.id && 
    // Double check method is active - stricter check than before
    method.is_active !== false
  );
  
  console.log('[ShippingMethodSelector] Available methods before filtering:', methods.length);
  console.log('[ShippingMethodSelector] Filtered methods after active check:', filteredMethods.length);
  
  // If we have no shipping methods after filtering, log a warning
  useEffect(() => {
    if (methods.length > 0 && filteredMethods.length === 0) {
      console.warn('All shipping methods were filtered out - something might be wrong with the data');
      console.log('Original methods:', methods);
    }
  }, [methods, filteredMethods]);
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(price);
  };

  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'standard':
        return <Truck className="h-5 w-5 text-primary" />;
      case 'express':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'nextDay':
        return <Zap className="h-5 w-5 text-green-500" />;
      default:
        return <Truck className="h-5 w-5 text-primary" />;
    }
  };

  // If the selected method is no longer active, reset it
  useEffect(() => {
    if (selectedMethodId && !filteredMethods.some(m => m.id === selectedMethodId) && filteredMethods.length > 0) {
      onSelect(filteredMethods[0].id);
    }
  }, [selectedMethodId, filteredMethods, onSelect]);

  return (
    <RadioGroup
      value={selectedMethodId}
      onValueChange={onSelect}
      className="space-y-3"
      disabled={disabled}
    >
      {filteredMethods.map((method) => (
        <Card
          key={method.id}
          className={`cursor-pointer transition-all ${
            selectedMethodId === method.id
              ? 'border-primary'
              : 'hover:border-gray-300'
          }`}
        >
          <CardContent className="p-4">
            <RadioGroupItem
              value={method.id}
              id={`shipping-${method.id}`}
              className="peer sr-only"
            />
            <Label
              htmlFor={`shipping-${method.id}`}
              className="flex items-start cursor-pointer"
            >
              <div className="flex-shrink-0 mt-0.5 mr-3">
                {getIcon(method.icon)}
              </div>
              <div className="flex-grow">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{method.name}</span>
                  <span className="font-medium">
                    {method.price === 0
                      ? 'FREE'
                      : formatPrice(method.price)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {method.description}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Estimated delivery: {method.estimatedDays}
                </p>
              </div>
            </Label>
          </CardContent>
        </Card>
      ))}
    </RadioGroup>
  );
}
