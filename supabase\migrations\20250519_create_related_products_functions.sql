-- Migration to create functions for managing related products

-- Function to update product relationships
CREATE OR REPLACE FUNCTION public.update_product_relationships(
  p_product_id UUID,
  p_related_ids UUID[],
  p_relationship_type TEXT DEFAULT 'related'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  related_id UUID;
  display_order INTEGER;
BEGIN
  -- Delete existing relationships for this product
  DELETE FROM public.related_products
  WHERE product_id = p_product_id;
  
  -- Insert new relationships with proper display order
  display_order := 0;
  FOREACH related_id IN ARRAY p_related_ids
  LOOP
    INSERT INTO public.related_products (
      product_id,
      related_product_id,
      display_order
    ) VALUES (
      p_product_id,
      related_id,
      display_order
    );
    display_order := display_order + 1;
  END LOOP;
  
  RETURN TRUE;
END;
$$;

-- Function to get related products for a product
CREATE OR REPLACE FUNCTION public.get_related_products(
  product_id_param UUID
)
RETURNS TABLE (
  related_product_id UUID,
  display_order INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT rp.related_product_id, rp.display_order
  FROM public.related_products rp
  WHERE rp.product_id = product_id_param
  ORDER BY rp.display_order ASC;
END;
$$;
