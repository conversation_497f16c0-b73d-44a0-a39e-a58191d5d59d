// Direct fix for shipping methods
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function directFix() {
  console.log('🔧 Direct fix for shipping methods...');
  
  try {
    // 1. Get all shipping methods
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} total shipping methods`);
    
    // Find the Next Day Delivery method
    const nextDayMethod = methods.find(m => m.name.includes('Next Day'));
    if (!nextDayMethod) {
      console.log('Next Day Delivery method not found');
      return;
    }
    
    console.log('Next Day Delivery method found:');
    console.log(`- ID: ${nextDayMethod.id}`);
    console.log(`- Name: ${nextDayMethod.name}`);
    console.log(`- Active: ${nextDayMethod.is_active}`);
    console.log(`- Type of is_active: ${typeof nextDayMethod.is_active}`);
    
    // 2. Force update ALL shipping methods to ensure consistency
    console.log('\nUpdating ALL shipping methods to ensure data consistency...');
    
    for (const method of methods) {
      // Force the Next Day method to be inactive, keep others as they are
      const shouldBeActive = method.id === nextDayMethod.id ? false : Boolean(method.is_active);
      
      console.log(`Updating ${method.name} (${method.id}) to is_active=${shouldBeActive}`);
      
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ 
          is_active: shouldBeActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', method.id);
        
      if (updateError) {
        console.error(`Error updating ${method.name}:`, updateError);
      } else {
        console.log(`✅ ${method.name} updated successfully`);
      }
    }
    
    // 3. Verify the update for Next Day Delivery
    const { data: verifyData, error: verifyError } = await supabase
      .from('shipping_methods')
      .select('*')
      .eq('id', nextDayMethod.id)
      .single();
      
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('\nVerification:');
    console.log(`- Name: ${verifyData.name}`);
    console.log(`- Active: ${verifyData.is_active}`);
    console.log(`- Type of is_active: ${typeof verifyData.is_active}`);
    
    if (verifyData.is_active === false) {
      console.log('✅ Next Day Delivery is confirmed inactive');
    } else {
      console.log('❌ Next Day Delivery is still active - database update failed');
    }
    
    // 4. Update the database schema to ensure is_active is properly typed
    console.log('\nUpdating database schema to ensure proper typing...');
    
    const { error: schemaError } = await supabase.rpc('execute_sql', {
      sql: `
        ALTER TABLE shipping_methods 
        ALTER COLUMN is_active SET DATA TYPE boolean USING is_active::boolean;
      `
    });
    
    if (schemaError) {
      console.error('Error updating schema:', schemaError);
      console.log('Trying alternative approach...');
      
      // Try direct SQL approach
      const { error: directError } = await supabase
        .from('shipping_methods')
        .update({ is_active: false })
        .eq('id', nextDayMethod.id);
        
      if (directError) {
        console.error('Error with direct update:', directError);
      } else {
        console.log('✅ Direct update successful');
      }
    } else {
      console.log('✅ Schema updated successfully');
    }
    
    console.log('\n🔄 To ensure changes take effect:');
    console.log('1. Restart the development server');
    console.log('2. Clear your browser cache or use incognito mode');
    console.log('3. Check the admin panel and checkout page');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
directFix()
  .catch(err => {
    console.error('Error running script:', err);
  });
