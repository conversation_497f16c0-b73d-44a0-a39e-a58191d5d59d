/**
 * Social Media Publishing System - Publisher Interface
 * 
 * This file defines the core interfaces for the social media publishing system.
 * It establishes the contract that all platform-specific publishers must implement.
 */

import {
  PostContent,
  PublishResult,
  PostStatusInfo,
  SocialMediaAccount,
  TokenData,
  PostAnalytics,
  ContentValidationResult,
  PostDraft
} from './social_media_types';

/**
 * Interface for platform-specific social media publishers
 */
export interface SocialMediaPublisher {
  /**
   * Authenticate with the social media platform
   * @returns Promise resolving to boolean indicating success
   */
  authenticate(): Promise<boolean>;
  
  /**
   * Refresh the authentication token
   * @returns Promise resolving to boolean indicating success
   */
  refreshToken(): Promise<boolean>;
  
  /**
   * Publish content to the social media platform
   * @param content The content to publish
   * @returns Promise resolving to the publish result
   */
  publishPost(content: PostContent): Promise<PublishResult>;
  
  /**
   * Get the status of a published post
   * @param postId The platform-specific post ID
   * @returns Promise resolving to the post status
   */
  getPostStatus(postId: string): Promise<PostStatusInfo>;
  
  /**
   * Delete a post from the platform
   * @param postId The platform-specific post ID
   * @returns Promise resolving to boolean indicating success
   */
  deletePost(postId: string): Promise<boolean>;
  
  /**
   * Validate content against platform-specific policies
   * @param content The content to validate
   * @returns Promise resolving to validation result
   */
  validateContent(content: PostContent): Promise<ContentValidationResult>;
  
  /**
   * Get analytics for a specific post
   * @param postId The platform-specific post ID
   * @returns Promise resolving to post analytics
   */
  getPostAnalytics(postId: string): Promise<PostAnalytics>;
}

/**
 * Interface for token management
 */
export interface TokenManager {
  /**
   * Get a valid access token for an account
   * @param accountId The account ID
   * @returns Promise resolving to access token
   */
  getAccessToken(accountId: string): Promise<string>;
  
  /**
   * Store token data for an account
   * @param accountId The account ID
   * @param tokenData The token data to store
   * @returns Promise resolving to boolean indicating success
   */
  storeTokenData(accountId: string, tokenData: TokenData): Promise<boolean>;
  
  /**
   * Refresh token for an account
   * @param accountId The account ID
   * @returns Promise resolving to boolean indicating success
   */
  refreshToken(accountId: string): Promise<boolean>;
  
  /**
   * Check if token is valid
   * @param accountId The account ID
   * @returns Promise resolving to boolean indicating if token is valid
   */
  isTokenValid(accountId: string): Promise<boolean>;
}

/**
 * Interface for error handling
 */
export interface ErrorHandler {
  /**
   * Handle an error from a social media API
   * @param error The error to handle
   * @param platform The platform where the error occurred
   * @param operation The operation that caused the error
   * @returns Never returns, always throws an appropriate error
   */
  handle(error: any, platform: string, operation: string): never;
  
  /**
   * Log an error
   * @param error The error to log
   * @param context Additional context for the error
   */
  logError(error: any, context?: Record<string, any>): void;
}

/**
 * Interface for rate limiting
 */
export interface RateLimiter {
  /**
   * Check if operation is within rate limits and update usage
   * @param accountId The account ID
   * @param operation The operation to check
   * @returns Promise resolving to boolean indicating if operation is allowed
   */
  checkAndUpdateRateLimit(accountId: string, operation: string): Promise<boolean>;
  
  /**
   * Get estimated time until operation is allowed
   * @param accountId The account ID
   * @param operation The operation to check
   * @returns Promise resolving to milliseconds until operation is allowed
   */
  getTimeUntilAllowed(accountId: string, operation: string): Promise<number>;
}

/**
 * Abstract base class for social media publishers
 * Implements common functionality for all publishers
 */
export abstract class BaseSocialMediaPublisher implements SocialMediaPublisher {
  protected accountId: string;
  protected tokenManager: TokenManager;
  protected errorHandler: ErrorHandler;
  protected rateLimiter: RateLimiter;
  protected account: SocialMediaAccount | null = null;
  
  /**
   * Create a new BaseSocialMediaPublisher
   * @param accountId The account ID
   * @param tokenManager The token manager
   * @param errorHandler The error handler
   * @param rateLimiter The rate limiter
   */
  constructor(
    accountId: string,
    tokenManager: TokenManager,
    errorHandler: ErrorHandler,
    rateLimiter: RateLimiter
  ) {
    this.accountId = accountId;
    this.tokenManager = tokenManager;
    this.errorHandler = errorHandler;
    this.rateLimiter = rateLimiter;
  }
  
  /**
   * Get the account information
   * @returns Promise resolving to account information
   */
  protected abstract getAccountInfo(): Promise<SocialMediaAccount>;
  
  /**
   * Authenticate with the social media platform
   * @returns Promise resolving to boolean indicating success
   */
  abstract authenticate(): Promise<boolean>;
  
  /**
   * Refresh the authentication token
   * @returns Promise resolving to boolean indicating success
   */
  abstract refreshToken(): Promise<boolean>;
  
  /**
   * Publish content to the social media platform
   * @param content The content to publish
   * @returns Promise resolving to the publish result
   */
  abstract publishPost(content: PostContent): Promise<PublishResult>;
  
  /**
   * Get the status of a published post
   * @param postId The platform-specific post ID
   * @returns Promise resolving to the post status
   */
  abstract getPostStatus(postId: string): Promise<PostStatusInfo>;
  
  /**
   * Delete a post from the platform
   * @param postId The platform-specific post ID
   * @returns Promise resolving to boolean indicating success
   */
  abstract deletePost(postId: string): Promise<boolean>;
  
  /**
   * Validate content against platform-specific policies
   * @param content The content to validate
   * @returns Promise resolving to validation result
   */
  abstract validateContent(content: PostContent): Promise<ContentValidationResult>;
  
  /**
   * Get analytics for a specific post
   * @param postId The platform-specific post ID
   * @returns Promise resolving to post analytics
   */
  abstract getPostAnalytics(postId: string): Promise<PostAnalytics>;
  
  /**
   * Get a valid access token
   * @returns Promise resolving to access token
   */
  protected async getAccessToken(): Promise<string> {
    try {
      const isValid = await this.tokenManager.isTokenValid(this.accountId);
      
      if (!isValid) {
        await this.tokenManager.refreshToken(this.accountId);
      }
      
      return this.tokenManager.getAccessToken(this.accountId);
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'getAccessToken');
    }
  }
  
  /**
   * Check if operation is within rate limits
   * @param operation The operation to check
   * @returns Promise resolving to boolean indicating if operation is allowed
   */
  protected async checkRateLimit(operation: string): Promise<boolean> {
    try {
      return this.rateLimiter.checkAndUpdateRateLimit(this.accountId, operation);
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'checkRateLimit');
    }
  }
  
  /**
   * Get the platform name
   * @returns The platform name
   */
  protected abstract getPlatformName(): string;
}

/**
 * Factory for creating platform-specific publishers
 */
export interface PublisherFactory {
  /**
   * Create a publisher for a specific platform
   * @param platform The platform to create a publisher for
   * @param accountId The account ID
   * @returns The created publisher
   */
  createPublisher(platform: string, accountId: string): SocialMediaPublisher;
}

/**
 * Interface for the unified social media publishing service
 */
export interface SocialMediaPublishingService {
  /**
   * Connect a social media account
   * @param platform The platform to connect
   * @param redirectUrl The OAuth redirect URL
   * @returns Promise resolving to authorization URL
   */
  connectAccount(platform: string, redirectUrl: string): Promise<string>;
  
  /**
   * Complete OAuth connection with authorization code
   * @param platform The platform being connected
   * @param code The authorization code
   * @param redirectUrl The OAuth redirect URL
   * @param userId The user ID
   * @returns Promise resolving to the connected account
   */
  completeAccountConnection(platform: string, code: string, redirectUrl: string, userId: string): Promise<SocialMediaAccount>;
  
  /**
   * Get connected accounts for a user
   * @param userId The user ID
   * @returns Promise resolving to array of connected accounts
   */
  getConnectedAccounts(userId: string): Promise<SocialMediaAccount[]>;
  
  /**
   * Disconnect a social media account
   * @param accountId The account ID to disconnect
   * @returns Promise resolving to boolean indicating success
   */
  disconnectAccount(accountId: string): Promise<boolean>;
  
  /**
   * Create a post draft
   * @param content The post content
   * @param userId The user ID
   * @returns Promise resolving to the created draft
   */
  createPostDraft(content: PostContent, userId: string): Promise<PostDraft>;
  
  /**
   * Schedule a post for later publishing
   * @param content The post content
   * @param accountId The account ID to publish to
   * @param scheduledTime The time to publish
   * @returns Promise resolving to the publish result
   */
  schedulePost(content: PostContent, accountId: string, scheduledTime: Date): Promise<PublishResult>;
  
  /**
   * Publish a post immediately
   * @param draft The post draft
   * @param accountId The account ID to publish to
   * @returns Promise resolving to the publish result
   */
  publishNow(draft: PostContent, accountId: string): Promise<PublishResult>;
  
  /**
   * Get scheduled posts
   * @param userId The user ID
   * @param filters Optional filters
   * @returns Promise resolving to array of scheduled posts
   */
  getScheduledPosts(userId: string, filters?: any): Promise<any[]>;
  
  /**
   * Cancel a scheduled post
   * @param postId The post ID to cancel
   * @returns Promise resolving to boolean indicating success
   */
  cancelScheduledPost(postId: string): Promise<boolean>;
  
  /**
   * Get analytics for a post
   * @param postId The post ID
   * @returns Promise resolving to post analytics
   */
  getPostAnalytics(postId: string): Promise<PostAnalytics>;
}
