// /api/proxy.ts - Backend proxy endpoint for handling CORS issues with images
import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

// List of allowed domains for security
const ALLOWED_DOMAINS = [
  'unsplash.com', 'images.unsplash.com',
  'pexels.com', 'images.pexels.com',
  'pixabay.com', 'cdn.pixabay.com',
  // Add more trusted domains as needed
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { url } = req.query;
  
  // Validate URL parameter
  if (!url || typeof url !== 'string') {
    return res.status(400).json({ error: 'Image URL is required' });
  }
  
  try {
    // Decode and validate the URL
    const imageUrl = decodeURIComponent(url);
    const urlObj = new URL(imageUrl);
    
    // Security check: only allow specific domains
    const isDomainAllowed = ALLOWED_DOMAINS.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
    );
    
    if (!isDomainAllowed) {
      console.warn(`Blocked request to non-allowed domain: ${urlObj.hostname}`);
      return res.status(403).json({ error: 'Domain not allowed for security reasons' });
    }
    
    // Fetch the image
    const response = await axios({
      method: 'get',
      url: imageUrl,
      responseType: 'arraybuffer',
      timeout: 5000,
      headers: {
        'User-Agent': 'BitsNBongs/1.0',
        'Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://bitsnbongs.com'
      }
    });
    
    // Set appropriate headers
    res.setHeader('Content-Type', response.headers['content-type'] || 'image/jpeg');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
    
    // Return the image data
    return res.send(Buffer.from(response.data));
    
  } catch (error) {
    console.error('Image proxy error:', error);
    return res.status(500).json({ error: 'Failed to proxy image' });
  }
}
