# PayPal Integration Guide

This guide explains how to set up and use the PayPal integration for the Bits N Bongs e-commerce platform.

## Overview

The PayPal integration allows customers to pay for their orders using PayPal. The integration includes:

- PayPal checkout button in the payment step of the checkout process
- Payment processing service that handles PayPal transactions
- Webhook handler for PayPal payment notifications
- Database tables to store payment information

## Setup Instructions

### 1. Apply Database Migration

First, apply the database migration to create the necessary tables:

```bash
node scripts/apply-migration.js migrations/add_payment_tables.sql
```

This will create:
- `payment_sessions` table to store payment information
- Add `payment_provider` and `payment_session_id` fields to the `orders` table

### 2. Configure PayPal Email

Set your PayPal merchant email in the environment variables:

1. Open your `.env` file
2. Add the following line:

```
VITE_PAYPAL_MERCHANT_EMAIL=<EMAIL>
```

Replace `<EMAIL>` with your actual PayPal merchant email address.

## How It Works

### Checkout Flow

1. Customer selects PayPal as the payment method in the checkout process
2. Customer clicks the "Pay with PayPal" button
3. A payment session is created in the database
4. Customer is redirected to PayPal to complete the payment
5. After payment, customer is redirected back to the confirmation page
6. The payment is verified and the order is created

### Webhook Handling

PayPal sends notifications to the webhook endpoint when payment status changes. The webhook handler:

1. Receives the notification from PayPal
2. Verifies the payment with PayPal
3. Updates the payment session status
4. Creates or updates the order

## Testing

To test the PayPal integration:

1. Add items to your cart
2. Proceed to checkout
3. Select PayPal as the payment method
4. Click the "Pay with PayPal" button
5. Complete the payment on the PayPal site
6. You should be redirected back to the confirmation page

## Future Enhancements

- Add PayPal SDK for a more seamless checkout experience
- Implement PayPal Smart Buttons for a better user experience
- Add support for PayPal subscriptions

## Troubleshooting

### Common Issues

- **Payment not being processed**: Check that your PayPal email is correctly configured
- **Webhook not receiving notifications**: Ensure your server is publicly accessible
- **Database errors**: Verify that the migration was applied correctly

### Logs

Check the server logs for any errors related to PayPal integration. Look for log messages with:

- "PayPal checkout error"
- "PayPal verification error"
- "Error processing PayPal webhook"

## Adding Stripe Integration

The payment service is designed to easily accommodate Stripe integration in the future. When you have the Stripe API keys, you'll need to:

1. Create a Stripe payment service class
2. Add Stripe checkout component
3. Update the payment step to include Stripe payment option
4. Create a webhook handler for Stripe events

The existing payment service structure makes it easy to add Stripe as an additional payment provider.