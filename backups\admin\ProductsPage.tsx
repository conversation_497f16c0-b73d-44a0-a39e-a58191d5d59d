import React, { useState, useEffect, useMemo, useRef } from "react";
import { useProductFormState } from "@/hooks/useProductFormState";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { PlusCircle, Edit, Trash, X, Upload, Check, Tag, Loader2, FilterX } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useCategoriesQuery } from "@/components/admin/product-form/useCategoriesQuery";
import { useBrandsQuery } from "@/components/admin/product-form/hooks/useBrandsQuery";
import { ProductForm } from "@/components/admin/ProductForm";

// Define a more flexible product type for our database results
interface ExtendedProduct {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  sale_price: number | null;
  cost_price: number | null;
  image: string | null;
  additional_images: string[] | null;
  sku: string | null;
  barcode?: string | null;
  quantity?: number | null;
  is_active: boolean;
  in_stock: boolean;
  is_featured: boolean;
  is_new: boolean;
  is_sale?: boolean;
  is_best_seller?: boolean;
  rating?: number;
  review_count?: number;
  created_at: string;
  updated_at: string | null;
  category_id: string | null;
  subcategory_id: string | null;
  brand_id: string | null;
  options: any;
  // Allow any additional properties
  [key: string]: any;
}

// Extend the category type to include parent_id
interface Category {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  created_at?: string;
  parent_id?: string | null;
}

export default function ProductsPage() {
  // Use the persistent form state hook
  const {
    isAddingOrEditing,
    selectedProduct,
    handleEdit,
    handleAddNewProduct,
    handleCloseForm
  } = useProductFormState();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string>("");
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [filterSubcategories, setFilterSubcategories] = useState<Category[]>([]);
  const [isBulkAssigning, setIsBulkAssigning] = useState(false);
  const [isBulkBrandAssigning, setIsBulkBrandAssigning] = useState(false);
  const [selectedBrandId, setSelectedBrandId] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>("");
  
  // Debounce search query to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  const [filterCategoryId, setFilterCategoryId] = useState<string>("all");
  const [filterSubcategoryId, setFilterSubcategoryId] = useState<string>("all");
  const [filterBrandId, setFilterBrandId] = useState<string>("all");
  const [filterInStock, setFilterInStock] = useState<string>("all");
  const [filterActive, setFilterActive] = useState<string>("active"); // Default to showing only active products
  const [isBulkStatusToggling, setIsBulkStatusToggling] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<boolean>(true);
  const [pageSize, setPageSize] = useState<number>(50);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  
  // Fetch brands for filtering
  const { data: brands } = useBrandsQuery();
  const queryClient = useQueryClient();

  // State for storing all loaded products
  const [products, setProducts] = useState<ExtendedProduct[]>([]);
  
  // State for product counts
  const [productCounts, setProductCounts] = useState({
    total: 0,
    active: 0,
    inactive: 0
  });
  
  // Fetch products with filtering
  const {
    data: queryData,
    isLoading,
    isFetching,
    error: productsError,
    refetch
  } = useQuery({
    queryKey: [
      "products", 
      filterCategoryId, 
      filterSubcategoryId, 
      filterBrandId, 
      filterInStock, 
      filterActive, 
      debouncedSearchQuery,
      currentPage,
      pageSize
    ],
    queryFn: async () => {
      try {
        // Build query with filters applied at the database level
        let query = supabase
          .from("products")
          .select("*", { count: 'exact' })
          .order("name");
        
        // Apply filters directly in the database query
        if (filterCategoryId !== "all") {
          query = query.eq('category_id', filterCategoryId);
        }
        
        if (filterSubcategoryId !== "all") {
          query = query.eq('subcategory_id', filterSubcategoryId);
        }
        
        if (filterBrandId !== "all") {
          query = query.eq('brand_id', filterBrandId);
        }
        
        if (filterInStock !== "all") {
          query = query.eq('in_stock', filterInStock === "in-stock");
        }
        
        if (filterActive !== "all") {
          query = query.eq('is_active', filterActive === "active");
        }
        
        // Apply search filter if provided
        if (debouncedSearchQuery && debouncedSearchQuery.trim() !== '') {
          const searchTerm = `%${debouncedSearchQuery.trim()}%`;
          
          // Fix the OR condition syntax for Supabase
          query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm},sku.ilike.${searchTerm}`);
        }
        
        // Apply pagination after filters
        query = query.range((currentPage - 1) * pageSize, currentPage * pageSize - 1);

        const { data, error, count } = await query;

        if (error) throw error;
        console.log(`Retrieved ${data?.length || 0} products from database (page ${currentPage}, total count: ${count || 'unknown'})`);
        
        // Update hasMore state based on count
        if (count) {
          setHasMore((currentPage * pageSize) < count);
        } else {
          setHasMore(data && data.length === pageSize);
        }
        
        // Ensure we return an empty array if data is null
        return data || [];
      } catch (error) {
        console.error("Error fetching products:", error);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  
  // When data changes, update the products state
  useEffect(() => {
    if (queryData) {
      if (currentPage === 1) {
        setProducts(queryData as ExtendedProduct[]);
      } else {
        // Append new products to existing ones
        setProducts(prev => [...prev, ...(queryData as ExtendedProduct[])]);
      }
      
      // Check if we have more products to load
      if (Array.isArray(queryData)) {
        setHasMore(queryData.length === pageSize);
      }
    }
  }, [queryData, currentPage, pageSize]);
  
  // Function to load more products
  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
  };
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("*")
          .order("name");
        
        if (error) {
          console.error("Error fetching categories:", error);
          return;
        }
        
        if (data) {
          // Cast the data to our Category type which includes parent_id
          const categoriesData = data as Category[];
          setCategories(categoriesData);
          console.log("Categories loaded:", categoriesData.length);
        }
      } catch (err) {
        console.error("Error in fetchCategories:", err);
      }
    };
    
    fetchCategories();
    fetchProductCounts();
  }, []);
  
  // Fetch product counts
  const fetchProductCounts = async () => {
    try {
      // Get total count
      const { count: totalCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });
      
      // Get active count
      const { count: activeCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);
      
      // Get inactive count
      const { count: inactiveCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', false);
      
      setProductCounts({
        total: totalCount || 0,
        active: activeCount || 0,
        inactive: inactiveCount || 0
      });
    } catch (error) {
      console.error('Error fetching product counts:', error);
    }
  };
  
  // Fetch subcategories when category changes (for bulk assignment)
  useEffect(() => {
    if (!selectedCategoryId) {
      setSubcategories([]);
      return;
    }
    
    const fetchSubcategories = async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .eq("parent_id", selectedCategoryId)
        .order("name");
      
      if (error) {
        console.error("Error fetching subcategories:", error);
        return;
      }
      
      if (data) {
        // Cast the data to our Category type
        setSubcategories(data as Category[]);
      }
    };
    
    fetchSubcategories();
  }, [selectedCategoryId]);
  
  // Fetch subcategories when filter category changes
  useEffect(() => {
    if (filterCategoryId === "all") {
      setFilterSubcategories([]);
      return;
    }
    
    console.log("Fetching subcategories for filter category:", filterCategoryId);
    
    const fetchFilterSubcategories = async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .eq("parent_id", filterCategoryId)
        .order("name");
      
      if (error) {
        console.error("Error fetching subcategories for filter:", error);
        return;
      }
      
      if (data) {
        console.log("Filter subcategories found:", data.length, data);
        // Cast the data to our Category type
        setFilterSubcategories(data as Category[]);
      } else {
        console.log("No filter subcategories found");
        setFilterSubcategories([]);
      }
    };
    
    fetchFilterSubcategories();
  }, [filterCategoryId]);

  // Delete product mutation
  const deleteProduct = useMutation({
    mutationFn: async (productId: string) => {
      // First, get the product to access its image URLs
      const { data: product, error: fetchError } = await supabase
        .from("products")
        .select("image, additional_images")
        .eq("id", productId)
        .single<{ image: string | null; additional_images: string[] | null }>();
      
      if (fetchError) throw fetchError;
      
      // Delete the product from the database
      const { error } = await supabase
        .from("products")
        .delete()
        .eq("id", productId);
      
      if (error) throw error;
      
      // If product had images, delete them from storage
      if (product) {
        try {
          // Helper function to extract filename from URL
          const getFilenameFromUrl = (url: string) => {
            if (!url) return null;
            
            // Log the URL for debugging
            console.log('Processing image URL:', url);
            
            // Handle both full URLs and relative paths
            let filename;
            if (url.includes('product-images/')) {
              // Extract the path after 'product-images/'
              const match = url.match(/product-images\/(.*?)($|\?)/i);
              filename = match ? match[1] : null;
            } else {
              // Just use the last part of the path
              const parts = url.split('/');
              filename = parts[parts.length - 1];
            }
            
            console.log('Extracted filename:', filename);
            return filename;
          };
          
          // Delete main image if exists
          if (product.image) {
            const filename = getFilenameFromUrl(product.image);
            if (filename) {
              console.log('Attempting to delete main image:', filename);
              const { data, error } = await supabase.storage
                .from('product-images')
                .remove([filename]);
              
              console.log('Delete result:', { data, error });
              if (error) {
                console.error('Error deleting main image:', error);
              } else {
                console.log('Successfully deleted main image:', data);
              }
            }
          }
          
          // Delete additional images if they exist
          if (product.additional_images && Array.isArray(product.additional_images)) {
            console.log('Processing additional images:', product.additional_images);
            const filenames = product.additional_images
              .map(getFilenameFromUrl)
              .filter(Boolean) as string[];
              
            console.log('Additional image filenames to delete:', filenames);
            if (filenames.length > 0) {
              const { data, error } = await supabase.storage
                .from('product-images')
                .remove(filenames);
                
              console.log('Additional images delete result:', { data, error });
              if (error) {
                console.error('Error deleting additional images:', error);
              } else {
                console.log('Successfully deleted additional images:', data);
              }
            }
          }
        } catch (storageError) {
          console.error('Error deleting product images:', storageError);
          // Continue with product deletion even if image deletion fails
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      setIsDeleteDialogOpen(false);
      // Refresh product counts after deletion
      fetchProductCounts();
      toast({
        title: "Product deleted",
        description: "The product has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error("Error deleting product:", error);
      toast({ 
        title: "Error", 
        description: `Failed to delete product: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    },
  });
  
  // Handle delete button click
  const handleDelete = (product: ExtendedProduct) => {
    // @ts-ignore - ExtendedProduct is compatible with Product but TypeScript doesn't recognize it
    handleEdit(product); // Use handleEdit from useProductFormState to set the selected product
    setIsDeleteDialogOpen(true);
  };
  
  // Get the product ID to delete from the cache
  const getProductIdToDelete = () => {
    return selectedProduct?.id;
  };
  
  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(product => product.id));
    }
    setSelectAll(!selectAll);
  };
  
  // Handle individual product selection
  const handleSelectProduct = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
      setSelectAll(false);
    } else {
      setSelectedProducts([...selectedProducts, productId]);
      if (selectedProducts.length + 1 === filteredProducts.length) {
        setSelectAll(true);
      }
    }
  };
  
  // Create a filtered products array based on the current filters
  const filteredProducts = useMemo(() => {
    return products || [];
  }, [products]);

  // Track if filters are being changed
  const [isChangingFilters, setIsChangingFilters] = useState(false);

  // Reset selections when products change
  useEffect(() => {
    if (products && products.length > 0) {
      setSelectAll(false);
      setSelectedProducts([]);
      setIsBulkAssigning(false);
      setIsBulkBrandAssigning(false);
    }
  }, [products]);
  
  // Reset pagination when filters change
  useEffect(() => {
    // Reset to first page when any filter changes
    setCurrentPage(1);
    // Don't clear products here as it causes the "no products" flash
    // The products will be replaced when the query completes
  }, [searchQuery, filterCategoryId, filterSubcategoryId, filterBrandId, filterInStock, filterActive]);
  

// Add a message when no products are found
const NoProductsMessage = () => {
  if (isLoading) return null;
  
  if (filteredProducts.length === 0) {
    return (
      <div className="flex justify-center mt-6">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => {
            setFilterBrandId("all");
            setFilterInStock("all");
            setFilterActive("active");
            setCurrentPage(1);
          }}
        >
          <FilterX className="mr-2 h-4 w-4" />
          Clear Filters
        </Button>
      </div>
    );
  }
  
  return null;
};
  
  // Bulk toggle product status mutation
  const bulkToggleStatus = useMutation({
    mutationFn: async () => {
      if (selectedProducts.length === 0) {
        throw new Error("Please select at least one product");
      }
      
      const { error } = await supabase
        .from("products")
        .update({ is_active: selectedStatus })
        .in("id", selectedProducts);
      
      if (error) throw error;
      
      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['products'] });
      // Refresh product counts after bulk update
      fetchProductCounts();
      toast({
        title: "Product status updated",
        description: `Successfully ${selectedStatus ? 'activated' : 'deactivated'} ${selectedProducts.length} products.`,
      });
      setSelectedProducts([]);
      setSelectAll(false);
      setIsBulkStatusToggling(false);
    },
    onError: (error) => {
      console.error('Error updating product status:', error);
      toast({
        title: "Error",
        description: `Failed to update product status: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      setIsBulkStatusToggling(false);
    },
  });
  
  // Bulk assign category mutation
  const bulkAssignCategory = useMutation({
    mutationFn: async () => {
      if (!selectedCategoryId || selectedProducts.length === 0) {
        throw new Error("Please select a category and at least one product");
      }
      
      // Prepare update data
      const updateData: { category_id?: string | null, subcategory_id?: string | null } = {
        category_id: selectedCategoryId === "none" ? null : selectedCategoryId
      };
      
      // If subcategory is selected, include it in the update
      if (selectedSubcategoryId) {
        updateData.subcategory_id = selectedSubcategoryId === "none" ? null : selectedSubcategoryId;
      } else if (selectedCategoryId === "none") {
        // If category is set to none, also clear subcategory
        updateData.subcategory_id = null;
      }
      
      const { error } = await supabase
        .from("products")
        .update(updateData)
        .in("id", selectedProducts);
      
      if (error) throw error;
      
      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['products'] });
      // Refresh product counts after bulk update
      fetchProductCounts();
      toast({
        title: "Categories updated",
        description: `Successfully assigned ${selectedProducts.length} products to the selected category.`,
      });
      setSelectedProducts([]);
      setSelectAll(false);
      setSelectedCategoryId("");
      setSelectedSubcategoryId("");
      setIsBulkAssigning(false);
    },
    onError: (error) => {
      console.error('Error updating products:', error);
      toast({
        title: "Error",
        description: `Failed to update categories: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      setIsBulkAssigning(false);
    },
  });
  
  // Bulk assign brand mutation
  const bulkAssignBrand = useMutation({
    mutationFn: async () => {
      if (!selectedBrandId || selectedProducts.length === 0) {
        throw new Error("Please select a brand and at least one product");
      }
      
      // Prepare update data with the correct type for products table
      const updateData = {
        brand_id: selectedBrandId === "none" ? null : selectedBrandId
      } as Partial<Product>;
      
      const { error } = await supabase
        .from("products")
        .update(updateData)
        .in("id", selectedProducts);
      
      if (error) throw error;
      
      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['products'] });
      // Refresh product counts after bulk update
      fetchProductCounts();
      toast({
        title: "Brands updated",
        description: `Successfully assigned ${selectedProducts.length} products to the selected brand.`,
      });
      setSelectedProducts([]);
      setSelectAll(false);
      setSelectedBrandId("");
      setIsBulkBrandAssigning(false);
    },
    onError: (error) => {
      console.error('Error updating product brands:', error);
      toast({
        title: "Error",
        description: `Failed to update brands: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      setIsBulkBrandAssigning(false);
    },
  });

  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus search input when it's rendered
  useEffect(() => {
    if (searchInputRef.current) {
      // @ts-ignore - focus() exists on HTMLInputElement but TypeScript doesn't recognize it
      searchInputRef.current.focus();
    }
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (productsError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h3 className="text-xl font-bold text-red-600">Error loading products</h3>
        <p>{productsError instanceof Error ? productsError.message : String(productsError)}</p>
      </div>
    );
  }

  return (
    <div className="container py-6">
      {!isAddingOrEditing ? (
        // Products List View
        <>
          <div className="flex flex-col gap-4 mb-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold">Products</h1>
                <p className="text-gray-500 mb-1">Manage your store products</p>
                <div className="text-xs font-medium">
                  <span className="mr-3">Total: {productCounts.total}</span>
                  <span className="mr-3 text-green-600">Active: {productCounts.active}</span>
                  <span className="text-gray-500">Inactive: {productCounts.inactive}</span>
                </div>
              </div>
              <div className="flex gap-2">
                {selectedProducts.length > 0 && (
                  <div className="flex gap-2">
                  {!isBulkAssigning && !isBulkBrandAssigning && !isBulkStatusToggling && (
                    <>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBulkStatusToggling(true)}
                      >
                        <Tag className="mr-2 h-4 w-4" />
                        {filterActive === "inactive" ? "Activate Products" : "Deactivate Products"}
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBulkAssigning(true)}
                      >
                        <Tag className="mr-2 h-4 w-4" />
                        Assign to Category
                      </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setIsBulkBrandAssigning(true)}
                        >
                          <Tag className="mr-2 h-4 w-4" />
                          Assign to Brand
                        </Button>
                      </>
                    )}
                  </div>
                )}
                <Button onClick={handleAddNewProduct}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Product
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/admin/product-import">
                    <Upload className="mr-2 h-4 w-4" /> Import Products
                  </Link>
                </Button>
              </div>
            </div>
            
            {/* Search and Filter Controls */}
            <div className="flex flex-col md:flex-row gap-4 w-full">
              {/* Search Input */}
              <div className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  type="text"
                  placeholder="Search products by name, description, category..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                  ref={searchInputRef}
                />
                {searchQuery && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => {
                      setSearchQuery('');
                      // Find the input and refocus it after clearing
                      const inputElement = document.querySelector('input[type="text"]');
                      if (inputElement) {
                        inputElement.focus();
                      }
                    }}
                    aria-label="Clear search"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              
              {/* Filter Controls */}
              <div className="flex flex-wrap gap-2 items-center">
                {/* Category Filter */}
                <Select
                  value={filterCategoryId}
                  onValueChange={(value) => {
                    setFilterCategoryId(value);
                    setFilterSubcategoryId("all"); // Reset subcategory when category changes
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories ({productCounts.total})</SelectItem>
                    {categories.filter(c => !c.parent_id).map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Subcategory Filter - Only show if a category is selected */}
                {filterCategoryId !== "all" && (
                  <Select
                    value={filterSubcategoryId}
                    onValueChange={setFilterSubcategoryId}
                    disabled={filterSubcategories.length === 0}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder={filterSubcategories.length === 0 ? "No subcategories" : "Filter by subcategory"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Subcategories</SelectItem>
                      {filterSubcategories.map((subcategory) => (
                        <SelectItem key={subcategory.id} value={subcategory.id}>
                          {subcategory.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                
                {/* Brand Filter */}
                <Select
                  value={filterBrandId}
                  onValueChange={setFilterBrandId}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by brand" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Brands</SelectItem>
                    {brands && brands.map((brand) => (
                      <SelectItem key={brand.id} value={brand.id}>
                        {brand.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Stock Status Filter */}
                <Select
                    value={filterInStock}
                    onValueChange={setFilterInStock}
                  >
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Stock status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Products</SelectItem>
                      <SelectItem value="in-stock">In Stock</SelectItem>
                      <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select
                    value={filterActive}
                    onValueChange={setFilterActive}
                  >
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Product status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Products ({productCounts.total})</SelectItem>
                      <SelectItem value="active">Active Products ({productCounts.active})</SelectItem>
                      <SelectItem value="inactive">Inactive Products ({productCounts.inactive})</SelectItem>
                    </SelectContent>
                  </Select>
                
                {/* Clear Filters Button - Only show if any filter is active */}
                {(filterCategoryId !== "all" || filterSubcategoryId !== "all" || filterBrandId !== "all" || filterInStock !== "all" || filterActive !== "all" || searchQuery) && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      // Batch state updates to minimize re-renders
                      const resetFilters = () => {
                        setFilterCategoryId("all");
                        setFilterSubcategoryId("all");
                        setFilterBrandId("all");
                        setFilterInStock("all");
                        setFilterActive("active");
                        setSearchQuery("");
                      };
                      resetFilters();
                    }}
                  >
                    <FilterX className="mr-2 h-4 w-4" />
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          {/* Bulk Category Assignment UI */}
          {selectedProducts.length > 0 && isBulkAssigning && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Assign Categories</h3>
                      <p className="text-sm text-muted-foreground">
                        Assign {selectedProducts.length} selected products to a category
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsBulkAssigning(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 items-center">
                    <Select
                      value={selectedCategoryId}
                      onValueChange={setSelectedCategoryId}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None (Remove Category)</SelectItem>
                        {categories.filter(c => !c.parent_id).map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    {selectedCategoryId && selectedCategoryId !== "none" && (
                      <Select
                        value={selectedSubcategoryId}
                        onValueChange={setSelectedSubcategoryId}
                        disabled={!selectedCategoryId || selectedCategoryId === "none" || subcategories.length === 0}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder={subcategories.length === 0 ? "No subcategories" : "Select subcategory"} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None (Remove Subcategory)</SelectItem>
                          {subcategories.map((subcategory) => (
                            <SelectItem key={subcategory.id} value={subcategory.id}>
                              {subcategory.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    
                    <Button 
                      onClick={() => bulkAssignCategory.mutate()}
                      disabled={!selectedCategoryId || selectedProducts.length === 0 || bulkAssignCategory.isPending}
                      className="whitespace-nowrap"
                    >
                      {bulkAssignCategory.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Assigning...
                        </>
                      ) : (
                        <>Assign to category</>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bulk Status Toggle UI */}
          {selectedProducts.length > 0 && isBulkStatusToggling && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Update Product Status</h3>
                      <p className="text-sm text-muted-foreground">
                        Set {selectedProducts.length} selected products as active or inactive
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsBulkStatusToggling(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 items-center">
                    <Select
                      value={selectedStatus ? "active" : "inactive"}
                      onValueChange={(value) => setSelectedStatus(value === "active")}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active (Visible)</SelectItem>
                        <SelectItem value="inactive">Inactive (Hidden)</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button 
                      onClick={() => bulkToggleStatus.mutate()}
                      disabled={selectedProducts.length === 0 || bulkToggleStatus.isPending}
                      className="whitespace-nowrap"
                    >
                      {bulkToggleStatus.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>Update status</>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bulk Brand Assignment UI */}
          {selectedProducts.length > 0 && isBulkBrandAssigning && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Assign Brands</h3>
                      <p className="text-sm text-muted-foreground">
                        Assign {selectedProducts.length} selected products to a brand
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setIsBulkBrandAssigning(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 items-center">
                    <Select
                      value={selectedBrandId}
                      onValueChange={setSelectedBrandId}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select brand" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None (Remove Brand)</SelectItem>
                        {brands && brands.map((brand) => (
                          <SelectItem key={brand.id} value={brand.id}>
                            {brand.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Button 
                      onClick={() => bulkAssignBrand.mutate()}
                      disabled={!selectedBrandId || selectedProducts.length === 0 || bulkAssignBrand.isPending}
                      className="whitespace-nowrap"
                    >
                      {bulkAssignBrand.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Assigning...
                        </>
                      ) : (
                        <>Assign to brand</>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Show results count when filtering or searching */}
          {(searchQuery.trim() || filterCategoryId !== "all" || filterSubcategoryId !== "all" || filterBrandId !== "all" || filterInStock !== "all" || filterActive !== "all") && filteredProducts.length > 0 && (
            <div className="mb-4">
              <p className="text-sm text-muted-foreground">
                Found {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
                {searchQuery.trim() && <> matching "{searchQuery}"</>}
                {filterCategoryId !== "all" && <> in {categories.find(c => c.id === filterCategoryId)?.name || 'selected category'}</>}
                {filterSubcategoryId !== "all" && <> / {categories.find(c => c.id === filterSubcategoryId)?.name || 'subcategory'}</>}
                {filterInStock !== "all" && <> that are {filterInStock === "in-stock" ? "in stock" : "out of stock"}</>}
                {filterBrandId !== "all" && <> with brand {brands?.find(b => b.id === filterBrandId)?.name || 'selected brand'}</>}
                {filterActive !== "all" && <> that are {filterActive === "active" ? "active" : "inactive"}</>}
              </p>
            </div>
          )}
          
          {/* Apply filters to loaded products */}
          {isLoading || isChangingFilters ? (
            <div className="flex justify-center my-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading products...</span>
            </div>
          ) : filteredProducts && filteredProducts.length > 0 ? (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectAll}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Image</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Subcategory</TableHead>
                      <TableHead>Brand</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Sale Price</TableHead>
                      <TableHead>In Stock</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((product) => (
                      <TableRow key={product.id} className={product.is_active === false ? "bg-gray-50" : ""}>
                        <TableCell>
                          <Checkbox 
                            checked={selectedProducts.includes(product.id)} 
                            onCheckedChange={() => handleSelectProduct(product.id)}
                            aria-label={`Select ${product.name}`}
                          />
                        </TableCell>
                        <TableCell>
                          {product.image ? (
                            <img
                              src={product.image}
                              alt={product.name}
                              className="h-10 w-10 object-cover rounded-md"
                            />
                          ) : (
                            <div className="h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center text-gray-400">
                              No img
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>
                          {product.category_id ? (
                            categories.find(c => c.id === product.category_id)?.name
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {product.subcategory_id ? (
                            categories.find(c => c.id === product.subcategory_id)?.name
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {product.brand_id ? (
                            brands?.find(b => b.id === product.brand_id)?.name
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </TableCell>
                        <TableCell>£{product.price?.toFixed(2)}</TableCell>
                        <TableCell>
                          {product.sale_price ? (
                            `£${product.sale_price.toFixed(2)}`
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {product.in_stock ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <X className="h-4 w-4 text-red-500" />
                          )}
                        </TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${product.is_active === false ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'}`}>
                            {product.is_active === false ? 'Inactive' : 'Active'}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEdit(product as Product)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(product)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : searchQuery.trim() ? (
            <Card className="w-full">
              <CardHeader>
                <CardTitle>No Results Found</CardTitle>
                <CardDescription>
                  No products match your search for "{searchQuery}". Try a different search term or clear the search.
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  <X className="mr-2 h-4 w-4" /> Clear Search
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Products</CardTitle>
                <CardDescription>
                  {filteredProducts.length > 0 
                    ? `Showing ${filteredProducts.length} products` 
                    : "No products found"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Only show NoProductsMessage when we're not loading or changing filters */}
                {!isLoading && !isChangingFilters && filteredProducts.length === 0 ? (
                  <NoProductsMessage />
                ) : (
                  <>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectAll}
                                onCheckedChange={handleSelectAll}
                                aria-label="Select all"
                              />
                            </TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Brand</TableHead>
                            <TableHead>Stock</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredProducts.map((product) => (
                            <TableRow key={product.id} className={product.is_active === false ? "bg-gray-50" : ""}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedProducts.includes(product.id)}
                                  onCheckedChange={() => handleSelectProduct(product.id)}
                                  aria-label={`Select ${product.name}`}
                                />
                              </TableCell>
                              <TableCell className="font-medium">{product.name}</TableCell>
                              <TableCell>
                                ${parseFloat(product.price).toFixed(2)}
                                {product.sale_price && (
                                  <span className="ml-2 text-sm text-red-500">
                                    ${parseFloat(product.sale_price).toFixed(2)}
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                {categories.find(c => c.id === product.category_id)?.name || "—"}
                                {product.subcategory_id && (
                                  <span className="text-xs text-gray-500 block">
                                    {categories.find(c => c.id === product.subcategory_id)?.name}
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                {brands?.find(b => b.id === product.brand_id)?.name || "—"}
                              </TableCell>
                              <TableCell>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${product.in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                  {product.in_stock ? "In Stock" : "Out of Stock"}
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs text-muted-foreground">{String(product.review_count || 0)} reviews</span>
                                </div>
                                <div className="flex justify-end space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEdit(product as Product)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDelete(product)}
                                  >
                                    <Trash className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    
                    {/* Load More Button */}
                    {hasMore && (
                      <div className="flex justify-center mt-6">
                        <Button 
                          variant="outline" 
                          onClick={handleLoadMore}
                          disabled={isFetching}
                        >
                          {isFetching ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            "Load More Products"
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          )}
          
          {/* Load More button */}
          {hasMore && !isLoading && !isFetching && (
            <div className="flex justify-center mt-6 mb-6">
              <Button 
                onClick={handleLoadMore} 
                variant="outline"
                className="w-full max-w-xs"
                disabled={isFetching}
              >
                {isFetching ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading more...
                  </>
                ) : (
                  <>Load More Products</>
                )}
              </Button>
            </div>
          )}
        </>
      ) : (
        // Inline Product Form View
        <Card className="w-full">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>{selectedProduct ? "Edit Product" : "Add New Product"}</CardTitle>
                <CardDescription>
                  Enter the product details below to {selectedProduct ? "update" : "create"} a product.
                </CardDescription>
              </div>
              <Button variant="outline" onClick={handleCloseForm}>
                Back to Products
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ProductForm
              product={selectedProduct}
              onSuccess={() => {
                queryClient.invalidateQueries({ queryKey: ["products"] });
                handleCloseForm();
              }}
              onCancel={handleCloseForm}
            />
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedProduct?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                const productId = getProductIdToDelete();
                if (productId) {
                  deleteProduct.mutate(productId);
                }
              }}
              disabled={deleteProduct.isPending}
            >
              {deleteProduct.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
