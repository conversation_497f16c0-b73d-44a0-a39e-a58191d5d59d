// Import required modules
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import Papa from 'papaparse';
import dotenv from 'dotenv';

// Configure dotenv
dotenv.config();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Utility function to normalize text for comparison
function normalizeText(text) {
  if (!text) return '';
  return text.toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric
    .replace(/\s+/g, ''); // Remove whitespace
}

// Utility function to calculate similarity between two strings (0-1)
function stringSimilarity(str1, str2) {
  const s1 = normalizeText(str1);
  const s2 = normalizeText(str2);
  
  if (s1 === s2) return 1; // Exact match
  if (s1.includes(s2) || s2.includes(s1)) return 0.9; // One is subset of other
  
  // Calculate Levenshtein distance
  const len1 = s1.length;
  const len2 = s2.length;
  const max = Math.max(len1, len2);
  if (max === 0) return 1; // Both empty strings
  
  const distance = levenshteinDistance(s1, s2);
  return 1 - distance / max;
}

// Levenshtein distance calculation
function levenshteinDistance(str1, str2) {
  const len1 = str1.length;
  const len2 = str2.length;
  
  // Create matrix
  const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
  
  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  // Fill matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }
  
  return matrix[len1][len2];
}

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Log the Supabase connection details (without the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'undefined';
console.log(`Connecting to Supabase at: ${supabaseUrl} with key: ${maskedKey}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase URL or key is missing. Make sure your .env file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const CSV_FILE_PATH = path.resolve(__dirname, '../docs/catalog_products.csv');
const DRY_RUN = process.argv.includes('--dry-run');

// Main function
async function updateProductOptionPricing() {
  console.log('Starting product option pricing update...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);
  
  // Set a similarity threshold for name matching (0.7 = 70% similar)
  const SIMILARITY_THRESHOLD = 0.7;

  try {
    // 1. Read and parse the CSV file
    console.log('Reading CSV file...');
    const csvData = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    
    // Parse CSV
    const parseResult = Papa.parse(csvData, {
      header: true,
      skipEmptyLines: true,
    });
    
    if (parseResult.errors.length > 0) {
      console.error('CSV parsing errors:', parseResult.errors);
      return;
    }
    
    const csvProducts = parseResult.data;
    console.log(`Found ${csvProducts.length} rows in CSV file`);

    // 2. Process the CSV data to extract product variants
    const productMap = new Map(); // Map to store main products
    const variantMap = new Map(); // Map to store variants by parent product ID

    console.log('Analyzing CSV structure...');
    
    // First row to understand structure
    if (csvProducts.length > 0) {
      console.log('CSV columns:', Object.keys(csvProducts[0]).slice(0, 20)); // Show first 20 columns only
    }
    
    // First pass: identify main products and variants
    for (let i = 0; i < csvProducts.length; i++) {
      const row = csvProducts[i];
      const productId = row.handleId || '';
      const fieldType = row.fieldType || '';
      
      if (!productId) continue;
      
      // Check if this is a variant
      const isVariant = fieldType === 'Variant';
      
      // Extract the base product ID
      const baseProductId = productId.split(',')[0];
      
      if (!isVariant) {
        // This is a main product
        const product = {
          id: baseProductId,
          name: row.name || '',
          price: parseFloat(row.price) || 0,
          optionName: row.productOptionName1 || '',
          optionType: row.productOptionType1 || '',
          optionValues: row.productOptionValues1 || ''
        };
        
        // Only add products with options
        if (product.optionName && product.optionValues) {
          console.log(`Found product with options: ${product.name} (${product.id})`);
          console.log(`  Option name: ${product.optionName}`);
          console.log(`  Option values: ${product.optionValues}`);
          
          productMap.set(baseProductId, product);
          
          // Initialize variant array for this product
          if (!variantMap.has(baseProductId)) {
            variantMap.set(baseProductId, []);
          }
        }
      } else {
        // This is a variant
        // Look ahead to find the parent product
        let parentProduct = null;
        
        // If we already have the parent product in our map, use it
        if (productMap.has(baseProductId)) {
          parentProduct = productMap.get(baseProductId);
        } else {
          // Otherwise, look for the parent product in the CSV
          for (let j = 0; j < csvProducts.length; j++) {
            const potentialParent = csvProducts[j];
            if (potentialParent.handleId === baseProductId || 
                potentialParent.handleId.startsWith(baseProductId + ',')) {
              if (potentialParent.fieldType === 'Product') {
                // Found the parent
                parentProduct = {
                  id: baseProductId,
                  name: potentialParent.name || '',
                  price: parseFloat(potentialParent.price) || 0,
                  optionName: potentialParent.productOptionName1 || '',
                  optionType: potentialParent.productOptionType1 || '',
                  optionValues: potentialParent.productOptionValues1 || ''
                };
                
                // Only add products with options
                if (parentProduct.optionName && parentProduct.optionValues) {
                  productMap.set(baseProductId, parentProduct);
                  
                  // Initialize variant array for this product
                  if (!variantMap.has(baseProductId)) {
                    variantMap.set(baseProductId, []);
                  }
                }
                break;
              }
            }
          }
        }
        
        // If we found a parent product with options, add this variant
        if (parentProduct && parentProduct.optionName && parentProduct.optionValues) {
          // Find which option value this variant represents
          // In the CSV, the variant's option value is in the column matching the option name
          const optionValue = row[parentProduct.optionName] || '';
          
          if (optionValue) {
            const variants = variantMap.get(baseProductId) || [];
            variants.push({
              parentId: baseProductId,
              optionValue: optionValue,
              price: parseFloat(row.price) || 0
            });
            variantMap.set(baseProductId, variants);
            
            console.log(`  Found variant for ${parentProduct.name} with option value: ${optionValue}, price: ${row.price}`);
          }
        }
      }
    }

    // No need for the forEach closing bracket as we've replaced it with a for loop

    console.log(`Identified ${productMap.size} main products and variants for ${variantMap.size} products`);

    // 3. Fetch products from the database
    console.log('Fetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, external_id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('option_name1', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts?.length || 0} products in database with options`);
    
    // Debug the first few products
    if (dbProducts && dbProducts.length > 0) {
      console.log('Sample product from database:', dbProducts[0]);
    } else {
      console.log('No products found with options in the database');
    }

    // 4. Process each product with options
    const productsToUpdate = [];
    const productsWithIssues = [];

    // Create a list of CSV products for matching
    const csvProductsList = [];
    for (const [productId, product] of productMap.entries()) {
      const variants = variantMap.get(productId) || [];
      // Only include products with variants
      if (variants.length > 0) {
        csvProductsList.push({
          id: productId,
          product: product,
          variants: variants,
          normalizedName: normalizeText(product.name)
        });
      }
    }

    console.log(`Found ${csvProductsList.length} CSV products with variants for matching`);
    
    // Log some sample products to help with debugging
    if (csvProductsList.length > 0) {
      console.log('Sample CSV products:');
      for (let i = 0; i < Math.min(3, csvProductsList.length); i++) {
        const product = csvProductsList[i];
        console.log(`  ${product.product.name} (${product.id})`);
        console.log(`    Option name: ${product.product.optionName}`);
        console.log(`    Option values: ${product.product.optionValues}`);
        console.log(`    Variants: ${product.variants.length}`);
        if (product.variants.length > 0) {
          console.log(`    Sample variant: ${product.variants[0].optionValue}, price: ${product.variants[0].price}`);
        }
      }
    }

    for (const dbProduct of dbProducts) {
      // Skip products without options
      if (!dbProduct.option_values1 || !dbProduct.option_name1) continue;

      // Get option values as an array
      const optionValues = dbProduct.option_values1.split(';').map(v => v.trim());
      if (optionValues.length === 0) continue;

      // Get the base price
      const basePrice = dbProduct.price || 0;
      if (basePrice <= 0) {
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Invalid base price: ${basePrice}`,
        });
        continue;
      }
      
      // Try to find a matching product in the CSV by name similarity
      const normalizedDbName = normalizeText(dbProduct.name);
      let bestMatch = null;
      let bestSimilarity = 0;
      
      // Find the best matching product based on name similarity
      for (const csvProduct of csvProductsList) {
        const similarity = stringSimilarity(dbProduct.name, csvProduct.product.name);
        
        if (similarity > bestSimilarity && similarity >= SIMILARITY_THRESHOLD) {
          bestMatch = csvProduct;
          bestSimilarity = similarity;
        }
      }
      
      // If no good match found, skip this product
      if (!bestMatch) {
        console.log(`No CSV match found for DB product: ${dbProduct.name}`);
        continue;
      }
      
      console.log(`Found match for DB product: '${dbProduct.name}' -> CSV '${bestMatch.product.name}' (similarity: ${bestSimilarity.toFixed(2)})`);
      
      // Get the variants for this product
      const variants = bestMatch.variants;

      try {
        // Calculate price adjustments for each option value
        const priceAdjustments = [];
        let allValuesFound = true;

        // Log the option values we're looking for
        console.log(`Processing DB product: ${dbProduct.name}`);
        console.log(`  Option values to find: ${optionValues.join(', ')}`);
        console.log(`  Available variants: ${variants.length}`);
        
        // Debug: Show available variants
        if (variants.length > 0) {
          console.log(`  Variant option values: ${variants.map(v => v.optionValue).join(', ')}`);
        }

        for (const optionValue of optionValues) {
          // Find the variant with this option value - try exact and normalized matching
          const normalizedOptionValue = optionValue.toLowerCase().replace(/[^a-z0-9]/g, '');
          
          // Try to find a variant with this option value
          let variant = variants.find(v => v.optionValue === optionValue);
          
          // If not found, try case-insensitive match
          if (!variant) {
            variant = variants.find(v => v.optionValue.toLowerCase() === optionValue.toLowerCase());
          }
          
          // If still not found, try normalized match
          if (!variant) {
            const normalizedVariants = variants.map(v => ({
              ...v,
              normalizedValue: v.optionValue.toLowerCase().replace(/[^a-z0-9]/g, '')
            }));
            
            const matchingVariant = normalizedVariants.find(v => 
              v.normalizedValue === normalizedOptionValue ||
              v.normalizedValue.includes(normalizedOptionValue) ||
              normalizedOptionValue.includes(v.normalizedValue)
            );
            
            if (matchingVariant) {
              variant = variants.find(v => v.optionValue === matchingVariant.optionValue);
              console.log(`  Found fuzzy match: '${optionValue}' -> '${matchingVariant.optionValue}'`);
            }
          }
          
          if (variant) {
            // Calculate the price adjustment (variant price - base price)
            const adjustment = variant.price - basePrice;
            console.log(`  Option value '${optionValue}': variant price ${variant.price}, base price ${basePrice}, adjustment ${adjustment}`);
            priceAdjustments.push(adjustment);
          } else {
            // If we can't find a variant for this option value, use 0 as the adjustment
            console.log(`  No variant found for option value '${optionValue}'`);
            priceAdjustments.push(0);
            allValuesFound = false;
          }
        }

        // Format price adjustments as semicolon-separated string
        const priceAdjustmentStr = priceAdjustments.join(';');

        // Add to update list
        productsToUpdate.push({
          id: dbProduct.id,
          option_price_adjustment1: priceAdjustmentStr,
        });

        console.log(`Product: ${dbProduct.name}`);
        console.log(`  Option values: ${optionValues.join(', ')}`);
        console.log(`  Price adjustments: ${priceAdjustmentStr}`);
        
        if (!allValuesFound) {
          productsWithIssues.push({
            id: dbProduct.id,
            name: dbProduct.name,
            issue: 'Some option values could not be matched to variants',
          });
        }
      } catch (err) {
        console.error(`Error processing product ${dbProduct.name}:`, err);
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Error: ${err.message}`,
        });
      }
    }

    // 5. Update products in the database (if not dry run)
    if (productsToUpdate.length > 0) {
      console.log(`\nReady to update ${productsToUpdate.length} products with price adjustments`);
      
      if (!DRY_RUN) {
        console.log('Updating products in database...');
        
        // Update products in batches to avoid hitting API limits
        const BATCH_SIZE = 50;
        for (let i = 0; i < productsToUpdate.length; i += BATCH_SIZE) {
          const batch = productsToUpdate.slice(i, i + BATCH_SIZE);
          
          // Update each product individually to avoid issues
          for (const product of batch) {
            const { error } = await supabase
              .from('products')
              .update({ option_price_adjustment1: product.option_price_adjustment1 })
              .eq('id', product.id);
              
            if (error) {
              console.error(`Error updating product ${product.id}:`, error);
              productsWithIssues.push({
                id: product.id,
                name: 'Unknown',
                issue: `Update error: ${error.message}`,
              });
            }
          }
          
          console.log(`Updated batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(productsToUpdate.length / BATCH_SIZE)}`);
        }
        
        console.log('Update completed successfully!');
      } else {
        console.log('DRY RUN: No changes were made to the database');
      }
    } else {
      console.log('No products to update');
    }

    // 6. Report issues
    if (productsWithIssues.length > 0) {
      console.log(`\n${productsWithIssues.length} products had issues:`);
      productsWithIssues.forEach(p => {
        console.log(`- ${p.name} (${p.id}): ${p.issue}`);
      });
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
updateProductOptionPricing().catch(console.error);
