-- SQL script to import transformed products and variants
-- Run this in the Supabase SQL Editor

-- First, clear existing products if needed (UNCOMMENT IF NEEDED)
-- DELETE FROM product_variants;
-- DELETE FROM products;

-- Import products from the transformed_products.csv file
-- Note: You'll need to upload the CSV file to Supabase storage first
-- Then use the COPY command to import it

-- Example:
-- COPY products(
--   id, name, slug, description, price, sale_price, cost_price, 
--   image, additional_images, category_id, subcategory_id, brand_id, 
--   sku, stock_quantity, weight, dimensions, in_stock, is_active, 
--   is_featured, is_new, is_best_seller, rating, review_count, 
--   option_definitions, created_at, updated_at
-- )
-- FROM 'transformed_products.csv'
-- WITH (FORMAT csv, HEADER true);

-- Import variants from the transformed_variants.csv file
-- COPY product_variants(
--   id, product_id, variant_name, sku, price, sale_price, 
--   stock_quantity, in_stock, image, option_combination, 
--   is_active, created_at, updated_at
-- )
-- FROM 'transformed_variants.csv'
-- WITH (FORMAT csv, HEADER true);

-- Alternatively, you can manually insert products and variants
-- This is useful for testing with a small number of products

-- Example manual insert:
INSERT INTO products (
  id, name, slug, description, price, image, is_active, in_stock,
  option_definitions, created_at, updated_at
) VALUES (
  '11111111-1111-1111-1111-111111111111',
  'Test Product',
  'test-product',
  'This is a test product description',
  29.99,
  'product-images/test-product.webp',
  true,
  true,
  '{"Size": {"name": "Size", "display_type": "dropdown", "values": ["Small", "Medium", "Large"]}}',
  NOW(),
  NOW()
);

-- Example variant insert:
INSERT INTO product_variants (
  id, product_id, variant_name, sku, price, stock_quantity, in_stock,
  option_combination, created_at, updated_at, is_active
) VALUES (
  '22222222-2222-2222-2222-222222222222',
  '11111111-1111-1111-1111-111111111111',
  'Small',
  'TEST-SML',
  29.99,
  100,
  true,
  '{"Size": "Small"}',
  NOW(),
  NOW(),
  true
);
