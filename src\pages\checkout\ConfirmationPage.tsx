import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, CheckCircle, AlertCircle, ShoppingBag, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { getPaymentService } from '@/services/paymentService';
import { useCart } from '@/hooks/useCart';

const ConfirmationPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [paymentProvider, setPaymentProvider] = useState<'stripe' | 'paypal' | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { clearCart } = useCart();
  const paymentService = getPaymentService();

  useEffect(() => {
    const processPayment = async () => {
      setIsLoading(true);
      try {
        // Check for Stripe payment confirmation
        const urlParams = new URLSearchParams(location.search);
        const paymentIntent = urlParams.get('payment_intent');
        const paymentStatus = urlParams.get('payment_status');
        const stripeRedirect = paymentIntent && paymentStatus;

        // Check for PayPal payment confirmation
        const paypalSessionId = localStorage.getItem('paypal_session_id');
        const paypalStatus = urlParams.get('payment_status');
        const paypalTransactionId = urlParams.get('tx');
        const paypalRedirect = paypalSessionId && paypalStatus === 'Completed' && paypalTransactionId;

        if (stripeRedirect) {
          // Handle Stripe payment confirmation
          setPaymentProvider('stripe');
          const orderId = await paymentService.processStripePaymentConfirmation(paymentIntent);
          if (orderId) {
            const orderData = await fetchOrderDetails(orderId);
            setOrderDetails(orderData);
            clearCart();
            toast({
              title: 'Payment Successful',
              description: `Your order #${orderId} has been confirmed.`,
            });
          } else {
            throw new Error('Failed to process payment confirmation');
          }
        } else if (paypalRedirect) {
          // Handle PayPal payment confirmation
          setPaymentProvider('paypal');
          const isVerified = await paymentService.verifyPayPalPayment(
            paypalSessionId,
            paypalTransactionId
          );
          
          if (isVerified) {
            // Process the payment and create order
            const orderId = await paymentService.processSuccessfulPayment(paypalSessionId);
            localStorage.removeItem('paypal_session_id');
            
            const orderData = await fetchOrderDetails(orderId);
            setOrderDetails(orderData);
            clearCart();
            
            toast({
              title: 'Payment Successful',
              description: `Your order #${orderId} has been confirmed.`,
            });
          } else {
            throw new Error('Payment verification failed');
          }
        } else {
          // No valid payment confirmation found
          throw new Error('No valid payment confirmation found');
        }
      } catch (err) {
        console.error('Payment confirmation error:', err);
        setError(err instanceof Error ? err.message : 'An error occurred processing your payment');
        toast({
          title: 'Payment Error',
          description: 'There was a problem confirming your payment.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    processPayment();
  }, [location.search]);

  const fetchOrderDetails = async (orderId: string) => {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('*, order_items(*)')
        .eq('id', orderId)
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error fetching order details:', err);
      return null;
    }
  };

  const handleContinueShopping = () => {
    navigate('/shop');
  };

  const handleViewOrder = () => {
    if (orderDetails?.id) {
      navigate(`/account/orders/${orderDetails.id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Processing Your Payment</h2>
        <p className="text-muted-foreground text-center max-w-md">
          Please wait while we confirm your payment and prepare your order...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <Alert variant="destructive" className="max-w-2xl w-full mb-6">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle>Payment Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        
        <div className="flex flex-col sm:flex-row gap-4">
          <Button onClick={() => navigate('/checkout')} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" /> Return to Checkout
          </Button>
          <Button onClick={handleContinueShopping}>
            <ShoppingBag className="mr-2 h-4 w-4" /> Continue Shopping
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-12">
      <div className="flex flex-col items-center text-center mb-8">
        <div className="rounded-full bg-green-100 p-3 mb-4">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold mb-2">Order Confirmed!</h1>
        <p className="text-muted-foreground max-w-md">
          Thank you for your purchase. Your order has been received and is being processed.
        </p>
      </div>

      {orderDetails && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Order #{orderDetails.id.substring(0, 8)}</CardTitle>
            <CardDescription>
              Placed on {new Date(orderDetails.created_at).toLocaleDateString()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Payment Method</h3>
                <p>{paymentProvider === 'stripe' ? 'Credit Card (Stripe)' : 'PayPal'}</p>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Order Summary</h3>
                <div className="border rounded-md divide-y">
                  {orderDetails.order_items?.map((item: any) => (
                    <div key={item.id} className="flex justify-between p-3">
                      <div>
                        <p className="font-medium">{item.product_name}</p>
                        <p className="text-sm text-muted-foreground">Qty: {item.quantity}</p>
                      </div>
                      <p className="font-medium">£{(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex flex-col gap-2 pt-4">
                <div className="flex justify-between">
                  <p>Subtotal</p>
                  <p>£{orderDetails.subtotal.toFixed(2)}</p>
                </div>
                <div className="flex justify-between">
                  <p>Shipping</p>
                  <p>£{orderDetails.shipping_cost.toFixed(2)}</p>
                </div>
                {orderDetails.tax_amount > 0 && (
                  <div className="flex justify-between">
                    <p>Tax</p>
                    <p>£{orderDetails.tax_amount.toFixed(2)}</p>
                  </div>
                )}
                <div className="flex justify-between font-bold pt-2 border-t mt-2">
                  <p>Total</p>
                  <p>£{orderDetails.total_amount.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-4 sm:justify-end">
            <Button onClick={handleViewOrder} variant="outline">
              View Order Details
            </Button>
            <Button onClick={handleContinueShopping}>
              Continue Shopping
            </Button>
          </CardFooter>
        </Card>
      )}

      <div className="text-center">
        <p className="mb-4">Have questions about your order?</p>
        <Button onClick={() => navigate('/contact')} variant="outline">
          Contact Support
        </Button>
      </div>
    </div>
  );
};

export default ConfirmationPage;