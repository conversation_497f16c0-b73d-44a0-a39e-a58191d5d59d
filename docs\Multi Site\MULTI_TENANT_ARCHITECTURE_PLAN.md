# 🏢 Multi-Tenant SaaS Architecture Implementation Plan

## Executive Summary

This document outlines the comprehensive plan for transforming the BitsNBongs cannabis/CBD e-commerce platform into a multi-tenant SaaS solution. The implementation follows a phased approach with zero-downtime deployment and robust data isolation.

## Current State Analysis

### Database Overview
- **Project**: BitsNBongs (Supabase Project ID: pkjyjuaiokrhgbutjhla)
- **Status**: ACTIVE_HEALTHY
- **Region**: eu-west-2
- **Database Version**: PostgreSQL **********
- **Total Tables**: 39
- **Current Records**: 2,207 products with RLS already enabled

### Existing Table Structure

#### Core E-commerce Tables (Priority 1 - Tenant Isolation Required)
```
✅ products (2,207 records, RLS enabled)
✅ categories
✅ brands
✅ orders
✅ order_items
✅ blogs
✅ blog_categories
✅ discount_codes
✅ settings
```

#### User-Specific Tables (Priority 2 - User-Tenant Association)
```
✅ profiles
✅ addresses
✅ cart_items
✅ wishlists
✅ wishlist_items
✅ saved_items
✅ newsletter_subscribers
```

#### Shared/Global Tables (No tenant_id needed)
```
✅ shipping_methods
✅ shipping_zones
✅ payment_sessions
✅ Filter option tables (flowering_time_options, thc_content_options, etc.)
```

## Phase 1: Foundation Setup

### 1.1 Tenant Management System

#### Create Tenants Table
```sql
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  domain TEXT UNIQUE,
  subdomain TEXT UNIQUE,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  plan_type TEXT DEFAULT 'basic' CHECK (plan_type IN ('basic', 'premium', 'enterprise')),
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Tenants can view own data" ON tenants
  FOR SELECT USING (id = current_setting('app.current_tenant_id')::UUID);
```

#### Create Tenant Users Association
```sql
CREATE TABLE tenant_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tenant_id, user_id)
);

ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;
```

### 1.2 Tenant Context Functions

```sql
-- Function to set tenant context
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID)
RETURNS VOID AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::TEXT, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current tenant
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
  RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Phase 2: Schema Migration

### 2.1 Add tenant_id Columns

#### Priority 1 Tables Migration Script
```sql
-- Products table
ALTER TABLE products ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_products_tenant_id ON products(tenant_id);

-- Categories table
ALTER TABLE categories ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_categories_tenant_id ON categories(tenant_id);

-- Brands table
ALTER TABLE brands ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_brands_tenant_id ON brands(tenant_id);

-- Orders table
ALTER TABLE orders ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_orders_tenant_id ON orders(tenant_id);

-- Order items table
ALTER TABLE order_items ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_order_items_tenant_id ON order_items(tenant_id);

-- Blogs table
ALTER TABLE blogs ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_blogs_tenant_id ON blogs(tenant_id);

-- Blog categories table
ALTER TABLE blog_categories ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_blog_categories_tenant_id ON blog_categories(tenant_id);

-- Discount codes table
ALTER TABLE discount_codes ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_discount_codes_tenant_id ON discount_codes(tenant_id);

-- Settings table
ALTER TABLE settings ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_settings_tenant_id ON settings(tenant_id);
```

#### Priority 2 Tables Migration Script
```sql
-- Profiles table (associate with tenant through tenant_users)
ALTER TABLE profiles ADD COLUMN current_tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_profiles_current_tenant_id ON profiles(current_tenant_id);

-- Cart items table
ALTER TABLE cart_items ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_cart_items_tenant_id ON cart_items(tenant_id);

-- Wishlists table
ALTER TABLE wishlists ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_wishlists_tenant_id ON wishlists(tenant_id);

-- Wishlist items table
ALTER TABLE wishlist_items ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_wishlist_items_tenant_id ON wishlist_items(tenant_id);

-- Saved items table
ALTER TABLE saved_items ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_saved_items_tenant_id ON saved_items(tenant_id);

-- Newsletter subscribers table
ALTER TABLE newsletter_subscribers ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_newsletter_subscribers_tenant_id ON newsletter_subscribers(tenant_id);
```

### 2.2 Data Migration Strategy

#### Create Default Tenant for Existing Data
```sql
-- Insert default tenant for existing data
INSERT INTO tenants (id, name, slug, domain, status, plan_type)
VALUES (
  gen_random_uuid(),
  'BitsNBongs Original',
  'bitsnbongs-original',
  'bitsnbongs.com',
  'active',
  'enterprise'
);

-- Store the default tenant ID
SET @default_tenant_id = (SELECT id FROM tenants WHERE slug = 'bitsnbongs-original');

-- Update existing data with default tenant
UPDATE products SET tenant_id = @default_tenant_id WHERE tenant_id IS NULL;
UPDATE categories SET tenant_id = @default_tenant_id WHERE tenant_id IS NULL;
UPDATE brands SET tenant_id = @default_tenant_id WHERE tenant_id IS NULL;
-- ... repeat for all tables
```

## Phase 3: Row Level Security Implementation

### 3.1 RLS Policies for Core Tables

```sql
-- Products RLS policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON products;
CREATE POLICY "Tenant isolation for products" ON products
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Categories RLS policies
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for categories" ON categories
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Brands RLS policies
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for brands" ON brands
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Orders RLS policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for orders" ON orders
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Order items RLS policies
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for order_items" ON order_items
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Blogs RLS policies
ALTER TABLE blogs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for blogs" ON blogs
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Blog categories RLS policies
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for blog_categories" ON blog_categories
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Discount codes RLS policies
ALTER TABLE discount_codes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for discount_codes" ON discount_codes
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Settings RLS policies
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for settings" ON settings
  FOR ALL USING (tenant_id = get_current_tenant_id());
```

### 3.2 User-Specific Table Policies

```sql
-- Cart items policies
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for cart_items" ON cart_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Wishlists policies
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for wishlists" ON wishlists
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Wishlist items policies
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for wishlist_items" ON wishlist_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    EXISTS (
      SELECT 1 FROM wishlists w 
      WHERE w.id = wishlist_items.wishlist_id 
      AND w.user_id = auth.uid()
    )
  );

-- Saved items policies
ALTER TABLE saved_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for saved_items" ON saved_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );
```

## Phase 4: Application Layer Updates

### 4.1 Middleware for Tenant Context

```typescript
// middleware/tenant.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextRequest, NextResponse } from 'next/server'

export async function tenantMiddleware(request: NextRequest) {
  const response = NextResponse.next()
  const supabase = createMiddlewareClient({ req: request, res: response })
  
  // Extract tenant from subdomain or domain
  const host = request.headers.get('host')
  const tenantSlug = extractTenantFromHost(host)
  
  if (tenantSlug) {
    // Set tenant context in database
    await supabase.rpc('set_tenant_context', { tenant_uuid: tenantSlug })
    
    // Store tenant in request headers for app use
    response.headers.set('x-tenant-id', tenantSlug)
  }
  
  return response
}

function extractTenantFromHost(host: string | null): string | null {
  if (!host) return null
  
  // Handle subdomain: tenant.bitsnbongs.com
  if (host.includes('.bitsnbongs.com')) {
    return host.split('.')[0]
  }
  
  // Handle custom domain: tenant.com
  // Query database for custom domain mapping
  return null
}
```

### 4.2 Database Query Updates

```typescript
// lib/supabase-tenant.ts
import { createClient } from '@supabase/supabase-js'

export class TenantSupabaseClient {
  private client: any
  private tenantId: string
  
  constructor(tenantId: string) {
    this.client = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_ANON_KEY!)
    this.tenantId = tenantId
  }
  
  async query(table: string) {
    // Set tenant context before any query
    await this.client.rpc('set_tenant_context', { tenant_uuid: this.tenantId })
    return this.client.from(table)
  }
  
  async getProducts() {
    const query = await this.query('products')
    return query.select('*')
  }
  
  async getCategories() {
    const query = await this.query('categories')
    return query.select('*')
  }
}
```

## Phase 5: OAuth Implementation

### 5.1 Tenant-Aware Authentication

```typescript
// hooks/useAuth.ts - Enhanced with tenant awareness
import { useTenant } from './useTenant';
import { supabase } from '@/lib/supabase';

export function useAuth() {
  const { tenant } = useTenant();
  
  const signInWithOAuth = async (provider) => {
    return supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback?tenant=${tenant.id}`,
        queryParams: { tenant_id: tenant.id }
      }
    });
  };
  
  const signInWithEmail = async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email, password
    });
    
    if (data.user && !error) {
      // Check if user has access to current tenant
      const { data: tenantAccess } = await supabase
        .from('tenant_users')
        .select('*')
        .match({ tenant_id: tenant.id, user_id: data.user.id })
        .single();
      
      if (!tenantAccess) {
        return { error: { message: 'You do not have access to this organization' } };
      }
    }
    
    return { data, error };
  };
  
  return {
    signInWithEmail,
    signInWithOAuth,
    // ...other auth methods
  };
}
```

### 5.2 JWT Enhancement for Tenant Context

```typescript
// supabase/functions/jwt-custom-claims.ts
import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0';

const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

serve(async (req) => {
  const { event, type } = await req.json();
  
  if (type === 'jwt.create') {
    const { user, token } = event;
    
    // Get user's tenant associations
    const { data: tenantUsers, error } = await supabaseAdmin
      .from('tenant_users')
      .select('tenant_id, role')
      .eq('user_id', user.id);
    
    if (error) {
      console.error('Error fetching tenant users:', error);
      return new Response(JSON.stringify({ token }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    // Get current tenant from profile
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('current_tenant_id')
      .eq('id', user.id)
      .single();
    
    // Add tenant info to JWT
    token.tenants = tenantUsers.map(tu => ({
      id: tu.tenant_id,
      role: tu.role
    }));
    
    token.current_tenant_id = profile?.current_tenant_id;
    
    return new Response(JSON.stringify({ token }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }
  
  return new Response(JSON.stringify({}), {
    headers: { 'Content-Type': 'application/json' },
  });
});
```

### 5.3 Tenant Context from JWT

```sql
-- Function to automatically set tenant context from JWT
CREATE OR REPLACE FUNCTION apply_tenant_context_from_jwt()
RETURNS TRIGGER AS $$
DECLARE
  tenant_id UUID;
BEGIN
  -- Try to extract tenant_id from JWT claims
  BEGIN
    tenant_id := nullif(current_setting('request.jwt.claims', true)::json->>'current_tenant_id', '')::UUID;
  EXCEPTION
    WHEN OTHERS THEN
      tenant_id := NULL;
  END;
  
  -- Set tenant context if available
  IF tenant_id IS NOT NULL THEN
    PERFORM set_tenant_context(tenant_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply the trigger to relevant tables
CREATE TRIGGER set_tenant_context_on_products
  BEFORE SELECT OR INSERT OR UPDATE OR DELETE ON products
  FOR EACH STATEMENT
  EXECUTE FUNCTION apply_tenant_context_from_jwt();
```

### 5.4 Tenant Switching UI

```jsx
// components/TenantSwitcher.tsx
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { useTenant } from '@/hooks/useTenant';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';

export function TenantSwitcher() {
  const { user } = useAuth();
  const { tenant, setTenant } = useTenant();
  const [availableTenants, setAvailableTenants] = useState([]);
  
  useEffect(() => {
    if (!user) return;
    
    async function fetchUserTenants() {
      const { data, error } = await supabase
        .from('tenant_users')
        .select(`
          tenant_id,
          role,
          tenants:tenant_id(id, name, logo_url, primary_color, subdomain)
        `)
        .eq('user_id', user.id);
      
      if (data && !error) {
        setAvailableTenants(data.map(item => ({
          id: item.tenants.id,
          name: item.tenants.name,
          logo: item.tenants.logo_url,
          color: item.tenants.primary_color,
          subdomain: item.tenants.subdomain,
          role: item.role
        })));
      }
    }
    
    fetchUserTenants();
  }, [user]);
  
  const handleSwitchTenant = async (tenantId) => {
    // Update profile with new tenant
    await supabase
      .from('profiles')
      .update({ current_tenant_id: tenantId })
      .eq('id', user.id);
    
    // Get tenant details
    const newTenant = availableTenants.find(t => t.id === tenantId);
    
    // If we have a subdomain and not on it, redirect
    const currentHost = window.location.host;
    const baseDomain = process.env.NEXT_PUBLIC_BASE_DOMAIN;
    
    if (newTenant.subdomain && 
        !currentHost.startsWith(`${newTenant.subdomain}.`)) {
      // Redirect to subdomain
      window.location.href = `https://${newTenant.subdomain}.${baseDomain}`;
      return;
    }
    
    // Otherwise just reload to refresh JWT
    window.location.reload();
  };
  
  if (!tenant || availableTenants.length === 0) {
    return null;
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center gap-2 px-3 py-2">
        {tenant.logo ? (
          <img 
            src={tenant.logo} 
            alt={tenant.name} 
            className="w-6 h-6 rounded-full"
          />
        ) : (
          <div 
            className="w-6 h-6 rounded-full flex items-center justify-center"
            style={{ backgroundColor: tenant.color || '#4A7C59' }}
          >
            {tenant.name.charAt(0)}
          </div>
        )}
        <span>{tenant.name}</span>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        {availableTenants.map(t => (
          <DropdownMenuItem
            key={t.id}
            onClick={() => handleSwitchTenant(t.id)}
            className="flex items-center gap-2 cursor-pointer"
          >
            {t.logo ? (
              <img 
                src={t.logo} 
                alt={t.name} 
                className="w-5 h-5 rounded-full"
              />
            ) : (
              <div 
                className="w-5 h-5 rounded-full flex items-center justify-center text-xs text-white"
                style={{ backgroundColor: t.color || '#4A7C59' }}
              >
                {t.name.charAt(0)}
              </div>
            )}
            <div className="flex flex-col">
              <span>{t.name}</span>
              <span className="text-xs text-gray-500">{t.role}</span>
            </div>
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="cursor-pointer"
          onClick={() => window.location.href = '/dashboard/tenants/create'}
        >
          Create new store
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

### 5.5 Auth Callback Handler

```tsx
// pages/auth/callback.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

export default function AuthCallback() {
  const router = useRouter();
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const { tenant } = router.query;
    
    async function handleCallback() {
      try {
        // Get session
        const { data, error } = await supabase.auth.getSession();
        
        if (error) throw error;
        if (!data.session) throw new Error('No session found');
        
        const user = data.session.user;
        
        // Check if user is already associated with tenant
        const { data: existingAssociation } = await supabase
          .from('tenant_users')
          .select('*')
          .match({ tenant_id: tenant, user_id: user.id })
          .single();
        
        // If not associated, create association
        if (!existingAssociation) {
          await supabase.from('tenant_users').insert({
            tenant_id: tenant,
            user_id: user.id,
            role: 'member'
          });
        }
        
        // Update current tenant in profile
        await supabase
          .from('profiles')
          .update({ current_tenant_id: tenant })
          .eq('id', user.id);
          
        // Redirect to dashboard or home
        router.push('/dashboard');
      } catch (err) {
        console.error('Auth callback error:', err);
        setError(err.message);
      }
    }
    
    if (tenant) {
      handleCallback();
    }
  }, [router.query]);
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="bg-red-50 p-4 rounded-lg">
          <h2 className="text-red-800 font-medium">Authentication Error</h2>
          <p className="text-red-600">{error}</p>
          <button 
            onClick={() => router.push('/auth')}
            className="mt-4 px-4 py-2 bg-red-100 text-red-800 rounded"
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      <span className="ml-3">Completing authentication...</span>
    </div>
  );
}
```

## Phase 6: Deployment Strategy

### 6.1 Zero-Downtime Migration Plan

1. **Pre-Migration Phase**
   - Create tenant management tables
   - Add tenant_id columns (nullable initially)
   - Create indexes
   - Deploy application code with backward compatibility

2. **Migration Phase**
   - Populate tenant_id for existing data
   - Enable RLS policies
   - Update application to use tenant context

3. **Post-Migration Phase**
   - Make tenant_id NOT NULL
   - Remove old non-tenant-aware code
   - Performance monitoring and optimization

### 6.2 Rollback Strategy

```sql
-- Emergency rollback script
DO $$
BEGIN
  -- Disable RLS on all tables
  ALTER TABLE products DISABLE ROW LEVEL SECURITY;
  ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
  ALTER TABLE brands DISABLE ROW LEVEL SECURITY;
  -- ... repeat for all tables
  
  -- Drop tenant_id columns if needed (DANGEROUS - data loss)
  -- ALTER TABLE products DROP COLUMN tenant_id;
  
  RAISE NOTICE 'Rollback completed - RLS disabled';
END $$;
```

## Phase 7: Monitoring & Maintenance

### 7.1 Monitoring Queries

```sql
-- Monitor tenant data distribution
SELECT 
  t.name as tenant_name,
  COUNT(p.id) as product_count,
  COUNT(o.id) as order_count,
  COUNT(b.id) as blog_count
FROM tenants t
LEFT JOIN products p ON t.id = p.tenant_id
LEFT JOIN orders o ON t.id = o.tenant_id
LEFT JOIN blogs b ON t.id = b.tenant_id
GROUP BY t.id, t.name
ORDER BY product_count DESC;

-- Monitor RLS policy performance
SELECT 
  schemaname,
  tablename,
  n_tup_ins,
  n_tup_upd,
  n_tup_del,
  seq_scan,
  seq_tup_read,
  idx_scan,
  idx_tup_fetch
FROM pg_stat_user_tables
WHERE tablename IN ('products', 'categories', 'brands', 'orders');
```

### 7.2 Maintenance Procedures

```sql
-- Regular maintenance function
CREATE OR REPLACE FUNCTION maintain_tenant_data()
RETURNS VOID AS $$
BEGIN
  -- Update table statistics
  ANALYZE products;
  ANALYZE categories;
  ANALYZE brands;
  ANALYZE orders;
  
  -- Vacuum tables if needed
  VACUUM (ANALYZE) products;
  
  -- Check for orphaned records
  DELETE FROM products WHERE tenant_id NOT IN (SELECT id FROM tenants);
  
  RAISE NOTICE 'Tenant data maintenance completed';
END;
$$ LANGUAGE plpgsql;

-- Schedule maintenance (run weekly)
-- SELECT cron.schedule('tenant-maintenance', '0 2 * * 0', 'SELECT maintain_tenant_data();');
```

## Implementation Timeline

### Week 1-2: Foundation
- [ ] Create tenant management system
- [ ] Implement tenant context functions
- [ ] Set up development environment
- [ ] Create migration scripts

### Week 3-4: Schema Migration
- [ ] Add tenant_id columns to all tables
- [ ] Migrate existing data to default tenant
- [ ] Create and test RLS policies
- [ ] Performance testing

### Week 5-6: Application Updates
- [ ] Implement tenant middleware
- [ ] Update all database queries
- [ ] Create tenant-aware API endpoints
- [ ] Update frontend components

### Week 7-8: Testing & Security
- [ ] Comprehensive security testing
- [ ] Performance optimization
- [ ] Load testing with multiple tenants
- [ ] Documentation and training

### Week 9-10: Deployment
- [ ] Staging environment deployment
- [ ] Production migration
- [ ] Monitoring setup
- [ ] Post-deployment validation

## Risk Assessment & Mitigation

### High Risk Items
1. **Data Loss During Migration**
   - Mitigation: Full database backup before migration
   - Rollback plan with tested procedures

2. **Performance Degradation**
   - Mitigation: Comprehensive indexing strategy
   - Query optimization and monitoring

3. **Security Vulnerabilities**
   - Mitigation: Thorough RLS testing
   - Security audit by external team

### Medium Risk Items
1. **Application Downtime**
   - Mitigation: Blue-green deployment strategy
   - Feature flags for gradual rollout

2. **Complex Query Performance**
   - Mitigation: Query analysis and optimization
   - Caching layer implementation

## Success Metrics

### Technical Metrics
- [ ] 100% data isolation between tenants
- [ ] < 10% performance degradation post-migration
- [ ] Zero data loss during migration
- [ ] < 1 hour total downtime

### Business Metrics
- [ ] Ability to onboard new tenants in < 24 hours
- [ ] Support for 100+ concurrent tenants
- [ ] 99.9% uptime SLA compliance
- [ ] Reduced operational costs per tenant

## Conclusion

This comprehensive plan provides a structured approach to implementing multi-tenancy in the BitsNBongs platform. The phased approach ensures minimal risk while maintaining system reliability and performance. Regular monitoring and maintenance procedures will ensure long-term success of the multi-tenant architecture.

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Next Review**: Post Phase 1 Completion