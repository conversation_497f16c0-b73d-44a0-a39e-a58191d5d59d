'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Address } from '@/hooks/useAddresses';
import { ShippingMethod } from '@/components/checkout/ShippingMethodSelector';
import { PaymentMethod } from '@/components/checkout/PaymentMethodSelector';
import { formatPrice } from '@/lib/utils';
import { ArrowLeft, CreditCard, Truck, MapPin, Loader2 } from 'lucide-react';

interface ReviewStepProps {
  address: Address | null;
  shippingMethod: ShippingMethod | undefined;
  paymentMethod: PaymentMethod | undefined;
  onPlaceOrder: () => Promise<void>;
  onBack: () => void;
}

export function ReviewStep({
  address,
  shippingMethod,
  paymentMethod,
  onPlaceOrder,
  onBack,
}: ReviewStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePlaceOrder = async () => {
    setIsSubmitting(true);
    try {
      await onPlaceOrder();
    } catch (error) {
      console.error('Error placing order:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!address || !shippingMethod || !paymentMethod) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p className="text-red-500">
            Missing required information. Please go back and complete previous steps.
          </p>
          <Button variant="outline" onClick={onBack} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Payment
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Review Your Order</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Shipping Address */}
          <div>
            <div className="flex items-center mb-2">
              <MapPin className="h-5 w-5 mr-2 text-primary" />
              <h3 className="font-medium">Shipping Address</h3>
            </div>
            <div className="pl-7 text-sm">
              <p className="font-medium">{address.full_name}</p>
              <p>{address.street}</p>
              <p>
                {address.city}
                {address.state ? `, ${address.state}` : ''}
              </p>
              <p>{address.postal_code}</p>
              <p>{address.country}</p>
              <p className="mt-1">{address.phone}</p>
            </div>
          </div>

          <Separator />

          {/* Shipping Method */}
          <div>
            <div className="flex items-center mb-2">
              <Truck className="h-5 w-5 mr-2 text-primary" />
              <h3 className="font-medium">Shipping Method</h3>
            </div>
            <div className="pl-7 text-sm">
              <div className="flex justify-between">
                <p>{shippingMethod.name}</p>
                <p>{formatPrice(shippingMethod.price)}</p>
              </div>
              <p className="text-gray-500">{shippingMethod.description}</p>
              <p className="text-gray-500 text-xs mt-1">
                Estimated delivery: {shippingMethod.estimatedDelivery}
              </p>
            </div>
          </div>

          <Separator />

          {/* Payment Method */}
          <div>
            <div className="flex items-center mb-2">
              <CreditCard className="h-5 w-5 mr-2 text-primary" />
              <h3 className="font-medium">Payment Method</h3>
            </div>
            <div className="pl-7 text-sm">
              <p>{paymentMethod.name}</p>
              {paymentMethod.id === 'card' && (
                <p className="text-gray-500">
                  Card ending in **** (for demo purposes)
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={onBack} disabled={isSubmitting}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Payment
        </Button>

        <Button
          onClick={handlePlaceOrder}
          disabled={isSubmitting}
          className="min-w-[150px]"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            'Place Order'
          )}
        </Button>
      </div>
    </>
  );
}
