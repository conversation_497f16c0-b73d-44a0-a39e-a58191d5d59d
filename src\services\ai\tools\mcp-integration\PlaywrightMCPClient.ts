/**
 * PlaywrightMCPClient.ts
 * 
 * Client wrapper for the Model Context Protocol (MCP) Playwright server
 * Provides a clean interface for web automation tasks
 */

/**
 * Response from MCP server
 */
interface MCPResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * MCP Playwright client configuration
 */
interface PlaywrightMCPConfig {
  serverUrl?: string;
  timeout?: number;
  headless?: boolean;
}

/**
 * Element extraction options
 */
interface ElementExtractionOptions {
  selector: string;
  attributes: string[];
}

/**
 * Client for interacting with MCP Playwright server
 */
export class PlaywrightMCPClient {
  private serverUrl: string;
  private timeout: number;
  private headless: boolean;
  private connected: boolean = false;

  /**
   * Constructor
   * @param config - Client configuration
   */
  constructor(config: PlaywrightMCPConfig = {}) {
    this.serverUrl = config.serverUrl || 'http://localhost:3000';
    this.timeout = config.timeout || 30000;
    this.headless = config.headless !== false;
  }

  /**
   * Connect to MCP server
   */
  async connect(): Promise<void> {
    if (this.connected) return;

    try {
      // Check if server is running
      const response = await this.callMCP('browser_install', {});
      
      if (!response.success) {
        throw new Error(`Failed to connect to MCP server: ${response.error}`);
      }
      
      this.connected = true;
    } catch (error) {
      console.error('Failed to connect to MCP server:', error);
      throw new Error(`Failed to connect to MCP server: ${error.message}`);
    }
  }

  /**
   * Navigate to URL
   * @param url - URL to navigate to
   */
  async navigate(url: string): Promise<void> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_navigate', { url });
    
    if (!response.success) {
      throw new Error(`Failed to navigate to ${url}: ${response.error}`);
    }
  }

  /**
   * Take snapshot of current page
   * @returns Page snapshot
   */
  async snapshot(): Promise<any> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_snapshot', {});
    
    if (!response.success) {
      throw new Error(`Failed to take snapshot: ${response.error}`);
    }
    
    return response.data;
  }

  /**
   * Click element
   * @param options - Click options
   */
  async click(options: { element: string, ref: string }): Promise<void> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_click', options);
    
    if (!response.success) {
      throw new Error(`Failed to click element: ${response.error}`);
    }
  }

  /**
   * Type text into element
   * @param options - Type options
   */
  async type(options: { element: string, ref: string, text: string, submit?: boolean }): Promise<void> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_type', options);
    
    if (!response.success) {
      throw new Error(`Failed to type text: ${response.error}`);
    }
  }

  /**
   * Take screenshot
   * @param options - Screenshot options
   * @returns Screenshot data URL
   */
  async takeScreenshot(options: { element?: string, ref?: string } = {}): Promise<string> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_take_screenshot', options);
    
    if (!response.success) {
      throw new Error(`Failed to take screenshot: ${response.error}`);
    }
    
    return response.data.screenshot;
  }

  /**
   * Wait for text or time
   * @param options - Wait options
   */
  async waitFor(options: { text?: string, textGone?: string, time?: number }): Promise<void> {
    await this.ensureConnected();
    
    const response = await this.callMCP('browser_wait_for', options);
    
    if (!response.success) {
      throw new Error(`Wait failed: ${response.error}`);
    }
  }

  /**
   * Extract elements from page
   * @param options - Element extraction options
   * @returns Array of elements with requested attributes
   */
  async extractElements(options: ElementExtractionOptions): Promise<any[]> {
    await this.ensureConnected();
    
    // Get page snapshot
    const snapshot = await this.snapshot();
    
    // Use evaluate to extract elements and attributes
    const script = `
      Array.from(document.querySelectorAll('${options.selector}')).map(el => {
        const result = {};
        ${options.attributes.map(attr => `
          result['${attr}'] = el.getAttribute('${attr}') || 
                             (el['${attr}'] !== undefined ? el['${attr}'] : '');
        `).join('\n')}
        return result;
      })
    `;
    
    return await this.evaluateOnPage(script);
  }

  /**
   * Evaluate JavaScript on page
   * @param script - JavaScript to evaluate
   * @returns Evaluation result
   */
  async evaluateOnPage(script: string): Promise<any> {
    await this.ensureConnected();
    
    // In a real implementation, this would call the browser_evaluate MCP method
    // For now, we'll simulate it with a direct call
    const response = await this.callMCP('browser_evaluate', { script });
    
    if (!response.success) {
      throw new Error(`Failed to evaluate script: ${response.error}`);
    }
    
    return response.data;
  }

  /**
   * Ensure connected to MCP server
   */
  private async ensureConnected(): Promise<void> {
    if (!this.connected) {
      await this.connect();
    }
  }

  /**
   * Call MCP server
   * @param method - Method name
   * @param params - Method parameters
   * @returns MCP response
   */
  private async callMCP<T>(method: string, params: any): Promise<MCPResponse<T>> {
    try {
      // In a real implementation, this would make an HTTP request to the MCP server
      // For now, we'll use the MCP tool functions directly if available
      
      const mcpMethod = `mcp0_${method}`;
      
      // This is a placeholder - in a real implementation, we would call the MCP method
      // For now, we'll return a success response
      return {
        success: true,
        data: {} as T
      };
    } catch (error) {
      console.error(`MCP call failed (${method}):`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Close browser
   */
  async close(): Promise<void> {
    if (!this.connected) return;
    
    try {
      await this.callMCP('browser_close', {});
      this.connected = false;
    } catch (error) {
      console.error('Failed to close browser:', error);
    }
  }
}
