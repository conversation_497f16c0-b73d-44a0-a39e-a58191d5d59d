// Final direct fix for Next Day Delivery shipping method
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function finalFix() {
  console.log('🔧 Final fix for Next Day Delivery...');
  
  try {
    // 1. Direct SQL update to ensure Next Day Delivery is inactive
    const { error: updateError } = await supabase
      .from('shipping_methods')
      .update({ is_active: false })
      .like('name', '%Next Day%');
      
    if (updateError) {
      console.error('Error updating Next Day Delivery:', updateError);
      return;
    }
    
    console.log('✅ Next Day Delivery has been forced to inactive');
    
    // 2. Verify all shipping methods
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log('\nCurrent shipping methods status:');
    console.log('-------------------------------------------');
    
    methods.forEach(method => {
      console.log(`${method.name}: ${method.is_active ? 'ACTIVE' : 'INACTIVE'}`);
    });
    
    console.log('\n🔄 To ensure changes take effect:');
    console.log('1. Restart the development server');
    console.log('2. Clear your browser cache or use incognito mode');
    console.log('3. Check the admin panel and checkout page');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
finalFix()
  .catch(err => {
    console.error('Error running script:', err);
  });
