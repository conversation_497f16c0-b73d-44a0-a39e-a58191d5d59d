import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/auth.basic';
import { Loader2, Upload, FileText, Check, X } from 'lucide-react';
import Papa from 'papaparse';
import slugify from 'slugify';

// Define the CSV product structure based on your file
interface CSVProduct {
  handleId: string;
  fieldType: string;
  name: string;
  description: string;
  productImageUrl: string;
  collection: string;
  sku: string;
  ribbon: string;
  price: string;
  surcharge: string;
  visible: string;
  discountMode: string;
  discountValue: string;
  inventory: string;
  weight: string;
  cost: string;
  brand: string;
  // Product options
  productOptionName1?: string;
  productOptionType1?: string;
  productOptionDescription1?: string;
  productOptionName2?: string;
  productOptionType2?: string;
  productOptionDescription2?: string;
  productOptionName3?: string;
  productOptionType3?: string;
  productOptionDescription3?: string;
  // Additional information
  additionalInfoTitle1?: string;
  additionalInfoDescription1?: string;
  additionalInfoTitle2?: string;
  additionalInfoDescription2?: string;
  additionalInfoTitle3?: string;
  additionalInfoDescription3?: string;
}

// Define the database product structure
interface DBProduct {
  id?: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  sale_price: number | null;
  image: string;
  additional_images?: string[];
  category_id: string | null;
  brand_id: string | null;
  sku?: string;
  stock_quantity?: number;
  weight?: number;
  in_stock: boolean;
  is_featured: boolean;
  is_new: boolean;
  is_best_seller: boolean;
  
  // Product options
  option_name1?: string;
  option_type1?: string;
  option_description1?: string;
  option_name2?: string;
  option_type2?: string;
  option_description2?: string;
  option_name3?: string;
  option_type3?: string;
  option_description3?: string;
  
  // Additional information
  additional_info_title1?: string;
  additional_info_description1?: string;
  additional_info_title2?: string;
  additional_info_description2?: string;
  additional_info_title3?: string;
  additional_info_description3?: string;
}

const ProductImport: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const [csvData, setCsvData] = useState<CSVProduct[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importedCount, setImportedCount] = useState(0);
  const [failedCount, setFailedCount] = useState(0);
  const [activeTab, setActiveTab] = useState('upload');
  const [selectedProducts, setSelectedProducts] = useState<CSVProduct[]>([]);
  
  // Read import strategy from localStorage (set by ProductImportHelper)
  const [skipImages, setSkipImages] = useState(() => {
    const strategy = localStorage.getItem('importStrategy');
    return strategy === 'skip';
  });
  
  const [useExternalUrls, setUseExternalUrls] = useState(() => {
    const strategy = localStorage.getItem('importStrategy');
    return strategy === 'external';
  });
  
  const [useLocalImages, setUseLocalImages] = useState(() => {
    const strategy = localStorage.getItem('importStrategy');
    return strategy === 'local';
  });
  
  const [localImagesPath, setLocalImagesPath] = useState(() => {
    return localStorage.getItem('localImagesPath') || '/images/products/wix-imports';
  });
  
  const [wixDomain, setWixDomain] = useState(() => {
    return localStorage.getItem('wixDomain') || '';
  });

  // Fetch categories and brands on component mount
  const [brands, setBrands] = useState<{ id: string; name: string }[]>([]);
  
  React.useEffect(() => {
    const fetchData = async () => {
      // Fetch categories
      const { data: categoryData, error: categoryError } = await supabase
        .from('categories')
        .select('id, name');
      
      if (categoryError) {
        console.error('Error fetching categories:', categoryError);
        toast({
          title: 'Error',
          description: 'Failed to fetch categories',
          variant: 'destructive',
        });
        return;
      }
      
      if (categoryData) {
        setCategories(categoryData);
      }
      
      // Fetch brands
      try {
        const { data: brandData, error: brandError } = await supabase
          .from('brands')
          .select('id, name');
        
        if (brandError) {
          console.error('Error fetching brands:', brandError);
          toast({
            title: 'Error',
            description: 'Failed to fetch brands',
            variant: 'destructive',
          });
          return;
        }
        
        if (brandData) {
          setBrands(brandData);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch brands',
          variant: 'destructive',
        });
      }
    };

    fetchData();
  }, [toast]);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          setCsvData(results.data as CSVProduct[]);
          setActiveTab('preview');
        },
        error: (error) => {
          toast({
            title: 'Error',
            description: `Failed to parse CSV: ${error.message}`,
            variant: 'destructive',
          });
        }
      });
    }
  }, [toast, setActiveTab]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv'],
    },
    maxFiles: 1
  });

  // Toggle product selection
  const toggleProductSelection = (product: CSVProduct) => {
    if (selectedProducts.some(p => p.handleId === product.handleId)) {
      setSelectedProducts(selectedProducts.filter(p => p.handleId !== product.handleId));
    } else {
      setSelectedProducts([...selectedProducts, product]);
    }
  };

  // Select all products
  const selectAllProducts = () => {
    if (selectedProducts.length === csvData.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts([...csvData]);
    }
  };

  // Map CSV product to DB format
  const mapProductToDBFormat = (product: CSVProduct, categoryId: string | null, brandId: string | null): DBProduct => {
    // Extract images from semicolon-separated list (Wix format)
    const images = product.productImageUrl ? product.productImageUrl.split(';') : [];
    const firstImage = images.length > 0 ? images[0].trim() : '';
    
    // Extract additional images (all images except the first one)
    const additionalImages = images.length > 1 ? images.slice(1).map(img => img.trim()) : [];
    
    // Determine if product is in stock
    const inStock = product.inventory === 'InStock';
    
    // Calculate sale price if discount is available
    let salePrice = null;
    if (product.discountMode === 'PERCENT' && product.discountValue) {
      const discountValue = parseFloat(product.discountValue);
      const originalPrice = parseFloat(product.price);
      if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
        salePrice = originalPrice * (1 - discountValue / 100);
      }
    }
    
    return {
      name: product.name,
      slug: slugify(product.name, { lower: true, strict: true }),
      description: product.description || '',
      price: parseFloat(product.price) || 0,
      sale_price: salePrice,
      image: firstImage, // This will be the Wix filename, which will be processed in processImage
      additional_images: additionalImages,
      category_id: categoryId,
      brand_id: brandId,
      in_stock: inStock,
      is_featured: false, // Default values, can be updated later
      is_new: false,
      is_best_seller: false,
      
      // Map product options
      option_name1: product.productOptionName1 || null,
      option_type1: product.productOptionType1 || null,
      option_description1: product.productOptionDescription1 || null,
      option_name2: product.productOptionName2 || null,
      option_type2: product.productOptionType2 || null,
      option_description2: product.productOptionDescription2 || null,
      option_name3: product.productOptionName3 || null,
      option_type3: product.productOptionType3 || null,
      option_description3: product.productOptionDescription3 || null,
      
      // Map additional information
      additional_info_title1: product.additionalInfoTitle1 || null,
      additional_info_description1: product.additionalInfoDescription1 || null,
      additional_info_title2: product.additionalInfoTitle2 || null,
      additional_info_description2: product.additionalInfoDescription2 || null,
      additional_info_title3: product.additionalInfoTitle3 || null,
      additional_info_description3: product.additionalInfoDescription3 || null,
    };
  };

  // Process image based on selected strategy
  const processImage = async (imageUrl: string, productName: string): Promise<string> => {
    try {
      // Skip if no image URL
      if (!imageUrl) return '';
      
      // If skip images is enabled, return placeholder
      if (skipImages) {
        return '/placeholder-product.jpg';
      }
      
      // If using external URLs, construct Wix CDN URL
      if (useExternalUrls) {
        if (imageUrl.includes('~mv2')) {
          // This is a Wix image filename
          return `https://static.wixstatic.com/media/${imageUrl}`;
        } else if (imageUrl.startsWith('http')) {
          // Already a full URL, use as is
          return imageUrl;
        } else {
          // Assume it's a relative path on Wix
          return `https://static.wixstatic.com/media/${imageUrl}`;
        }
      }
      
      // If using locally downloaded images
      if (useLocalImages) {
        if (imageUrl.includes('~mv2')) {
          // This is a Wix image filename that should be in our local directory
          console.log(`Using locally downloaded Wix image: ${imageUrl}`);
          
          // Try to find the image with different extensions
          const baseFilename = imageUrl.split('.')[0];
          const extensions = ['webp', 'jpg', 'png', 'jpeg'];
          
          for (const ext of extensions) {
            try {
              // Check if file exists in public directory with this extension
              const altFileName = `${baseFilename}.${ext}`;
              const localFilePath = `${localImagesPath}/${altFileName}`;
              console.log(`Attempting to load image from: ${localFilePath}`);
              
              // Create a File object from the local image
              const response = await fetch(localFilePath);
              
              if (!response.ok) {
                console.log(`Image not found at ${localFilePath}, trying next extension`);
                continue; // Try next extension
              }
              
              console.log(`Successfully loaded image from: ${localFilePath}`);
              const blob = await response.blob();
              
              // Create a sanitized filename for storage (remove special characters)
              const sanitizedFilename = `${baseFilename.replace(/~mv2/g, '')}.${ext}`;
              
              // Upload to Supabase storage with sanitized filename
              const { data, error } = await supabase.storage
                .from('product-images')
                .upload(sanitizedFilename, blob, {
                  contentType: `image/${ext}`,
                  cacheControl: '3600',
                  upsert: true
                });
              
              if (error) {
                console.error(`Error uploading image ${altFileName}:`, error);
                continue; // Try next extension
              }
              
              // Get the public URL
              const { data: publicUrlData } = supabase.storage
                .from('product-images')
                .getPublicUrl(sanitizedFilename);
              
              return publicUrlData.publicUrl;
            } catch (e) {
              console.log(`Error processing image with extension ${ext}:`, e);
              // Continue to next extension
            }
          }
          
          // If we get here, all extensions failed
          console.log(`No matching image found for ${imageUrl}`);
          return '/placeholder-product.jpg';
        }
      }
      
      // Standard approach - upload to Supabase
      console.log(`Processing image: ${imageUrl}`);
      
      // Check if this is a Wix image filename (contains ~mv2)
      if (imageUrl.includes('~mv2')) {
        // Create a sanitized filename for storage (remove special characters)
        const baseFilename = imageUrl.split('.')[0];
        const fileExt = imageUrl.split('.').pop() || 'jpg';
        const sanitizedFilename = `${baseFilename.replace(/~mv2/g, '')}.${fileExt}`;
        
        // This is a Wix image filename, get the public URL from Supabase storage
        // We assume the image has already been uploaded to Supabase with the sanitized filename
        const { data: publicUrlData } = supabase.storage
          .from('product-images')
          .getPublicUrl(sanitizedFilename);
        
        console.log(`Using existing Wix image from Supabase: ${sanitizedFilename}`);
        return publicUrlData.publicUrl;
      }
      
      // For non-Wix images or URLs, use the original upload logic
      console.log(`Uploading image from URL: ${imageUrl}`);
      
      // Extract filename from URL or generate one based on product name
      const fileName = imageUrl.split('/').pop() || `${slugify(productName)}.jpg`;
      const uniqueFileName = `${Date.now()}-${fileName}`;
      
      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error(`Failed to fetch image: ${response.statusText}`);
      
      const blob = await response.blob();
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(uniqueFileName, blob, {
          contentType: blob.type,
          cacheControl: '3600'
        });
      
      if (error) throw error;
      
      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from('product-images')
        .getPublicUrl(uniqueFileName);
      
      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error processing image:', error);
      // Return placeholder as fallback
      return '/placeholder-product.jpg';
    }
  };

  // Import selected products
  const importProducts = async () => {
    if (selectedProducts.length === 0) {
      toast({
        title: 'No products selected',
        description: 'Please select at least one product to import',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setProgress(0);
    setImportedCount(0);
    setFailedCount(0);
    setActiveTab('importing');

    const total = selectedProducts.length;
    let imported = 0;
    let failed = 0;

    for (const [index, product] of selectedProducts.entries()) {
      try {
        // Get category ID based on collection
        let categoryId = null;
        if (product.collection) {
          const collections = product.collection.split(';');
          if (collections.length > 0) {
            const matchingCategory = categories.find(c => c.name === collections[0]);
            if (matchingCategory) {
              categoryId = matchingCategory.id;
            }
          }
        }
        
        // Get brand ID if available
        let brandId = null;
        if (product.brand) {
          const matchingBrand = brands.find(b => b.name === product.brand);
          if (matchingBrand) {
            brandId = matchingBrand.id;
          }
        }

        // Process images based on selected strategy
        let imageUrl = '';
        let additionalImageUrls: string[] = [];
        
        // If skip images is enabled, use placeholder
        if (skipImages) {
          imageUrl = '/placeholder-product.jpg';
        }
        // Otherwise process the images if available
        else if (product.productImageUrl) {
          // Update progress to show we're processing the image
          setProgress(Math.round(((index + 0.5) / total) * 100));
          
          // Handle multiple images (semicolon-separated)
          const images = product.productImageUrl.split(';');
          if (images.length > 0) {
            // Use the first image as the main product image
            const firstImage = images[0].trim();
            imageUrl = await processImage(firstImage, product.name);
            
            // Process additional images if any
            if (images.length > 1) {
              const additionalImages = images.slice(1);
              for (const img of additionalImages) {
                if (img.trim()) {
                  const processedUrl = await processImage(img.trim(), product.name);
                  if (processedUrl) {
                    additionalImageUrls.push(processedUrl);
                  }
                }
              }
            }
          }
        }

        // Map product to DB format
        const dbProduct = mapProductToDBFormat(product, categoryId, brandId);
        
        // Update with the uploaded image URLs
        dbProduct.image = imageUrl;
        if (additionalImageUrls.length > 0) {
          dbProduct.additional_images = additionalImageUrls;
        }
        
        // Insert product into database
        const { error } = await supabase
          .from('products')
          .insert(dbProduct);
        
        if (error) throw error;
        
        imported++;
        setImportedCount(imported);
      } catch (error) {
        console.error('Error importing product:', error);
        failed++;
        setFailedCount(failed);
      }
      
      // Update progress
      setProgress(Math.round(((index + 1) / total) * 100));
    }
    
    setIsLoading(false);
    
    toast({
      title: 'Import Complete',
      description: `${imported} products imported, ${failed} failed`,
      variant: failed > 0 ? 'destructive' : 'default',
    });
  };

  // Handle file change (for traditional file input)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          setCsvData(results.data as CSVProduct[]);
          setActiveTab('preview');
        },
        error: (error) => {
          toast({
            title: 'Error',
            description: `Failed to parse CSV: ${error.message}`,
            variant: 'destructive',
          });
        }
      });
    }
  };

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Product Import</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="upload">Upload CSV</TabsTrigger>
          <TabsTrigger value="preview" disabled={csvData.length === 0}>Preview Data</TabsTrigger>
          <TabsTrigger value="importing" disabled={!isLoading && importedCount === 0}>Importing</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload">
          <Card>
            <CardHeader>
              <CardTitle>Upload Product CSV</CardTitle>
              <CardDescription>
                Upload a CSV file containing product data. The file should include columns for product name, 
                description, price, and other details.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div 
                {...getRootProps()} 
                className={`border-2 border-dashed rounded-md p-10 text-center cursor-pointer ${
                  isDragActive ? 'border-primary bg-primary/10' : 'border-gray-300'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">
                  {isDragActive
                    ? "Drop the CSV file here..."
                    : "Drag and drop a CSV file here, or click to select a file"
                  }
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  Only CSV files are supported
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="preview">
          <Card>
            <CardHeader>
              <CardTitle>Preview Products</CardTitle>
              <CardDescription>
                Select the products you want to import from the CSV file.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={selectAllProducts}
                  className="mr-2"
                >
                  {selectedProducts.length === csvData.length ? 'Deselect All' : 'Select All'}
                </Button>
                <span className="text-sm text-gray-500">
                  {selectedProducts.length} of {csvData.length} products selected
                </span>
              </div>
              
              <div className="border rounded-md">
                <div className="grid grid-cols-12 gap-2 p-3 font-medium bg-gray-100 border-b">
                  <div className="col-span-1"></div>
                  <div className="col-span-4">Name</div>
                  <div className="col-span-2">Price</div>
                  <div className="col-span-3">Collection</div>
                  <div className="col-span-2">Status</div>
                </div>
                
                <div className="max-h-96 overflow-y-auto">
                  {csvData.slice(0, 100).map((product, index) => (
                    <div 
                      key={product.handleId || index} 
                      className={`grid grid-cols-12 gap-2 p-3 border-b hover:bg-gray-50 cursor-pointer ${
                        selectedProducts.some(p => p.handleId === product.handleId) ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => toggleProductSelection(product)}
                    >
                      <div className="col-span-1">
                        <div className="flex items-center h-full">
                          <input
                            type="checkbox"
                            checked={selectedProducts.some(p => p.handleId === product.handleId)}
                            onChange={() => {}}
                            className="rounded border-gray-300"
                          />
                        </div>
                      </div>
                      <div className="col-span-4 truncate">{product.name}</div>
                      <div className="col-span-2">£{product.price}</div>
                      <div className="col-span-3 truncate">{product.collection?.split(';')[0] || 'Uncategorized'}</div>
                      <div className="col-span-2">
                        {product.visible === 'TRUE' ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Hidden
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {csvData.length > 100 && (
                    <div className="p-3 text-center text-gray-500 border-t">
                      {csvData.length - 100} more products not shown in preview
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={importProducts} 
                disabled={selectedProducts.length === 0}
              >
                Import Selected Products
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="importing">
          <Card>
            <CardHeader>
              <CardTitle>Importing Products</CardTitle>
              <CardDescription>
                Please wait while your products are being imported...
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Progress value={progress} className="w-full" />
                
                <div className="flex justify-between text-sm">
                  <span>Progress: {progress}%</span>
                  <span>
                    {importedCount} imported, {failedCount} failed
                  </span>
                </div>
                
                {isLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Importing products...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-4">
                    {failedCount === 0 ? (
                      <Check className="h-8 w-8 text-green-500" />
                    ) : (
                      <X className="h-8 w-8 text-red-500" />
                    )}
                    <span className="ml-2">
                      Import {failedCount === 0 ? 'completed successfully' : 'completed with errors'}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => setActiveTab('upload')} variant="outline" className="mr-2">
                Start Over
              </Button>
              <Button onClick={() => window.location.href = '/admin'}>
                Go to Admin Dashboard
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProductImport;

