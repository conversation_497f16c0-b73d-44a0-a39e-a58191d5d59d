"""
Enhanced Dutch Passion Website Scraper

This script scrapes all products (all 136) from Dutch Passion's website,
including their relationships with filter categories.
"""

import requests
import json
import re
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Base URL
BASE_URL = "https://dutch-passion.com"
SEEDS_URL = "https://dutch-passion.com/en/cannabis-seeds"

# Headers to mimic a browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Connection": "keep-alive",
    "Referer": "https://dutch-passion.com/",
    "DNT": "1",  # Do Not Track
}

# Function to get page content with age verification bypass
def get_page_content(url):
    print(f"Fetching content from: {url}")
    
    # First request to get cookies
    session = requests.Session()
    response = session.get(url, headers=HEADERS)
    
    # Check if we need to handle age verification
    if "ARE YOU AGED 18 OR OVER?" in response.text:
        print("Handling age verification...")
        
        # Extract any necessary tokens or form data
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Simulate clicking the "Enter" button by making a POST request
        verification_url = urljoin(BASE_URL, "/en/age-verification")
        verification_data = {
            "age_verification": "true",
            "redirect": "/en/cannabis-seeds"
        }
        
        # Send POST request to handle age verification
        response = session.post(verification_url, data=verification_data, headers=HEADERS)
        
        # Get the main page again after verification
        response = session.get(url, headers=HEADERS)
    
    return response.text

# Function to extract filter categories and options
def extract_filters(html_content):
    print("Extracting filter categories and options...")
    filter_data = {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all filter sections using the correct selector
    filter_sections = soup.select('section.facet.clearfix')
    
    print(f"Found {len(filter_sections)} filter sections")
    
    for section in filter_sections:
        try:
            # Get category name from the correct element
            category_element = section.select_one('div.facet-title.hidden-sm-down, div.h6.facet-title')
            if not category_element:
                continue
                
            category_name = category_element.text.strip()
            filter_data[category_name] = []
            
            # Get all options in this category
            options = section.select('li')
            
            for option in options:
                option_link = option.select_one('a.custom-control-label')
                if not option_link:
                    continue
                    
                option_text = option_link.text.strip()
                
                # Extract option name and count
                magnitude = option_link.select_one('span.magnitude')
                if magnitude:
                    count_text = magnitude.text.strip()
                    # Extract the number from format like "(25)"
                    count = re.search(r'\((\d+)\)', count_text)
                    count = count.group(1) if count else "0"
                    
                    # Remove the count from the option text to get clean name
                    name = option_text.replace(count_text, '').strip()
                else:
                    name = option_text
                    count = "0"
                
                # Try to get value from data attribute or href
                value = name
                if option_link.get('href'):
                    # Extract value from URL parameter if available
                    href = option_link.get('href')
                    param_match = re.search(r'tn_fk_\w+=([^&]+)', href)
                    if param_match:
                        value = param_match.group(1)
                
                filter_data[category_name].append({
                    "name": name,
                    "count": count,
                    "value": value
                })
        except Exception as e:
            print(f"Error extracting filter section: {e}")
    
    return filter_data

# Function to extract products from a single page
def extract_products_from_page(html_content):
    print("Extracting products from page...")
    products = []
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all product cards - try multiple possible selectors
    product_cards = soup.select('.js-product-miniature-wrapper, .product-miniature, article')
    
    print(f"Found {len(product_cards)} product cards on this page")
    
    for card in product_cards:
        try:
            product = {}
            
            # Extract product name - try multiple possible selectors
            name_elem = card.select_one('.product-title a, h3 a, .product-name')
            if name_elem:
                product["name"] = name_elem.text.strip()
            else:
                continue  # Skip if no name found
            
            # Extract price - try multiple possible selectors
            price_elem = card.select_one('.product-price-and-shipping .price, .price, .product-price')
            if price_elem:
                product["price"] = price_elem.text.strip()
            else:
                product["price"] = "N/A"
            
            # Extract seed type - try multiple indicators
            seed_type_indicators = {
                "Autoflower": ['.seed-type:contains("Auto"), .product-flag.autoflower, .badge-auto'],
                "Feminized": ['.seed-type:contains("Fem"), .product-flag.feminized, .badge-fem'],
                "Regular": ['.seed-type:contains("Reg"), .product-flag.regular, .badge-reg']
            }
            
            seed_type = "Unknown"
            for type_name, selectors in seed_type_indicators.items():
                for selector in selectors:
                    try:
                        if card.select_one(selector) or (card.text and type_name.lower() in card.text.lower()):
                            seed_type = type_name
                            break
                    except:
                        pass
                if seed_type != "Unknown":
                    break
                    
            product["seed_type"] = seed_type
            
            # Extract product URL
            link_elem = card.select_one('a[href]')
            if link_elem:
                product_url = link_elem.get('href')
                if not product_url.startswith('http'):
                    product_url = urljoin(BASE_URL, product_url)
                product["url"] = product_url
            
            # Extract image URL
            img_elem = card.select_one('img')
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url and not img_url.startswith('http'):
                    img_url = urljoin(BASE_URL, img_url)
                product["image_url"] = img_url
            
            # Extract any data attributes that might contain filter information
            for attr in card.attrs:
                if attr.startswith('data-'):
                    product[attr] = card[attr]
            
            products.append(product)
        except Exception as e:
            print(f"Error extracting product: {e}")
    
    return products

# Function to extract product details and filter relationships
def extract_product_details(product_url):
    try:
        html_content = get_page_content(product_url)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        details = {}
        filter_relationships = {}
        
        # Extract product specifications/attributes
        specs = {}
        spec_elements = soup.select('.product-features dl, .product-attributes, .product-info')
        
        for spec_group in spec_elements:
            try:
                # Try different structures
                dt_elements = spec_group.select('dt, .attribute-name, .feature-name')
                dd_elements = spec_group.select('dd, .attribute-value, .feature-value')
                
                for i in range(min(len(dt_elements), len(dd_elements))):
                    label = dt_elements[i].text.strip()
                    value = dd_elements[i].text.strip()
                    specs[label] = value
            except Exception as e:
                print(f"Error extracting specification group: {e}")
        
        details["specifications"] = specs
        
        # Extract product description
        desc_elem = soup.select_one('#description, .product-description, .description')
        if desc_elem:
            details["description"] = desc_elem.text.strip()
        else:
            details["description"] = ""
        
        # Extract filter relationships
        # Look for indicators of filter categories on the product page
        
        # 1. Flowertime/Flowering time
        flowering_time_elements = soup.select('.product-feature:contains("Flowering time"), .product-info:contains("weeks"), .product-attributes:contains("Flowering")')
        for elem in flowering_time_elements:
            text = elem.text.lower()
            if 'week' in text:
                # Try to extract the number of weeks
                weeks_match = re.search(r'(\d+)(?:\s*-\s*\d+)?\s*weeks?', text)
                if weeks_match:
                    filter_relationships["flowertime"] = weeks_match.group(0)
        
        # 2. Yield
        yield_elements = soup.select('.product-feature:contains("Yield"), .product-info:contains("Yield"), .product-attributes:contains("Yield")')
        for elem in yield_elements:
            text = elem.text.lower()
            for yield_type in ["XXL", "XL", "L", "M", "M/L"]:
                if yield_type.lower() in text:
                    filter_relationships["yield"] = yield_type
                    break
        
        # 3. THC content
        thc_elements = soup.select('.product-feature:contains("THC"), .product-info:contains("THC"), .product-attributes:contains("THC")')
        for elem in thc_elements:
            text = elem.text.lower()
            for thc_level in ["Extremely high", "Very High", "High", "Medium", "Low"]:
                if thc_level.lower() in text:
                    filter_relationships["thc"] = thc_level
                    break
        
        # 4. Effect (Sativa/Indica/Hybrid)
        effect_elements = soup.select('.product-feature:contains("Effect"), .product-info:contains("Sativa"), .product-info:contains("Indica"), .product-info:contains("Hybrid")')
        for elem in effect_elements:
            text = elem.text.lower()
            for effect in ["Sativa", "Indica", "Hybrid"]:
                if effect.lower() in text:
                    filter_relationships["effect"] = effect
                    break
        
        # 5. Seed Family
        family_elements = soup.select('.product-feature:contains("Family"), .product-info:contains("Family"), .product-attributes:contains("Family")')
        for elem in family_elements:
            text = elem.text.lower()
            for family in ["Afghani Kush", "Blue Family", "CBD Rich", "Classics", "Dutch Outdoor"]:
                if family.lower() in text:
                    filter_relationships["seed_family"] = family
                    break
        
        # 6. CBD content
        cbd_elements = soup.select('.product-feature:contains("CBD"), .product-info:contains("CBD"), .product-attributes:contains("CBD")')
        for elem in cbd_elements:
            text = elem.text.lower()
            if "cbd" in text:
                # Try to extract CBD percentage
                cbd_match = re.search(r'(\d+(?:\.\d+)?)%', text)
                if cbd_match:
                    filter_relationships["cbd"] = f"{cbd_match.group(1)}%"
                else:
                    for cbd_level in ["Low", "Medium", "High"]:
                        if cbd_level.lower() in text:
                            filter_relationships["cbd"] = cbd_level
                            break
        
        # 7. Prize Winner
        prize_elements = soup.select('.product-feature:contains("Prize"), .product-info:contains("Prize"), .product-info:contains("Cup"), .product-info:contains("Award")')
        if prize_elements:
            filter_relationships["prize_winner"] = "Yes"
        
        # Combine details and filter relationships
        return {**details, "filter_relationships": filter_relationships}
    
    except Exception as e:
        print(f"Error getting product details: {e}")
        return {"specifications": {}, "description": "", "filter_relationships": {}}

# Function to extract pagination information
def extract_pagination(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Try to find pagination elements
    pagination = soup.select('.pagination a, .page-numbers')
    
    max_page = 1
    for page_link in pagination:
        try:
            page_num = int(page_link.text.strip())
            if page_num > max_page:
                max_page = page_num
        except ValueError:
            pass
    
    return max_page

# Main function to run the scraper
def main():
    print("Starting Enhanced Dutch Passion Website Scraper...")
    
    try:
        # Get main page content
        main_page_html = get_page_content(SEEDS_URL)
        
        # Extract filter data
        filter_data = extract_filters(main_page_html)
        
        # Extract pagination information
        max_page = extract_pagination(main_page_html)
        print(f"Found {max_page} pages of products")
        
        # Extract products from all pages
        all_products = []
        
        # Extract products from first page
        first_page_products = extract_products_from_page(main_page_html)
        all_products.extend(first_page_products)
        
        # Extract products from remaining pages
        for page in range(2, max_page + 1):
            page_url = f"{SEEDS_URL}?page={page}"
            page_html = get_page_content(page_url)
            page_products = extract_products_from_page(page_html)
            all_products.extend(page_products)
            time.sleep(1)  # Be nice to the server
        
        print(f"Extracted {len(all_products)} products from all pages")
        
        # Get detailed information and filter relationships for each product
        detailed_products = []
        
        for i, product in enumerate(all_products):
            print(f"Getting details for product {i+1}/{len(all_products)}: {product['name']}")
            details = extract_product_details(product['url'])
            detailed_product = {**product, **details}
            detailed_products.append(detailed_product)
            time.sleep(1)  # Be nice to the server
        
        # Save data to JSON files
        with open('dutch_passion_all_filters.json', 'w', encoding='utf-8') as f:
            json.dump(filter_data, f, indent=2)
        
        with open('dutch_passion_all_products.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_products, f, indent=2)
        
        # Create a simplified version with just products and their filter relationships
        simplified_products = []
        for product in detailed_products:
            simplified_product = {
                "id": product.get("data-id-product", ""),
                "name": product.get("name", ""),
                "price": product.get("price", ""),
                "seed_type": product.get("seed_type", ""),
                "image_url":
(Content truncated due to size limit. Use line ranges to read in chunks)