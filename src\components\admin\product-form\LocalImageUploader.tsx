import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Upload } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface LocalImageUploaderProps {
  onImageUploaded: (url: string) => void;
  buttonText?: string;
  disabled?: boolean;
}

/**
 * A simple image uploader that converts images to data URLs without using Supabase storage.
 * This is a temporary solution for the client demo.
 */
export function LocalImageUploader({ 
  onImageUploaded, 
  buttonText = 'Upload Image', 
  disabled = false 
}: LocalImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUpload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Upload button clicked");
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("File input changed");
    const files = e.target.files;
    if (!files || files.length === 0) {
      console.log("No files selected");
      return;
    }

    const file = files[0];
    console.log("File selected:", file.name);
    setIsUploading(true);

    try {
      // Convert the file to a data URL
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          resolve(reader.result as string);
        };
        reader.onerror = () => {
          reject(new Error('Failed to read file'));
        };
        reader.readAsDataURL(file);
      });

      console.log("Image converted to data URL");
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      // Notify the parent component with the new URL
      onImageUploaded(dataUrl);
      
      toast({
        title: 'Image uploaded',
        description: 'Your image has been uploaded successfully',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'There was an error uploading your image',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleUpload}
        disabled={isUploading || disabled}
      >
        {isUploading ? 'Uploading...' : (
          <>
            <Upload className="mr-1 h-4 w-4" />
            {buttonText}
          </>
        )}
      </Button>
    </div>
  );
}
