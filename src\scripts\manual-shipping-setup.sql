-- Manual Shipping Setup Script
-- Copy and paste this into your Supabase SQL Editor

-- Step 1: Create shipping zones table
CREATE TABLE IF NOT EXISTS shipping_zones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    countries JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Create shipping methods table
CREATE TABLE IF NOT EXISTS shipping_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zone_id UUID NOT NULL REFERENCES shipping_zones(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    free_shipping_threshold DECIMAL(10,2),
    estimated_days_min INTEGER NOT NULL DEFAULT 1,
    estimated_days_max INTEGER NOT NULL DEFAULT 7,
    icon VARCHAR(50) NOT NULL DEFAULT 'standard',
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create indexes
CREATE INDEX IF NOT EXISTS idx_shipping_zones_active ON shipping_zones(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_zones_countries ON shipping_zones USING GIN(countries);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_zone_id ON shipping_methods(zone_id);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_active ON shipping_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_sort_order ON shipping_methods(sort_order);

-- Step 4: Create update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 5: Apply triggers
DROP TRIGGER IF EXISTS update_shipping_zones_updated_at ON shipping_zones;
CREATE TRIGGER update_shipping_zones_updated_at
    BEFORE UPDATE ON shipping_zones
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_shipping_methods_updated_at ON shipping_methods;
CREATE TRIGGER update_shipping_methods_updated_at
    BEFORE UPDATE ON shipping_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 6: Insert default shipping zones (only if they don't exist)
DO $$
BEGIN
    -- Insert UK zone if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'United Kingdom') THEN
        INSERT INTO shipping_zones (name, description, countries, is_active) VALUES
        ('United Kingdom', 'Domestic shipping within the UK', '["United Kingdom"]'::jsonb, true);
    END IF;

    -- Insert EU zone if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'European Union') THEN
        INSERT INTO shipping_zones (name, description, countries, is_active) VALUES
        ('European Union', 'Shipping to EU countries', '["Ireland", "France", "Germany", "Spain", "Italy", "Netherlands", "Belgium", "Portugal", "Austria", "Denmark", "Sweden", "Finland", "Poland", "Czech Republic", "Hungary", "Slovakia", "Slovenia", "Croatia", "Estonia", "Latvia", "Lithuania", "Luxembourg", "Malta", "Cyprus", "Bulgaria", "Romania", "Greece"]'::jsonb, true);
    END IF;

    -- Insert Rest of Europe zone if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'Rest of Europe') THEN
        INSERT INTO shipping_zones (name, description, countries, is_active) VALUES
        ('Rest of Europe', 'Shipping to non-EU European countries', '["Norway", "Switzerland"]'::jsonb, true);
    END IF;
END $$;

-- Step 7: Insert UK shipping methods
DO $$
DECLARE
    uk_zone_id UUID;
BEGIN
    -- Get UK zone ID
    SELECT id INTO uk_zone_id FROM shipping_zones WHERE name = 'United Kingdom';

    -- Insert UK methods (only if zone exists and methods don't already exist)
    IF uk_zone_id IS NOT NULL THEN
        INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order) VALUES
        (uk_zone_id, 'Free Standard Shipping', 'Free delivery within 3-5 business days for orders over £50', 0.00, 50.00, 3, 5, 'free', 1),
        (uk_zone_id, 'Standard Shipping', 'Delivery within 3-5 business days', 5.99, NULL, 3, 5, 'standard', 2),
        (uk_zone_id, 'Express Shipping', 'Delivery within 2-3 business days', 9.99, NULL, 2, 3, 'express', 3),
        (uk_zone_id, 'Next Day Delivery', 'Order before 2pm for next day delivery', 14.99, NULL, 1, 1, 'nextDay', 4);
    END IF;
END $$;

-- Step 8: Insert EU shipping methods
DO $$
DECLARE
    eu_zone_id UUID;
BEGIN
    -- Get EU zone ID
    SELECT id INTO eu_zone_id FROM shipping_zones WHERE name = 'European Union';

    -- Insert EU methods
    INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order) VALUES
    (eu_zone_id, 'EU Free Shipping', 'Free delivery within 7-10 business days for orders over £100', 0.00, 100.00, 7, 10, 'free', 1),
    (eu_zone_id, 'EU Standard Shipping', 'Delivery within 7-10 business days', 12.99, NULL, 7, 10, 'standard', 2),
    (eu_zone_id, 'EU Express Shipping', 'Delivery within 5-7 business days', 19.99, NULL, 5, 7, 'express', 3);
END $$;

-- Step 9: Insert Rest of Europe shipping methods
DO $$
DECLARE
    europe_zone_id UUID;
BEGIN
    -- Get Rest of Europe zone ID
    SELECT id INTO europe_zone_id FROM shipping_zones WHERE name = 'Rest of Europe';

    -- Insert Europe methods
    INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order) VALUES
    (europe_zone_id, 'Europe Standard Shipping', 'Delivery within 10-14 business days', 15.99, NULL, 10, 14, 'standard', 1);
END $$;

-- Step 10: Enable RLS (Row Level Security)
ALTER TABLE shipping_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_methods ENABLE ROW LEVEL SECURITY;

-- Step 11: Create policies for admin access
DROP POLICY IF EXISTS "Admin can manage shipping zones" ON shipping_zones;
CREATE POLICY "Admin can manage shipping zones" ON shipping_zones
    FOR ALL USING (true);

DROP POLICY IF EXISTS "Admin can manage shipping methods" ON shipping_methods;
CREATE POLICY "Admin can manage shipping methods" ON shipping_methods
    FOR ALL USING (true);

-- Step 12: Create policies for public read access
DROP POLICY IF EXISTS "Public can read active shipping zones" ON shipping_zones;
CREATE POLICY "Public can read active shipping zones" ON shipping_zones
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Public can read active shipping methods" ON shipping_methods;
CREATE POLICY "Public can read active shipping methods" ON shipping_methods
    FOR SELECT USING (is_active = true);

-- Step 13: Verify the setup
SELECT
    'Setup Complete!' as status,
    (SELECT COUNT(*) FROM shipping_zones) as zones_created,
    (SELECT COUNT(*) FROM shipping_methods) as methods_created;

-- Step 14: Show the created zones and methods
SELECT 'ZONES:' as type, name, array_length(countries::text[], 1) as country_count FROM shipping_zones
UNION ALL
SELECT 'METHODS:' as type, sm.name, CONCAT('£', sm.price, ' (', sm.estimated_days_min, '-', sm.estimated_days_max, ' days)')
FROM shipping_methods sm
JOIN shipping_zones sz ON sm.zone_id = sz.id
ORDER BY type, name;
