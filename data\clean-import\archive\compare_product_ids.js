const fs = require('fs');
const csv = require('csv-parser');

// Input files
const productsFile = 'transformed_products.csv';
const variantsFile = 'transformed_variants_final.csv';

// Store product IDs
const productIds = new Map(); // Map to store product ID and name
const variantProductIds = new Set();
const missingProductIds = new Set();

// Read products file
console.log(`Reading products from ${productsFile}...`);
fs.createReadStream(productsFile)
  .pipe(csv())
  .on('data', (row) => {
    productIds.set(row.id, row.name);
  })
  .on('end', () => {
    console.log(`Found ${productIds.size} products`);
    
    // Read variants file
    console.log(`Reading variants from ${variantsFile}...`);
    fs.createReadStream(variantsFile)
      .pipe(csv())
      .on('data', (row) => {
        variantProductIds.add(row.product_id);
        
        // Check if product ID exists
        if (!productIds.has(row.product_id)) {
          missingProductIds.add(row.product_id);
        }
      })
      .on('end', () => {
        console.log(`Found ${variantProductIds.size} unique product IDs in variants file`);
        
        if (missingProductIds.size > 0) {
          console.log(`\nFound ${missingProductIds.size} product IDs in variants file that don't exist in products file:`);
          missingProductIds.forEach(id => {
            console.log(`- ${id}`);
          });
          
          // Check for similar IDs (case sensitivity, whitespace, etc.)
          console.log('\nChecking for similar IDs...');
          missingProductIds.forEach(id => {
            const similarIds = [];
            productIds.forEach((name, productId) => {
              // Check for case insensitive match
              if (productId.toLowerCase() === id.toLowerCase() && productId !== id) {
                similarIds.push({ productId, name });
              }
              // Check for whitespace differences
              else if (productId.replace(/\s+/g, '') === id.replace(/\s+/g, '') && productId !== id) {
                similarIds.push({ productId, name });
              }
              // Check for similar IDs (Levenshtein distance <= 2)
              else if (levenshteinDistance(productId, id) <= 2) {
                similarIds.push({ productId, name });
              }
            });
            
            if (similarIds.length > 0) {
              console.log(`\nSimilar IDs found for ${id}:`);
              similarIds.forEach(similar => {
                console.log(`- ${similar.productId} (${similar.name})`);
              });
            }
          });
        } else {
          console.log('\nAll product IDs in variants file exist in products file');
        }
        
        // Sample some product IDs from both files for comparison
        console.log('\nSample product IDs from products file:');
        let count = 0;
        productIds.forEach((name, id) => {
          if (count < 5) {
            console.log(`- ${id} (${name})`);
            count++;
          }
        });
        
        console.log('\nSample product IDs from variants file:');
        count = 0;
        variantProductIds.forEach(id => {
          if (count < 5) {
            console.log(`- ${id} (${productIds.get(id) || 'Unknown product'})`);
            count++;
          }
        });
      });
  });

// Levenshtein distance function to find similar strings
function levenshteinDistance(a, b) {
  const matrix = [];
  
  // Initialize matrix
  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }
  
  // Fill matrix
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      if (b.charAt(i - 1) === a.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }
  
  return matrix[b.length][a.length];
}
