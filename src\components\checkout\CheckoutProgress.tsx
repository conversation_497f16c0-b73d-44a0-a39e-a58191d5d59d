import { Check<PERSON><PERSON><PERSON>, MapPin, CreditCard, ShoppingBag } from 'lucide-react';

type CheckoutStep = 'shipping' | 'payment' | 'review';

interface CheckoutProgressProps {
  currentStep: CheckoutStep;
}

export function CheckoutProgress({ currentStep }: CheckoutProgressProps) {
  // Get step status
  const getStepStatus = (step: CheckoutStep) => {
    const order: CheckoutStep[] = ['shipping', 'payment', 'review'];
    const currentIndex = order.indexOf(currentStep);
    const stepIndex = order.indexOf(step);
    
    if (stepIndex < currentIndex) return 'complete';
    if (stepIndex === currentIndex) return 'current';
    return 'upcoming';
  };

  return (
    <div className="mb-10">
      <div className="flex justify-center items-center">
        <div className="w-full max-w-3xl flex items-center">
          {/* Shipping Step */}
          <div className="flex-1 relative">
            <div className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                getStepStatus('shipping') === 'complete' 
                  ? 'bg-sage-500 border-sage-500 text-white' 
                  : getStepStatus('shipping') === 'current' 
                    ? 'bg-sage-500 border-sage-500 text-white' 
                    : 'bg-white border-gray-300 text-gray-500'
              }`}>
                {getStepStatus('shipping') === 'complete' 
                  ? <CheckCircle className="w-5 h-5" /> 
                  : <MapPin className="w-5 h-5" />
                }
              </div>
              <p className={`mt-2 text-sm font-medium ${
                getStepStatus('shipping') === 'complete' 
                  ? 'text-sage-500' 
                  : getStepStatus('shipping') === 'current' 
                    ? 'text-sage-500' 
                    : 'text-gray-500'
              }`}>
                Shipping
              </p>
            </div>
            <div className="absolute top-5 left-1/2 w-full h-0.5 bg-gray-200">
              <div 
                className={`h-full ${getStepStatus('shipping') === 'complete' ? 'bg-sage-500' : 'bg-gray-200'}`} 
                style={{ width: getStepStatus('shipping') === 'complete' ? '100%' : '0%' }}
              ></div>
            </div>
          </div>

          {/* Payment Step */}
          <div className="flex-1 relative">
            <div className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                getStepStatus('payment') === 'complete' 
                  ? 'bg-sage-500 border-sage-500 text-white' 
                  : getStepStatus('payment') === 'current' 
                    ? 'bg-sage-500 border-sage-500 text-white' 
                    : 'bg-white border-gray-300 text-gray-500'
              }`}>
                {getStepStatus('payment') === 'complete' 
                  ? <CheckCircle className="w-5 h-5" /> 
                  : <CreditCard className="w-5 h-5" />
                }
              </div>
              <p className={`mt-2 text-sm font-medium ${
                getStepStatus('payment') === 'complete' 
                  ? 'text-sage-500' 
                  : getStepStatus('payment') === 'current' 
                    ? 'text-sage-500' 
                    : 'text-gray-500'
              }`}>
                Payment
              </p>
            </div>
            <div className="absolute top-5 left-1/2 w-full h-0.5 bg-gray-200">
              <div 
                className={`h-full ${getStepStatus('payment') === 'complete' ? 'bg-sage-500' : 'bg-gray-200'}`} 
                style={{ width: getStepStatus('payment') === 'complete' ? '100%' : '0%' }}
              ></div>
            </div>
          </div>

          {/* Review Step */}
          <div className="flex-1">
            <div className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                getStepStatus('review') === 'complete' 
                  ? 'bg-sage-500 border-sage-500 text-white' 
                  : getStepStatus('review') === 'current' 
                    ? 'bg-sage-500 border-sage-500 text-white' 
                    : 'bg-white border-gray-300 text-gray-500'
              }`}>
                {getStepStatus('review') === 'complete' 
                  ? <CheckCircle className="w-5 h-5" /> 
                  : <ShoppingBag className="w-5 h-5" />
                }
              </div>
              <p className={`mt-2 text-sm font-medium ${
                getStepStatus('review') === 'complete' 
                  ? 'text-sage-500' 
                  : getStepStatus('review') === 'current' 
                    ? 'text-sage-500' 
                    : 'text-gray-500'
              }`}>
                Review
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
