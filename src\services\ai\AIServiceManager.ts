/**
 * AI Service Manager - Safe Rollout Controller
 *
 * Manages the transition from legacy AI systems to the unified AI system
 * with feature flags, fallbacks, and gradual rollout capabilities
 */

import { UnifiedAIService } from './core/UnifiedAIService';
import { AIRequest, AIRequestType } from './types/AIRequest';
import { AIResponse } from './types/AIResponse';

interface FeatureFlags {
  useUnifiedProductAI: boolean;
  useUnifiedBlogAI: boolean;
  useUnifiedNewsletterAI: boolean;
  useUnifiedSocialAI: boolean;
  useUnifiedImageSearch: boolean;
  enableCostOptimization: boolean;
  enableAutoFallback: boolean;
}

interface LegacyAIService {
  generateProductDescription(product: any): Promise<string>;
  generateBlogContent(topic: string, category: string): Promise<string>;
  generateNewsletterContent(template: string): Promise<string>;
  findProductImages(product: any): Promise<any[]>;
}

export class AIServiceManager {
  private unifiedAI: UnifiedAIService | null = null;
  private legacyAI: LegacyAIService | null = null;
  private featureFlags: FeatureFlags;
  private isInitialized: boolean = false;

  constructor() {
    // Start with all flags OFF for safety
    this.featureFlags = {
      useUnifiedProductAI: false,
      useUnifiedBlogAI: false,
      useUnifiedNewsletterAI: false,
      useUnifiedSocialAI: true, // New feature, safe to enable
      useUnifiedImageSearch: false,
      enableCostOptimization: true,
      enableAutoFallback: true
    };
  }

  /**
   * Initialize the AI services
   */
  async initialize(config: {
    deepseek_key?: string;
    gemini_key?: string;
    openrouter_key?: string;
  }): Promise<void> {
    try {
      // Initialize unified AI service
      this.unifiedAI = new UnifiedAIService({
        deepseek: {
          api_key: config.deepseek_key || '',
          enabled: !!config.deepseek_key
        },
        gemini: {
          api_key: config.gemini_key || '',
          enabled: !!config.gemini_key
        },
        openrouter: {
          api_key: config.openrouter_key || '',
          enabled: !!config.openrouter_key
        },
        default_provider: 'deepseek',
        cost_optimization: this.featureFlags.enableCostOptimization,
        auto_fallback: this.featureFlags.enableAutoFallback
      });

      // Test the unified service
      await this.testUnifiedService();

      this.isInitialized = true;
      console.log('✅ AIServiceManager initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize AIServiceManager:', error);
      this.isInitialized = false;
    }
  }

  /**
   * Generate product description with smart routing
   */
  async generateProductDescription(product: {
    name: string;
    category?: string;
    id?: string;
  }): Promise<string> {
    if (this.featureFlags.useUnifiedProductAI && this.unifiedAI && this.isInitialized) {
      try {
        const request: AIRequest = {
          type: 'product_description',
          content: `Product: ${product.name}`,
          context: {
            business_type: 'cannabis',
            category: product.category,
            brand_voice: {
              tone: 'professional',
              personality: 'Expert, trustworthy, and compliant with regulations'
            },
            format: 'html'
          },
          fallback_enabled: true
        };

        const response = await this.unifiedAI.processRequest(request);

        if (response.success && response.content) {
          console.log(`✅ Generated product description using ${response.provider}`);
          return response.content;
        } else {
          throw new Error(response.error?.message || 'Unified AI failed');
        }

      } catch (error) {
        console.warn('⚠️ Unified AI failed, falling back to legacy:', error);
        // Fall through to legacy system
      }
    }

    // Use legacy system (your existing implementation)
    return this.useLegacyProductDescription(product);
  }

  /**
   * Generate blog content with smart routing
   */
  async generateBlogContent(topic: string, category: string): Promise<string> {
    if (this.featureFlags.useUnifiedBlogAI && this.unifiedAI && this.isInitialized) {
      try {
        const request: AIRequest = {
          type: 'blog_content',
          content: topic,
          context: {
            business_type: 'cannabis',
            category: category,
            brand_voice: {
              tone: 'professional',
              personality: 'Educational, informative, and engaging'
            },
            format: 'html',
            max_length: 2000
          },
          provider: 'gemini', // Prefer Gemini for creative content
          fallback_enabled: true
        };

        const response = await this.unifiedAI.processRequest(request);

        if (response.success && response.content) {
          console.log(`✅ Generated blog content using ${response.provider}`);
          return response.content;
        } else {
          throw new Error(response.error?.message || 'Unified AI failed');
        }

      } catch (error) {
        console.warn('⚠️ Unified AI failed, falling back to legacy:', error);
        // Fall through to legacy system
      }
    }

    // Use legacy system
    return this.useLegacyBlogGeneration(topic, category);
  }

  /**
   * Generate social media content (NEW FEATURE)
   */
  async generateSocialMediaContent(product: any, platform: string): Promise<{
    caption: string;
    hashtags: string[];
    image_suggestions: string[];
  }> {
    if (!this.unifiedAI || !this.isInitialized) {
      throw new Error('Unified AI not available for social media generation');
    }

    const request: AIRequest = {
      type: 'social_media_post',
      content: `Create ${platform} post for product: ${product.name}. Description: ${product.description || 'Premium cannabis product'}. Category: ${product.category || 'cannabis'}. Price: £${product.price || 'N/A'}.`,
      context: {
        business_type: 'cannabis',
        category: product.category,
        product_name: product.name,
        product_description: product.description,
        product_price: product.price,
        brand_voice: {
          tone: 'friendly',
          personality: 'Engaging, compliant, and community-focused'
        },
        target_audience: 'cannabis enthusiasts',
        format: 'json'
      },
      provider: 'deepseek', // Fast and cost-effective for social media
      fallback_enabled: true
    };

    const response = await this.unifiedAI.processRequest(request);

    if (response.success && response.content) {
      console.log(`✅ Generated ${platform} content using ${response.provider}`);

      // Try to parse JSON response first
      try {
        const parsedContent = JSON.parse(response.content);
        return {
          caption: parsedContent.caption || response.content,
          hashtags: parsedContent.hashtags || [`#${product.category}`, '#cannabis', '#quality', '#premium'],
          image_suggestions: parsedContent.image_suggestions || [
            `${product.name} product shot`,
            `Lifestyle image with ${product.name}`,
            `${product.category} collection showcase`
          ]
        };
      } catch (parseError) {
        // If JSON parsing fails, use the content as caption
        return {
          caption: response.content,
          hashtags: [`#${product.category}`, '#cannabis', '#quality', '#premium', `#${product.name.replace(/\s+/g, '')}`],
          image_suggestions: [
            `${product.name} product shot`,
            `Lifestyle image with ${product.name}`,
            `${product.category} collection showcase`
          ]
        };
      }
    }

    throw new Error('Failed to generate social media content');
  }

  /**
   * Get AI system status and recommendations
   */
  async getSystemStatus(): Promise<{
    unified_ai_available: boolean;
    active_features: string[];
    cost_savings: number;
    recommendations: string[];
  }> {
    const activeFeatures: string[] = [];
    let costSavings = 0;
    const recommendations: string[] = [];

    // Check which features are active
    if (this.featureFlags.useUnifiedProductAI) activeFeatures.push('Product AI');
    if (this.featureFlags.useUnifiedBlogAI) activeFeatures.push('Blog AI');
    if (this.featureFlags.useUnifiedNewsletterAI) activeFeatures.push('Newsletter AI');
    if (this.featureFlags.useUnifiedSocialAI) activeFeatures.push('Social Media AI');

    // Calculate cost savings (rough estimate)
    if (this.unifiedAI && this.isInitialized) {
      const stats = await this.unifiedAI.getUsageStats();
      costSavings = this.calculateCostSavings(stats);
    }

    // Generate recommendations
    if (!this.featureFlags.useUnifiedProductAI) {
      recommendations.push('Enable unified Product AI to reduce costs and improve quality');
    }

    if (activeFeatures.length === 0) {
      recommendations.push('Consider enabling unified AI features for cost savings and better performance');
    }

    return {
      unified_ai_available: this.isInitialized,
      active_features: activeFeatures,
      cost_savings: costSavings,
      recommendations
    };
  }

  /**
   * Enable/disable feature flags (for gradual rollout)
   */
  updateFeatureFlags(flags: Partial<FeatureFlags>): void {
    this.featureFlags = { ...this.featureFlags, ...flags };
    console.log('🔄 Feature flags updated:', this.featureFlags);
  }

  /**
   * Get current feature flags
   */
  getFeatureFlags(): FeatureFlags {
    return { ...this.featureFlags };
  }

  private async testUnifiedService(): Promise<void> {
    if (!this.unifiedAI) {
      throw new Error('Unified AI not initialized');
    }

    // Simple test request
    const testRequest: AIRequest = {
      type: 'product_description',
      content: 'Test product',
      context: { business_type: 'cannabis' }
    };

    const response = await this.unifiedAI.processRequest(testRequest);

    if (!response.success) {
      throw new Error(`Unified AI test failed: ${response.error?.message}`);
    }

    console.log('✅ Unified AI test passed');
  }

  private async useLegacyProductDescription(product: any): Promise<string> {
    // This would call your existing product AI implementation
    // For now, return a placeholder
    console.log('🔄 Using legacy product description generation');

    return `<p>${product.name} - A high-quality product designed for optimal performance and user satisfaction. ${product.category ? `This premium ${product.category} product offers exceptional value and reliability.` : ''} Perfect for those seeking quality and durability.</p>`;
  }

  private async useLegacyBlogGeneration(topic: string, category: string): Promise<string> {
    // This would call your existing blog AI implementation
    console.log('🔄 Using legacy blog generation');

    return `<h2>${topic}</h2><p>This is a comprehensive guide about ${topic} in the ${category} category. Content would be generated by your existing Gemini implementation.</p>`;
  }

  private calculateCostSavings(stats: any): number {
    // Rough calculation of cost savings vs legacy approach
    // In production, you'd have more detailed cost tracking

    const legacyCostPerRequest = 0.005; // Estimated legacy cost
    const unifiedCostPerRequest = stats.total_cost / Math.max(stats.total_requests, 1);

    const savings = (legacyCostPerRequest - unifiedCostPerRequest) * stats.total_requests;
    return Math.max(0, savings);
  }
}

// Export singleton instance
export const aiServiceManager = new AIServiceManager();
