/**
 * Chat Interface Component
 *
 * This file implements the React components for the Cheech chatbot interface,
 * including the floating chat button and chat window.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Message, Product, ChatResponse } from './types';

// Chat Button Component
interface ChatButtonProps {
  onClick: () => void;
  isOpen: boolean;
}

const ChatButton: React.FC<ChatButtonProps> = ({ onClick, isOpen }) => {
  return (
    <button
      className={`chat-button ${isOpen ? 'chat-button-open' : ''}`}
      onClick={onClick}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
    >
      {isOpen ? (
        <span className="close-icon">×</span>
      ) : (
        <div className="chat-icon">
          <img src="/assets/cheech-icon.png" alt="Cheech" />
        </div>
      )}
    </button>
  );
};

// Message Bubble Component
interface MessageBubbleProps {
  message: Message;
  products?: Product[];
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, products }) => {
  const isUser = message.role === 'user';

  // Function to render message content with markdown-like formatting
  const renderContent = (content: string) => {
    // Replace **text** with bold
    const boldFormatted = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Replace links
    const linkFormatted = boldFormatted.replace(
      /\[(.*?)\]\((.*?)\)/g,
      '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>'
    );

    // Replace newlines with <br>
    const newlineFormatted = linkFormatted.replace(/\n/g, '<br>');

    return <div dangerouslySetInnerHTML={{ __html: newlineFormatted }} />;
  };

  return (
    <div className={`message-bubble ${isUser ? 'user-message' : 'assistant-message'}`}>
      {!isUser && (
        <div className="avatar">
          <img src="/assets/cheech-avatar.png" alt="Cheech" />
        </div>
      )}
      <div className="message-content">
        {renderContent(message.content)}

        {/* Render product cards if available */}
        {!isUser && products && products.length > 0 && (
          <div className="product-cards">
            {products.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
      <div className="message-timestamp">
        {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>
    </div>
  );
};

// Product Card Component
interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  return (
    <div className="product-card">
      <div className="product-image">
        <img src={product.imageUrl} alt={product.name} />
      </div>
      <div className="product-info">
        <h3>{product.name}</h3>
        <p className="product-price">${product.price.toFixed(2)}</p>
        {product.rating && (
          <div className="product-rating">
            {Array.from({ length: 5 }).map((_, i) => (
              <span
                key={i}
                className={`star ${i < Math.floor(product.rating!) ? 'filled' : ''}`}
              >
                ★
              </span>
            ))}
            <span className="rating-count">({product.reviewCount})</span>
          </div>
        )}
        <button className="view-product-button">View Product</button>
      </div>
    </div>
  );
};

// Suggestion Chips Component
interface SuggestionChipsProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
}

const SuggestionChips: React.FC<SuggestionChipsProps> = ({
  suggestions,
  onSuggestionClick
}) => {
  return (
    <div className="suggestion-chips">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="suggestion-chip"
          onClick={() => onSuggestionClick(suggestion)}
        >
          {suggestion}
        </button>
      ))}
    </div>
  );
};

// Typing Indicator Component
const TypingIndicator: React.FC = () => {
  return (
    <div className="typing-indicator">
      <span></span>
      <span></span>
      <span></span>
    </div>
  );
};

// Main Chat Interface Component
interface ChatInterfaceProps {
  sessionId: string;
  onSendMessage: (message: string) => Promise<ChatResponse>;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  sessionId,
  onSendMessage
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [products, setProducts] = useState<Record<string, Product[]>>({});
  const [audioEnabled, setAudioEnabled] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Add welcome message and audio on first open
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        role: 'assistant',
        content: "Hey there! I'm Cheech, your BitsnBongs product expert. How can I help you today?",
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);
      setSuggestions([
        'Show popular products',
        'Help me find something',
        'How do I clean my bong?'
      ]);

      // Play welcome audio
      playWelcomeAudio();
    }
  }, [isOpen, messages.length]);

  // Play Cheech's welcome audio message
  const playWelcomeAudio = () => {
    if (!audioEnabled) return;

    try {
      const audio = new Audio('/audio/hey-welcom-to-bits-n-bongs-how-can-i-help-you-man-101soundboards.mp3');
      audio.volume = 0.7; // Set volume to 70%
      audio.play().catch(error => {
        console.log('Audio autoplay prevented by browser:', error);
        // Browsers often block autoplay, this is normal
      });
    } catch (error) {
      console.error('Error playing welcome audio:', error);
    }
  };

  // Toggle audio on/off
  const toggleAudio = () => {
    setAudioEnabled(!audioEnabled);
  };

  // Play notification sound for new messages (optional)
  const playNotificationSound = () => {
    if (!audioEnabled) return;

    try {
      // You can add a subtle notification sound here
      // const audio = new Audio('/audio/notification.mp3');
      // audio.volume = 0.3;
      // audio.play().catch(() => {});
    } catch (error) {
      console.error('Error playing notification sound:', error);
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Toggle chat open/closed
  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setSuggestions([]);

    try {
      // Get response from chatbot
      const response = await onSendMessage(inputValue);

      // Add assistant message
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: response.message,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Update suggestions
      if (response.suggestions) {
        setSuggestions(response.suggestions);
      }

      // Store products if available
      if (response.products) {
        setProducts(prev => ({
          ...prev,
          [assistantMessage.id]: response.products || []
        }));
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: "I'm sorry, but I encountered an error. Please try again.",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Handle pressing Enter to send
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle clicking a suggestion
  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    handleSendMessage();
  };

  return (
    <div className="chat-container">
      <ChatButton onClick={toggleChat} isOpen={isOpen} />

      {isOpen && (
        <div className="chat-window">
          <div className="chat-header">
            <div className="chat-title">
              <img src="/assets/cheech-avatar.png" alt="Cheech" className="header-avatar" />
              <h2>Chat with Cheech</h2>
            </div>
            <div className="header-controls">
              <button
                className="audio-button"
                onClick={playWelcomeAudio}
                aria-label="Play welcome message"
                title="Play Cheech's welcome message"
              >
                🔊
              </button>
              <button
                className={`audio-toggle ${audioEnabled ? 'enabled' : 'disabled'}`}
                onClick={toggleAudio}
                aria-label={audioEnabled ? 'Disable audio' : 'Enable audio'}
                title={audioEnabled ? 'Disable audio' : 'Enable audio'}
              >
                {audioEnabled ? '🔈' : '🔇'}
              </button>
              <button
                className="close-button"
                onClick={toggleChat}
                aria-label="Close chat"
              >
                ×
              </button>
            </div>
          </div>

          <div className="chat-messages">
            {messages.map(message => (
              <MessageBubble
                key={message.id}
                message={message}
                products={products[message.id]}
              />
            ))}

            {isTyping && (
              <div className="message-bubble assistant-message">
                <div className="avatar">
                  <img src="/assets/cheech-avatar.png" alt="Cheech" />
                </div>
                <div className="message-content">
                  <TypingIndicator />
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {suggestions.length > 0 && (
            <SuggestionChips
              suggestions={suggestions}
              onSuggestionClick={handleSuggestionClick}
            />
          )}

          <div className="chat-input">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              rows={1}
            />
            <button
              className="send-button"
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isTyping}
              aria-label="Send message"
            >
              <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
