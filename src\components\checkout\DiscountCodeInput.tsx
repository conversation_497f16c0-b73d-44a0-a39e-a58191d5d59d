import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, Tag, CheckCircle, XCircle } from 'lucide-react';
import { validateDiscountCode } from '@/services/discountService';
import { AppliedDiscount } from '@/types/discount';

interface DiscountCodeInputProps {
  orderTotal: number;
  onApplyDiscount: (discount: AppliedDiscount) => void;
  onRemoveDiscount: () => void;
  appliedDiscount: AppliedDiscount | null;
  disabled?: boolean;
}

export function DiscountCodeInput({
  orderTotal,
  onApplyDiscount,
  onRemoveDiscount,
  appliedDiscount,
  disabled = false
}: DiscountCodeInputProps) {
  const [discountCode, setDiscountCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApplyDiscount = async () => {
    if (!discountCode.trim()) {
      setError('Please enter a discount code');
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const result = await validateDiscountCode(discountCode, orderTotal);
      
      if (result.isValid && result.discount) {
        onApplyDiscount(result.discount);
        setDiscountCode('');
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error validating discount code:', err);
      setError('An error occurred while validating the discount code');
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveDiscount = () => {
    onRemoveDiscount();
    setDiscountCode('');
    setError(null);
  };

  if (appliedDiscount) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            <div>
              <p className="text-sm font-medium">
                Code <span className="font-bold">{appliedDiscount.code}</span> applied
              </p>
              <p className="text-xs text-gray-500">
                {appliedDiscount.discountType === 'percentage' 
                  ? `${appliedDiscount.discountValue}% off`
                  : `£${appliedDiscount.discountValue.toFixed(2)} off`}
              </p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleRemoveDiscount}
            disabled={disabled}
            className="h-8 text-gray-500 hover:text-red-500"
          >
            Remove
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-2">
      <div className="flex items-center space-x-2">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Tag className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Enter discount code"
            value={discountCode}
            onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}
            className="pl-10 uppercase"
            disabled={isValidating || disabled}
          />
        </div>
        <Button
          onClick={handleApplyDiscount}
          disabled={isValidating || !discountCode.trim() || disabled}
          className="whitespace-nowrap"
        >
          {isValidating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Applying...
            </>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
      
      {error && (
        <div className="flex items-center text-red-500 text-sm">
          <XCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
}
