# AI-Assisted Newsletter and Social Media Guide

This guide outlines the implementation plan for adding AI-assisted content creation capabilities to the newsletter system and creating a social media post generator for Buds N Bongs Boutique.

## Table of Contents

1. [Overview](#overview)
2. [Current State Analysis](#current-state-analysis)
3. [Implementation Plan](#implementation-plan)
4. [Newsletter System Enhancement](#newsletter-system-enhancement)
5. [Social Media Post Generator](#social-media-post-generator)
6. [Technical Considerations](#technical-considerations)
7. [Resources and References](#resources-and-references)

## Overview

The goal is to leverage the existing AI integration (Gemini and DeepSeek) to enhance the newsletter system with AI-assisted content creation and to build a social media post generator. This will streamline marketing efforts and ensure consistent, engaging content across all channels.

## Current State Analysis

### Newsletter System
- Basic newsletter subscription system is implemented
- Users can subscribe via the website footer
- <PERSON><PERSON> can view and manage subscribers in the NewsletterPage
- The "Send Newsletter" button exists but is marked as "Coming Soon"
- No content creation or template system exists yet

### AI Implementation
- Robust AI integration with both Gemini and DeepSeek APIs
- Blog editor has sophisticated AI content generation capabilities
- AI can generate blog titles, content, and help find images
- The system supports different content types, tones, and lengths

### Social Media
- Basic social media sharing exists for blog posts
- No dedicated social media content creation system yet

## Implementation Plan

### Phase 1: Newsletter Editor (2-3 days)
1. Create newsletter database table for tracking sends
2. Implement the NewsletterEditorPage component
3. Add newsletter-specific prompts to the AI library
4. Connect the "Send Newsletter" button in the existing NewsletterPage

### Phase 2: Social Media Generator (2-3 days)
1. Create the SocialMediaPage component
2. Add social media-specific prompts to the AI library
3. Implement clipboard functionality for easy copying
4. Add scheduling capabilities (optional)

### Phase 3: Integration and Enhancement (3-4 days)
1. Connect newsletter content with product promotions
2. Add analytics tracking for newsletter opens/clicks
3. Implement A/B testing for different AI-generated content
4. Add image generation for social media posts

## Newsletter System Enhancement

### 1. Database Schema Updates

Add a new table to track newsletter sends:

```sql
CREATE TABLE newsletter_sends (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  html_content TEXT,
  recipient_count INTEGER NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);
```

### 2. Newsletter Editor Component

Create a new page at `src/pages/admin/NewsletterEditorPage.tsx` that includes:

- Subject line input
- Content editor with markdown support
- AI generation panel with topic/prompt input
- Preview panel showing HTML and plain text versions
- Send button with recipient selection

### 3. AI Prompt Templates for Newsletters

Add newsletter-specific prompt templates to the AI library in `src/lib/ai.ts`:

```typescript
// In the createPrompt function
if (contentType === 'newsletter') {
  return `Create an engaging email newsletter about ${topic} for a CBD/cannabis e-commerce store.

  Requirements:
  - Word count: ${wordCount} words
  - Tone: ${tone}
  - Include a compelling subject line suggestion
  - Start with a friendly greeting
  - Include 2-3 main sections about products, news, or tips
  - Add a special offer or promotion section
  - End with a clear call-to-action
  - Use simple HTML formatting that works well in emails
  
  The newsletter should be informative, engaging, and drive customers to visit our online store.`;
}
```

### 4. Newsletter Sending Service

Implement a service to send newsletters to subscribers in `src/integrations/supabase/newsletter.ts`:

```typescript
/**
 * Send a newsletter to subscribers
 */
export async function sendNewsletter(
  subject: string,
  htmlContent: string,
  textContent: string,
  recipientIds?: string[]
) {
  try {
    // Get subscribers to send to
    let query = supabase
      .from('newsletter_subscribers')
      .select('id, email, first_name')
      .eq('is_active', true);
    
    // If specific recipients are provided, filter by those IDs
    if (recipientIds && recipientIds.length > 0) {
      query = query.in('id', recipientIds);
    }
    
    const { data: subscribers, error } = await query;
    
    if (error) {
      console.error('Error fetching subscribers:', error);
      return { error: 'Failed to fetch subscribers' };
    }
    
    if (!subscribers || subscribers.length === 0) {
      return { error: 'No active subscribers found' };
    }
    
    // Call your email sending service (e.g., SendGrid, Mailchimp, etc.)
    // This is a placeholder - you'll need to implement the actual email sending
    const emailResult = await sendBulkEmail({
      subject,
      htmlContent,
      textContent,
      recipients: subscribers.map(sub => ({
        email: sub.email,
        name: sub.first_name || undefined
      }))
    });
    
    // Log the newsletter send in the database
    await supabase
      .from('newsletter_sends')
      .insert({
        subject,
        content: textContent,
        html_content: htmlContent,
        recipient_count: subscribers.length,
        sent_at: new Date().toISOString(),
        status: emailResult.success ? 'sent' : 'failed'
      });
    
    return { 
      success: emailResult.success, 
      recipientCount: subscribers.length 
    };
  } catch (error) {
    console.error('Error sending newsletter:', error);
    return { error: 'An unexpected error occurred' };
  }
}
```

## Social Media Post Generator

### 1. Social Media Post Generator Component

Create a new page at `src/pages/admin/SocialMediaPage.tsx` that includes:

- Topic/theme input
- Product name input (optional)
- AI provider selection
- Platform-specific post generation (Twitter, Facebook, Instagram)
- Copy to clipboard functionality
- Post scheduling (optional)

### 2. AI Prompt Templates for Social Media

Add social media-specific prompt templates to the AI library in `src/lib/ai.ts`:

```typescript
// In the createPrompt function
if (contentType === 'social') {
  const platformSpecific = options.platform === 'twitter' 
    ? 'Keep it under 280 characters and include 1-2 relevant hashtags.'
    : options.platform === 'instagram'
    ? 'Include 5-7 relevant hashtags at the end. Make it visually descriptive.'
    : options.platform === 'facebook'
    ? 'Write 2-3 paragraphs with an engaging question to encourage comments.'
    : '';
    
  return `Create an engaging ${options.platform} post about ${topic} for a CBD/cannabis e-commerce store.
  
  Requirements:
  - Tone: ${tone}
  - ${platformSpecific}
  - Include a clear call-to-action
  - Be conversational and authentic
  - Highlight benefits or unique selling points
  
  ${options.productName ? `Feature this product: ${options.productName}` : ''}`;
}
```

## Technical Considerations

### Email Delivery Service

You'll need to integrate with an email delivery service. Options include:

1. **SendGrid**
   - Reliable delivery with good analytics
   - Free tier available (100 emails/day)
   - Easy to integrate with Node.js

2. **Mailchimp**
   - Full-featured marketing platform
   - Template builder and campaign management
   - More expensive but comprehensive

3. **AWS SES (Simple Email Service)**
   - Very cost-effective for high volume
   - Requires more setup but highly reliable
   - Good for transactional emails

### Social Media API Integration

For direct posting to social media platforms, consider:

1. **Buffer API**
   - Schedule posts across multiple platforms
   - Analytics and reporting
   - Easier than direct platform APIs

2. **Platform-specific APIs**
   - Twitter API (requires developer account)
   - Facebook Graph API (requires app approval)
   - Instagram Graph API (requires business account)

## Resources and References

- [Gemini API Documentation](https://ai.google.dev/docs)
- [DeepSeek API Documentation](https://platform.deepseek.com/docs)
- [SendGrid API Documentation](https://docs.sendgrid.com/api-reference)
- [Mailchimp API Documentation](https://mailchimp.com/developer/marketing/api/)
- [Buffer API Documentation](https://buffer.com/developers/api)
