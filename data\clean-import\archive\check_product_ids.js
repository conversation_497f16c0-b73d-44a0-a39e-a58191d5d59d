const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input files
const productsFile = 'transformed_products.csv';
const variantsFile = 'transformed_variants_all_prices.csv';
const outputFile = 'transformed_variants_fixed_ids.csv';

// Store product IDs
const productIds = new Set();
const variantProductIds = new Set();
const missingProductIds = new Set();

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Read products file
console.log(`Reading products from ${productsFile}...`);
fs.createReadStream(productsFile)
  .pipe(csv())
  .on('data', (row) => {
    productIds.add(row.id);
  })
  .on('end', () => {
    console.log(`Found ${productIds.size} products`);

    // Read variants file
    console.log(`Reading variants from ${variantsFile}...`);
    const validVariants = [];

    fs.createReadStream(variantsFile)
      .pipe(csv())
      .on('data', (row) => {
        variantProductIds.add(row.product_id);

        // Check if product ID exists
        if (!productIds.has(row.product_id)) {
          missingProductIds.add(row.product_id);
        } else {
          validVariants.push(row);
        }
      })
      .on('end', async () => {
        console.log(`Found ${variantProductIds.size} unique product IDs in variants file`);
        console.log(`Found ${missingProductIds.size} product IDs in variants file that don't exist in products file`);

        if (missingProductIds.size > 0) {
          console.log('\nMissing product IDs:');
          missingProductIds.forEach(id => {
            console.log(id);
          });
        }

        // Write valid variants to new file
        console.log(`\nWriting ${validVariants.length} valid variants to ${outputFile}...`);
        await csvWriter.writeRecords(validVariants);
        console.log(`Created new variants file: ${outputFile}`);
      });
  });
