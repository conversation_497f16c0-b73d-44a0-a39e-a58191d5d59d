"""
Dutch Passion Rich Media Extractor

This script extracts rich media elements from Dutch Passion product pages:
- Terpene profile charts and percentages
- Cannabinoid content indicators
- Product icons and badges
- Product images
"""

import requests
import json
import re
import os
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# Base URL
BASE_URL = "https://dutch-passion.com"
SEEDS_URL = "https://dutch-passion.com/en/cannabis-seeds"

# Headers to mimic a browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Connection": "keep-alive",
    "Referer": "https://dutch-passion.com/",
    "DNT": "1",  # Do Not Track
}

# Create directories for media
def create_directories():
    os.makedirs("dutch_passion_media/icons", exist_ok=True)
    os.makedirs("dutch_passion_media/product_images", exist_ok=True)
    os.makedirs("dutch_passion_media/terpene_charts", exist_ok=True)
    os.makedirs("dutch_passion_media/cannabinoid_charts", exist_ok=True)

# Function to get page content with age verification bypass
def get_page_content(url):
    print(f"Fetching content from: {url}")
    
    # First request to get cookies
    session = requests.Session()
    response = session.get(url, headers=HEADERS)
    
    # Check if we need to handle age verification
    if "ARE YOU AGED 18 OR OVER?" in response.text:
        print("Handling age verification...")
        
        # Extract any necessary tokens or form data
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Simulate clicking the "Enter" button by making a POST request
        verification_url = urljoin(BASE_URL, "/en/age-verification")
        verification_data = {
            "age_verification": "true",
            "redirect": "/en/cannabis-seeds"
        }
        
        # Send POST request to handle age verification
        response = session.post(verification_url, data=verification_data, headers=HEADERS)
        
        # Get the main page again after verification
        response = session.get(url, headers=HEADERS)
    
    return response.text, session

# Function to download an image
def download_image(session, url, save_path):
    try:
        response = session.get(url, headers=HEADERS)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            print(f"Failed to download image: {url}, status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading image {url}: {e}")
        return False

# Function to extract filename from URL
def get_filename_from_url(url):
    parsed_url = urlparse(url)
    path = parsed_url.path
    filename = os.path.basename(path)
    return filename

# Function to extract terpene profiles
def extract_terpene_profiles(soup, product_id, session):
    terpene_data = {}
    
    # Look for terpene chart
    terpene_chart = soup.select('.terpene-chart, .cannabinoid-profile, .terpenes-section')
    
    if terpene_chart:
        # Extract terpene percentages
        terpene_items = soup.select('.terpene-item, .terpene-percentage, .cannabinoid-item')
        
        for item in terpene_items:
            try:
                name_elem = item.select_one('.terpene-name, .cannabinoid-name, .name')
                value_elem = item.select_one('.terpene-value, .cannabinoid-value, .percentage, .value')
                
                if name_elem and value_elem:
                    name = name_elem.text.strip()
                    value = value_elem.text.strip()
                    
                    # Try to extract percentage
                    percentage_match = re.search(r'(\d+(?:\.\d+)?)%', value)
                    if percentage_match:
                        value = percentage_match.group(1) + '%'
                    
                    terpene_data[name] = value
            except Exception as e:
                print(f"Error extracting terpene item: {e}")
        
        # Try to find and download terpene chart image
        chart_img = soup.select_one('.terpene-chart img, .cannabinoid-profile img, .terpenes-section img')
        if chart_img:
            img_url = chart_img.get('src') or chart_img.get('data-src')
            if img_url:
                if not img_url.startswith('http'):
                    img_url = urljoin(BASE_URL, img_url)
                
                filename = f"terpene_chart_{product_id}.jpg"
                save_path = os.path.join("dutch_passion_media/terpene_charts", filename)
                
                if download_image(session, img_url, save_path):
                    terpene_data['chart_image'] = save_path
    
    return terpene_data

# Function to extract cannabinoid profiles
def extract_cannabinoid_profiles(soup, product_id, session):
    cannabinoid_data = {}
    
    # Look for cannabinoid information
    cannabinoid_sections = soup.select('.cannabinoid-section, .thc-section, .cbd-section')
    
    for section in cannabinoid_sections:
        try:
            # Extract cannabinoid type and value
            type_elem = section.select_one('.cannabinoid-type, .type, h3, h4')
            value_elem = section.select_one('.cannabinoid-value, .value, .percentage')
            
            if type_elem and value_elem:
                type_name = type_elem.text.strip()
                value = value_elem.text.strip()
                
                # Try to extract percentage
                percentage_match = re.search(r'(\d+(?:\.\d+)?)%', value)
                if percentage_match:
                    value = percentage_match.group(1) + '%'
                
                cannabinoid_data[type_name] = value
        except Exception as e:
            print(f"Error extracting cannabinoid section: {e}")
    
    # Look for THC/CBD meter or chart
    meter_img = soup.select_one('.thc-meter img, .cannabinoid-meter img, .thc-chart img')
    if meter_img:
        img_url = meter_img.get('src') or meter_img.get('data-src')
        if img_url:
            if not img_url.startswith('http'):
                img_url = urljoin(BASE_URL, img_url)
            
            filename = f"cannabinoid_chart_{product_id}.jpg"
            save_path = os.path.join("dutch_passion_media/cannabinoid_charts", filename)
            
            if download_image(session, img_url, save_path):
                cannabinoid_data['chart_image'] = save_path
    
    # If no specific cannabinoid data found, try to extract from text
    if not cannabinoid_data:
        # Look for THC/CBD mentions in product description
        description = soup.select_one('#description, .product-description')
        if description:
            text = description.text.lower()
            
            # Extract THC percentage
            thc_match = re.search(r'thc[^\d]*(\d+(?:\.\d+)?)%', text)
            if thc_match:
                cannabinoid_data['THC'] = thc_match.group(1) + '%'
            
            # Extract CBD percentage
            cbd_match = re.search(r'cbd[^\d]*(\d+(?:\.\d+)?)%', text)
            if cbd_match:
                cannabinoid_data['CBD'] = cbd_match.group(1) + '%'
    
    return cannabinoid_data

# Function to extract product icons
def extract_product_icons(soup, product_id, session):
    icons_data = {}
    
    # Look for product characteristic icons
    icon_sections = soup.select('.product-characteristics, .product-icons, .product-features')
    
    for section in icon_sections:
        try:
            icon_items = section.select('li, .icon-item, .characteristic-item')
            
            for i, item in enumerate(icon_items):
                icon_name = ""
                icon_value = ""
                icon_image = None
                
                # Try to get icon name
                name_elem = item.select_one('.icon-name, .characteristic-name, .name, span')
                if name_elem:
                    icon_name = name_elem.text.strip()
                
                # Try to get icon value
                value_elem = item.select_one('.icon-value, .characteristic-value, .value')
                if value_elem:
                    icon_value = value_elem.text.strip()
                
                # Try to get icon image
                img_elem = item.select_one('img')
                if img_elem:
                    img_url = img_elem.get('src') or img_elem.get('data-src')
                    if img_url:
                        if not img_url.startswith('http'):
                            img_url = urljoin(BASE_URL, img_url)
                        
                        # Use icon name for filename if available, otherwise use index
                        if icon_name:
                            safe_name = re.sub(r'[^\w\-_]', '_', icon_name.lower())
                            filename = f"icon_{safe_name}_{product_id}.png"
                        else:
                            filename = f"icon_{i}_{product_id}.png"
                        
                        save_path = os.path.join("dutch_passion_media/icons", filename)
                        
                        if download_image(session, img_url, save_path):
                            icon_image = save_path
                
                # Add to icons data
                if icon_name or icon_value or icon_image:
                    icon_key = icon_name if icon_name else f"icon_{i}"
                    icons_data[icon_key] = {
                        "name": icon_name,
                        "value": icon_value,
                        "image": icon_image
                    }
        except Exception as e:
            print(f"Error extracting icon section: {e}")
    
    return icons_data

# Function to extract product images
def extract_product_images(soup, product_id, session):
    images_data = []
    
    # Look for product images
    image_containers = soup.select('.product-images, .product-gallery, .images-container')
    
    if not image_containers:
        # If no specific container found, look for images directly
        image_containers = [soup]
    
    for container in image_containers:
        try:
            # Find all product images
            img_elements = container.select('img.product-image, .product-gallery img, .carousel-item img')
            
            for i, img in enumerate(img_elements):
                img_url = img.get('src') or img.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    # Skip small thumbnails or icons
                    if 'thumbnail' in img_url.lower() or 'icon' in img_url.lower():
                        continue
                    
                    filename = f"product_{product_id}_{i}.jpg"
                    save_path = os.path.join("dutch_passion_media/product_images", filename)
                    
                    if download_image(session, img_url, save_path):
                        images_data.append({
                            "url": img_url,
                            "local_path": save_path,
                            "is_main": i == 0  # First image is usually the main one
                        })
        except Exception as e:
            print(f"Error extracting product images: {e}")
    
    return images_data

# Main function to extract rich media from product pages
def extract_rich_media():
    print("Starting Dutch Passion Rich Media Extractor...")
    
    # Create directories for media files
    create_directories()
    
    # Load product data
    try:
        with open('dutch_passion_all_products.json', 'r', encoding='utf-8') as f:
            products = json.load(f)
    except Exception as e:
        print(f"Error loading product data: {e}")
        return
    
    # Extract rich media for each product
    rich_media_data = {}
    
    for i, product in enumerate(products):
        try:
            product_id = product.get('id') or product.get('data-id-product') or str(i+1)
            product_name = product.get('name', f"Product {product_id}")
            product_url = product.get('url')
            
            if not product_url:
                print(f"No URL found for product {product_name}, skipping...")
                continue
            
            print(f"Extracting rich media for product {i+1}/{len(products)}: {product_name}")
            
            # Get product page content
            html_content, session = get_page_content(product_url)
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract rich media elements
            terpene_data = extract_terpene_profiles(soup, product_id, session)
            cannabinoid_data = extract_cannabinoid_profiles(soup, product_id, session)
            icons_data = extract_product_icons(soup, product_id, session)
            images_data = extract_product_images(soup, product_id, session)
            
            # Store rich media data
            rich_media_data[product_id] = {
                "product_name": product_name,
                "product_url": product_url,
                "terpene_profile": terpene_data,
                "cannabinoid_profile": cannabinoid_data,
                "product_icons": icons_data,
                "product_images": images_data
            }
            
            # Be nice to the server
            time.sleep(1)
            
            # Process only a sample of products for testing
            if i >= 9:  # Process 10 products for initial testing
                break
            
        except Exception as e:
            print(f"Error processing product {product.get('name', '')}: {e}")
    
    # Save rich media data to JSON
    with open('dutch_passion_rich_media.json', 'w', encoding='utf-8') as f:
        json.dump(rich_media_data, f, indent=2)
    
    print(f"Rich media extraction completed for {len(rich_media_data)} products.")
    print("Data saved to dutch_passion_rich_media.json and media files saved to dutch_passion_media/ directory.")

if __name__ == "__main__":
    extract_rich_media()
