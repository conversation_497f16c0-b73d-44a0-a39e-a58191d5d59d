import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { X } from 'lucide-react';

// Define types for our filter data
interface FilterOption {
  id: string;
  name: string;
  value: string;
}

interface FilterGroup {
  id: string;
  name: string;
  options: FilterOption[];
}

interface SimpleSeedFiltersProps {
  onFiltersChange: (filters: Record<string, string[]>) => void;
}

// Mock filter data to use when database connection fails
const mockFilterGroups: FilterGroup[] = [
  {
    id: 'seed_type',
    name: 'Seed Type',
    options: [
      { id: 'st1', name: 'Feminized', value: 'feminized' },
      { id: 'st2', name: 'Regular', value: 'regular' },
      { id: 'st3', name: 'Autoflowering', value: 'autoflowering' }
    ]
  },
  {
    id: 'flowering_time',
    name: 'Flowering Time',
    options: [
      { id: 'ft1', name: '6-8 weeks', value: '6-8' },
      { id: 'ft2', name: '8-10 weeks', value: '8-10' },
      { id: 'ft3', name: '10+ weeks', value: '10+' }
    ]
  },
  {
    id: 'yield',
    name: 'Yield',
    options: [
      { id: 'y1', name: 'Low', value: 'low' },
      { id: 'y2', name: 'Medium', value: 'medium' },
      { id: 'y3', name: 'High', value: 'high' }
    ]
  },
  {
    id: 'thc_content',
    name: 'THC Content',
    options: [
      { id: 'thc1', name: 'Low (<15%)', value: 'low' },
      { id: 'thc2', name: 'Medium (15-20%)', value: 'medium' },
      { id: 'thc3', name: 'High (>20%)', value: 'high' }
    ]
  }
];

const SimpleSeedFilters: React.FC<SimpleSeedFiltersProps> = ({ onFiltersChange }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterGroups, setFilterGroups] = useState<FilterGroup[]>(mockFilterGroups);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({
    seed_type: [],
    flowering_time: [],
    yield: [],
    thc_content: []
  });
  const [loading, setLoading] = useState(true);

  // Initialize filters from URL params
  useEffect(() => {
    const initialFilters: Record<string, string[]> = {
      seed_type: [],
      flowering_time: [],
      yield: [],
      thc_content: []
    };

    // Parse URL parameters
    Object.keys(initialFilters).forEach(key => {
      const paramValue = searchParams.get(key);
      if (paramValue) {
        initialFilters[key] = paramValue.split(',');
      }
    });

    setActiveFilters(initialFilters);
    
    // Notify parent of initial filters
    onFiltersChange(initialFilters);
  }, [searchParams, onFiltersChange]);

  // Fetch filter data from seed_product_attributes table
  useEffect(() => {
    const fetchFilterData = async () => {
      setLoading(true);
      
      try {
        // Use type assertion to bypass TypeScript schema validation
        // We know these tables exist in the database but they might not be in the TypeScript schema
        const { data: seedTypeData } = await (supabase as any)
          .from('seed_type_options')
          .select('*')
          .order('display_order', { ascending: true });

        // Get unique values from seed_product_attributes
        const { data: attributesData } = await (supabase as any)
          .from('seed_product_attributes')
          .select('*');

        if (attributesData && attributesData.length > 0) {
          // Extract unique flowering times
          const floweringTimes = [...new Set(attributesData
            .map((item: any) => item.flowering_time)
            .filter(Boolean))]
            .map((value, index) => ({
              id: `ft-${index}`,
              name: value,
              value: value
            }));

          // Extract unique yields
          const yields = [...new Set(attributesData
            .map((item: any) => item.yield)
            .filter(Boolean))]
            .map((value, index) => ({
              id: `y-${index}`,
              name: value.charAt(0).toUpperCase() + value.slice(1),
              value: value
            }));

          // Extract unique THC levels
          const thcLevels = [...new Set(attributesData
            .map((item: any) => item.thc_level)
            .filter(Boolean))]
            .map((value, index) => ({
              id: `thc-${index}`,
              name: value.charAt(0).toUpperCase() + value.slice(1),
              value: value
            }));

          // Create filter groups from the data
          const dbFilterGroups: FilterGroup[] = [
            {
              id: 'seed_type',
              name: 'Seed Type',
              options: seedTypeData ? seedTypeData.map((item: any) => ({
                id: item.id,
                name: item.display_name,
                value: item.value
              })) : mockFilterGroups[0].options
            },
            {
              id: 'flowering_time',
              name: 'Flowering Time',
              options: floweringTimes.length > 0 ? floweringTimes : mockFilterGroups[1].options
            },
            {
              id: 'yield',
              name: 'Yield',
              options: yields.length > 0 ? yields : mockFilterGroups[2].options
            },
            {
              id: 'thc_content',
              name: 'THC Content',
              options: thcLevels.length > 0 ? thcLevels : mockFilterGroups[3].options
            }
          ];

          setFilterGroups(dbFilterGroups);
        } else {
          // Fallback to mock data if no attributes data
          setFilterGroups(mockFilterGroups);
        }
      } catch (error) {
        console.error('Error fetching filter data:', error);
        // Fallback to mock data
        setFilterGroups(mockFilterGroups);
      } finally {
        setLoading(false);
      }
    };

    fetchFilterData();
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((groupId: string, optionValue: string, isChecked: boolean) => {
    setActiveFilters(prevFilters => {
      // Create a copy of the current filters for this group
      const currentGroupFilters = [...(prevFilters[groupId] || [])];
      
      // Update the filters based on checkbox state
      const updatedGroupFilters = isChecked
        ? [...currentGroupFilters, optionValue] // Add the value if checked
        : currentGroupFilters.filter(value => value !== optionValue); // Remove if unchecked
      
      // Create the updated filters object
      const updatedFilters = {
        ...prevFilters,
        [groupId]: updatedGroupFilters
      };
      
      // Update URL parameters
      const newParams = new URLSearchParams(searchParams);
      Object.entries(updatedFilters).forEach(([key, values]) => {
        if (values.length > 0) {
          newParams.set(key, values.join(','));
        } else {
          newParams.delete(key);
        }
      });
      setSearchParams(newParams, { replace: true });
      
      // Notify parent component of filter changes
      onFiltersChange(updatedFilters);
      
      return updatedFilters;
    });
  }, [searchParams, setSearchParams, onFiltersChange]);
  
  // Clear all active filters
  const clearAllFilters = useCallback(() => {
    // Create a new object with empty arrays for all filter groups
    const clearedFilters: Record<string, string[]> = {};
    filterGroups.forEach(group => {
      clearedFilters[group.id] = [];
    });
    
    setActiveFilters(clearedFilters);
    
    // Remove filter parameters from URL
    const newParams = new URLSearchParams(searchParams);
    filterGroups.forEach(group => {
      newParams.delete(group.id);
    });
    setSearchParams(newParams, { replace: true });
    
    // Notify parent component of filter changes
    onFiltersChange(clearedFilters);
  }, [filterGroups, searchParams, setSearchParams, onFiltersChange]);

  // Check if any filters are active
  const hasActiveFilters = Object.values(activeFilters).some(values => values.length > 0);

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold">Filter Seeds</h3>
        {hasActiveFilters && (
          <button 
            onClick={clearAllFilters}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Clear All
          </button>
        )}
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-20">
          <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
        </div>
      ) : (
        <div className="space-y-4">
          {filterGroups.map(group => (
            <div key={group.id} className="border-t pt-4 last:border-b-0">
              <h4 className="font-medium mb-2">{group.name}</h4>
              <div className="grid grid-cols-2 gap-2">
                {group.options.map(option => {
                  const isActive = activeFilters[group.id]?.includes(option.value) || false;
                  
                  return (
                    <div key={option.id} className="flex items-center">
                      <Checkbox
                        id={`${group.id}-${option.id}`}
                        checked={isActive}
                        onCheckedChange={(checked) => 
                          handleFilterChange(group.id, option.value, checked === true)
                        }
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <Label 
                        htmlFor={`${group.id}-${option.id}`} 
                        className="ml-2 text-sm text-gray-700"
                      >
                        {option.name}
                      </Label>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
          
          {filterGroups.length === 0 && (
            <div className="text-center text-gray-500 py-4">
              No filter options available
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SimpleSeedFilters;
