#!/usr/bin/env tsx
/**
 * Import all Super Agent enriched batches
 * This script imports both batch 1 and batch 2 from the Super Agent
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import <PERSON> from 'papaparse';
import { join } from 'path';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface EnrichedSeedData {
  product_name: string;
  description: string;
  thc_level: string;
  cbd_level: string;
  flowering_time: string;
  effect: string;
  genetics: string;
  difficulty: string;
}

async function findProductByName(productName: string) {
  // Try exact match first
  let { data: products } = await supabase
    .from('products')
    .select('id, name')
    .ilike('name', productName)
    .limit(1);

  if (products && products.length > 0) {
    return products[0];
  }

  // Try fuzzy matching by removing common variations
  const cleanName = productName
    .replace(/Royal Queen of Seeds\s*/i, '')
    .replace(/\(Feminised?\s*x?\d*\)/i, '')
    .replace(/\(\d+\s*Feminised?\)/i, '')
    .replace(/\(Feminised?\)/i, '')
    .replace(/\(\d+\s*Female?\)/i, '')
    .replace(/\(x?\d+\s*Female?\)/i, '')
    .replace(/x\d+/i, '')
    .trim();

  ({ data: products } = await supabase
    .from('products')
    .select('id, name')
    .ilike('name', `%${cleanName}%`)
    .limit(5));

  if (products && products.length > 0) {
    console.log(`🔍 Fuzzy match for "${productName}":`);
    products.forEach((p, i) => console.log(`   ${i + 1}. ${p.name}`));
    return products[0]; // Return best match
  }

  return null;
}

async function importBatch(batchFile: string, batchNumber: number) {
  console.log(`\n🌱 Importing Super Agent Batch ${batchNumber}: ${batchFile}`);
  
  try {
    const csvPath = join(process.cwd(), 'docs', 'super_agent', batchFile);
    const csvContent = readFileSync(csvPath, 'utf8');
    
    const parseResult = Papa.parse(csvContent, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.trim()
    });
    
    if (parseResult.errors.length > 0) {
      console.error('❌ CSV parsing errors:', parseResult.errors);
      return { imported: 0, skipped: 0, errors: 1 };
    }
    
    const records: EnrichedSeedData[] = parseResult.data as EnrichedSeedData[];
    console.log(`📄 Found ${records.length} products in batch ${batchNumber}\n`);

    let imported = 0;
    let skipped = 0;
    let errors = 0;

    for (const record of records) {
      if (!record.product_name || record.product_name.trim() === '') {
        continue; // Skip empty rows
      }
      
      console.log(`🔍 Processing: ${record.product_name}`);

      try {
        const product = await findProductByName(record.product_name);
        
        if (!product) {
          console.log(`   ⚠️  No matching product found - skipping`);
          skipped++;
          continue;
        }

        console.log(`   ✅ Matched to: ${product.name}`);

        // Update product description
        if (record.description && record.description.trim().length > 50) {
          const { error: descError } = await supabase
            .from('products')
            .update({ description: record.description })
            .eq('id', product.id);
          
          if (descError) {
            console.log(`   ⚠️  Could not update description: ${descError.message}`);
          } else {
            console.log(`   ✅ Updated description (${record.description.length} chars)`);
          }
        }

        // Insert/update seed attributes
        const { data: existingAttr } = await supabase
          .from('seed_product_attributes')
          .select('id')
          .eq('product_id', product.id)
          .single();

        const attributeData = {
          product_id: product.id,
          seed_type: 'Feminised', // All these are feminised seeds
          flowering_time: record.flowering_time,
          thc_level: record.thc_level,
          cbd_level: record.cbd_level,
          effect: record.effect,
          genetics: record.genetics,
          seed_family: record.genetics
        };

        let attrError;
        if (existingAttr) {
          const { error } = await supabase
            .from('seed_product_attributes')
            .update(attributeData)
            .eq('product_id', product.id);
          attrError = error;
        } else {
          const { error } = await supabase
            .from('seed_product_attributes')
            .insert(attributeData);
          attrError = error;
        }

        if (attrError) {
          console.error(`   ❌ Error updating attributes: ${attrError.message}`);
          errors++;
          continue;
        }

        console.log(`   ✅ Successfully imported enriched data`);
        imported++;

      } catch (err) {
        console.error(`   ❌ Error processing ${record.product_name}:`, err);
        errors++;
      }
    }

    return { imported, skipped, errors };

  } catch (err) {
    console.error(`❌ Batch ${batchNumber} import failed:`, err);
    return { imported: 0, skipped: 0, errors: 1 };
  }
}

async function importAllBatches() {
  console.log('🚀 Importing all Super Agent enriched batches...\n');

  const batches = [
    { file: 'enriched_active_seeds.csv', number: 1 },
    { file: 'enriched_active_seeds_batch2.csv', number: 2 }
  ];

  let totalImported = 0;
  let totalSkipped = 0;
  let totalErrors = 0;

  for (const batch of batches) {
    const result = await importBatch(batch.file, batch.number);
    totalImported += result.imported;
    totalSkipped += result.skipped;
    totalErrors += result.errors;
  }

  console.log('\n📊 FINAL IMPORT SUMMARY:');
  console.log(`   ✅ Successfully imported: ${totalImported}`);
  console.log(`   ⚠️  Skipped (no match): ${totalSkipped}`);
  console.log(`   ❌ Errors: ${totalErrors}`);
  console.log(`   📄 Total processed: ${totalImported + totalSkipped + totalErrors}`);

  if (totalImported > 0) {
    console.log('\n🎉 Import completed! Your active products now have:');
    console.log('   📝 Professional descriptions');
    console.log('   🌱 Complete seed attributes');
    console.log('   🎯 Ready for enhanced filtering');
    console.log('\n💡 Next steps:');
    console.log('   1. Test the product pages with new descriptions');
    console.log('   2. Verify the seed filtering system');
    console.log('   3. Request next batch from Super Agent');
  }
}

// Run the import
importAllBatches().catch(console.error);
