import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  image: string;
  date: string;
  author: string;
  category: string;
}

interface BlogPreviewProps {
  posts: BlogPost[];
}

const BlogPreview = ({ posts }: BlogPreviewProps) => {
  return (
    <section className="py-16 bg-white">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10">
          <div>
            <h2 className="section-heading mb-2">From Our Blog</h2>
            <p className="text-clay-700 max-w-2xl">
              Discover educational content about CBD, smoking culture, and product guides.
            </p>
          </div>
          <Button asChild variant="outline" className="mt-4 md:mt-0">
            <a href="http://localhost:8080/blog">View All Posts</a>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((post) => (
            <a 
              key={post.id} 
              href={`http://localhost:8080/blog/${post.slug}`}
              className="group block focus:outline-none focus:ring-2 focus:ring-primary rounded-lg"
              tabIndex={0}
            >
              <div className="rounded-lg overflow-hidden shadow-md transition-transform duration-300 group-hover:shadow-2xl group-hover:-translate-y-2 bg-white">
                <div className="relative overflow-hidden">
                  <AspectRatio ratio={16/9}>
                    <img 
                      src={post.image} 
                      alt={post.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                  </AspectRatio>
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/90 text-clay-900 px-3 py-1 text-xs font-medium rounded">
                      {post.category}
                    </span>
                  </div>
                </div>
                <div className="p-6 bg-white">
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>By {post.author}</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-clay-900 group-hover:text-primary transition-colors">
                    {post.title}
                  </h3>
                  <p className="text-clay-700 line-clamp-3">
                    {post.excerpt}
                  </p>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogPreview;
