-- Migration to fix profile update permissions
-- This adds proper Row Level Security (RLS) policies to the profiles table

-- Enable Row Level Security on the profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows users to select their own profile
CREATE POLICY select_own_profile ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Create a policy that allows users to update their own profile
CREATE POLICY update_own_profile ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Create a policy that allows users to insert their own profile
CREATE POLICY insert_own_profile ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Ensure profiles are created with the correct user ID
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, created_at, updated_at, is_admin)
  VALUES (new.id, new.email, now(), now(), false);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
