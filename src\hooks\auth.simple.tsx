import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Session, User, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

// Helper function to clear auth storage
export const clearAuthStorage = () => {
  try {
    localStorage.removeItem('supabase.auth.token');
    sessionStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('sb-refresh-token');
    sessionStorage.removeItem('sb-refresh-token');
    localStorage.removeItem('sb-access-token');
    sessionStorage.removeItem('sb-access-token');
    console.log('Auth storage cleared successfully');
    return true;
  } catch (error) {
    console.error('Error clearing auth storage:', error);
    return false;
  }
};

interface Profile {
  id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  is_admin: boolean;
  created_at?: string;
  updated_at?: string;
  [key: string]: any; // Allow any additional properties
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  isAdmin: boolean;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: AuthError }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error?: AuthError }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error?: AuthError }>;
  updatePassword: (password: string) => Promise<{ error?: AuthError }>;
  fetchUserProfile: (userId: string) => Promise<Profile | null>;
  updateUserProfile: (profile: Partial<Profile>) => Promise<{ error?: AuthError }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch user profile from Supabase
  const fetchUserProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      if (data) {
        console.log('Raw is_admin value:', data.is_admin);
        console.log('is_admin type:', typeof data.is_admin);
        
        // Check for all possible field names (case-insensitive)
        const adminFields = ['is_admin', 'isAdmin', 'IsAdmin', 'is_Admin', 'IS_ADMIN'];
        
        // Find the first admin field that exists
        let adminValue: any = null;
        for (const field of adminFields) {
          if (field in data && data[field as keyof typeof data] !== null && data[field as keyof typeof data] !== undefined) {
            adminValue = data[field as keyof typeof data];
            console.log(`Found admin field: ${field} with value:`, adminValue);
            break;
          }
        }
        
        // Force is_admin to be a proper boolean
        let isAdminValue = false;
        
        if (adminValue !== null) {
          if (typeof adminValue === 'string') {
            const adminValueStr = adminValue as string;
            isAdminValue = adminValueStr.toLowerCase() === 'true';
          } else if (typeof adminValue === 'number') {
            isAdminValue = adminValue === 1;
          } else if (typeof adminValue === 'boolean') {
            isAdminValue = adminValue;
          }
        } else {
          // Default to the original is_admin field if it exists
          const originalIsAdmin = data.is_admin;
          if (typeof originalIsAdmin === 'string') {
            isAdminValue = (originalIsAdmin as string).toLowerCase() === 'true';
          } else if (typeof originalIsAdmin === 'number') {
            isAdminValue = originalIsAdmin === 1;
          } else if (typeof originalIsAdmin === 'boolean') {
            isAdminValue = originalIsAdmin;
          }
        }
        
        // Set the is_admin field to the computed boolean value
        data.is_admin = isAdminValue;
        
        console.log('Processed is_admin value:', data.is_admin);
      }
      
      return data as Profile;
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    // Add a timeout to prevent infinite loading
    const authTimeout = setTimeout(() => {
      if (isLoading) {
        console.log('Auth loading timeout reached, resetting auth state');
        setIsLoading(false);
        clearAuthStorage();
      }
    }, 10000); // 10 seconds timeout

    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data } = await supabase.auth.getSession();
        const currentSession = data.session;
        
        console.log('Initial session check:', currentSession ? 'Found session' : 'No session');
        
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        
        if (currentSession?.user) {
          const userProfile = await fetchUserProfile(currentSession.user.id);
          
          if (!userProfile) {
            console.error('Failed to fetch user profile, clearing auth state');
            clearAuthStorage();
            setSession(null);
            setUser(null);
            setProfile(null);
            setIsAdmin(false);
          } else {
            setProfile(userProfile);
            
            // TEMPORARY FIX: Hardcode admin access for specific user ID
            const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
            const isKnownAdmin = currentSession.user && knownAdminUserIds.includes(currentSession.user.id);
            
            let finalAdminStatus = false;
            
            if (isKnownAdmin) {
              // This user is a known admin, bypass the profile check
              finalAdminStatus = true;
              console.log('Admin access granted in auth hook based on hardcoded user ID');
            } else {
              // Check if user is admin from profile or user metadata
              const adminFromProfile = userProfile?.is_admin === true;
              const adminFromMetadata = 
                currentSession.user.user_metadata?.is_admin === true || 
                currentSession.user.app_metadata?.is_admin === true || 
                currentSession.user.app_metadata?.admin === true || 
                currentSession.user.app_metadata?.role === 'admin';
              
              finalAdminStatus = adminFromProfile || adminFromMetadata;
            }
            
            setIsAdmin(finalAdminStatus);
            
            console.log('User profile loaded:', { 
              profile: userProfile, 
              isAdmin: finalAdminStatus 
            });
          }
        }
      } catch (error) {
        console.error('Error in initial session processing:', error);
        clearAuthStorage();
      } finally {
        setIsLoading(false);
      }
    };
    
    // Set up auth state change listener
    const setupAuthListener = () => {
      try {
        const { data } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
          console.log('Auth state changed:', event, currentSession ? 'Has session' : 'No session');
          
          try {
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            
            if (currentSession?.user) {
              const userProfile = await fetchUserProfile(currentSession.user.id);
              
              if (!userProfile) {
                console.error('Failed to fetch user profile on auth change, clearing auth state');
                clearAuthStorage();
                setSession(null);
                setUser(null);
                setProfile(null);
                setIsAdmin(false);
              } else {
                setProfile(userProfile);
                
                // TEMPORARY FIX: Hardcode admin access for specific user ID
                const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
                const isKnownAdmin = currentSession.user && knownAdminUserIds.includes(currentSession.user.id);
                
                let finalAdminStatus = false;
                
                if (isKnownAdmin) {
                  // This user is a known admin, bypass the profile check
                  finalAdminStatus = true;
                  console.log('Admin access granted in auth hook based on hardcoded user ID');
                } else {
                  // Check if user is admin from profile or user metadata
                  const adminFromProfile = userProfile?.is_admin === true;
                  const adminFromMetadata = 
                    currentSession.user.user_metadata?.is_admin === true || 
                    currentSession.user.app_metadata?.is_admin === true || 
                    currentSession.user.app_metadata?.admin === true || 
                    currentSession.user.app_metadata?.role === 'admin';
                  
                  finalAdminStatus = adminFromProfile || adminFromMetadata;
                }
                
                setIsAdmin(finalAdminStatus);
                
                console.log('User profile loaded on auth change:', { 
                  profile: userProfile, 
                  isAdmin: finalAdminStatus 
                });
              }
            } else {
              setProfile(null);
              setIsAdmin(false);
            }
          } catch (error) {
            console.error('Error in auth state change processing:', error);
            clearAuthStorage();
          }
        });
        
        return data.subscription;
      } catch (error) {
        console.error('Error setting up auth listener:', error);
        return null;
      }
    };
    
    // Initialize auth
    initializeAuth();
    
    // Set up auth listener
    const subscription = setupAuthListener();
    
    // Cleanup function
    return () => {
      clearTimeout(authTimeout);
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) {
        console.error('Error signing in:', error);
        return { error };
      }
      
      return {};
    } catch (error) {
      console.error('Exception during sign in:', error);
      return { error: error as AuthError };
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      const { data, error } = await supabase.auth.signUp({ 
        email, 
        password,
        options: {
          data: metadata
        }
      });
      
      if (error) {
        console.error('Error signing up:', error);
        return { error };
      }
      
      if (data?.user) {
        toast({
          title: 'Account created',
          description: 'Please check your email to confirm your account',
        });
      }
      
      return {};
    } catch (error) {
      console.error('Exception during sign up:', error);
      return { error: error as AuthError };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      clearAuthStorage();
      
      // Clear state
      setSession(null);
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      
      toast({
        title: 'Signed out',
        description: 'You have been signed out successfully',
      });
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: 'Error',
        description: 'Failed to sign out',
        variant: 'destructive',
      });
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });
      
      if (error) {
        console.error('Error resetting password:', error);
        return { error };
      }
      
      toast({
        title: 'Password reset email sent',
        description: 'Check your email for a password reset link',
      });
      
      return {};
    } catch (error) {
      console.error('Exception during password reset:', error);
      return { error: error as AuthError };
    }
  };

  // Update password
  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({ password });
      
      if (error) {
        console.error('Error updating password:', error);
        return { error };
      }
      
      toast({
        title: 'Password updated',
        description: 'Your password has been updated successfully',
      });
      
      return {};
    } catch (error) {
      console.error('Exception during password update:', error);
      return { error: error as AuthError };
    }
  };

  // Update user profile
  const updateUserProfile = async (profileData: Partial<Profile>) => {
    if (!user) {
      return { error: new Error('No user logged in') as unknown as AuthError };
    }
    
    try {
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          ...profileData,
          updated_at: new Date().toISOString(),
        });
      
      if (error) {
        console.error('Error updating profile:', error);
        return { error: error as unknown as AuthError };
      }
      
      // Refresh profile
      const updatedProfile = await fetchUserProfile(user.id);
      if (updatedProfile) {
        setProfile(updatedProfile);
      }
      
      return {};
    } catch (error) {
      console.error('Exception during profile update:', error);
      return { error: error as AuthError };
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        isAdmin,
        isLoading,
        signIn,
        signUp,
        signOut,
        resetPassword,
        updatePassword,
        fetchUserProfile,
        updateUserProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
