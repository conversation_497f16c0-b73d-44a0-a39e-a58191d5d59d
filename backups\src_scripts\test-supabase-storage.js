// Test script to verify Supabase storage access
import { createClient } from '@supabase/supabase-js';

// Hardcoded Supabase credentials to ensure they work
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to test listing buckets
async function testListBuckets() {
  console.log('Testing Supabase storage access...');
  console.log('Supabase URL:', supabaseUrl);
  console.log('Supabase Key length:', supabaseKey.length);

  try {
    // List all storage buckets
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      return false;
    }

    console.log('Successfully listed buckets:', data);
    return true;
  } catch (error) {
    console.error('Exception when listing buckets:', error);
    return false;
  }
}

// Function to test listing files in the product-images bucket
async function testListFiles() {
  try {
    console.log('Testing listing files in product-images bucket...');

    const { data, error } = await supabase.storage
      .from('product-images')
      .list();

    if (error) {
      console.error('Error listing files:', error);
      return false;
    }

    console.log('Successfully listed files:', data);
    return true;
  } catch (error) {
    console.error('Exception when listing files:', error);
    return false;
  }
}

// Function to test uploading a test file
async function testUploadFile() {
  try {
    console.log('Testing uploading a test file...');

    // Create a simple text file
    const testContent = 'This is a test file ' + new Date().toISOString();
    const testFileName = 'test-file-' + Date.now() + '.txt';

    // Upload the file
    const { data, error } = await supabase.storage
      .from('product-images')
      .upload(testFileName, testContent, {
        contentType: 'text/plain',
        upsert: true
      });

    if (error) {
      console.error('Error uploading test file:', error);
      return false;
    }

    console.log('Successfully uploaded test file:', data);

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('product-images')
      .getPublicUrl(testFileName);

    console.log('Public URL:', urlData.publicUrl);

    return true;
  } catch (error) {
    console.error('Exception when uploading test file:', error);
    return false;
  }
}

// Run all tests
async function runTests() {
  const bucketsResult = await testListBuckets();
  const filesResult = await testListFiles();
  const uploadResult = await testUploadFile();

  console.log('\nTest Results:');
  console.log('- List Buckets:', bucketsResult ? 'PASS' : 'FAIL');
  console.log('- List Files:', filesResult ? 'PASS' : 'FAIL');
  console.log('- Upload File:', uploadResult ? 'PASS' : 'FAIL');

  if (bucketsResult && filesResult && uploadResult) {
    console.log('\nAll tests PASSED! Supabase storage is working correctly.');
  } else {
    console.log('\nSome tests FAILED. Supabase storage has issues.');
  }
}

// Run the tests
runTests();
