import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";

interface FilterOption {
  id: string;
  category_id: string;
  name: string;
  display_name: string;
  display_order: number;
  product_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FilterOptionFormProps {
  option: FilterOption | null;
  categoryId: string;
  onSubmit: (option: Partial<FilterOption>) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export function FilterOptionForm({
  option,
  categoryId,
  onSubmit,
  onCancel,
  isLoading
}: FilterOptionFormProps) {
  const [name, setName] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [displayOrder, setDisplayOrder] = useState(0);
  const [isActive, setIsActive] = useState(true);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Initialize form with option data if editing
  useEffect(() => {
    if (option) {
      setName(option.name);
      setDisplayName(option.display_name);
      setDisplayOrder(option.display_order);
      setIsActive(option.is_active);
    } else {
      // For new options, set the display order to be the next in sequence
      const getNextDisplayOrder = async () => {
        const { data, error } = await supabase
          .from("filter_options")
          .select("display_order")
          .eq("category_id", categoryId)
          .order("display_order", { ascending: false })
          .limit(1);

        if (!error && data && data.length > 0) {
          setDisplayOrder(data[0].display_order + 10);
        } else {
          setDisplayOrder(10);
        }
      };

      getNextDisplayOrder();
    }
  }, [option, categoryId]);

  // Generate a slug-like name from display name
  const handleDisplayNameChange = (value: string) => {
    setDisplayName(value);
    // Only auto-generate the name if it's a new option or the name hasn't been manually edited
    if (!option || option.name === name) {
      setName(value.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, ''));
    }
  };

  // Validate form before submission
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!name.trim()) {
      errors.name = "Name is required";
    } else if (!/^[a-z0-9_]+$/.test(name)) {
      errors.name = "Name can only contain lowercase letters, numbers, and underscores";
    }

    if (!displayName.trim()) {
      errors.displayName = "Display name is required";
    }

    if (displayOrder < 0) {
      errors.displayOrder = "Display order must be a positive number";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    onSubmit({
      category_id: categoryId,
      name,
      display_name: displayName,
      display_order: displayOrder,
      is_active: isActive,
      product_count: option?.product_count || 0
    });
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {option ? "Edit Filter Option" : "Add Filter Option"}
          </DialogTitle>
          <DialogDescription>
            {option 
              ? "Update the details for this filter option." 
              : "Create a new filter option for the selected category."}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="display-name" className="text-right">
              Display Name
            </Label>
            <div className="col-span-3">
              <Input
                id="display-name"
                value={displayName}
                onChange={(e) => handleDisplayNameChange(e.target.value)}
                placeholder="e.g. Feminized"
              />
              {formErrors.displayName && (
                <p className="text-sm text-red-500 mt-1">{formErrors.displayName}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Internal Name
            </Label>
            <div className="col-span-3">
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g. feminized"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Used internally. Use lowercase letters, numbers, and underscores only.
              </p>
              {formErrors.name && (
                <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="display-order" className="text-right">
              Display Order
            </Label>
            <div className="col-span-3">
              <Input
                id="display-order"
                type="number"
                value={displayOrder}
                onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
                min={0}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Lower numbers appear first in the filter list.
              </p>
              {formErrors.displayOrder && (
                <p className="text-sm text-red-500 mt-1">{formErrors.displayOrder}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="is-active" className="text-right">
              Active
            </Label>
            <div className="flex items-center space-x-2 col-span-3">
              <Switch
                id="is-active"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="is-active">
                {isActive ? "Active" : "Inactive"}
              </Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : (option ? "Update" : "Create")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
