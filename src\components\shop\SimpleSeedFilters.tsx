import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { X } from 'lucide-react';

// Define types for our filter data
interface FilterOption {
  id: string;
  name: string;
  value: string;
}

interface FilterGroup {
  id: string;
  name: string;
  options: FilterOption[];
}

interface SimpleSeedFiltersProps {
  onFiltersChange: (filters: Record<string, string[]>) => void;
}

// Mock filter data to use when database connection fails - updated to match database structure
const mockFilterGroups: FilterGroup[] = [
  {
    id: 'seed_type',
    name: 'Seed Type',
    options: [
      { id: 'st1', name: '<PERSON><PERSON>', value: 'autoflower' },
      { id: 'st2', name: 'Feminized', value: 'feminized' },
      { id: 'st3', name: 'Regular', value: 'regular' }
    ]
  },
  {
    id: 'flowertime',
    name: 'Flowering Time',
    options: [
      { id: 'ft1', name: '7 weeks', value: '7_weeks' },
      { id: 'ft2', name: '8 weeks', value: '8_weeks' },
      { id: 'ft3', name: '9 weeks', value: '9_weeks' },
      { id: 'ft4', name: '10 weeks', value: '10_weeks' },
      { id: 'ft5', name: '11 weeks', value: '11_weeks' },
      { id: 'ft6', name: '12+ weeks', value: '12_weeks' }
    ]
  },
  {
    id: 'yield',
    name: 'Yield',
    options: [
      { id: 'y1', name: 'M', value: 'm' },
      { id: 'y2', name: 'M/L', value: 'm_l' },
      { id: 'y3', name: 'L', value: 'l' },
      { id: 'y4', name: 'XL', value: 'xl' },
      { id: 'y5', name: 'XXL', value: 'xxl' }
    ]
  },
  {
    id: 'thc',
    name: 'THC Level',
    options: [
      { id: 'thc1', name: 'Low', value: 'low' },
      { id: 'thc2', name: 'Medium', value: 'medium' },
      { id: 'thc3', name: 'High', value: 'high' },
      { id: 'thc4', name: 'Very High', value: 'very_high' },
      { id: 'thc5', name: 'Extremely High', value: 'extremely_high' }
    ]
  }
];

const SimpleSeedFilters: React.FC<SimpleSeedFiltersProps> = ({ onFiltersChange }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterGroups, setFilterGroups] = useState<FilterGroup[]>(mockFilterGroups);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(true);

  // Initialize filters from URL params - run after filter groups are loaded
  useEffect(() => {
    if (filterGroups.length > 0) {
      const initialFilters: Record<string, string[]> = {};

      // Initialize with empty arrays for all available filter groups
      filterGroups.forEach(group => {
        initialFilters[group.id] = [];
      });

      // Parse URL parameters
      Object.keys(initialFilters).forEach(key => {
        const paramValue = searchParams.get(key);
        if (paramValue) {
          initialFilters[key] = paramValue.split(',');
        }
      });

      setActiveFilters(initialFilters);

      // Notify parent of initial filters
      onFiltersChange(initialFilters);
    }
  }, [searchParams, onFiltersChange, filterGroups]);

  // Fetch filter data from the new filter system
  useEffect(() => {
    const fetchFilterData = async () => {
      setLoading(true);

      try {
        // Get filter categories and their options from the new system
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('filter_categories')
          .select(`
            id,
            name,
            display_name,
            filter_options (
              id,
              name,
              display_name,
              product_count,
              display_order
            )
          `)
          .eq('is_active', true)
          .order('display_order');

        if (categoriesError) {
          console.error('Error fetching filter categories:', categoriesError);
          setFilterGroups(mockFilterGroups);
          return;
        }

        if (categoriesData && categoriesData.length > 0) {
          // Transform the data into our FilterGroup format
          const dbFilterGroups: FilterGroup[] = categoriesData.map(category => ({
            id: category.name, // Use the category name as the ID (seed_type, flowering_time, etc.)
            name: category.display_name,
            options: (category.filter_options || [])
              .filter((option: any) => option.product_count > 0) // Only show options with products
              .sort((a: any, b: any) => a.display_order - b.display_order)
              .map((option: any) => ({
                id: option.id,
                name: `${option.display_name} (${option.product_count})`, // Show product count
                value: option.name // Use the option name as the value
              }))
          })).filter(group => group.options.length > 0); // Only show groups with options

          console.log('Loaded filter groups from database:', dbFilterGroups);
          setFilterGroups(dbFilterGroups);

          // Don't update activeFilters here - let the URL initialization handle it
        } else {
          console.log('No filter categories found, using mock data');
          setFilterGroups(mockFilterGroups);
        }
      } catch (error) {
        console.error('Error fetching filter data:', error);
        // Fallback to mock data
        setFilterGroups(mockFilterGroups);
      } finally {
        setLoading(false);
      }
    };

    fetchFilterData();
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((groupId: string, optionValue: string, isChecked: boolean) => {
    setActiveFilters(prevFilters => {
      // Create a copy of the current filters for this group
      const currentGroupFilters = [...(prevFilters[groupId] || [])];

      // Update the filters based on checkbox state
      const updatedGroupFilters = isChecked
        ? [...currentGroupFilters, optionValue] // Add the value if checked
        : currentGroupFilters.filter(value => value !== optionValue); // Remove if unchecked

      // Create the updated filters object
      const updatedFilters = {
        ...prevFilters,
        [groupId]: updatedGroupFilters
      };

      // Update URL parameters
      const newParams = new URLSearchParams(searchParams);
      Object.entries(updatedFilters).forEach(([key, values]) => {
        if (values.length > 0) {
          newParams.set(key, values.join(','));
        } else {
          newParams.delete(key);
        }
      });
      setSearchParams(newParams, { replace: true });

      // Notify parent component of filter changes
      onFiltersChange(updatedFilters);

      return updatedFilters;
    });
  }, [searchParams, setSearchParams, onFiltersChange]);

  // Clear all active filters
  const clearAllFilters = useCallback(() => {
    // Create a new object with empty arrays for all filter groups
    const clearedFilters: Record<string, string[]> = {};
    filterGroups.forEach(group => {
      clearedFilters[group.id] = [];
    });

    setActiveFilters(clearedFilters);

    // Remove filter parameters from URL
    const newParams = new URLSearchParams(searchParams);
    filterGroups.forEach(group => {
      newParams.delete(group.id);
    });
    setSearchParams(newParams, { replace: true });

    // Notify parent component of filter changes
    onFiltersChange(clearedFilters);
  }, [filterGroups, searchParams, setSearchParams, onFiltersChange]);

  // Check if any filters are active
  const hasActiveFilters = Object.values(activeFilters).some(values => values.length > 0);

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold">Filter Seeds</h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Clear All
          </button>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-20">
          <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
        </div>
      ) : (
        <div className="space-y-4">
          {filterGroups.map(group => (
            <div key={group.id} className="border-t pt-4 last:border-b-0">
              <h4 className="font-medium mb-2">{group.name}</h4>
              <div className="space-y-2">
                {group.options.map(option => {
                  const isActive = activeFilters[group.id]?.includes(option.value) || false;

                  return (
                    <div key={option.id} className="flex items-center">
                      <Checkbox
                        id={`${group.id}-${option.id}`}
                        checked={isActive}
                        onCheckedChange={(checked) =>
                          handleFilterChange(group.id, option.value, checked === true)
                        }
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded flex-shrink-0"
                      />
                      <Label
                        htmlFor={`${group.id}-${option.id}`}
                        className="ml-2 text-sm text-gray-700 leading-tight"
                      >
                        {option.name}
                      </Label>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}

          {filterGroups.length === 0 && (
            <div className="text-center text-gray-500 py-4">
              No filter options available
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SimpleSeedFilters;
