
import { useState } from 'react';
import ProductCard3D from './ProductCard3D';
import AnimatedOnScroll from '../animations/AnimatedOnScroll';
import { Button } from '@/components/ui/button';
import { Product } from '@/components/ProductCard';

interface EnhancedProductGridProps {
  products: Product[];
  title?: string;
  description?: string;
  showLoadMore?: boolean;
  initialCount?: number;
  loadMoreIncrement?: number;
}

const EnhancedProductGrid = ({ 
  products, 
  title, 
  description,
  showLoadMore = false,
  initialCount = 4,
  loadMoreIncrement = 4
}: EnhancedProductGridProps) => {
  const [visibleCount, setVisibleCount] = useState(initialCount);
  
  const handleLoadMore = () => {
    setVisibleCount(prevCount => Math.min(prevCount + loadMoreIncrement, products.length));
  };
  
  const visibleProducts = products.slice(0, visibleCount);
  
  return (
    <section className="py-16 bg-gradient-to-b from-transparent to-sage-50/30">
      <div className="container-custom">
        <AnimatedOnScroll animation="fade-up">
          {title && <h2 className="section-heading text-center">{title}</h2>}
          {description && <p className="text-clay-700 mb-12 max-w-3xl mx-auto text-center text-lg">{description}</p>}
        </AnimatedOnScroll>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {visibleProducts.map((product, index) => (
            <AnimatedOnScroll 
              key={product.id} 
              animation="fade-up" 
              delay={index * 100}
              threshold={0.1}
            >
              <ProductCard3D product={product} />
            </AnimatedOnScroll>
          ))}
        </div>
        
        {showLoadMore && visibleCount < products.length && (
          <div className="mt-12 flex justify-center">
            <Button 
              onClick={handleLoadMore}
              variant="outline" 
              size="lg"
              className="border-sage-500 text-sage-800 hover:bg-sage-500 hover:text-white transition-all duration-300"
            >
              Load More Products
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default EnhancedProductGrid;
