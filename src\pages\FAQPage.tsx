import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { FAQ } from '@/types/database';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useToast } from '@/hooks/use-toast';
import { Loader2, Plus, Edit, Trash2, Search, HelpCircle } from 'lucide-react';
import { useAuth } from '@/hooks/auth.basic';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

const FAQPage = () => {
  const { toast } = useToast();
  const { isAdmin } = useAuth();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFAQ, setSelectedFAQ] = useState<FAQ | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: 'Products',
    is_published: true
  });

  // Fetch FAQs from Supabase
  const { data: faqs, isLoading, error, refetch } = useQuery({
    queryKey: ['faqs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .order('order_index', { ascending: true });
      
      if (error) throw error;
      return data as FAQ[];
    }
  });

  // Filter and group FAQs by category
  const filteredFAQs = faqs?.filter(faq => {
    // Filter by search query
    if (searchQuery) {
      return (
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    return true;
  });

  // Group FAQs by category
  const groupedFAQs = filteredFAQs?.reduce((acc, faq) => {
    const category = faq.category || 'General';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(faq);
    return acc;
  }, {} as Record<string, FAQ[]>) || {};
  
  // Get all unique categories
  const categories = faqs ? [...new Set(faqs.map(faq => faq.category || 'General'))] : [];

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, is_published: checked }));
  };

  // Handle select changes
  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, category: value }));
  };

  // Reset form
  const resetForm = () => {
    setSelectedFAQ(null);
    setFormData({
      question: '',
      answer: '',
      category: 'Products',
      is_published: true
    });
  };

  // Open dialog for adding/editing FAQ
  const openEditDialog = (faq?: FAQ) => {
    if (faq) {
      setSelectedFAQ(faq);
      setFormData({
        question: faq.question,
        answer: faq.answer,
        category: faq.category || 'Products',
        is_published: faq.is_published
      });
    } else {
      resetForm();
    }
    setOpenDialog(true);
  };

  // Save FAQ
  const saveFAQ = async () => {
    try {
      if (!formData.question || !formData.answer) {
        toast({
          title: "Error",
          description: "Question and answer are required",
          variant: "destructive"
        });
        return;
      }

      if (selectedFAQ) {
        // Update existing FAQ
        const { error } = await supabase
          .from('faqs')
          .update({
            question: formData.question,
            answer: formData.answer,
            category: formData.category,
            is_published: formData.is_published,
            updated_at: new Date().toISOString()
          })
          .eq('id', selectedFAQ.id);

        if (error) throw error;
        
        toast({
          title: "Success",
          description: "FAQ updated successfully"
        });
      } else {
        // Add new FAQ
        const maxOrderIndex = faqs?.reduce((max, faq) => Math.max(max, faq.order_index), 0) || 0;
        
        const { error } = await supabase
          .from('faqs')
          .insert({
            question: formData.question,
            answer: formData.answer,
            category: formData.category,
            is_published: formData.is_published,
            order_index: maxOrderIndex + 1
          });

        if (error) throw error;
        
        toast({
          title: "Success",
          description: "FAQ added successfully"
        });
      }

      resetForm();
      setOpenDialog(false);
      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to save FAQ",
        variant: "destructive"
      });
    }
  };

  // Delete FAQ
  const deleteFAQ = async (id: string) => {
    if (!confirm("Are you sure you want to delete this FAQ?")) return;
    
    try {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "FAQ deleted successfully"
      });
      
      refetch();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete FAQ",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container-custom py-16 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Error Loading FAQs</h2>
          <p>Please try again later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Banner */}
      <div className="relative bg-gradient-to-r from-primary/90 to-primary overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-20" style={{ 
          backgroundImage: `url('/question-marks.jpg')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          mixBlendMode: 'overlay'
        }}></div>
        <div className="container mx-auto px-4 py-16 relative z-10">
          <div className="max-w-3xl mx-auto text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Frequently Asked Questions</h1>
            <p className="text-lg md:text-xl opacity-90 mb-8">Find answers to the most common questions about our products and services</p>
            
            {/* Search Bar */}
            <div className="relative max-w-xl mx-auto">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-white/70" />
              </div>
              <Input
                type="text"
                placeholder="Search for answers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-6 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20 focus:ring-white/50 rounded-full"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Admin Controls */}
          {isAdmin && (
            <div className="mb-8 flex justify-end">
              <Button onClick={() => openEditDialog()} className="shadow-md">
                <Plus className="h-4 w-4 mr-2" />
                Add FAQ
              </Button>
            </div>
          )}

          {/* Category Filters */}
          <div className="mb-8 flex flex-wrap gap-2">
            <Badge 
              variant={activeCategory === null ? "default" : "outline"}
              className={cn(
                "px-4 py-2 text-sm cursor-pointer transition-all",
                activeCategory === null ? "bg-primary hover:bg-primary/90" : "hover:bg-gray-100"
              )}
              onClick={() => setActiveCategory(null)}
            >
              All
            </Badge>
            {categories.map(category => (
              <Badge 
                key={category}
                variant={activeCategory === category ? "default" : "outline"}
                className={cn(
                  "px-4 py-2 text-sm cursor-pointer transition-all",
                  activeCategory === category ? "bg-primary hover:bg-primary/90" : "hover:bg-gray-100"
                )}
                onClick={() => setActiveCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>

          {/* FAQ Content */}
          <div className="space-y-8">
            {isLoading ? (
              <Card className="flex justify-center items-center py-16">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </Card>
            ) : error ? (
              <Card className="p-8 text-center text-red-500">
                <p>Error loading FAQs. Please try again later.</p>
              </Card>
            ) : Object.keys(groupedFAQs).length > 0 ? (
              Object.entries(groupedFAQs)
                .filter(([category]) => activeCategory === null || category === activeCategory)
                .map(([category, categoryFaqs]) => (
                <div key={category} className="mb-10">
                  <div className="flex items-center mb-4 gap-2">
                    <HelpCircle className="h-5 w-5 text-primary" />
                    <h2 className="text-xl font-semibold">{category}</h2>
                  </div>
                  <Card>
                    <CardContent className="p-0">
                      <Accordion type="single" collapsible className="w-full">
                        {categoryFaqs.map((faq) => (
                          <AccordionItem key={faq.id} value={faq.id} className="border-b last:border-b-0 px-1">
                            <div className="flex justify-between items-center w-full">
                              <AccordionTrigger className="px-4 py-4 hover:bg-gray-50 transition-all rounded-md data-[state=open]:bg-gray-50 flex-1">
                                <span className="text-left font-medium text-gray-800">{faq.question}</span>
                              </AccordionTrigger>
                              {isAdmin && (
                                <div className="flex space-x-1 pr-4" onClick={(e) => e.stopPropagation()}>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openEditDialog(faq);
                                    }}
                                    className="h-8 w-8 rounded-full"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      deleteFAQ(faq.id);
                                    }}
                                    className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </div>
                            <AccordionContent className="px-6 pb-6 pt-2 text-gray-700">
                              <div className="prose max-w-none">
                                {faq.answer.split('\n').map((paragraph, index) => (
                                  <p key={index} className="mb-3 last:mb-0">{paragraph}</p>
                                ))}
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    </CardContent>
                  </Card>
                </div>
              ))
            ) : searchQuery ? (
              <Card className="p-8 text-center">
                <p className="text-lg text-gray-500">No FAQs match your search. Try different keywords.</p>
              </Card>
            ) : (
              <Card className="p-8 text-center">
                <p className="text-lg text-gray-500">No FAQs available at the moment.</p>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Add/Edit FAQ Dialog */}
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedFAQ ? 'Edit FAQ' : 'Add New FAQ'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="question">Question</Label>
              <Input
                id="question"
                name="question"
                value={formData.question}
                onChange={handleInputChange}
                placeholder="Enter the question"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="answer">Answer</Label>
              <Textarea
                id="answer"
                name="answer"
                value={formData.answer}
                onChange={handleInputChange}
                placeholder="Enter the answer"
                rows={6}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={handleSelectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Products">Products</SelectItem>
                  <SelectItem value="Usage">Usage</SelectItem>
                  <SelectItem value="Shipping">Shipping</SelectItem>
                  <SelectItem value="Legal">Legal</SelectItem>
                  <SelectItem value="General">General</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_published"
                checked={formData.is_published}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="is_published">Published</Label>
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={() => setOpenDialog(false)}>
                Cancel
              </Button>
              <Button onClick={saveFAQ}>
                {selectedFAQ ? 'Update' : 'Add'} FAQ
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FAQPage;

