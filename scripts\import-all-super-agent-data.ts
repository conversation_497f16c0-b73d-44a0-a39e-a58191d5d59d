#!/usr/bin/env tsx
/**
 * Import ALL Super Agent enriched data - both Agent #1 and Agent #2
 * This is the comprehensive import for all batches
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync, existsSync } from 'fs';
import <PERSON> from 'papaparse';
import { join } from 'path';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

console.log('🔑 Using service role key for admin operations...');

const supabase = createClient(supabaseUrl, supabaseKey);

interface EnrichedSeedData {
  product_name: string;
  description: string;
  thc_level: string;
  cbd_level: string;
  flowering_time: string;
  effect: string;
  genetics: string;
  difficulty?: string;
  recommend_activation?: string;
  market_appeal_reason?: string;
}

// All the batch files to import
const BATCH_FILES = [
  // Agent #1 (Active Products)
  { file: 'enriched_active_seeds.csv', agent: 1, type: 'active', batch: 1 },
  { file: 'enriched_active_seeds_batch2.csv', agent: 1, type: 'active', batch: 2 },
  { file: 'enriched_active_seeds_batch3.csv', agent: 1, type: 'active', batch: 3 },
  { file: 'enriched_active_seeds_final_batch.csv', agent: 1, type: 'active', batch: 4 },
  { file: 'final_enriched_active_seeds.csv', agent: 1, type: 'active', batch: 5 },

  // Agent #2 (Premium Inactive)
  { file: 'enriched_premium_cannabis_seeds.csv', agent: 2, type: 'premium', batch: 1 },
  { file: 'batch_2_enriched_premium_cannabis_seeds.csv', agent: 2, type: 'premium', batch: 2 }
];

async function findProductByName(productName: string) {
  // Try exact match first
  let { data: products } = await supabase
    .from('products')
    .select('id, name, is_active')
    .ilike('name', productName)
    .limit(1);

  if (products && products.length > 0) {
    return products[0];
  }

  // Try fuzzy matching
  const cleanName = productName
    .replace(/Royal Queen of Seeds\s*/i, '')
    .replace(/Barneys? Farm\s*/i, '')
    .replace(/420 Fast ?Buds:?\s*/i, '')
    .replace(/Perfect Tree Seeds:?\s*/i, '')
    .replace(/Serious Seeds:?\s*/i, '')
    .replace(/Paradise Seeds\s*/i, '')
    .replace(/The Cali Connection's\s*/i, '')
    .replace(/Humboldt Seed Company\s*/i, '')
    .replace(/\(Feminised?\s*x?\d*\)/i, '')
    .replace(/\(\d+\s*Feminised?\)/i, '')
    .replace(/\(Feminised?\)/i, '')
    .replace(/\(\d+\s*Female?\)/i, '')
    .replace(/\(x?\d+\s*Female?\)/i, '')
    .replace(/x\d+/i, '')
    .trim();

  ({ data: products } = await supabase
    .from('products')
    .select('id, name, is_active')
    .ilike('name', `%${cleanName}%`)
    .limit(5));

  if (products && products.length > 0) {
    return products[0]; // Return best match
  }

  return null;
}

async function importBatch(batchInfo: any) {
  const { file, agent, type, batch } = batchInfo;
  console.log(`\n🌱 Importing Agent #${agent} ${type} batch ${batch}: ${file}`);

  try {
    const csvPath = join(process.cwd(), 'docs', 'super_agent', file);

    if (!existsSync(csvPath)) {
      console.log(`   ⚠️  File not found: ${file} - skipping`);
      return { imported: 0, skipped: 0, errors: 1 };
    }

    const csvContent = readFileSync(csvPath, 'utf8');

    const parseResult = Papa.parse(csvContent, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.trim()
    });

    if (parseResult.errors.length > 0) {
      console.error('❌ CSV parsing errors:', parseResult.errors);
      return { imported: 0, skipped: 0, errors: 1 };
    }

    const records: EnrichedSeedData[] = parseResult.data as EnrichedSeedData[];
    console.log(`📄 Found ${records.length} products in this batch\n`);

    let imported = 0;
    let skipped = 0;
    let errors = 0;

    for (const record of records) {
      if (!record.product_name || record.product_name.trim() === '') {
        continue; // Skip empty rows
      }

      console.log(`🔍 Processing: ${record.product_name}`);

      try {
        const product = await findProductByName(record.product_name);

        if (!product) {
          console.log(`   ⚠️  No matching product found - skipping`);
          skipped++;
          continue;
        }

        console.log(`   ✅ Matched to: ${product.name} (${product.is_active ? 'Active' : 'Inactive'})`);

        // Update product description if it's good quality
        if (record.description && record.description.trim().length > 100) {
          const { error: descError } = await supabase
            .from('products')
            .update({
              description: record.description,
              updated_at: new Date().toISOString()
            })
            .match({ id: product.id });

          if (descError) {
            console.log(`   ⚠️  Could not update description: ${descError.message}`);
          } else {
            console.log(`   ✅ Updated description (${record.description.length} chars)`);
          }
        }

        // Insert/update seed attributes
        const { data: existingAttr } = await supabase
          .from('seed_product_attributes')
          .select('id')
          .eq('product_id', product.id)
          .single();

        const attributeData = {
          product_id: product.id,
          seed_type: (record.genetics?.includes('Auto') || record.product_name.toLowerCase().includes('auto') ? 'Autoflower' : 'Feminised').substring(0, 50),
          flowering_time: record.flowering_time?.substring(0, 50),
          thc_level: record.thc_level?.substring(0, 50),
          cbd_level: record.cbd_level?.substring(0, 50),
          effect: record.effect?.substring(0, 50),
          seed_family: record.genetics?.substring(0, 50), // Use seed_family instead of genetics
          is_manually_verified: true
        };

        let attrError;
        if (existingAttr) {
          const { error } = await supabase
            .from('seed_product_attributes')
            .update(attributeData)
            .eq('product_id', product.id);
          attrError = error;
        } else {
          const { error } = await supabase
            .from('seed_product_attributes')
            .insert(attributeData);
          attrError = error;
        }

        if (attrError) {
          console.error(`   ❌ Error updating attributes: ${attrError.message}`);
          errors++;
          continue;
        }

        // For Agent #2 premium products, mark for potential activation
        if (agent === 2 && record.recommend_activation === 'Yes') {
          console.log(`   💎 Premium product marked for activation consideration`);
        }

        console.log(`   ✅ Successfully imported enriched data`);
        imported++;

      } catch (err) {
        console.error(`   ❌ Error processing ${record.product_name}:`, err);
        errors++;
      }
    }

    return { imported, skipped, errors };

  } catch (err) {
    console.error(`❌ Batch import failed:`, err);
    return { imported: 0, skipped: 0, errors: 1 };
  }
}

async function importAllSuperAgentData() {
  console.log('🚀 COMPREHENSIVE SUPER AGENT DATA IMPORT\n');
  console.log('📊 Importing all batches from both agents...\n');

  let totalImported = 0;
  let totalSkipped = 0;
  let totalErrors = 0;

  const agent1Results = { imported: 0, skipped: 0, errors: 0 };
  const agent2Results = { imported: 0, skipped: 0, errors: 0 };

  for (const batchInfo of BATCH_FILES) {
    const result = await importBatch(batchInfo);

    totalImported += result.imported;
    totalSkipped += result.skipped;
    totalErrors += result.errors;

    if (batchInfo.agent === 1) {
      agent1Results.imported += result.imported;
      agent1Results.skipped += result.skipped;
      agent1Results.errors += result.errors;
    } else {
      agent2Results.imported += result.imported;
      agent2Results.skipped += result.skipped;
      agent2Results.errors += result.errors;
    }
  }

  console.log('\n🎉 COMPREHENSIVE IMPORT SUMMARY:');
  console.log('\n📊 AGENT #1 (Active Products):');
  console.log(`   ✅ Successfully imported: ${agent1Results.imported}`);
  console.log(`   ⚠️  Skipped (no match): ${agent1Results.skipped}`);
  console.log(`   ❌ Errors: ${agent1Results.errors}`);

  console.log('\n💎 AGENT #2 (Premium Products):');
  console.log(`   ✅ Successfully imported: ${agent2Results.imported}`);
  console.log(`   ⚠️  Skipped (no match): ${agent2Results.skipped}`);
  console.log(`   ❌ Errors: ${agent2Results.errors}`);

  console.log('\n🏆 TOTAL RESULTS:');
  console.log(`   ✅ Successfully imported: ${totalImported}`);
  console.log(`   ⚠️  Skipped (no match): ${totalSkipped}`);
  console.log(`   ❌ Errors: ${totalErrors}`);
  console.log(`   📄 Total processed: ${totalImported + totalSkipped + totalErrors}`);

  if (totalImported > 0) {
    console.log('\n🎉 MISSION ACCOMPLISHED!');
    console.log('   📝 Professional descriptions imported');
    console.log('   🌱 Complete seed attributes added');
    console.log('   🎯 Filtering system ready');
    console.log('   💰 Catalog value significantly increased');
    console.log('\n💡 Next steps:');
    console.log('   1. Test product pages with new descriptions');
    console.log('   2. Verify seed filtering system');
    console.log('   3. Consider activating premium products');
    console.log('   4. Present results to client');
  }
}

// Run the comprehensive import
importAllSuperAgentData().catch(console.error);
