const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');
const path = require('path');

// Input and output files
const inputFile = path.join('..', '..', 'docs', 'catalog_products.csv');
const outputFile = 'transformed_variants_all_prices.csv';
const variantsInputFile = 'transformed_variants_clean.csv';

// Create a custom CSV parser to handle the complex format
const customCsvParser = () => {
  return csv({
    separator: ',',
    escape: '"',
    quote: '"',
    headers: [
      'handleId', 'fieldType', 'name', 'description', 'productImageUrl', 'collection',
      'sku', 'ribbon', 'price', 'surcharge', 'visible', 'discountMode', 'discountValue',
      'inventory', 'weight', 'cost', 'productOptionName1', 'productOptionType1',
      'productOptionDescription1', 'productOptionName2', 'productOptionType2',
      'productOptionDescription2', 'productOptionName3', 'productOptionType3',
      'productOptionDescription3', 'productOptionName4', 'productOptionType4',
      'productOptionDescription4', 'productOptionName5', 'productOptionType5',
      'productOptionDescription5', 'productOptionName6', 'productOptionType6',
      'productOptionDescription6', 'additionalInfoTitle1', 'additionalInfoDescription1',
      'additionalInfoTitle2', 'additionalInfoDescription2', 'additionalInfoTitle3',
      'additionalInfoDescription3', 'additionalInfoTitle4', 'additionalInfoDescription4',
      'additionalInfoTitle5', 'additionalInfoDescription5', 'additionalInfoTitle6',
      'additionalInfoDescription6', 'customTextField1', 'customTextCharLimit1',
      'customTextMandatory1', 'customTextField2', 'customTextCharLimit2',
      'customTextMandatory2', 'brand', 'productOptionValue1'
    ]
  });
};

// Process the CSV file
console.log(`Reading products from ${inputFile}...`);

// Store products and their variants
const products = {};
const priceVariations = {};

// First pass: collect all products with Pack Size options
fs.createReadStream(inputFile)
  .pipe(customCsvParser())
  .on('data', (row) => {
    if (row.fieldType === 'Product' && row.productOptionName1 === 'Pack Size') {
      products[row.handleId] = {
        name: row.name,
        basePrice: parseFloat(row.price) || 0,
        options: row.productOptionDescription1 ? row.productOptionDescription1.split(';') : [],
        variants: []
      };
    }
  })
  .on('end', () => {
    console.log(`Found ${Object.keys(products).length} products with Pack Size options`);

    // Second pass: collect all variants
    fs.createReadStream(inputFile)
      .pipe(customCsvParser())
      .on('data', (row) => {
        if (row.fieldType === 'Variant' && products[row.handleId]) {
          const product = products[row.handleId];
          const optionValue = row.productOptionValue1;
          const surcharge = parseFloat(row.surcharge) || 0;

          console.log(`Found variant for ${product.name}: ${optionValue} with surcharge ${surcharge}`);

          if (optionValue) {
            const totalPrice = product.basePrice + surcharge;

            // Add to variants array
            product.variants.push({
              option: optionValue,
              surcharge: surcharge,
              totalPrice: totalPrice
            });

            // Add to price variations object
            if (!priceVariations[row.handleId]) {
              priceVariations[row.handleId] = {};
            }
            priceVariations[row.handleId][optionValue] = totalPrice;
          }
        }
      })
      .on('end', () => {
        // Include all products with variants
        const productsWithVariations = Object.entries(products)
          .filter(([id, product]) => {
            // Include all products with variants
            return product.variants.length > 0;
          })
          .map(([id, product]) => {
            return {
              id,
              name: product.name,
              basePrice: product.basePrice,
              variants: product.variants
            };
          });

        console.log(`Found ${productsWithVariations.length} products with price variations out of ${Object.keys(products).length} products with Pack Size options`);

        // Output the results
        console.log('\nProducts with price variations:');
        productsWithVariations.forEach(product => {
          console.log(`\n${product.name} (${product.id}) - Base Price: £${product.basePrice}`);
          product.variants.forEach(variant => {
            console.log(`  ${variant.option}: £${variant.totalPrice} (surcharge: £${variant.surcharge})`);
          });
        });

        // Generate code for fix_variant_prices.js
        console.log('\n\nCode for fix_variant_prices.js:');
        console.log('const priceVariations = {');
        productsWithVariations.forEach(product => {
          console.log(`  // ${product.name}`);
          console.log(`  '${product.id}': {`);
          product.variants.forEach(variant => {
            console.log(`    '${variant.option}': ${variant.totalPrice},`);
          });
          console.log('  },');
        });
        console.log('};');

        // Now update the variants CSV file with the correct prices
        const csvWriter = createObjectCsvWriter({
          path: outputFile,
          header: [
            { id: 'id', title: 'id' },
            { id: 'product_id', title: 'product_id' },
            { id: 'variant_name', title: 'variant_name' },
            { id: 'sku', title: 'sku' },
            { id: 'price', title: 'price' },
            { id: 'sale_price', title: 'sale_price' },
            { id: 'stock_quantity', title: 'stock_quantity' },
            { id: 'in_stock', title: 'in_stock' },
            { id: 'image', title: 'image' },
            { id: 'option_combination', title: 'option_combination' },
            { id: 'is_active', title: 'is_active' },
            { id: 'created_at', title: 'created_at' },
            { id: 'updated_at', title: 'updated_at' }
          ]
        });

        // Read the variants CSV file and update the prices
        const variants = [];
        fs.createReadStream(variantsInputFile)
          .pipe(csv())
          .on('data', (row) => {
            // Check if this product has price variations
            if (priceVariations[row.product_id]) {
              // Get the variant name (e.g., "3 Pack", "5 Pack", "10 Pack")
              const variantName = row.variant_name;

              // Check if we have a specific price for this variant
              if (priceVariations[row.product_id][variantName] !== undefined) {
                // Update the price
                row.price = priceVariations[row.product_id][variantName];
                console.log(`Updated price for ${row.product_id} - ${variantName} to £${row.price}`);
              }
            }

            variants.push(row);
          })
          .on('end', async () => {
            console.log(`Read ${variants.length} variants from CSV`);

            // Write variants to CSV
            await csvWriter.writeRecords(variants);
            console.log(`Wrote ${variants.length} variants to ${outputFile}`);

            console.log('Price update complete!');
          });
      });
  });
