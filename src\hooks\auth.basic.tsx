import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

// Helper function to clear auth storage
export const clearAuthStorage = () => {
  try {
    // Clear all Supabase auth storage
    localStorage.removeItem('supabase.auth.token');
    sessionStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('sb-refresh-token');
    sessionStorage.removeItem('sb-refresh-token');
    localStorage.removeItem('sb-access-token');
    sessionStorage.removeItem('sb-access-token');
    console.log('Auth storage cleared successfully');
    return true;
  } catch (error) {
    console.error('Error clearing auth storage:', error);
    return false;
  }
};

interface Profile {
  id: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  is_admin: boolean;
  [key: string]: any;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  isAdmin: boolean;
  isLoading: boolean;
  isInitializing: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: any }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error?: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitializing, setIsInitializing] = useState(true);

  // Simple function to fetch user profile
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return data as Profile;
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    // Function to initialize auth
    const initAuth = async () => {
      try {
        setIsInitializing(true);
        // Get current session
        const { data } = await supabase.auth.getSession();
        const currentSession = data.session;
        
        if (currentSession?.user) {
          setSession(currentSession);
          setUser(currentSession.user);
          
          const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
          if (knownAdminUserIds.includes(currentSession.user.id)) {
            console.log('Admin access granted based on hardcoded user ID');
            setIsAdmin(true);
            
            // Create a simple profile object
            setProfile({
              id: currentSession.user.id,
              email: currentSession.user.email,
              is_admin: true
            });
          } else {
            // Try to fetch profile from database
            const userProfile = await fetchUserProfile(currentSession.user.id);
            if (userProfile) {
              setProfile(userProfile);
              setIsAdmin(userProfile.is_admin === true);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
        setIsInitializing(false);
      }
    };

    // Set up auth state change listener
    const { data } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      console.log('Auth state changed:', event);
      
      if (currentSession?.user) {
        setSession(currentSession);
        setUser(currentSession.user);
        
        const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
        if (knownAdminUserIds.includes(currentSession.user.id)) {
          console.log('Admin access granted based on hardcoded user ID');
          setIsAdmin(true);
          
          // Create a simple profile object
          setProfile({
            id: currentSession.user.id,
            email: currentSession.user.email,
            is_admin: true
          });
        } else {
          // Try to fetch profile from database
          const userProfile = await fetchUserProfile(currentSession.user.id);
          if (userProfile) {
            setProfile(userProfile);
            setIsAdmin(userProfile.is_admin === true);
          }
        }
      } else {
        // Clear state on sign out
        setSession(null);
        setUser(null);
        setProfile(null);
        setIsAdmin(false);
      }
    });

    // Initialize auth
    initAuth();
    
    // Cleanup function
    return () => {
      data.subscription.unsubscribe();
    };
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) {
        console.error('Error signing in:', error);
        return { error };
      }
      
      return {};
    } catch (error) {
      console.error('Exception during sign in:', error);
      return { error };
    }
  };
  
  // Sign up function
  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
          emailRedirectTo: window.location.origin
        }
      });
      
      if (error) {
        console.error('Error signing up:', error);
        return { error };
      }
      
      return { data };
    } catch (error) {
      console.error('Exception during sign up:', error);
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      clearAuthStorage();
      
      // Clear state
      setSession(null);
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      
      toast({
        title: 'Signed out',
        description: 'You have been signed out successfully',
      });
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: 'Error',
        description: 'Failed to sign out',
        variant: 'destructive',
      });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        isAdmin,
        isLoading,
        isInitializing,
        signIn,
        signUp,
        signOut
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
