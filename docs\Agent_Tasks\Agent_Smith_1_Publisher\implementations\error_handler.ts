/**
 * Error Handler Implementation
 * 
 * This file implements the ErrorHandler interface for the Social Media Publishing System.
 * It handles error logging, categorization, and management for all platform operations.
 */

import { ErrorHandler } from './social_media_publisher';
import { ErrorType } from './social_media_types';

/**
 * Interface for error logging service
 */
interface LoggingService {
  debug(message: string, context?: Record<string, any>): void;
  info(message: string, context?: Record<string, any>): void;
  warn(message: string, context?: Record<string, any>, error?: Error): void;
  error(message: string, context?: Record<string, any>, error?: Error): void;
}

/**
 * Custom error class for social media publishing errors
 */
export class SocialMediaPublishingError extends Error {
  readonly type: ErrorType;
  readonly platform: string;
  readonly operation: string;
  readonly originalError?: any;
  readonly retryable: boolean;
  
  constructor(
    message: string,
    type: ErrorType,
    platform: string,
    operation: string,
    originalError?: any,
    retryable: boolean = false
  ) {
    super(message);
    this.name = 'SocialMediaPublishingError';
    this.type = type;
    this.platform = platform;
    this.operation = operation;
    this.originalError = originalError;
    this.retryable = retryable;
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SocialMediaPublishingError);
    }
  }
}

/**
 * Implementation of the ErrorHandler interface
 */
export class SocialMediaErrorHandler implements ErrorHandler {
  private logger: LoggingService;
  private notificationService?: (error: SocialMediaPublishingError) => void;
  
  /**
   * Create a new SocialMediaErrorHandler
   * @param logger The logging service
   * @param notificationService Optional notification service for critical errors
   */
  constructor(
    logger: LoggingService,
    notificationService?: (error: SocialMediaPublishingError) => void
  ) {
    this.logger = logger;
    this.notificationService = notificationService;
  }
  
  /**
   * Handle an error from a social media API
   * @param error The error to handle
   * @param platform The platform where the error occurred
   * @param operation The operation that caused the error
   * @returns Never returns, always throws an appropriate error
   */
  handle(error: any, platform: string, operation: string): never {
    // Categorize the error
    const { type, message, retryable } = this.categorizeError(error, platform, operation);
    
    // Log the error
    this.logError(error, {
      platform,
      operation,
      errorType: type,
      retryable
    });
    
    // Create a standardized error
    const publishingError = new SocialMediaPublishingError(
      message,
      type,
      platform,
      operation,
      error,
      retryable
    );
    
    // Notify if critical
    if (this.notificationService && this.isCriticalError(publishingError)) {
      this.notificationService(publishingError);
    }
    
    // Throw the error
    throw publishingError;
  }
  
  /**
   * Log an error
   * @param error The error to log
   * @param context Additional context for the error
   */
  logError(error: any, context?: Record<string, any>): void {
    // Sanitize context to remove sensitive information
    const sanitizedContext = context ? this.sanitizeContext(context) : undefined;
    
    // Extract error details
    let errorMessage = 'Unknown error';
    let errorStack: string | undefined;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = JSON.stringify(error);
    }
    
    // Log with appropriate level
    const errorType = context?.errorType || 'generic';
    
    switch (errorType) {
      case 'auth':
        this.logger.warn(
          `Authentication error: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
        break;
      case 'rate_limit':
        this.logger.warn(
          `Rate limit error: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
        break;
      case 'content_policy':
        this.logger.warn(
          `Content policy violation: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
        break;
      case 'media_format':
        this.logger.warn(
          `Media format error: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
        break;
      case 'network':
        this.logger.error(
          `Network error: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
        break;
      default:
        this.logger.error(
          `Error in social media operation: ${errorMessage}`,
          sanitizedContext,
          error instanceof Error ? error : undefined
        );
    }
  }
  
  /**
   * Categorize an error
   * @param error The error to categorize
   * @param platform The platform where the error occurred
   * @param operation The operation that caused the error
   * @returns Error categorization
   */
  private categorizeError(
    error: any,
    platform: string,
    operation: string
  ): { type: ErrorType; message: string; retryable: boolean } {
    // Default values
    let type: ErrorType = 'generic';
    let message = 'An unknown error occurred';
    let retryable = false;
    
    // Handle axios errors
    if (error.isAxiosError) {
      const response = error.response;
      
      if (response) {
        const status = response.status;
        
        // Handle based on status code
        if (status === 401 || status === 403) {
          type = 'auth';
          message = `Authentication failed for ${platform} ${operation}: ${response.data?.message || 'Unauthorized'}`;
          retryable = true;
        } else if (status === 429) {
          type = 'rate_limit';
          message = `Rate limit exceeded for ${platform} ${operation}`;
          retryable = true;
        } else if (status >= 500) {
          type = 'network';
          message = `${platform} server error during ${operation}: ${response.data?.message || 'Server error'}`;
          retryable = true;
        } else {
          // Try to extract more specific information from response data
          const data = response.data;
          
          if (data) {
            message = data.message || data.error?.message || `Error during ${platform} ${operation}`;
            
            // Look for specific error codes or messages
            const errorCode = data.code || data.error?.code;
            const errorType = data.error_type || data.error?.type;
            
            if (this.isAuthError(errorCode, errorType, message)) {
              type = 'auth';
              retryable = true;
            } else if (this.isRateLimitError(errorCode, errorType, message)) {
              type = 'rate_limit';
              retryable = true;
            } else if (this.isContentPolicyError(errorCode, errorType, message)) {
              type = 'content_policy';
              retryable = false;
            } else if (this.isMediaFormatError(errorCode, errorType, message)) {
              type = 'media_format';
              retryable = false;
            }
          }
        }
      } else if (error.request) {
        // Request was made but no response received
        type = 'network';
        message = `Network error during ${platform} ${operation}: No response received`;
        retryable = true;
      } else {
        // Error in setting up the request
        message = `Error setting up request for ${platform} ${operation}: ${error.message}`;
      }
    } else if (error instanceof Error) {
      // Handle standard errors
      message = error.message;
      
      // Try to categorize based on error message
      if (this.isAuthError(undefined, undefined, message)) {
        type = 'auth';
        retryable = true;
      } else if (this.isRateLimitError(undefined, undefined, message)) {
        type = 'rate_limit';
        retryable = true;
      } else if (this.isContentPolicyError(undefined, undefined, message)) {
        type = 'content_policy';
        retryable = false;
      } else if (this.isMediaFormatError(undefined, undefined, message)) {
        type = 'media_format';
        retryable = false;
      }
    }
    
    return { type, message, retryable };
  }
  
  /**
   * Check if an error is an authentication error
   * @param errorCode The error code
   * @param errorType The error type
   * @param message The error message
   * @returns Whether the error is an authentication error
   */
  private isAuthError(
    errorCode?: string | number,
    errorType?: string,
    message?: string
  ): boolean {
    const authErrorCodes = [
      401, 403, '401', '403',
      'invalid_token', 'expired_token', 'unauthorized',
      'OAuthException', 'invalid_auth', 'auth_error'
    ];
    
    const authErrorMessages = [
      'unauthorized', 'unauthenticated', 'auth', 'authentication',
      'token expired', 'invalid token', 'access token', 'refresh token'
    ];
    
    // Check error code
    if (errorCode && authErrorCodes.includes(errorCode.toString().toLowerCase())) {
      return true;
    }
    
    // Check error type
    if (errorType && errorType.toLowerCase().includes('auth')) {
      return true;
    }
    
    // Check message
    if (message) {
      const lowerMessage = message.toLowerCase();
      return authErrorMessages.some(term => lowerMessage.includes(term));
    }
    
    return false;
  }
  
  /**
   * Check if an error is a rate limit error
   * @param errorCode The error code
   * @param errorType The error type
   * @param message The error message
   * @returns Whether the error is a rate limit error
   */
  private isRateLimitError(
    errorCode?: string | number,
    errorType?: string,
    message?: string
  ): boolean {
    const rateLimitErrorCodes = [
      429, '429',
      'rate_limit_exceeded', 'rate_limit', 'too_many_requests',
      'quota_exceeded', 'usage_limit_exceeded'
    ];
    
    const rateLimitErrorMessages = [
      'rate limit', 'too many requests', 'quota exceeded',
      'usage limit', 'throttled', 'too many calls'
    ];
    
    // Check error code
    if (errorCode && rateLimitErrorCodes.includes(errorCode.toString().toLowerCase())) {
      return true;
    }
    
    // Check error type
    if (errorType && (
      errorType.toLowerCase().includes('rate') ||
      errorType.toLowerCase().includes('limit') ||
      errorType.toLowerCase().includes('throttle')
    )) {
      return true;
    }
    
    // Check message
    if (message) {
      const lowerMessage = message.toLowerCase();
      return rateLimitErrorMessages.some(term => lowerMessage.includes(term));
    }
    
    return false;
  }
  
  /**
   * Check if an error is a content policy error
   * @param errorCode The error code
   * @param errorType The error type
   * @param message The error message
   * @returns Whether the error is a content policy error
   */
  private isContentPolicyError(
    errorCode?: string | number,
    errorType?: string,
    message?: string
  ): boolean {
    const contentPolicyErrorCodes = [
      'content_violation', 'policy_violation', 'community_guidelines',
      'inappropriate_content', 'prohibited_content'
    ];
    
    const contentPolicyErrorMessages = [
      'content policy', 'community guidelines', 'terms of service',
      'prohibited content', 'inappropriate', 'violates', 'violation'
    ];
    
    // Check error code
    if (errorCode && contentPolicyErrorCodes.includes(errorCode.toString().toLowerCase())) {
      return true;
    }
    
    // Check error type
    if (errorType && (
      errorType.toLowerCase().includes('content') ||
      errorType.toLowerCase().includes('policy') ||
      errorType.toLowerCase().includes('guideline')
    )) {
      return true;
    }
    
    // Check message
    if (message) {
      const lowerMessage = message.toLowerCase();
      return contentPolicyErrorMessages.some(term => lowerMessage.includes(term));
    }
    
    return false;
  }
  
  /**
   * Check if an error is a media format error
   * @param errorCode The error code
   * @param errorType The error type
   * @param message The error message
   * @returns Whether the error is a media format error
   */
  private isMediaFormatError(
    errorCode?: string | number,
    errorType?: string,
    message?: string
  ): boolean {
    const mediaFormatErrorCodes = [
      'invalid_media', 'media_error', 'format_error',
      'invalid_image', 'invalid_video', 'media_too_large'
    ];
    
    const mediaFormatErrorMessages = [
      'media format', 'file format', 'image format', 'video format',
      'too large', 'invalid media', 'unsupported format'
    ];
    
    // Check error code
    if (errorCode && mediaFormatErrorCodes.includes(errorCode.toString().toLowerCase())) {
      return true;
    }
    
    // Check error type
    if (errorType && (
      errorType.toLowerCase().includes('media') ||
      errorType.toLowerCase().includes('format') ||
      errorType.toLowerCase().includes('file')
    )) {
      return true;
    }
    
    // Check message
    if (message) {
      const lowerMessage = message.toLowerCase();
      return mediaFormatErrorMessages.some(term => lowerMessage.includes(term));
    }
    
    return false;
  }
  
  /**
   * Check if an error is critical
   * @param error The error to check
   * @returns Whether the error is critical
   */
  private isCriticalError(error: SocialMediaPublishingError): boolean {
    // Consider network errors and uncategorized errors as critical
    return error.type === 'network' || error.type === 'generic';
  }
  
  /**
   * Sanitize context to remove sensitive information
   * @param context The context to sanitize
   * @returns Sanitized context
   */
  private sanitizeContext(context: Record<string, any>): Record<string, any> {
    // Create a deep copy to avoid modifying the original
    const sanitized = JSON.parse(JSON.stringify(context));
    
    // List of sensitive fields to redact
    const sensitiveFields = [
      'token', 'accessToken', 'refreshToken', 'apiKey', 'apiSecret',
      'clientSecret', 'password', 'secret', 'authorization'
    ];
    
    // Recursive function to sanitize objects
    const sanitizeObject = (obj: Record<string, any>) => {
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        
        if (typeof value === 'object' && value !== null) {
          sanitizeObject(value);
        } else if (
          sensitiveFields.some(field => lowerKey.includes(field.toLowerCase())) &&
          typeof value === 'string'
        ) {
          obj[key] = '[REDACTED]';
        }
      }
    };
    
    sanitizeObject(sanitized);
    return sanitized;
  }
}
