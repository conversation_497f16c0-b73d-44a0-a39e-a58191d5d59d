// Script to clear the products table
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in environment variables');
  console.log('Available environment variables:', Object.keys(process.env).filter(key => key.includes('VITE')));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearProducts() {
  try {
    console.log('Clearing products table...');
    
    // First check if the product_variants table exists and clear it if it does
    // This prevents foreign key constraint errors
    try {
      const { error: variantError } = await supabase
        .from('product_variants')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
      
      if (variantError) {
        console.log('Note: product_variants table might not exist yet or another error occurred:', variantError.message);
      } else {
        console.log('Cleared product_variants table');
      }
    } catch (e) {
      console.log('Note: product_variants table might not exist yet');
    }
    
    // Now clear the products table
    const { error } = await supabase
      .from('products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
    
    if (error) {
      throw error;
    }
    
    console.log('Successfully cleared products table');
  } catch (error) {
    console.error('Error clearing products table:', error);
  }
}

// Run the function
clearProducts();
