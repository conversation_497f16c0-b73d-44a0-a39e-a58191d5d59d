import { ShippingMethod as BaseShippingMethod } from '@/components/checkout/ShippingMethodSelector';

// Extend the base shipping method type to include our enhanced properties
type ShippingMethod = BaseShippingMethod & {
  description: string;
  estimatedDays: string;
};
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Truck, Clock, Zap, Check } from 'lucide-react';

interface EnhancedShippingMethodSelectorProps {
  methods: ShippingMethod[];
  selectedMethodId: string;
  onSelect: (methodId: string) => void;
}

export function EnhancedShippingMethodSelector({
  methods,
  selectedMethodId,
  onSelect
}: EnhancedShippingMethodSelectorProps) {
  // Get icon component based on method icon name
  const getMethodIcon = (iconName: string) => {
    switch (iconName) {
      case 'standard':
        return <Truck className="h-5 w-5" />;
      case 'express':
        return <Zap className="h-5 w-5" />;
      case 'nextDay':
        return <Clock className="h-5 w-5" />;
      default:
        return <Truck className="h-5 w-5" />;
    }
  };

  // Only filter out invalid methods, respect the active/inactive status from the database
  const filteredMethods = methods.filter(method => method && method.id);
  
  return (
    <div className="space-y-3">
      {filteredMethods.map((method) => (
        <Card
          key={method.id}
          className={`relative border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
            selectedMethodId === method.id
              ? 'border-sage-500 bg-sage-50'
              : 'border-gray-200 hover:border-sage-300'
          }`}
          onClick={() => onSelect(method.id)}
        >
          {selectedMethodId === method.id && (
            <div className="absolute top-4 right-4 bg-sage-500 text-white rounded-full p-1">
              <Check className="h-4 w-4" />
            </div>
          )}
          
          <CardContent className="p-4 flex items-center">
            <div className={`p-3 rounded-full mr-4 ${
              selectedMethodId === method.id
                ? 'bg-sage-100 text-sage-600'
                : 'bg-gray-100 text-gray-600'
            }`}>
              {getMethodIcon(method.icon)}
            </div>
            
            <div className="flex-grow">
              <div className="flex items-center">
                <h3 className="font-medium">{method.name}</h3>
                <Badge 
                  className="ml-2 bg-sage-100 text-sage-700 border-sage-200"
                >
                  £{method.price.toFixed(2)}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">{method.description}</p>
              <p className="text-xs text-gray-500 mt-1">
                <Clock className="inline h-3 w-3 mr-1" />
                {method.estimatedDays}
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
