import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Trash2, ArrowUp, ArrowDown, Wand2, Loader2, Image } from 'lucide-react';
import { LocalImageUploader } from './LocalImageUploader';
import { cn } from '@/lib/utils';

interface FixedProductImageManagerProps {
  initialMainImage?: string;
  initialAdditionalImages?: string[];
  onImagesChange: (mainImage: string, additionalImages: string[]) => void;
  onFindImagesWithAI?: () => void;
  isFindingImages?: boolean;
}

export function FixedProductImageManager({
  initialMainImage = '',
  initialAdditionalImages = [],
  onImagesChange,
  onFindImagesWithAI,
  isFindingImages = false
}: FixedProductImageManagerProps) {
  // Maintain local state for images
  const [mainImage, setMainImage] = useState<string>(initialMainImage);
  const [additionalImages, setAdditionalImages] = useState<string[]>(initialAdditionalImages);

  // Update local state when props change
  useEffect(() => {
    setMainImage(initialMainImage);
    setAdditionalImages(initialAdditionalImages);
  }, [initialMainImage, initialAdditionalImages]);

  // Function to add a new image
  const addImage = (url: string) => {
    // If there's no main image, set this as the main image
    if (!mainImage) {
      setMainImage(url);
      onImagesChange(url, additionalImages);
    } else {
      // Otherwise add to additional images
      const newAdditionalImages = [...additionalImages, url];
      setAdditionalImages(newAdditionalImages);
      onImagesChange(mainImage, newAdditionalImages);
    }
  };

  // Function to remove an image from additional images
  const removeImage = (index: number) => {
    const newAdditionalImages = [...additionalImages];
    newAdditionalImages.splice(index, 1);
    setAdditionalImages(newAdditionalImages);
    onImagesChange(mainImage, newAdditionalImages);
  };

  // Function to remove the main image
  const removeMainImage = () => {
    // If we have additional images, make the first one the main image
    if (additionalImages.length > 0) {
      const newMainImage = additionalImages[0];
      const newAdditionalImages = additionalImages.slice(1);
      setMainImage(newMainImage);
      setAdditionalImages(newAdditionalImages);
      onImagesChange(newMainImage, newAdditionalImages);
    } else {
      // Otherwise just clear the main image
      setMainImage('');
      onImagesChange('', []);
    }
  };

  // Function to set an additional image as the main image
  const setAsMainImage = (index: number) => {
    const imageToMakeMain = additionalImages[index];
    
    // Create new additional images array without the selected image
    const newAdditionalImages = additionalImages.filter((_, i) => i !== index);
    
    // If there's a current main image, add it to additional images
    if (mainImage) {
      newAdditionalImages.push(mainImage);
    }
    
    // Update state
    setMainImage(imageToMakeMain);
    setAdditionalImages(newAdditionalImages);
    
    // Notify parent
    onImagesChange(imageToMakeMain, newAdditionalImages);
  };

  // Function to move an image up in the list
  const moveImageUp = (index: number) => {
    if (index > 0) {
      const newAdditionalImages = [...additionalImages];
      const temp = newAdditionalImages[index];
      newAdditionalImages[index] = newAdditionalImages[index - 1];
      newAdditionalImages[index - 1] = temp;
      setAdditionalImages(newAdditionalImages);
      onImagesChange(mainImage, newAdditionalImages);
    }
  };

  // Function to move an image down in the list
  const moveImageDown = (index: number) => {
    if (index < additionalImages.length - 1) {
      const newAdditionalImages = [...additionalImages];
      const temp = newAdditionalImages[index];
      newAdditionalImages[index] = newAdditionalImages[index + 1];
      newAdditionalImages[index + 1] = temp;
      setAdditionalImages(newAdditionalImages);
      onImagesChange(mainImage, newAdditionalImages);
    }
  };

  return (
    <div className="space-y-4">
      <Label htmlFor="images">
        <span className="flex items-center gap-1">
          <Image className="h-4 w-4" /> Product Images
        </span>
      </Label>

      {/* AI Image Search Button */}
      {onFindImagesWithAI && (
        <div className="mb-4">
          <Button
            type="button"
            variant="outline"
            onClick={onFindImagesWithAI}
            disabled={isFindingImages}
          >
            {isFindingImages ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Finding Images with AI...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Find Images with AI
              </>
            )}
          </Button>
          <p className="text-xs text-gray-500 mt-1">
            AI will search for relevant product images based on the product name and category
          </p>
        </div>
      )}

      {/* Upload controls */}
      <div className="flex flex-wrap gap-2 mb-4">
        <LocalImageUploader
          onImageUploaded={addImage}
          buttonText="Upload New Image"
        />
      </div>

      {/* Main image */}
      {mainImage && (
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Main Image</h3>
          <div className="relative rounded-md overflow-hidden border-2 border-primary w-full max-h-[200px] aspect-video group">
            <img
              src={mainImage}
              alt="Main product image"
              className="w-full h-full object-cover"
              onError={(e) => {
                // Show a placeholder on error
                e.currentTarget.src = "https://placehold.co/600x400/e5e7eb/a1a1aa?text=Main+Image";
              }}
            />
            <div className="absolute top-1 right-1 bg-primary text-white text-xs px-2 py-0.5 rounded-sm">
              Main
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                onClick={removeMainImage}
              >
                <Trash2 className="h-3 w-3 text-white" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Additional images */}
      {additionalImages.length > 0 && (
        <div>
          <h3 className="text-sm font-medium mb-2">Additional Images</h3>
          <div className="space-y-2">
            {additionalImages.map((img, index) => (
              <div
                key={index}
                className="relative rounded-md overflow-hidden border border-gray-200 w-full max-h-[150px] aspect-video group"
              >
                <img
                  src={img}
                  alt={`Product image ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Show a placeholder on error
                    e.currentTarget.src = "https://placehold.co/600x400/e5e7eb/a1a1aa?text=Image+Error";
                  }}
                />

                {/* Image controls */}
                <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-between items-center">
                  <div className="flex gap-1">
                    {/* Move up button */}
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 bg-white/20 hover:bg-white/40"
                        onClick={() => moveImageUp(index)}
                      >
                        <ArrowUp className="h-3 w-3 text-white" />
                      </Button>
                    )}

                    {/* Move down button */}
                    {index < additionalImages.length - 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 bg-white/20 hover:bg-white/40"
                        onClick={() => moveImageDown(index)}
                      >
                        <ArrowDown className="h-3 w-3 text-white" />
                      </Button>
                    )}
                  </div>

                  <div className="flex gap-1">
                    {/* Set as main image button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs bg-white/20 hover:bg-white/40 text-white"
                      onClick={() => setAsMainImage(index)}
                    >
                      Make Main
                    </Button>

                    {/* Delete button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                      onClick={() => removeImage(index)}
                    >
                      <Trash2 className="h-3 w-3 text-white" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!mainImage && additionalImages.length === 0 && (
        <div className="flex flex-col items-center justify-center w-full h-40 rounded-md border border-dashed border-gray-300 p-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">No images added yet</p>
            <p className="text-xs text-gray-400 mt-1">Upload images or find images with AI</p>
          </div>
        </div>
      )}

      <p className="text-xs text-gray-500">
        Use the arrows to reorder images. The main image will be displayed as the product thumbnail.
      </p>
    </div>
  );
}
