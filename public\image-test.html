<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Image Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .image-test {
      border: 1px solid #ccc;
      padding: 10px;
      width: 300px;
    }
    .image-test img {
      max-width: 100%;
      height: auto;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
  </style>
</head>
<body>
  <h1>Image Test</h1>
  <p>This page tests if the product images are loading correctly from Supabase storage.</p>
  
  <div id="image-test-container" class="image-container">
    <!-- Images will be added here dynamically -->
    <p>Loading images...</p>
  </div>

  <script>
    // Supabase URL from environment or hardcoded for testing
    const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    
    // Function to test image loading
    async function testImages() {
      const container = document.getElementById('image-test-container');
      container.innerHTML = ''; // Clear loading message
      
      try {
        // Fetch some products from the database
        const response = await fetch('/api/test-images');
        const products = await response.json();
        
        if (products.length === 0) {
          container.innerHTML = '<p class="error">No products found with images</p>';
          return;
        }
        
        // Test each product image
        products.forEach(product => {
          const imageDiv = document.createElement('div');
          imageDiv.className = 'image-test';
          
          const nameEl = document.createElement('h3');
          nameEl.textContent = product.name;
          imageDiv.appendChild(nameEl);
          
          const imageUrl = product.image;
          const urlEl = document.createElement('p');
          urlEl.textContent = imageUrl;
          urlEl.style.fontSize = '10px';
          urlEl.style.wordBreak = 'break-all';
          imageDiv.appendChild(urlEl);
          
          const img = document.createElement('img');
          img.src = imageUrl;
          img.alt = product.name;
          
          // Add load and error handlers
          img.onload = () => {
            const statusEl = document.createElement('p');
            statusEl.className = 'success';
            statusEl.textContent = 'Image loaded successfully';
            imageDiv.appendChild(statusEl);
          };
          
          img.onerror = () => {
            const statusEl = document.createElement('p');
            statusEl.className = 'error';
            statusEl.textContent = 'Failed to load image';
            imageDiv.appendChild(statusEl);
          };
          
          imageDiv.appendChild(img);
          container.appendChild(imageDiv);
        });
      } catch (error) {
        container.innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    }
    
    // Manually test specific image URLs
    function testSpecificImages() {
      const container = document.getElementById('image-test-container');
      container.innerHTML = ''; // Clear loading message
      
      // Test a few different image URL formats
      const testUrls = [
        {
          name: "WebP Image Test",
          url: `${supabaseUrl}/storage/v1/object/public/product-images/7caa35_9a64c78e5bb846f9a19881ede6d0a066.webp`
        },
        {
          name: "Original JPG Image Test",
          url: `${supabaseUrl}/storage/v1/object/public/product-images/7caa35_9a64c78e5bb846f9a19881ede6d0a066~mv2.jpg`
        },
        {
          name: "Placeholder Image Test",
          url: "/placeholder-product.jpg"
        }
      ];
      
      testUrls.forEach(test => {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'image-test';
        
        const nameEl = document.createElement('h3');
        nameEl.textContent = test.name;
        imageDiv.appendChild(nameEl);
        
        const urlEl = document.createElement('p');
        urlEl.textContent = test.url;
        urlEl.style.fontSize = '10px';
        urlEl.style.wordBreak = 'break-all';
        imageDiv.appendChild(urlEl);
        
        const img = document.createElement('img');
        img.src = test.url;
        img.alt = test.name;
        
        // Add load and error handlers
        img.onload = () => {
          const statusEl = document.createElement('p');
          statusEl.className = 'success';
          statusEl.textContent = 'Image loaded successfully';
          imageDiv.appendChild(statusEl);
        };
        
        img.onerror = () => {
          const statusEl = document.createElement('p');
          statusEl.className = 'error';
          statusEl.textContent = 'Failed to load image';
          imageDiv.appendChild(statusEl);
        };
        
        imageDiv.appendChild(img);
        container.appendChild(imageDiv);
      });
    }
    
    // Run the test
    testSpecificImages();
  </script>
</body>
</html>
