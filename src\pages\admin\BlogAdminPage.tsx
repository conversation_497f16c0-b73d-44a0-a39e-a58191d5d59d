import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Blog, BlogCategory } from '@/types/database';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/auth.basic';
import { 
  Edit,
  Trash2,
  Plus,
  Eye,
  Search,
  Filter,
  ArrowUpDown,
  Calendar,
  Tag,
  Clock,
  CheckCircle2,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const BlogAdminPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string>('updated_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterPublished, setFilterPublished] = useState<string>('all');
  const [blogToDelete, setBlogToDelete] = useState<Blog | null>(null);
  
  // Redirect if not admin
  if (!isAdmin) {
    navigate('/');
    return null;
  }
  
  // Fetch blogs from Supabase
  const { data: blogs, isLoading: blogsLoading, error: blogsError } = useQuery({
    queryKey: ['admin_blogs', sortBy, sortOrder, filterPublished],
    queryFn: async () => {
      try {
        // First get the blogs
        let query = supabase
          .from('blogs')
          .select('*')
          .order(sortBy, { ascending: sortOrder === 'asc' });
        
        if (filterPublished !== 'all') {
          query = query.eq('is_published', filterPublished === 'published');
        }
        
        const { data: blogData, error } = await query;
        
        if (error) {
          console.error('Error fetching blogs:', error);
          return [];
        }

        // Then get author information for each blog
        if (blogData && blogData.length > 0) {
          const authorIds = blogData
            .filter(blog => blog.author_id)
            .map(blog => blog.author_id);
          
          if (authorIds.length > 0) {
            const { data: profilesData } = await supabase
              .from('profiles')
              .select('id, first_name, last_name')
              .in('id', authorIds);
            
            // Combine blog data with author data
            return blogData.map(blog => ({
              ...blog,
              author: profilesData?.find(profile => profile.id === blog.author_id) || null
            }));
          }
        }
        
        return blogData || [];
      } catch (error) {
        console.error('Error in blog query:', error);
        return [];
      }
    }
  });
  
  // Fetch blog categories
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['blog_categories'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('blog_categories')
          .select('*')
          .order('name', { ascending: true });
        
        if (error) {
          console.error('Error fetching categories:', error);
          return [];
        }
        return data as any[];
      } catch (error) {
        console.error('Error in categories query:', error);
        return [];
      }
    }
  });
  
  // Delete blog mutation
  const deleteBlogMutation = useMutation({
    mutationFn: async (blogId: string) => {
      try {
        // First delete related records
        await supabase.from('blog_comments').delete().eq('blog_id', blogId);
        await supabase.from('blog_images').delete().eq('blog_id', blogId);
        await supabase.from('blog_ai_metadata').delete().eq('blog_id', blogId);
        await supabase.from('blog_social_shares').delete().eq('blog_id', blogId);
        
        // Then delete the blog
        const { error } = await supabase.from('blogs').delete().eq('id', blogId);
        if (error) {
          console.error('Error deleting blog:', error);
          throw new Error('Failed to delete blog post');
        }
      } catch (error) {
        console.error('Error in delete mutation:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: "Blog deleted",
        description: "The blog post has been permanently deleted."
      });
      queryClient.invalidateQueries({ queryKey: ['admin_blogs'] });
      setBlogToDelete(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete blog: ${error.message}`,
        variant: "destructive"
      });
    }
  });
  
  // Toggle publish status mutation
  const togglePublishMutation = useMutation({
    mutationFn: async ({ id, isPublished }: { id: string, isPublished: boolean }) => {
      const { error } = await supabase
        .from('blogs')
        .update({ 
          is_published: isPublished,
          published_at: isPublished ? new Date().toISOString() : null
        })
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      toast({
        title: variables.isPublished ? "Blog published" : "Blog unpublished",
        description: variables.isPublished 
          ? "The blog post is now live on the site." 
          : "The blog post has been unpublished."
      });
      queryClient.invalidateQueries({ queryKey: ['admin_blogs'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update blog status: ${error.message}`,
        variant: "destructive"
      });
    }
  });
  
  // Filter blogs based on search query and selected category
  const filteredBlogs = blogs?.filter(blog => {
    const matchesSearch = searchQuery === '' || 
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      blog.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === null || blog.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Get author name
  const getAuthorName = (blog: Blog & { author?: { first_name: string, last_name: string } }) => {
    if (blog.author?.first_name && blog.author?.last_name) {
      return `${blog.author.first_name} ${blog.author.last_name}`;
    }
    return 'Staff Writer';
  };
  
  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Blog Management</h1>
          <p className="text-gray-600">Create, edit, and manage your blog posts</p>
        </div>
        <Button asChild className="mt-4 md:mt-0">
          <Link to="/admin/blogs/new">
            <Plus className="mr-2 h-4 w-4" />
            Create New Post
          </Link>
        </Button>
      </div>
      
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4 mb-6">
        <div className="md:col-span-5">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search blog posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="md:col-span-3">
          <Select value={selectedCategory || 'all'} onValueChange={(value) => setSelectedCategory(value === 'all' ? null : value)}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories?.map(category => (
                <SelectItem key={category.id} value={category.slug}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="md:col-span-2">
          <Select value={filterPublished} onValueChange={(value: string) => setFilterPublished(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="md:col-span-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                <span className="flex items-center">
                  <ArrowUpDown className="mr-2 h-4 w-4" />
                  Sort
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => handleSortChange('updated_at')}>
                Last Updated {sortBy === 'updated_at' && (sortOrder === 'asc' ? '↑' : '↓')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('published_at')}>
                Publish Date {sortBy === 'published_at' && (sortOrder === 'asc' ? '↑' : '↓')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('title')}>
                Title {sortBy === 'title' && (sortOrder === 'asc' ? '↑' : '↓')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSortChange('view_count')}>
                Views {sortBy === 'view_count' && (sortOrder === 'asc' ? '↑' : '↓')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {/* Blog Posts Grid */}
      <div className="space-y-4">
        {blogsLoading ? (
          // Loading skeletons
          Array(5).fill(0).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-6 w-3/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                  <Skeleton className="h-10 w-24" />
                </div>
              </div>
            </Card>
          ))
        ) : blogsError ? (
          <div className="text-center py-12">
            <p className="text-red-500 font-medium">Error loading blog posts. Please try again later.</p>
          </div>
        ) : filteredBlogs && filteredBlogs.length > 0 ? (
          filteredBlogs.map(blog => (
            <Card key={blog.id} className="overflow-hidden">
              <div className="p-6">
                <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h3 className="text-xl font-semibold">
                        {blog.title}
                      </h3>
                      {blog.is_featured && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-300">
                          Featured
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>Updated: {formatDate(blog.updated_at)}</span>
                      </div>
                      
                      {blog.is_published ? (
                        <div className="flex items-center gap-1">
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                          <span>Published: {formatDate(blog.published_at)}</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <XCircle className="h-4 w-4 text-amber-500" />
                          <span>Draft</span>
                        </div>
                      )}
                      
                      {blog.category && (
                        <div className="flex items-center gap-1">
                          <Tag className="h-4 w-4" />
                          <span>{blog.category}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>{blog.view_count || 0} views</span>
                      </div>
                      
                      {blog.reading_time && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{blog.reading_time} min read</span>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-gray-600 line-clamp-1">
                      {blog.summary || blog.content.substring(0, 100) + '...'}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2 shrink-0">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => navigate(`/blog/${blog.slug}`)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    
                    <Button 
                      variant={blog.is_published ? "destructive" : "default"}
                      size="sm"
                      onClick={() => togglePublishMutation.mutate({ 
                        id: blog.id, 
                        isPublished: !blog.is_published 
                      })}
                    >
                      {blog.is_published ? 'Unpublish' : 'Publish'}
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => navigate(`/admin/blogs/edit/${blog.id}`)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-red-600 focus:text-red-600" 
                          onClick={() => setBlogToDelete(blog)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 font-medium">No blog posts found matching your criteria.</p>
            {(searchQuery || selectedCategory || filterPublished !== 'all') && (
              <Button 
                variant="link" 
                className="mt-2"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory(null);
                  setFilterPublished('all');
                }}
              >
                Clear filters
              </Button>
            )}
          </div>
        )}
      </div>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!blogToDelete} onOpenChange={(open) => !open && setBlogToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the blog post "{blogToDelete?.title}" and all associated data including comments, images, and social shares. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700"
              onClick={() => blogToDelete && deleteBlogMutation.mutate(blogToDelete.id)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default BlogAdminPage;

