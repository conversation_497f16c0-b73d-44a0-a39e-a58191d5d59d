# BITS N BONGS Checkout Flow

This README provides an overview of the checkout flow implementation for the BITS N BONGS e-commerce platform.

## Features

- Multi-step checkout process (Shipping, Payment, Review)
- Address management (add, edit, delete, set default)
- Shipping method selection
- Payment method selection
- Order review and confirmation
- Support for product variants

## Implementation

The checkout flow is implemented using React and Next.js with the following components:

- `CheckoutContainer`: Main container component
- `ShippingStep`: Shipping address and method selection
- `PaymentStep`: Payment method selection
- `ReviewStep`: Order review
- `OrderSummary`: Order summary with pricing details

## Database Schema

The checkout flow uses the following database tables:

- `addresses`: Stores user shipping and billing addresses
- `orders`: Stores order information
- `order_items`: Stores order line items
- `product_variants`: Stores product variant information

## Getting Started

1. Apply the database migrations:
   ```bash
   psql -U postgres -d your_database_name -f src/migrations/create_addresses_table.sql
   psql -U postgres -d your_database_name -f src/migrations/create_product_variants_table.sql
   psql -U postgres -d your_database_name -f src/migrations/update_orders_table.sql
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Navigate to the checkout page:
   ```
   http://localhost:8080/checkout
   ```

## Usage

1. Add items to your cart from the product pages
2. Click "Proceed to Checkout" in the cart
3. Enter or select a shipping address
4. Select a shipping method
5. Enter payment details or select PayPal
6. Review your order
7. Place your order

## Notes

- The payment processing is currently simulated and does not process real payments
- For testing, you can use any valid-looking credit card number (e.g., 4242 4242 4242 4242)
- The checkout flow requires a user account for saving addresses

## Future Enhancements

- Integration with Stripe and PayPal for real payment processing
- Order tracking functionality
- Discount code support
- Guest checkout
- Address validation and autocomplete

## Documentation

For more detailed documentation, see [docs/checkout-flow.md](docs/checkout-flow.md).
