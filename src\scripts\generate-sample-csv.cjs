/**
 * Generate Sample CSV for Testing
 * 
 * This script creates a sample CSV file with product data that can be used
 * to test the CSV transformation script.
 * 
 * Usage:
 * 1. Run: node src/scripts/generate-sample-csv.cjs
 * 2. Find the sample CSV in the data directory
 */

const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');

// Define configuration
const CONFIG = {
  outputDir: './data',
  outputFileName: 'sample-products.csv',
};

// Sample product data
const sampleProducts = [
  {
    handleId: '1001',
    fieldType: 'Product',
    name: 'Northern Lights Seeds',
    description: 'A classic indica strain known for its resinous buds and relaxing effects.',
    productImageUrl: 'northern-lights.jpg~mv2',
    collection: 'Indica Seeds',
    sku: 'NL-001',
    ribbon: 'Best Seller',
    price: '£29.99',
    surcharge: '',
    visible: 'TRUE',
    discountMode: 'PERCENT',
    discountValue: '10',
    inventory: '50',
    weight: '0.01',
    cost: '15.00',
    brand: 'Premium Seeds',
    productOptionName1: 'Pack Size',
    productOptionType1: 'DROP_DOWN',
    productOptionDescription1: '3 Seeds;5 Seeds;10 Seeds',
    productOptionName2: 'Feminized',
    productOptionType2: 'DROP_DOWN',
    productOptionDescription2: 'Yes;No',
    additionalInfoTitle1: 'Growing Info',
    additionalInfoDescription1: 'Indoor/Outdoor: Both\\nFlowering Time: 7-9 weeks\\nYield: High',
    additionalInfoTitle2: 'Effects',
    additionalInfoDescription2: 'Relaxing, sleep-inducing, pain relief',
  },
  {
    handleId: '1002',
    fieldType: 'Product',
    name: 'Sour Diesel Seeds',
    description: 'A sativa-dominant strain with energetic effects and a pungent diesel aroma.',
    productImageUrl: 'https://example.com/images/sour-diesel.png~mv2',
    collection: 'Sativa Seeds',
    sku: 'SD-002',
    ribbon: 'New',
    price: '£34.99',
    surcharge: '',
    visible: 'TRUE',
    discountMode: '',
    discountValue: '',
    inventory: '30',
    weight: '0.01',
    cost: '18.00',
    brand: 'Premium Seeds',
    productOptionName1: 'Pack Size',
    productOptionType1: 'DROP_DOWN',
    productOptionDescription1: '3 Seeds;5 Seeds;10 Seeds',
    productOptionName2: 'Feminized',
    productOptionType2: 'DROP_DOWN',
    productOptionDescription2: 'Yes;No',
    additionalInfoTitle1: 'Growing Info',
    additionalInfoDescription1: 'Indoor/Outdoor: Both\\nFlowering Time: 10-11 weeks\\nYield: Medium-High',
    additionalInfoTitle2: 'Effects',
    additionalInfoDescription2: 'Energetic, uplifting, creative',
  },
  {
    handleId: '1003',
    fieldType: 'Product',
    name: 'Blue Dream Seeds',
    description: 'A balanced hybrid offering gentle cerebral invigoration and body relaxation.',
    productImageUrl: '', // No image to test inactive flag
    collection: 'Hybrid Seeds',
    sku: 'BD-003',
    ribbon: '',
    price: '£32.99',
    surcharge: '',
    visible: 'TRUE',
    discountMode: 'AMOUNT',
    discountValue: '5',
    inventory: '25',
    weight: '0.01',
    cost: '16.50',
    brand: 'Premium Seeds',
    productOptionName1: 'Pack Size',
    productOptionType1: 'DROP_DOWN',
    productOptionDescription1: '3 Seeds;5 Seeds;10 Seeds',
    productOptionName2: 'Feminized',
    productOptionType2: 'DROP_DOWN',
    productOptionDescription2: 'Yes;No',
    productOptionName3: 'Auto-flowering',
    productOptionType3: 'DROP_DOWN',
    productOptionDescription3: 'Yes;No',
    additionalInfoTitle1: 'Growing Info',
    additionalInfoDescription1: 'Indoor/Outdoor: Both\\nFlowering Time: 9-10 weeks\\nYield: High',
    additionalInfoTitle2: 'Effects',
    additionalInfoDescription2: 'Balanced, creative, gentle relaxation',
  },
  {
    handleId: '1004',
    fieldType: 'Product',
    name: 'OG Kush Seeds',
    description: 'A legendary strain with a complex aroma and potent effects.',
    productImageUrl: 'https://example.com/images/og-kush.jpg~mv2',
    collection: 'Hybrid Seeds',
    sku: 'OGK-004',
    ribbon: 'Popular',
    price: '£36.99',
    surcharge: '',
    visible: 'TRUE',
    discountMode: '',
    discountValue: '',
    inventory: '15',
    weight: '0.01',
    cost: '20.00',
    brand: 'Premium Seeds',
    productOptionName1: 'Pack Size',
    productOptionType1: 'DROP_DOWN',
    productOptionDescription1: '3 Seeds;5 Seeds;10 Seeds',
    additionalInfoTitle1: 'Growing Info',
    additionalInfoDescription1: 'Indoor/Outdoor: Indoor preferred\\nFlowering Time: 8-9 weeks\\nYield: Medium',
    additionalInfoTitle2: 'Effects',
    additionalInfoDescription2: 'Euphoric, relaxing, stress relief',
  },
  {
    handleId: '1005',
    fieldType: 'Product',
    name: 'Girl Scout Cookies Seeds',
    description: 'An award-winning hybrid strain with sweet and earthy flavors.',
    productImageUrl: 'https://example.com/images/gsc.jpeg~mv2',
    collection: 'Hybrid Seeds',
    sku: 'GSC-005',
    ribbon: 'Award Winner',
    price: '£39.99',
    surcharge: '',
    visible: 'TRUE',
    discountMode: 'PERCENT',
    discountValue: '15',
    inventory: '20',
    weight: '0.01',
    cost: '22.00',
    brand: 'Premium Seeds',
    productOptionName1: 'Pack Size',
    productOptionType1: 'DROP_DOWN',
    productOptionDescription1: '3 Seeds;5 Seeds;10 Seeds',
    productOptionName2: 'Feminized',
    productOptionType2: 'DROP_DOWN',
    productOptionDescription2: 'Yes;No',
    additionalInfoTitle1: 'Growing Info',
    additionalInfoDescription1: 'Indoor/Outdoor: Both\\nFlowering Time: 9-10 weeks\\nYield: Medium-High',
    additionalInfoTitle2: 'Effects',
    additionalInfoDescription2: 'Euphoric, happy, relaxing',
  }
];

// Generate CSV
function generateSampleCsv() {
  console.log('Generating sample CSV...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Convert to CSV
  const csv = Papa.unparse(sampleProducts);
  
  // Write to file
  const outputPath = path.join(CONFIG.outputDir, CONFIG.outputFileName);
  fs.writeFileSync(outputPath, csv);
  
  console.log(`Sample CSV generated at: ${outputPath}`);
  console.log(`Contains ${sampleProducts.length} sample products`);
}

// Run the generator
generateSampleCsv();
