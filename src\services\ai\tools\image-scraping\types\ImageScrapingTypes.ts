/**
 * Image Scraping Types for Second Agent
 * 
 * These types define the interface between the image scraping system
 * and the main AI orchestration system
 */

export interface ProductImage {
  url: string;
  alt: string;
  caption?: string;
  quality_score: number;
  source: string;
  width?: number;
  height?: number;
  file_size?: number;
  format?: string;
}

export interface ImageSearchOptions {
  max_images?: number;
  min_quality_score?: number;
  preferred_sources?: string[];
  image_types?: ('product' | 'lifestyle' | 'detail' | 'packaging')[];
  min_dimensions?: {
    width: number;
    height: number;
  };
}

export interface ImageQualityScore {
  overall_score: number;
  relevance_score: number;
  technical_score: number;
  dimensions_score: number;
  source_reliability_score: number;
  factors: {
    has_product_in_alt: boolean;
    good_dimensions: boolean;
    from_product_page: boolean;
    not_thumbnail: boolean;
    clear_image: boolean;
  };
}

export interface BulkProcessingReport {
  total_products: number;
  successful_products: number;
  failed_products: number;
  total_images_found: number;
  average_images_per_product: number;
  processing_time: number;
  cost_savings: number;
  errors: {
    product_id: string;
    product_name: string;
    error: string;
  }[];
  success_details: {
    product_id: string;
    product_name: string;
    images_found: number;
    best_image_url: string;
  }[];
}

export interface RetailerSource {
  name: string;
  base_url: string;
  search_path: string;
  selectors: {
    product_images: string[];
    product_titles: string[];
    product_links: string[];
  };
  rate_limit: {
    requests_per_minute: number;
    delay_between_requests: number;
  };
  categories: string[];
  reliability_score: number;
}

export interface ScrapingSession {
  session_id: string;
  start_time: Date;
  end_time?: Date;
  products_processed: number;
  images_found: number;
  sources_used: string[];
  errors: string[];
  status: 'running' | 'completed' | 'failed' | 'cancelled';
}

/**
 * Main interface that the AI core will use to interact with image scraping
 */
export interface ImageScrapingService {
  /**
   * Find images for a single product
   */
  findProductImages(
    product: { name: string; category?: string; id?: string }, 
    options?: ImageSearchOptions
  ): Promise<ProductImage[]>;
  
  /**
   * Process multiple products in bulk
   */
  bulkProcessProducts(
    products: { name: string; category?: string; id: string }[],
    options?: ImageSearchOptions
  ): Promise<BulkProcessingReport>;
  
  /**
   * Validate image quality and relevance
   */
  validateImageQuality(imageUrl: string, productName: string): Promise<ImageQualityScore>;
  
  /**
   * Get available retailer sources
   */
  getAvailableSources(category?: string): Promise<RetailerSource[]>;
  
  /**
   * Check scraping service health
   */
  checkHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'down';
    sources_available: number;
    last_successful_scrape: Date;
    error_rate: number;
  }>;
}
