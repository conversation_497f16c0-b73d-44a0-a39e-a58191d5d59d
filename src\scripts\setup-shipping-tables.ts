import { supabase } from '../integrations/supabase/client';
import fs from 'fs';
import path from 'path';

async function setupShippingTables() {
  console.log('🚀 Setting up shipping tables...');

  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-shipping-tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      console.error('❌ Error executing SQL:', error);
      throw error;
    }

    console.log('✅ Shipping tables created successfully!');

    // Verify the tables were created
    const { data: zones, error: zonesError } = await supabase
      .from('shipping_zones')
      .select('*')
      .limit(5);

    const { data: methods, error: methodsError } = await supabase
      .from('shipping_methods')
      .select('*')
      .limit(5);

    if (zonesError || methodsError) {
      console.error('❌ Error verifying tables:', zonesError || methodsError);
      throw zonesError || methodsError;
    }

    console.log(`✅ Verification complete:`);
    console.log(`   - ${zones?.length || 0} shipping zones created`);
    console.log(`   - ${methods?.length || 0} shipping methods created`);

    console.log('\n📋 Default shipping zones:');
    zones?.forEach(zone => {
      console.log(`   - ${zone.name}: ${zone.countries.length} countries`);
    });

    console.log('\n🚚 Default shipping methods:');
    methods?.forEach(method => {
      console.log(`   - ${method.name}: £${method.price} (${method.estimated_days_min}-${method.estimated_days_max} days)`);
    });

  } catch (error) {
    console.error('❌ Failed to setup shipping tables:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution if RPC doesn't work
async function setupShippingTablesAlternative() {
  console.log('🚀 Setting up shipping tables (alternative method)...');

  try {
    // Create shipping zones table
    const { error: zonesError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS shipping_zones (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          countries JSONB NOT NULL DEFAULT '[]'::jsonb,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (zonesError) {
      console.error('❌ Error creating shipping_zones table:', zonesError);
      throw zonesError;
    }

    // Create shipping methods table
    const { error: methodsError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS shipping_methods (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          zone_id UUID NOT NULL REFERENCES shipping_zones(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          free_shipping_threshold DECIMAL(10,2),
          estimated_days_min INTEGER NOT NULL DEFAULT 1,
          estimated_days_max INTEGER NOT NULL DEFAULT 7,
          icon VARCHAR(50) NOT NULL DEFAULT 'standard',
          is_active BOOLEAN NOT NULL DEFAULT true,
          sort_order INTEGER NOT NULL DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (methodsError) {
      console.error('❌ Error creating shipping_methods table:', methodsError);
      throw methodsError;
    }

    console.log('✅ Shipping tables created successfully!');

    // Insert default data
    await insertDefaultShippingData();

  } catch (error) {
    console.error('❌ Failed to setup shipping tables:', error);
    process.exit(1);
  }
}

async function insertDefaultShippingData() {
  console.log('📦 Inserting default shipping data...');

  try {
    // Insert UK zone
    const { data: ukZone, error: ukError } = await supabase
      .from('shipping_zones')
      .insert([{
        name: 'United Kingdom',
        description: 'Domestic shipping within the UK',
        countries: ['United Kingdom'],
        is_active: true
      }])
      .select()
      .single();

    if (ukError) {
      console.error('❌ Error inserting UK zone:', ukError);
      throw ukError;
    }

    // Insert EU zone
    const { data: euZone, error: euError } = await supabase
      .from('shipping_zones')
      .insert([{
        name: 'European Union',
        description: 'Shipping to EU countries',
        countries: [
          'Ireland', 'France', 'Germany', 'Spain', 'Italy', 'Netherlands', 
          'Belgium', 'Portugal', 'Austria', 'Denmark', 'Sweden', 'Finland',
          'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Slovenia',
          'Croatia', 'Estonia', 'Latvia', 'Lithuania', 'Luxembourg',
          'Malta', 'Cyprus', 'Bulgaria', 'Romania', 'Greece'
        ],
        is_active: true
      }])
      .select()
      .single();

    if (euError) {
      console.error('❌ Error inserting EU zone:', euError);
      throw euError;
    }

    // Insert UK shipping methods
    const ukMethods = [
      {
        zone_id: ukZone.id,
        name: 'Free Standard Shipping',
        description: 'Free delivery within 3-5 business days for orders over £50',
        price: 0.00,
        free_shipping_threshold: 50.00,
        estimated_days_min: 3,
        estimated_days_max: 5,
        icon: 'free',
        sort_order: 1
      },
      {
        zone_id: ukZone.id,
        name: 'Standard Shipping',
        description: 'Delivery within 3-5 business days',
        price: 5.99,
        estimated_days_min: 3,
        estimated_days_max: 5,
        icon: 'standard',
        sort_order: 2
      },
      {
        zone_id: ukZone.id,
        name: 'Express Shipping',
        description: 'Delivery within 2-3 business days',
        price: 9.99,
        estimated_days_min: 2,
        estimated_days_max: 3,
        icon: 'express',
        sort_order: 3
      },
      {
        zone_id: ukZone.id,
        name: 'Next Day Delivery',
        description: 'Order before 2pm for next day delivery',
        price: 14.99,
        estimated_days_min: 1,
        estimated_days_max: 1,
        icon: 'nextDay',
        sort_order: 4
      }
    ];

    const { error: ukMethodsError } = await supabase
      .from('shipping_methods')
      .insert(ukMethods);

    if (ukMethodsError) {
      console.error('❌ Error inserting UK methods:', ukMethodsError);
      throw ukMethodsError;
    }

    // Insert EU shipping methods
    const euMethods = [
      {
        zone_id: euZone.id,
        name: 'EU Free Shipping',
        description: 'Free delivery within 7-10 business days for orders over £100',
        price: 0.00,
        free_shipping_threshold: 100.00,
        estimated_days_min: 7,
        estimated_days_max: 10,
        icon: 'free',
        sort_order: 1
      },
      {
        zone_id: euZone.id,
        name: 'EU Standard Shipping',
        description: 'Delivery within 7-10 business days',
        price: 12.99,
        estimated_days_min: 7,
        estimated_days_max: 10,
        icon: 'standard',
        sort_order: 2
      },
      {
        zone_id: euZone.id,
        name: 'EU Express Shipping',
        description: 'Delivery within 5-7 business days',
        price: 19.99,
        estimated_days_min: 5,
        estimated_days_max: 7,
        icon: 'express',
        sort_order: 3
      }
    ];

    const { error: euMethodsError } = await supabase
      .from('shipping_methods')
      .insert(euMethods);

    if (euMethodsError) {
      console.error('❌ Error inserting EU methods:', euMethodsError);
      throw euMethodsError;
    }

    console.log('✅ Default shipping data inserted successfully!');

  } catch (error) {
    console.error('❌ Failed to insert default shipping data:', error);
    throw error;
  }
}

// Run the setup
if (require.main === module) {
  setupShippingTablesAlternative()
    .then(() => {
      console.log('\n🎉 Shipping system setup complete!');
      console.log('\n📝 Next steps:');
      console.log('   1. Add the ShippingManager component to your admin area');
      console.log('   2. Update your checkout to use the new shipping service');
      console.log('   3. Test the shipping calculation with different countries');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error);
      process.exit(1);
    });
}

export { setupShippingTables, setupShippingTablesAlternative, insertDefaultShippingData };
