import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ShoppingBag, Heart } from 'lucide-react';
import { useSavedItems } from '@/hooks/useSavedItems';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useAuth } from '@/hooks/auth.basic';

export default function SavedItems() {
  const { user } = useAuth();

  // Only use savedItems if user is authenticated
  const savedItemsHook = user ? useSavedItems() : {
    savedItems: [],
    isLoading: false,
    removeFromSaved: () => {},
    moveToCart: () => {}
  };

  const { savedItems, isLoading, removeFromSaved, moveToCart } = savedItemsHook;

  if (isLoading) {
    return (
      <div className="flex justify-center py-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (savedItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 text-center">
        <Heart className="h-12 w-12 text-gray-300 mb-4" />
        <p className="text-gray-500 mb-2">No saved items</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm border-b pb-2">Saved For Later ({savedItems.length})</h3>
      {savedItems.map((item) => (
        <div key={item.id} className="flex gap-3 py-3 border-b last:border-0">
          <div className="w-16 h-16 overflow-hidden rounded-md">
            <Link to={`/product/${item.product?.slug}`}>
              <AspectRatio ratio={1/1} className="bg-gray-100">
                <img
                  src={item.product?.image || '/placeholder.svg'}
                  alt={item.product?.name}
                  className="object-cover"
                />
              </AspectRatio>
            </Link>
          </div>

          <div className="flex-1">
            <Link
              to={`/product/${item.product?.slug}`}
              className="font-medium hover:text-primary truncate max-w-[150px] block"
            >
              {item.product?.name}
            </Link>

            <div className="flex items-center mt-1 text-sm text-gray-700">
              {item.product?.sale_price && item.product.sale_price > 0 ? (
                <>
                  <span className="font-medium">£{item.product.sale_price.toFixed(2)}</span>
                  <span className="line-through text-gray-500 ml-2">£{item.product.price.toFixed(2)}</span>
                </>
              ) : (
                <span className="font-medium">£{item.product?.price.toFixed(2)}</span>
              )}
            </div>

            <div className="flex gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 text-xs px-2"
                onClick={() => moveToCart(item.id)}
              >
                <ShoppingBag className="h-3 w-3 mr-1" />
                Add to Cart
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 text-xs px-2"
                onClick={() => removeFromSaved(item.id)}
              >
                Remove
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

