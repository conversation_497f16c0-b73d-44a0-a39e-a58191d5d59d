/**
 * SourceManager.ts
 * 
 * Manages retailer sources for image scraping
 * Handles source configuration, categorization, and selection
 */

import { RetailerSource } from './types/ImageScrapingTypes';

/**
 * Source category
 */
export enum SourceCategory {
  SEEDS = 'seeds',
  CBD = 'cbd',
  VAPORIZERS = 'vaporizers',
  ACCESSORIES = 'accessories',
  GENERAL = 'general'
}

/**
 * Source manager configuration
 */
interface SourceManagerConfig {
  defaultSources?: RetailerSource[];
  sourcesFilePath?: string;
}

/**
 * Manages retailer sources for image scraping
 */
export class SourceManager {
  private sources: RetailerSource[] = [];
  private sourcesFilePath: string | null = null;

  /**
   * Constructor
   * @param config - Source manager configuration
   */
  constructor(config: SourceManagerConfig = {}) {
    if (config.defaultSources) {
      this.sources = [...config.defaultSources];
    } else {
      this.sources = this.getDefaultSources();
    }
    
    this.sourcesFilePath = config.sourcesFilePath || null;
    
    // Load sources from file if path provided
    if (this.sourcesFilePath) {
      this.loadSourcesFromFile().catch(error => {
        console.error('Failed to load sources from file:', error);
      });
    }
  }

  /**
   * Get all sources
   * @returns Array of retailer sources
   */
  getAllSources(): RetailerSource[] {
    return [...this.sources];
  }

  /**
   * Get sources by category
   * @param category - Source category
   * @returns Array of retailer sources
   */
  getSourcesByCategory(category: string): RetailerSource[] {
    return this.sources.filter(source => 
      source.categories.some(cat => 
        cat.toLowerCase() === category.toLowerCase()
      )
    );
  }

  /**
   * Get sources by reliability
   * @param minReliability - Minimum reliability score
   * @returns Array of retailer sources
   */
  getSourcesByReliability(minReliability: number): RetailerSource[] {
    return this.sources.filter(source => 
      source.reliability_score >= minReliability
    );
  }

  /**
   * Get relevant sources for product
   * @param productName - Product name
   * @param category - Product category
   * @returns Array of retailer sources
   */
  getRelevantSources(productName: string, category?: string): RetailerSource[] {
    // Start with all sources
    let relevantSources = [...this.sources];
    
    // Filter by category if provided
    if (category) {
      const categorySources = this.getSourcesByCategory(category);
      
      // If we have category-specific sources, use them
      if (categorySources.length > 0) {
        relevantSources = categorySources;
      }
    }
    
    // Sort by reliability score
    relevantSources.sort((a, b) => b.reliability_score - a.reliability_score);
    
    return relevantSources;
  }

  /**
   * Add new source
   * @param source - Retailer source
   */
  addSource(source: RetailerSource): void {
    // Check if source already exists
    const existingIndex = this.sources.findIndex(s => s.name === source.name);
    
    if (existingIndex >= 0) {
      // Update existing source
      this.sources[existingIndex] = source;
    } else {
      // Add new source
      this.sources.push(source);
    }
    
    // Save sources to file if path provided
    if (this.sourcesFilePath) {
      this.saveSourcesToFile().catch(error => {
        console.error('Failed to save sources to file:', error);
      });
    }
  }

  /**
   * Remove source
   * @param sourceName - Source name
   * @returns True if source was removed
   */
  removeSource(sourceName: string): boolean {
    const initialLength = this.sources.length;
    
    // Remove source
    this.sources = this.sources.filter(source => source.name !== sourceName);
    
    // Check if source was removed
    const removed = this.sources.length < initialLength;
    
    // Save sources to file if path provided and source was removed
    if (removed && this.sourcesFilePath) {
      this.saveSourcesToFile().catch(error => {
        console.error('Failed to save sources to file:', error);
      });
    }
    
    return removed;
  }

  /**
   * Update source
   * @param sourceName - Source name
   * @param updates - Source updates
   * @returns True if source was updated
   */
  updateSource(sourceName: string, updates: Partial<RetailerSource>): boolean {
    // Find source
    const sourceIndex = this.sources.findIndex(source => source.name === sourceName);
    
    if (sourceIndex < 0) {
      return false;
    }
    
    // Update source
    this.sources[sourceIndex] = {
      ...this.sources[sourceIndex],
      ...updates
    };
    
    // Save sources to file if path provided
    if (this.sourcesFilePath) {
      this.saveSourcesToFile().catch(error => {
        console.error('Failed to save sources to file:', error);
      });
    }
    
    return true;
  }

  /**
   * Load sources from file
   */
  private async loadSourcesFromFile(): Promise<void> {
    if (!this.sourcesFilePath) {
      return;
    }
    
    try {
      // In a real implementation, this would load sources from a file
      // For now, we'll use default sources
      this.sources = this.getDefaultSources();
    } catch (error) {
      console.error('Failed to load sources from file:', error);
      throw error;
    }
  }

  /**
   * Save sources to file
   */
  private async saveSourcesToFile(): Promise<void> {
    if (!this.sourcesFilePath) {
      return;
    }
    
    try {
      // In a real implementation, this would save sources to a file
      // For now, we'll just log the sources
      console.log('Saving sources to file:', this.sources);
    } catch (error) {
      console.error('Failed to save sources to file:', error);
      throw error;
    }
  }

  /**
   * Get default sources
   * @returns Array of default retailer sources
   */
  private getDefaultSources(): RetailerSource[] {
    return [
      // Seeds
      {
        name: 'Seedsman',
        base_url: 'https://seedsman.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-media img'],
          product_titles: ['.product-item-link', '.product-name'],
          product_links: ['.product-item-link', '.product-name']
        },
        rate_limit: {
          requests_per_minute: 10,
          delay_between_requests: 6000
        },
        categories: [SourceCategory.SEEDS],
        reliability_score: 9
      },
      {
        name: 'Royal Queen Seeds',
        base_url: 'https://royalqueenseeds.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image-container img', '.product-image img'],
          product_titles: ['.product-name', '.product-title'],
          product_links: ['.product-name a', '.product-title a']
        },
        rate_limit: {
          requests_per_minute: 8,
          delay_between_requests: 7500
        },
        categories: [SourceCategory.SEEDS],
        reliability_score: 8
      },
      {
        name: 'Barneys Farm',
        base_url: 'https://barneysfarm.com',
        search_path: '/search?type=product&q={query}',
        selectors: {
          product_images: ['.product-image img', '.product__media img'],
          product_titles: ['.product-title', '.product-item__title'],
          product_links: ['.product-title a', '.product-item__title a']
        },
        rate_limit: {
          requests_per_minute: 6,
          delay_between_requests: 10000
        },
        categories: [SourceCategory.SEEDS],
        reliability_score: 7
      },
      
      // CBD Products
      {
        name: 'CBD Oil UK',
        base_url: 'https://cbdoil.co.uk',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-thumb img'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 10,
          delay_between_requests: 6000
        },
        categories: [SourceCategory.CBD],
        reliability_score: 8
      },
      {
        name: 'Love CBD',
        base_url: 'https://lovecbd.org',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product__image'],
          product_titles: ['.product-title', '.product__title'],
          product_links: ['.product-title a', '.product__title a']
        },
        rate_limit: {
          requests_per_minute: 8,
          delay_between_requests: 7500
        },
        categories: [SourceCategory.CBD],
        reliability_score: 7
      },
      {
        name: 'CBD Life',
        base_url: 'https://cbdlife.co.uk',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-img-primary'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 6,
          delay_between_requests: 10000
        },
        categories: [SourceCategory.CBD],
        reliability_score: 6
      },
      
      // Vaporizers
      {
        name: 'Vaporizer Chief',
        base_url: 'https://vaporizerchief.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-img'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 10,
          delay_between_requests: 6000
        },
        categories: [SourceCategory.VAPORIZERS],
        reliability_score: 8
      },
      {
        name: 'Planet of the Vapes',
        base_url: 'https://planetofthevapes.co.uk',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product__media img'],
          product_titles: ['.product-title', '.product__title'],
          product_links: ['.product-title a', '.product__title a']
        },
        rate_limit: {
          requests_per_minute: 8,
          delay_between_requests: 7500
        },
        categories: [SourceCategory.VAPORIZERS],
        reliability_score: 9
      },
      {
        name: 'Vapefiend',
        base_url: 'https://vapefiend.co.uk',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-thumbnail img'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 6,
          delay_between_requests: 10000
        },
        categories: [SourceCategory.VAPORIZERS],
        reliability_score: 7
      },
      
      // Smoking Accessories
      {
        name: 'Everyone Does It',
        base_url: 'https://everyonedoesit.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-img'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 10,
          delay_between_requests: 6000
        },
        categories: [SourceCategory.ACCESSORIES],
        reliability_score: 8
      },
      {
        name: 'Grass City',
        base_url: 'https://grasscity.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product-photo__img'],
          product_titles: ['.product-title', '.product-name'],
          product_links: ['.product-title a', '.product-name a']
        },
        rate_limit: {
          requests_per_minute: 8,
          delay_between_requests: 7500
        },
        categories: [SourceCategory.ACCESSORIES],
        reliability_score: 9
      },
      {
        name: 'Shiva Online',
        base_url: 'https://shivaonline.co.uk',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img', '.product__image'],
          product_titles: ['.product-title', '.product__title'],
          product_links: ['.product-title a', '.product__title a']
        },
        rate_limit: {
          requests_per_minute: 6,
          delay_between_requests: 10000
        },
        categories: [SourceCategory.ACCESSORIES],
        reliability_score: 7
      }
    ];
  }
}
