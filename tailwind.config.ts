
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./src/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				sage: {
					50: '#f2fce2',
					100: '#e7eedf',
					200: '#d5e0c9',
					300: '#b1c19e',
					400: '#8ca173',
					500: '#6c8450',
					600: '#566a3d',
					700: '#445433',
					800: '#393e2c',
					900: '#2e332a',
					950: '#1a1e17',
				},
				clay: {
					50: '#f7f3e9',
					100: '#e6deca',
					200: '#d5c8a8',
					300: '#c3ae84',
					400: '#b69c6b',
					500: '#a68a55',
					600: '#8a7047',
					700: '#6e593a',
					800: '#554532',
					900: '#403e43',
					950: '#221f26',
				},
				// Social media colors
				facebook: '#1877F2',
				twitter: '#1DA1F2',
				linkedin: '#0A66C2',
				pinterest: '#E60023',
				instagram: '#E4405F'
			},
			fontFamily: {
				sans: ['Inter', 'sans-serif'],
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' },
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' },
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'options-indicator': {
					'0%': {
						transform: 'scale(1)',
						opacity: '0.7'
					},
					'50%': {
						transform: 'scale(1.2)',
						opacity: '1'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '0.7'
					}
				},
				'fade-out': {
					'0%': {
						opacity: '1',
						transform: 'translateY(0)'
					},
					'100%': {
						opacity: '0',
						transform: 'translateY(10px)'
					}
				},
				'scale-in': {
					'0%': {
						transform: 'scale(0.95)',
						opacity: '0'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'float': {
					'0%, 100%': {
						transform: 'translateY(0)',
					},
					'50%': {
						transform: 'translateY(-10px)',
					},
				},
				'gentle-pulse': {
					'0%, 100%': {
						transform: 'scale(1)',
						boxShadow: '0 0 0 0 rgba(108, 132, 80, 0.2)',
					},
					'50%': {
						transform: 'scale(1.03)',
						boxShadow: '0 0 10px 5px rgba(108, 132, 80, 0.2)',
					}
				},
				'bounce-slow': {
					'0%, 100%': {
						transform: 'translateY(0)',
						animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
					},
					'50%': {
						transform: 'translateY(-15%)',
						animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
					},
				},
				'pulse-glow': {
					'0%': {
						transform: 'scale(1)',
						boxShadow: '0 0 0 0 rgba(108, 132, 80, 0.4)',
					},
					'25%': {
						transform: 'scale(1.02)',
						boxShadow: '0 0 5px 2px rgba(108, 132, 80, 0.2)',
					},
					'50%': {
						transform: 'scale(1.04)',
						boxShadow: '0 0 8px 4px rgba(108, 132, 80, 0.3)',
					},
					'75%': {
						transform: 'scale(1.02)',
						boxShadow: '0 0 5px 2px rgba(108, 132, 80, 0.2)',
					},
					'100%': {
						transform: 'scale(1)',
						boxShadow: '0 0 0 0 rgba(108, 132, 80, 0.4)',
					},
				},
				'shimmer': {
					'100%': {
						transform: 'translateX(100%)',
					},
				},
				'pulse-subtle': {
					'0%, 100%': {
						opacity: '1',
					},
					'50%': {
						opacity: '0.85',
					},
				},
				'fadeIn': {
					'0%': {
						opacity: '0',
					},
					'100%': {
						opacity: '1',
					},
				},
				'float-leaf': {
					'0%': {
						transform: 'translateY(0) translateX(0)',
						opacity: '0.2',
					},
					'25%': {
						transform: 'translateY(-30px) translateX(15px)',
						opacity: '0.3',
					},
					'50%': {
						transform: 'translateY(-10px) translateX(30px)',
						opacity: '0.2',
					},
					'75%': {
						transform: 'translateY(-20px) translateX(15px)',
						opacity: '0.3',
					},
					'100%': {
						transform: 'translateY(0) translateX(0)',
						opacity: '0.2',
					},
				},
				'rotate-leaf': {
					'0%': {
						transform: 'rotate(0deg)',
					},
					'100%': {
						transform: 'rotate(360deg)',
					},
				},
				'ping-once': {
					'0%': {
						transform: 'scale(1)',
						opacity: '1',
					},
					'50%': {
						transform: 'scale(1.5)',
						opacity: '0.5',
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '0',
					},
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.6s ease-out',
				'fade-out': 'fade-out 0.6s ease-out',
				'options-indicator': 'options-indicator 2s ease-in-out infinite',
				'scale-in': 'scale-in 0.6s ease-out',
				'float': 'float 3s ease-in-out infinite',
				'bounce-slow': 'bounce-slow 3s ease-in-out infinite',
				'gentle-pulse': 'gentle-pulse 8s ease-in-out infinite',
				'pulse-glow': 'pulse-glow 6s ease-in-out infinite',
				'shimmer': 'shimmer 1.5s ease-in-out infinite',
				'float-leaf': 'float-leaf 15s ease-in-out infinite',
				'rotate-leaf': 'rotate-leaf 20s linear infinite',
				'pulse-subtle': 'pulse-subtle 2s ease-in-out infinite',
				'fadeIn': 'fadeIn 0.3s ease-in-out',
				'ping-once': 'ping-once 0.8s ease-in-out',
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
