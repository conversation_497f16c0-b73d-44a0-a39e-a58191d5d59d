/**
 * Smart image caching system to reduce API calls and improve performance
 */

interface CachedImage {
  url: string;
  thumbnail?: string;
  title?: string;
  source: string;
  timestamp: number;
  topic: string;
}

interface CacheEntry {
  images: CachedImage[];
  timestamp: number;
  expiresAt: number;
}

class ImageCache {
  private cache = new Map<string, CacheEntry>();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached topics
  private readonly STORAGE_KEY = 'bitsnbongs_image_cache';

  constructor() {
    this.loadFromStorage();
    this.startCleanupInterval();
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.cache = new Map(Object.entries(data));
        console.log(`Loaded ${this.cache.size} cached image topics`);
      }
    } catch (error) {
      console.warn('Failed to load image cache from storage:', error);
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveToStorage(): void {
    try {
      const data = Object.fromEntries(this.cache);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save image cache to storage:', error);
    }
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    // If cache is still too large, remove oldest entries
    if (this.cache.size > this.MAX_CACHE_SIZE) {
      const entries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, this.cache.size - this.MAX_CACHE_SIZE);
      toRemove.forEach(([key]) => this.cache.delete(key));
      removedCount += toRemove.length;
    }

    if (removedCount > 0) {
      console.log(`Cleaned up ${removedCount} expired cache entries`);
      this.saveToStorage();
    }
  }

  /**
   * Start periodic cleanup
   */
  private startCleanupInterval(): void {
    setInterval(() => this.cleanup(), 60 * 60 * 1000); // Clean every hour
  }

  /**
   * Generate cache key from topic
   */
  private getCacheKey(topic: string): string {
    return topic.toLowerCase().trim().replace(/\s+/g, '_');
  }

  /**
   * Check if we have cached images for a topic
   */
  has(topic: string): boolean {
    const key = this.getCacheKey(topic);
    const entry = this.cache.get(key);

    if (!entry) return false;

    // Check if expired
    if (entry.expiresAt < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return entry.images.length > 0;
  }

  /**
   * Get cached images for a topic
   */
  get(topic: string): CachedImage[] {
    const key = this.getCacheKey(topic);
    const entry = this.cache.get(key);

    if (!entry || entry.expiresAt < Date.now()) {
      return [];
    }

    console.log(`Cache hit for topic: ${topic} (${entry.images.length} images)`);
    return [...entry.images]; // Return copy to prevent mutation
  }

  /**
   * Get a random cached image for a topic
   */
  getRandomImage(topic: string, excludeUrls: string[] = []): CachedImage | null {
    const images = this.get(topic);
    if (images.length === 0) return null;

    // Filter out excluded URLs to get different images
    const availableImages = excludeUrls.length > 0
      ? images.filter(img => !excludeUrls.includes(img.url))
      : images;

    if (availableImages.length === 0) {
      // If all images are excluded, return a random one anyway
      const randomIndex = Math.floor(Math.random() * images.length);
      return images[randomIndex];
    }

    const randomIndex = Math.floor(Math.random() * availableImages.length);
    return availableImages[randomIndex];
  }

  /**
   * Cache images for a topic
   */
  set(topic: string, images: CachedImage[]): void {
    const key = this.getCacheKey(topic);
    const now = Date.now();

    const entry: CacheEntry = {
      images: images.map(img => ({ ...img, topic, timestamp: now })),
      timestamp: now,
      expiresAt: now + this.CACHE_DURATION
    };

    this.cache.set(key, entry);
    console.log(`Cached ${images.length} images for topic: ${topic}`);

    this.saveToStorage();
  }

  /**
   * Add a single image to existing cache entry
   */
  addImage(topic: string, image: CachedImage): void {
    const existing = this.get(topic);

    // Check if image already exists
    const isDuplicate = existing.some(img => img.url === image.url);
    if (isDuplicate) return;

    const updatedImages = [...existing, { ...image, topic, timestamp: Date.now() }];
    this.set(topic, updatedImages);
  }

  /**
   * Clear all cached data
   */
  clear(): void {
    this.cache.clear();
    localStorage.removeItem(this.STORAGE_KEY);
    console.log('Image cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats(): { topics: number; totalImages: number; oldestEntry: number | null } {
    let totalImages = 0;
    let oldestEntry: number | null = null;

    for (const entry of this.cache.values()) {
      totalImages += entry.images.length;
      if (oldestEntry === null || entry.timestamp < oldestEntry) {
        oldestEntry = entry.timestamp;
      }
    }

    return {
      topics: this.cache.size,
      totalImages,
      oldestEntry
    };
  }

  /**
   * Get similar topics that might have cached images
   */
  getSimilarTopics(topic: string): string[] {
    const searchTerms = topic.toLowerCase().split(/\s+/);
    const similarTopics: string[] = [];

    for (const [cachedKey, entry] of this.cache.entries()) {
      if (entry.expiresAt < Date.now()) continue;

      const cachedTerms = cachedKey.split('_');
      const hasCommonTerms = searchTerms.some(term =>
        cachedTerms.some(cached => cached.includes(term) || term.includes(cached))
      );

      if (hasCommonTerms && cachedKey !== this.getCacheKey(topic)) {
        similarTopics.push(entry.images[0]?.topic || cachedKey);
      }
    }

    return similarTopics.slice(0, 3); // Return up to 3 similar topics
  }
}

// Create singleton instance
export const imageCache = new ImageCache();

// Export types for use in other modules
export type { CachedImage };
