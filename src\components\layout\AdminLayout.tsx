import { Link, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/auth.basic';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

import {
  Package,
  Tag,
  ShoppingCart,
  Users,
  Settings,
  Home,
  LogOut,
  Menu,
  X,
  Upload,
  FileUp,
  Briefcase,
  HelpCircle,
  BookOpen,
  Mail,
  Ticket,
  PercentSquare,
  Filter,
  Truck,
  Brain,
  Zap,
  Image
} from 'lucide-react';
import { useState } from 'react';

const sidebarItems = [
  { icon: Home, label: 'Dashboard', href: '/admin', exact: true },
  { icon: Package, label: 'Products', href: '/admin/products' },
  { icon: Tag, label: 'Categories', href: '/admin/categories' },
  { icon: Briefcase, label: 'Brands', href: '/admin/brands' },
  { icon: Filter, label: 'Seed Filters', href: '/admin/seed-filters' },
  { icon: ShoppingCart, label: 'Orders', href: '/admin/orders' },
  { icon: PercentSquare, label: 'Discount Codes', href: '/admin/discount-codes' },
  { icon: Truck, label: 'Shipping', href: '/admin/shipping' },
  { icon: Users, label: 'Users', href: '/admin/users' },
  { icon: BookOpen, label: 'Blog Posts', href: '/admin/blogs' },
  { icon: Mail, label: 'Newsletter', href: '/admin/newsletter' },
  { icon: Brain, label: 'AI Management', href: '/admin/ai-management' },
  { icon: Zap, label: 'Social Media AI', href: '/admin/social-media' },
  { icon: Image, label: 'Image Scraping', href: '/admin/image-scraping' },
  { icon: HelpCircle, label: 'FAQs', href: '/admin/faqs' },
];

const AdminLayout = () => {
  const { signOut, profile } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar toggle */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </div>

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 w-64 bg-white shadow-lg transform transition-transform z-40",
          sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-4 border-b">
            <h2 className="text-xl font-bold">Admin Dashboard</h2>
            <Button asChild variant="outline" className="mt-3 w-full">
              <Link to="/">← Back to Store</Link>
            </Button>
          </div>

          <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
            {sidebarItems.map((item) => {
              const isActive = item.exact
                ? location.pathname === item.href
                : location.pathname.startsWith(item.href);

              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    "flex items-center px-4 py-2 rounded-md group",
                    isActive
                      ? "bg-gray-100 text-primary font-medium"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className={cn(
                    "h-5 w-5 mr-3",
                    isActive ? "text-primary" : "text-gray-500 group-hover:text-gray-700"
                  )} />
                  {item.label}
                </Link>
              );
            })}
          </nav>

          <div className="p-4 border-t">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                  <Users size={16} />
                </div>
                <span className="ml-2 font-medium">
                  {profile?.first_name ? profile.first_name : 'Admin'}
                </span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => signOut()}
                title="Sign Out"
              >
                <LogOut size={18} />
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="w-full flex items-center justify-center gap-2"
              asChild
            >
              <Link to="/admin/settings">
                <Settings size={14} />
                Profile Settings
              </Link>
            </Button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className={cn(
        "flex-1 ml-0 lg:ml-64 transition-all",
        sidebarOpen && "lg:ml-64"
      )}>
        <main className="p-4 h-full overflow-auto">
          <Outlet />
        </main>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/20 z-30"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default AdminLayout;
