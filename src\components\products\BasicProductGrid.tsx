import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

// Very simple product interface
interface SimpleProduct {
  id: string;
  name: string;
  price: number;
}

interface BasicProductGridProps {
  categoryId?: string;
  subcategoryId?: string;
  searchQuery?: string;
}

const BasicProductGrid: React.FC<BasicProductGridProps> = ({
  categoryId,
  subcategoryId,
  searchQuery = '',
}) => {
  const [products, setProducts] = useState<SimpleProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        console.log('Fetching products with params:', { categoryId, subcategoryId, searchQuery });
        
        let query = supabase
          .from('products')
          .select('id, name, price');

        // Apply filters
        if (categoryId && categoryId !== 'all') {
          query = query.eq('category_id', categoryId);
        }
        
        if (subcategoryId && subcategoryId !== 'all') {
          query = query.eq('subcategory_id', subcategoryId);
        }
        
        if (searchQuery) {
          query = query.ilike('name', `%${searchQuery}%`);
        }
        
        const { data, error } = await query;
        
        if (error) {
          console.error('Error fetching products:', error);
          setError(error.message);
          setProducts([]);
          setLoading(false);
          return;
        }
        
        console.log('Products fetched:', data);
        
        if (!data || data.length === 0) {
          setProducts([]);
          setLoading(false);
          return;
        }
        
        setProducts(data);
        setLoading(false);
      } catch (err) {
        console.error('Exception fetching products:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setProducts([]);
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, [categoryId, subcategoryId, searchQuery]);
  
  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="text-center py-20 bg-red-50 rounded-lg">
        <h3 className="text-xl font-medium text-red-900">Error loading products</h3>
        <p className="mt-2 text-red-500">{error}</p>
      </div>
    );
  }
  
  if (products.length === 0) {
    return (
      <div className="text-center py-20 bg-sage-50 rounded-lg">
        <h3 className="text-xl font-medium text-gray-900">No products found</h3>
        <p className="mt-2 text-gray-500">
          Try adjusting your filters or check back later as we add new products to our store.
        </p>
      </div>
    );
  }
  
  // Very simple product display - just a list
  return (
    <div className="py-4">
      <h2 className="text-xl font-bold mb-4">Products ({products.length})</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div key={product.id} className="border p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <h3 className="font-medium">{product.name}</h3>
            <p className="text-lg font-bold mt-2">£{(product.price || 0).toFixed(2)}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BasicProductGrid;
