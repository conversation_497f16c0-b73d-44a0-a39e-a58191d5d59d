# Stock Synchronization System

## Overview

The Stock Synchronization System is designed to help you sync stock levels from your EPOS (Electronic Point of Sale) system with your e-commerce store. It also identifies incomplete products that could be activated with the right data.

## Key Features

### 🔄 **Stock Synchronization**
- Upload CSV files from your EPOS system
- Automatically match products using multiple strategies
- Update stock quantities, prices, and availability
- Preview changes before applying them

### 🔍 **Product Matching Strategies**
1. **SKU Matching** (Highest confidence - 100%)
2. **Barcode Matching** (95% confidence)
3. **Exact Name Matching** (90% confidence)
4. **Fuzzy Name Matching** (70%+ similarity, reduced confidence)

### 📊 **Incomplete Product Analysis**
- Identifies inactive products with missing data
- Flags products that can be activated with CSV data
- Provides detailed reports on what's missing
- Prioritizes products ready for activation

### 📈 **Comprehensive Reporting**
- Sync summary with counts and statistics
- Incomplete products report with activation potential
- Unmatched CSV rows for manual review
- Exportable reports in CSV format

## How to Use

### 1. Access the Stock Sync Manager

Navigate to **Admin → EPOS Integration → Stock Sync** tab.

### 2. Upload Your CSV File

**Required CSV Columns:**
- `sku` - Stock Keeping Unit (primary matching field)
- `product_name` - Product name (fallback matching)
- `stock_quantity` - Current stock level
- `price` - Product price (optional)
- `sale_price` - Sale price (optional)
- `barcode` - Product barcode (optional)

**Optional Columns:**
- `description` - Product description
- `category` - Product category
- `brand` - Product brand

### 3. Preview Changes

Click **"Preview Changes"** to see what will happen without making any actual changes:
- Shows how many products will be updated
- Identifies products that can be activated
- Lists unmatched CSV rows
- Analyzes incomplete products

### 4. Review Results

The system provides four tabs of information:

#### **Summary Tab**
- Visual overview with counts
- Updated, activated, unmatched, and failed products
- Overall sync summary

#### **Incomplete Products Tab**
- Lists inactive products with missing data
- Shows what's missing (image, price, description, etc.)
- Indicates which products can be activated
- Highlights products with CSV matches
- Export functionality for detailed analysis

#### **Unmatched Tab**
- CSV rows that couldn't be matched to existing products
- Potential new products to add manually
- Export functionality for review

#### **Details Tab**
- Information about matching strategies
- Activation criteria explanation
- Technical details

### 5. Apply Changes

Once you're satisfied with the preview, click **"Apply Changes"** to:
- Update stock quantities
- Activate eligible products
- Update prices (if provided)
- Add missing SKUs

## Product Activation Criteria

A product can be automatically activated if it has:
- ✅ **Name** (required)
- ✅ **Price** (required)
- ✅ **Stock Quantity** (required)
- 📷 **Image** (recommended but not required)
- 📝 **Description** (recommended but not required)

## Matching Examples

### Perfect SKU Match
```csv
sku,product_name,stock_quantity,price
"CBD001","CBD Oil 10ml",25,29.99
```
→ Matches product with SKU "CBD001" (100% confidence)

### Name-Based Matching
```csv
sku,product_name,stock_quantity,price
"NEW001","Hemp Seed Oil 250ml",15,19.99
```
→ Matches product named "Hemp Seed Oil 250ml" (90% confidence)

### Fuzzy Matching
```csv
sku,product_name,stock_quantity,price
"HEMP001","Hemp Oil 250ml Organic",8,22.99
```
→ Might match "Hemp Oil 250ml" (75% confidence)

## Benefits for Your Business

### 🎯 **Immediate Value**
- Sync 1000+ products in minutes
- Automatically activate ready products
- Identify data completion opportunities

### 📋 **Data Quality Improvement**
- Find products missing images
- Identify products without prices
- Locate products needing descriptions
- Prioritize completion tasks

### 🔄 **Ongoing Maintenance**
- Regular stock level updates
- Price synchronization
- Inventory management
- EPOS system integration

## Best Practices

### 📁 **CSV Preparation**
1. Export from your EPOS system with all available fields
2. Ensure SKUs are consistent and unique
3. Include product names for fallback matching
4. Verify stock quantities are accurate

### 🔍 **Before Applying Changes**
1. Always preview first
2. Review unmatched products
3. Check incomplete product opportunities
4. Export reports for record-keeping

### 📊 **After Sync**
1. Review activation results
2. Complete missing product data
3. Test activated products on the site
4. Schedule regular syncs

## Troubleshooting

### ❌ **No Products Matched**
- Check CSV column names
- Verify SKU format consistency
- Ensure product names are similar
- Check for extra spaces or characters

### ⚠️ **Low Match Confidence**
- Review fuzzy matches manually
- Update product names for better matching
- Add missing SKUs to products
- Use barcode matching if available

### 🔧 **Products Not Activating**
- Check if products have required fields (name, price, stock)
- Verify CSV data completeness
- Review activation criteria
- Check for data validation errors

## Technical Details

### Database Updates
- Updates `stock_quantity` field
- Sets `in_stock` based on stock levels
- Updates `is_active` for eligible products
- Records `last_synced_at` timestamp
- Updates `sync_status` to 'synced'

### Performance
- Processes products in batches
- Uses efficient database queries
- Provides progress feedback
- Handles large CSV files (1000+ rows)

### Security
- Validates CSV data before processing
- Prevents SQL injection
- Logs all changes for audit trail
- Requires admin permissions

## Integration with Existing EPOS System

This stock sync system works alongside the existing EPOS integration:
- **Real-time sync**: For live inventory updates
- **CSV sync**: For bulk updates and data completion
- **SKU management**: For product mapping
- **Import/Export**: For data exchange

Both systems complement each other for comprehensive inventory management.
