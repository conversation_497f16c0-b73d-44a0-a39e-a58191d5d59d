# Windsurf Integration Guide for Dutch Passion Filter System

This guide provides step-by-step instructions for implementing a Dutch Passion-style filter system in Windsurf, a no-code AI editor. The implementation will include product filtering, rich media display, and detailed product pages.

## Table of Contents
1. [Data Import](#data-import)
2. [Filter UI Implementation](#filter-ui-implementation)
3. [Product Grid Implementation](#product-grid-implementation)
4. [Product Detail Page](#product-detail-page)
5. [Media Asset Integration](#media-asset-integration)
6. [Filter Logic Implementation](#filter-logic-implementation)
7. [Product Categorization Tool](#product-categorization-tool)

## Data Import

### Step 1: Prepare Your Data Collections

In Windsurf, you'll need to create the following collections:

1. **Products Collection**
   - Import the `dutch_passion_all_products.json` file
   - Key fields: id, name, price, seed_type, url, description

2. **Filters Collection**
   - Import the `dutch_passion_all_filters.json` file
   - Key fields: filter_category, filter_options

3. **Product-Filter Relationships Collection**
   - Import the `dutch_passion_products_with_filters.json` file
   - This maps products to their filter attributes

4. **Rich Media Collection**
   - Import the `dutch_passion_rich_media_complete.json` file
   - Contains terpene profiles, cannabinoid data, and image references

### Step 2: Upload Media Assets

1. Create a media library in Windsurf with the following folders:
   - `product_images/` - For main product images
   - `icons/` - For characteristic icons
   - `terpene_charts/` - For terpene profile visualizations
   - `cannabinoid_charts/` - For THC/CBD visualizations

2. Upload all files from the corresponding folders in the `dutch_passion_media/` directory

### Step 3: Link Media Assets to Products

In Windsurf, create relationships between:
- Products and their images
- Products and their characteristic icons
- Products and their terpene/cannabinoid charts

## Filter UI Implementation

### Step 1: Create the Filter Sidebar

1. Add a sidebar container with these sections:
   ```html
   <div class="filter-sidebar">
     <div class="filter-section" data-category="flowertime">
       <h3>Flowertime</h3>
       <div class="filter-options">
         <!-- Options will be populated dynamically -->
       </div>
     </div>
     
     <!-- Repeat for each filter category -->
     <div class="filter-section" data-category="yield">
       <h3>Yield</h3>
       <div class="filter-options">
         <!-- Options will be populated dynamically -->
       </div>
     </div>
     
     <!-- Continue with other filter categories -->
   </div>
   ```

2. Style the sidebar using Windsurf's style editor:
   - Set width to 25% for desktop, 100% for mobile
   - Add collapsible behavior for mobile
   - Style filter headers and options to match Dutch Passion

### Step 2: Create Filter Option Template

For each filter option, create a template like:
```html
<div class="filter-option">
  <input type="checkbox" id="{{option_id}}" data-filter="{{filter_category}}" data-value="{{option_value}}">
  <label for="{{option_id}}">{{option_name}} ({{product_count}})</label>
</div>
```

### Step 3: Populate Filter Options

Use Windsurf's data binding to:
1. Loop through the Filters Collection
2. For each filter category, populate its options
3. Show product counts for each option

### Step 4: Add "See More/Less" Toggle

For filter categories with many options:
```html
<div class="filter-section" data-category="seed_family">
  <h3>Seed Family</h3>
  <div class="filter-options limited">
    <!-- First 5 options shown by default -->
  </div>
  <button class="see-more-btn">See more</button>
</div>
```

Add toggle behavior using Windsurf's action editor.

## Product Grid Implementation

### Step 1: Create the Product Grid Container

```html
<div class="product-grid-container">
  <div class="product-controls">
    <div class="product-count">Showing <span id="shown-count">X</span> of <span id="total-count">Y</span> products</div>
    <div class="product-sort">
      <label for="sort-select">Sort by:</label>
      <select id="sort-select">
        <option value="relevance">Relevance</option>
        <option value="name-asc">Name (A-Z)</option>
        <option value="name-desc">Name (Z-A)</option>
        <option value="price-asc">Price (Low to High)</option>
        <option value="price-desc">Price (High to Low)</option>
      </select>
    </div>
  </div>
  
  <div class="product-grid">
    <!-- Products will be populated here -->
  </div>
  
  <div class="pagination">
    <!-- Pagination controls -->
  </div>
</div>
```

### Step 2: Create Product Card Template

```html
<div class="product-card" data-product-id="{{product.id}}">
  <div class="product-image">
    <img src="{{product.main_image_url}}" alt="{{product.name}}">
  </div>
  <div class="product-info">
    <h3 class="product-name">{{product.name}}</h3>
    <div class="product-type">{{product.seed_type}}</div>
    <div class="product-price">€{{product.price}}</div>
  </div>
  <div class="product-icons">
    <!-- Show up to 3 characteristic icons -->
    {{#each product.icons limit=3}}
      <img src="{{this.image}}" alt="{{this.name}}" title="{{this.name}}">
    {{/each}}
  </div>
  <a href="/product/{{product.id}}" class="view-product-btn">View Details</a>
</div>
```

### Step 3: Implement Sorting and Pagination

Use Windsurf's built-in data controls to:
1. Sort products based on selected criteria
2. Implement pagination with configurable items per page
3. Update product count display

## Product Detail Page

### Step 1: Create the Product Detail Template

```html
<div class="product-detail-container">
  <div class="product-gallery">
    <div class="main-image">
      <img src="{{product.main_image_url}}" alt="{{product.name}}" id="main-product-image">
    </div>
    <div class="thumbnail-gallery">
      {{#each product.images}}
        <div class="thumbnail" data-image-url="{{this.url}}">
          <img src="{{this.url}}" alt="{{../product.name}} view {{@index}}">
        </div>
      {{/each}}
    </div>
  </div>
  
  <div class="product-info">
    <h1 class="product-name">{{product.name}}</h1>
    <div class="product-type-badge">{{product.seed_type}}</div>
    
    <div class="product-characteristics">
      {{#each product.icons}}
        <div class="characteristic">
          <img src="{{this.image}}" alt="{{this.name}}">
          <span>{{this.name}}</span>
        </div>
      {{/each}}
    </div>
    
    <div class="product-description">
      {{product.description}}
    </div>
    
    {{#if product.terpene_profile}}
      <div class="terpene-profile">
        <h3>Terpene Profile</h3>
        <div class="terpene-chart">
          <img src="{{product.terpene_profile.chart_image}}" alt="Terpene profile chart">
        </div>
        <div class="terpene-percentages">
          {{#each product.terpene_profile}}
            {{#unless @key includes "chart"}}
              <div class="terpene">
                <span class="terpene-name">{{@key}}</span>
                <span class="terpene-value">{{this}}</span>
              </div>
            {{/unless}}
          {{/each}}
        </div>
      </div>
    {{/if}}
    
    {{#if product.cannabinoid_profile}}
      <div class="cannabinoid-profile">
        <h3>Cannabinoid Profile</h3>
        {{#if product.cannabinoid_profile.chart_image}}
          <div class="cannabinoid-chart">
            <img src="{{product.cannabinoid_profile.chart_image}}" alt="Cannabinoid profile chart">
          </div>
        {{/if}}
        <div class="cannabinoid-percentages">
          {{#each product.cannabinoid_profile}}
            {{#unless @key includes "chart"}}
              <div class="cannabinoid">
                <span class="cannabinoid-name">{{@key}}</span>
                <span class="cannabinoid-value">{{this}}</span>
              </div>
            {{/unless}}
          {{/each}}
        </div>
      </div>
    {{/if}}
    
    <div class="product-pricing">
      <h3>Seed Packs</h3>
      <div class="seed-packs">
        <!-- Seed pack options with prices -->
      </div>
    </div>
  </div>
</div>
```

### Step 2: Implement Image Gallery Interaction

Add JavaScript in Windsurf's code editor to:
1. Switch main image when thumbnails are clicked
2. Implement image zoom on hover/click
3. Add mobile-friendly swipe gestures

## Media Asset Integration

### Step 1: Configure Media References

In Windsurf, ensure all media assets are properly referenced:

1. **Product Images**
   - Main product image
   - Additional gallery images
   - Thumbnail images

2. **Characteristic Icons**
   - Yield icons (XXL, XL, etc.)
   - Effect icons (Indica, Sativa, Hybrid)
   - Growing condition icons (Indoor, Outdoor)

3. **Terpene & Cannabinoid Charts**
   - Terpene profile wheels
   - THC/CBD meters
   - Cannabinoid charts

### Step 2: Implement Responsive Image Handling

Configure Windsurf to:
1. Serve appropriate image sizes based on device
2. Implement lazy loading for performance
3. Provide fallback images when specific assets are missing

## Filter Logic Implementation

### Step 1: Set Up Filter Event Handlers

In Windsurf's action editor, create event handlers for:

1. **Filter Selection**
   ```javascript
   function handleFilterSelection(event) {
     const checkbox = event.target;
     const category = checkbox.dataset.filter;
     const value = checkbox.dataset.value;
     
     // Update active filters
     if (checkbox.checked) {
       addActiveFilter(category, value);
     } else {
       removeActiveFilter(category, value);
     }
     
     // Apply filters and update product display
     applyFilters();
   }
   ```

2. **Filter Clearing**
   ```javascript
   function clearAllFilters() {
     // Uncheck all filter checkboxes
     document.querySelectorAll('.filter-option input[type="checkbox"]').forEach(checkbox => {
       checkbox.checked = false;
     });
     
     // Clear active filters
     activeFilters = {};
     
     // Reset product display
     applyFilters();
   }
   ```

### Step 2: Implement Filter Logic

Create the core filtering function:

```javascript
function applyFilters() {
  // Get all products
  const allProducts = getProductsCollection();
  
  // Filter products based on active filters
  let filteredProducts = allProducts;
  
  // For each filter category
  Object.keys(activeFilters).forEach(category => {
    const selectedOptions = activeFilters[category];
    
    if (selectedOptions.length > 0) {
      // Filter products that match ANY option within THIS category (OR logic)
      filteredProducts = filteredProducts.filter(product => {
        return product.filters.some(filter => 
          filter.category === category && 
          selectedOptions.includes(filter.value)
        );
      });
    }
  });
  
  // Apply sorting
  applySorting(filteredProducts);
  
  // Update product display
  updateProductDisplay(filteredProducts);
  
  // Update URL parameters for shareable filtered views
  updateUrlParameters();
}
```

### Step 3: Implement URL Parameter Handling

Add functions to:
1. Update URL with current filter selections
2. Parse URL parameters on page load to restore filter state
3. Enable sharing of filtered product views

## Product Categorization Tool

### Step 1: Create Matching Interface

Build a tool to help categorize your client's existing products:

```html
<div class="categorization-tool">
  <div class="tool-header">
    <h2>Product Categorization Tool</h2>
    <p>Match your existing products to Dutch Passion categories</p>
  </div>
  
  <div class="product-matcher">
    <div class="your-product">
      <h3>Your Product</h3>
      <select id="your-product-select">
        <!-- Your products will be populated here -->
      </select>
      <div class="product-details" id="your-product-details">
        <!-- Selected product details will appear here -->
      </div>
    </div>
    
    <div class="dutch-passion-matches">
      <h3>Potential Dutch Passion Matches</h3>
      <div class="matches-list" id="matches-list">
        <!-- Matching products will appear here -->
      </div>
    </div>
  </div>
  
  <div class="category-assignment">
    <h3>Assign Categories</h3>
    <div class="category-options">
      <!-- Category options will be populated here -->
    </div>
    <button id="save-categories-btn">Save Categories</button>
  </div>
</div>
```

### Step 2: Implement Matching Logic

Create functions to:
1. Find similar Dutch Passion products based on name, type, or characteristics
2. Suggest appropriate filter categories for your products
3. Save categorization data for your product database

### Step 3: Build Export Functionality

Add features to:
1. Export categorized product data as CSV or JSON
2. Bulk categorize multiple products
3. Review and adjust categorizations

## Final Steps

1. **Test the Implementation**
   - Verify all filters work correctly
   - Ensure product display is responsive
   - Check that all media assets load properly

2. **Optimize Performance**
   - Implement caching for filter results
   - Optimize image loading
   - Minimize database queries

3. **Launch and Monitor**
   - Deploy the implementation
   - Monitor user interactions
   - Gather feedback for improvements

---

This implementation guide provides a comprehensive framework for replicating Dutch Passion's filtering system in Windsurf. Adjust the specific implementation details based on your Windsurf environment and specific requirements.
