import React from 'react';
import { Ruler } from 'lucide-react';

interface SizeSelectorProps {
  sizes: string[];
  selectedSize: string | null;
  onSelectSize: (size: string) => void;
  disabledSizes?: string[];
  sizeType?: 'clothing' | 'numeric' | 'dimensional' | 'generic';
}

export const SizeSelector: React.FC<SizeSelectorProps> = ({
  sizes,
  selectedSize,
  onSelectSize,
  disabledSizes = [],
  sizeType = 'generic',
}) => {
  // Determine if a size is a standard clothing size
  const isClothingSize = (size: string): boolean => {
    const clothingSizes = ['xs', 's', 'm', 'l', 'xl', '2xl', '3xl', '4xl', '5xl', 'xxs', 'xxl', 'xxxl', 'xxxxl', 'xxxxxl'];
    return clothingSizes.includes(size.toLowerCase());
  };

  // Determine if a size is numeric (e.g., 10, 12, 14)
  const isNumericSize = (size: string): boolean => {
    return !isNaN(Number(size));
  };

  // Determine if a size is dimensional (e.g., 10x12, 5cm, 3in)
  const isDimensionalSize = (size: string): boolean => {
    return /\d+\s*[x×]\s*\d+|\d+\s*(cm|mm|m|in|ft)/i.test(size);
  };

  // Auto-detect size type if not specified
  const detectSizeType = (): string => {
    if (sizeType !== 'generic') return sizeType;

    // Check the first few sizes to determine type
    const sampleSizes = sizes.slice(0, 3);

    if (sampleSizes.some(isClothingSize)) return 'clothing';
    if (sampleSizes.some(isDimensionalSize)) return 'dimensional';
    if (sampleSizes.some(isNumericSize)) return 'numeric';

    return 'generic';
  };

  const detectedSizeType = detectSizeType();

  // Function to handle size selection with event propagation stopped
  const handleSizeClick = (size: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onSelectSize(size);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Size
        </label>
        <span
          className="text-xs text-primary flex items-center gap-1 hover:underline cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            window.alert('Size guide would open here');
          }}
        >
          <Ruler className="h-3 w-3" />
          Size Guide
        </span>
      </div>

      <div className="flex flex-wrap gap-2">
        {sizes.map((size) => {
          const isDisabled = disabledSizes.includes(size);
          const isSelected = selectedSize === size;

          return (
            <button
              key={size}
              type="button"
              disabled={isDisabled}
              onClick={(e) => handleSizeClick(size, e)}
              style={{
                padding: '8px 12px',
                borderRadius: '6px',
                fontWeight: '500',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                backgroundColor: isSelected ? '#4CAF50' : 'white',
                color: isSelected ? 'white' : '#333',
                border: isSelected ? 'none' : '1px solid #ddd',
                opacity: isDisabled ? 0.5 : 1,
                position: 'relative',
                zIndex: 100,
                boxShadow: isSelected ? '0 2px 8px rgba(76, 175, 80, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.2s ease'
              }}
            >
              {size}
            </button>
          );
        })}
      </div>

      {detectedSizeType === 'dimensional' && (
        <p className="text-xs text-gray-500 mt-1">
          Dimensions shown in the format Width × Height
        </p>
      )}

      {selectedSize && (
        <p className="text-sm text-gray-600 mt-1">
          Selected: <span className="font-medium">{selectedSize}</span>
        </p>
      )}
    </div>
  );
};
