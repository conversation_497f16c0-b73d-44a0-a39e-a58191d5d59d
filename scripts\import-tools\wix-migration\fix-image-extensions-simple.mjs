// <PERSON>ript to replace all image extensions with .webp in the database
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('Error: Supabase key is missing. Please check your .env file.');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to replace image extensions with .webp
async function replaceImageExtensions() {
  console.log('Starting to replace image extensions with .webp...');
  
  try {
    // Fetch all products
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products in the database`);
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Function to replace image extension with .webp and remove ~mv2
      const replaceExtension = (imageUrl) => {
        if (!imageUrl) return null;
        
        // Skip if the URL doesn't point to Supabase storage
        if (!imageUrl.includes('/storage/v1/object/public/product-images/')) {
          return imageUrl;
        }
        
        // Skip if the URL already ends with .webp and doesn't have ~mv2
        if (imageUrl.endsWith('.webp') && !imageUrl.includes('~mv2')) {
          return imageUrl;
        }
        
        // Extract parts of the URL
        const urlParts = imageUrl.split('/');
        const filename = urlParts.pop();
        
        // Remove ~mv2 and change extension to .webp
        const filenameWithoutExtension = filename.split('.')[0].replace(/~mv2$/, '');
        const newFilename = `${filenameWithoutExtension}.webp`;
        
        // Reconstruct the URL
        urlParts.push(newFilename);
        return urlParts.join('/');
      };
      
      // Replace main image extension
      if (product.image) {
        const updatedImageUrl = replaceExtension(product.image);
        if (updatedImageUrl && updatedImageUrl !== product.image) {
          updates.image = updatedImageUrl;
          needsUpdate = true;
        }
      }
      
      // Replace additional image extensions
      if (product.additional_images && Array.isArray(product.additional_images) && 
          product.additional_images.length > 0) {
        const updatedAdditionalImages = product.additional_images
          .map(replaceExtension)
          .filter(Boolean);
        
        // Check if any URLs were changed
        const hasChanges = JSON.stringify(updatedAdditionalImages) !== 
                          JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = updatedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error replacing image extensions:', error);
  }
}

// Run the script
replaceImageExtensions().catch(console.error);
