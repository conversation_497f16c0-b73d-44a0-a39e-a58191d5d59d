// This script tests the variant components by adding variants to a product
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Example option definitions with special colors
const optionDefinitions = {
  'Size': ['Small', 'Medium', 'Large', 'XL'],
  'Color': ['Red', 'Blue', 'Gold', 'Rasta', 'Metallic', 'Holographic'],
  'Pack Size': ['3 Pack', '5 Pack', '10 Pack']
};

async function fetchProduct() {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id, name')
      .limit(1)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('No products found in the database');
    }

    console.log('✅ Found product:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching product:', error);
    return null;
  }
}

async function addOptionDefinitions(productId) {
  try {
    const { data, error } = await supabase
      .from('products')
      .update({ option_definitions: optionDefinitions })
      .eq('id', productId)
      .select();

    if (error) {
      throw error;
    }

    console.log('✅ Added option definitions to product:', data);
    return true;
  } catch (error) {
    console.error('❌ Error adding option definitions:', error);
    return false;
  }
}

async function addVariants(productId, productName) {
  try {
    // Create variants based on option definitions
    const variants = [];

    // Generate all combinations of options
    for (const size of optionDefinitions['Size']) {
      for (const color of optionDefinitions['Color']) {
        for (const packSize of optionDefinitions['Pack Size']) {
          // Calculate price based on options
          let basePrice = 9.99;

          // Adjust price based on size
          if (size === 'Medium') basePrice += 3;
          if (size === 'Large') basePrice += 5;
          if (size === 'XL') basePrice += 7;

          // Adjust price based on pack size
          if (packSize === '5 Pack') basePrice += 5;
          if (packSize === '10 Pack') basePrice += 15;

          // Adjust price for premium colors
          if (['Gold', 'Rasta', 'Metallic', 'Holographic'].includes(color)) {
            basePrice += 2;
          }

          // Create variant
          variants.push({
            variant_name: `${productName} - ${size}/${color}/${packSize}`,
            option_combination: { 'Size': size, 'Color': color, 'Pack Size': packSize },
            price: basePrice,
            stock_quantity: Math.floor(Math.random() * 20) + 1 // Random stock between 1-20
          });
        }
      }
    }

    // Prepare variants data
    const variantsToCreate = variants.map(variant => ({
      product_id: productId,
      variant_name: variant.variant_name,
      sku: `${productId.substring(0, 6)}-${variant.option_combination.Size?.substring(0, 1)}${variant.option_combination.Color?.substring(0, 1)}${variant.option_combination['Pack Size']?.replace(/\D/g, '')}`,
      price: variant.price,
      sale_price: null,
      stock_quantity: variant.stock_quantity,
      in_stock: true,
      image: null,
      option_combination: variant.option_combination,
      is_active: true,
      external_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    console.log(`Creating ${variantsToCreate.length} variants...`);

    // Insert variants in batches to avoid hitting request size limits
    const batchSize = 20;
    for (let i = 0; i < variantsToCreate.length; i += batchSize) {
      const batch = variantsToCreate.slice(i, i + batchSize);

      const { data, error } = await supabase
        .from('product_variants')
        .insert(batch)
        .select();

      if (error) {
        throw error;
      }

      console.log(`✅ Added batch ${i/batchSize + 1} of variants (${batch.length} variants)`);
    }

    return true;
  } catch (error) {
    console.error('❌ Error adding variants:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing variant components...');

  // Step 1: Fetch a product
  const product = await fetchProduct();
  if (!product) {
    console.error('❌ Failed to fetch a product. Aborting.');
    return;
  }

  const { id: productId, name: productName } = product;

  // Step 2: Add option definitions
  const optionsAdded = await addOptionDefinitions(productId);
  if (!optionsAdded) {
    console.error('❌ Failed to add option definitions. Aborting.');
    return;
  }

  // Step 3: Add variants
  const variantsAdded = await addVariants(productId, productName);
  if (!variantsAdded) {
    console.error('❌ Failed to add variants.');
    return;
  }

  console.log('✅ Successfully added variants to product!');
  console.log('Product ID:', productId);
  console.log('Product Name:', productName);
  console.log('');
  console.log('Now you can test the variant components in the admin UI:');
  console.log('1. Go to the Products page');
  console.log('2. Find the product with the variant badge');
  console.log('3. Click Edit to open the product form');
  console.log('4. Test the variant management UI');
}

// Run the script
main().catch(console.error);
