import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface VariantBadgeProps {
  count: number;
  onClick?: () => void;
}

export function VariantBadge({ count, onClick }: VariantBadgeProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={`cursor-pointer ${count > 0 ? 'bg-primary/10 hover:bg-primary/20' : 'bg-muted hover:bg-muted/80'}`}
            onClick={onClick}
          >
            {count} {count === 1 ? 'Variant' : 'Variants'}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to view and manage variants</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
