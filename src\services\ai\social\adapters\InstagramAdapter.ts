/**
 * Instagram Adapter - Visual Storytelling AI
 *
 * "The Matrix has you... but Instagram has your audience!" 📸
 *
 * Specialized AI for Instagram's visual-first platform
 * Optimized for Stories, Posts, Reels, and maximum engagement
 */

import { socialMediaAI } from '../SocialMediaAI';

export interface InstagramContent {
  post_type: 'photo' | 'carousel' | 'reel' | 'story';
  caption: string;
  hashtags: string[];
  story_elements?: {
    stickers: string[];
    polls: string[];
    questions: string[];
  };
  reel_hooks: string[];
  carousel_slides?: string[];
  visual_suggestions: {
    composition: string;
    lighting: string;
    colors: string[];
    props: string[];
  };
}

export interface InstagramCampaign {
  main_post: InstagramContent;
  story_series: InstagramContent[];
  reel_content: InstagramContent;
  engagement_strategy: {
    optimal_times: string[];
    hashtag_strategy: string;
    story_highlights: string[];
  };
}

export class InstagramAdapter {
  private static instance: InstagramAdapter;

  // Instagram-specific optimization data
  private instagramTrends = {
    popular_formats: ['carousel', 'reel', 'story'],
    trending_audio: ['trending_sound_1', 'viral_audio_2', 'popular_beat_3'],
    engagement_boosters: ['polls', 'questions', 'quizzes', 'countdowns'],
    visual_trends: ['minimalist', 'lifestyle', 'behind_scenes', 'product_focus'],
    optimal_posting_times: ['11:00', '14:00', '17:00', '20:00']
  };

  private constructor() {}

  static getInstance(): InstagramAdapter {
    if (!InstagramAdapter.instance) {
      InstagramAdapter.instance = new InstagramAdapter();
    }
    return InstagramAdapter.instance;
  }

  /**
   * Generate complete Instagram campaign for a product
   */
  async generateInstagramCampaign(productData: {
    name: string;
    category: string;
    description?: string;
    price?: number;
    features?: string[];
  }): Promise<InstagramCampaign> {

    try {
      // Generate main feed post
      const mainPost = await this.generateMainPost(productData);

      // Generate story series (3-5 stories)
      const storySeries = await this.generateStorySeries(productData);

      // Generate reel content
      const reelContent = await this.generateReelContent(productData);

      // Create engagement strategy
      const engagementStrategy = this.createEngagementStrategy(productData);

      return {
        main_post: mainPost,
        story_series: storySeries,
        reel_content: reelContent,
        engagement_strategy: engagementStrategy
      };

    } catch (error) {
      console.error('Instagram campaign generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate main Instagram post (photo/carousel)
   */
  async generateMainPost(productData: any): Promise<InstagramContent> {
    const hashtags = await this.generateInstagramHashtags(productData.category);

    // Use AI to generate the caption instead of hardcoded template
    const aiCaption = await this.generateAICaption(productData);

    // Get product URL
    const productUrl = productData.product_url || productData.shop_link;

    // Ensure caption includes product URL
    let caption = aiCaption;
    if (productUrl && !caption.includes(productUrl)) {
      // Check if caption already has a call to action with a link
      if (!caption.includes('http') && !caption.includes('shop now') && !caption.includes('buy now')) {
        // Add product link at the end of the caption
        caption = caption.trim() + `\n\n🛒 Shop now: ${productUrl}`;
      }
    }

    return {
      post_type: 'carousel',
      caption: caption,
      hashtags,
      carousel_slides: [
        `${productData.name} - Hero shot with premium lighting`,
        `Lifestyle image - ${productData.name} in natural setting`,
        `Detail shot - Close-up of ${productData.name} quality`,
        `Collection shot - ${productData.name} with complementary products`,
        `Behind the scenes - Quality testing process`
      ],
      visual_suggestions: {
        composition: 'Rule of thirds with product as focal point',
        lighting: 'Natural lighting with soft shadows',
        colors: this.getInstagramColorPalette(productData.category),
        props: this.getInstagramProps(productData.category)
      },
      product_url: productUrl // Explicitly include product URL
    };
  }

  /**
   * Generate Instagram Stories series
   */
  async generateStorySeries(productData: any): Promise<InstagramContent[]> {
    const stories: InstagramContent[] = [];

    // Story 1: Product Introduction
    stories.push({
      post_type: 'story',
      caption: `New arrival alert! 🚨 ${productData.name}`,
      hashtags: ['#NewProduct', '#Quality', '#Premium'],
      story_elements: {
        stickers: ['🔥', '✨', '🌟'],
        polls: [`Love ${productData.category}?`],
        questions: ['What\'s your favorite feature?']
      },
      reel_hooks: [],
      visual_suggestions: {
        composition: 'Full screen product showcase',
        lighting: 'Bright, clean lighting',
        colors: ['#FF6B6B', '#4ECDC4', '#45B7D1'],
        props: ['Clean background', 'Minimal styling']
      }
    });

    // Story 2: Features Highlight
    stories.push({
      post_type: 'story',
      caption: `Why ${productData.name} is special 💎`,
      hashtags: ['#Quality', '#Features', '#Premium'],
      story_elements: {
        stickers: ['💎', '✅', '🏆'],
        polls: ['Which feature excites you most?'],
        questions: []
      },
      reel_hooks: [],
      visual_suggestions: {
        composition: 'Split screen with feature callouts',
        lighting: 'Professional product lighting',
        colors: ['#6C5CE7', '#A29BFE', '#FD79A8'],
        props: ['Feature highlight graphics', 'Clean typography']
      }
    });

    // Story 3: Social Proof
    stories.push({
      post_type: 'story',
      caption: `Our customers love ${productData.name}! ⭐`,
      hashtags: ['#CustomerLove', '#Reviews', '#Testimonials'],
      story_elements: {
        stickers: ['⭐', '❤️', '👏'],
        polls: [],
        questions: ['Share your experience!']
      },
      reel_hooks: [],
      visual_suggestions: {
        composition: 'Customer testimonial overlay',
        lighting: 'Warm, inviting lighting',
        colors: ['#00B894', '#00CEC9', '#81ECEC'],
        props: ['5-star graphics', 'Customer photos']
      }
    });

    return stories;
  }

  /**
   * Generate Instagram Reel content
   */
  async generateReelContent(productData: any): Promise<InstagramContent> {
    const hooks = this.generateReelHooks(productData);
    const hashtags = await this.generateReelHashtags(productData.category);

    return {
      post_type: 'reel',
      caption: `POV: You found the perfect ${productData.category} 😍 ${productData.name} hits different! ✨`,
      hashtags,
      reel_hooks: hooks,
      visual_suggestions: {
        composition: 'Quick cuts, dynamic movement',
        lighting: 'High contrast, dramatic lighting',
        colors: ['#FF7675', '#74B9FF', '#00B894'],
        props: ['Dynamic backgrounds', 'Quick transitions', 'Text overlays']
      }
    };
  }

  /**
   * Generate Instagram-optimized hashtags
   */
  async generateInstagramHashtags(category: string): Promise<string[]> {
    const categoryHashtags = {
      cbd: [
        '#CBD', '#CBDOil', '#Hemp', '#Wellness', '#NaturalHealing',
        '#CBDLife', '#Cannabidiol', '#HempOil', '#CBDProducts', '#PlantMedicine',
        '#CBDCommunity', '#HolisticHealth', '#NaturalWellness'
      ],
      seeds: [
        '#CannabisSeeds', '#GrowYourOwn', '#Seeds', '#Cannabis', '#Cultivation',
        '#Genetics', '#Strains', '#GrowLife', '#Homegrown', '#SeedBank',
        '#CannabisGrowing', '#IndoorGrowing', '#GrowTips'
      ],
      accessories: [
        '#SmokeShop', '#GlassArt', '#Vaporizer', '#SmokeAccessories', '#Headshop',
        '#GlassPipe', '#VapeLife', '#SmokeGear', '#420Accessories', '#CannabisAccessories',
        '#GlassCollection', '#FunctionalGlass', '#HeadyGlass'
      ]
    };

    const instagramSpecific = [
      '#InstaGood', '#PhotoOfTheDay', '#Quality', '#Premium', '#Lifestyle',
      '#UKCannabis', '#Glasgow', '#Scotland', '#BitsNBongs'
    ];

    const categoryTags = categoryHashtags[category.toLowerCase() as keyof typeof categoryHashtags] || categoryHashtags.cbd;

    return [...categoryTags.slice(0, 20), ...instagramSpecific].slice(0, 30);
  }

  /**
   * Generate Reel-specific hashtags for maximum reach
   */
  async generateReelHashtags(category: string): Promise<string[]> {
    const reelHashtags = [
      '#Reels', '#Viral', '#Trending', '#FYP', '#Explore',
      '#ReelsInstagram', '#ViralReels', '#TrendingReels'
    ];

    const categoryTags = await this.generateInstagramHashtags(category);

    return [...reelHashtags, ...categoryTags.slice(0, 15)].slice(0, 30);
  }

  /**
   * Generate AI-powered Instagram caption
   */
  private async generateAICaption(productData: any): Promise<string> {
    console.log('🔍 generateAICaption called with:', productData);

    try {
      // Use the social media AI to generate platform content
      console.log('🤖 Calling socialMediaAI.generatePlatformContent...');
      const platformContent = await socialMediaAI.generatePlatformContent(
        productData,
        'instagram',
        'product_showcase'
      );

      console.log('🔍 AI Platform Content Response:', platformContent);

      if (platformContent && platformContent.content && platformContent.content.caption) {
        console.log('✅ AI-generated Instagram caption received:', platformContent.content.caption);
        return platformContent.content.caption;
      }

      // Fallback to template if AI fails
      console.warn('⚠️ AI caption generation returned empty, using fallback template');
      return this.generateInstagramCaption(productData);

    } catch (error) {
      console.error('❌ AI caption generation failed, using fallback:', error);
      return this.generateInstagramCaption(productData);
    }
  }

  /**
   * Generate engaging Instagram caption (fallback)
   */
  private generateInstagramCaption(productData: any): string {
    console.log('🔍 Fallback template called with product data:', productData);
    console.log('🔍 Product name in template:', productData?.name);
    console.log('🔍 Product category in template:', productData?.category);

    const emojis = this.getCategoryEmojis(productData.category);

    const caption = `${emojis[0]} Discover the premium quality of ${productData.name}! ${emojis[1]}

Our carefully curated ${productData.category} collection brings you the finest products for the modern cannabis enthusiast.

${emojis[2]} Lab tested for purity
🔬 Premium quality guaranteed
🚚 Fast, discreet delivery
💯 Customer satisfaction promise

Experience the difference quality makes!

What's your favorite ${productData.category} feature? Let us know in the comments! 👇

#QualityFirst #PremiumCannabis #BitsNBongs`;

    console.log('🔍 Generated caption:', caption);
    return caption;
  }

  /**
   * Generate viral Reel hooks
   */
  private generateReelHooks(productData: any): string[] {
    return [
      `POV: You found the perfect ${productData.category}`,
      `This ${productData.name} hits different...`,
      `When you finally find quality ${productData.category}:`,
      `${productData.category} lovers, this one's for you!`,
      `Plot twist: ${productData.name} exceeded expectations`,
      `Tell me you love quality without telling me...`,
      `${productData.category} that actually delivers? Yes please!`
    ];
  }

  /**
   * Create comprehensive engagement strategy
   */
  private createEngagementStrategy(productData: any): any {
    return {
      optimal_times: this.instagramTrends.optimal_posting_times,
      hashtag_strategy: `Mix of trending (30%), niche (50%), and branded (20%) hashtags for maximum reach and engagement`,
      story_highlights: [
        `${productData.category} Collection`,
        'Customer Reviews',
        'Behind the Scenes',
        'Quality Promise',
        'New Arrivals'
      ]
    };
  }

  /**
   * Get category-specific emojis
   */
  private getCategoryEmojis(category: string): string[] {
    const emojiMap = {
      cbd: ['🌿', '✨', '💚'],
      seeds: ['🌱', '🌿', '🌟'],
      accessories: ['🔥', '✨', '💎'],
      vaporizers: ['💨', '⚡', '🔥'],
      bongs: ['💎', '🌊', '✨']
    };

    return emojiMap[category.toLowerCase() as keyof typeof emojiMap] || emojiMap.cbd;
  }

  /**
   * Get Instagram color palette for category
   */
  private getInstagramColorPalette(category: string): string[] {
    const colorPalettes = {
      cbd: ['#2ECC71', '#27AE60', '#F39C12', '#E67E22'],
      seeds: ['#27AE60', '#2ECC71', '#F1C40F', '#E67E22'],
      accessories: ['#9B59B6', '#8E44AD', '#E74C3C', '#C0392B'],
      vaporizers: ['#3498DB', '#2980B9', '#1ABC9C', '#16A085'],
      bongs: ['#34495E', '#2C3E50', '#95A5A6', '#7F8C8D']
    };

    return colorPalettes[category.toLowerCase() as keyof typeof colorPalettes] || colorPalettes.cbd;
  }

  /**
   * Get Instagram props for category
   */
  private getInstagramProps(category: string): string[] {
    const propSets = {
      cbd: ['Natural lighting', 'Plant elements', 'Wellness props', 'Clean backgrounds'],
      seeds: ['Growing equipment', 'Natural elements', 'Garden props', 'Earth tones'],
      accessories: ['Artistic lighting', 'Modern props', 'Geometric shapes', 'Bold colors'],
      vaporizers: ['Tech elements', 'Clean lines', 'Modern styling', 'Minimalist props'],
      bongs: ['Artistic elements', 'Water features', 'Glass props', 'Dramatic lighting']
    };

    return propSets[category.toLowerCase() as keyof typeof propSets] || propSets.cbd;
  }
}

// Export singleton instance
export const instagramAdapter = InstagramAdapter.getInstance();
