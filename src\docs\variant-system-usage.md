# Product Variant System Usage Guide

This guide explains how to use the new product variant system in the admin UI.

## Overview

The product variant system allows you to create multiple variants of a product with different options, such as size, color, and pack size. Each variant can have its own price, stock quantity, and SKU.

## Important Notes

1. **New System**: The new variant system completely replaces the legacy options system. The old options section has been removed.

2. **Save First**: You must save the product before you can add variants. This is because variants are linked to the product ID. You'll see a message reminding you to save the product first.

3. **Option Definitions**: After saving the product, define your options (e.g., Size, Color, Pack Size) first, then add variants based on those options.

## Step-by-Step Guide

### 1. Create or Edit a Product

1. Go to the Products page in the admin UI.
2. Click "Add Product" or edit an existing product.
3. Fill in the basic product information (name, description, price, etc.).
4. Click "Save" to create the product.

### 2. Define Options

After saving the product, you'll see the "Product Variants" section:

1. Click "Add Option" to add a new option (e.g., Size, Color, Pack Size).
2. Enter the option name (e.g., "Size").
3. Click "Add Value" to add option values (e.g., "Small", "Medium", "Large", "XL").
4. Repeat for each option you want to add.
5. Click "Save" to save the product with the option definitions.

### 3. Create Variants

There are two ways to create variants:

#### Individual Variants

1. Click "Add Variant" to open the Variant Form.
2. Select the option values for the variant.
3. Enter the price, stock quantity, and other details.
4. Click "Save" to create the variant.

#### Bulk Generation

1. Click "Bulk Generate" to open the Bulk Variant Generator.
2. Set the global settings for all variants (price, stock quantity, etc.).
3. Adjust individual variant settings if needed.
4. Click "Generate Variants" to create all the variants at once.

### 4. Manage Variants

1. Click "Manage Variants" to open the Variants Dialog.
2. View all variants for the product.
3. Edit or delete individual variants.
4. Add new variants.

## Troubleshooting

### Form Submits When Adding Options

If the form submits when you click "Add Option" or "Add Value", make sure you're using the latest version of the code. The buttons should have `type="button"` and `e.preventDefault()` to prevent form submission.

### Can't Add Variants to a New Product

If you can't add variants to a product, make sure you've saved the product first. You'll see a yellow message box reminding you to save the product before adding variants.

### Variants Not Showing Up

If your variants aren't showing up, check the following:

1. Make sure you've saved the product first.
2. Check that you've defined options and values.
3. Verify that the variants were created successfully.
4. Check the browser console for any errors.

## Best Practices

1. **Option Names**: Use clear, concise names for options.
2. **Option Values**: Keep option values simple and consistent.
3. **SKUs**: Use a consistent pattern for SKUs, such as `BASE-SIZE-COLOR-PACK`.
4. **Prices**: Set base prices for variants and adjust based on options.
5. **Images**: Add images for variants with different appearances.

## Need Help?

If you need further assistance, please contact the development team.
