# Cheech Chatbot - Technical Architecture

## System Architecture Overview

The Cheech chatbot system is designed with a modular architecture to ensure flexibility, maintainability, and scalability. The system is divided into several key components that work together to provide a seamless user experience.

```
┌─────────────────────────────────────────────────────────────────┐
│                       Client Application                        │
│                                                                 │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐   │
│  │  Chat Button  │    │ Chat Interface │    │ Message Display│   │
│  └───────────────┘    └───────────────┘    └───────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                          API Gateway                            │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Backend Services                          │
│                                                                 │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐   │
│  │  RAG Engine   │    │ Context Manager│    │ Web Navigation│   │
│  └───────────────┘    └───────────────┘    └───────────────┘   │
│                                                                 │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐   │
│  │ Knowledge Base│    │ Analytics     │    │ LLM Interface │   │
│  └───────────────┘    └───────────────┘    └───────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Data Storage                              │
│                                                                 │
│  ┌───────────────┐    ┌───────────────┐    ┌───────────────┐   │
│  │ Vector Store  │    │ Product DB    │    │ Conversation  │   │
│  │               │    │               │    │ History       │   │
│  └───────────────┘    └───────────────┘    └───────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Component Details

### 1. Client Application

#### Chat Button Component
- **Technology**: React + TypeScript
- **Functionality**: 
  - Floating button that follows scroll
  - Animated transitions when clicked
  - Accessibility-compliant implementation
- **Key Files**:
  - `ChatButton.tsx`
  - `ChatButton.css`

#### Chat Interface Component
- **Technology**: React + TypeScript
- **Functionality**:
  - Expandable chat window
  - Message history display
  - Input field with send button
  - Typing indicators
- **Key Files**:
  - `ChatInterface.tsx`
  - `ChatInterface.css`
  - `MessageBubble.tsx`
  - `TypingIndicator.tsx`

#### Message Display Component
- **Technology**: React + TypeScript
- **Functionality**:
  - Renders different message types (text, links, product cards)
  - Supports markdown formatting
  - Handles message grouping and timestamps
- **Key Files**:
  - `MessageDisplay.tsx`
  - `ProductCard.tsx`
  - `SuggestionChips.tsx`

### 2. Backend Services

#### RAG Engine
- **Technology**: LangChain + Vector Database (Pinecone)
- **Functionality**:
  - Converts queries to embeddings
  - Retrieves relevant documents from vector store
  - Ranks and filters results by relevance
  - Augments LLM prompts with retrieved information
- **Key Files**:
  - `RAGEngine.ts`
  - `DocumentRetriever.ts`
  - `EmbeddingService.ts`

#### Context Manager
- **Technology**: LangChain Memory + TypeScript
- **Functionality**:
  - Maintains conversation history
  - Tracks user preferences and context
  - Manages conversation state
  - Implements forgetting mechanisms for long conversations
- **Key Files**:
  - `ContextManager.ts`
  - `ConversationMemory.ts`
  - `UserPreferenceTracker.ts`

#### Web Navigation Assistant
- **Technology**: Puppeteer + TypeScript
- **Functionality**:
  - Navigates to relevant product pages
  - Captures screenshots for visual guidance
  - Extracts product information dynamically
  - Generates navigation instructions
- **Key Files**:
  - `WebNavigationAssistant.ts`
  - `PageInteractionService.ts`
  - `ScreenshotService.ts`

#### Knowledge Base
- **Technology**: TypeScript + JSON/YAML
- **Functionality**:
  - Structured product information
  - FAQs and common questions
  - Usage guides and best practices
  - Compliance information
- **Key Files**:
  - `KnowledgeBase.ts`
  - `ProductKnowledge.ts`
  - `FAQRepository.ts`

#### Analytics Service
- **Technology**: TypeScript + Database
- **Functionality**:
  - Tracks user interactions
  - Monitors question frequency
  - Measures response effectiveness
  - Identifies knowledge gaps
- **Key Files**:
  - `AnalyticsService.ts`
  - `InteractionTracker.ts`
  - `PerformanceMetrics.ts`

#### LLM Interface
- **Technology**: OpenAI API + TypeScript
- **Functionality**:
  - Manages communication with LLM
  - Handles prompt engineering
  - Implements retry logic and error handling
  - Optimizes token usage
- **Key Files**:
  - `LLMInterface.ts`
  - `PromptTemplates.ts`
  - `ResponseParser.ts`

### 3. Data Storage

#### Vector Store
- **Technology**: Pinecone / Qdrant
- **Functionality**:
  - Stores document embeddings
  - Enables semantic search
  - Supports metadata filtering
  - Handles incremental updates
- **Key Files**:
  - `VectorStoreService.ts`
  - `EmbeddingPipeline.ts`

#### Product Database
- **Technology**: JSON / Supabase
- **Functionality**:
  - Stores comprehensive product information
  - Maintains categories and relationships
  - Tracks inventory and availability
  - Supports filtering and search
- **Key Files**:
  - `ProductDatabase.ts`
  - `CategoryManager.ts`

#### Conversation History
- **Technology**: Supabase / LocalStorage
- **Functionality**:
  - Persists conversation history
  - Enables conversation resumption
  - Supports user identification
  - Implements privacy controls
- **Key Files**:
  - `ConversationStorage.ts`
  - `HistoryManager.ts`

## Data Flow

### Conversation Initiation
1. User clicks floating chat button
2. Chat interface expands
3. Welcome message displayed with suggested topics
4. User session initialized in backend

### Query Processing
1. User submits question
2. Query sent to backend API
3. Context manager adds conversation history
4. RAG engine retrieves relevant information
5. LLM generates response with retrieved context
6. Response sent back to client

### Product Recommendation
1. User asks for product recommendation
2. Query analyzed for product category and requirements
3. Product database queried for matching items
4. Top matches formatted as product cards
5. Response includes recommendation rationale
6. Suggestion chips offer refinement options

### Web Navigation Assistance
1. User requests help finding specific information
2. Web navigation assistant identifies relevant page
3. Puppeteer navigates to page and captures key elements
4. Step-by-step instructions generated
5. Optional: Screenshot with highlighted elements provided
6. Deep link to specific page section included in response

## API Endpoints

### `/api/chat`
- **Method**: POST
- **Purpose**: Submit user message and get response
- **Request Body**:
  ```json
  {
    "message": "Which product is best for cleaning my bong?",
    "sessionId": "user123-session456",
    "context": {
      "previousMessages": [...]
    }
  }
  ```
- **Response**:
  ```json
  {
    "message": "For cleaning your bong, I'd recommend our 'Crystal Clear' solution...",
    "suggestions": ["Show me more options", "How do I use it?"],
    "products": [
      {
        "id": "cc-500ml",
        "name": "Crystal Clear 500ml",
        "image": "url-to-image",
        "price": 12.99,
        "rating": 4.8
      }
    ]
  }
  ```

### `/api/navigation`
- **Method**: POST
- **Purpose**: Get navigation assistance to relevant page
- **Request Body**:
  ```json
  {
    "query": "Where can I find information about vaporizers?",
    "sessionId": "user123-session456"
  }
  ```
- **Response**:
  ```json
  {
    "instructions": "I can help you find our vaporizer collection...",
    "pageUrl": "/products/vaporizers",
    "screenshot": "base64-encoded-image",
    "highlightedElements": [...]
  }
  ```

### `/api/feedback`
- **Method**: POST
- **Purpose**: Submit user feedback on response quality
- **Request Body**:
  ```json
  {
    "messageId": "msg123",
    "helpful": true,
    "comments": "This was exactly what I needed!"
  }
  ```

## Security Considerations

### Data Protection
- All conversations encrypted in transit (HTTPS)
- Sensitive user information not stored
- Conversation history anonymized for analytics

### Compliance
- Age verification integration
- Content filtering for prohibited terms
- Compliance with cannabis advertising regulations

### Privacy
- Clear user opt-in for data collection
- Option to delete conversation history
- No persistent tracking across sessions without consent

## Performance Optimization

### Response Time
- Edge caching for common queries
- Optimized embedding generation
- Parallel processing of RAG and context management

### Resource Usage
- Efficient token usage for LLM calls
- Batched vector database operations
- Lazy loading of UI components

## Testing Strategy

### Unit Tests
- Component-level testing for all modules
- Mock services for external dependencies
- Coverage targets: >80% for core logic

### Integration Tests
- End-to-end conversation flows
- API contract validation
- Cross-browser compatibility

### Performance Tests
- Response time benchmarking
- Concurrent user simulation
- Memory usage profiling

## Deployment Architecture

### Development
- Local development environment with mock services
- Docker containers for consistent environments
- Hot reloading for rapid iteration

### Staging
- Full integration with test databases
- Automated deployment from CI/CD
- Synthetic traffic generation for testing

### Production
- Containerized deployment
- Auto-scaling based on traffic
- CDN for static assets
- Monitoring and alerting
