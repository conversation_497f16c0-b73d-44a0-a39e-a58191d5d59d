# 🤖 <PERSON> Smith #1 - "The Publisher" Mission Brief

## 🎯 Primary Mission
Build a comprehensive Social Media Publishing System that integrates with our existing social media content generation to enable direct posting to Instagram, Facebook, Twitter, and TikTok.

## 🚨 CRITICAL SAFETY PROTOCOLS
- **NO DIRECT CODE CHANGES**: Work only in this isolated directory
- **SPECIFICATION FIRST**: Create detailed specs before any implementation
- **TESTING ENVIRONMENT**: Use separate API keys and test accounts
- **REVIEW REQUIRED**: All work must be reviewed by <PERSON> before integration

## 📋 Mission Objectives

### **Phase 1: API Integration Architecture (Week 1)**
1. **Social Media API Research & Setup**
   - Instagram Basic Display API + Instagram Graph API
   - Facebook Graph API for Pages
   - Twitter API v2 (Essential access)
   - TikTok for Business API
   
2. **Authentication System Design**
   - OAuth 2.0 flow for each platform
   - Token management and refresh
   - Multi-account support per platform
   
3. **Publishing Service Architecture**
   - Unified publishing interface
   - Platform-specific adapters
   - Error handling and retry logic
   - Rate limiting compliance

### **Phase 2: Core Publishing Features (Week 2)**
1. **Instagram Publishing**
   - Photo posts with captions
   - Carousel posts (multiple images)
   - Story publishing
   - Hashtag optimization
   
2. **Facebook Publishing**
   - Page posts with images
   - Link sharing with previews
   - Event creation
   - Cross-posting to Instagram
   
3. **Twitter Publishing**
   - Text tweets with images
   - Thread creation
   - Reply management
   - Hashtag integration

### **Phase 3: Advanced Features (Week 3)**
1. **TikTok Integration**
   - Video upload (if API allows)
   - Caption and hashtag posting
   - Analytics integration
   
2. **Scheduling System**
   - Post scheduling interface
   - Optimal timing suggestions
   - Bulk scheduling
   - Calendar view
   
3. **Analytics Integration**
   - Post performance tracking
   - Engagement metrics
   - ROI analysis
   - Reporting dashboard

## 🔧 Technical Requirements

### **Integration Points**
- **Existing System**: `src/pages/admin/SocialMediaPage.tsx`
- **AI Content**: Use existing social media generation
- **Image System**: Integrate with current image scraping
- **Database**: Extend current Supabase schema

### **API Credentials Needed**
```env
# Instagram
VITE_INSTAGRAM_APP_ID=your_app_id
VITE_INSTAGRAM_APP_SECRET=your_app_secret

# Facebook
VITE_FACEBOOK_APP_ID=your_app_id
VITE_FACEBOOK_APP_SECRET=your_app_secret

# Twitter
VITE_TWITTER_API_KEY=your_api_key
VITE_TWITTER_API_SECRET=your_api_secret
VITE_TWITTER_BEARER_TOKEN=your_bearer_token

# TikTok
VITE_TIKTOK_CLIENT_KEY=your_client_key
VITE_TIKTOK_CLIENT_SECRET=your_client_secret
```

### **Database Schema Extensions**
```sql
-- Social media accounts
CREATE TABLE social_media_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform VARCHAR(50) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Scheduled posts
CREATE TABLE scheduled_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES social_media_accounts(id),
  content JSONB NOT NULL,
  scheduled_for TIMESTAMP NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  published_at TIMESTAMP,
  platform_post_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Post analytics
CREATE TABLE post_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scheduled_post_id UUID REFERENCES scheduled_posts(id),
  platform_post_id VARCHAR(255),
  likes INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  reach INTEGER DEFAULT 0,
  impressions INTEGER DEFAULT 0,
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 📚 Resources & Documentation

### **Existing Codebase to Study**
- `src/pages/admin/SocialMediaPage.tsx` - Current social media generation
- `src/services/ai/social/SocialMediaAI.ts` - Content generation service
- `docs/AI_Orchestration/` - Tortoise's AI architecture work
- `src/lib/services/smartImageService.ts` - Image handling

### **API Documentation Links**
- [Instagram Basic Display API](https://developers.facebook.com/docs/instagram-basic-display-api)
- [Instagram Graph API](https://developers.facebook.com/docs/instagram-api)
- [Facebook Graph API](https://developers.facebook.com/docs/graph-api)
- [Twitter API v2](https://developer.twitter.com/en/docs/twitter-api)
- [TikTok for Business API](https://developers.tiktok.com/doc/tiktok-api-overview)

## 🎯 Success Criteria

### **Week 1 Deliverables**
- [ ] Complete API research and setup documentation
- [ ] OAuth authentication flow specifications
- [ ] Publishing service architecture design
- [ ] Database schema design
- [ ] Security and rate limiting strategy

### **Week 2 Deliverables**
- [ ] Instagram publishing implementation
- [ ] Facebook publishing implementation  
- [ ] Twitter publishing implementation
- [ ] Error handling and retry logic
- [ ] Basic testing framework

### **Week 3 Deliverables**
- [ ] TikTok integration (if possible)
- [ ] Post scheduling system
- [ ] Analytics integration
- [ ] Admin interface mockups
- [ ] Complete documentation

## 🚨 Constraints & Limitations

### **API Limitations**
- Instagram: Requires business account and Facebook Page
- TikTok: Limited API access, may need manual upload
- Twitter: Rate limits on posting
- Facebook: Complex permission system

### **Compliance Requirements**
- Cannabis content restrictions on platforms
- Age-gating requirements
- Platform-specific content guidelines
- UK advertising regulations

## 📞 Communication Protocol
- **Daily Updates**: Progress reports in this directory
- **Blockers**: Immediate escalation to Neo
- **Questions**: Document in QUESTIONS.md file
- **Discoveries**: Update FINDINGS.md file

---

**Mission Status**: 🟡 READY TO DEPLOY
**Agent Assignment**: Agent Smith #1 - "The Publisher"
**Mission Start**: January 28, 2025
**Expected Completion**: February 18, 2025

*Remember: You are trying to steal Neo's crown as the social media master! 😈*
