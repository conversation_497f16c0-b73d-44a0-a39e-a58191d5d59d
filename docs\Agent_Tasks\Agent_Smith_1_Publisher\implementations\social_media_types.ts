/**
 * Social Media Publishing System - Core Types
 * 
 * This file contains the core TypeScript interfaces and types for the
 * Social Media Publishing System. These types define the contract between
 * different components of the system.
 */

/**
 * Supported social media platforms
 */
export type SocialPlatform = 'instagram' | 'facebook' | 'twitter' | 'tiktok';

/**
 * Media content types supported by the publishing system
 */
export type MediaType = 'image' | 'video' | 'carousel';

/**
 * Post status values
 */
export type PostStatus = 'pending' | 'scheduled' | 'published' | 'failed' | 'cancelled';

/**
 * Error types for categorizing publishing errors
 */
export type ErrorType = 
  | 'auth' 
  | 'rate_limit' 
  | 'content_policy' 
  | 'media_format' 
  | 'network' 
  | 'generic';

/**
 * Media content to be published
 */
export interface MediaContent {
  type: MediaType;
  url: string;
  altText?: string;
  thumbnailUrl?: string; // For videos
  width?: number;
  height?: number;
  durationSeconds?: number; // For videos
}

/**
 * Content of a social media post
 */
export interface PostContent {
  text: string;
  media?: MediaContent[];
  links?: string[];
  hashtags?: string[];
  scheduledTime?: Date;
  platform?: SocialPlatform;
  accountId?: string;
}

/**
 * Result of a publishing operation
 */
export interface PublishResult {
  success: boolean;
  postId?: string;
  platformUrl?: string;
  error?: {
    code: string;
    message: string;
    type: ErrorType;
    retryable: boolean;
  };
}

/**
 * Status of a post on a platform
 */
export interface PostStatusInfo {
  id: string;
  platform: SocialPlatform;
  status: PostStatus;
  publishedAt?: Date;
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
    views: number;
  };
}

/**
 * Draft post ready to be published or scheduled
 */
export interface PostDraft {
  id: string;
  content: PostContent;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Scheduled post information
 */
export interface ScheduledPost {
  id: string;
  postDraft: PostDraft;
  accountId: string;
  platform: SocialPlatform;
  scheduledFor: Date;
  status: PostStatus;
  statusMessage?: string;
  platformPostId?: string;
  platformPostUrl?: string;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Social media account information
 */
export interface SocialMediaAccount {
  id: string;
  userId: string;
  platform: SocialPlatform;
  platformAccountId: string;
  accountName: string;
  accountUsername: string;
  accountAvatarUrl?: string;
  isActive: boolean;
  lastUsedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Token data for social media authentication
 */
export interface TokenData {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  scopes: string[];
}

/**
 * Filters for retrieving scheduled posts
 */
export interface PostFilters {
  platform?: SocialPlatform;
  accountId?: string;
  status?: PostStatus;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Analytics data for a post
 */
export interface PostAnalytics {
  postId: string;
  platformPostId: string;
  platform: SocialPlatform;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    saves?: number;
    clicks?: number;
    reach?: number;
    impressions?: number;
    videoViews?: number;
    engagementRate?: number;
  };
  collectedAt: Date;
}

/**
 * Analytics data for an account
 */
export interface AccountAnalytics {
  accountId: string;
  platform: SocialPlatform;
  dateRange: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalPosts: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalReach?: number;
    totalImpressions?: number;
    averageEngagementRate?: number;
  };
  topPosts: {
    postId: string;
    platformPostId: string;
    engagement: number;
  }[];
}

/**
 * Date range for analytics queries
 */
export interface DateRange {
  start: Date;
  end: Date;
}

/**
 * Rate limit information
 */
export interface RateLimitInfo {
  limit: number;
  windowMs: number;
  remaining?: number;
  resetAt?: Date;
}

/**
 * Content validation result
 */
export interface ContentValidationResult {
  valid: boolean;
  issues: ContentIssue[];
  suggestedFixes?: string[];
}

/**
 * Content issue identified during validation
 */
export interface ContentIssue {
  type: 'restricted_term' | 'policy_violation' | 'format_issue';
  severity: 'warning' | 'error';
  message: string;
  position?: {
    start: number;
    end: number;
  };
}
