import { useState } from 'react';
import { searchImagesForTopic } from '../api/directImageUrl';
import { isGoogleImageSearchConfigured, setCustomSearchEngineId } from '../api/googleImageSearch';

/**
 * Component to test the Google Image Search functionality
 */
export default function GoogleImageTest() {
  const [topic, setTopic] = useState<string>('CBD oil benefits');
  const [cxId, setCxId] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [isConfigured, setIsConfigured] = useState<boolean>(isGoogleImageSearchConfigured());

  // Search for images based on the topic
  const handleSearch = async () => {
    if (!topic.trim()) {
      setError('Please enter a search topic');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const results = await searchImagesForTopic(topic, 5);
      setImages(results);
      
      if (results.length === 0) {
        setError('No images found for this topic');
      }
    } catch (err) {
      setError(`Error searching for images: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Set the Custom Search Engine ID
  const handleSetCxId = () => {
    if (!cxId.trim()) {
      setError('Please enter a Custom Search Engine ID');
      return;
    }

    try {
      setCustomSearchEngineId(cxId);
      setIsConfigured(isGoogleImageSearchConfigured());
      setError('');
    } catch (err) {
      setError(`Error setting CX ID: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Google Image Search Test</h1>
      
      {/* Configuration Status */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Google Image Search Configuration</h2>
        <div className="flex items-center gap-2 mb-4">
          <div 
            className={`w-3 h-3 rounded-full ${isConfigured ? 'bg-green-500' : 'bg-red-500'}`}
          />
          <span>
            {isConfigured 
              ? 'Google Image Search is configured' 
              : 'Google Image Search is not configured'}
          </span>
        </div>
        
        {!isConfigured && (
          <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
            <p className="font-medium">To configure Google Image Search:</p>
            <ol className="list-decimal ml-5 mt-1 space-y-1">
              <li>Visit <a href="https://programmablesearchengine.google.com/controlpanel/create" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Programmable Search Engine</a></li>
              <li>Create a new search engine</li>
              <li>Get your Search Engine ID (CX ID)</li>
              <li>Enter it below</li>
            </ol>
          </div>
        )}
        
        <div className="mt-4 flex gap-2">
          <input
            type="text"
            value={cxId}
            onChange={(e) => setCxId(e.target.value)}
            placeholder="Enter Custom Search Engine ID (CX ID)"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleSetCxId}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Set CX ID
          </button>
        </div>
      </div>
      
      {/* Search Form */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Search for Images</h2>
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="Enter a search topic"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleSearch}
            disabled={loading || !isConfigured}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
        
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-700 mb-4">
            {error}
          </div>
        )}
      </div>
      
      {/* Search Results */}
      {images.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Search Results</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((imageUrl, index) => (
              <div key={index} className="border rounded overflow-hidden">
                <img
                  src={imageUrl}
                  alt={`Result ${index + 1}`}
                  className="w-full h-48 object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 300 200">
                        <rect width="300" height="200" fill="#f8f8f8" />
                        <text x="50%" y="50%" font-family="Arial" font-size="16" text-anchor="middle" fill="#999">Image Failed to Load</text>
                      </svg>
                    `)}`;
                  }}
                />
                <div className="p-2">
                  <p className="text-xs text-gray-500 break-all">
                    {imageUrl.substring(0, 50)}{imageUrl.length > 50 ? '...' : ''}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
