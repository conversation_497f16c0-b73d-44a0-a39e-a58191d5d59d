// Import enriched seed data from Super Agent
// This script imports the CSV data returned by the Super Agent
// and updates both products and seed_product_attributes tables

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import csv from 'csv-parser';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface EnrichedSeedData {
  product_id: string;
  product_name: string;
  enriched_description?: string;
  enriched_price_gbp?: string;
  market_price_low?: string;
  market_price_high?: string;
  market_price_average?: string;
  price_sources?: string;
  enriched_image_url?: string;
  image_download_url?: string;
  enriched_seed_type?: string;
  enriched_flowering_time?: string;
  enriched_yield?: string;
  enriched_thc_level?: string;
  enriched_cbd_level?: string;
  enriched_effect?: string;
  enriched_seed_family?: string;
  enriched_lifecycle?: string;
  enriched_other?: string;
  agent_confidence?: string;
  agent_notes?: string;
}

async function importEnrichedSeeds(csvFilePath: string) {
  try {
    console.log('🤖 Starting import of enriched seed data from Super Agent...');

    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV file not found: ${csvFilePath}`);
    }

    // Read and parse CSV
    const enrichedData: EnrichedSeedData[] = [];

    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          enrichedData.push(row);
        })
        .on('end', () => {
          console.log(`📊 Parsed ${enrichedData.length} records from CSV`);
          resolve();
        })
        .on('error', reject);
    });

    let updatedProducts = 0;
    let updatedAttributes = 0;
    let createdAttributes = 0;
    let updatedPrices = 0;
    let updatedImages = 0;
    let errors = 0;

    // Process each record
    for (const record of enrichedData) {
      try {
        const productId = record.product_id;

        if (!productId) {
          console.warn('⚠️ Skipping record with missing product_id');
          continue;
        }

        // Prepare product updates
        const productUpdates: any = {};
        let hasProductUpdates = false;

        // Update description if provided
        if (record.enriched_description && record.enriched_description.trim()) {
          productUpdates.description = record.enriched_description.trim();
          hasProductUpdates = true;
        }

        // Update price if provided
        if (record.enriched_price_gbp && !isNaN(parseFloat(record.enriched_price_gbp))) {
          productUpdates.price = parseFloat(record.enriched_price_gbp);
          hasProductUpdates = true;
        }

        // Update image if provided
        if (record.enriched_image_url && record.enriched_image_url.trim()) {
          productUpdates.image = record.enriched_image_url.trim();
          hasProductUpdates = true;
        }

        // Apply product updates if any
        if (hasProductUpdates) {
          const { error: productError } = await supabase
            .from('products')
            .update(productUpdates)
            .eq('id', productId);

          if (productError) {
            console.error(`❌ Error updating product ${productId}:`, productError.message);
            errors++;
          } else {
            updatedProducts++;
            if (productUpdates.price) updatedPrices++;
            if (productUpdates.image) updatedImages++;
            console.log(`✅ Updated product data for: ${record.product_name}`);
          }
        }

        // Check if we have any attribute data to update
        const hasAttributeData = record.enriched_seed_type ||
          record.enriched_flowering_time ||
          record.enriched_yield ||
          record.enriched_thc_level ||
          record.enriched_cbd_level ||
          record.enriched_effect ||
          record.enriched_seed_family ||
          record.enriched_lifecycle ||
          record.enriched_other;

        if (hasAttributeData) {
          // Check if attributes record already exists
          const { data: existingAttributes } = await supabase
            .from('seed_product_attributes')
            .select('id')
            .eq('product_id', productId)
            .single();

          const attributeData = {
            product_id: productId,
            seed_type: record.enriched_seed_type?.trim() || null,
            flowering_time: record.enriched_flowering_time?.trim() || null,
            yield: record.enriched_yield?.trim() || null,
            thc_level: record.enriched_thc_level?.trim() || null,
            cbd_level: record.enriched_cbd_level?.trim() || null,
            effect: record.enriched_effect?.trim() || null,
            seed_family: record.enriched_seed_family?.trim() || null,
            is_manually_verified: true, // Mark as manually verified since it came from agent
            updated_at: new Date().toISOString()
          };

          if (existingAttributes) {
            // Update existing record
            const { error: attributeError } = await supabase
              .from('seed_product_attributes')
              .update(attributeData)
              .eq('product_id', productId);

            if (attributeError) {
              console.error(`❌ Error updating attributes for ${productId}:`, attributeError.message);
              errors++;
            } else {
              updatedAttributes++;
              console.log(`🔄 Updated attributes for: ${record.product_name}`);
            }
          } else {
            // Create new record
            const { error: attributeError } = await supabase
              .from('seed_product_attributes')
              .insert(attributeData);

            if (attributeError) {
              console.error(`❌ Error creating attributes for ${productId}:`, attributeError.message);
              errors++;
            } else {
              createdAttributes++;
              console.log(`➕ Created attributes for: ${record.product_name}`);
            }
          }
        }

      } catch (recordError) {
        console.error(`❌ Error processing record for ${record.product_name}:`, recordError);
        errors++;
      }
    }

    // Print summary
    console.log('\n✅ Import completed!');
    console.log('📊 Summary:');
    console.log(`   Total records processed: ${enrichedData.length}`);
    console.log(`   Products updated: ${updatedProducts}`);
    console.log(`   Prices updated: ${updatedPrices}`);
    console.log(`   Images updated: ${updatedImages}`);
    console.log(`   Attributes updated: ${updatedAttributes}`);
    console.log(`   Attributes created: ${createdAttributes}`);
    console.log(`   Errors: ${errors}`);

    if (errors > 0) {
      console.log('\n⚠️ Some errors occurred during import. Please check the logs above.');
    } else {
      console.log('\n🎉 All data imported successfully!');
    }

  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  }
}

// Get CSV file path from command line argument
const csvFilePath = process.argv[2];

if (!csvFilePath) {
  console.error('❌ Please provide the path to the enriched CSV file');
  console.log('Usage: npm run import-enriched-seeds path/to/enriched-seeds.csv');
  process.exit(1);
}

// Run the import
importEnrichedSeeds(csvFilePath);
