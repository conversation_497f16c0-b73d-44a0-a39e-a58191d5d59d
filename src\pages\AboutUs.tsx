import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, Users, Calendar, Award, Heart } from 'lucide-react';

const AboutUs = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Removed the Navbar component from here as it's already in ShopLayout */}
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative bg-sage-50 py-16 md:py-24">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="max-w-3xl mx-auto text-center"
            >
              <h1 className="text-4xl md:text-5xl font-bold text-clay-900 mb-6">About Us</h1>
              <p className="text-xl text-clay-700 leading-relaxed">
                Discover the story behind Bits n Bongs and our commitment to quality CBD and smoking accessories.
              </p>
            </motion.div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-background to-transparent"></div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                className="space-y-6"
              >
                <h2 className="text-3xl font-bold text-clay-900">Our Story</h2>
                <div className="prose text-clay-800 max-w-none">
                  <p className="text-lg leading-relaxed">
                    At Bits n Bongs, we are passionate about providing high-quality smoking products to our customers. Our online smoke shop offers a wide range of CBD products, bongs & pipes, seeds, papers, and machinery.
                  </p>
                  <p className="text-lg leading-relaxed">
                    Our products are carefully selected to ensure that our customers have access to the best products on the market. Whether you are a seasoned smoker or just starting, we have something for everyone.
                  </p>
                </div>
                <Button className="group" size="lg">
                  Browse Our Products
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.7, delay: 0.4 }}
                className="relative"
              >
                <div className="rounded-lg overflow-hidden aspect-square shadow-xl">
                  <img 
                    src="https://images.unsplash.com/photo-1605123728338-a5c4f24fb7bc" 
                    alt="Glass blowing craftsmanship" 
                    className="w-full h-full object-cover transition-all duration-500 hover:scale-105"
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 bg-primary text-white p-4 rounded-lg shadow-lg">
                  <p className="font-bold">Established</p>
                  <p className="text-2xl">2021</p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <motion.section 
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          variants={containerVariants}
          className="py-16 bg-clay-50"
        >
          <div className="container-custom">
            <div className="text-center mb-12">
              <motion.h2 variants={itemVariants} className="text-3xl font-bold text-clay-900 mb-4">Our Values</motion.h2>
              <motion.p variants={itemVariants} className="text-lg text-clay-700 max-w-2xl mx-auto">
                What drives us every day to provide the best products and experience for our customers
              </motion.p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { 
                  icon: <Users className="h-8 w-8" />, 
                  title: "Customer First", 
                  desc: "We prioritise our customers' needs and satisfaction above all else." 
                },
                { 
                  icon: <Award className="h-8 w-8" />, 
                  title: "Quality", 
                  desc: "We never compromise on the quality of our products and services." 
                },
                { 
                  icon: <Heart className="h-8 w-8" />, 
                  title: "Passion", 
                  desc: "We're passionate about cannabis culture and education." 
                },
                { 
                  icon: <Calendar className="h-8 w-8" />, 
                  title: "Consistency", 
                  desc: "We consistently deliver excellence in everything we do." 
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="bg-primary/10 p-4 rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4 mx-auto">
                    {item.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-clay-900 text-center">{item.title}</h3>
                  <p className="text-clay-700 text-center">{item.desc}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>

        {/* Team Section */}
        <section className="py-16">
          <div className="container-custom">
            <div className="text-center mb-12">
              <motion.h2 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-clay-900 mb-4"
              >
                Meet Our Team
              </motion.h2>
              <motion.p 
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-lg text-clay-700 max-w-2xl mx-auto"
              >
                The passionate individuals behind Bits n Bongs
              </motion.p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { 
                  name: "Emma Williams", 
                  role: "Founder & CEO", 
                  image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=500" 
                },
                { 
                  name: "James Thompson", 
                  role: "Head of Product", 
                  image: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=500" 
                },
                { 
                  name: "Sarah Chan", 
                  role: "CBD Specialist", 
                  image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=500" 
                }
              ].map((person, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300">
                    <div className="h-64 overflow-hidden">
                      <img 
                        src={person.image} 
                        alt={person.name} 
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-clay-900">{person.name}</h3>
                      <p className="text-primary">{person.role}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <motion.section 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className="py-16 bg-primary/5"
        >
          <div className="container-custom">
            <div className="bg-gradient-to-r from-sage-500 to-primary rounded-xl p-8 md:p-12 shadow-lg">
              <div className="max-w-3xl mx-auto text-center text-white">
                <h2 className="text-3xl font-bold mb-6">Ready to Experience Bits n Bongs?</h2>
                <p className="text-xl mb-8 opacity-90">
                  Join our community of satisfied customers and explore our premium range of products.
                </p>
                <Button size="lg" variant="outline" className="bg-white hover:bg-white/90 text-primary border-white">
                  Shop Now
                </Button>
              </div>
            </div>
          </div>
        </motion.section>
      </main>
      
      {/* Removed Footer component from here as it's already in ShopLayout */}
    </div>
  );
};

export default AboutUs;
