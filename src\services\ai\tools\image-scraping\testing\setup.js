/**
 * Setup script for the isolated testing environment
 * This script initializes the testing environment without affecting the main project
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Current directory
const currentDir = __dirname;

console.log('📦 Setting up isolated testing environment for image scraping system...');

try {
  // Create mock directories if they don't exist
  const mockDirs = [
    path.join(currentDir, '__mocks__'),
    path.join(currentDir, '__mocks__/mcp-integration')
  ];

  mockDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`  ✓ Created directory: ${path.relative(currentDir, dir)}`);
    }
  });

  // Install dependencies
  console.log('\n📥 Installing dependencies...');
  execSync('npm install', { cwd: currentDir, stdio: 'inherit' });

  // Get test files
  const testDir = path.join(currentDir, '..', '__tests__');
  const testFiles = fs.readdirSync(testDir).filter(file => file.endsWith('.test.ts'));
  
  console.log('\n🔗 Setting up test files...');
  testFiles.forEach(file => {
    console.log(`  ✓ ${file}`);
  });

  // Create a simple test file that doesn't depend on external modules
  console.log('\n🧪 Creating a simple test file to verify setup...');
  const simpleTestPath = path.join(currentDir, 'simple.test.js');
  fs.writeFileSync(simpleTestPath, `
    describe('Simple test', () => {
      it('should pass', () => {
        expect(1 + 1).toBe(2);
      });
    });
  `);
  
  // Run the simple test to verify Jest is working
  console.log('\n🧪 Running a simple test to verify setup...');
  try {
    execSync('npx jest simple.test.js', {
      cwd: currentDir,
      stdio: 'inherit'
    });
    console.log('  ✓ Test ran successfully');
    
    // Clean up the simple test file
    fs.unlinkSync(simpleTestPath);
  } catch (testError) {
    console.log('  ✗ Test failed, but setup may still be complete');
    console.log('    Check the error message above for details');
  }

  console.log('\n✅ Setup complete! You can now run tests with:');
  console.log('  cd src/services/ai/tools/image-scraping/testing');
  console.log('  npm test');
} catch (error) {
  console.error('\n❌ Setup failed:', error.message);
  process.exit(1);
}
