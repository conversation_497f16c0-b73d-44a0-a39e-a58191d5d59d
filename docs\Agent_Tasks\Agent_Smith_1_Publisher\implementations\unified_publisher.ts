/**
 * Unified Publisher Service
 * 
 * This file implements the unified social media publishing service that orchestrates
 * all platform-specific publishers and provides a single interface for the application.
 */

import {
  SocialPlatform,
  PostContent,
  PublishResult,
  SocialMediaAccount,
  PostAnalytics,
  PostFilters,
  ScheduledPost,
  PostDraft,
  DateRange
} from './social_media_types';

import {
  SocialMediaPublisher,
  SocialMediaPublishingService,
  PublisherFactory,
  TokenManager,
  ErrorHandler,
  RateLimiter
} from './social_media_publisher';

import { InstagramPublisher } from './instagram_publisher';
import { FacebookPublisher } from './facebook_publisher';
import { TwitterPublisher } from './twitter_publisher';
import { TikTokPublisher } from './tiktok_publisher';

/**
 * Database service interface for storing and retrieving data
 */
interface DatabaseService {
  getAccount(accountId: string): Promise<SocialMediaAccount>;
  getAccounts(userId: string, platform?: SocialPlatform): Promise<SocialMediaAccount[]>;
  createAccount(account: Partial<SocialMediaAccount>): Promise<SocialMediaAccount>;
  updateAccount(accountId: string, updates: Partial<SocialMediaAccount>): Promise<SocialMediaAccount>;
  deleteAccount(accountId: string): Promise<boolean>;
  
  createPostDraft(draft: Partial<PostDraft>): Promise<PostDraft>;
  getPostDraft(draftId: string): Promise<PostDraft>;
  updatePostDraft(draftId: string, updates: Partial<PostContent>): Promise<PostDraft>;
  deletePostDraft(draftId: string): Promise<boolean>;
  
  createScheduledPost(post: Partial<ScheduledPost>): Promise<ScheduledPost>;
  getScheduledPost(postId: string): Promise<ScheduledPost>;
  getScheduledPosts(filters: PostFilters): Promise<ScheduledPost[]>;
  updateScheduledPost(postId: string, updates: Partial<ScheduledPost>): Promise<ScheduledPost>;
  deleteScheduledPost(postId: string): Promise<boolean>;
  
  savePostAnalytics(analytics: PostAnalytics): Promise<void>;
  getPostAnalytics(postId: string): Promise<PostAnalytics[]>;
}

/**
 * Authentication service interface for handling OAuth flows
 */
interface AuthenticationService {
  getAuthorizationUrl(platform: SocialPlatform, redirectUrl: string): string;
  exchangeCodeForToken(platform: SocialPlatform, code: string, redirectUrl: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
    platformAccountId: string;
    platformUsername: string;
    platformName: string;
  }>;
  revokeToken(platform: SocialPlatform, accountId: string): Promise<boolean>;
}

/**
 * Implementation of the publisher factory
 */
class SocialMediaPublisherFactory implements PublisherFactory {
  private tokenManager: TokenManager;
  private errorHandler: ErrorHandler;
  private rateLimiter: RateLimiter;
  private config: Record<string, any>;
  
  constructor(
    tokenManager: TokenManager,
    errorHandler: ErrorHandler,
    rateLimiter: RateLimiter,
    config: Record<string, any>
  ) {
    this.tokenManager = tokenManager;
    this.errorHandler = errorHandler;
    this.rateLimiter = rateLimiter;
    this.config = config;
  }
  
  createPublisher(platform: string, accountId: string): SocialMediaPublisher {
    switch (platform) {
      case 'instagram':
        return new InstagramPublisher(
          accountId,
          this.tokenManager,
          this.errorHandler,
          this.rateLimiter,
          this.config.instagram
        );
      case 'facebook':
        return new FacebookPublisher(
          accountId,
          this.tokenManager,
          this.errorHandler,
          this.rateLimiter,
          this.config.facebook
        );
      case 'twitter':
        return new TwitterPublisher(
          accountId,
          this.tokenManager,
          this.errorHandler,
          this.rateLimiter,
          this.config.twitter
        );
      case 'tiktok':
        return new TikTokPublisher(
          accountId,
          this.tokenManager,
          this.errorHandler,
          this.rateLimiter,
          this.config.tiktok
        );
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }
}

/**
 * Implementation of the unified social media publishing service
 */
export class UnifiedSocialMediaPublishingService implements SocialMediaPublishingService {
  private publisherFactory: PublisherFactory;
  private databaseService: DatabaseService;
  private authService: AuthenticationService;
  private errorHandler: ErrorHandler;
  
  constructor(
    publisherFactory: PublisherFactory,
    databaseService: DatabaseService,
    authService: AuthenticationService,
    errorHandler: ErrorHandler
  ) {
    this.publisherFactory = publisherFactory;
    this.databaseService = databaseService;
    this.authService = authService;
    this.errorHandler = errorHandler;
  }
  
  /**
   * Get a publisher for a specific account
   * @param accountId The account ID
   * @returns Promise resolving to the publisher
   */
  private async getPublisherForAccount(accountId: string): Promise<SocialMediaPublisher> {
    try {
      const account = await this.databaseService.getAccount(accountId);
      return this.publisherFactory.createPublisher(account.platform, accountId);
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'getPublisherForAccount');
    }
  }
  
  /**
   * Connect a social media account
   * @param platform The platform to connect
   * @param redirectUrl The OAuth redirect URL
   * @returns Promise resolving to authorization URL
   */
  async connectAccount(platform: string, redirectUrl: string): Promise<string> {
    try {
      return this.authService.getAuthorizationUrl(platform as SocialPlatform, redirectUrl);
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'connectAccount');
    }
  }
  
  /**
   * Complete OAuth connection with authorization code
   * @param platform The platform being connected
   * @param code The authorization code
   * @param redirectUrl The OAuth redirect URL
   * @param userId The user ID
   * @returns Promise resolving to the connected account
   */
  async completeAccountConnection(
    platform: string,
    code: string,
    redirectUrl: string,
    userId: string
  ): Promise<SocialMediaAccount> {
    try {
      const tokenData = await this.authService.exchangeCodeForToken(
        platform as SocialPlatform,
        code,
        redirectUrl
      );
      
      // Create account in database
      const account = await this.databaseService.createAccount({
        userId,
        platform: platform as SocialPlatform,
        platformAccountId: tokenData.platformAccountId,
        accountName: tokenData.platformName,
        accountUsername: tokenData.platformUsername,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Store token data (in a real implementation, this would be handled by the token manager)
      
      return account;
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'completeAccountConnection');
    }
  }
  
  /**
   * Get connected accounts for a user
   * @param userId The user ID
   * @param platform Optional platform filter
   * @returns Promise resolving to array of connected accounts
   */
  async getConnectedAccounts(userId: string, platform?: string): Promise<SocialMediaAccount[]> {
    try {
      return this.databaseService.getAccounts(userId, platform as SocialPlatform);
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'getConnectedAccounts');
    }
  }
  
  /**
   * Disconnect a social media account
   * @param accountId The account ID to disconnect
   * @returns Promise resolving to boolean indicating success
   */
  async disconnectAccount(accountId: string): Promise<boolean> {
    try {
      const account = await this.databaseService.getAccount(accountId);
      
      // Revoke token with the platform
      await this.authService.revokeToken(account.platform, accountId);
      
      // Delete account from database
      return this.databaseService.deleteAccount(accountId);
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'disconnectAccount');
    }
  }
  
  /**
   * Create a post draft
   * @param content The post content
   * @param userId The user ID
   * @returns Promise resolving to the created draft
   */
  async createPostDraft(content: PostContent, userId: string): Promise<PostDraft> {
    try {
      return this.databaseService.createPostDraft({
        content,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'createPostDraft');
    }
  }
  
  /**
   * Schedule a post for later publishing
   * @param content The post content
   * @param accountId The account ID to publish to
   * @param scheduledTime The time to publish
   * @returns Promise resolving to the publish result
   */
  async schedulePost(
    content: PostContent,
    accountId: string,
    scheduledTime: Date
  ): Promise<PublishResult> {
    try {
      const account = await this.databaseService.getAccount(accountId);
      
      // Create draft
      const draft = await this.createPostDraft(content, account.userId);
      
      // Create scheduled post
      const scheduledPost = await this.databaseService.createScheduledPost({
        postDraft: draft,
        accountId,
        platform: account.platform,
        scheduledFor: scheduledTime,
        status: 'scheduled',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Return a successful publish result
      return {
        success: true,
        postId: scheduledPost.id,
        platformUrl: undefined // No platform URL yet as it's only scheduled
      };
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'schedulePost');
    }
  }
  
  /**
   * Publish a post immediately
   * @param content The post content
   * @param accountId The account ID to publish to
   * @returns Promise resolving to the publish result
   */
  async publishNow(content: PostContent, accountId: string): Promise<PublishResult> {
    try {
      const publisher = await this.getPublisherForAccount(accountId);
      
      // Set account ID in content
      const contentWithAccount = {
        ...content,
        accountId
      };
      
      // Publish the post
      const result = await publisher.publishPost(contentWithAccount);
      
      if (result.success) {
        // Create a record of the published post
        const account = await this.databaseService.getAccount(accountId);
        const draft = await this.createPostDraft(content, account.userId);
        
        await this.databaseService.createScheduledPost({
          postDraft: draft,
          accountId,
          platform: account.platform,
          scheduledFor: new Date(),
          status: 'published',
          platformPostId: result.postId,
          platformPostUrl: result.platformUrl,
          publishedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          type: 'generic',
          retryable: true
        }
      };
    }
  }
  
  /**
   * Get scheduled posts
   * @param userId The user ID
   * @param filters Optional filters
   * @returns Promise resolving to array of scheduled posts
   */
  async getScheduledPosts(userId: string, filters?: PostFilters): Promise<ScheduledPost[]> {
    try {
      // Get accounts for user
      const accounts = await this.databaseService.getAccounts(userId);
      const accountIds = accounts.map(account => account.id);
      
      // Apply account filter
      const fullFilters: PostFilters = {
        ...filters,
        accountId: filters?.accountId || (accountIds.length === 1 ? accountIds[0] : undefined)
      };
      
      return this.databaseService.getScheduledPosts(fullFilters);
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'getScheduledPosts');
    }
  }
  
  /**
   * Cancel a scheduled post
   * @param postId The post ID to cancel
   * @returns Promise resolving to boolean indicating success
   */
  async cancelScheduledPost(postId: string): Promise<boolean> {
    try {
      const post = await this.databaseService.getScheduledPost(postId);
      
      // Can only cancel pending or scheduled posts
      if (post.status !== 'pending' && post.status !== 'scheduled') {
        throw new Error(`Cannot cancel post with status: ${post.status}`);
      }
      
      // Update post status
      await this.databaseService.updateScheduledPost(postId, {
        status: 'cancelled',
        updatedAt: new Date()
      });
      
      return true;
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'cancelScheduledPost');
    }
  }
  
  /**
   * Process scheduled posts that are due
   * @returns Promise resolving to array of processing results
   */
  async processScheduledPosts(): Promise<Array<{ postId: string; result: PublishResult }>> {
    try {
      // Get posts that are scheduled and due
      const now = new Date();
      const posts = await this.databaseService.getScheduledPosts({
        status: 'scheduled',
        dateRange: {
          start: new Date(0), // Beginning of time
          end: now // Now
        }
      });
      
      const results: Array<{ postId: string; result: PublishResult }> = [];
      
      // Process each post
      for (const post of posts) {
        try {
          const publisher = await this.getPublisherForAccount(post.accountId);
          
          // Publish the post
          const result = await publisher.publishPost(post.postDraft.content);
          
          // Update post status
          if (result.success) {
            await this.databaseService.updateScheduledPost(post.id, {
              status: 'published',
              platformPostId: result.postId,
              platformPostUrl: result.platformUrl,
              publishedAt: new Date(),
              updatedAt: new Date()
            });
          } else {
            await this.databaseService.updateScheduledPost(post.id, {
              status: 'failed',
              statusMessage: result.error?.message,
              updatedAt: new Date()
            });
          }
          
          results.push({ postId: post.id, result });
        } catch (error) {
          // Log error but continue processing other posts
          this.errorHandler.logError(error, { operation: 'processScheduledPost', postId: post.id });
          
          // Update post status
          await this.databaseService.updateScheduledPost(post.id, {
            status: 'failed',
            statusMessage: error instanceof Error ? error.message : 'Unknown error occurred',
            updatedAt: new Date()
          });
          
          results.push({
            postId: post.id,
            result: {
              success: false,
              error: {
                code: 'PROCESSING_ERROR',
                message: error instanceof Error ? error.message : 'Unknown error occurred',
                type: 'generic',
                retryable: true
              }
            }
          });
        }
      }
      
      return results;
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'processScheduledPosts');
    }
  }
  
  /**
   * Get analytics for a post
   * @param postId The post ID
   * @returns Promise resolving to post analytics
   */
  async getPostAnalytics(postId: string): Promise<PostAnalytics> {
    try {
      const post = await this.databaseService.getScheduledPost(postId);
      
      // Post must be published and have a platform post ID
      if (post.status !== 'published' || !post.platformPostId) {
        throw new Error('Post is not published or has no platform post ID');
      }
      
      const publisher = await this.getPublisherForAccount(post.accountId);
      
      // Get analytics from platform
      const analytics = await publisher.getPostAnalytics(post.platformPostId);
      
      // Save analytics to database
      await this.databaseService.savePostAnalytics(analytics);
      
      return analytics;
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'getPostAnalytics');
    }
  }
  
  /**
   * Get analytics for an account
   * @param accountId The account ID
   * @param dateRange The date range for analytics
   * @returns Promise resolving to account analytics
   */
  async getAccountAnalytics(accountId: string, dateRange: DateRange): Promise<any> {
    try {
      // Get posts for account in date range
      const posts = await this.databaseService.getScheduledPosts({
        accountId,
        status: 'published',
        dateRange
      });
      
      // Get analytics for each post
      const analyticsPromises = posts.map(post => {
        if (post.platformPostId) {
          return this.getPostAnalytics(post.id);
        }
        return null;
      });
      
      const analyticsResults = await Promise.all(analyticsPromises);
      const validAnalytics = analyticsResults.filter(Boolean) as PostAnalytics[];
      
      // Aggregate analytics
      const totalLikes = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.likes || 0), 0);
      const totalComments = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.comments || 0), 0);
      const totalShares = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.shares || 0), 0);
      const totalReach = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.reach || 0), 0);
      const totalImpressions = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.impressions || 0), 0);
      
      // Calculate average engagement rate
      let averageEngagementRate = 0;
      if (validAnalytics.length > 0) {
        const totalEngagementRate = validAnalytics.reduce((sum, analytics) => sum + (analytics.metrics.engagementRate || 0), 0);
        averageEngagementRate = totalEngagementRate / validAnalytics.length;
      }
      
      // Find top posts by engagement
      const topPosts = validAnalytics
        .map(analytics => ({
          postId: analytics.postId,
          platformPostId: analytics.platformPostId,
          engagement: (analytics.metrics.likes || 0) + (analytics.metrics.comments || 0) + (analytics.metrics.shares || 0)
        }))
        .sort((a, b) => b.engagement - a.engagement)
        .slice(0, 5);
      
      // Get account
      const account = await this.databaseService.getAccount(accountId);
      
      return {
        accountId,
        platform: account.platform,
        dateRange,
        metrics: {
          totalPosts: posts.length,
          totalLikes,
          totalComments,
          totalShares,
          totalReach,
          totalImpressions,
          averageEngagementRate
        },
        topPosts
      };
    } catch (error) {
      return this.errorHandler.handle(error, 'unified', 'getAccountAnalytics');
    }
  }
}
