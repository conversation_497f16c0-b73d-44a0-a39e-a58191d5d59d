-- Migration: Implement Row Level Security Policies for Multi-Tenant Architecture
-- Description: Creates RLS policies for tenant isolation across all tables
-- Author: AI Assistant
-- Date: 2025-01-27
-- Dependencies: 20250127000002_add_tenant_columns.sql

-- Products table RLS policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON products;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON products;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON products;

CREATE POLICY "Tenant isolation for products" ON products
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Categories table RLS policies
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for categories" ON categories
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Brands table RLS policies
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for brands" ON brands
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Orders table RLS policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for orders" ON orders
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Order items table RLS policies
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for order_items" ON order_items
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Blogs table RLS policies
ALTER TABLE blogs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for blogs" ON blogs
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Blog categories table RLS policies
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for blog_categories" ON blog_categories
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Blog comments table RLS policies (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_comments') THEN
    ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;
    EXECUTE 'CREATE POLICY "Tenant isolation for blog_comments" ON blog_comments FOR ALL USING (tenant_id = get_current_tenant_id())';
  END IF;
END $$;

-- Blog images table RLS policies (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'blog_images') THEN
    ALTER TABLE blog_images ENABLE ROW LEVEL SECURITY;
    EXECUTE 'CREATE POLICY "Tenant isolation for blog_images" ON blog_images FOR ALL USING (tenant_id = get_current_tenant_id())';
  END IF;
END $$;

-- Discount codes table RLS policies
ALTER TABLE discount_codes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for discount_codes" ON discount_codes
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Settings table RLS policies
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for settings" ON settings
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- FAQs table RLS policies
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for faqs" ON faqs
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Related products table RLS policies
ALTER TABLE related_products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for related_products" ON related_products
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Product variants table RLS policies (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_variants') THEN
    ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
    EXECUTE 'CREATE POLICY "Tenant isolation for product_variants" ON product_variants FOR ALL USING (tenant_id = get_current_tenant_id())';
  END IF;
END $$;

-- User-specific tables with combined user and tenant isolation

-- Addresses table RLS policies
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for addresses" ON addresses
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Cart items table RLS policies
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for cart_items" ON cart_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Wishlists table RLS policies
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for wishlists" ON wishlists
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Wishlist items table RLS policies
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for wishlist_items" ON wishlist_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    EXISTS (
      SELECT 1 FROM wishlists w 
      WHERE w.id = wishlist_items.wishlist_id 
      AND w.user_id = auth.uid()
      AND w.tenant_id = get_current_tenant_id()
    )
  );

-- Saved items table RLS policies
ALTER TABLE saved_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "User and tenant isolation for saved_items" ON saved_items
  FOR ALL USING (
    tenant_id = get_current_tenant_id() AND 
    user_id = auth.uid()
  );

-- Newsletter subscribers table RLS policies
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Tenant isolation for newsletter_subscribers" ON newsletter_subscribers
  FOR ALL USING (tenant_id = get_current_tenant_id());

-- Profiles table RLS policies (update existing)
DROP POLICY IF EXISTS "Users can view own profile." ON profiles;
DROP POLICY IF EXISTS "Users can update own profile." ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (id = auth.uid());

-- Create function to validate tenant access for operations
CREATE OR REPLACE FUNCTION validate_tenant_access()
RETURNS TRIGGER AS $$
DECLARE
  current_tenant UUID;
  user_uuid UUID;
BEGIN
  -- Get current user and tenant
  user_uuid := auth.uid();
  current_tenant := get_current_tenant_id();
  
  -- Skip validation for system operations
  IF user_uuid IS NULL THEN
    RETURN COALESCE(NEW, OLD);
  END IF;
  
  -- Validate tenant access for INSERT/UPDATE operations
  IF TG_OP IN ('INSERT', 'UPDATE') THEN
    -- Ensure tenant_id matches current tenant context
    IF NEW.tenant_id IS NOT NULL AND NEW.tenant_id != current_tenant THEN
      RAISE EXCEPTION 'Access denied: Invalid tenant context';
    END IF;
    
    -- Set tenant_id if not provided
    IF NEW.tenant_id IS NULL AND current_tenant IS NOT NULL THEN
      NEW.tenant_id := current_tenant;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add tenant validation triggers to core tables
CREATE TRIGGER validate_products_tenant
  BEFORE INSERT OR UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_categories_tenant
  BEFORE INSERT OR UPDATE ON categories
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_brands_tenant
  BEFORE INSERT OR UPDATE ON brands
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_orders_tenant
  BEFORE INSERT OR UPDATE ON orders
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_order_items_tenant
  BEFORE INSERT OR UPDATE ON order_items
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_blogs_tenant
  BEFORE INSERT OR UPDATE ON blogs
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_blog_categories_tenant
  BEFORE INSERT OR UPDATE ON blog_categories
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_discount_codes_tenant
  BEFORE INSERT OR UPDATE ON discount_codes
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_settings_tenant
  BEFORE INSERT OR UPDATE ON settings
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_faqs_tenant
  BEFORE INSERT OR UPDATE ON faqs
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_related_products_tenant
  BEFORE INSERT OR UPDATE ON related_products
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

-- Add tenant validation triggers to user-specific tables
CREATE TRIGGER validate_addresses_tenant
  BEFORE INSERT OR UPDATE ON addresses
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_cart_items_tenant
  BEFORE INSERT OR UPDATE ON cart_items
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_wishlists_tenant
  BEFORE INSERT OR UPDATE ON wishlists
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_wishlist_items_tenant
  BEFORE INSERT OR UPDATE ON wishlist_items
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_saved_items_tenant
  BEFORE INSERT OR UPDATE ON saved_items
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

CREATE TRIGGER validate_newsletter_subscribers_tenant
  BEFORE INSERT OR UPDATE ON newsletter_subscribers
  FOR EACH ROW EXECUTE FUNCTION validate_tenant_access();

-- Create function to bypass RLS for system operations
CREATE OR REPLACE FUNCTION bypass_rls_for_system()
RETURNS VOID AS $$
BEGIN
  -- This function can be used by system processes to bypass RLS
  -- Should be used with extreme caution and proper authentication
  PERFORM set_config('row_security', 'off', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to re-enable RLS
CREATE OR REPLACE FUNCTION enable_rls_for_system()
RETURNS VOID AS $$
BEGIN
  PERFORM set_config('row_security', 'on', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON FUNCTION validate_tenant_access() IS 'Validates tenant access and sets tenant_id for INSERT/UPDATE operations';
COMMENT ON FUNCTION bypass_rls_for_system() IS 'Bypasses RLS for system operations - use with caution';
COMMENT ON FUNCTION enable_rls_for_system() IS 'Re-enables RLS after system operations';

-- Create view for tenant statistics (accessible by tenant owners/admins)
CREATE OR REPLACE VIEW tenant_statistics AS
SELECT 
  t.id as tenant_id,
  t.name as tenant_name,
  t.slug as tenant_slug,
  COUNT(DISTINCT p.id) as product_count,
  COUNT(DISTINCT c.id) as category_count,
  COUNT(DISTINCT b.id) as brand_count,
  COUNT(DISTINCT o.id) as order_count,
  COUNT(DISTINCT bl.id) as blog_count,
  COUNT(DISTINCT ns.id) as newsletter_subscriber_count,
  t.created_at,
  t.plan_type,
  t.status
FROM tenants t
LEFT JOIN products p ON t.id = p.tenant_id
LEFT JOIN categories c ON t.id = c.tenant_id
LEFT JOIN brands b ON t.id = b.tenant_id
LEFT JOIN orders o ON t.id = o.tenant_id
LEFT JOIN blogs bl ON t.id = bl.tenant_id
LEFT JOIN newsletter_subscribers ns ON t.id = ns.tenant_id
WHERE t.id IN (
  SELECT tenant_id FROM tenant_users 
  WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
)
GROUP BY t.id, t.name, t.slug, t.created_at, t.plan_type, t.status;

COMMENT ON VIEW tenant_statistics IS 'Provides statistics for tenants accessible by tenant owners/admins';

RAISE NOTICE 'Multi-tenant RLS policies have been successfully implemented';
RAISE NOTICE 'All tables now have tenant isolation enabled';
RAISE NOTICE 'Tenant validation triggers have been added to prevent data leakage';