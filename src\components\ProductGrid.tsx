
import ProductCard from '@/components/ProductCard';
import { Product as DbProduct } from '@/types/database';
import { Product } from '@/components/ProductCard';

interface ProductGridProps {
  products: (Product | DbProduct)[];
  title?: string;
  description?: string;
}

const ProductGrid = ({ products, title, description }: ProductGridProps) => {
  return (
    <section className="py-12">
      <div className="container-custom">
        {title && <h2 className="section-heading">{title}</h2>}
        {description && <p className="text-clay-700 mb-8 max-w-3xl">{description}</p>}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard key={product.id} product={product as Product} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductGrid;
