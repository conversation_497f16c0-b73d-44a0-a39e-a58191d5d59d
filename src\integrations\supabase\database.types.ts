export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      addresses: {
        Row: {
          id: string
          user_id: string
          full_name: string
          street: string
          city: string
          state: string | null
          postal_code: string
          country: string
          phone: string
          is_default: boolean
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          full_name: string
          street: string
          city: string
          state?: string | null
          postal_code: string
          country: string
          phone: string
          is_default?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          full_name?: string
          street?: string
          city?: string
          state?: string | null
          postal_code?: string
          country?: string
          phone?: string
          is_default?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "addresses_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      wishlists: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          is_default: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wishlists_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      wishlist_items: {
        Row: {
          id: string
          wishlist_id: string
          product_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          wishlist_id: string
          product_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          wishlist_id?: string
          product_id?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wishlist_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wishlist_items_wishlist_id_fkey"
            columns: ["wishlist_id"]
            isOneToOne: false
            referencedRelation: "wishlists"
            referencedColumns: ["id"]
          }
        ]
      },
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          image: string | null
          parent_id: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          }
        ]
      },
      products: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          price: number
          sale_price: number | null
          cost_price: number | null
          image: string | null
          additional_images: string[] | null
          category_id: string | null
          subcategory_id: string | null
          brand_id: string | null
          sku: string | null
          stock_quantity: number | null
          weight: number | null
          dimensions: string | null
          size: string | null
          additional_info: string | null
          in_stock: boolean
          is_active: boolean
          is_featured: boolean
          is_new: boolean
          is_best_seller: boolean
          rating: number
          review_count: number
          pricing_model: string | null
          bulk_pricing_tiers: string | null
          created_at: string
          updated_at: string
          external_id: string | null
          last_synced_at: string | null
          sync_status: string | null
          external_stock_quantity: number | null
          options: Json | null
          price_adjustments: Json | null
          option_name1: string | null
          option_type1: string | null
          option_description1: string | null
          option_price_adjustment1: string | null
          option_name2: string | null
          option_type2: string | null
          option_description2: string | null
          option_price_adjustment2: string | null
          option_name3: string | null
          option_type3: string | null
          option_description3: string | null
          option_price_adjustment3: string | null
          additional_info_title1: string | null
          additional_info_description1: string | null
          additional_info_title2: string | null
          additional_info_description2: string | null
          additional_info_title3: string | null
          additional_info_description3: string | null
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          price: number
          sale_price?: number | null
          cost_price?: number | null
          image?: string | null
          additional_images?: string[] | null
          category_id?: string | null
          subcategory_id?: string | null
          brand_id?: string | null
          sku?: string | null
          stock_quantity?: number | null
          weight?: number | null
          dimensions?: string | null
          size?: string | null
          additional_info?: string | null
          in_stock?: boolean
          is_active?: boolean
          is_featured?: boolean
          is_new?: boolean
          is_best_seller?: boolean
          rating?: number
          review_count?: number
          pricing_model?: string | null
          bulk_pricing_tiers?: string | null
          created_at?: string
          updated_at?: string
          external_id?: string | null
          last_synced_at?: string | null
          sync_status?: string | null
          external_stock_quantity?: number | null
          options?: Json | null
          price_adjustments?: Json | null
          option_name1?: string | null
          option_type1?: string | null
          option_description1?: string | null
          option_price_adjustment1?: string | null
          option_name2?: string | null
          option_type2?: string | null
          option_description2?: string | null
          option_price_adjustment2?: string | null
          option_name3?: string | null
          option_type3?: string | null
          option_description3?: string | null
          option_price_adjustment3?: string | null
          additional_info_title1?: string | null
          additional_info_description1?: string | null
          additional_info_title2?: string | null
          additional_info_description2?: string | null
          additional_info_title3?: string | null
          additional_info_description3?: string | null
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          price?: number
          sale_price?: number | null
          cost_price?: number | null
          image?: string | null
          additional_images?: string[] | null
          category_id?: string | null
          subcategory_id?: string | null
          brand_id?: string | null
          sku?: string | null
          stock_quantity?: number | null
          weight?: number | null
          dimensions?: string | null
          size?: string | null
          additional_info?: string | null
          in_stock?: boolean
          is_active?: boolean
          is_featured?: boolean
          is_new?: boolean
          is_best_seller?: boolean
          rating?: number
          review_count?: number
          pricing_model?: string | null
          bulk_pricing_tiers?: string | null
          created_at?: string
          updated_at?: string
          external_id?: string | null
          last_synced_at?: string | null
          sync_status?: string | null
          external_stock_quantity?: number | null
          options?: Json | null
          price_adjustments?: Json | null
          option_name1?: string | null
          option_type1?: string | null
          option_description1?: string | null
          option_price_adjustment1?: string | null
          option_name2?: string | null
          option_type2?: string | null
          option_description2?: string | null
          option_price_adjustment2?: string | null
          option_name3?: string | null
          option_type3?: string | null
          option_description3?: string | null
          option_price_adjustment3?: string | null
          additional_info_title1?: string | null
          additional_info_description1?: string | null
          additional_info_title2?: string | null
          additional_info_description2?: string | null
          additional_info_title3?: string | null
          additional_info_description3?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "products_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brands"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_subcategory_id_fkey"
            columns: ["subcategory_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          }
        ]
      },
      related_products: {
        Row: {
          id: string
          product_id: string
          related_product_id: string
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          related_product_id: string
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          related_product_id?: string
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "related_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "related_products_related_product_id_fkey"
            columns: ["related_product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      },
      profiles: {
        Row: {
          id: string
          email: string | null
          first_name: string | null
          last_name: string | null
          address: string | null
          city: string | null
          postal_code: string | null
          country: string | null
          is_admin: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          first_name?: string | null
          last_name?: string | null
          address?: string | null
          city?: string | null
          postal_code?: string | null
          country?: string | null
          is_admin?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          first_name?: string | null
          last_name?: string | null
          address?: string | null
          city?: string | null
          postal_code?: string | null
          country?: string | null
          is_admin?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      update_user_profile: {
        Args: {
          p_first_name: string
          p_last_name: string
          p_address: string
          p_city: string
          p_postal_code: string
          p_country: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
