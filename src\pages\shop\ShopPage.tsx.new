import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import type { Product } from '@/types/database';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import CategoryBanner from '@/components/shop/CategoryBanner';
import { categoryBannerImages, categoryDescriptions } from '@/data/categoryBanners';
import TransitionLayout from '@/components/layout/TransitionLayout';
import SearchInput from '@/components/shop/SearchInput';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import ProductGridSimple from '@/components/products/ProductGridSimple';

// Database category type from Supabase
interface DatabaseCategory {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string | null;
}

// Extended Category interface for UI
interface Category extends DatabaseCategory {
  subcategories?: Category[];
}

// SubCategory interface
interface SubCategory extends DatabaseCategory {
  parent?: Category;
}

const ShopPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
  const [selectedBrand, setSelectedBrand] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [forceRefresh, setForceRefresh] = useState(0); // Add a state to force refresh
  
  // Refs for debouncing
  const searchTimeoutRef = useRef<number>();
  const urlUpdateTimeoutRef = useRef<number>();
  
  // Flag to prevent URL updates from triggering state changes
  const isUpdatingFromUrlRef = useRef(false);

  // Effect to scroll to top when URL parameters change
  useEffect(() => {
    // Use a small timeout to ensure the scroll happens after the transition starts
    const timer = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 50);
    
    return () => clearTimeout(timer);
  }, [searchParams]);
  
  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);
  
  // One-time initialization from URL parameters when categories are loaded
  useEffect(() => {
    // Only run this effect when categories are first loaded
    if (categories.length === 0 || isUpdatingFromUrlRef.current) return;
    
    const categoryParam = searchParams.get('category');
    const subcategoryParam = searchParams.get('subcategory');
    const brandParam = searchParams.get('brand');
    const queryParam = searchParams.get('search');
    
    console.log('Initial URL Parameters:', { categoryParam, subcategoryParam, brandParam, queryParam });
    
    // Set the flag to prevent recursive updates
    isUpdatingFromUrlRef.current = true;
    
    try {
      // Handle category parameter if present
      if (categoryParam) {
        const categoryBySlug = categories.find(cat => cat.slug === categoryParam);
        if (categoryBySlug) {
          console.log('Found category by slug:', categoryBySlug.name);
          setSelectedCategory(categoryBySlug.id);
        } else {
          console.log('Could not find category with slug:', categoryParam);
          setSelectedCategory('all');
        }
      } else {
        setSelectedCategory('all');
      }
      
      // Handle subcategory parameter if present
      if (subcategoryParam && subcategories.length > 0) {
        const subcategoryBySlug = subcategories.find(subcat => subcat.slug === subcategoryParam);
        if (subcategoryBySlug) {
          console.log('Found subcategory by slug:', subcategoryBySlug.name);
          setSelectedSubcategory(subcategoryBySlug.id);
        } else {
          console.log('Could not find subcategory with slug:', subcategoryParam);
          setSelectedSubcategory('all');
        }
      } else {
        setSelectedSubcategory('all');
      }
      
      // Handle brand parameter if present
      if (brandParam) {
        setSelectedBrand(brandParam);
      } else {
        setSelectedBrand('all');
      }
      
      // Handle search query parameter if present
      if (queryParam) {
        setSearchQuery(queryParam);
      } else {
        setSearchQuery('');
      }
    } finally {
      // Reset the flag after a short delay
      setTimeout(() => {
        isUpdatingFromUrlRef.current = false;
      }, 100);
    }
  }, [categories, subcategories, searchParams]);
  
  // Get filtered subcategories based on selected category
  const filteredSubcategories = subcategories.filter(
    subcat => subcat.parent_id === selectedCategory
  );
  
  // Get category and subcategory names for display
  const categoryName = selectedCategory !== 'all'
    ? categories.find(cat => cat.id === selectedCategory)?.name || 'Products'
    : 'All Products';
    
  const subcategoryName = selectedSubcategory !== 'all'
    ? subcategories.find(subcat => subcat.id === selectedSubcategory)?.name || ''
    : '';

  // Combine for page title
  const pageTitle = subcategoryName 
    ? `${subcategoryName} - ${categoryName}`
    : categoryName;
  
  // Fetch categories and subcategories
  const fetchCategories = async () => {
    try {
      setLoading(true);
      
      // Fetch all categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('*')
        .is('parent_id', null) // Only get top-level categories
        .order('display_order', { ascending: true });
      
      if (categoriesError) {
        console.error('Error fetching categories:', categoriesError);
        return;
      }
      
      // Fetch all subcategories
      const { data: subcategoriesData, error: subcategoriesError } = await supabase
        .from('categories')
        .select('*, parent:parent_id(*)')
        .not('parent_id', 'is', null) // Only get subcategories
        .order('display_order', { ascending: true });
      
      if (subcategoriesError) {
        console.error('Error fetching subcategories:', subcategoriesError);
        return;
      }
      
      // Set state with fetched data
      setCategories(categoriesData as Category[]);
      setSubcategories(subcategoriesData as SubCategory[]);
      
      // Log for debugging
      console.log('Categories loaded:', categoriesData);
      console.log('Subcategories loaded:', subcategoriesData);
    } catch (error) {
      console.error('Error in fetchCategories:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Update URL based on current state without triggering state changes
  const updateURL = () => {
    // Clear the timeout if it exists
    if (urlUpdateTimeoutRef.current) {
      clearTimeout(urlUpdateTimeoutRef.current);
    }
    
    // Debounce URL updates to prevent too many history entries
    urlUpdateTimeoutRef.current = window.setTimeout(() => {
      // Set the flag to prevent recursive updates
      isUpdatingFromUrlRef.current = true;
      
      try {
        // Create a new URLSearchParams object based on current params
        const newParams = new URLSearchParams(searchParams);
        
        // Add category parameter if not 'all'
        if (selectedCategory !== 'all') {
          const category = categories.find(cat => cat.id === selectedCategory);
          if (category) {
            newParams.set('category', category.slug);
          }
        } else {
          // Remove category param if 'all' is selected
          newParams.delete('category');
        }
        
        // Add subcategory parameter if not 'all'
        if (selectedSubcategory !== 'all') {
          const subcategory = subcategories.find(subcat => subcat.id === selectedSubcategory);
          if (subcategory) {
            newParams.set('subcategory', subcategory.slug);
          }
        } else {
          // Remove subcategory param if 'all' is selected
          newParams.delete('subcategory');
        }
        
        // Add brand parameter if not 'all'
        if (selectedBrand !== 'all') {
          newParams.set('brand', selectedBrand);
        } else {
          // Remove brand param if 'all' is selected
          newParams.delete('brand');
        }
        
        // Add search parameter if not empty
        if (searchQuery) {
          newParams.set('search', searchQuery);
        } else {
          // Remove search param if empty
          newParams.delete('search');
        }
        
        // Update the URL without causing a page reload
        setSearchParams(newParams, { replace: true });
        
        // Force a refresh of the ProductGridSimple component
        setForceRefresh(prev => prev + 1);
      } finally {
        // Reset the flag after a short delay
        setTimeout(() => {
          isUpdatingFromUrlRef.current = false;
        }, 100);
      }
    }, 300); // 300ms debounce
  };

  // Get the current category data for the banner
  const getCategoryData = () => {
    let bannerImage = '';
    let description = '';
    let isDefault = false;
    let categorySlug = '';
    
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        categorySlug = category.slug;
        
        // Check if we have a custom banner for this category
        if (categoryBannerImages[category.slug]) {
          bannerImage = categoryBannerImages[category.slug];
          description = categoryDescriptions[category.slug] || category.description || '';
          isDefault = false;
        } else {
          // Use category image if available, otherwise default
          bannerImage = category.image || categoryBannerImages['all'];
          description = category.description || categoryDescriptions['all'];
          isDefault = !category.image;
        }
      }
    } else {
      // Default "All Products" banner
      bannerImage = categoryBannerImages['all'];
      description = categoryDescriptions['all'];
      isDefault = true;
    }
    
    return { bannerImage, description, isDefault, categorySlug };
  };
  
  const { bannerImage, description, isDefault, categorySlug } = getCategoryData();
  
  if (loading) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">Shop</h1>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
          <span className="ml-2">Loading categories...</span>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container-custom py-8">
      {/* Category Banner */}
      <CategoryBanner 
        title={pageTitle}
        image={bannerImage}
        description={description}
        isDefault={isDefault}
      />
      
      <div className="flex flex-col md:flex-row justify-between mb-8 gap-4">
        <div className="flex-1">
          <SearchInput
            initialValue={searchQuery}
            onSearch={(value) => {
              // Only update if the value has changed
              if (value !== searchQuery) {
                setSearchQuery(value);
                
                // Update URL with the new search value
                setTimeout(() => updateURL(), 0);
              }
            }}
            placeholder="Search products..."
            className="max-w-sm"
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          {/* Category dropdown */}
          <Select
            value={selectedCategory}
            onValueChange={(value) => {
              setSelectedCategory(value);
              setSelectedSubcategory('all'); // Reset subcategory when category changes
              
              // Wait for state to update before updating URL
              setTimeout(() => updateURL(), 0);
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          
          {/* Subcategory dropdown - only show if a category is selected and it has subcategories */}
          {selectedCategory !== 'all' && filteredSubcategories.length > 0 && (
            <Select
              value={selectedSubcategory}
              onValueChange={(value) => {
                setSelectedSubcategory(value);
                
                // Wait for state to update before updating URL
                setTimeout(() => updateURL(), 0);
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select Subcategory" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="all">All {categoryName}</SelectItem>
                  {filteredSubcategories.map((subcategory) => (
                    <SelectItem key={subcategory.id} value={subcategory.id}>
                      {subcategory.name}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
      
      <ProductGridSimple
        key={`product-grid-${forceRefresh}`} // Force re-render when filters change
        categoryId={selectedCategory !== 'all' ? selectedCategory : undefined}
        subcategoryId={selectedSubcategory !== 'all' ? selectedSubcategory : undefined}
        searchQuery={searchQuery}
      />
    </div>
  );
};

export default ShopPage;
