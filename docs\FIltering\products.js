// products.js - <PERSON>les product data loading and display

// Sample product data structure (will be replaced with actual data from JSON)
let allProducts = [];
let currentFilters = {};
let currentPage = 1;
let productsPerPage = 12;

// Function to load products from JSON file
async function loadProducts() {
    try {
        const response = await fetch('data/products.json');
        allProducts = await response.json();
        renderProducts(allProducts);
        updateProductCount(allProducts.length);
    } catch (error) {
        console.error('Error loading products:', error);
        document.getElementById('product-grid').innerHTML = '<p class="error-message">Failed to load products. Please try again later.</p>';
    }
}

// Function to render products to the grid
function renderProducts(products, page = 1) {
    const productGrid = document.getElementById('product-grid');
    productGrid.innerHTML = '';
    
    // Calculate pagination
    const startIndex = (page - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    const paginatedProducts = products.slice(startIndex, endIndex);
    
    if (paginatedProducts.length === 0) {
        productGrid.innerHTML = '<p class="no-products">No products match your selected filters.</p>';
        return;
    }
    
    paginatedProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.setAttribute('data-id', product.id || '');
        
        // Set data attributes for filtering
        if (product.seed_type) {
            productCard.setAttribute('data-seed-type', product.seed_type.toLowerCase());
        }
        
        // Add other data attributes based on product properties
        Object.keys(product).forEach(key => {
            if (typeof product[key] === 'string' || typeof product[key] === 'number') {
                productCard.setAttribute(`data-${key.replace(/_/g, '-')}`, product[key].toString().toLowerCase());
            }
        });
        
        // Create badge class based on seed type
        let badgeClass = '';
        let badgeText = '';
        
        if (product.seed_type) {
            const seedType = product.seed_type.toLowerCase();
            if (seedType === 'autoflower') {
                badgeClass = 'badge-auto';
                badgeText = 'Auto';
            } else if (seedType === 'feminized') {
                badgeClass = 'badge-fem';
                badgeText = 'Fem';
            } else if (seedType === 'regular') {
                badgeClass = 'badge-reg';
                badgeText = 'Reg';
            }
        }
        
        productCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image_url || 'img/placeholder.jpg'}" alt="${product.name}">
                ${badgeText ? `<span class="product-badge ${badgeClass}">${badgeText}</span>` : ''}
            </div>
            <div class="product-info">
                <h3 class="product-title"><a href="#">${product.name}</a></h3>
                <div class="product-price">${product.price || '€0.00'}</div>
                <div class="product-actions">
                    <select class="quantity-select">
                        <option value="1">1 seed</option>
                        <option value="3">3 seeds</option>
                        <option value="5">5 seeds</option>
                        <option value="10">10 seeds</option>
                    </select>
                    <button class="add-to-cart" data-id="${product.id || ''}">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                </div>
            </div>
        `;
        
        productGrid.appendChild(productCard);
    });
    
    // Update pagination
    updatePagination(products.length, page);
}

// Function to update product count display
function updateProductCount(count) {
    document.getElementById('product-total').textContent = count;
}

// Function to update pagination
function updatePagination(totalProducts, currentPage) {
    const paginationElement = document.querySelector('.pagination');
    const totalPages = Math.ceil(totalProducts / productsPerPage);
    
    let paginationHTML = '';
    
    // Previous page
    if (currentPage > 1) {
        paginationHTML += `<a href="#" data-page="${currentPage - 1}" class="prev">&laquo;</a>`;
    }
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            paginationHTML += `<a href="#" data-page="${i}" ${i === currentPage ? 'class="active"' : ''}>${i}</a>`;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            paginationHTML += `<span>...</span>`;
        }
    }
    
    // Next page
    if (currentPage < totalPages) {
        paginationHTML += `<a href="#" data-page="${currentPage + 1}" class="next">&raquo;</a>`;
    }
    
    paginationElement.innerHTML = paginationHTML;
    
    // Add event listeners to pagination links
    document.querySelectorAll('.pagination a').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = parseInt(link.getAttribute('data-page'));
            currentPage = page;
            renderProducts(filterProducts(allProducts, currentFilters), page);
        });
    });
}

// Function to handle sorting
function handleSorting(sortValue, products) {
    const sortedProducts = [...products];
    
    switch (sortValue) {
        case 'name-asc':
            sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'name-desc':
            sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
            break;
        case 'price-asc':
            sortedProducts.sort((a, b) => {
                const priceA = parseFloat(a.price.replace(/[^0-9.]/g, ''));
                const priceB = parseFloat(b.price.replace(/[^0-9.]/g, ''));
                return priceA - priceB;
            });
            break;
        case 'price-desc':
            sortedProducts.sort((a, b) => {
                const priceA = parseFloat(a.price.replace(/[^0-9.]/g, ''));
                const priceB = parseFloat(b.price.replace(/[^0-9.]/g, ''));
                return priceB - priceA;
            });
            break;
        default:
            // Default sorting (relevance) - no change
            break;
    }
    
    return sortedProducts;
}

// Initialize product loading when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    loadProducts();
    
    // Set up sort select handler
    const sortSelect = document.getElementById('sort-select');
    sortSelect.addEventListener('change', () => {
        const sortValue = sortSelect.value;
        const filteredProducts = filterProducts(allProducts, currentFilters);
        const sortedProducts = handleSorting(sortValue, filteredProducts);
        renderProducts(sortedProducts, currentPage);
    });
    
    // Set up add to cart functionality
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('add-to-cart') || e.target.parentElement.classList.contains('add-to-cart')) {
            const button = e.target.classList.contains('add-to-cart') ? e.target : e.target.parentElement;
            const productId = button.getAttribute('data-id');
            const quantitySelect = button.closest('.product-actions').querySelector('.quantity-select');
            const quantity = parseInt(quantitySelect.value);
            
            addToCart(productId, quantity);
        }
    });
});

// Function to add product to cart
function addToCart(productId, quantity) {
    // In a real implementation, this would update a cart object and possibly send to server
    console.log(`Added product ${productId} to cart, quantity: ${quantity}`);
    
    // Update cart count (simplified)
    const cartCount = document.getElementById('cart-count');
    const currentCount = parseInt(cartCount.textContent);
    cartCount.textContent = currentCount + 1;
    
    // Show confirmation message
    const product = allProducts.find(p => p.id === productId || p.data_id_product === productId);
    if (product) {
        alert(`Added ${quantity} ${quantity === 1 ? 'seed' : 'seeds'} of ${product.name} to your cart.`);
    }
}
