export type DiscountType = 'percentage' | 'fixed_amount';

export interface DiscountCode {
  id: string;
  code: string;
  description: string | null;
  discount_type: DiscountType;
  discount_value: number;
  minimum_order_amount: number;
  start_date: string | null;
  end_date: string | null;
  usage_limit: number | null;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DiscountCodeFormData {
  id?: string;
  code: string;
  description: string;
  discount_type: DiscountType;
  discount_value: number;
  minimum_order_amount: number;
  start_date: string | null;
  end_date: string | null;
  usage_limit: number | null;
  is_active: boolean;
}

export interface AppliedDiscount {
  code: string;
  discountType: DiscountType;
  discountValue: number;
  discountAmount: number; // The actual amount discounted from the order
}

export interface DiscountValidationResult {
  isValid: boolean;
  message: string;
  discount?: AppliedDiscount;
}
