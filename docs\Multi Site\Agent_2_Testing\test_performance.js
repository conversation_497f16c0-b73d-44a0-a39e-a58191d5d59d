/**
 * Performance Testing Script for Multi-Tenant Architecture
 * 
 * This script runs performance tests on the multi-tenant database
 * to evaluate query efficiency and index usage.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const config = {
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseKey: process.env.SUPABASE_KEY
};

// Initialize Supabase client
const supabase = createClient(config.supabaseUrl, config.supabaseKey);

/**
 * Get tenant IDs
 * @returns {Promise<Object>} Object containing tenant IDs
 */
async function getTenantIds() {
  const { data, error } = await supabase
    .from('tenant_management.tenants')
    .select('id, name, subdomain');
  
  if (error) {
    throw new Error(`Failed to get tenant IDs: ${error.message}`);
  }
  
  const tenants = {};
  data.forEach(tenant => {
    tenants[tenant.subdomain] = {
      id: tenant.id,
      name: tenant.name
    };
  });
  
  return tenants;
}

/**
 * Run a performance test for a specific query
 * @param {string} description - Description of the test
 * @param {Function} queryFn - Function that returns a query builder
 * @returns {Promise<Object>} Test results
 */
async function runPerformanceTest(description, queryFn) {
  console.log(`Running performance test: ${description}`);
  
  const startTime = performance.now();
  
  // Run the query
  const query = queryFn();
  const { data, error } = await query;
  
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  
  if (error) {
    console.error(`Error running query: ${error.message}`);
    return {
      description,
      success: false,
      error: error.message,
      executionTime
    };
  }
  
  // Get the execution plan for the query
  const { data: explainData, error: explainError } = await supabase.rpc(
    'explain_query',
    { query_text: query.toSQL().sql.replace(/"/g, '') }
  );
  
  const usesIndex = explainData ? explainData.some(line => line.includes('Index Scan')) : false;
  
  console.log(`✅ Query completed in ${executionTime.toFixed(2)}ms`);
  console.log(`✅ Rows returned: ${data?.length || 0}`);
  console.log(`✅ Uses index: ${usesIndex ? 'Yes' : 'No'}`);
  
  return {
    description,
    success: true,
    executionTime,
    rowCount: data?.length || 0,
    usesIndex
  };
}

/**
 * Run all performance tests
 * @returns {Promise<void>}
 */
async function runAllPerformanceTests() {
  try {
    const tenants = await getTenantIds();
    const tenantA = tenants['fashion'];
    const tenantB = tenants['techhub'];
    const tenantC = tenants['booknook'];
    
    const results = [];
    
    // Test 1: Simple query with tenant filter
    results.push(await runPerformanceTest(
      'Simple query with tenant filter',
      () => supabase
        .from('products')
        .select('*')
        .eq('tenant_id', tenantA.id)
    ));
    
    // Test 2: Join query with tenant filter
    results.push(await runPerformanceTest(
      'Join query with tenant filter',
      () => supabase
        .from('orders')
        .select(`
          id,
          total_amount,
          status,
          customers (name, email)
        `)
        .eq('tenant_id', tenantB.id)
    ));
    
    // Test 3: Query with multiple conditions
    results.push(await runPerformanceTest(
      'Query with multiple conditions',
      () => supabase
        .from('products')
        .select('*')
        .eq('tenant_id', tenantC.id)
        .gt('price', 20)
        .order('price', { ascending: false })
    ));
    
    // Test 4: Count query with tenant filter
    results.push(await runPerformanceTest(
      'Count query with tenant filter',
      () => supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('tenant_id', tenantA.id)
    ));
    
    // Test 5: Query without tenant filter (should be blocked by RLS)
    results.push(await runPerformanceTest(
      'Query without tenant filter (should be blocked by RLS)',
      () => supabase
        .from('products')
        .select('*')
    ));
    
    // Test 6: Query with OR condition on tenant_id (should be blocked by RLS)
    results.push(await runPerformanceTest(
      'Query with OR condition on tenant_id',
      () => supabase
        .from('products')
        .select('*')
        .or(`tenant_id.eq.${tenantA.id},tenant_id.eq.${tenantB.id}`)
    ));
    
    // Test 7: Performance with large dataset (if available)
    results.push(await runPerformanceTest(
      'Performance with potentially larger dataset',
      () => supabase
        .from('products')
        .select('*')
        .eq('tenant_id', tenantA.id)
        .range(0, 999)
    ));
    
    // Print summary
    console.log('\nPerformance Test Summary:');
    console.table(results.map(r => ({
      Test: r.description,
      'Time (ms)': r.executionTime.toFixed(2),
      Rows: r.rowCount,
      'Uses Index': r.usesIndex ? 'Yes' : 'No',
      Success: r.success ? 'Yes' : 'No'
    })));
    
    // Check for any performance issues
    const performanceIssues = results.filter(r => 
      (r.executionTime > 500 && r.rowCount < 1000) || // Slow queries
      (!r.usesIndex && r.rowCount > 10) // Not using indexes for larger datasets
    );
    
    if (performanceIssues.length > 0) {
      console.log('\n⚠️ Performance Issues Detected:');
      performanceIssues.forEach(issue => {
        console.log(`- ${issue.description}: ${issue.executionTime.toFixed(2)}ms, Uses Index: ${issue.usesIndex ? 'Yes' : 'No'}`);
      });
    } else {
      console.log('\n✅ No performance issues detected');
    }
    
  } catch (error) {
    console.error(`❌ Performance tests failed: ${error.message}`);
  }
}

/**
 * Create an RPC function to explain query plans
 * @returns {Promise<void>}
 */
async function createExplainFunction() {
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE OR REPLACE FUNCTION explain_query(query_text TEXT)
      RETURNS TEXT[] AS $$
      DECLARE
        result TEXT[];
        row_record RECORD;
      BEGIN
        FOR row_record IN EXECUTE 'EXPLAIN ' || query_text
        LOOP
          result := array_append(result, row_record."QUERY PLAN");
        END LOOP;
        RETURN result;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `
  });
  
  if (error) {
    console.error(`Failed to create explain function: ${error.message}`);
    throw error;
  }
  
  console.log('✅ Created explain function for query analysis');
}

/**
 * Main function to run performance tests
 */
async function main() {
  try {
    console.log('Starting performance tests...');
    
    // Create explain function if it doesn't exist
    await createExplainFunction();
    
    // Run all performance tests
    await runAllPerformanceTests();
    
    console.log('✅ All performance tests completed');
  } catch (error) {
    console.error(`❌ Performance tests failed: ${error.message}`);
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  runPerformanceTest,
  runAllPerformanceTests
};
