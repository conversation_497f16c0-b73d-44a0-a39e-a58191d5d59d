
-- First, drop existing policies and functions to avoid conflicts
DROP POLICY IF EXISTS "Users can read their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "user_read_own_profile" ON profiles;
DROP POLICY IF EXISTS "admin_read_all_profiles" ON profiles;
DROP FUNCTION IF EXISTS get_profile(UUID);
DROP FUNCTION IF EXISTS get_user_profile(UUID);
DROP FUNCTION IF EXISTS is_admin(UUID);

-- Create a function to check if user is admin that DOES NOT query profiles
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  admin_status BOOLEAN;
BEGIN
  -- Direct query to avoid RLS recursion
  SELECT is_admin INTO admin_status FROM profiles WHERE id = user_id;
  RETURN COALESCE(admin_status, false);
END;
$$;

-- Create a function to get profile by id (for single profile)
CREATE OR REPLACE FUNCTION get_profile(user_id UUID DEFAULT auth.uid())
RETURNS SETOF profiles
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT * FROM profiles WHERE id = user_id;
$$;

-- Create a function to get ALL profiles for admins or just the user's own profile
CREATE OR REPLACE FUNCTION get_user_profile()
RETURNS SETOF profiles
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  -- This will return either all profiles for admins or just the user's profile
  SELECT p.*
  FROM profiles p
  WHERE 
    -- Either it's the user's own profile
    p.id = auth.uid()
    -- Or the requesting user is an admin
    OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND is_admin = true);
$$;

-- Enable RLS on profiles table (in case it's not already enabled)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies to regulate access directly (without nesting profile queries)
CREATE POLICY "Users can read their own profile or admins can read all"
ON profiles
FOR SELECT
USING (
  -- Either it's their own profile
  auth.uid() = id
  -- Or they're an admin
  OR is_admin()
);

-- Add policy for inserts (users can only insert their own profile)
CREATE POLICY "Users can insert their own profile"
ON profiles
FOR INSERT
WITH CHECK (auth.uid() = id);

-- Add policy for updates (users can update their own profile, admins can update any)
CREATE POLICY "Users can update their own profile"
ON profiles
FOR UPDATE
USING (
  auth.uid() = id
  OR is_admin()
);
