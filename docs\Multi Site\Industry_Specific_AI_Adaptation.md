# Industry-Specific AI Adaptation for Multi-Tenant Architecture

This document outlines strategies for adapting AI features across different industry verticals in our multi-tenant architecture.

## Challenges

When expanding beyond CBD/cannabis to serve multiple industries, our AI systems face several challenges:

1. **Domain Knowledge**: Different industries have unique terminology and product attributes
2. **Content Style**: Content expectations vary by industry (technical for electronics, visual for fashion)
3. **Source Relevance**: Image and data sources that work for CBD may not work for other industries
4. **Compliance Requirements**: Different industries have varying regulatory considerations

## Adaptation Strategies

### 1. Tenant Context Injection

Enhance the `UnifiedAIService` to include tenant-specific context in every AI request:

```typescript
async function generateContent(prompt: string, tenantId: string) {
  // Fetch tenant industry and preferences
  const tenant = await getTenantDetails(tenantId);
  
  // Inject tenant context into the prompt
  const contextualizedPrompt = `
    [CONTEXT: This request is for a ${tenant.industry} business named ${tenant.name}]
    ${prompt}
  `;
  
  return aiProvider.generate(contextualizedPrompt);
}
```

### 2. Industry-Specific Prompt Templates

Store industry-specific prompt templates in your database:

```typescript
// In your database
interface PromptTemplate {
  id: string;
  industry: string;
  templateType: string; // e.g., "product_description", "blog_post"
  template: string;
  tenant_id: string; // Null for global templates, specific for custom ones
}

// When generating content
async function generateProductDescription(product, tenantId) {
  const tenant = await getTenantDetails(tenantId);
  const template = await getPromptTemplate(tenant.industry, "product_description");
  
  const prompt = template.replace("{{product_name}}", product.name)
                         .replace("{{product_features}}", product.features);
                         
  return aiProvider.generate(prompt);
}
```

### 3. Dynamic Provider Selection

The `AIServiceManager` can select different AI providers based on tenant industry:

```typescript
function selectProviderForTenant(tenantId, taskType) {
  const tenant = getTenantDetails(tenantId);
  
  if (tenant.industry === "CBD" && taskType === "product_description") {
    return new DeepSeekProvider(); // Specialized for CBD
  } else if (tenant.industry === "Fashion") {
    return new GeminiProvider(); // Better for fashion
  }
  
  return new OpenRouterProvider(); // Default
}
```

### 4. Tenant-Specific Fine-Tuning

For industries requiring specialized knowledge:

1. Create fine-tuned models for each major industry vertical
2. Store the model selection in tenant settings
3. Route requests to the appropriate model

```typescript
async function getModelForTenant(tenantId) {
  const tenant = await getTenantDetails(tenantId);
  
  // Map industry to fine-tuned model
  const industryModelMap = {
    "CBD": "ft:deepseek-cbd-v1",
    "Fashion": "ft:gemini-fashion-v2",
    "Tech": "ft:openrouter-tech-v1"
  };
  
  return industryModelMap[tenant.industry] || "default-model";
}
```

### 5. Image Scraping Adaptation

Adapting the `ImageScrapingService` for different industries:

```typescript
class MultiIndustrySourceManager extends SourceManager {
  constructor(tenantId) {
    super();
    this.tenantId = tenantId;
    this.loadIndustrySpecificSources();
  }
  
  async loadIndustrySpecificSources() {
    const tenant = await getTenantDetails(this.tenantId);
    
    // Load sources specific to this tenant's industry
    const sources = await getSourcesForIndustry(tenant.industry);
    this.setSources(sources);
  }
}
```

## Implementation Roadmap

1. **Industry Classification System**
   - Add `industry` field to tenant table
   - Create standardized industry taxonomy
   - Develop industry selection during tenant onboarding

2. **Prompt Template Library**
   - Build template database for common AI tasks
   - Create industry-specific variations
   - Allow tenant-specific customization

3. **AI Provider Routing**
   - Implement provider selection logic
   - Test performance across industry types
   - Optimize routing based on results

4. **Source Management**
   - Create industry-specific source databases
   - Implement automatic source relevance scoring
   - Build tenant-specific source management UI

5. **Testing Framework**
   - Develop cross-industry test cases
   - Benchmark AI performance by industry
   - Create automated quality assurance system

## Integration with Existing Architecture

This approach builds on our current AI architecture:

- `AIServiceManager` handles tenant context and routing
- `UnifiedAIService` manages the provider selection
- Integration layers (ImageScrapingIntegration, BlogAIIntegration, etc.) use tenant context
- Tenant-specific settings stored in the database

By extending rather than replacing our current architecture, we can maintain backward compatibility while expanding to new industries.
