import React, { useState } from 'react';
import { QRCodeCanvas } from 'qrcode.react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Download, RefreshCw } from 'lucide-react';

// Default base URL - should be configurable in settings
const DEFAULT_BASE_URL = 'https://www.bitsnbongs.com';

interface ProductQRCodeProps {
  productSlug: string;
  productName: string;
}

export function ProductQRCode({ productSlug, productName }: ProductQRCodeProps) {
  const [size, setSize] = useState(200);
  const [baseUrl, setBaseUrl] = useState(DEFAULT_BASE_URL);
  const [fgColor, setFgColor] = useState('#000000');
  const [bgColor, setBgColor] = useState('#ffffff');
  
  // Generate the product URL
  const productUrl = `${baseUrl}/products/${productSlug}`;
  
  // Handle QR code download
  const handleDownload = (format: 'png' | 'svg') => {
    const canvas = document.getElementById('product-qr-code') as HTMLCanvasElement;
    
    if (format === 'png') {
      const link = document.createElement('a');
      link.download = `${productSlug}-qrcode.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    } else if (format === 'svg') {
      // For SVG, we need to create an SVG string
      // This is a simplified approach - for production, you might want a more robust solution
      const svgElement = document.getElementById('product-qr-code')?.parentNode as SVGElement;
      if (svgElement) {
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);
        
        const link = document.createElement('a');
        link.download = `${productSlug}-qrcode.svg`;
        link.href = svgUrl;
        link.click();
        
        URL.revokeObjectURL(svgUrl);
      }
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="base-url">Base URL</Label>
            <div className="flex gap-2">
              <Input
                id="base-url"
                value={baseUrl}
                onChange={(e) => setBaseUrl(e.target.value)}
                placeholder="https://yourdomain.com"
              />
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => setBaseUrl(DEFAULT_BASE_URL)}
                title="Reset to default URL"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              This is the base URL used for the QR code. Update this when your site goes live.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label>QR Code Size</Label>
            <Slider
              value={[size]}
              min={100}
              max={400}
              step={10}
              onValueChange={(values) => setSize(values[0])}
            />
            <p className="text-xs text-muted-foreground">
              {size}px × {size}px
            </p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fg-color">Foreground Color</Label>
              <div className="flex gap-2">
                <div 
                  className="h-9 w-9 rounded-md border"
                  style={{ backgroundColor: fgColor }}
                />
                <Input
                  id="fg-color"
                  type="color"
                  value={fgColor}
                  onChange={(e) => setFgColor(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bg-color">Background Color</Label>
              <div className="flex gap-2">
                <div 
                  className="h-9 w-9 rounded-md border"
                  style={{ backgroundColor: bgColor }}
                />
                <Input
                  id="bg-color"
                  type="color"
                  value={bgColor}
                  onChange={(e) => setBgColor(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </div>
          
          <div className="pt-2">
            <p className="text-sm font-medium mb-2">Download Options</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => handleDownload('png')}
              >
                <Download className="mr-2 h-4 w-4" />
                Download PNG
              </Button>
              <Button 
                variant="outline" 
                onClick={() => handleDownload('svg')}
              >
                <Download className="mr-2 h-4 w-4" />
                Download SVG
              </Button>
            </div>
          </div>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <div className="mb-4 flex justify-center">
                <QRCodeCanvas
                  id="product-qr-code"
                  value={productUrl}
                  size={size}
                  fgColor={fgColor}
                  bgColor={bgColor}
                  level="H"
                  includeMargin={true}
                />
              </div>
              <p className="text-sm font-medium">{productName}</p>
              <p className="text-xs text-muted-foreground break-all mt-1">
                {productUrl}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
