import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { formatPrice } from '@/lib/utils';

interface RelatedProductsProps {
  productId: string;
  categoryId?: string;
  limit?: number;
}

type RelatedProductResult = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  sale_price: number | null;
  image: string | null;
  category_id: string | null;
  is_active: boolean;
  in_stock: boolean;
};

export default function RelatedProducts({ productId, categoryId, limit = 4 }: RelatedProductsProps) {
  const [relatedProducts, setRelatedProducts] = useState<RelatedProductResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      if (!productId) return;

      try {
        setIsLoading(true);

        // Use the get_related_products function to fetch related products
        // This function will automatically handle the logic of:
        // 1. First trying to get user-selected related products
        // 2. Then falling back to products from the same category
        // 3. Finally falling back to random products if needed
        // It also ensures all products are active and have images
        const { data, error } = await supabase
          .rpc('get_related_products', {
            p_product_id: productId,
            p_limit: limit
          });

        if (error) throw error;

        if (data && Array.isArray(data) && data.length > 0) {
          setRelatedProducts(data);
        }
      } catch (error) {
        console.error('Error fetching related products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedProducts();
  }, [productId, categoryId, limit]);

  if (isLoading) {
    return (
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="h-40 w-full rounded-md" />
              <Skeleton className="h-4 w-3/4 rounded-md" />
              <Skeleton className="h-4 w-1/2 rounded-md" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (relatedProducts.length === 0) {
    return null;
  }

  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {relatedProducts.map((product) => (
          <Link
            key={product.id}
            to={`/shop/${product.slug}`}
            className="group"
          >
            <div className="rounded-md overflow-hidden aspect-square relative mb-2">
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-sm">No image</span>
                </div>
              )}

              {product.sale_price && product.sale_price > 0 && (
                <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                  Sale
                </div>
              )}
            </div>

            <h3 className="font-medium text-gray-900 group-hover:text-primary transition-colors">
              {product.name}
            </h3>

            <div className="mt-1">
              {product.sale_price && product.sale_price > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-primary font-medium">
                    {formatPrice(product.sale_price)}
                  </span>
                  <span className="text-gray-500 text-sm line-through">
                    {formatPrice(product.price)}
                  </span>
                </div>
              ) : (
                <span className="text-gray-900 font-medium">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
