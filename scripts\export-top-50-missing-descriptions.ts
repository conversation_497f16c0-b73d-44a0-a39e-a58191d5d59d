#!/usr/bin/env tsx
/**
 * Export TOP 50 seed products with missing descriptions for maximum credit utilization
 * Focus on highest value products regardless of active/inactive status
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function exportTop50MissingDescriptions() {
  console.log('💰 Finding TOP 50 highest-value seed products with missing descriptions...\n');
  
  try {
    // Get all products (active and inactive)
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        is_active,
        description,
        image,
        sku
      `);
    
    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }
    
    // Filter to seed products with missing/poor descriptions
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      const isSeed = name.includes('seed') || name.includes('auto') || 
                     name.includes('feminised') || name.includes('feminized') ||
                     name.includes('female') || name.includes('strain');
      
      const needsDescription = !p.description || p.description.trim().length < 100;
      
      return isSeed && needsDescription && p.price && p.price > 0;
    }) || [];
    
    console.log(`📄 Found ${seedProducts.length} seed products with missing/poor descriptions\n`);
    
    // Calculate priority score for each product
    const analysis = seedProducts.map(product => {
      let priorityScore = 0;
      
      // Price-based scoring (higher price = higher priority)
      if (product.price > 80) priorityScore += 50;
      else if (product.price > 60) priorityScore += 40;
      else if (product.price > 40) priorityScore += 30;
      else if (product.price > 20) priorityScore += 20;
      else priorityScore += 10;
      
      // Active products get bonus
      if (product.is_active) priorityScore += 25;
      
      // Brand recognition bonus
      const name = product.name.toLowerCase();
      if (name.includes('barney') || name.includes('royal queen')) priorityScore += 15;
      if (name.includes('420 fastbuds') || name.includes('fastbuds')) priorityScore += 15;
      if (name.includes('humboldt') || name.includes('perfect tree')) priorityScore += 10;
      
      // Popular strain genetics bonus
      const popularStrains = [
        'gorilla', 'cookies', 'gelato', 'zkittlez', 'runtz', 'wedding cake',
        'purple punch', 'og kush', 'diesel', 'haze', 'amnesia', 'white widow',
        'northern lights', 'jack herer', 'blue dream', 'girl scout cookies',
        'mimosa', 'biscotti', 'sherbert', 'lemon', 'strawberry'
      ];
      
      if (popularStrains.some(strain => name.includes(strain))) {
        priorityScore += 10;
      }
      
      // Auto genetics bonus (popular category)
      if (name.includes('auto')) priorityScore += 5;
      
      return {
        name: product.name,
        price: product.price || 0,
        is_active: product.is_active,
        description_length: product.description?.length || 0,
        has_image: !!product.image,
        has_sku: !!(product.sku && product.sku.trim().length > 0),
        priority_score: priorityScore,
        status: product.is_active ? 'ACTIVE' : 'INACTIVE',
        value_tier: product.price > 60 ? 'PREMIUM' : product.price > 30 ? 'MID-TIER' : 'BUDGET'
      };
    });
    
    // Sort by priority score (highest first)
    analysis.sort((a, b) => b.priority_score - a.priority_score);
    
    // Take top 50
    const top50 = analysis.slice(0, 50);
    
    console.log('🎯 TOP 50 HIGHEST-VALUE PRODUCTS FOR AGENT #1:\n');
    
    top50.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   💰 £${product.price} | ${product.status} | ${product.value_tier} | Score: ${product.priority_score}`);
      console.log(`   📝 Description: ${product.description_length} chars | 🖼️ Image: ${product.has_image ? '✅' : '❌'}`);
      console.log('');
    });
    
    // Create CSV for Agent #1
    const csvContent = [
      'product_name,price,status,value_tier,priority_score,description_length,has_image,has_sku,notes',
      ...top50.map(p => {
        const notes = [];
        if (p.is_active) notes.push('ACTIVE - Immediate sales impact');
        if (p.price > 60) notes.push('PREMIUM pricing');
        if (p.description_length === 0) notes.push('NO description');
        else if (p.description_length < 50) notes.push('POOR description');
        
        return `"${p.name}",${p.price},"${p.status}","${p.value_tier}",${p.priority_score},${p.description_length},${p.has_image},${p.has_sku},"${notes.join('; ')}"`;
      })
    ].join('\n');
    
    writeFileSync('docs/super_agent/top_50_high_value_missing_descriptions.csv', csvContent);
    
    // Statistics
    const activeCount = top50.filter(p => p.is_active).length;
    const premiumCount = top50.filter(p => p.value_tier === 'PREMIUM').length;
    const totalValue = top50.reduce((sum, p) => sum + p.price, 0);
    const avgPrice = totalValue / top50.length;
    
    console.log('📊 TOP 50 SUMMARY:');
    console.log(`   💰 Total value: £${totalValue}`);
    console.log(`   📈 Average price: £${avgPrice.toFixed(2)}`);
    console.log(`   ✅ Active products: ${activeCount}/50 (${Math.round(activeCount/50*100)}%)`);
    console.log(`   💎 Premium products: ${premiumCount}/50 (${Math.round(premiumCount/50*100)}%)`);
    
    console.log('\n🎯 STRATEGIC VALUE:');
    console.log(`   🚀 Maximum credit utilization`);
    console.log(`   💰 Highest ROI products selected`);
    console.log(`   ⚡ Mix of active (immediate impact) + premium inactive (future activation)`);
    console.log(`   🏆 Better than 10 products - maximize while credits last!`);
    
    console.log(`\n📁 Generated: docs/super_agent/top_50_high_value_missing_descriptions.csv`);
    console.log('\n🚀 READY FOR AGENT #1 MAXIMUM IMPACT MISSION!');
    
  } catch (err) {
    console.error('❌ Export failed:', err);
  }
}

// Run the export
exportTop50MissingDescriptions().catch(console.error);
