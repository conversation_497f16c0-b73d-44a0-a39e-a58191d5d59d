// Script to directly fix the Next Day Delivery option
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function directFixShipping() {
  console.log('🔧 Direct fix for Next Day Delivery...');
  
  try {
    // 1. Check for the shipping method by name
    console.log('Searching for Next Day Delivery method...');
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*')
      .ilike('name', '%Next Day%');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} shipping methods matching "Next Day"`);
    
    if (methods.length === 0) {
      console.log('No Next Day Delivery method found');
      return;
    }
    
    // Log all matching methods for debugging
    methods.forEach(method => {
      console.log(`- ID: ${method.id}, Name: ${method.name}, Active: ${method.is_active}`);
    });
    
    // 2. Force update all matching methods to be inactive
    console.log('Setting all Next Day Delivery methods to inactive...');
    
    for (const method of methods) {
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', method.id);
        
      if (updateError) {
        console.error(`Error updating method ${method.id}:`, updateError);
      } else {
        console.log(`✅ Method ${method.id} (${method.name}) set to inactive`);
      }
    }
    
    // 3. Clear any cached data
    console.log('Adding cache invalidation timestamp...');
    
    const timestamp = new Date().toISOString();
    
    const { error: settingsError } = await supabase
      .from('settings')
      .upsert({ 
        key: 'shipping_cache_timestamp', 
        value: timestamp
      });
      
    if (settingsError) {
      console.error('Error updating settings:', settingsError);
    } else {
      console.log('✅ Cache timestamp updated');
    }
    
    // 4. Verify all methods are inactive
    const { data: verifyData, error: verifyError } = await supabase
      .from('shipping_methods')
      .select('*')
      .ilike('name', '%Next Day%');
      
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('Verification results:');
    verifyData.forEach(method => {
      console.log(`- ${method.name}: ${method.is_active ? 'STILL ACTIVE ❌' : 'INACTIVE ✅'}`);
    });
    
    console.log('\n🔄 To ensure changes take effect:');
    console.log('1. Restart the development server');
    console.log('2. Reload the browser');
    console.log('3. Check the admin panel and checkout page');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
directFixShipping()
  .catch(err => {
    console.error('Error running script:', err);
  }); 