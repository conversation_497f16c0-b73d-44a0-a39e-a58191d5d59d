
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';
import { AIServiceManager } from '@/services/ai/AIServiceManager';

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

interface Product {
  id: string;
  name: string;
  price: number;
  imageUrl: string;
  description: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  inStock?: boolean;
}

interface ChatResponse {
  message: string;
  suggestions?: string[];
  products?: Product[];
}

const ChatInterface = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [aiService, setAiService] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize Cheech on component mount
  useEffect(() => {
    initializeCheech();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Add welcome message when initialized
  useEffect(() => {
    if (isInitialized && messages.length === 0) {
      addWelcomeMessage();
    }
  }, [isInitialized]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Cheech AI system
  const initializeCheech = async () => {
    try {
      const aiManager = new AIServiceManager();
      await aiManager.initialize({
        deepseek_key: import.meta.env.VITE_DEEPSEEK_API_KEY,
        gemini_key: import.meta.env.VITE_GEMINI_API_KEY,
        openrouter_key: import.meta.env.VITE_OPENROUTER_API_KEY
      });

      setAiService(aiManager.getUnifiedAI());
      setIsInitialized(true);
      console.log('🎵 Cheech is ready!');
    } catch (error) {
      console.error('Failed to initialize Cheech:', error);
      // Fallback to demo mode
      setIsInitialized(true);
    }
  };

  // Add welcome message with audio
  const addWelcomeMessage = () => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      content: "Hey there! I'm Cheech, your BitsnBongs product expert. How can I help you today?",
      isUser: false,
      timestamp: new Date()
    };

    setMessages([welcomeMessage]);
    playWelcomeAudio();
  };

  // Audio functions
  const welcomeAudioFiles = [
    '/audio/hey-welcom-to-bits-n-bongs-how-can-i-help-you-man.mp3',
    '/audio/hey-dude-whats-up-need-a-hand-with-anything-man.mp3',
    '/audio/hey-man-how-can-i-help-you.mp3'
  ];

  const playWelcomeAudio = () => {
    if (!audioEnabled) return;

    try {
      const randomIndex = Math.floor(Math.random() * welcomeAudioFiles.length);
      const selectedAudio = welcomeAudioFiles[randomIndex];

      const audio = new Audio(selectedAudio);
      audio.volume = 0.7;
      audio.play().catch(error => {
        console.log('Audio autoplay prevented by browser:', error);
      });
    } catch (error) {
      console.error('Error playing welcome audio:', error);
    }
  };

  // Keep track of current audio to prevent overlapping
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);

  const playResponseAudio = (responseType: 'thinking' | 'found' | 'error' | 'goodbye') => {
    if (!audioEnabled) return;

    // Stop any currently playing audio
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current.currentTime = 0;
    }

    const audioVariations = {
      thinking: [
        '/audio/no-worries-dude-ill-get-right-on-that.mp3',
        '/audio/no-worries-dude-ill-get-right-on-that-man-but-first-let-me-grab-a-little-snack-cause-you-know-cant-work-on-an-empty-stomach-righ.mp3'
      ],
      found: ['/audio/hey-man-this-is-what-youre-looking-fo.mp3'],
      error: ['/audio/sorry-man-let-me-try-that-again.mp3'],
      goodbye: [
        '/audio/catch-you-later-man-happy-shopping.mp3',
        '/audio/catch-you-later-man-happy-shopping-and-dont-forget-to-snag-some-munchies-for-the-road-ese.mp3'
      ]
    };

    try {
      const variations = audioVariations[responseType];
      if (variations && variations.length > 0) {
        const randomIndex = Math.floor(Math.random() * variations.length);
        const selectedAudio = variations[randomIndex];

        const audio = new Audio(selectedAudio);
        audio.volume = 0.6;
        currentAudioRef.current = audio;

        // Clear reference when audio ends
        audio.addEventListener('ended', () => {
          currentAudioRef.current = null;
        });

        audio.play().catch(error => {
          console.log('Response audio blocked by browser:', error);
          currentAudioRef.current = null;
        });
      }
    } catch (error) {
      console.error('Error playing response audio:', error);
      currentAudioRef.current = null;
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsTyping(true);

    // Play thinking audio
    playResponseAudio('thinking');

    try {
      let responseContent = '';

      if (aiService) {
        // Use real AI service
        const aiRequest = {
          type: 'chat_response',
          content: `You are Cheech, a knowledgeable and friendly chatbot for BitsnBongs, a cannabis and CBD product retailer.
          User question: ${currentInput}

          Respond in a friendly, laid-back tone. Be helpful and knowledgeable about cannabis/CBD products. Keep responses conversational and under 150 words.`,
          context: {
            business_type: 'cannabis',
            brand_voice: {
              tone: 'friendly',
              personality: 'knowledgeable and helpful product expert'
            },
            max_length: 150,
            format: 'plain'
          },
          provider: 'auto',
          complexity: 'medium',
          urgency: 'medium'
        };

        const aiResponse = await aiService.processRequest(aiRequest);
        responseContent = aiResponse.success ? aiResponse.content : getFallbackResponse(currentInput);
      } else {
        // Fallback response
        responseContent = getFallbackResponse(currentInput);
      }

      const botResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: responseContent,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);

      // Play appropriate response audio (with delay to avoid overlap)
      setTimeout(() => {
        if (currentInput.toLowerCase().includes('bye') || currentInput.toLowerCase().includes('thanks')) {
          playResponseAudio('goodbye');
        } else {
          playResponseAudio('found');
        }
      }, 1000); // Wait 1 second to let thinking audio finish

    } catch (error) {
      console.error('Error getting AI response:', error);

      // Play error audio with delay
      setTimeout(() => {
        playResponseAudio('error');
      }, 500);

      const errorResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "Sorry man, let me try that again. I'm having a bit of trouble right now, but I'm here to help!",
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const getFallbackResponse = (input: string): string => {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('product') || lowerInput.includes('buy') || lowerInput.includes('shop')) {
      return "Dude, we've got some amazing products! Check out our bongs, vaporizers, CBD oils, and accessories. What kind of vibe are you going for?";
    } else if (lowerInput.includes('cbd') || lowerInput.includes('oil')) {
      return "CBD is awesome, man! We've got oils, gummies, topicals - all the good stuff. Perfect for chilling out and staying mellow.";
    } else if (lowerInput.includes('bong') || lowerInput.includes('pipe')) {
      return "Now we're talking! Our glass collection is fire - from simple pieces to artistic masterpieces. What's your style?";
    } else if (lowerInput.includes('help') || lowerInput.includes('question')) {
      return "No worries dude, I'm here to help! Ask me about products, CBD info, or anything else. What's on your mind?";
    } else {
      return "Right on! I'm here to help you find exactly what you need. Feel free to ask about our products or anything cannabis-related!";
    }
  };

  return (
    <div className="flex flex-col h-full max-h-96">
      {/* Cheech Header */}
      {isInitialized && (
        <div className="flex items-center justify-between p-2 bg-sage-100 rounded-t-lg border-b border-sage-200 mb-2">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-sage-600 rounded-full flex items-center justify-center text-white font-bold mr-2">
              🌿
            </div>
            <span className="font-medium text-sage-800">Cheech</span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={playWelcomeAudio}
              className="text-sage-600 hover:text-sage-700 text-sm"
              title="Play welcome message"
            >
              🔊
            </button>
            <button
              onClick={() => setAudioEnabled(!audioEnabled)}
              className={`text-sm ${audioEnabled ? 'text-sage-600' : 'text-gray-400'}`}
              title={audioEnabled ? 'Disable audio' : 'Enable audio'}
            >
              {audioEnabled ? '🔈' : '🔇'}
            </button>
          </div>
        </div>
      )}

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto mb-4 space-y-4">
        {messages.map(message => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-4 py-2 ${
                message.isUser
                  ? 'bg-sage-600 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              {!message.isUser && (
                <div className="flex items-center mb-1">
                  <span className="text-xs font-medium text-sage-600">Cheech</span>
                </div>
              )}
              <p>{message.content}</p>
              <p className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})}
              </p>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-800 rounded-lg px-4 py-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input area */}
      <div className="flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Type your question..."
          className="flex-1"
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <Button
          onClick={handleSendMessage}
          disabled={!inputValue.trim()}
          className="bg-sage-600 hover:bg-sage-700"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default ChatInterface;
