'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PaymentMethodSelector, PaymentMethod } from '@/components/checkout/PaymentMethodSelector';
import { PayPalCheckout } from '@/components/checkout/PayPalCheckout';
import { StripeCheckout } from '@/components/checkout/StripeCheckout';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { ArrowLeft } from 'lucide-react';

// Define schema for credit card form
const cardSchema = z.object({
  cardNumber: z.string()
    .min(16, 'Card number must be at least 16 digits')
    .max(19, 'Card number must be at most 19 digits')
    .regex(/^[0-9\s-]+$/, 'Card number must contain only digits, spaces, or hyphens'),
  cardholderName: z.string().min(3, 'Cardholder name is required'),
  expiryDate: z.string()
    .regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/, 'Expiry date must be in MM/YY format'),
  cvv: z.string()
    .min(3, 'CVV must be at least 3 digits')
    .max(4, 'CVV must be at most 4 digits')
    .regex(/^[0-9]+$/, 'CVV must contain only digits'),
});

type CardFormValues = z.infer<typeof cardSchema>;

interface PaymentStepProps {
  paymentMethods: PaymentMethod[];
  selectedPaymentMethodId: string;
  onPaymentMethodSelect: (methodId: string) => void;
  onContinue: () => void;
  onBack: () => void;
  shippingCost: number;
  taxAmount: number;
}

export function PaymentStep({
  paymentMethods,
  selectedPaymentMethodId,
  onPaymentMethodSelect,
  onContinue,
  onBack,
  shippingCost,
  taxAmount,
}: PaymentStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form for credit card
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CardFormValues>({
    resolver: zodResolver(cardSchema),
    defaultValues: {
      cardNumber: '',
      cardholderName: '',
      expiryDate: '',
      cvv: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: CardFormValues) => {
    setIsSubmitting(true);
    try {
      // In a real implementation, this would validate the card details
      // For now, we'll just simulate a successful validation
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Continue to next step
      onContinue();
    } catch (error) {
      console.error('Error validating payment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Payment Method</CardTitle>
        </CardHeader>
        <CardContent>
          <PaymentMethodSelector
            methods={paymentMethods}
            selectedMethodId={selectedPaymentMethodId}
            onSelect={onPaymentMethodSelect}
          />

          {selectedPaymentMethodId === 'paypal' ? (
            <div className="mt-6">
              <PayPalCheckout
                shippingCost={shippingCost}
                taxAmount={taxAmount}
                onSuccess={(orderId) => {
                  console.log('PayPal payment successful, order ID:', orderId);
                  onContinue();
                }}
                onCancel={() => {
                  console.log('PayPal payment cancelled');
                }}
              />
            </div>
          ) : selectedPaymentMethodId === 'stripe' ? (
            <div className="mt-6">
              <StripeCheckout
                shippingCost={shippingCost}
                taxAmount={taxAmount}
                onSuccess={(orderId) => {
                  console.log('Stripe payment successful, order ID:', orderId);
                  onContinue();
                }}
                onCancel={() => {
                  console.log('Stripe payment cancelled');
                }}
              />
            </div>
          ) : selectedPaymentMethodId === 'card' && (
            <form onSubmit={handleSubmit(onSubmit)} className="mt-6 space-y-4">
              <div>
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  {...register('cardNumber')}
                />
                {errors.cardNumber && (
                  <p className="text-sm text-red-500 mt-1">{errors.cardNumber.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="cardholderName">Cardholder Name</Label>
                <Input
                  id="cardholderName"
                  placeholder="John Doe"
                  {...register('cardholderName')}
                />
                {errors.cardholderName && (
                  <p className="text-sm text-red-500 mt-1">{errors.cardholderName.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expiryDate">Expiry Date</Label>
                  <Input
                    id="expiryDate"
                    placeholder="MM/YY"
                    {...register('expiryDate')}
                  />
                  {errors.expiryDate && (
                    <p className="text-sm text-red-500 mt-1">{errors.expiryDate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    type="password"
                    {...register('cvv')}
                  />
                  {errors.cvv && (
                    <p className="text-sm text-red-500 mt-1">{errors.cvv.message}</p>
                  )}
                </div>
              </div>
            </form>
          )}

          {(selectedPaymentMethodId === 'paypal' || selectedPaymentMethodId === 'stripe') && (
            <div className="mt-6 p-4 bg-blue-50 rounded-md">
              <p className="text-center text-gray-700">
                You will be redirected to PayPal to complete your payment after reviewing your order.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-between mt-6">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Shipping
        </Button>
        
        {selectedPaymentMethodId === 'card' ? (
          <Button 
            type="submit" 
            onClick={handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            Continue to Review
          </Button>
        ) : (
          <Button onClick={onContinue}>
            Continue to Review
          </Button>
        )}
      </div>
    </>
  );
}
