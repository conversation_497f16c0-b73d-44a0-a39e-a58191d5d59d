// Script to calculate option pricing adjustments based on percentage of base price
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Configure dotenv
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Log the Supabase connection details (without the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'undefined';
console.log(`Connecting to Supabase at: ${supabaseUrl} with key: ${maskedKey}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase URL or key is missing. Make sure your .env file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const DRY_RUN = process.argv.includes('--dry-run');
const DEFAULT_ADJUSTMENT_PERCENTAGE = 10; // Default 10% price increase for options

// Parse command line arguments for custom percentage
let adjustmentPercentage = DEFAULT_ADJUSTMENT_PERCENTAGE;
const percentageArg = process.argv.find(arg => arg.startsWith('--percentage='));
if (percentageArg) {
  const percentage = parseFloat(percentageArg.split('=')[1]);
  if (!isNaN(percentage)) {
    adjustmentPercentage = percentage;
  }
}

// Main function
async function calculateOptionPricing() {
  console.log('Starting option pricing calculation...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);
  console.log(`Using adjustment percentage: ${adjustmentPercentage}%`);

  try {
    // 1. Fetch products from the database with options
    console.log('Fetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('option_name1', 'is', null)
      .not('option_values1', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts?.length || 0} products in database with options`);

    if (!dbProducts || dbProducts.length === 0) {
      console.log('No products found with options in the database');
      return;
    }

    // 2. Process each product with options
    const productsToUpdate = [];
    const productsWithIssues = [];

    for (const dbProduct of dbProducts) {
      // Skip products without options
      if (!dbProduct.option_values1 || !dbProduct.option_name1) continue;

      // Get option values as an array
      const optionValues = dbProduct.option_values1.split(';').map(v => v.trim());
      if (optionValues.length === 0) continue;

      // Get the base price
      const basePrice = dbProduct.price || 0;
      if (basePrice <= 0) {
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Invalid base price: ${basePrice}`,
        });
        continue;
      }

      try {
        // Calculate price adjustments based on percentage of base price
        // First option is the base (0 adjustment), others get percentage increase
        const priceAdjustments = [0]; // First option has no adjustment
        
        // Calculate adjustments for remaining options (if any)
        for (let i = 1; i < optionValues.length; i++) {
          const adjustment = (basePrice * adjustmentPercentage) / 100;
          priceAdjustments.push(adjustment);
        }

        // Format price adjustments as semicolon-separated string
        const priceAdjustmentStr = priceAdjustments.join(';');

        // Add to update list
        productsToUpdate.push({
          id: dbProduct.id,
          name: dbProduct.name,
          option_price_adjustment1: priceAdjustmentStr,
          option_values: optionValues.join(', '),
          base_price: basePrice
        });

        console.log(`Product: ${dbProduct.name}`);
        console.log(`  Option values: ${optionValues.join(', ')}`);
        console.log(`  Price adjustments: ${priceAdjustmentStr}`);
      } catch (err) {
        console.error(`Error processing product ${dbProduct.name}:`, err);
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Error: ${err.message}`,
        });
      }
    }

    // 3. Update products in the database (if not dry run)
    if (productsToUpdate.length > 0) {
      console.log(`\nReady to update ${productsToUpdate.length} products with price adjustments`);
      
      if (!DRY_RUN) {
        console.log('Updating products in database...');
        
        // Update products in batches to avoid hitting API limits
        const BATCH_SIZE = 50;
        for (let i = 0; i < productsToUpdate.length; i += BATCH_SIZE) {
          const batch = productsToUpdate.slice(i, i + BATCH_SIZE);
          
          // Update each product individually to avoid issues
          for (const product of batch) {
            const { error } = await supabase
              .from('products')
              .update({ option_price_adjustment1: product.option_price_adjustment1 })
              .eq('id', product.id);
              
            if (error) {
              console.error(`Error updating product ${product.id}:`, error);
              productsWithIssues.push({
                id: product.id,
                name: product.name,
                issue: `Update error: ${error.message}`,
              });
            }
          }
          
          console.log(`Updated batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(productsToUpdate.length / BATCH_SIZE)}`);
        }
        
        console.log('Update completed successfully!');
      } else {
        console.log('DRY RUN: No changes were made to the database');
        console.log('\nSample of products that would be updated:');
        for (let i = 0; i < Math.min(10, productsToUpdate.length); i++) {
          const product = productsToUpdate[i];
          console.log(`- ${product.name} (£${product.base_price}):`);
          console.log(`  Options: ${product.option_values}`);
          console.log(`  Adjustments: ${product.option_price_adjustment1}`);
        }
      }
    } else {
      console.log('No products to update');
    }

    // 4. Report issues
    if (productsWithIssues.length > 0) {
      console.log(`\n${productsWithIssues.length} products had issues:`);
      productsWithIssues.forEach(p => {
        console.log(`- ${p.name} (${p.id}): ${p.issue}`);
      });
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
calculateOptionPricing().catch(console.error);
