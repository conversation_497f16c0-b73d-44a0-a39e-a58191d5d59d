/**
 * This script enhances the Select components to ensure they work correctly
 * It uses direct DOM manipulation to fix dropdown interaction issues
 */

export function enhanceDropdowns() {
  // Wait for the DOM to be fully loaded
  setTimeout(() => {
    console.log('Enhancing dropdowns...');
    
    // Find all SelectTrigger elements
    const selectTriggers = document.querySelectorAll('[data-radix-select-trigger]');
    
    selectTriggers.forEach(trigger => {
      // Get the ID of the trigger
      const id = trigger.getAttribute('id');
      console.log(`Found select trigger: ${id}`);
      
      // Make sure it's clickable
      if (trigger instanceof HTMLElement) {
        // Add a click handler to ensure the dropdown opens
        trigger.addEventListener('click', (e) => {
          console.log(`Clicked select trigger: ${id}`);
          
          // Find the associated value element
          const valueElement = trigger.querySelector('[data-radix-select-value-label]');
          if (valueElement) {
            console.log(`Found value element for ${id}`);
          }
          
          // Force the select to be focusable
          trigger.setAttribute('tabindex', '0');
          trigger.focus();
        });
      }
    });
    
    // Find all SelectItem elements
    const selectItems = document.querySelectorAll('[data-radix-select-item]');
    
    selectItems.forEach(item => {
      if (item instanceof HTMLElement) {
        // Add a click handler to ensure the item is selectable
        item.addEventListener('click', (e) => {
          console.log(`Clicked select item: ${item.textContent}`);
          
          // Get the value of the item
          const value = item.getAttribute('data-value');
          console.log(`Item value: ${value}`);
          
          // Find the parent select content
          const selectContent = item.closest('[data-radix-select-content]');
          if (selectContent) {
            // Find the associated trigger
            const triggerId = selectContent.getAttribute('data-trigger-id');
            console.log(`Associated trigger ID: ${triggerId}`);
            
            // Find the trigger element
            const trigger = document.getElementById(triggerId || '');
            if (trigger) {
              // Dispatch a custom event to notify React
              const event = new CustomEvent('selectchange', { 
                detail: { 
                  id: triggerId, 
                  value: value 
                },
                bubbles: true
              });
              trigger.dispatchEvent(event);
            }
          }
        });
      }
    });
    
    console.log('Dropdown enhancement completed');
  }, 1000); // Wait 1 second for the DOM to be fully loaded
}
