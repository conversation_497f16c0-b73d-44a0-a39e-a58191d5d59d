import React from 'react';
import { Package } from 'lucide-react';

interface PackSizeSelectorProps {
  packSizes: string[];
  selectedPackSize: string | null;
  onSelectPackSize: (packSize: string) => void;
  disabledPackSizes?: string[];
}

export const PackSizeSelector: React.FC<PackSizeSelectorProps> = ({
  packSizes,
  selectedPackSize,
  onSelectPackSize,
  disabledPackSizes = [],
}) => {
  // Extract numeric value from pack size (e.g., "3 Pack" -> 3)
  const getPackSizeValue = (packSize: string): number => {
    const match = packSize.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : 1;
  };

  // Sort pack sizes by their numeric value
  const sortedPackSizes = [...packSizes].sort((a, b) => {
    return getPackSizeValue(a) - getPackSizeValue(b);
  });

  // Generate a visual representation based on pack size
  const getPackSizeVisual = (packSize: string): React.ReactNode => {
    const value = getPackSizeValue(packSize);

    // For very large pack sizes, just show the number
    if (value > 10) {
      return <span className="font-bold">{value}</span>;
    }

    // For smaller pack sizes, show dots
    return (
      <div className="flex justify-center">
        {Array.from({ length: Math.min(value, 10) }).map((_, i) => (
          <span
            key={i}
            className="w-1.5 h-1.5 rounded-full bg-current mx-0.5"
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700 flex items-center gap-1">
        <Package className="h-4 w-4" />
        Pack Size
      </label>

      <div className="flex flex-wrap gap-2">
        {sortedPackSizes.map((packSize) => {
          const isDisabled = disabledPackSizes.includes(packSize);
          const isSelected = selectedPackSize === packSize;

          return (
            <button
              key={packSize}
              type="button"
              style={{
                padding: '8px 12px',
                borderRadius: '6px',
                fontWeight: '500',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                backgroundColor: isSelected ? '#4CAF50' : 'white',
                color: isSelected ? 'white' : '#333',
                border: isSelected ? 'none' : '1px solid #ddd',
                opacity: isDisabled ? 0.5 : 1,
                position: 'relative',
                zIndex: 100,
                boxShadow: isSelected ? '0 2px 8px rgba(76, 175, 80, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.2s ease'
              }}
              disabled={isDisabled}
              onClick={(e) => {
                e.stopPropagation();
                onSelectPackSize(packSize);
              }}
            >
              <div className="flex flex-col items-center gap-1">
                <span>{packSize}</span>
                <div className="text-xs">
                  {getPackSizeVisual(packSize)}
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {selectedPackSize && (
        <p className="text-sm text-gray-600 mt-1">
          Selected: <span className="font-medium">{selectedPackSize}</span>
        </p>
      )}
    </div>
  );
};
