/**
 * Image Scraping Integration Layer
 * 
 * "There is no spoon... only perfect images!" 🥄
 * 
 * Integration layer for Agent 2's MCP Playwright image scraping system
 * Provides clean interface for the admin to process 1000+ products
 */

import { ImageScrapingService } from '../tools/image-scraping/ImageScrapingService';

export interface ImageScrapingOptions {
  product_id: string;
  product_name: string;
  category: string;
  max_images?: number;
  min_quality?: number;
  preferred_sources?: string[];
}

export interface ImageScrapingResult {
  product_id: string;
  images: Array<{
    url: string;
    alt: string;
    quality_score: number;
    source: string;
    file_size?: number;
    dimensions?: { width: number; height: number };
  }>;
  total_found: number;
  processing_time: number;
  cost_savings: number; // vs Google Image Search
  success: boolean;
  error?: string;
}

export interface BulkImageScrapingOptions {
  products: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  batch_size?: number;
  delay_between_batches?: number;
  max_images_per_product?: number;
  min_quality_threshold?: number;
}

export interface BulkImageScrapingResult {
  total_products: number;
  successful_products: number;
  failed_products: number;
  total_images_found: number;
  average_images_per_product: number;
  total_processing_time: number;
  total_cost_savings: number;
  success_rate: number;
  detailed_results: ImageScrapingResult[];
  summary: {
    by_category: { [category: string]: { success: number; failed: number } };
    by_source: { [source: string]: number };
    quality_distribution: { [range: string]: number };
  };
}

export interface ImageScrapingSystemStatus {
  service_available: boolean;
  mcp_connection_status: 'connected' | 'disconnected' | 'error';
  available_sources: number;
  estimated_processing_speed: string; // e.g., "500 products/hour"
  cost_comparison: {
    google_image_search: string;
    our_system: string;
    savings_per_product: string;
  };
  recommendations: string[];
}

export class ImageScrapingIntegration {
  private static instance: ImageScrapingIntegration;
  private imageScrapingService: ImageScrapingService;
  
  private constructor() {
    this.imageScrapingService = new ImageScrapingService();
  }
  
  static getInstance(): ImageScrapingIntegration {
    if (!ImageScrapingIntegration.instance) {
      ImageScrapingIntegration.instance = new ImageScrapingIntegration();
    }
    return ImageScrapingIntegration.instance;
  }

  /**
   * Scrape images for a single product
   */
  async scrapeProductImages(options: ImageScrapingOptions): Promise<ImageScrapingResult> {
    const startTime = Date.now();
    
    try {
      // Use Agent 2's image scraping service
      const images = await this.imageScrapingService.findProductImages(
        options.product_name,
        options.category,
        options.max_images || 10
      );

      // Calculate cost savings vs Google Image Search
      const costSavings = this.calculateCostSavings(images.length);
      
      return {
        product_id: options.product_id,
        images: images.map(img => ({
          url: img.url,
          alt: img.alt || `${options.product_name} - Premium Quality`,
          quality_score: img.quality_score,
          source: img.source,
          file_size: img.file_size,
          dimensions: img.dimensions
        })),
        total_found: images.length,
        processing_time: Date.now() - startTime,
        cost_savings: costSavings,
        success: true
      };
      
    } catch (error) {
      console.error(`Image scraping failed for product ${options.product_id}:`, error);
      
      return {
        product_id: options.product_id,
        images: [],
        total_found: 0,
        processing_time: Date.now() - startTime,
        cost_savings: 0,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Bulk process multiple products (1000+ products capability)
   */
  async bulkScrapeProducts(options: BulkImageScrapingOptions): Promise<BulkImageScrapingResult> {
    const startTime = Date.now();
    const results: ImageScrapingResult[] = [];
    
    try {
      // Use Agent 2's bulk processing service
      const bulkResult = await this.imageScrapingService.bulkProcessProducts(
        options.products.map(p => ({
          name: p.name,
          category: p.category,
          max_images: options.max_images_per_product || 10
        })),
        {
          batch_size: options.batch_size || 5,
          delay_between_batches: options.delay_between_batches || 1000,
          min_quality: options.min_quality_threshold || 0.7
        }
      );

      // Process results into our format
      for (let i = 0; i < options.products.length; i++) {
        const product = options.products[i];
        const productResult = bulkResult.results[i];
        
        if (productResult && productResult.success) {
          results.push({
            product_id: product.id,
            images: productResult.images.map(img => ({
              url: img.url,
              alt: img.alt || `${product.name} - Premium Quality`,
              quality_score: img.quality_score,
              source: img.source,
              file_size: img.file_size,
              dimensions: img.dimensions
            })),
            total_found: productResult.images.length,
            processing_time: productResult.processing_time || 0,
            cost_savings: this.calculateCostSavings(productResult.images.length),
            success: true
          });
        } else {
          results.push({
            product_id: product.id,
            images: [],
            total_found: 0,
            processing_time: 0,
            cost_savings: 0,
            success: false,
            error: productResult?.error || 'Unknown error'
          });
        }
      }

      // Calculate summary statistics
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);
      const totalImages = successful.reduce((sum, r) => sum + r.total_found, 0);
      const totalCostSavings = successful.reduce((sum, r) => sum + r.cost_savings, 0);

      // Generate category and source summaries
      const byCategory: { [category: string]: { success: number; failed: number } } = {};
      const bySource: { [source: string]: number } = {};
      const qualityDistribution: { [range: string]: number } = {
        'High (0.8-1.0)': 0,
        'Medium (0.6-0.8)': 0,
        'Low (0.4-0.6)': 0,
        'Poor (0.0-0.4)': 0
      };

      options.products.forEach((product, index) => {
        const category = product.category;
        if (!byCategory[category]) {
          byCategory[category] = { success: 0, failed: 0 };
        }
        
        if (results[index].success) {
          byCategory[category].success++;
          
          // Count images by source and quality
          results[index].images.forEach(img => {
            bySource[img.source] = (bySource[img.source] || 0) + 1;
            
            if (img.quality_score >= 0.8) qualityDistribution['High (0.8-1.0)']++;
            else if (img.quality_score >= 0.6) qualityDistribution['Medium (0.6-0.8)']++;
            else if (img.quality_score >= 0.4) qualityDistribution['Low (0.4-0.6)']++;
            else qualityDistribution['Poor (0.0-0.4)']++;
          });
        } else {
          byCategory[category].failed++;
        }
      });

      return {
        total_products: options.products.length,
        successful_products: successful.length,
        failed_products: failed.length,
        total_images_found: totalImages,
        average_images_per_product: successful.length > 0 ? totalImages / successful.length : 0,
        total_processing_time: Date.now() - startTime,
        total_cost_savings: totalCostSavings,
        success_rate: (successful.length / options.products.length) * 100,
        detailed_results: results,
        summary: {
          by_category: byCategory,
          by_source: bySource,
          quality_distribution: qualityDistribution
        }
      };
      
    } catch (error) {
      console.error('Bulk image scraping failed:', error);
      throw new Error(`Bulk image scraping failed: ${error.message}`);
    }
  }

  /**
   * Get system status and capabilities
   */
  async getSystemStatus(): Promise<ImageScrapingSystemStatus> {
    try {
      const healthStatus = await this.imageScrapingService.checkHealth();
      const availableSources = await this.imageScrapingService.getAvailableSources();
      
      return {
        service_available: healthStatus.status === 'healthy',
        mcp_connection_status: healthStatus.status === 'healthy' ? 'connected' : 'error',
        available_sources: availableSources.length,
        estimated_processing_speed: '500 products/hour',
        cost_comparison: {
          google_image_search: '£0.005 per image',
          our_system: '£0.000 per image',
          savings_per_product: '£0.025-0.050 per product'
        },
        recommendations: [
          'Process products in batches of 5-10 for optimal performance',
          'Use category-specific sources for better relevance',
          'Set minimum quality threshold to 0.7 for best results',
          'Schedule bulk processing during off-peak hours'
        ]
      };
      
    } catch (error) {
      return {
        service_available: false,
        mcp_connection_status: 'error',
        available_sources: 0,
        estimated_processing_speed: 'Service unavailable',
        cost_comparison: {
          google_image_search: '£0.005 per image',
          our_system: 'Service unavailable',
          savings_per_product: 'Service unavailable'
        },
        recommendations: [
          'Check MCP Playwright connection',
          'Verify image scraping service configuration',
          'Contact system administrator if issues persist'
        ]
      };
    }
  }

  /**
   * Validate image quality for a specific image
   */
  async validateImageQuality(imageUrl: string, productName: string): Promise<{
    quality_score: number;
    relevance_score: number;
    technical_score: number;
    recommendations: string[];
  }> {
    try {
      const validation = await this.imageScrapingService.validateImageQuality(imageUrl, productName);
      
      return {
        quality_score: validation.overall_score,
        relevance_score: validation.relevance_score,
        technical_score: validation.technical_score,
        recommendations: validation.recommendations
      };
      
    } catch (error) {
      console.error('Image quality validation failed:', error);
      return {
        quality_score: 0,
        relevance_score: 0,
        technical_score: 0,
        recommendations: ['Unable to validate image quality', 'Check image URL and try again']
      };
    }
  }

  /**
   * Get available retailer sources for a category
   */
  async getAvailableSources(category?: string): Promise<Array<{
    name: string;
    url: string;
    reliability_score: number;
    categories: string[];
    last_updated: string;
  }>> {
    try {
      const sources = await this.imageScrapingService.getAvailableSources(category);
      
      return sources.map(source => ({
        name: source.name,
        url: source.url,
        reliability_score: source.reliability_score,
        categories: source.categories,
        last_updated: source.last_updated || new Date().toISOString()
      }));
      
    } catch (error) {
      console.error('Failed to get available sources:', error);
      return [];
    }
  }

  /**
   * Calculate cost savings vs Google Image Search
   */
  private calculateCostSavings(imageCount: number): number {
    // Google Image Search costs approximately £0.005 per image
    // Our system costs £0.000 per image
    const googleCost = imageCount * 0.005;
    const ourCost = 0;
    return googleCost - ourCost;
  }
}

// Export singleton instance
export const imageScrapingIntegration = ImageScrapingIntegration.getInstance();
