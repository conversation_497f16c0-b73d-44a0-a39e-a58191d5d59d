// <PERSON>ript to transpile and run the TypeScript fix script
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import * as dotenv from 'dotenv';

// Set up environment variables
dotenv.config();

// Create a require function
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Register ts-node
require('ts-node').register();

// Import the fix script
import './src/scripts/fix-imported-products.ts';

