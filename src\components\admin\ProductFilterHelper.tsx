import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';

interface Product {
  id: string;
  name: string;
  category_id: string | null;
  subcategory_id: string | null;
  [key: string]: any;
}

const ProductFilterHelper: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [unassignedProducts, setUnassignedProducts] = useState<Product[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [hideAssigned, setHideAssigned] = useState(true);
  
  const findUnassignedProducts = async () => {
    setIsLoading(true);
    try {
      // Fetch products without categories assigned
      const { data, error } = await supabase
        .from('products')
        .select('id, name, category_id, subcategory_id')
        .is('category_id', null)
        .limit(100);
        
      if (error) {
        console.error('Error fetching unassigned products:', error);
        return;
      }
      
      setUnassignedProducts(data || []);
      setShowResults(true);
    } catch (err) {
      console.error('Error in findUnassignedProducts:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="hide-assigned" 
              checked={hideAssigned}
              onCheckedChange={(checked) => setHideAssigned(checked === true)}
            />
            <label 
              htmlFor="hide-assigned"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
            >
              Only show products without categories
            </label>
          </div>
          
          <Button 
            onClick={findUnassignedProducts}
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Find Unassigned Products'}
          </Button>
          
          {showResults && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Unassigned Products ({unassignedProducts.length})</h3>
              {unassignedProducts.length > 0 ? (
                <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                  <ul className="space-y-1">
                    {unassignedProducts.map(product => (
                      <li key={product.id} className="text-sm">
                        {product.name}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p>No unassigned products found.</p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductFilterHelper;
