
import { useRef, useEffect, useState, ReactNode } from 'react';

interface AnimatedOnScrollProps {
  children: ReactNode;
  animation: 'fade-up' | 'fade-in' | 'slide-right' | 'slide-left' | 'zoom-in' | 'bounce';
  delay?: number;
  threshold?: number;
  className?: string;
}

const AnimatedOnScroll = ({ 
  children, 
  animation, 
  delay = 0,
  threshold = 0.1,
  className = '' 
}: AnimatedOnScrollProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Once it's visible, unobserve
          if (elementRef.current) observer.unobserve(elementRef.current);
        }
      },
      { 
        threshold: threshold,
        rootMargin: '0px 0px -100px 0px' 
      }
    );
    
    if (elementRef.current) {
      observer.observe(elementRef.current);
    }
    
    return () => {
      if (elementRef.current) observer.unobserve(elementRef.current);
    };
  }, [threshold]);
  
  // Animation classes
  const animationClasses = {
    'fade-up': 'opacity-0 translate-y-10 transition-all duration-700 ease-out',
    'fade-in': 'opacity-0 transition-opacity duration-700 ease-out',
    'slide-right': 'opacity-0 -translate-x-10 transition-all duration-700 ease-out',
    'slide-left': 'opacity-0 translate-x-10 transition-all duration-700 ease-out',
    'zoom-in': 'opacity-0 scale-95 transition-all duration-700 ease-out',
    'bounce': 'opacity-0 transition-all duration-700 ease-out'
  };
  
  const visibleClasses = {
    'fade-up': 'opacity-100 translate-y-0',
    'fade-in': 'opacity-100',
    'slide-right': 'opacity-100 translate-x-0',
    'slide-left': 'opacity-100 translate-x-0',
    'zoom-in': 'opacity-100 scale-100',
    'bounce': 'opacity-100 animate-bounce'
  };
  
  const baseClass = animationClasses[animation];
  const visibleClass = isVisible ? visibleClasses[animation] : '';
  
  const inlineStyle = {
    transitionDelay: `${delay}ms`
  };
  
  return (
    <div 
      ref={elementRef} 
      className={`${baseClass} ${visibleClass} ${className}`}
      style={inlineStyle}
    >
      {children}
    </div>
  );
};

export default AnimatedOnScroll;
