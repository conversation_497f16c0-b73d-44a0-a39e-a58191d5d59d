/**
 * Blog Category Management Component
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  fetchBlogCategories, 
  createBlogCategory, 
  updateBlogCategory, 
  deleteBlogCategory,
  getCategoryUsageStats,
  generateUniqueSlug,
  type CreateCategoryData,
  type UpdateCategoryData
} from '@/api/blogCategories';
import { BlogCategory } from '@/types/database';

interface EditingCategory {
  id?: string;
  name: string;
  slug: string;
  description: string;
}

const CategoryManager = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [isCreating, setIsCreating] = useState(false);
  const [editingCategory, setEditingCategory] = useState<EditingCategory | null>(null);
  const [newCategory, setNewCategory] = useState<EditingCategory>({
    name: '',
    slug: '',
    description: ''
  });

  // Fetch categories
  const { data: categories = [], isLoading: categoriesLoading } = useQuery({
    queryKey: ['blog_categories'],
    queryFn: fetchBlogCategories
  });

  // Fetch usage stats
  const { data: usageStats = [] } = useQuery({
    queryKey: ['category_usage_stats'],
    queryFn: getCategoryUsageStats
  });

  // Create category mutation
  const createMutation = useMutation({
    mutationFn: createBlogCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog_categories'] });
      queryClient.invalidateQueries({ queryKey: ['category_usage_stats'] });
      setIsCreating(false);
      setNewCategory({ name: '', slug: '', description: '' });
      toast({
        title: "Success",
        description: "Category created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create category",
        variant: "destructive"
      });
    }
  });

  // Update category mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCategoryData }) => 
      updateBlogCategory(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog_categories'] });
      setEditingCategory(null);
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update category",
        variant: "destructive"
      });
    }
  });

  // Delete category mutation
  const deleteMutation = useMutation({
    mutationFn: deleteBlogCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blog_categories'] });
      queryClient.invalidateQueries({ queryKey: ['category_usage_stats'] });
      toast({
        title: "Success",
        description: "Category deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete category",
        variant: "destructive"
      });
    }
  });

  // Auto-generate slug from name
  const handleNameChange = async (name: string, isNew: boolean = false) => {
    if (isNew) {
      const slug = await generateUniqueSlug(name);
      setNewCategory(prev => ({ ...prev, name, slug }));
    } else if (editingCategory) {
      const slug = await generateUniqueSlug(name, editingCategory.id);
      setEditingCategory(prev => prev ? { ...prev, name, slug } : null);
    }
  };

  const handleCreateCategory = () => {
    if (!newCategory.name.trim()) {
      toast({
        title: "Error",
        description: "Category name is required",
        variant: "destructive"
      });
      return;
    }

    createMutation.mutate({
      name: newCategory.name.trim(),
      slug: newCategory.slug.trim(),
      description: newCategory.description.trim() || undefined
    });
  };

  const handleUpdateCategory = () => {
    if (!editingCategory || !editingCategory.name.trim()) {
      toast({
        title: "Error",
        description: "Category name is required",
        variant: "destructive"
      });
      return;
    }

    updateMutation.mutate({
      id: editingCategory.id!,
      data: {
        name: editingCategory.name.trim(),
        slug: editingCategory.slug.trim(),
        description: editingCategory.description.trim() || undefined
      }
    });
  };

  const handleDeleteCategory = (category: BlogCategory) => {
    const usage = usageStats.find(stat => stat.category_id === category.id);
    if (usage && usage.blog_count > 0) {
      toast({
        title: "Cannot Delete",
        description: `This category is used by ${usage.blog_count} blog post(s). Please reassign or delete those posts first.`,
        variant: "destructive"
      });
      return;
    }

    if (confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
      deleteMutation.mutate(category.id);
    }
  };

  const startEditing = (category: BlogCategory) => {
    setEditingCategory({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description || ''
    });
  };

  const cancelEditing = () => {
    setEditingCategory(null);
  };

  const cancelCreating = () => {
    setIsCreating(false);
    setNewCategory({ name: '', slug: '', description: '' });
  };

  if (categoriesLoading) {
    return <div>Loading categories...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Blog Categories</h2>
        <Button 
          onClick={() => setIsCreating(true)} 
          disabled={isCreating}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Category
        </Button>
      </div>

      {/* Create New Category */}
      {isCreating && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Category</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Name *</label>
              <Input
                value={newCategory.name}
                onChange={(e) => handleNameChange(e.target.value, true)}
                placeholder="Category name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Slug</label>
              <Input
                value={newCategory.slug}
                onChange={(e) => setNewCategory(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="category-slug"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Description</label>
              <Textarea
                value={newCategory.description}
                onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Category description"
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handleCreateCategory}
                disabled={createMutation.isPending}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {createMutation.isPending ? 'Creating...' : 'Create'}
              </Button>
              <Button 
                variant="outline" 
                onClick={cancelCreating}
                disabled={createMutation.isPending}
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories List */}
      <div className="grid gap-4">
        {categories.map((category) => {
          const usage = usageStats.find(stat => stat.category_id === category.id);
          const isEditing = editingCategory?.id === category.id;

          return (
            <Card key={category.id}>
              <CardContent className="p-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Name *</label>
                      <Input
                        value={editingCategory.name}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Category name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Slug</label>
                      <Input
                        value={editingCategory.slug}
                        onChange={(e) => setEditingCategory(prev => prev ? { ...prev, slug: e.target.value } : null)}
                        placeholder="category-slug"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Description</label>
                      <Textarea
                        value={editingCategory.description}
                        onChange={(e) => setEditingCategory(prev => prev ? { ...prev, description: e.target.value } : null)}
                        placeholder="Category description"
                        rows={3}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        onClick={handleUpdateCategory}
                        disabled={updateMutation.isPending}
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Save className="h-4 w-4" />
                        {updateMutation.isPending ? 'Saving...' : 'Save'}
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={cancelEditing}
                        disabled={updateMutation.isPending}
                        size="sm"
                      >
                        <X className="h-4 w-4" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">{category.name}</h3>
                        <Badge variant="secondary">
                          {usage?.blog_count || 0} posts
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">
                        Slug: <code className="bg-gray-100 px-1 rounded">{category.slug}</code>
                      </p>
                      {category.description && (
                        <p className="text-sm text-gray-700">{category.description}</p>
                      )}
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing(category)}
                        className="flex items-center gap-1"
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCategory(category)}
                        disabled={deleteMutation.isPending}
                        className="flex items-center gap-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {categories.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No categories found. Create your first category to get started.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CategoryManager;
