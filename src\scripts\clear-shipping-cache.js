// Script to clear the shipping methods cache
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearShippingCache() {
  console.log('🧹 Clearing shipping methods cache...');
  
  try {
    // 1. Verify the shipping methods in the database
    const { data: methods, error } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (error) {
      console.error('Error fetching shipping methods:', error);
      return;
    }
    
    console.log(`Found ${methods.length} total shipping methods in the database`);
    
    // 2. Create a timestamp to force cache invalidation
    const timestamp = new Date().toISOString();
    
    // 3. Update a timestamp in the shipping_settings table if it exists
    const { data: settingsData, error: settingsError } = await supabase
      .from('settings')
      .select('*')
      .eq('key', 'shipping_cache_timestamp')
      .maybeSingle();
    
    if (settingsError && settingsError.code !== 'PGRST116') {
      console.error('Error checking settings:', settingsError);
    }
    
    if (settingsData) {
      // Update existing timestamp
      const { error: updateError } = await supabase
        .from('settings')
        .update({ value: timestamp })
        .eq('key', 'shipping_cache_timestamp');
        
      if (updateError) {
        console.error('Error updating cache timestamp:', updateError);
      } else {
        console.log('✅ Updated cache timestamp in settings table');
      }
    } else {
      // Create new timestamp entry
      const { error: insertError } = await supabase
        .from('settings')
        .insert({ key: 'shipping_cache_timestamp', value: timestamp });
        
      if (insertError) {
        console.error('Error creating cache timestamp:', insertError);
      } else {
        console.log('✅ Created new cache timestamp in settings table');
      }
    }
    
    // 4. Create a local file to force cache invalidation on the client
    const cacheFile = path.join(process.cwd(), 'public', 'shipping-cache.json');
    const cacheData = {
      timestamp,
      lastUpdated: new Date().toLocaleString(),
      message: 'This file is used to force cache invalidation for shipping methods'
    };
    
    fs.writeFileSync(cacheFile, JSON.stringify(cacheData, null, 2));
    console.log(`✅ Created cache invalidation file at ${cacheFile}`);
    
    console.log('\n✅ Cache clearing complete!');
    console.log('To ensure changes take effect:');
    console.log('1. Restart the development server');
    console.log('2. Clear your browser cache or use incognito mode');
    console.log('3. Check the checkout page to verify the shipping methods');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
clearShippingCache()
  .catch(err => {
    console.error('Error running script:', err);
  });
