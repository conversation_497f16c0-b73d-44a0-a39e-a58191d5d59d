import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { 
  Loader2, Store, ChevronDown, ShoppingBag, 
  Cigarette, Cannabis, Droplets, Pill, Sparkles, 
  Scissors, Scroll, Leaf, Wind
} from 'lucide-react';

interface Category {
  id: string;
  name: string;
  slug: string;
  parent_id?: string | null;
  subcategories?: Category[];
}

interface CategoryResponse {
  id: string;
  name: string;
  slug: string;
  parent_id: string | null;
  display_order?: number;
}

export const CategoryNavigation = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        // First, fetch all categories with display_order for proper sorting
        const { data, error } = await supabase
          .from('categories')
          .select('id, name, slug, parent_id, display_order')
          .order('display_order', { ascending: true });
        
        if (error) {
          throw error;
        }
        
        if (data) {
          console.log('Raw categories data:', data);
          
          // Safely cast the data to our CategoryResponse type
          const categories = data as unknown as CategoryResponse[];
          
          // Organize categories into a hierarchy
          // Main categories are those without a parent_id or with a null parent_id
          const mainCategories = categories.filter(cat => !cat.parent_id);
          // Subcategories are those with a non-null parent_id
          const subcategories = categories.filter(cat => cat.parent_id);
          
          console.log('Subcategories with display_order:', subcategories.map(s => ({ 
            name: s.name, 
            parent_id: s.parent_id, 
            display_order: s.display_order 
          })));
          
          console.log('Main categories:', mainCategories);
          console.log('Subcategories:', subcategories);
          
          // Add subcategories to their parent categories
          const categoriesWithSubs = mainCategories.map(mainCat => {
            // Get subcategories for this parent and sort them by display_order
            const subs = subcategories
              .filter(subCat => subCat.parent_id === mainCat.id)
              .sort((a, b) => {
                // Handle undefined display_order values
                const orderA = a.display_order || 999;
                const orderB = b.display_order || 999;
                return orderA - orderB;
              });
              
            return {
              ...mainCat,
              subcategories: subs.length > 0 ? subs : undefined
            } as Category;
          });
          
          console.log('Categories with subs:', categoriesWithSubs);
          setCategories(categoriesWithSubs);
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        setError(err.message || 'Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Map category names to icons
  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase();
    if (name.includes('bong') || name.includes('pipe')) return <Droplets className="h-4 w-4" />;
    if (name.includes('cbd')) return <Cannabis className="h-4 w-4" />;
    if (name.includes('rolling')) return <Scroll className="h-4 w-4" />;
    if (name.includes('paper')) return <Scroll className="h-4 w-4" />;
    if (name.includes('grinder')) return <Scissors className="h-4 w-4" />;
    if (name.includes('vaporiser') || name.includes('vape')) return <Wind className="h-4 w-4" />;
    if (name.includes('accessory') || name.includes('accessories')) return <Sparkles className="h-4 w-4" />;
    if (name.includes('clean')) return <Sparkles className="h-4 w-4" />;
    if (name.includes('seed')) return <Leaf className="h-4 w-4" />;
    return <ShoppingBag className="h-4 w-4" />;
  };

  // Handler to close the dropdown menu and scroll to top
  const handleCategoryClick = () => {
    setIsOpen(false);
    
    // Instant scroll to top
    window.scrollTo(0, 0);
  };
  
  // Handler for category navigation using standard React Router
  const handleCategoryNavigation = (categorySlug: string, subcategorySlug?: string) => {
    handleCategoryClick();
    
    // Log the navigation attempt for debugging
    console.log('Navigation attempt:', { categorySlug, subcategorySlug });
    
    // Find the category in our current data
    const category = categories.find(cat => cat.slug === categorySlug);
    if (!category) {
      console.warn(`Category ${categorySlug} not found. Redirecting to main shop.`);
      navigate('/shop');
      return;
    }
    
    console.log('Found category:', category.name, category.id);
    
    // Build the URL with proper parameters
    let url = '/shop';
    const params = new URLSearchParams();
    
    // Use the category slug for better URL readability
    params.set('category', categorySlug);
    
    // Create the full URL with parameters
    const queryString = params.toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    console.log('Navigating to URL with category slug:', fullUrl);
    
    // Use direct URL navigation to ensure a full page reload
    // This is more reliable than React Router state
    console.log('Using direct URL navigation with category ID:', category.id);
    
    // Add the category_id parameter to ensure direct filtering
    params.set('category_id', category.id);
    const fullUrlWithId = `${url}?${params.toString()}`;
    
    // Use window.location for a full page reload
    window.location.href = fullUrlWithId;
  };
  
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="nav-link hover:bg-primary/10 transition-colors flex items-center gap-1.5">
          <Store className="h-4 w-4" />
          <span>Shop</span>
          <ChevronDown className="h-3.5 w-3.5 ml-0.5 opacity-70" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="center" 
        className="w-72 p-2 rounded-xl shadow-lg border-none animate-in fade-in-80 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:zoom-out-95 max-h-[80vh] overflow-y-auto"
        sideOffset={8}
      >
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span>Loading categories...</span>
          </div>
        ) : error ? (
          <div className="p-4 text-red-500 text-center">
            <p>Failed to load categories</p>
          </div>
        ) : categories.length === 0 ? (
          <div className="p-4 text-center">
            <p>No categories found</p>
          </div>
        ) : (
          <div className="grid gap-1">
            <DropdownMenuItem className="rounded-lg hover:bg-primary/10 transition-colors">
              <button 
                onClick={() => navigate('/shop')} 
                className="w-full px-3 py-2 font-medium text-primary flex items-center gap-2 text-left"
              >
                <ShoppingBag className="h-4 w-4" />
                <span>All Products</span>
              </button>
            </DropdownMenuItem>
            
            <div className="h-px bg-gray-100 my-1"></div>
            
            {categories.map((category) => (
              <div key={category.id} className="mb-1 last:mb-0">
                <DropdownMenuItem className="rounded-lg hover:bg-primary/10 transition-colors dropdown-category-item">
                  <button 
                    onClick={() => handleCategoryNavigation(category.slug)}
                    className="w-full px-3 py-2 font-medium flex items-center gap-2 group text-left"
                  >
                    <span className="text-gray-600 group-hover:text-primary transition-colors">
                      {getCategoryIcon(category.name)}
                    </span>
                    <span>{category.name}</span>
                  </button>
                </DropdownMenuItem>
                
                {/* Show subcategories if available */}
                {category.subcategories && category.subcategories.length > 0 && (
                  <div className="pl-3 mt-1 grid gap-0.5">
                    {category.subcategories.map(subcat => (
                      <DropdownMenuItem key={subcat.id} className="rounded-lg hover:bg-gray-50 transition-colors dropdown-subcategory-item">
                        <button 
                          onClick={() => handleCategoryNavigation(category.slug, subcat.slug)}
                          className="w-full px-3 py-1.5 text-sm text-gray-700 hover:text-primary transition-colors flex items-center gap-2 group text-left"
                        >
                          <span className="text-gray-400 group-hover:text-primary transition-colors opacity-70">
                            {getCategoryIcon(subcat.name)}
                          </span>
                          <span>{subcat.name}</span>
                        </button>
                      </DropdownMenuItem>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CategoryNavigation;
