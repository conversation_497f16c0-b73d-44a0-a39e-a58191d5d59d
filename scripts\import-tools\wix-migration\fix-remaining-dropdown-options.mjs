// fix-remaining-dropdown-options.mjs
// <PERSON>ript to fix any remaining products with "DROP_DOWN" in their option_type fields

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixRemainingDropdownOptions() {
  console.log('Checking for products with "DROP_DOWN" in option_type fields...');
  
  // Get all products with "DROP_DOWN" in option_type fields
  const { data: products, error } = await supabase
    .from('products')
    .select('*')
    .or('option_type1.eq.DROP_DOWN,option_type2.eq.DROP_DOWN,option_type3.eq.DROP_DOWN');
  
  if (error) {
    console.error('Error fetching products:', error);
    return;
  }
  
  console.log(`Found ${products.length} products with "DROP_DOWN" in option_type fields.`);
  
  let updatedCount = 0;
  let skippedCount = 0;
  
  // Process each product
  for (const product of products) {
    console.log(`Processing product: ${product.name} (ID: ${product.id})`);
    
    const updates = {};
    let hasUpdates = false;
    
    // Check each option field
    for (let i = 1; i <= 3; i++) {
      const typeKey = `option_type${i}`;
      const descKey = `option_description${i}`;
      
      if (product[typeKey] === 'DROP_DOWN' && product[descKey]) {
        // Use the description as the option values
        updates[typeKey] = product[descKey];
        console.log(`Updating ${typeKey} for ${product.name} from 'DROP_DOWN' to: ${product[descKey]}`);
        hasUpdates = true;
      }
    }
    
    if (hasUpdates) {
      // Update the product in the database
      const { error: updateError } = await supabase
        .from('products')
        .update(updates)
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.name}:`, updateError);
        skippedCount++;
      } else {
        console.log(`Successfully updated options for: ${product.name}`);
        updatedCount++;
      }
    } else {
      console.log(`No updates needed for: ${product.name}`);
      skippedCount++;
    }
  }
  
  console.log('\nUpdate complete!');
  console.log(`Updated ${updatedCount} products with options.`);
  console.log(`Skipped ${skippedCount} products that couldn't be updated.`);
}

// Run the script
fixRemainingDropdownOptions().catch(error => {
  console.error('Error running script:', error);
});
