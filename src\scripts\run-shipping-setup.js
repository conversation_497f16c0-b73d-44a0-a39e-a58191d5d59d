// Simple Node.js script to run the shipping setup
// Run with: node src/scripts/run-shipping-setup.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// You'll need to replace these with your actual Supabase credentials
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'your-supabase-url';
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function setupShippingTables() {
  console.log('🚀 Setting up shipping tables...');

  try {
    // Create shipping zones table
    console.log('📦 Creating shipping_zones table...');
    const { error: zonesError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS shipping_zones (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          countries JSONB NOT NULL DEFAULT '[]'::jsonb,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_shipping_zones_active ON shipping_zones(is_active);
        CREATE INDEX IF NOT EXISTS idx_shipping_zones_countries ON shipping_zones USING GIN(countries);
      `
    });

    if (zonesError) {
      console.error('❌ Error creating shipping_zones table:', zonesError);
      throw zonesError;
    }

    // Create shipping methods table
    console.log('🚚 Creating shipping_methods table...');
    const { error: methodsError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS shipping_methods (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          zone_id UUID NOT NULL REFERENCES shipping_zones(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          free_shipping_threshold DECIMAL(10,2),
          estimated_days_min INTEGER NOT NULL DEFAULT 1,
          estimated_days_max INTEGER NOT NULL DEFAULT 7,
          icon VARCHAR(50) NOT NULL DEFAULT 'standard',
          is_active BOOLEAN NOT NULL DEFAULT true,
          sort_order INTEGER NOT NULL DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_shipping_methods_zone_id ON shipping_methods(zone_id);
        CREATE INDEX IF NOT EXISTS idx_shipping_methods_active ON shipping_methods(is_active);
        CREATE INDEX IF NOT EXISTS idx_shipping_methods_sort_order ON shipping_methods(sort_order);
      `
    });

    if (methodsError) {
      console.error('❌ Error creating shipping_methods table:', methodsError);
      throw methodsError;
    }

    console.log('✅ Tables created successfully!');

    // Insert default data
    await insertDefaultShippingData();

  } catch (error) {
    console.error('❌ Failed to setup shipping tables:', error);
    process.exit(1);
  }
}

async function insertDefaultShippingData() {
  console.log('📦 Inserting default shipping data...');

  try {
    // Insert UK zone
    const { data: ukZone, error: ukError } = await supabase
      .from('shipping_zones')
      .insert([{
        name: 'United Kingdom',
        description: 'Domestic shipping within the UK',
        countries: ['United Kingdom'],
        is_active: true
      }])
      .select()
      .single();

    if (ukError) {
      console.error('❌ Error inserting UK zone:', ukError);
      throw ukError;
    }

    // Insert EU zone
    const { data: euZone, error: euError } = await supabase
      .from('shipping_zones')
      .insert([{
        name: 'European Union',
        description: 'Shipping to EU countries',
        countries: [
          'Ireland', 'France', 'Germany', 'Spain', 'Italy', 'Netherlands',
          'Belgium', 'Portugal', 'Austria', 'Denmark', 'Sweden', 'Finland',
          'Poland', 'Czech Republic', 'Hungary', 'Slovakia', 'Slovenia',
          'Croatia', 'Estonia', 'Latvia', 'Lithuania', 'Luxembourg',
          'Malta', 'Cyprus', 'Bulgaria', 'Romania', 'Greece'
        ],
        is_active: true
      }])
      .select()
      .single();

    if (euError) {
      console.error('❌ Error inserting EU zone:', euError);
      throw euError;
    }

    // Insert UK shipping methods
    const ukMethods = [
      {
        zone_id: ukZone.id,
        name: 'Free Standard Shipping',
        description: 'Free delivery within 3-5 business days for orders over £50',
        price: 0.00,
        free_shipping_threshold: 50.00,
        estimated_days_min: 3,
        estimated_days_max: 5,
        icon: 'free',
        sort_order: 1
      },
      {
        zone_id: ukZone.id,
        name: 'Standard Shipping',
        description: 'Delivery within 3-5 business days',
        price: 5.99,
        free_shipping_threshold: null,
        estimated_days_min: 3,
        estimated_days_max: 5,
        icon: 'standard',
        sort_order: 2
      },
      {
        zone_id: ukZone.id,
        name: 'Express Shipping',
        description: 'Delivery within 2-3 business days',
        price: 9.99,
        free_shipping_threshold: null,
        estimated_days_min: 2,
        estimated_days_max: 3,
        icon: 'express',
        sort_order: 3
      },
      {
        zone_id: ukZone.id,
        name: 'Next Day Delivery',
        description: 'Order before 2pm for next day delivery',
        price: 14.99,
        free_shipping_threshold: null,
        estimated_days_min: 1,
        estimated_days_max: 1,
        icon: 'nextDay',
        sort_order: 4
      }
    ];

    const { error: ukMethodsError } = await supabase
      .from('shipping_methods')
      .insert(ukMethods);

    if (ukMethodsError) {
      console.error('❌ Error inserting UK methods:', ukMethodsError);
      throw ukMethodsError;
    }

    // Insert EU shipping methods
    const euMethods = [
      {
        zone_id: euZone.id,
        name: 'EU Free Shipping',
        description: 'Free delivery within 7-10 business days for orders over £100',
        price: 0.00,
        free_shipping_threshold: 100.00,
        estimated_days_min: 7,
        estimated_days_max: 10,
        icon: 'free',
        sort_order: 1
      },
      {
        zone_id: euZone.id,
        name: 'EU Standard Shipping',
        description: 'Delivery within 7-10 business days',
        price: 12.99,
        free_shipping_threshold: null,
        estimated_days_min: 7,
        estimated_days_max: 10,
        icon: 'standard',
        sort_order: 2
      },
      {
        zone_id: euZone.id,
        name: 'EU Express Shipping',
        description: 'Delivery within 5-7 business days',
        price: 19.99,
        free_shipping_threshold: null,
        estimated_days_min: 5,
        estimated_days_max: 7,
        icon: 'express',
        sort_order: 3
      }
    ];

    const { error: euMethodsError } = await supabase
      .from('shipping_methods')
      .insert(euMethods);

    if (euMethodsError) {
      console.error('❌ Error inserting EU methods:', euMethodsError);
      throw euMethodsError;
    }

    console.log('✅ Default shipping data inserted successfully!');

    // Verify the setup
    const { data: zones } = await supabase.from('shipping_zones').select('*');
    const { data: methods } = await supabase.from('shipping_methods').select('*');

    console.log(`\n📋 Setup complete:`);
    console.log(`   - ${zones?.length || 0} shipping zones created`);
    console.log(`   - ${methods?.length || 0} shipping methods created`);

  } catch (error) {
    console.error('❌ Failed to insert default shipping data:', error);
    throw error;
  }
}

// Run the setup
setupShippingTables()
  .then(() => {
    console.log('\n🎉 Shipping system setup complete!');
    console.log('\n📝 Next steps:');
    console.log('   1. Visit /admin/shipping to manage your shipping rates');
    console.log('   2. Update your checkout to use the new shipping service');
    console.log('   3. Test the shipping calculation with different countries');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  });
