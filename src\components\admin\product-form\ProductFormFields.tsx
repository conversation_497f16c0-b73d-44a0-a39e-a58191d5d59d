import React, { useState, useEffect } from "react";
import { Product, Category, Brand } from "@/types/database";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Wand2, Plus, Image, Loader2, Trash } from "lucide-react";
import { ImageManagerWrapper } from "./ImageManagerWrapper";
import { RelatedProductSelector } from "./RelatedProductSelector";
import { ProductQRCode } from "./ProductQRCode";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { BrandForm } from "../BrandForm";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import dynamic from 'next/dynamic';
// No longer using the useProductDescription hook

// Dynamically import React-Quill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';

interface ProductFormFieldsProps {
  formData: Partial<Product>;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (name: string, value: any) => void;
  handleSwitchChange: (name: string, checked: boolean) => void;
  brands: Brand[];
  categories: Category[];
  subcategories?: Category[]; // Add subcategories prop
  filteredSubcategories?: Category[]; // Add filteredSubcategories prop
  isLoadingSubcategories?: boolean; // Add loading state for subcategories
  handleFindImages?: (productName: string) => void;
  handleGenerateDescription?: () => void;
  isFindingImages?: boolean;
  isGeneratingDescription?: boolean;
  swapMainImage?: (newMainImageUrl: string) => void;
  // Related products
  relatedProducts?: Product[];
  onAddRelatedProduct?: (product: Product) => void;
  onRemoveRelatedProduct?: (productId: string) => void;
  onReorderRelatedProducts?: (products: Product[]) => void;
}

export function ProductFormFields({
  formData,
  handleChange,
  handleSelectChange,
  handleSwitchChange,
  brands,
  categories,
  subcategories = [],
  filteredSubcategories = [],
  isLoadingSubcategories = false,
  handleFindImages,
  handleGenerateDescription,
  isFindingImages = false,
  isGeneratingDescription = false,
  swapMainImage,
  // Related products
  relatedProducts = [],
  onAddRelatedProduct,
  onRemoveRelatedProduct,
  onReorderRelatedProducts,
}: ProductFormFieldsProps) {
  const [isBrandDialogOpen, setIsBrandDialogOpen] = useState(false);

  // State for dynamic options
  const [productOptions, setProductOptions] = useState<number[]>([1]);

  // State for dynamic additional info sections
  const [infoSections, setInfoSections] = useState<number[]>([1]);

  // Format the description once when the product is loaded
  const [initialDescription, setInitialDescription] = useState<string>("");

  // Handle initial formatting of description when product changes
  useEffect(() => {
    if (!formData?.id || !formData?.description) {
      setInitialDescription("");
      return;
    }

    const description = formData.description;

    // Check if the description already contains HTML
    const hasHtml = /<[a-z][\s\S]*>/i.test(description);

    if (hasHtml) {
      // Description already has HTML, use it as is
      setInitialDescription(description);
    } else {
      // Convert plain text to HTML
      const formattedText = description
        .split('\n\n')
        .map(para => para.trim() ? `<p>${para.trim()}</p>` : '')
        .join('');

      setInitialDescription(formattedText || description);
    }
  }, [formData?.id, formData?.description]);

  // Custom function to handle setting a new main image
  const handleSetMainImage = (url: string) => {
    console.log('Custom handleSetMainImage called with:', url);

    // Store the current state values we need
    const currentMainImage = formData.image;
    const currentAdditionalImages = [...(formData.additional_images || [])];

    // Step 1: If the previous main image exists and is valid, add it to additional images
    if (currentMainImage && currentMainImage !== url && currentMainImage.trim() !== '') {
      if (!currentAdditionalImages.includes(currentMainImage)) {
        currentAdditionalImages.push(currentMainImage);
        console.log('Added previous main image to additional images:', currentMainImage);
      }
    }

    // Step 2: Remove the new main image from additional images if it's there
    const updatedAdditionalImages = currentAdditionalImages.filter(img => img !== url);

    // Step 3: Update the form state with both changes
    console.log('Setting main image to:', url);
    console.log('Setting additional images to:', updatedAdditionalImages);

    // Update main image first
    handleSelectChange('image', url);

    // Then update additional images
    handleSelectChange('additional_images', updatedAdditionalImages);
  };

  const handleBrandDialogClose = () => {
    setIsBrandDialogOpen(false);
  };

  return (
    <>
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <p className="text-sm text-muted-foreground">
              Enter the basic details of your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name ?? ""}
                  onChange={handleChange}
                  placeholder="Enter product name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  name="sku"
                  value={formData.sku ?? ""}
                  onChange={handleChange}
                  placeholder="Enter product SKU"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug ?? ""}
                  onChange={handleChange}
                  placeholder="auto-generated-if-empty"
                />
                <p className="text-xs text-gray-500">
                  Leave empty to auto-generate from name
                </p>
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="images">
                  <span className="flex items-center gap-1">
                    <Image className="h-4 w-4" /> Product Images
                  </span>
                </Label>

                <ImageManagerWrapper
                  mainImage={formData.image || ''}
                  additionalImages={formData.additional_images || []}
                  onMainImageChange={(url) => {
                    console.log('ImageManagerWrapper onMainImageChange called with:', url);
                    if (swapMainImage) {
                      swapMainImage(url);
                    } else {
                      handleSelectChange('image', url);
                    }
                  }}
                  onAdditionalImagesChange={(urls) => {
                    console.log('ImageManagerWrapper onAdditionalImagesChange called with:', urls);
                    handleSelectChange('additional_images', urls);
                  }}
                  onFindImagesWithAI={handleFindImages ? () => handleFindImages(formData.name || '') : undefined}
                  isFindingImages={isFindingImages}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">
                  Description
                </Label>
                <div className="relative">
                  {/* Rich Text Editor for Description */}
                  <div className="border rounded-md">
                    <ReactQuill
                      id="description"
                      value={formData.id ? (initialDescription || formData.description || "") : (formData.description || "")}
                      onChange={(content) => {
                        // Create a synthetic event object to match the handleChange signature
                        const syntheticEvent = {
                          target: {
                            name: "description",
                            value: content
                          }
                        };
                        handleChange(syntheticEvent as React.ChangeEvent<HTMLInputElement>);
                      }}
                      placeholder="Enter product description"
                      theme="snow"
                      modules={{
                        toolbar: [
                          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                          ['bold', 'italic', 'underline', 'strike'],
                          [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                          [{ 'color': [] }, { 'background': [] }],
                          ['link'],
                          ['clean']
                        ],
                      }}
                      className="editor-container"
                      style={{ height: '300px' }}
                      key={`editor-${formData.id || 'new'}`} // Force re-render when product changes
                    />
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-500">
                      Click in the editor area above to start typing. Use the toolbar to format your text.
                    </p>
                    {handleGenerateDescription && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleGenerateDescription}
                        disabled={isGeneratingDescription}
                        className="ml-2 whitespace-nowrap"
                      >
                        {isGeneratingDescription ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Wand2 className="mr-2 h-4 w-4" />
                            Generate with AI
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pricing & Inventory</CardTitle>
            <p className="text-sm text-muted-foreground">
              Set the pricing and inventory details.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="price">Price (£)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sale_price">Sale Price (£)</Label>
                <Input
                  id="sale_price"
                  name="sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.sale_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500">
                  Leave empty if there's no sale
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost_price">Cost (£)</Label>
                <Input
                  id="cost_price"
                  name="cost_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cost_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock_quantity">Quantity</Label>
                <Input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.stock_quantity ?? ""}
                  onChange={handleChange}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="track_inventory"
                  checked={formData.in_stock ?? false}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("in_stock", checked)
                  }
                />
                <Label htmlFor="track_inventory">In Stock</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured ?? false}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("is_featured", checked)
                  }
                />
                <Label htmlFor="is_featured">Featured</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Organization</CardTitle>
            <p className="text-sm text-muted-foreground">
              Categorize and organize your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="brand_id">Brand</Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.brand_id?.toString() ?? ""}
                    onValueChange={(value) => handleSelectChange("brand_id", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id.toString()}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setIsBrandDialogOpen(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category_id">Category</Label>
                <select
                  id="category_id"
                  name="category_id"
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={formData.category_id?.toString() ?? ""}
                  onChange={(e) => {
                    handleSelectChange("category_id", e.target.value);
                    // Reset subcategory when category changes
                    handleSelectChange("subcategory_id", null);
                  }}
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option
                      key={category.id}
                      value={category.id.toString()}
                    >
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Subcategory dropdown - only show if a category is selected */}
              {formData.category_id && (
                <div className="space-y-2">
                  <Label htmlFor="subcategory_id">
                    Subcategory {isLoadingSubcategories && <span className="ml-2 text-xs text-muted-foreground">(Loading...)</span>}
                  </Label>
                  <select
                    id="subcategory_id"
                    name="subcategory_id"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.subcategory_id?.toString() ?? ""}
                    onChange={(e) => handleSelectChange("subcategory_id", e.target.value === "" ? null : e.target.value)}
                    disabled={isLoadingSubcategories}
                  >
                    <option value="">None</option>
                    {filteredSubcategories.map((subcategory) => (
                      <option
                        key={subcategory.id}
                        value={subcategory.id.toString()}
                      >
                        {subcategory.name}
                      </option>
                    ))}
                  </select>

                  {/* Debug info - remove in production */}
                  <div className="text-xs text-muted-foreground mt-1 p-2 bg-gray-100 rounded">
                    <p><strong>Debug Info:</strong></p>
                    <p>Selected Category ID: {formData.category_id}</p>
                    <p>Loading Subcategories: {isLoadingSubcategories ? 'Yes' : 'No'}</p>
                    <p>Total Subcategories: {subcategories.length}</p>
                    <p>Filtered Subcategories: {filteredSubcategories.length}</p>
                    <p>Selected Subcategory ID: {formData.subcategory_id || 'None'}</p>
                    <p>Filtered Subcategory Names: {filteredSubcategories.map(s => s.name).join(', ') || 'None'}</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.additional_info ?? ""}
                  onChange={handleChange}
                  placeholder="Enter tags separated by commas"
                />
                <p className="text-xs text-gray-500">
                  Separate tags with commas
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Legacy Product Options section has been removed in favor of the new variant system */}

        {/* Additional Information */}
        <Card className="mt-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Additional Information</CardTitle>
              <p className="text-sm text-muted-foreground">
                Add extra details about the product.
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="h-8 gap-1"
              onClick={() => {
                // Add a new info section
                const nextSectionNumber = Math.max(...infoSections) + 1;
                if (nextSectionNumber <= 3) { // Limit to 3 sections
                  setInfoSections([...infoSections, nextSectionNumber]);
                } else {
                  alert('Maximum of 3 information sections allowed');
                }
              }}
            >
              <Plus className="h-3.5 w-3.5" />
              Add Section
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Map through info sections */}
              {infoSections.map((sectionNum) => (
                <div key={`info-section-${sectionNum}`} className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${sectionNum > 1 ? 'border-t pt-4 mt-4' : ''}`}>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor={`additional_info_title${sectionNum}`}>Section Title {sectionNum > 1 ? sectionNum : ''}</Label>
                      {sectionNum > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setInfoSections(infoSections.filter(num => num !== sectionNum));
                          }}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <Input
                      id={`additional_info_title${sectionNum}`}
                      name={`additional_info_title${sectionNum}`}
                      value={formData[`additional_info_title${sectionNum}` as keyof typeof formData] as string || ''}
                      onChange={handleChange}
                      placeholder="e.g. Features, Specifications"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`additional_info_description${sectionNum}`}>Section Content</Label>
                    <Textarea
                      id={`additional_info_description${sectionNum}`}
                      name={`additional_info_description${sectionNum}`}
                      value={formData[`additional_info_description${sectionNum}` as keyof typeof formData] as string || ''}
                      onChange={handleChange}
                      placeholder="Enter details for this section"
                      rows={3}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Related Products Section */}
        <Card>
          <CardHeader>
            <CardTitle>Related Products</CardTitle>
            <p className="text-sm text-muted-foreground">
              Add products that customers might also want to purchase.
            </p>
          </CardHeader>
          <CardContent>
            {onAddRelatedProduct && onRemoveRelatedProduct && onReorderRelatedProducts && (
              <RelatedProductSelector
                productId={formData.id}
                selectedProducts={relatedProducts}
                onAddProduct={onAddRelatedProduct}
                onRemoveProduct={onRemoveRelatedProduct}
                onReorderProducts={onReorderRelatedProducts}
              />
            )}
          </CardContent>
        </Card>

        {/* QR Code Section - Only show if we have a slug */}
        {formData.slug && (
          <Card>
            <CardHeader>
              <CardTitle>QR Code</CardTitle>
              <p className="text-sm text-muted-foreground">
                Generate a QR code for this product for marketing materials.
              </p>
            </CardHeader>
            <CardContent>
              <ProductQRCode
                productSlug={formData.slug}
                productName={formData.name || ""}
              />
            </CardContent>
          </Card>
        )}
      </div>

      {/* Brand Form Dialog */}
      <Dialog open={isBrandDialogOpen} onOpenChange={setIsBrandDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Add New Brand</DialogTitle>
            <DialogDescription>
              Enter the brand details below to create a new brand.
            </DialogDescription>
          </DialogHeader>
          <BrandForm
            brand={null}
            onSuccess={handleBrandDialogClose}
            onCancel={handleBrandDialogClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
