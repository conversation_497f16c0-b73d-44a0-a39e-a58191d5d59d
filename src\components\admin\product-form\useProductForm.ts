
import { useState, useEffect } from "react";
import { Product } from "@/types/database";
import { useProductFormState } from "./hooks/useProductFormState";
import { useProductSave } from "./hooks/useProductSave";
import { useProductAI } from "./hooks/useProductAI";
import { useProductValidation } from "./hooks/useProductValidation";
import { supabase } from "@/integrations/supabase/client";

interface UseProductFormProps {
  product: Product | null;
  onSuccess: () => void;
}

export function useProductForm({ product, onSuccess }: UseProductFormProps) {
  // Use the individual hooks we've created
  const {
    formData,
    setFormData,
    handleChange,
    handleSwitchChange,
    handleSelectChange
  } = useProductFormState(product);

  const { validateForm } = useProductValidation();

  const {
    isSubmitting,
    setIsSubmitting,
    saveProduct
  } = useProductSave({ product, onSuccess });

  const {
    isGeneratingDescription,
    isFindingImages,
    handleGenerateDescription,
    handleFindImages
  } = useProductAI(formData, setFormData);

  // State for related products - using a simplified type to avoid type errors
  type SimpleProduct = {
    id: string;
    name: string;
    image?: string;
    price?: number;
    sku?: string;
  };

  const [relatedProducts, setRelatedProducts] = useState<SimpleProduct[]>([]);

  // Fetch related products when editing an existing product
  useEffect(() => {
    if (product?.id) {
      const fetchRelatedProducts = async () => {
        try {
          // Get related products directly from the related_products table
          const { data: relationData, error: relationError } = await supabase
            .from('related_products')
            .select('*')
            .eq('product_id', product.id)
            .order('display_order');

          if (relationError) {
            console.error('Error fetching related product IDs:', relationError);
            return;
          }

          if (!relationData || relationData.length === 0) {
            return;
          }

          // Extract the related product IDs
          const relatedProductIds = relationData.map(item => item.related_product_id);

          // Then fetch the actual product data
          const { data: productsData, error: productsError } = await supabase
            .from('products')
            .select('id, name, image, price, sku')
            .in('id', relatedProductIds);

          if (productsError) {
            console.error('Error fetching related products:', productsError);
            return;
          }

          // Sort the products according to the original order
          const orderedProducts = relatedProductIds.map(id => {
            const product = productsData.find(p => p.id === id);
            if (product) {
              return {
                id: product.id,
                name: product.name,
                image: product.image,
                price: product.price,
                sku: product.sku
              } as SimpleProduct;
            }
            return null;
          }).filter(Boolean) as SimpleProduct[];

          setRelatedProducts(orderedProducts);
        } catch (error) {
          console.error('Error fetching related products:', error);
        }
      };

      fetchRelatedProducts();
    }
  }, [product?.id]);

  // Related products handlers
  const handleAddRelatedProduct = (product: Product) => {
    setRelatedProducts(prev => [...prev, product]);
  };

  const handleRemoveRelatedProduct = (productId: string) => {
    setRelatedProducts(prev => prev.filter(p => p.id !== productId));
  };

  const handleReorderRelatedProducts = (products: Product[]) => {
    setRelatedProducts(products);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("Form submitted with data:", formData);

    setIsSubmitting(true);

    // Validate the form data
    const validationResult = validateForm(formData);

    if (!validationResult.isValid) {
      console.error("Form validation failed:", validationResult.errors);
      setIsSubmitting(false);
      return;
    }

    // Save the product
    try {
      const savedProduct = await saveProduct.mutateAsync(formData as any);

      // If we have a product ID, handle related products
      if (savedProduct?.id) {
        console.log('Handling related products for:', savedProduct.id);

        try {
          // First, delete existing relationships
          const { error: deleteError } = await supabase
            .from('related_products')
            .delete()
            .eq('product_id', savedProduct.id);

          if (deleteError) {
            console.error('Error deleting existing relationships:', deleteError);
          }

          // If we have related products, add them
          if (relatedProducts.length > 0) {
            // Prepare the data for insertion
            const relatedProductsData = relatedProducts.map((product, index) => ({
              product_id: savedProduct.id,
              related_product_id: product.id,
              display_order: index
            }));

            // Insert the new relationships
            const { error: insertError } = await supabase
              .from('related_products')
              .insert(relatedProductsData);

            if (insertError) {
              console.error('Error saving product relationships:', insertError);
            } else {
              console.log('Successfully saved product relationships');
            }
          }
        } catch (error) {
          console.error('Error handling product relationships:', error);
        }
      }

      // Clear the form data from localStorage if this was a new product
      if (!product) {
        localStorage.removeItem('productFormData');
      }

    } catch (error) {
      console.error("Error saving product:", error);
      setIsSubmitting(false);
    }
  };

  return {
    formData,
    setFormData,
    isSubmitting,
    isGeneratingDescription,
    isFindingImages,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
    handleSubmit,
    handleGenerateDescription,
    handleFindImages,
    // Related products
    relatedProducts,
    handleAddRelatedProduct,
    handleRemoveRelatedProduct,
    handleReorderRelatedProducts,
  };
}
