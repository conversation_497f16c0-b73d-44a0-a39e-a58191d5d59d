// filter.js - Handles filter functionality

// Store current filter state
let currentFilters = {};

// Function to initialize filter functionality
function initializeFilters() {
    // Set up filter section toggle
    const filterTitles = document.querySelectorAll('.filter-title');
    filterTitles.forEach(title => {
        title.addEventListener('click', () => {
            title.classList.toggle('collapsed');
            const options = title.nextElementSibling;
            if (options) {
                options.style.display = title.classList.contains('collapsed') ? 'none' : 'flex';
            }
        });
    });

    // Set up "see more"/"see less" toggles
    const toggleMoreButtons = document.querySelectorAll('.toggle-more');
    toggleMoreButtons.forEach(toggle => {
        const showMore = toggle.querySelector('.show-more');
        const showLess = toggle.querySelector('.show-less');
        const moreOptions = toggle.previousElementSibling;

        if (showMore && showLess && moreOptions) {
            showMore.addEventListener('click', () => {
                moreOptions.classList.remove('hidden');
                showMore.classList.add('hidden');
                showLess.classList.remove('hidden');
            });

            showLess.addEventListener('click', () => {
                moreOptions.classList.add('hidden');
                showLess.classList.add('hidden');
                showMore.classList.remove('hidden');
            });
        }
    });

    // Set up filter checkbox handlers
    const filterCheckboxes = document.querySelectorAll('.filter-option input[type="checkbox"]');
    filterCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const filterType = checkbox.getAttribute('data-filter');
            const filterValue = checkbox.getAttribute('data-value');
            
            // Update current filters
            if (!currentFilters[filterType]) {
                currentFilters[filterType] = [];
            }
            
            if (checkbox.checked) {
                if (!currentFilters[filterType].includes(filterValue)) {
                    currentFilters[filterType].push(filterValue);
                }
            } else {
                currentFilters[filterType] = currentFilters[filterType].filter(val => val !== filterValue);
                if (currentFilters[filterType].length === 0) {
                    delete currentFilters[filterType];
                }
            }
            
            // Apply filters and update URL
            applyFilters();
            updateUrlWithFilters();
        });
    });

    // Load filters from URL on page load
    loadFiltersFromUrl();
}

// Function to filter products based on selected filters
function filterProducts(products, filters) {
    if (Object.keys(filters).length === 0) {
        return products;
    }
    
    return products.filter(product => {
        // Check if product matches all filter categories (AND logic between categories)
        return Object.keys(filters).every(filterType => {
            const filterValues = filters[filterType];
            
            // If no values selected for this filter type, it passes
            if (!filterValues || filterValues.length === 0) {
                return true;
            }
            
            // Convert filter type to data attribute format
            const dataAttr = `data-${filterType}`;
            
            // Special handling for different filter types
            switch (filterType) {
                case 'flowertime':
                    // Check if product's flowertime matches any selected values
                    return filterValues.some(value => {
                        const productFlowertime = product.flowertime || '';
                        return productFlowertime.toLowerCase().includes(value.replace(/-/g, ' '));
                    });
                
                case 'yield':
                    // Check if product's yield matches any selected values
                    return filterValues.some(value => {
                        const productYield = product.yield || '';
                        return productYield.toLowerCase() === value.replace(/-/g, ' ');
                    });
                
                case 'thc':
                    // Check if product's THC level matches any selected values
                    return filterValues.some(value => {
                        const productThc = product.thc || '';
                        return productThc.toLowerCase().includes(value.replace(/-/g, ' '));
                    });
                
                case 'seed-type':
                    // Check if product's seed type matches any selected values
                    return filterValues.some(value => {
                        const productSeedType = product.seed_type || '';
                        return productSeedType.toLowerCase() === value;
                    });
                
                case 'effect':
                    // Check if product's effect matches any selected values
                    return filterValues.some(value => {
                        const productEffect = product.effect || '';
                        return productEffect.toLowerCase() === value;
                    });
                
                default:
                    // Generic handling for other filter types
                    // Check if product has the attribute and if it matches any selected values
                    return filterValues.some(value => {
                        const productValue = product[filterType.replace(/-/g, '_')] || '';
                        return productValue.toString().toLowerCase().includes(value.replace(/-/g, ' '));
                    });
            }
        });
    });
}

// Function to apply filters to the product display
function applyFilters() {
    const filteredProducts = filterProducts(allProducts, currentFilters);
    const sortSelect = document.getElementById('sort-select');
    const sortedProducts = handleSorting(sortSelect.value, filteredProducts);
    
    // Reset to first page when filters change
    currentPage = 1;
    
    // Render filtered products
    renderProducts(sortedProducts, currentPage);
    updateProductCount(filteredProducts.length);
    
    // Update filter counts (this would require additional data in a real implementation)
    // updateFilterCounts(filteredProducts);
}

// Function to update URL with current filters (for bookmarking/sharing)
function updateUrlWithFilters() {
    const url = new URL(window.location.href);
    
    // Clear existing filter parameters
    const searchParams = url.searchParams;
    for (const key of [...searchParams.keys()]) {
        if (key.startsWith('filter_')) {
            searchParams.delete(key);
        }
    }
    
    // Add current filters to URL
    Object.keys(currentFilters).forEach(filterType => {
        const values = currentFilters[filterType];
        if (values && values.length > 0) {
            searchParams.set(`filter_${filterType}`, values.join(','));
        }
    });
    
    // Update URL without reloading page
    window.history.replaceState({}, '', url);
}

// Function to load filters from URL parameters
function loadFiltersFromUrl() {
    const url = new URL(window.location.href);
    const searchParams = url.searchParams;
    
    // Clear current filters
    currentFilters = {};
    
    // Check each filter parameter
    for (const [key, value] of searchParams.entries()) {
        if (key.startsWith('filter_')) {
            const filterType = key.replace('filter_', '');
            const filterValues = value.split(',');
            
            if (filterValues.length > 0) {
                currentFilters[filterType] = filterValues;
                
                // Check corresponding checkboxes
                filterValues.forEach(val => {
                    const checkbox = document.querySelector(`.filter-option input[data-filter="${filterType}"][data-value="${val}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
        }
    }
    
    // Apply filters if any were loaded
    if (Object.keys(currentFilters).length > 0) {
        applyFilters();
    }
}

// Function to create a JSON file from the scraped data
function createProductsJson(scrapedProducts) {
    // Transform scraped data to match our filter structure
    const transformedProducts = scrapedProducts.map((product, index) => {
        return {
            id: product.data_id_product || `product_${index + 1}`,
            name: product.name,
            price: product.price,
            seed_type: product.seed_type,
            image_url: product.image_url,
            url: product.url,
            // Add filter attributes based on available data
            // These would need to be extracted from product details in a real implementation
            flowertime: "9 weeks", // Example - would come from product details
            yield: "XL",           // Example - would come from product details
            thc: "Very High",      // Example - would come from product details
            effect: "Hybrid",      // Example - would come from product details
            // Additional attributes as needed
        };
    });
    
    return JSON.stringify(transformedProducts, null, 2);
}

// Function to create a mapping tool for client's products
function createProductMappingTool(clientProducts, filterCategories) {
    // This would be a more complex implementation in a real system
    // For now, we'll create a simple structure for demonstration
    
    const mappingTool = {
        unmappedProducts: clientProducts,
        filterCategories: filterCategories,
        suggestedMappings: {}
    };
    
    // For each client product, suggest mappings based on name similarity
    clientProducts.forEach(product => {
        mappingTool.suggestedMappings[product.id] = {
            product: product,
            suggestedFilters: {
                // These would be algorithmically determined in a real implementation
                flowertime: null,
                yield: null,
                thc: null,
                seed_type: detectSeedType(product.name),
                effect: null
            }
        };
    });
    
    return mappingTool;
}

// Helper function to detect seed type from product name
function detectSeedType(productName) {
    const name = productName.toLowerCase();
    if (name.includes('auto')) {
        return 'Autoflower';
    } else if (name.includes('fem')) {
        return 'Feminized';
    } else if (name.includes('reg')) {
        return 'Regular';
    }
    return null;
}

// Initialize filters when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeFilters();
});
