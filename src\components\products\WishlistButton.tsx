import React, { useState } from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { useAuth } from '@/hooks/auth.basic';
import { useWishlists } from '@/hooks/useWishlists';
import { toast } from '@/components/ui/use-toast';

interface WishlistButtonProps {
  productId: string;
  className?: string;
}

/**
 * Wishlist Button that integrates with the WishlistsProvider
 */
const WishlistButton: React.FC<WishlistButtonProps> = ({ productId, className }) => {
  const { user } = useAuth();
  const { addToWishlist, removeFromWishlist, isInWishlist, getDefaultWishlist, isLoading } = useWishlists();
  const [isProcessing, setIsProcessing] = useState(false);

  // Check if product is in any wishlist (using default wishlist)
  const defaultWishlist = getDefaultWishlist();
  const isInDefaultWishlist = defaultWishlist ? isInWishlist(productId, defaultWishlist.id) : false;

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!user) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to use the wishlist feature',
        variant: 'destructive',
      });
      return;
    }

    if (isProcessing || isLoading) return;

    setIsProcessing(true);
    try {
      if (isInDefaultWishlist) {
        // Remove from wishlist
        await removeFromWishlist(productId, defaultWishlist?.id);
      } else {
        // Add to wishlist
        await addToWishlist(productId);
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`p-1.5 rounded-full bg-white transition-colors duration-200 ${
        isInDefaultWishlist
          ? 'text-red-500 hover:text-red-600'
          : 'text-gray-400 hover:text-red-500'
      } ${className}`}
      aria-label={isInDefaultWishlist ? "Remove from wishlist" : "Add to wishlist"}
      disabled={isProcessing || isLoading}
    >
      {isProcessing || isLoading ? (
        <Loader2 className="h-5 w-5 animate-spin" />
      ) : (
        <Heart className={`h-5 w-5 ${isInDefaultWishlist ? 'fill-current' : ''}`} />
      )}
    </button>
  );
};

export default WishlistButton;
