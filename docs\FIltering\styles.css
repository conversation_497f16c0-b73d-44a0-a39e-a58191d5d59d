/* Main Styles for Cannabis Seeds Filter System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f8f8;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 24px;
    color: #2c7a2c;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #2c7a2c;
}

.cart a {
    text-decoration: none;
    color: #333;
    font-size: 18px;
    position: relative;
}

.cart a span {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #2c7a2c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Main Content Styles */
main {
    padding: 30px 0;
}

.page-title {
    margin-bottom: 30px;
}

.page-title h2 {
    font-size: 28px;
    color: #2c7a2c;
}

.shop-container {
    display: flex;
    gap: 30px;
}

/* Filter Sidebar Styles */
.filter-sidebar {
    width: 280px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    align-self: flex-start;
    position: sticky;
    top: 90px;
}

.filter-sidebar h3 {
    font-size: 22px;
    margin-bottom: 20px;
    color: #2c7a2c;
}

.filter-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.filter-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.filter-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
}

.filter-title h4 {
    font-size: 16px;
    font-weight: 600;
}

.toggle-icon {
    font-size: 14px;
    transition: transform 0.3s;
}

.filter-title.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
}

.filter-option input {
    margin-right: 8px;
}

.filter-option .count {
    margin-left: 5px;
    color: #777;
    font-size: 12px;
}

.more-options.hidden {
    display: none;
}

.toggle-more {
    margin-top: 10px;
    font-size: 13px;
    color: #2c7a2c;
    cursor: pointer;
}

.show-more, .show-less {
    display: inline-block;
}

.show-less.hidden, .show-more.hidden {
    display: none;
}

/* Product Container Styles */
.product-container {
    flex: 1;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.product-count {
    font-size: 16px;
    font-weight: 500;
}

.product-count span {
    font-weight: 600;
    color: #2c7a2c;
}

.product-sort select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 14px;
    cursor: pointer;
}

/* Product Grid Styles */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.product-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-auto {
    background-color: #ff9800;
    color: white;
}

.badge-fem {
    background-color: #e91e63;
    color: white;
}

.badge-reg {
    background-color: #2196f3;
    color: white;
}

.product-info {
    padding: 15px;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.product-title a {
    text-decoration: none;
    color: inherit;
}

.product-price {
    font-size: 18px;
    font-weight: 700;
    color: #2c7a2c;
    margin-bottom: 15px;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quantity-select {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.add-to-cart {
    background-color: #2c7a2c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.add-to-cart:hover {
    background-color: #225f22;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 5px;
}

.pagination a, .pagination span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    transition: background-color 0.3s, color 0.3s;
}

.pagination a {
    background-color: #fff;
    border: 1px solid #ddd;
}

.pagination a:hover {
    background-color: #f1f1f1;
}

.pagination a.active {
    background-color: #2c7a2c;
    color: white;
    border-color: #2c7a2c;
}

/* Footer Styles */
footer {
    background-color: #333;
    color: #fff;
    padding: 20px 0;
    text-align: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .shop-container {
        flex-direction: column;
    }

    .filter-sidebar {
        width: 100%;
        position: static;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        gap: 15px;
    }

    nav ul {
        justify-content: center;
    }

    .cart {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .product-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr;
    }
}
