-- Migration to add option_definitions JSONB field to products table
-- This will store the available options and their possible values for each product

-- Check if the column already exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'products'
    AND column_name = 'option_definitions'
  ) THEN
    -- Add the option_definitions column
    ALTER TABLE products
    ADD COLUMN option_definitions JSONB DEFAULT '{}';
    
    RAISE NOTICE 'Added option_definitions column to products table';
  ELSE
    RAISE NOTICE 'option_definitions column already exists in products table';
  END IF;
END $$;

-- Create index for performance if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE indexname = 'idx_products_option_definitions'
  ) THEN
    CREATE INDEX idx_products_option_definitions ON products USING GIN(option_definitions);
    RAISE NOTICE 'Created index on option_definitions column';
  ELSE
    RAISE NOTICE 'Index on option_definitions column already exists';
  END IF;
END $$;

-- Create indexes for product_variants table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE indexname = 'idx_product_variants_product_id'
  ) THEN
    CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
    RAISE NOTICE 'Created index on product_variants.product_id column';
  ELSE
    RAISE NOTICE 'Index on product_variants.product_id column already exists';
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE indexname = 'idx_product_variants_option_combination'
  ) THEN
    CREATE INDEX idx_product_variants_option_combination ON product_variants USING GIN(option_combination);
    RAISE NOTICE 'Created index on product_variants.option_combination column';
  ELSE
    RAISE NOTICE 'Index on product_variants.option_combination column already exists';
  END IF;
END $$;
