// Script to update product image references in the database to use .webp extension
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to update image URLs in the database
async function updateImageExtensions() {
  console.log('Starting image extension update...');
  
  try {
    // Fetch all products
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products in the database`);
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Check main image
      if (product.image && product.image.includes('.jpg') || product.image.includes('.png') || product.image.includes('.jpeg')) {
        // Replace the extension with .webp
        const webpUrl = product.image.replace(/\.(jpg|jpeg|png)($|\?)/, '.webp$2');
        updates.image = webpUrl;
        needsUpdate = true;
      }
      
      // Check additional images
      if (product.additional_images && Array.isArray(product.additional_images) && product.additional_images.length > 0) {
        const updatedAdditionalImages = product.additional_images.map(imgUrl => {
          if (imgUrl.includes('.jpg') || imgUrl.includes('.png') || imgUrl.includes('.jpeg')) {
            return imgUrl.replace(/\.(jpg|jpeg|png)($|\?)/, '.webp$2');
          }
          return imgUrl;
        });
        
        // Check if any additional images were updated
        const hasChanges = JSON.stringify(updatedAdditionalImages) !== JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = updatedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error updating image extensions:', error);
  }
}

// Run the script
updateImageExtensions().catch(console.error);
