// <PERSON>ript to set option pricing adjustments based on percentage of base price
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';

// Configure dotenv
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Log the Supabase connection details (without the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'undefined';
console.log(`Connecting to Supabase at: ${supabaseUrl} with key: ${maskedKey}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase URL or key is missing. Make sure your .env file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const DRY_RUN = process.argv.includes('--dry-run');

// Helper function to prompt for user input
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Main function
async function setOptionPricing() {
  console.log('Starting option pricing setup...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);

  try {
    // Get pricing strategy from user
    console.log('\nSelect pricing strategy:');
    console.log('1. Percentage-based (e.g., +10% of base price for each option)');
    console.log('2. Fixed amount (e.g., +5.00 for each option)');
    console.log('3. Progressive (e.g., +5.00 for first option, +10.00 for second, etc.)');
    
    const strategyChoice = await prompt('Enter choice (1-3): ');
    let strategy = 'percentage';
    
    if (strategyChoice === '2') {
      strategy = 'fixed';
    } else if (strategyChoice === '3') {
      strategy = 'progressive';
    }
    
    // Get adjustment values based on strategy
    let adjustmentValue = 0;
    let progressiveValues = [];
    
    if (strategy === 'percentage') {
      const percentStr = await prompt('Enter percentage increase for options (e.g., 10 for 10%): ');
      adjustmentValue = parseFloat(percentStr) || 10;
      console.log(`Using ${adjustmentValue}% of base price for option adjustments`);
    } else if (strategy === 'fixed') {
      const amountStr = await prompt('Enter fixed amount for options (e.g., 5 for £5.00): ');
      adjustmentValue = parseFloat(amountStr) || 5;
      console.log(`Using fixed amount of £${adjustmentValue.toFixed(2)} for option adjustments`);
    } else if (strategy === 'progressive') {
      const valuesStr = await prompt('Enter comma-separated progressive amounts (e.g., 5,10,15): ');
      progressiveValues = valuesStr.split(',').map(v => parseFloat(v.trim()) || 0);
      console.log(`Using progressive amounts: ${progressiveValues.map(v => `£${v.toFixed(2)}`).join(', ')}`);
    }
    
    // Ask if first option should be free (0 adjustment)
    const firstOptionFreeStr = await prompt('Should the first option have no price adjustment? (y/n): ');
    const firstOptionFree = firstOptionFreeStr.toLowerCase() === 'y';
    
    // 1. Fetch products from the database with options
    console.log('\nFetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('option_name1', 'is', null)
      .not('option_values1', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts?.length || 0} products in database with options`);

    if (!dbProducts || dbProducts.length === 0) {
      console.log('No products found with options in the database');
      return;
    }

    // 2. Process each product with options
    const productsToUpdate = [];
    const productsWithIssues = [];

    for (const dbProduct of dbProducts) {
      // Skip products without options
      if (!dbProduct.option_values1 || !dbProduct.option_name1) continue;

      // Get option values as an array
      const optionValues = dbProduct.option_values1.split(';').map(v => v.trim());
      if (optionValues.length === 0) continue;

      // Get the base price
      const basePrice = dbProduct.price || 0;
      if (basePrice <= 0) {
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Invalid base price: ${basePrice}`,
        });
        continue;
      }

      try {
        // Calculate price adjustments based on selected strategy
        const priceAdjustments = [];
        
        for (let i = 0; i < optionValues.length; i++) {
          let adjustment = 0;
          
          // First option might be free (0 adjustment)
          if (i === 0 && firstOptionFree) {
            adjustment = 0;
          } else {
            if (strategy === 'percentage') {
              adjustment = (basePrice * adjustmentValue) / 100;
            } else if (strategy === 'fixed') {
              adjustment = adjustmentValue;
            } else if (strategy === 'progressive') {
              // Use the progressive value if available, otherwise use the last one
              const valueIndex = firstOptionFree ? i - 1 : i;
              if (valueIndex >= 0 && valueIndex < progressiveValues.length) {
                adjustment = progressiveValues[valueIndex];
              } else if (progressiveValues.length > 0) {
                adjustment = progressiveValues[progressiveValues.length - 1];
              }
            }
          }
          
          priceAdjustments.push(adjustment);
        }

        // Format price adjustments as semicolon-separated string
        const priceAdjustmentStr = priceAdjustments.join(';');

        // Add to update list
        productsToUpdate.push({
          id: dbProduct.id,
          name: dbProduct.name,
          option_price_adjustment1: priceAdjustmentStr,
          option_values: optionValues.join(', '),
          base_price: basePrice,
          adjustments: priceAdjustments.map(a => a.toFixed(2)).join(', ')
        });

        console.log(`Product: ${dbProduct.name}`);
        console.log(`  Option values: ${optionValues.join(', ')}`);
        console.log(`  Price adjustments: ${priceAdjustmentStr}`);
      } catch (err) {
        console.error(`Error processing product ${dbProduct.name}:`, err);
        productsWithIssues.push({
          id: dbProduct.id,
          name: dbProduct.name,
          issue: `Error: ${err.message}`,
        });
      }
    }

    // 3. Update products in the database (if not dry run)
    if (productsToUpdate.length > 0) {
      console.log(`\nReady to update ${productsToUpdate.length} products with price adjustments`);
      
      const confirmUpdate = await prompt('Proceed with update? (y/n): ');
      
      if (confirmUpdate.toLowerCase() === 'y' && !DRY_RUN) {
        console.log('Updating products in database...');
        
        // Update products in batches to avoid hitting API limits
        const BATCH_SIZE = 50;
        for (let i = 0; i < productsToUpdate.length; i += BATCH_SIZE) {
          const batch = productsToUpdate.slice(i, i + BATCH_SIZE);
          
          // Update each product individually to avoid issues
          for (const product of batch) {
            const { error } = await supabase
              .from('products')
              .update({ option_price_adjustment1: product.option_price_adjustment1 })
              .eq('id', product.id);
              
            if (error) {
              console.error(`Error updating product ${product.id}:`, error);
              productsWithIssues.push({
                id: product.id,
                name: product.name,
                issue: `Update error: ${error.message}`,
              });
            }
          }
          
          console.log(`Updated batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(productsToUpdate.length / BATCH_SIZE)}`);
        }
        
        console.log('Update completed successfully!');
      } else {
        console.log('Update cancelled or dry run mode active. No changes were made to the database');
        console.log('\nSample of products that would be updated:');
        for (let i = 0; i < Math.min(10, productsToUpdate.length); i++) {
          const product = productsToUpdate[i];
          console.log(`- ${product.name} (£${product.base_price}):`);
          console.log(`  Options: ${product.option_values}`);
          console.log(`  Adjustments: ${product.adjustments}`);
        }
      }
    } else {
      console.log('No products to update');
    }

    // 4. Report issues
    if (productsWithIssues.length > 0) {
      console.log(`\n${productsWithIssues.length} products had issues:`);
      productsWithIssues.forEach(p => {
        console.log(`- ${p.name} (${p.id}): ${p.issue}`);
      });
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  } finally {
    rl.close();
  }
}

// Run the function
setOptionPricing().catch(console.error);
