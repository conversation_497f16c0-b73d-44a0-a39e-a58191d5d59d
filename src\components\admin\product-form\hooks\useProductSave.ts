
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";
import { toast } from "@/components/ui/use-toast";
import { generateSlug } from "@/utils/slugUtils";

interface UseProductSaveProps {
  product: Product | null;
  onSuccess: () => void;
}

export function useProductSave({ product, onSuccess }: UseProductSaveProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  const saveProduct = useMutation({
    mutationFn: async (data: Partial<Product> & { name: string, slug: string, price: number }) => {
      console.log("Saving product data:", data);
      
      // Process the data before saving
      const dataToSave = { ...data };
      
      // Generate slug if empty
      if (!dataToSave.slug || dataToSave.slug.trim() === '') {
        console.log("Generating slug from product name:", dataToSave.name);
        dataToSave.slug = generateSlug(dataToSave.name);
        console.log("Generated slug:", dataToSave.slug);
      }
      
      // Handle the "none" value for category_id
      if (dataToSave.category_id === "none") {
        dataToSave.category_id = null;
      }
      
      // Ensure boolean flags are properly set
      dataToSave.is_best_seller = !!dataToSave.is_best_seller;
      dataToSave.is_featured = !!dataToSave.is_featured;
      dataToSave.is_new = !!dataToSave.is_new;
      dataToSave.in_stock = dataToSave.in_stock !== false; // Default to true if not set
      
      console.log('Processed product data before save:', {
        is_best_seller: dataToSave.is_best_seller,
        is_featured: dataToSave.is_featured,
        is_new: dataToSave.is_new
      });
      
      if (product) {
        // Update existing product
        const { error, data: updatedProduct } = await supabase
          .from("products")
          .update(dataToSave)
          .eq("id", product.id)
          .select()
          .single();
        
        if (error) {
          console.error("Error updating product:", error);
          throw error;
        }
        
        console.log("Product updated successfully:", updatedProduct);
        return { ...product, ...dataToSave };
      } else {
        // Create new product
        const { data: newProduct, error } = await supabase
          .from("products")
          .insert(dataToSave)
          .select()
          .single();
        
        if (error) {
          console.error("Error creating product:", error);
          throw error;
        }
        
        console.log("Product created successfully:", newProduct);
        return newProduct;
      }
    },
    onSuccess: (data) => {
      toast({
        title: product ? "Product Updated" : "Product Created",
        description: `${data.name} has been successfully ${product ? "updated" : "created"}.`,
      });
      
      // Invalidate all product-related queries to ensure data is refreshed
      queryClient.invalidateQueries({ queryKey: ["products"] });
      
      // Also invalidate any specific product filters that might be cached
      if (data.is_best_seller) {
        console.log('Invalidating best sellers cache');
        queryClient.invalidateQueries({ queryKey: ["products", "best-sellers"] });
      }
      if (data.is_featured) {
        queryClient.invalidateQueries({ queryKey: ["products", "featured"] });
      }
      if (data.is_new) {
        queryClient.invalidateQueries({ queryKey: ["products", "new"] });
      }
      
      onSuccess();
    },
    onError: (error) => {
      console.error("Error in saveProduct mutation:", error);
      toast({
        title: "Error",
        description: `Failed to save product: ${error.message}`,
        variant: "destructive",
      });
      setIsSubmitting(false);
    },
  });

  return {
    isSubmitting,
    setIsSubmitting,
    saveProduct
  };
}
