/**
 * Twitter Publisher Implementation
 * 
 * This file implements the Twitter-specific publisher for the Social Media Publishing System.
 * It handles authentication, content publishing, and analytics for Twitter.
 */

import axios from 'axios';
import {
  PostContent,
  PublishResult,
  PostStatusInfo,
  SocialMediaAccount,
  PostAnalytics,
  ContentValidationResult,
  ContentIssue,
  MediaContent
} from './social_media_types';
import {
  BaseSocialMediaPublisher,
  TokenManager,
  ErrorHandler,
  RateLimiter
} from './social_media_publisher';

/**
 * Twitter-specific configuration
 */
interface TwitterConfig {
  apiBaseUrl: string;
  apiVersion: string;
  apiKey: string;
  apiSecret: string;
  bearerToken: string;
}

/**
 * Twitter API response for tweet creation
 */
interface TwitterTweetResponse {
  data: {
    id: string;
    text: string;
    edit_history_tweet_ids: string[];
  };
}

/**
 * Twitter API response for media upload
 */
interface TwitterMediaResponse {
  media_id_string: string;
  media_key?: string;
  size?: number;
  expires_after_secs?: number;
}

/**
 * Twitter API response for tweet metrics
 */
interface TwitterMetricsResponse {
  data: Array<{
    public_metrics: {
      retweet_count: number;
      reply_count: number;
      like_count: number;
      quote_count: number;
      impression_count?: number;
    };
  }>;
}

/**
 * Twitter Publisher implementation
 */
export class TwitterPublisher extends BaseSocialMediaPublisher {
  private config: TwitterConfig;
  
  /**
   * Create a new TwitterPublisher
   * @param accountId The account ID
   * @param tokenManager The token manager
   * @param errorHandler The error handler
   * @param rateLimiter The rate limiter
   * @param config Twitter-specific configuration
   */
  constructor(
    accountId: string,
    tokenManager: TokenManager,
    errorHandler: ErrorHandler,
    rateLimiter: RateLimiter,
    config: TwitterConfig
  ) {
    super(accountId, tokenManager, errorHandler, rateLimiter);
    this.config = config;
  }
  
  /**
   * Get the platform name
   * @returns The platform name
   */
  protected getPlatformName(): string {
    return 'twitter';
  }
  
  /**
   * Get the account information
   * @returns Promise resolving to account information
   */
  protected async getAccountInfo(): Promise<SocialMediaAccount> {
    if (this.account) {
      return this.account;
    }
    
    try {
      // Fetch account from database or API
      // This is a placeholder implementation
      this.account = {
        id: this.accountId,
        userId: 'user-id',
        platform: 'twitter',
        platformAccountId: 'twitter-account-id',
        accountName: 'Twitter Account Name',
        accountUsername: 'twitter_username',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      return this.account;
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'getAccountInfo');
    }
  }
  
  /**
   * Authenticate with Twitter
   * @returns Promise resolving to boolean indicating success
   */
  async authenticate(): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      
      // Verify token is valid by making a test API call
      const response = await axios.get(
        `${this.config.apiBaseUrl}/${this.config.apiVersion}/users/me`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      if (response.data && response.data.data && response.data.data.id) {
        return true;
      }
      
      return false;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        // Token is invalid, try to refresh
        return this.refreshToken();
      }
      
      return this.errorHandler.handle(error, this.getPlatformName(), 'authenticate');
    }
  }
  
  /**
   * Refresh the authentication token
   * @returns Promise resolving to boolean indicating success
   */
  async refreshToken(): Promise<boolean> {
    try {
      return await this.tokenManager.refreshToken(this.accountId);
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'refreshToken');
    }
  }
  
  /**
   * Publish content to Twitter
   * @param content The content to publish
   * @returns Promise resolving to the publish result
   */
  async publishPost(content: PostContent): Promise<PublishResult> {
    try {
      // Check rate limits
      const isWithinLimits = await this.checkRateLimit('publishPost');
      if (!isWithinLimits) {
        return {
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Twitter publishing rate limit exceeded',
            type: 'rate_limit',
            retryable: true
          }
        };
      }
      
      // Validate content
      const validationResult = await this.validateContent(content);
      if (!validationResult.valid) {
        return {
          success: false,
          error: {
            code: 'CONTENT_VALIDATION_FAILED',
            message: validationResult.issues.map(issue => issue.message).join(', '),
            type: 'content_policy',
            retryable: false
          }
        };
      }
      
      const accessToken = await this.getAccessToken();
      
      // Handle media upload if present
      let mediaIds: string[] = [];
      
      if (content.media && content.media.length > 0) {
        mediaIds = await this.uploadMedia(content.media, accessToken);
      }
      
      // Create tweet
      return this.createTweet(content, mediaIds, accessToken);
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          type: 'generic',
          retryable: true
        }
      };
    }
  }
  
  /**
   * Upload media to Twitter
   * @param media The media to upload
   * @param accessToken The access token
   * @returns Promise resolving to array of media IDs
   */
  private async uploadMedia(media: MediaContent[], accessToken: string): Promise<string[]> {
    const mediaIds: string[] = [];
    
    // Twitter allows up to 4 media items per tweet
    const mediaToUpload = media.slice(0, 4);
    
    for (const item of mediaToUpload) {
      try {
        // For simplicity, we're assuming the media URL is directly accessible
        // In a real implementation, we would download the media and upload it as a buffer
        
        // Step 1: INIT - Initialize media upload
        const initResponse = await axios.post(
          'https://upload.twitter.com/1.1/media/upload.json',
          {
            command: 'INIT',
            total_bytes: 0, // Would be actual file size
            media_type: item.type === 'image' ? 'image/jpeg' : 'video/mp4'
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
        
        const mediaId = initResponse.data.media_id_string;
        
        // Step 2: APPEND - Upload media chunks (simplified)
        // In a real implementation, we would upload the actual media in chunks
        
        // Step 3: FINALIZE - Finalize media upload
        await axios.post(
          'https://upload.twitter.com/1.1/media/upload.json',
          {
            command: 'FINALIZE',
            media_id: mediaId
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
        
        mediaIds.push(mediaId);
      } catch (error) {
        this.errorHandler.logError(error, { operation: 'uploadMedia', mediaUrl: item.url });
        // Continue with other media items
      }
    }
    
    return mediaIds;
  }
  
  /**
   * Create a tweet
   * @param content The content to tweet
   * @param mediaIds Array of media IDs to attach
   * @param accessToken The access token
   * @returns Promise resolving to the publish result
   */
  private async createTweet(content: PostContent, mediaIds: string[], accessToken: string): Promise<PublishResult> {
    try {
      const tweetText = this.formatTweetText(content);
      
      const tweetData: any = {
        text: tweetText
      };
      
      // Add media if present
      if (mediaIds.length > 0) {
        tweetData.media = {
          media_ids: mediaIds
        };
      }
      
      const response = await axios.post(
        `${this.config.apiBaseUrl}/${this.config.apiVersion}/tweets`,
        tweetData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      const tweetResponse: TwitterTweetResponse = response.data;
      const tweetId = tweetResponse.data.id;
      
      return {
        success: true,
        postId: tweetId,
        platformUrl: `https://twitter.com/i/web/status/${tweetId}`
      };
    } catch (error) {
      return this.handlePublishError(error);
    }
  }
  
  /**
   * Format tweet text with hashtags
   * @param content The post content
   * @returns Formatted tweet text
   */
  private formatTweetText(content: PostContent): string {
    let text = content.text || '';
    
    // Add hashtags if present
    if (content.hashtags && content.hashtags.length > 0) {
      // Check if we need to add a newline
      if (!text.endsWith('\n')) {
        text += '\n\n';
      }
      
      text += content.hashtags.map(tag => `#${tag.replace(/^#/, '')}`).join(' ');
    }
    
    // Add links if present and there's room
    if (content.links && content.links.length > 0 && text.length < 260) { // Leave room for link
      // Check if we need to add a newline
      if (!text.endsWith('\n')) {
        text += '\n\n';
      }
      
      text += content.links[0]; // Add only the first link
    }
    
    // Twitter has a 280 character limit
    if (text.length > 280) {
      text = text.substring(0, 277) + '...';
    }
    
    return text;
  }
  
  /**
   * Handle publishing errors
   * @param error The error to handle
   * @returns Publish result with error information
   */
  private handlePublishError(error: any): PublishResult {
    if (axios.isAxiosError(error)) {
      const response = error.response;
      
      if (response) {
        const data = response.data;
        const errors = data?.errors || [];
        const errorCode = errors[0]?.code || 'UNKNOWN_ERROR';
        const errorMessage = errors[0]?.message || 'Unknown error occurred';
        
        // Map Twitter error codes to our error types
        let errorType: 'auth' | 'rate_limit' | 'content_policy' | 'media_format' | 'network' | 'generic' = 'generic';
        let retryable = false;
        
        switch (errorCode) {
          case 32: // Could not authenticate you
          case 89: // Invalid or expired token
          case 99: // Unable to verify your credentials
            errorType = 'auth';
            retryable = true;
            break;
          case 88: // Rate limit exceeded
          case 185: // User is over daily status update limit
            errorType = 'rate_limit';
            retryable = true;
            break;
          case 186: // Tweet needs to be a bit shorter
          case 187: // Status is a duplicate
          case 226: // This request looks like it might be automated
            errorType = 'content_policy';
            retryable = false;
            break;
          case 324: // Media upload problem
          case 325: // Media upload validation failed
            errorType = 'media_format';
            retryable = false;
            break;
          default:
            if (response.status === 429) {
              errorType = 'rate_limit';
              retryable = true;
            } else if (response.status >= 500) {
              errorType = 'network';
              retryable = true;
            }
        }
        
        return {
          success: false,
          error: {
            code: errorCode.toString(),
            message: errorMessage,
            type: errorType,
            retryable
          }
        };
      }
    }
    
    // Generic error handling
    return {
      success: false,
      error: {
        code: 'UNKNOWN_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        type: 'generic',
        retryable: true
      }
    };
  }
  
  /**
   * Get the status of a published tweet
   * @param tweetId The Twitter tweet ID
   * @returns Promise resolving to the post status
   */
  async getPostStatus(tweetId: string): Promise<PostStatusInfo> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await axios.get(
        `${this.config.apiBaseUrl}/${this.config.apiVersion}/tweets/${tweetId}`,
        {
          params: {
            'tweet.fields': 'public_metrics,created_at'
          },
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      if (response.data && response.data.data) {
        const tweet = response.data.data;
        const metrics = tweet.public_metrics || {};
        
        const engagement = {
          likes: metrics.like_count || 0,
          comments: metrics.reply_count || 0,
          shares: metrics.retweet_count || 0,
          views: metrics.impression_count || 0
        };
        
        return {
          id: tweetId,
          platform: 'twitter',
          status: 'published',
          publishedAt: new Date(tweet.created_at),
          engagement
        };
      }
      
      return {
        id: tweetId,
        platform: 'twitter',
        status: 'failed'
      };
    } catch (error) {
      // If tweet not found, return failed status
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return {
          id: tweetId,
          platform: 'twitter',
          status: 'failed'
        };
      }
      
      return this.errorHandler.handle(error, this.getPlatformName(), 'getPostStatus');
    }
  }
  
  /**
   * Delete a tweet from Twitter
   * @param tweetId The Twitter tweet ID
   * @returns Promise resolving to boolean indicating success
   */
  async deletePost(tweetId: string): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await axios.delete(
        `${this.config.apiBaseUrl}/${this.config.apiVersion}/tweets/${tweetId}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      return response.data?.data?.deleted === true;
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'deletePost');
    }
  }
  
  /**
   * Validate content against Twitter's policies
   * @param content The content to validate
   * @returns Promise resolving to validation result
   */
  async validateContent(content: PostContent): Promise<ContentValidationResult> {
    const issues: ContentIssue[] = [];
    
    // Check text length
    const formattedText = this.formatTweetText({
      ...content,
      hashtags: [], // Temporarily remove hashtags to check base text length
      links: [] // Temporarily remove links to check base text length
    });
    
    if (formattedText.length > 280) {
      issues.push({
        type: 'format_issue',
        severity: 'error',
        message: 'Tweet text cannot exceed 280 characters'
      });
    }
    
    // Check media count
    if (content.media && content.media.length > 4) {
      issues.push({
        type: 'format_issue',
        severity: 'warning',
        message: 'Twitter allows a maximum of 4 media items per tweet, only the first 4 will be used'
      });
    }
    
    // Check for restricted cannabis terms
    // Twitter is less restrictive than other platforms, but still has some limitations
    const restrictedTerms = [
      'buy weed', 'buy cannabis', 'order weed', 'order cannabis',
      'weed delivery', 'cannabis delivery'
    ];
    
    const text = content.text || '';
    
    for (const term of restrictedTerms) {
      const regex = new RegExp(`\\b${term}\\b`, 'gi');
      if (regex.test(text)) {
        issues.push({
          type: 'restricted_term',
          severity: 'warning',
          message: `The term "${term}" may violate Twitter's content policy`,
          position: {
            start: text.toLowerCase().indexOf(term.toLowerCase()),
            end: text.toLowerCase().indexOf(term.toLowerCase()) + term.length
          }
        });
      }
    }
    
    // Generate suggested fixes if issues found
    const suggestedFixes = issues.length > 0 ? this.generateSuggestedFixes(content, issues) : undefined;
    
    return {
      valid: issues.filter(issue => issue.severity === 'error').length === 0,
      issues,
      suggestedFixes
    };
  }
  
  /**
   * Generate suggested fixes for content issues
   * @param content The original content
   * @param issues The identified issues
   * @returns Array of suggested fixes
   */
  private generateSuggestedFixes(content: PostContent, issues: ContentIssue[]): string[] {
    const fixes: string[] = [];
    
    // Fix for text length
    if (content.text && content.text.length > 280) {
      fixes.push('Shorten your tweet to 280 characters or fewer');
      
      // Suggest a shortened version
      const shortened = content.text.substring(0, 277) + '...';
      fixes.push(`Suggested shorter text: "${shortened}"`);
    }
    
    // Fix for media count
    if (content.media && content.media.length > 4) {
      fixes.push('Reduce the number of media items to 4 or fewer');
    }
    
    // Fix for restricted terms
    const restrictedTermIssues = issues.filter(issue => issue.type === 'restricted_term');
    if (restrictedTermIssues.length > 0) {
      const terms = restrictedTermIssues.map(issue => issue.message.match(/"([^"]+)"/)?.[1]).filter(Boolean);
      fixes.push(`Consider replacing these terms: ${terms.join(', ')}`);
      
      // Suggest alternative terms
      const alternativeTerms: Record<string, string[]> = {
        'buy weed': ['shop', 'browse products'],
        'buy cannabis': ['shop', 'browse products'],
        'order weed': ['shop', 'browse products'],
        'order cannabis': ['shop', 'browse products'],
        'weed delivery': ['delivery service', 'shipping'],
        'cannabis delivery': ['delivery service', 'shipping']
      };
      
      for (const term of terms) {
        if (term && alternativeTerms[term]) {
          fixes.push(`Replace "${term}" with: ${alternativeTerms[term].join(', ')}`);
        }
      }
    }
    
    return fixes;
  }
  
  /**
   * Get analytics for a specific tweet
   * @param tweetId The Twitter tweet ID
   * @returns Promise resolving to post analytics
   */
  async getPostAnalytics(tweetId: string): Promise<PostAnalytics> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await axios.get(
        `${this.config.apiBaseUrl}/${this.config.apiVersion}/tweets/${tweetId}`,
        {
          params: {
            'tweet.fields': 'public_metrics,non_public_metrics,organic_metrics,promoted_metrics'
          },
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      const tweet = response.data.data;
      const publicMetrics = tweet.public_metrics || {};
      const organicMetrics = tweet.organic_metrics || {};
      
      // Extract metrics
      const metrics = {
        likes: publicMetrics.like_count || 0,
        comments: publicMetrics.reply_count || 0,
        shares: publicMetrics.retweet_count + (publicMetrics.quote_count || 0),
        impressions: organicMetrics.impression_count || 0,
        engagementRate: 0
      };
      
      // Calculate engagement rate
      if (metrics.impressions > 0) {
        metrics.engagementRate = ((metrics.likes + metrics.comments + metrics.shares) / metrics.impressions) * 100;
      }
      
      return {
        postId: 'internal-post-id', // This would be the internal post ID
        platformPostId: tweetId,
        platform: 'twitter',
        metrics,
        collectedAt: new Date()
      };
    } catch (error) {
      return this.errorHandler.handle(error, this.getPlatformName(), 'getPostAnalytics');
    }
  }
}
