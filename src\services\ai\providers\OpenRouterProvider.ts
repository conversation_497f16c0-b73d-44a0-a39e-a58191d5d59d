/**
 * OpenRouter AI Provider
 * 
 * Premium provider for critical tasks and fallback scenarios
 * Provides access to multiple high-quality models through OpenRouter
 */

import { BaseProvider, ProviderConfig, ProviderCapabilities } from './BaseProvider';
import { AIRequest, AIRequestOptions } from '../types/AIRequest';
import { AIResponse, AIProviderStatus } from '../types/AIResponse';

interface OpenRouterConfig extends ProviderConfig {
  base_url?: string;
  model?: string;
  site_url?: string;
  app_name?: string;
}

interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenRouterProvider extends BaseProvider {
  private baseUrl: string;
  private model: string;
  private siteUrl: string;
  private appName: string;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  
  constructor(config: OpenRouterConfig) {
    const capabilities: ProviderCapabilities = {
      supports_streaming: true,
      supports_images: true,
      supports_function_calling: true,
      supports_system_prompts: true,
      max_context_length: 128000, // Varies by model, using Claude-3 as reference
      supported_formats: ['json', 'text', 'markdown', 'html']
    };
    
    super('openrouter', config, capabilities);
    
    this.baseUrl = config.base_url || 'https://openrouter.ai/api/v1';
    this.model = config.model || 'anthropic/claude-3-haiku'; // Good balance of quality and cost
    this.siteUrl = config.site_url || 'https://bitsnbongs.com';
    this.appName = config.app_name || 'BitsnBongs AI System';
  }
  
  async processRequest(request: AIRequest, options?: AIRequestOptions): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.valid) {
        throw new Error(`Invalid request: ${validation.errors.join(', ')}`);
      }
      
      // Handle rate limiting
      await this.handleRateLimit();
      
      // Prepare messages
      const messages = this.prepareMessages(request);
      
      // Make API call with retry logic
      const response = await this.retryWithBackoff(async () => {
        return this.makeAPICall(messages, options);
      });
      
      // Track usage
      this.requestCount++;
      this.lastRequestTime = Date.now();
      
      // Calculate cost (varies by model, using rough estimates)
      const cost = this.calculateCost(response.usage, this.model);
      
      return {
        content: response.choices[0].message.content,
        success: true,
        provider: this.name,
        model: response.model,
        tokens_used: response.usage.total_tokens,
        cost: cost,
        processing_time: Date.now() - startTime,
        confidence_score: this.calculateConfidenceScore(response),
        request_id: request.request_id,
        response_id: response.id,
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('OpenRouter API error:', error);
      
      return {
        content: '',
        success: false,
        provider: this.name,
        processing_time: Date.now() - startTime,
        timestamp: new Date(),
        error: {
          code: 'OPENROUTER_API_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }
      };
    }
  }
  
  async processStreamingRequest(request: AIRequest, options?: AIRequestOptions): Promise<ReadableStream<string>> {
    const messages = this.prepareMessages(request);
    
    return new ReadableStream({
      async start(controller) {
        try {
          const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.api_key}`,
              'HTTP-Referer': this.siteUrl,
              'X-Title': this.appName
            },
            body: JSON.stringify({
              model: this.model,
              messages: messages,
              stream: true,
              max_tokens: options?.max_tokens || this.config.max_tokens || 2000,
              temperature: options?.temperature || this.config.temperature || 0.7
            })
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No response body');
          }
          
          const decoder = new TextDecoder();
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(content);
                  }
                } catch (e) {
                  // Skip invalid JSON
                }
              }
            }
          }
          
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }
  
  async checkHealth(): Promise<AIProviderStatus> {
    try {
      const testResponse = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.api_key}`,
          'HTTP-Referer': this.siteUrl
        }
      });
      
      return {
        provider: this.name,
        available: testResponse.ok,
        rate_limited: testResponse.status === 429,
        error_rate: 0,
        average_response_time: 0,
        last_check: new Date()
      };
      
    } catch (error) {
      return {
        provider: this.name,
        available: false,
        rate_limited: false,
        error_rate: 1,
        average_response_time: 0,
        last_check: new Date()
      };
    }
  }
  
  async getUsageStats(): Promise<{
    requests_today: number;
    tokens_today: number;
    cost_today: number;
    rate_limit_remaining: number;
  }> {
    return {
      requests_today: this.requestCount,
      tokens_today: 0,
      cost_today: 0,
      rate_limit_remaining: 200 // OpenRouter has generous rate limits
    };
  }
  
  async estimateCost(request: AIRequest): Promise<number> {
    const estimatedInputTokens = Math.ceil(request.content.length / 4);
    const estimatedOutputTokens = 500;
    
    // Cost varies by model, using Claude-3 Haiku as reference
    const inputCost = (estimatedInputTokens / 1000000) * 0.25; // $0.25 per 1M input tokens
    const outputCost = (estimatedOutputTokens / 1000000) * 1.25; // $1.25 per 1M output tokens
    
    return inputCost + outputCost;
  }
  
  private prepareMessages(request: AIRequest): OpenRouterMessage[] {
    const messages: OpenRouterMessage[] = [];
    
    // Add system prompt
    const systemPrompt = this.buildSystemPrompt(request);
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user message
    messages.push({
      role: 'user',
      content: request.content
    });
    
    return messages;
  }
  
  private buildSystemPrompt(request: AIRequest): string {
    const context = request.context;
    let prompt = '';
    
    // Base prompt based on request type
    switch (request.type) {
      case 'product_description':
        prompt = 'You are an expert e-commerce copywriter. Create compelling, accurate product descriptions that drive conversions while maintaining authenticity.';
        break;
      case 'blog_content':
        prompt = 'You are a professional content writer. Create engaging, well-researched content that provides value to readers and performs well in search engines.';
        break;
      case 'fraud_detection':
        prompt = 'You are a fraud detection expert. Analyze the provided data for potential fraudulent patterns and provide a detailed risk assessment.';
        break;
      case 'customer_analysis':
        prompt = 'You are a customer behavior analyst. Analyze customer data to provide actionable insights for business improvement.';
        break;
      default:
        prompt = 'You are a helpful AI assistant with expertise in e-commerce and business operations.';
    }
    
    // Add business context
    if (context?.business_type === 'cannabis') {
      prompt += ' You specialize in cannabis and CBD products, ensuring all content is compliant with relevant laws and regulations.';
    }
    
    // Add brand voice
    if (context?.brand_voice) {
      prompt += ` Maintain a ${context.brand_voice.tone} tone and embody this personality: ${context.brand_voice.personality}.`;
    }
    
    return prompt;
  }
  
  private async makeAPICall(messages: OpenRouterMessage[], options?: AIRequestOptions): Promise<OpenRouterResponse> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.api_key}`,
        'HTTP-Referer': this.siteUrl,
        'X-Title': this.appName
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        max_tokens: options?.max_tokens || this.config.max_tokens || 2000,
        temperature: options?.temperature || this.config.temperature || 0.7,
        top_p: options?.top_p || 0.9,
        frequency_penalty: options?.frequency_penalty || 0,
        presence_penalty: options?.presence_penalty || 0,
        stop: options?.stop_sequences
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    return response.json();
  }
  
  private calculateCost(usage: any, model: string): number {
    // OpenRouter pricing varies by model
    // Using Claude-3 Haiku as reference (good balance of quality/cost)
    const inputCost = (usage.prompt_tokens / 1000000) * 0.25;
    const outputCost = (usage.completion_tokens / 1000000) * 1.25;
    
    return inputCost + outputCost;
  }
  
  private calculateConfidenceScore(response: OpenRouterResponse): number {
    const choice = response.choices[0];
    if (!choice || choice.finish_reason !== 'stop') {
      return 0.7; // Lower confidence for incomplete responses
    }
    
    const contentLength = choice.message.content.length;
    if (contentLength < 50) {
      return 0.8; // Lower confidence for very short responses
    }
    
    return 0.95; // High confidence for OpenRouter (premium models)
  }
  
  protected async handleRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    // OpenRouter has generous rate limits, but we'll be respectful
    const minInterval = 500; // 0.5 seconds between requests
    
    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
