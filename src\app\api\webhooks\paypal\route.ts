import { NextRequest, NextResponse } from 'next/server';
import { getPaymentService } from '@/services/paymentService';

/**
 * PayPal IPN (Instant Payment Notification) webhook handler
 * This endpoint receives notifications from PayPal when a payment status changes
 */
export async function POST(request: NextRequest) {
  try {
    // Get the payment service
    const paymentService = getPaymentService();
    
    // Parse the IPN message
    const formData = await request.formData();
    
    // Extract relevant information
    const paymentStatus = formData.get('payment_status') as string;
    const txnId = formData.get('txn_id') as string;
    const invoiceId = formData.get('invoice') as string; // This is our session ID
    const receiverEmail = formData.get('receiver_email') as string;
    
    console.log('PayPal IPN received:', {
      paymentStatus,
      txnId,
      invoiceId,
      receiverEmail
    });
    
    // Verify the payment with PayPal
    // In a production environment, you should validate the IPN by sending it back to PayPal
    // https://developer.paypal.com/docs/api-basics/notifications/ipn/IPNImplementation/
    
    if (paymentStatus === 'Completed') {
      // Verify the payment
      const isVerified = await paymentService.verifyPayPalPayment(invoiceId, txnId);
      
      if (isVerified) {
        // Process the payment
        await paymentService.processSuccessfulPayment(invoiceId);
        
        console.log('Payment processed successfully');
      } else {
        console.error('Payment verification failed');
      }
    }
    
    // Always respond with a 200 status to acknowledge receipt
    return new NextResponse('OK', { status: 200 });
  } catch (error) {
    console.error('Error processing PayPal webhook:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}