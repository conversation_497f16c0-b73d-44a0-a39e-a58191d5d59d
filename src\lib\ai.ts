/**
 * Utility functions for AI content generation
 */

export type AIProvider = 'gemini' | 'deepseek' | 'openrouter';
export type ContentType = 'full' | 'outline' | 'intro' | 'conclusion' | 'topic' | 'newsletter' | 'social';

/**
 * Result from content generation, including text and optional image URL
 */
export interface ContentGenerationResult {
  text: string;
  imageUrl?: string;
}

interface GenerateContentOptions {
  provider?: AIProvider;
  contentType: ContentType;
  topic: string;
  title?: string;
  existingContent?: string;
  tone?: 'informative' | 'casual' | 'professional' | 'persuasive';
  length?: 'short' | 'medium' | 'long';
  platform?: 'twitter' | 'facebook' | 'instagram';
  productName?: string;
}

/**
 * Generate content using an AI provider
 */
export async function generateContent({
  provider = 'gemini',
  contentType,
  topic,
  title,
  existingContent,
  tone = 'informative',
  length = 'medium',
  platform,
  productName
}: GenerateContentOptions): Promise<string> {
  // Prepare the prompt based on the content type
  const prompt = createPrompt(contentType, topic, title, existingContent, tone, length, platform, productName);

  // Generate content using the selected provider
  let text = '';
  if (provider === 'gemini') {
    text = await generateWithGemini(prompt);
  } else if (provider === 'deepseek') {
    text = await generateWithDeepSeek(prompt);
  } else if (provider === 'openrouter') {
    text = await generateWithOpenRouter(prompt);
  } else {
    throw new Error(`Unsupported AI provider: ${provider}`);
  }

  // We're not searching for images here anymore - that's handled in the component
  return text;
}

/**
 * Create a prompt based on the content type and parameters
 */
function createPrompt(
  contentType: ContentType,
  topic: string,
  title?: string,
  existingContent?: string,
  tone: string = 'informative',
  length: string = 'medium',
  platform?: string,
  productName?: string
): string {
  const wordCount = length === 'short' ? '300-500' : length === 'medium' ? '500-800' : '800-1200';

  switch (contentType) {
    case 'full':
      return `Write a comprehensive blog post about "${topic}" with the title "${title || topic}".

        Requirements:
        - Word count: ${wordCount} words
        - Tone: ${tone}
        - Include an engaging introduction, main content with subsections, and a conclusion
        - Use markdown formatting (headings, lists, bold/italic)
        - Include 3-5 relevant subheadings
        - Add bullet points for key information
        - End with 2-3 discussion questions for readers

        Focus on providing valuable, accurate information about ${topic} in the cannabis/CBD industry.`;

    case 'outline':
      return `Create a detailed outline for a blog post about "${topic}" with the title "${title || topic}".

        The outline should include:
        1. Introduction (hook, background, thesis)
        2. 3-5 main sections with 2-3 subsections each
        3. Key points to cover in each section
        4. Conclusion and call-to-action
        5. 5 potential discussion questions

        Format as a hierarchical markdown list.`;

    case 'intro':
      return `Write an engaging introduction for a blog post about "${topic}" with the title "${title || topic}".

        The introduction should:
        - Hook the reader with an interesting fact or question
        - Provide context about why this topic matters
        - Clearly state what the article will cover
        - Be ${length === 'short' ? '1-2 paragraphs' : '2-3 paragraphs'} long
        - Use a ${tone} tone
        - End with a smooth transition to the main content`;

    case 'conclusion':
      return `Write a compelling conclusion for a blog post about "${topic}".

        The conclusion should:
        - Summarize the key points from the article
        - Reinforce the main message
        - Provide a clear takeaway for the reader
        - Include a call-to-action ${existingContent ? 'that relates to the following content: ' + existingContent.substring(0, 500) + '...' : ''}
        - Be ${length === 'short' ? '1 paragraph' : '1-2 paragraphs'} long
        - Use a ${tone} tone`;

    case 'topic':
      return `Generate 5 engaging blog post topics about ${topic} that would appeal to cannabis and CBD enthusiasts.

        For each topic, provide:
        1. A catchy title (title case, max 10 words)
        2. A 1-2 sentence description
        3. 3-5 key points to cover

        Format the response in markdown with clear headings for each topic.`;

    case 'newsletter':
      return `Create an engaging email newsletter about ${topic} for a CBD/cannabis e-commerce store.

        Requirements:
        - Word count: ${wordCount} words
        - Tone: ${tone}
        - Use British currency (£ GBP) for all pricing, never use dollars ($)
        - Include a compelling subject line suggestion at the top
        - Start with a friendly greeting to subscribers
        - Include 2-3 main sections about products, news, or tips
        - Add a special offer or promotion section
        - Include product highlights or featured items
        - End with a clear call-to-action to visit the store
        - Use simple HTML formatting that works well in emails
        - Include social media links section
        - Add unsubscribe reminder at the bottom

        The newsletter should be informative, engaging, and drive customers to visit our online store.
        Focus on building relationships with customers while promoting products naturally.`;

    case 'social':
      const platformSpecific = platform === 'twitter'
        ? 'Keep it under 280 characters and include 1-2 relevant hashtags.'
        : platform === 'instagram'
        ? 'Include 5-7 relevant hashtags at the end. Make it visually descriptive and engaging.'
        : platform === 'facebook'
        ? 'Write 2-3 paragraphs with an engaging question to encourage comments and interaction.'
        : 'Make it engaging and platform-appropriate.';

      return `Create an engaging ${platform || 'social media'} post about ${topic} for a CBD/cannabis e-commerce store.

        Requirements:
        - Tone: ${tone}
        - ${platformSpecific}
        - Include a clear call-to-action
        - Be conversational and authentic
        - Highlight benefits or unique selling points
        - Use emojis appropriately for the platform
        - Ensure compliance with social media platform policies

        ${productName ? `Feature this specific product: ${productName}` : ''}

        The post should encourage engagement and drive traffic to our online store.`;

    default:
      throw new Error(`Unsupported content type: ${contentType}`);
  }
}

/**
 * Generate content using the Gemini API
 */
async function generateWithGemini(prompt: string): Promise<string> {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

  if (!apiKey) {
    throw new Error('Gemini API key not found. Please add VITE_GEMINI_API_KEY to your .env file.');
  }

  const response = await fetch(
    "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-goog-api-key": apiKey,
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 2048,
          topP: 0.8,
          topK: 40,
        },
      }),
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Gemini API error:", errorText);
    throw new Error(`Error calling Gemini API: ${errorText}`);
  }

  const data = await response.json();
  return data.candidates?.[0]?.content?.parts?.[0]?.text || "Failed to generate content";
}

/**
 * Generate content using the DeepSeek API
 */
async function generateWithDeepSeek(prompt: string): Promise<string> {
  const apiKey = import.meta.env.VITE_DEEPSEEK_API_KEY;

  if (!apiKey) {
    throw new Error('DeepSeek API key not found. Please add VITE_DEEPSEEK_API_KEY to your .env file.');
  }

  // DeepSeek API implementation
  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      model: 'deepseek-chat',  // Using the latest DeepSeek-V3 model
      messages: [
        {
          role: 'system',
          content: 'You are a professional content writer specializing in the cannabis and CBD industry. Create high-quality, informative content that is well-structured using markdown formatting. Always use British currency (£ GBP) for pricing, never use dollars ($).',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.8,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("DeepSeek API error:", errorText);
    throw new Error(`Error calling DeepSeek API: ${errorText}`);
  }

  const data = await response.json();
  return data.choices?.[0]?.message?.content || "Failed to generate content";
}

/**
 * Generate content using the OpenRouter API (provides access to various LLMs)
 */
async function generateWithOpenRouter(prompt: string): Promise<string> {
  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

  if (!apiKey) {
    throw new Error('OpenRouter API key not found. Please add VITE_OPENROUTER_API_KEY to your .env file.');
  }

  // OpenRouter API implementation
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'HTTP-Referer': window.location.origin, // Required by OpenRouter
      'X-Title': 'Bits N Bongs Blog Generator' // Optional but recommended
    },
    body: JSON.stringify({
      model: 'anthropic/claude-3-opus:beta', // Using Claude 3 Opus for high-quality content
      messages: [
        {
          role: 'system',
          content: 'You are a professional content writer specializing in the cannabis and CBD industry. Create high-quality, informative content that is well-structured using markdown formatting. Always use British currency (£ GBP) for pricing, never use dollars ($).'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2048
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("OpenRouter API error:", errorText);
    throw new Error(`Error calling OpenRouter API: ${errorText}`);
  }

  const data = await response.json();
  return data.choices?.[0]?.message?.content || "Failed to generate content";
}
