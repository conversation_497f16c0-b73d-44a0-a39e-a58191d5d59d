// Script to fix image references in the database to match what's in Supabase storage
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('Error: Supabase key is missing. Please check your .env file.');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to fix image references in the database
async function fixImageReferences() {
  console.log('Starting to fix image references...');
  
  try {
    // Get the list of files in the Supabase storage
    const { data: storageFiles, error: storageError } = await supabase
      .storage
      .from('product-images')
      .list();
    
    if (storageError) {
      console.error('Error listing storage files:', storageError);
      return;
    }
    
    console.log(`Found ${storageFiles.length} files in Supabase storage`);
    
    // Create a map of base filenames (without extension) to full filenames
    const storageFileMap = {};
    storageFiles.forEach(file => {
      // Extract the base filename without extension
      const baseFilename = file.name.split('.')[0].replace(/~mv2$/, '');
      storageFileMap[baseFilename] = file.name;
      
      // Also map the filename with ~mv2 suffix
      storageFileMap[`${baseFilename}~mv2`] = file.name;
    });
    
    // Fetch all products
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products in the database`);
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Function to fix image URL
      const fixImageUrl = (imageUrl) => {
        if (!imageUrl) return null;
        
        // Skip if the URL doesn't point to Supabase storage
        if (!imageUrl.includes('/storage/v1/object/public/product-images/')) {
          return imageUrl;
        }
        
        // Extract the filename from the URL
        const filename = imageUrl.split('/').pop();
        
        // Remove any query parameters
        const cleanFilename = filename.split('?')[0];
        
        // Get the base filename without extension
        const baseFilename = cleanFilename.split('.')[0];
        
        // Check if we have this file in storage
        if (storageFileMap[baseFilename]) {
          // Use the actual filename from storage
          return `${supabaseUrl}/storage/v1/object/public/product-images/${storageFileMap[baseFilename]}`;
        }
        
        // If we don't have a match, return the original URL
        return imageUrl;
      };
      
      // Fix main image URL
      if (product.image) {
        const fixedImageUrl = fixImageUrl(product.image);
        if (fixedImageUrl && fixedImageUrl !== product.image) {
          updates.image = fixedImageUrl;
          needsUpdate = true;
        }
      }
      
      // Fix additional image URLs
      if (product.additional_images && Array.isArray(product.additional_images) && 
          product.additional_images.length > 0) {
        const fixedAdditionalImages = product.additional_images
          .map(fixImageUrl)
          .filter(Boolean);
        
        // Check if any URLs were changed
        const hasChanges = JSON.stringify(fixedAdditionalImages) !== 
                          JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = fixedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error fixing image references:', error);
  }
}

// Run the script
fixImageReferences().catch(console.error);
