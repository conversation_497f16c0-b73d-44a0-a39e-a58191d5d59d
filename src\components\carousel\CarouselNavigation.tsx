
import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CarouselNavigationProps {
  onPrevious: () => void;
  onNext: () => void;
  currentIndex: number;
  totalImages: number;
  isAutoPlaying: boolean;
  onDotClick: (index: number) => void;
}

const CarouselNavigation: React.FC<CarouselNavigationProps> = ({
  onPrevious,
  onNext,
  currentIndex,
  totalImages,
  isAutoPlaying,
  onDotClick,
}) => {
  return (
    <div className="absolute bottom-10 left-0 right-0 flex items-center justify-center z-20 gap-4">
      <button 
        onClick={onPrevious}
        className="w-10 h-10 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white transition-all duration-300 hover:scale-110"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      
      <div className="flex gap-2">
        {Array.from({ length: totalImages }).map((_, index) => (
          <button
            key={index}
            onClick={() => onDotClick(index)}
            className={`h-2 rounded-full transition-all ${
              currentIndex === index 
                ? "w-8 bg-white" 
                : "w-2 bg-white/50 hover:bg-white/70"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          >
            {currentIndex === index && isAutoPlaying && (
              <div
                className="h-full bg-white rounded-full animate-progressbar"
                style={{
                  animation: isAutoPlaying ? 'progressbar 5s linear' : 'none'
                }}
              />
            )}
          </button>
        ))}
      </div>
      
      <button 
        onClick={onNext}
        className="w-10 h-10 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white transition-all duration-300 hover:scale-110"
        aria-label="Next slide"
      >
        <ChevronRight className="h-6 w-6" />
      </button>
    </div>
  );
};

export default CarouselNavigation;
