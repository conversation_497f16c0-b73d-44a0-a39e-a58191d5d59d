import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * This component forces a refresh of shipping methods on mount
 * and periodically checks for updates to ensure the most current data.
 */
export function ForceShippingRefresh() {
  const queryClient = useQueryClient();

  useEffect(() => {
    console.log('ForceShippingRefresh: Setting up periodic refresh');
    
    // Immediately clear cache and force refresh
    const refreshNow = () => {
      console.log('ForceShippingRefresh: Forcing shipping data refresh');
      
      // First remove any cached queries
      queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
      
      // Then reset query cache to force refetch
      queryClient.resetQueries({ queryKey: ['checkout-shipping'] });
      
      // Set a cache buster in localStorage
      localStorage.setItem('shipping_refresh_timestamp', Date.now().toString());
    };
    
    // Do an immediate refresh
    refreshNow();
    
    // Then set up periodic refresh every 30 seconds
    const intervalId = setInterval(refreshNow, 30000);
    
    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [queryClient]);

  // This component doesn't render anything
  return null;
} 