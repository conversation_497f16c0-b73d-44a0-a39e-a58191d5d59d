import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * This component forces a refresh of shipping methods on mount,
 * but only does it once per session to prevent infinite refresh loops.
 */
export function ForceShippingRefresh() {
  const queryClient = useQueryClient();
  const [hasRefreshed, setHasRefreshed] = useState(false);

  useEffect(() => {
    // Check if we've refreshed in the last minute
    const lastRefresh = localStorage.getItem('last_shipping_refresh');
    const currentTime = Date.now();
    const shouldRefresh = !lastRefresh || (currentTime - parseInt(lastRefresh)) > 60000;
    
    if (shouldRefresh && !hasRefreshed) {
      // Wait a short delay to avoid refresh loops
      const timer = setTimeout(() => {
        console.log('ForceShippingRefresh: Clearing cache (once per 60 seconds)');
        
        // Clear shipping-related caches
        queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
        
        // Record that we've refreshed to prevent loops
        localStorage.setItem('last_shipping_refresh', currentTime.toString());
        setHasRefreshed(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [queryClient, hasRefreshed]);

  // This component doesn't render anything
  return null;
} 