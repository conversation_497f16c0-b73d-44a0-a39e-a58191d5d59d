import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * This component forces a refresh of shipping methods on mount.
 * Place it at the top of the checkout page to ensure shipping methods are always fresh.
 */
export function ForceShippingRefresh() {
  const queryClient = useQueryClient();

  useEffect(() => {
    // Clear all shipping-related caches on mount
    console.log('Force clearing shipping method caches');
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    queryClient.removeQueries({ queryKey: ['shipping-methods'] });
    
    // Force browser to clear cache using localStorage timestamp
    localStorage.setItem('shipping_cache_bust', Date.now().toString());

    // Return cleanup function to prevent memory leaks
    return () => {
      console.log('Cleaning up ForceShippingRefresh');
    };
  }, [queryClient]);

  // This component doesn't render anything
  return null;
} 