import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { parse } from 'papaparse';
import slugify from 'slugify';

// Define types for CSV product data
interface CSVProduct {
  name: string;
  description?: string;
  productImageUrl?: string;
  price?: string;
  productOptionName1?: string;
  productOptionType1?: string;
  productOptionDescription1?: string;
  productOptionName2?: string;
  productOptionType2?: string;
  productOptionDescription2?: string;
  productOptionName3?: string;
  productOptionType3?: string;
  productOptionDescription3?: string;
  additionalInfoTitle1?: string;
  additionalInfoDescription1?: string;
  additionalInfoTitle2?: string;
  additionalInfoDescription2?: string;
  additionalInfoTitle3?: string;
  additionalInfoDescription3?: string;
  [key: string]: any; // Allow other properties
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to CSV file
const csvFilePath = path.join(process.cwd(), 'docs', 'catalog_products.csv');

// Path to local images
const localImagesPath = path.join(process.cwd(), 'public', 'images', 'products', 'wix-imports');

// Currency symbol
const CURRENCY_SYMBOL = '£'; // Set to GBP

async function fixImportedProducts() {
  console.log('Starting product fix script...');
  
  // Read CSV file
  const csvFile = fs.readFileSync(csvFilePath, 'utf8');
  
  // Parse CSV
  const { data } = parse<CSVProduct>(csvFile, {
    header: true,
    skipEmptyLines: true
  });
  
  console.log(`Found ${data.length} products in CSV`);
  
  // Process each product
  let updated = 0;
  let failed = 0;
  
  for (const product of data) {
    try {
      // Generate slug from name
      const slug = slugify(product.name, { lower: true, strict: true });
      
      // Find the product in the database by slug
      const { data: existingProduct, error: findError } = await supabase
        .from('products')
        .select('*')
        .eq('slug', slug)
        .single();
      
      if (findError) {
        console.log(`Product not found: ${product.name} (${slug})`);
        failed++;
        continue;
      }
      
      // Process images
      let imageUrl = '';
      let additionalImageUrls: string[] = [];
      
      if (product.productImageUrl) {
        // Handle multiple images (semicolon-separated)
        const images = product.productImageUrl.split(';');
        
        if (images.length > 0) {
          // Process main image
          const firstImage = images[0].trim();
          imageUrl = await processImage(firstImage);
          
          // Process additional images
          if (images.length > 1) {
            for (const img of images.slice(1)) {
              if (img.trim()) {
                const processedUrl = await processImage(img.trim());
                if (processedUrl) {
                  additionalImageUrls.push(processedUrl);
                }
              }
            }
          }
        }
      }
      
      // Update product data
      const updateData: any = {
        description: product.description || existingProduct.description,
        currency_symbol: CURRENCY_SYMBOL,
      };
      
      // Only update image if we found one
      if (imageUrl) {
        updateData.image = imageUrl;
      }
      
      // Only update additional images if we found any
      if (additionalImageUrls.length > 0) {
        updateData.additional_images = additionalImageUrls;
      }
      
      // Update product options
      if (product.productOptionName1 && product.productOptionType1) {
        updateData.option_name1 = product.productOptionName1;
        updateData.option_type1 = product.productOptionType1;
        updateData.option_description1 = product.productOptionDescription1 || null;
      }
      
      if (product.productOptionName2 && product.productOptionType2) {
        updateData.option_name2 = product.productOptionName2;
        updateData.option_type2 = product.productOptionType2;
        updateData.option_description2 = product.productOptionDescription2 || null;
      }
      
      if (product.productOptionName3 && product.productOptionType3) {
        updateData.option_name3 = product.productOptionName3;
        updateData.option_type3 = product.productOptionType3;
        updateData.option_description3 = product.productOptionDescription3 || null;
      }
      
      // Update additional info
      if (product.additionalInfoTitle1 && product.additionalInfoDescription1) {
        updateData.additional_info_title1 = product.additionalInfoTitle1;
        updateData.additional_info_description1 = product.additionalInfoDescription1;
      }
      
      if (product.additionalInfoTitle2 && product.additionalInfoDescription2) {
        updateData.additional_info_title2 = product.additionalInfoTitle2;
        updateData.additional_info_description2 = product.additionalInfoDescription2;
      }
      
      if (product.additionalInfoTitle3 && product.additionalInfoDescription3) {
        updateData.additional_info_title3 = product.additionalInfoTitle3;
        updateData.additional_info_description3 = product.additionalInfoDescription3;
      }
      
      // Update the product in the database
      const { error: updateError } = await supabase
        .from('products')
        .update(updateData)
        .eq('id', existingProduct.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.name}:`, updateError);
        failed++;
      } else {
        console.log(`Updated product: ${product.name}`);
        updated++;
      }
    } catch (error) {
      console.error(`Error processing product ${product.name}:`, error);
      failed++;
    }
    
    // Log progress every 10 products
    if ((updated + failed) % 10 === 0) {
      console.log(`Progress: ${updated + failed}/${data.length} (${updated} updated, ${failed} failed)`);
    }
  }
  
  console.log(`Finished! ${updated} products updated, ${failed} failed`);
}

// Process image based on filename
async function processImage(imageUrl: string): Promise<string> {
  try {
    if (!imageUrl) return '';
    
    // If this is a Wix image filename (contains ~mv2)
    if (imageUrl.includes('~mv2')) {
      const baseFilename = imageUrl.split('.')[0];
      const extensions = ['webp', 'jpg', 'png', 'jpeg'];
      
      for (const ext of extensions) {
        const alternateFilename = `${baseFilename}.${ext}`;
        const alternatePath = path.join(localImagesPath, alternateFilename);
        
        // Check if file exists
        if (fs.existsSync(alternatePath)) {
          console.log(`Found image with extension .${ext}: ${alternateFilename}`);
          
          // Read the file
          const fileBuffer = fs.readFileSync(alternatePath);
          
          // Upload to Supabase storage with the original filename from CSV
          const { data, error } = await supabase.storage
            .from('product-images')
            .upload(imageUrl, fileBuffer, {
              contentType: `image/${ext}`,
              cacheControl: '3600',
              upsert: true
            });
          
          if (error) {
            console.error(`Error uploading image ${alternateFilename}:`, error);
            continue;
          }
          
          // Get the public URL
          const { data: publicUrlData } = supabase.storage
            .from('product-images')
            .getPublicUrl(imageUrl);
          
          return publicUrlData.publicUrl;
        }
      }
      
      console.log(`No matching image found for ${imageUrl}`);
      return '';
    }
    
    return '';
  } catch (error) {
    console.error(`Error processing image ${imageUrl}:`, error);
    return '';
  }
}

// Run the script
fixImportedProducts().catch(console.error);
