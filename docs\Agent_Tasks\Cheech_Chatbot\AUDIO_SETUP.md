# 🎵 Cheech Audio Integration Guide

## Overview

The Cheech chatbot now includes audio functionality with a custom welcome message featuring <PERSON><PERSON><PERSON>'s voice saying "Hey, welcome to Bits N Bongs! How can I help you, man?"

## 🔧 Setup Instructions

### 1. Audio File Placement

Place your audio files in the correct location:

```
public/
└── audio/
    ├── hey-welcom-to-bits-n-bongs-how-can-i-help-you-man-101soundboards.mp3
    ├── hey-dude-whats-up-need-a-hand-with-anything-man-101soundboards.mp3
    ├── no-worries-dude-i-will-get-right-on-that.mp3
    ├── hey-man-this-is-what-you-are-looking-for.mp3
    ├── sorry-dude-let-me-try-that-again.mp3
    └── catch-you-later-dude-happy-shopping.mp3
```

### 2. File Requirements

- **Format**: MP3 (recommended for best browser compatibility)
- **Size**: Keep under 1MB for fast loading
- **Quality**: 128kbps is sufficient for voice
- **Volume**: Pre-normalize to avoid volume issues

### 3. Browser Compatibility

Modern browsers have autoplay restrictions:

- ✅ **Chrome**: Allows autoplay after user interaction
- ✅ **Firefox**: Allows autoplay after user interaction
- ✅ **Safari**: Allows autoplay after user interaction
- ✅ **Edge**: Allows autoplay after user interaction

**Note**: The first time a user opens the chat, browsers may block autoplay. This is normal and expected behavior.

## 🎛️ Audio Controls

### Welcome Message Button (🔊)
- Plays Cheech's welcome message on demand
- Located in the chat header
- Always available when chat is open

### Audio Toggle (🔈/🔇)
- Enables/disables all audio functionality
- Visual indicator shows current state:
  - 🔈 = Audio enabled
  - 🔇 = Audio disabled
- User preference is maintained during the session

## 🔊 Audio Features

### Current Features
- ✅ **Alternating Welcome Messages**: Randomly plays one of two greetings
- ✅ **Contextual Response Audio**: Different audio for different situations
- ✅ **Manual Replay**: Click 🔊 button to replay welcome
- ✅ **Audio Toggle**: Enable/disable audio with 🔈/🔇 button
- ✅ **Volume Control**: Welcome at 70%, responses at 60%
- ✅ **Error Handling**: Graceful fallback if audio fails

### Audio Clip Usage

#### Welcome Messages (Alternates Randomly)
1. **"Hey, welcome to Bits N Bongs! How can I help you, man?"**
   - File: `hey-welcom-to-bits-n-bongs-how-can-i-help-you-man-101soundboards.mp3`
   - Plays: When chat first opens

2. **"Hey dude, what's up? Need a hand with anything, man?"**
   - File: `hey-dude-whats-up-need-a-hand-with-anything-man-101soundboards.mp3`
   - Plays: When chat first opens (alternate)

#### Response Audio (Context-Aware)
3. **"No worries dude, I'll get right on that"**
   - File: `no-worries-dude-i-will-get-right-on-that.mp3`
   - Plays: When user sends a message (thinking/processing)

4. **"Hey man, this is what you're looking for"**
   - File: `hey-man-this-is-what-you-are-looking-for.mp3`
   - Plays: When returning search results or product recommendations

5. **"Sorry dude, let me try that again"**
   - File: `sorry-dude-let-me-try-that-again.mp3`
   - Plays: When an error occurs

6. **"Catch you later dude, happy shopping!"**
   - File: `catch-you-later-dude-happy-shopping.mp3`
   - Plays: When user says goodbye or thanks

### Future Enhancements
- 🔄 **Response Audio**: Text-to-speech for Cheech responses
- 🔄 **Notification Sounds**: Subtle sounds for new messages
- 🔄 **Voice Input**: Speech-to-text for user messages
- 🔄 **Multiple Languages**: Audio in different languages

## 🎨 Customization

### Adding More Audio Files

1. **Create audio directory structure**:
```
public/audio/
├── welcome.mp3
├── notification.mp3
├── goodbye.mp3
└── error.mp3
```

2. **Update the audio paths in ChatInterface.tsx**:
```tsx
const playWelcomeAudio = () => {
  const audio = new Audio('/audio/welcome.mp3');
  // ... rest of the function
};
```

### Custom Audio Messages

You can add different audio messages for different scenarios:

```tsx
// Welcome message
const playWelcome = () => {
  const audio = new Audio('/audio/welcome.mp3');
  audio.play();
};

// Goodbye message
const playGoodbye = () => {
  const audio = new Audio('/audio/goodbye.mp3');
  audio.play();
};

// Error message
const playError = () => {
  const audio = new Audio('/audio/error.mp3');
  audio.play();
};
```

## 🛠️ Technical Implementation

### Audio Loading Strategy
- **Lazy Loading**: Audio files are loaded only when needed
- **Error Handling**: Graceful fallback if files are missing
- **Memory Management**: Audio objects are garbage collected after use

### Performance Considerations
- Audio files are cached by the browser after first load
- Small file sizes ensure fast loading
- No preloading to avoid unnecessary bandwidth usage

### Accessibility
- All audio controls have proper ARIA labels
- Keyboard navigation support
- Screen reader friendly
- Visual indicators for audio state

## 🐛 Troubleshooting

### Audio Not Playing
1. **Check file path**: Ensure audio file is in `/public/audio/`
2. **Check file format**: Use MP3 for best compatibility
3. **Check browser console**: Look for error messages
4. **Check autoplay policy**: User interaction may be required

### Volume Issues
1. **Normalize audio files**: Use audio editing software
2. **Adjust volume in code**: Modify `audio.volume` value
3. **Check system volume**: Ensure user's volume is up

### Browser Compatibility
1. **Test in multiple browsers**: Chrome, Firefox, Safari, Edge
2. **Check mobile devices**: iOS Safari, Chrome Mobile
3. **Fallback gracefully**: Ensure chat works without audio

## 📱 Mobile Considerations

- **iOS Safari**: May require user interaction before audio plays
- **Android Chrome**: Generally works well with autoplay policies
- **Data Usage**: Consider user's data plan with audio files
- **Battery**: Audio playback can impact battery life

## 🎯 Best Practices

1. **Keep files small**: Under 1MB for voice clips
2. **Use appropriate formats**: MP3 for voice, WAV for effects
3. **Provide controls**: Always allow users to disable audio
4. **Test thoroughly**: Check across devices and browsers
5. **Graceful degradation**: Ensure functionality without audio

## 🚀 Integration with AI Orchestration

The audio system works seamlessly with your existing AI orchestration:

- **No additional API costs**: Audio is client-side only
- **Maintains performance**: Audio doesn't impact AI response times
- **Scalable**: Can be extended with TTS integration later
- **Compliant**: Respects user preferences and accessibility guidelines

---

**Ready to give Cheech a voice!** 🎤 The audio integration adds personality and engagement to your chatbot while maintaining professional functionality.
