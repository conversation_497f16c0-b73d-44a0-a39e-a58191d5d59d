import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ImageProxyTest from '@/components/ImageProxyTest';

const TestImagesPage = () => {
  const [images, setImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        // List all files in the product-images bucket
        const { data, error } = await supabase.storage
          .from('product-images')
          .list();

        if (error) {
          throw error;
        }

        if (data) {
          // Get public URLs for each file
          const imageUrls = data.map(file => {
            const { data: urlData } = supabase.storage
              .from('product-images')
              .getPublicUrl(file.name);
            return {
              name: file.name,
              url: urlData.publicUrl
            };
          });

          console.log('Image URLs:', imageUrls);
          setImages(imageUrls.map(img => img.url));
        }
      } catch (err: any) {
        console.error('Error fetching images:', err);
        setError(err.message || 'Failed to fetch images');
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, []);

  // Test with hardcoded image paths
  const testImages = [
    '/product-images/2-piece-large-black-shredder.jpg',
    '/product-images/Ztrawberry_2g2e-9l.jpg.jpg',
    '/product-images/XGVQZFZ2RH3_1.jpg'
  ];

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Test Images Page</h1>

      <Tabs defaultValue="proxy-test" className="w-full mb-8">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="proxy-test">Image Proxy Test</TabsTrigger>
          <TabsTrigger value="storage-test">Storage Tests</TabsTrigger>
        </TabsList>
        
        <TabsContent value="proxy-test" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Image Proxy Test</CardTitle>
            </CardHeader>
            <CardContent>
              <ImageProxyTest />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="storage-test" className="mt-4">
          <div className="grid grid-cols-1 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Images from Public Directory</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {testImages.map((imagePath, index) => (
                    <div key={`local-${index}`} className="border rounded-md p-4">
                      <p className="mb-2 text-sm text-gray-500">{imagePath}</p>
                      <div className="aspect-square relative bg-gray-100 rounded-md overflow-hidden">
                        <img 
                          src={imagePath} 
                          alt={`Test image ${index + 1}`} 
                          className="object-cover w-full h-full"
                          onError={(e) => {
                            console.error(`Failed to load image: ${imagePath}`);
                            (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Images from Supabase Storage</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <p>Loading images...</p>
                ) : error ? (
                  <p className="text-red-500">{error}</p>
                ) : images.length === 0 ? (
                  <p>No images found in storage.</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {images.map((imageUrl, index) => (
                      <div key={index} className="border rounded-md p-4">
                        <p className="mb-2 text-sm text-gray-500 truncate">{imageUrl}</p>
                        <div className="aspect-square relative bg-gray-100 rounded-md overflow-hidden">
                          <img 
                            src={imageUrl} 
                            alt={`Storage image ${index + 1}`} 
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              console.error(`Failed to load image: ${imageUrl}`);
                              (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Direct URL Test</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="border rounded-md p-4">
                    <p className="mb-2 text-sm text-gray-500">Direct URL with CORS headers</p>
                    <div className="aspect-square relative bg-gray-100 rounded-md overflow-hidden">
                      <img 
                        src={`${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/product-images/2-piece-large-black-shredder.jpg`}
                        alt="Direct URL test" 
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          console.error('Failed to load direct URL image');
                          (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                        }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
        <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-96">
          {JSON.stringify({ 
            supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
            images: images,
            testImages: testImages
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestImagesPage;
