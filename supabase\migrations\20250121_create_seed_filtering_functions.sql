-- Create RPC functions for seed filtering system
-- These functions provide the backend logic for seed product filtering

-- Function to identify seed products based on name patterns and keywords
CREATE OR REPLACE FUNCTION is_seed_product(product_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  lower_name TEXT;
BEGIN
  lower_name := LOWER(product_name);
  
  -- Exclude non-seed items first
  IF lower_name ~ ANY(ARRAY[
    'bong', 'pipe', 'grinder', 'rolling', 'paper', 'filter', 'tip',
    'lighter', 'ashtray', 'scale', 'jar', 'container', 'bag', 'mylar',
    'gauge', 'mesh', 'screen', 'tool', 'accessory', 'vaporizer', 'vape',
    'cleaner', 'solution', 'brush', 'mat', 'tray', 'storage'
  ]) THEN
    RETURN FALSE;
  END IF;
  
  -- Check for direct seed indicators
  IF lower_name ~ ANY(ARRAY[
    'seed', 'seeds', 'auto', 'feminised', 'feminized', 'regular',
    'strain', 'autoflower', 'photoperiod', 'genetics'
  ]) THEN
    RETURN TRUE;
  END IF;
  
  -- Check for seed-specific patterns
  IF lower_name ~ ANY(ARRAY[
    '\d+\s*(female|feminised|feminized|regular|auto)',
    '\(.*?(female|feminised|feminized|regular|auto).*?\)',
    'x\d+', -- x5, x10 etc
    '(pack|seeds?)\s*of\s*\d+',
    '\d+\s*(pack|seeds?)',
    '(auto|feminised|feminized)\s+\w+',
    '\w+\s+(auto|feminised|feminized)'
  ]) THEN
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get seed products with filtering
CREATE OR REPLACE FUNCTION get_seed_products(
  p_active_only BOOLEAN DEFAULT TRUE,
  p_category_id UUID DEFAULT NULL,
  p_search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  slug TEXT,
  description TEXT,
  price NUMERIC,
  sale_price NUMERIC,
  image TEXT,
  sku TEXT,
  category_id UUID,
  subcategory_id UUID,
  brand_id UUID,
  is_active BOOLEAN,
  in_stock BOOLEAN,
  stock_quantity INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.slug,
    p.description,
    p.price,
    p.sale_price,
    p.image,
    p.sku,
    p.category_id,
    p.subcategory_id,
    p.brand_id,
    p.is_active,
    p.in_stock,
    p.stock_quantity,
    p.created_at,
    p.updated_at
  FROM products p
  WHERE 
    -- Filter by seed products only
    is_seed_product(p.name) = TRUE
    -- Filter by active status if requested
    AND (NOT p_active_only OR p.is_active = TRUE)
    -- Filter by category if provided
    AND (p_category_id IS NULL OR p.category_id = p_category_id)
    -- Filter by search term if provided
    AND (p_search_term IS NULL OR p.name ILIKE '%' || p_search_term || '%')
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to find inactive seed products
CREATE OR REPLACE FUNCTION find_inactive_seed_products()
RETURNS TABLE (
  id UUID,
  name TEXT,
  slug TEXT,
  description TEXT,
  price NUMERIC,
  sale_price NUMERIC,
  image TEXT,
  sku TEXT,
  category_id UUID,
  subcategory_id UUID,
  brand_id UUID,
  is_active BOOLEAN,
  in_stock BOOLEAN,
  stock_quantity INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.slug,
    p.description,
    p.price,
    p.sale_price,
    p.image,
    p.sku,
    p.category_id,
    p.subcategory_id,
    p.brand_id,
    p.is_active,
    p.in_stock,
    p.stock_quantity,
    p.created_at,
    p.updated_at
  FROM products p
  WHERE 
    p.is_active = FALSE
    AND is_seed_product(p.name) = TRUE
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get filtered seed products based on filter options
CREATE OR REPLACE FUNCTION get_filtered_seed_products(
  p_category_id UUID DEFAULT NULL,
  p_filter_options UUID[] DEFAULT NULL,
  p_active_only BOOLEAN DEFAULT TRUE
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  slug TEXT,
  description TEXT,
  price NUMERIC,
  sale_price NUMERIC,
  image TEXT,
  sku TEXT,
  category_id UUID,
  subcategory_id UUID,
  brand_id UUID,
  is_active BOOLEAN,
  in_stock BOOLEAN,
  stock_quantity INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT
    p.id,
    p.name,
    p.slug,
    p.description,
    p.price,
    p.sale_price,
    p.image,
    p.sku,
    p.category_id,
    p.subcategory_id,
    p.brand_id,
    p.is_active,
    p.in_stock,
    p.stock_quantity,
    p.created_at,
    p.updated_at
  FROM products p
  LEFT JOIN product_filters pf ON p.id = pf.product_id
  WHERE 
    -- Filter by seed products only
    is_seed_product(p.name) = TRUE
    -- Filter by active status if requested
    AND (NOT p_active_only OR p.is_active = TRUE)
    -- Filter by category if provided
    AND (p_category_id IS NULL OR p.category_id = p_category_id)
    -- Filter by filter options if provided
    AND (
      p_filter_options IS NULL 
      OR array_length(p_filter_options, 1) IS NULL
      OR pf.filter_option_id = ANY(p_filter_options)
    )
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get all available filter options for a category
CREATE OR REPLACE FUNCTION get_filter_options_for_category(p_category_name TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  display_name TEXT,
  display_order INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fo.id,
    fo.name,
    fo.display_name,
    fo.display_order
  FROM filter_options fo
  JOIN filter_categories fc ON fo.category_id = fc.id
  WHERE 
    fc.name = p_category_name
    AND fc.is_active = TRUE
    AND fo.is_active = TRUE
  ORDER BY fo.display_order, fo.display_name;
END;
$$ LANGUAGE plpgsql;

-- Function to get all filter categories
CREATE OR REPLACE FUNCTION get_filter_categories()
RETURNS TABLE (
  id UUID,
  name TEXT,
  display_name TEXT,
  display_order INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    fc.id,
    fc.name,
    fc.display_name,
    fc.display_order
  FROM filter_categories fc
  WHERE fc.is_active = TRUE
  ORDER BY fc.display_order, fc.display_name;
END;
$$ LANGUAGE plpgsql;
