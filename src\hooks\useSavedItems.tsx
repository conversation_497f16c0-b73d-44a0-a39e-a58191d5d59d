import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/auth.basic';
import { SavedItem } from '@/types/database';
import { Database } from '@/types/supabase';

type SavedItemWithProduct = SavedItem & {
  product: Database['public']['Tables']['products']['Row'];
};

interface SavedItemsContextType {
  savedItems: SavedItemWithProduct[];
  isLoading: boolean;
  saveForLater: (productId: string) => Promise<void>;
  removeFromSaved: (savedItemId: string) => Promise<void>;
  moveToCart: (savedItemId: string) => Promise<void>;
  isSaved: (productId: string) => boolean;
}

const SavedItemsContext = createContext<SavedItemsContextType | undefined>(undefined);

export const SavedItemsProvider = ({ children }: { children: ReactNode }) => {
  const [savedItems, setSavedItems] = useState<SavedItemWithProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  
  // Import useCart hook to move items to cart
  const { addToCart } = useCart();

  // Fetch saved items whenever the user changes
  useEffect(() => {
    const fetchSavedItems = async () => {
      if (!user) {
        setSavedItems([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('saved_items')
          .select(`
            *,
            product:products(*)
          `)
          .eq('user_id', user.id);

        if (error) throw error;
        
        // Always set to an empty array or the data we received
        setSavedItems((data || []) as SavedItemWithProduct[]);
      } catch (error) {
        console.error('Error fetching saved items:', error);
        toast({
          title: 'Error',
          description: 'Failed to load saved items',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSavedItems();
  }, [user]);

  // Check if a product is already saved
  const isSaved = (productId: string) => {
    return savedItems.some(item => item.product_id === productId);
  };

  // Save item for later
  const saveForLater = async (productId: string) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to save items',
        variant: 'destructive',
      });
      return;
    }

    // Check if already saved
    if (isSaved(productId)) {
      toast({
        title: 'Already Saved',
        description: 'This item is already in your saved items',
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('saved_items')
        .insert({
          user_id: user.id,
          product_id: productId,
        })
        .select(`
          *,
          product:products(*)
        `)
        .single();

      if (error) throw error;
      
      if (data) {
        setSavedItems([...savedItems, data as SavedItemWithProduct]);
        toast({
          title: 'Item Saved',
          description: 'Item saved for later',
        });
      }
    } catch (error) {
      console.error('Error saving item:', error);
      toast({
        title: 'Error',
        description: 'Failed to save item',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Remove item from saved items
  const removeFromSaved = async (savedItemId: string) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('saved_items')
        .delete()
        .eq('id', savedItemId)
        .eq('user_id', user.id);

      if (error) throw error;
      
      setSavedItems(savedItems.filter(item => item.id !== savedItemId));
      toast({
        title: 'Item Removed',
        description: 'Item removed from saved items',
      });
    } catch (error) {
      console.error('Error removing saved item:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove item',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Move item from saved to cart
  const moveToCart = async (savedItemId: string) => {
    if (!user) return;

    const savedItem = savedItems.find(item => item.id === savedItemId);
    if (!savedItem) return;

    try {
      // Add to cart first
      await addToCart(savedItem.product_id);
      
      // Then remove from saved items
      await removeFromSaved(savedItemId);
      
      toast({
        title: 'Moved to Cart',
        description: 'Item moved to your cart',
      });
    } catch (error) {
      console.error('Error moving item to cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to move item to cart',
        variant: 'destructive',
      });
    }
  };

  return (
    <SavedItemsContext.Provider 
      value={{ 
        savedItems, 
        isLoading, 
        saveForLater, 
        removeFromSaved, 
        moveToCart,
        isSaved
      }}
    >
      {children}
    </SavedItemsContext.Provider>
  );
};

// Need to import this after the component definition to avoid circular dependency
import { useCart } from '@/hooks/useCart';

export const useSavedItems = () => {
  const context = useContext(SavedItemsContext);
  if (context === undefined) {
    throw new Error('useSavedItems must be used within a SavedItemsProvider');
  }
  return context;
};
