import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * This component forcefully clears all shipping-related caches when mounted
 * Add it to the checkout page to ensure all caches are cleared
 */
export function ClearShippingCache() {
  const queryClient = useQueryClient();

  useEffect(() => {
    // Force clear all shipping caches
    console.log('🧹 ClearShippingCache: Forcefully clearing all shipping caches');
    
    // First remove all shipping-related queries
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    queryClient.removeQueries({ queryKey: ['shipping-methods'] });
    
    // Set a cache buster in localStorage
    const timestamp = Date.now();
    localStorage.setItem('shipping_cache_bust', timestamp.toString());
    localStorage.removeItem('last_shipping_refresh');
    
    // Force React Query cache to fully reset for shipping
    queryClient.resetQueries({ queryKey: ['checkout-shipping'] });
    
    // Log so we can see this happening
    console.log(`🧹 Cache cleared at ${new Date().toISOString()}`);
    
    // Poll the localStorage to ensure it's cleared across all tabs
    const interval = setInterval(() => {
      const curTimestamp = localStorage.getItem('shipping_cache_bust');
      if (curTimestamp !== timestamp.toString()) {
        localStorage.setItem('shipping_cache_bust', timestamp.toString());
      }
    }, 1000);
    
    return () => {
      clearInterval(interval);
    };
  }, [queryClient]);

  // This component doesn't render anything
  return null;
} 