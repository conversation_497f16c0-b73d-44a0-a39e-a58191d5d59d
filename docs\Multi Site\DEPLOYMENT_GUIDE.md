# Multi-Tenant SaaS Deployment Guide

This guide provides step-by-step instructions for deploying the multi-tenant architecture for the BitsNBongs cannabis/CBD e-commerce platform.

## Table of Contents

1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Environment Setup](#environment-setup)
3. [Database Migration](#database-migration)
4. [Application Deployment](#application-deployment)
5. [DNS and Domain Configuration](#dns-and-domain-configuration)
6. [Testing and Validation](#testing-and-validation)
7. [Monitoring Setup](#monitoring-setup)
8. [Rollback Procedures](#rollback-procedures)
9. [Post-Deployment Tasks](#post-deployment-tasks)

## Pre-Deployment Checklist

### ✅ Prerequisites

- [ ] Supabase project is set up and accessible
- [ ] Database backup is created and verified
- [ ] All team members are notified of deployment window
- [ ] Staging environment is tested and validated
- [ ] DNS records are prepared (but not yet applied)
- [ ] SSL certificates are ready
- [ ] Monitoring tools are configured

### ✅ Code Preparation

- [ ] All migration scripts are reviewed and tested
- [ ] Application code is tested with multi-tenant features
- [ ] Environment variables are configured
- [ ] Build process is verified
- [ ] Rollback scripts are prepared

### ✅ Infrastructure

- [ ] Production environment is provisioned
- [ ] Load balancers are configured
- [ ] CDN is set up for static assets
- [ ] Backup systems are in place
- [ ] Security measures are implemented

## Environment Setup

### 1. Environment Variables

Create or update your environment configuration:

```bash
# .env.production

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Multi-Tenant Configuration
NEXT_PUBLIC_BASE_DOMAIN=bitsnbongs.com
NEXT_PUBLIC_DEFAULT_TENANT=bitsnbongs
NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS=true

# Application Configuration
NEXTAUTH_URL=https://bitsnbongs.com
NEXTAUTH_SECRET=your_nextauth_secret

# Monitoring and Analytics
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
SENTRY_DSN=your_sentry_dsn

# Email Configuration (for tenant invitations)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# Payment Processing
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
```

### 2. Build Configuration

Update your `next.config.js` for multi-tenant support:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  
  // Enable rewrites for tenant routing
  async rewrites() {
    return {
      beforeFiles: [
        // Tenant-specific rewrites
        {
          source: '/t/:tenant/:path*',
          destination: '/:path*',
        },
      ],
    };
  },
  
  // Headers for tenant context
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

## Database Migration

### Phase 1: Foundation Setup

1. **Apply tenant system migration:**

```bash
# Using Supabase CLI
supabase db push

# Or apply specific migration
supabase migration up --target **************
```

2. **Verify tenant tables creation:**

```sql
-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('tenants', 'tenant_users');

-- Check default tenant
SELECT * FROM tenants WHERE slug = 'bitsnbongs';
```

### Phase 2: Schema Migration

1. **Apply tenant columns migration:**

```bash
supabase migration up --target 20250127000002
```

2. **Verify tenant_id columns:**

```sql
-- Check tenant_id columns
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE column_name = 'tenant_id'
AND table_schema = 'public';
```

3. **Populate existing data:**

```sql
-- Get default tenant ID
SELECT id FROM tenants WHERE slug = 'bitsnbongs';

-- Update existing records (replace with actual tenant ID)
UPDATE products SET tenant_id = 'default-tenant-id' WHERE tenant_id IS NULL;
UPDATE categories SET tenant_id = 'default-tenant-id' WHERE tenant_id IS NULL;
UPDATE brands SET tenant_id = 'default-tenant-id' WHERE tenant_id IS NULL;
-- Continue for all tables...
```

### Phase 3: RLS Implementation

1. **Apply RLS policies:**

```bash
supabase migration up --target 20250127000003
```

2. **Verify RLS policies:**

```sql
-- Check RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;

-- Check policy count
SELECT 
    schemaname,
    tablename,
    COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY schemaname, tablename;
```

### Phase 4: Testing and Validation

1. **Run isolation tests:**

```bash
supabase migration up --target 20250127000004
```

2. **Execute test functions:**

```sql
-- Run all tests
SELECT test_tenant_isolation();
SELECT test_tenant_performance();
SELECT validate_rls_policies();
SELECT check_tenant_data_distribution();
```

## Application Deployment

### 1. Build and Deploy

```bash
# Install dependencies
npm install

# Build application
npm run build

# Deploy to your platform (Vercel example)
vercel --prod

# Or deploy to other platforms
# npm run deploy:aws
# npm run deploy:gcp
# npm run deploy:azure
```

### 2. Verify Deployment

```bash
# Check application health
curl -f https://bitsnbongs.com/api/health

# Check tenant routing
curl -f https://demo.bitsnbongs.com/api/tenant/info

# Check authentication
curl -f https://bitsnbongs.com/api/auth/session
```

## DNS and Domain Configuration

### 1. Main Domain Setup

```dns
# A Records
bitsnbongs.com.     A     your_server_ip
www.bitsnbongs.com. A     your_server_ip

# CNAME for subdomains
*.bitsnbongs.com.   CNAME bitsnbongs.com.
```

### 2. SSL Certificate Setup

```bash
# Using Let's Encrypt (Certbot)
sudo certbot --nginx -d bitsnbongs.com -d *.bitsnbongs.com

# Or configure in your hosting platform
# Vercel: Automatic SSL
# Cloudflare: Enable SSL/TLS
# AWS: Use ACM certificates
```

### 3. CDN Configuration

```javascript
// Cloudflare example
const cdnConfig = {
  zone: 'bitsnbongs.com',
  settings: {
    ssl: 'full',
    minify: {
      css: 'on',
      js: 'on',
      html: 'on'
    },
    cache_level: 'aggressive'
  }
};
```

## Testing and Validation

### 1. Functional Testing

```bash
# Run test suite
npm run test

# Run E2E tests
npm run test:e2e

# Run multi-tenant specific tests
npm run test:tenant
```

### 2. Performance Testing

```bash
# Load testing with Artillery
artillery run load-test.yml

# Database performance
npm run test:db-performance

# Tenant isolation testing
npm run test:tenant-isolation
```

### 3. Security Testing

```bash
# Security scan
npm audit

# Tenant security validation
npm run test:security

# RLS policy testing
npm run test:rls
```

## Monitoring Setup

### 1. Application Monitoring

```javascript
// Sentry configuration
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  beforeSend(event) {
    // Filter sensitive tenant data
    if (event.user) {
      delete event.user.tenant_id;
    }
    return event;
  }
});
```

### 2. Database Monitoring

```sql
-- Create monitoring views
CREATE VIEW tenant_health AS
SELECT 
    t.id,
    t.name,
    t.status,
    COUNT(DISTINCT tu.user_id) as user_count,
    COUNT(DISTINCT p.id) as product_count,
    COUNT(DISTINCT o.id) as order_count
FROM tenants t
LEFT JOIN tenant_users tu ON t.id = tu.tenant_id
LEFT JOIN products p ON t.id = p.tenant_id
LEFT JOIN orders o ON t.id = o.tenant_id
GROUP BY t.id, t.name, t.status;
```

### 3. Performance Monitoring

```javascript
// Custom metrics
export const trackTenantMetrics = {
  switchTime: (tenantId, duration) => {
    analytics.track('tenant_switch', {
      tenant_id: tenantId,
      duration_ms: duration
    });
  },
  
  queryPerformance: (query, duration) => {
    analytics.track('query_performance', {
      query_type: query,
      duration_ms: duration
    });
  }
};
```

## Rollback Procedures

### 1. Application Rollback

```bash
# Vercel rollback
vercel rollback

# Manual rollback
git revert HEAD
npm run build
npm run deploy
```

### 2. Database Rollback

```sql
-- Disable RLS (emergency only)
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
-- Continue for all tables...

-- Remove tenant_id columns (if needed)
ALTER TABLE products DROP COLUMN IF EXISTS tenant_id;
ALTER TABLE categories DROP COLUMN IF EXISTS tenant_id;
-- Continue for all tables...

-- Drop tenant tables (last resort)
DROP TABLE IF EXISTS tenant_users;
DROP TABLE IF EXISTS tenants;
```

### 3. DNS Rollback

```bash
# Revert DNS changes
# Remove wildcard subdomain
# Point traffic back to original setup
```

## Post-Deployment Tasks

### 1. Data Migration Verification

```sql
-- Check data integrity
SELECT 
    'products' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END) as with_tenant_id
FROM products
UNION ALL
SELECT 
    'categories',
    COUNT(*),
    COUNT(CASE WHEN tenant_id IS NOT NULL THEN 1 END)
FROM categories;
```

### 2. Performance Optimization

```sql
-- Analyze query performance
ANALYZE;

-- Update statistics
VACUUM ANALYZE;

-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### 3. User Communication

```markdown
# Deployment Announcement Template

## 🎉 Multi-Tenant SaaS Platform is Live!

We've successfully deployed our new multi-tenant architecture. Here's what's new:

### ✨ New Features
- **Tenant Management**: Create and manage multiple stores
- **Subdomain Support**: Access your store at `yourstore.bitsnbongs.com`
- **Custom Domains**: Use your own domain (coming soon)
- **Enhanced Security**: Improved data isolation and security

### 🔧 For Existing Users
- Your data has been migrated to the new system
- You can access your store at the same URL
- All existing functionality remains the same

### 📞 Support
If you experience any issues, please contact <NAME_EMAIL>
```

### 4. Monitoring Dashboard Setup

```javascript
// Grafana dashboard configuration
const dashboardConfig = {
  title: 'Multi-Tenant SaaS Metrics',
  panels: [
    {
      title: 'Active Tenants',
      type: 'stat',
      query: 'SELECT COUNT(*) FROM tenants WHERE status = \'active\''
    },
    {
      title: 'Tenant Switch Time',
      type: 'graph',
      query: 'SELECT AVG(duration_ms) FROM tenant_switch_metrics'
    },
    {
      title: 'RLS Policy Performance',
      type: 'table',
      query: 'SELECT * FROM pg_stat_user_tables WHERE schemaname = \'public\''
    }
  ]
};
```

## Troubleshooting

### Common Issues

1. **Tenant Context Not Set**
   ```sql
   -- Check current tenant context
   SELECT get_current_tenant_id();
   
   -- Reset tenant context
   SELECT set_tenant_context('tenant-uuid-here');
   ```

2. **RLS Policy Blocking Queries**
   ```sql
   -- Temporarily bypass RLS for debugging (use with caution)
   SELECT bypass_rls_for_system();
   -- Your debugging queries here
   SELECT enable_rls_for_system();
   ```

3. **Performance Issues**
   ```sql
   -- Check index usage
   SELECT * FROM pg_stat_user_indexes WHERE idx_scan = 0;
   
   -- Add missing indexes
   CREATE INDEX CONCURRENTLY idx_products_tenant_id ON products(tenant_id);
   ```

### Emergency Contacts

- **Technical Lead**: [email]
- **Database Admin**: [email]
- **DevOps Engineer**: [email]
- **Product Manager**: [email]

---

## Deployment Checklist Summary

- [ ] Pre-deployment checklist completed
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Application deployed and verified
- [ ] DNS and SSL configured
- [ ] Testing completed successfully
- [ ] Monitoring systems active
- [ ] Rollback procedures documented
- [ ] Team notified of successful deployment
- [ ] Documentation updated
- [ ] User communication sent

**Deployment Status**: ✅ Complete
**Deployed By**: [Name]
**Deployment Date**: [Date]
**Version**: [Version Number]