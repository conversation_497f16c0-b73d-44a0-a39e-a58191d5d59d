
import React from 'react';
import { motion } from 'framer-motion';

interface CarouselSlideProps {
  imagePath: string;
  index: number;
  currentIndex: number;
  direction: number;
  totalImages: number;
}

const CarouselSlide: React.FC<CarouselSlideProps> = ({
  imagePath,
  index,
  currentIndex,
  direction,
  totalImages,
}) => {
  // Enhanced variants with stronger transitions
  const variants = {
    enter: {
      opacity: 0,
      zIndex: 0,
    },
    center: {
      opacity: 1,
      zIndex: 1,
      transition: {
        opacity: { duration: 1.2, ease: "easeInOut" },
      }
    },
    exit: {
      opacity: 0,
      zIndex: 0,
      transition: {
        opacity: { duration: 1.2, ease: "easeInOut" },
      }
    },
  };

  return (
    <motion.div
      key={index}
      className="absolute inset-0"
      custom={direction}
      variants={variants}
      initial="enter"
      animate={index === currentIndex ? "center" : "exit"}
      exit="exit"
      transition={{ duration: 1.2 }}
    >
      <div className="absolute inset-0">
        <motion.img 
          src={imagePath}
          alt={`Hero image ${index + 1}`}
          className="absolute inset-0 w-full h-full object-cover object-center"
          loading="eager"
          initial={{ opacity: 0.5 }}
          animate={{ 
            opacity: index === currentIndex ? 1 : 0,
            scale: index === currentIndex ? 1.05 : 1,  // Enhanced scale effect for current image
            y: index === currentIndex ? 0 : `${direction * 8}%`, // Stronger parallax movement
          }}
          transition={{ duration: 1.5, ease: "easeOut" }}
          onError={(e) => {
            console.error(`Error loading image: ${imagePath}`);
            // Fallback styling if image fails to load
            e.currentTarget.style.display = 'none';
            e.currentTarget.parentElement?.classList.add('flex', 'items-center', 'justify-center');
            if (e.currentTarget.parentElement) {
              e.currentTarget.parentElement.innerHTML += `<div class="text-lg text-gray-500">Image not found: ${imagePath}</div>`;
            }
          }}
        />
      </div>
      <motion.div 
        className="absolute inset-0 bg-gradient-to-br from-sage-900/70 to-clay-900/70"
        initial={{ opacity: 0.5 }}
        animate={{ opacity: 0.7 }}
        transition={{ duration: 1.5 }}
      />
    </motion.div>
  );
};

export default CarouselSlide;
