
import { useState } from 'react';
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { 
  Facebook, 
  Instagram, 
  Store, 
  MapPin, 
  Phone, 
  Clock, 
  Mail,
  Send
} from "lucide-react";
// Footer is already included in ShopLayout, but kept here for reference
import Footer from '@/components/Footer';
import { AspectRatio } from "@/components/ui/aspect-ratio";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Simple validation
    if (!formData.name || !formData.email || !formData.message) {
      toast.error("Please fill in all fields");
      return;
    }
    
    // In a real app, you would submit to a backend/API here
    toast.success("Thank you for your message! We'll get back to you soon.");
    setFormData({ name: '', email: '', message: '' });
  };

  // Store hours data
  const storeHours = [
    { day: "Monday", hours: "10 am–5:45 pm" },
    { day: "Tuesday", hours: "10 am–5:45 pm" },
    { day: "Wednesday", hours: "10 am–5:45 pm" },
    { day: "Thursday", hours: "10 am–6 pm" },
    { day: "Friday", hours: "10 am–5:45 pm" },
    { day: "Saturday", hours: "10 am–5 pm" },
    { day: "Sunday", hours: "11 am–4 pm" }
  ];

  const socialLinks = [
    {
      name: "Facebook",
      url: "https://www.facebook.com/Bitsnbongsuk/",
      icon: Facebook,
      color: "bg-blue-600"
    },
    {
      name: "Instagram", 
      url: "https://www.instagram.com/bitsandbongs/",
      icon: Instagram,
      color: "bg-gradient-to-r from-pink-500 via-purple-500 to-orange-500"
    },
    {
      name: "Etsy",
      url: "https://www.etsy.com/uk/shop/BitsNBongsUK",
      icon: Store,
      color: "bg-orange-600"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-grow py-12 bg-background">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 text-clay-900">Contact Us</h1>
            <p className="text-lg text-clay-700 max-w-2xl mx-auto">
              Have questions about our products? Want to place a special order? 
              We'd love to hear from you!
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-16">
            {/* Contact Information */}
            <div className="space-y-8">
              <Card className="overflow-hidden">
                <div className="h-48 relative bg-gradient-to-r from-sage-500 to-sage-700 flex items-center justify-center">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <h2 className="text-3xl font-bold text-white relative z-10">Visit Our Store</h2>
                </div>
                
                {/* Store image added here */}
                <div className="p-0">
                  <AspectRatio ratio={16/9}>
                    <img 
                      src="/lovable-uploads/c2c38775-64cf-4544-9607-19c952b61e98.png" 
                      alt="Bits N Bongs Store Interior" 
                      className="w-full h-full object-cover"
                    />
                  </AspectRatio>
                </div>
                
                <CardContent className="p-6">
                  <div className="space-y-6 text-lg">
                    <div className="flex items-start gap-3">
                      <MapPin className="text-sage-600 h-6 w-6 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-clay-900">Address:</h3>
                        <p className="text-clay-700">78 Stanley St, Kinning Park, Glasgow G41 1JH</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <Phone className="text-sage-600 h-6 w-6 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-clay-900">Phone:</h3>
                        <p className="text-clay-700">
                          <a href="tel:01417373717" className="hover:text-sage-600 transition-colors">
                            0141 737 3717
                          </a>
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <Mail className="text-sage-600 h-6 w-6 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-clay-900">Email:</h3>
                        <p className="text-clay-700">
                          <a href="mailto:<EMAIL>" className="hover:text-sage-600 transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator className="my-6" />
                  
                  <div className="flex items-start gap-3">
                    <Clock className="text-sage-600 h-6 w-6 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <h3 className="font-medium text-clay-900 mb-2">Store Hours:</h3>
                      <div className="space-y-1">
                        {storeHours.map((item, index) => (
                          <div key={index} className="flex justify-between text-clay-700">
                            <span className="font-medium">{item.day}</span>
                            <span>{item.hours}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator className="my-6" />
                  
                  <div>
                    <h3 className="font-medium text-clay-900 mb-3 flex items-center gap-2">
                      Follow Us
                    </h3>
                    <div className="flex gap-4">
                      {socialLinks.map((social, index) => {
                        const Icon = social.icon;
                        return (
                          <a 
                            key={index} 
                            href={social.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex"
                          >
                            <span className={`${social.color} text-white p-3 rounded-lg transition-transform hover:scale-110`}>
                              <Icon className="h-6 w-6" />
                            </span>
                          </a>
                        );
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div>
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-2xl font-bold text-clay-900 mb-6">Send Us a Message</h2>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-medium text-clay-800">
                        Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Your name"
                        value={formData.name}
                        onChange={handleChange}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium text-clay-800">
                        Email
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Your email address"
                        value={formData.email}
                        onChange={handleChange}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label htmlFor="message" className="text-sm font-medium text-clay-800">
                        Message
                      </label>
                      <Textarea
                        id="message"
                        name="message"
                        placeholder="How can we help you?"
                        rows={5}
                        value={formData.message}
                        onChange={handleChange}
                      />
                    </div>
                    
                    <Button type="submit" className="w-full flex items-center gap-2">
                      Send Message
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
          
          {/* Map */}
          <Card className="mb-16 overflow-hidden">
            <CardContent className="p-0">
              <AspectRatio ratio={16/7}>
                <iframe 
                  title="BITS N BONGS Store Location"
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2239.2965412282346!2d-4.2753520525591585!3d55.8498221705605!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x488845dba47fcb57%3A0x293ff7877a76cf4!2s78%20Stanley%20St%2C%20Kinning%20Park%2C%20Glasgow%20G41%201JH%2C%20UK!5e0!3m2!1sen!2sus!4v1716657562438!5m2!1sen!2sus" 
                  width="100%" 
                  height="100%" 
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="w-full h-full"
                />
              </AspectRatio>
            </CardContent>
          </Card>
        </div>
      </main>
      {/* Footer is included in ShopLayout */}
    </div>
  );
};

export default ContactPage;
