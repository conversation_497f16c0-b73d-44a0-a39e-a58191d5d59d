import { supabase } from '../integrations/supabase/client';
import slugify from 'slugify';

// Define test products
const testProducts = [
  {
    name: 'Premium Black Shredder',
    description: 'High-quality 2-piece large black shredder with sharp teeth for perfect grinding. Made from durable aircraft-grade aluminum with a smooth finish.',
    price: 19.99,
    sale_price: null,
    image: '/product-images/2-piece-large-black-shredder.jpg',
    category_id: null, // We'll update this once we have categories
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: false,
  },
  {
    name: 'Strawberry Dream Glass Pipe',
    description: 'Beautiful hand-blown glass pipe with strawberry-inspired design. Features a deep bowl and comfortable grip for an enhanced smoking experience.',
    price: 24.99,
    sale_price: 19.99,
    image: '/product-images/Ztrawberry_2g2e-9l.jpg.jpg',
    category_id: null,
    in_stock: true,
    is_featured: true,
    is_new: false,
    is_best_seller: true,
  },
  {
    name: 'Deluxe Water Pipe',
    description: 'Premium water pipe with percolator for smooth filtration. Made from high-quality borosilicate glass with reinforced joints for durability.',
    price: 89.99,
    sale_price: null,
    image: '/product-images/XGVQZFZ2RH3_1.jpg',
    category_id: null,
    in_stock: true,
    is_featured: true,
    is_new: true,
    is_best_seller: true,
  }
];

// Function to add test categories
async function addTestCategories() {
  console.log('Adding test categories...');
  
  const categories = [
    { name: 'Grinders', slug: 'grinders', description: 'High-quality grinders for all your needs' },
    { name: 'Glass Pipes', slug: 'glass-pipes', description: 'Beautiful hand-blown glass pipes' },
    { name: 'Water Pipes', slug: 'water-pipes', description: 'Premium water pipes and bongs' },
  ];
  
  const { data, error } = await supabase
    .from('categories')
    .upsert(categories, { onConflict: 'slug' })
    .select();
  
  if (error) {
    console.error('Error adding categories:', error);
    return null;
  }
  
  console.log('Categories added successfully:', data);
  return data;
}

// Function to add test products
async function addTestProducts(categories: any[]) {
  console.log('Adding test products...');
  
  // Map category names to IDs
  const categoryMap = categories.reduce((map, category) => {
    map[category.name.toLowerCase()] = category.id;
    return map;
  }, {});
  
  // Assign categories to products
  const productsWithCategories = testProducts.map(product => {
    const slug = slugify(product.name, { lower: true, strict: true });
    let categoryId = null;
    
    if (product.name.toLowerCase().includes('shredder') || product.name.toLowerCase().includes('grinder')) {
      categoryId = categoryMap['grinders'];
    } else if (product.name.toLowerCase().includes('glass pipe')) {
      categoryId = categoryMap['glass pipes'];
    } else if (product.name.toLowerCase().includes('water pipe') || product.name.toLowerCase().includes('bong')) {
      categoryId = categoryMap['water pipes'];
    }
    
    return {
      ...product,
      slug,
      category_id: categoryId,
    };
  });
  
  // Insert products
  const { data, error } = await supabase
    .from('products')
    .upsert(productsWithCategories, { onConflict: 'slug' })
    .select();
  
  if (error) {
    console.error('Error adding products:', error);
    return;
  }
  
  console.log('Products added successfully:', data);
}

// Main function to run the script
async function main() {
  try {
    const categories = await addTestCategories();
    if (categories) {
      await addTestProducts(categories);
    }
    console.log('Test data added successfully!');
  } catch (error) {
    console.error('Error adding test data:', error);
  }
}

// Run the script
main();
