'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShippingAddressForm, ShippingAddressFormValues } from '@/components/checkout/ShippingAddressForm';
import { AddressList } from '@/components/checkout/AddressList';
import { ShippingMethodSelector, ShippingMethod } from '@/components/checkout/ShippingMethodSelector';
import { useAddresses, Address } from '@/hooks/useAddresses';
import { useAuth } from '@/hooks/auth.basic';
import { useToast } from '@/components/ui/use-toast';
import { Plus } from 'lucide-react';
import { ForceShippingRefresh } from './ForceShippingRefresh';
import { ShippingRefreshButton } from './ShippingRefreshButton';
import { useCheckoutShipping } from '@/hooks/useShipping';

interface ShippingStepProps {
  selectedAddressId?: string;
  onAddressSelect: (address: Address) => void;
  selectedShippingMethodId: string;
  shippingMethods: ShippingMethod[];
  onShippingMethodSelect: (methodId: string) => void;
  onContinue: () => void;
  isShippingLoading?: boolean;
}

export function ShippingStep({
  selectedAddressId,
  onAddressSelect,
  selectedShippingMethodId,
  shippingMethods,
  onShippingMethodSelect,
  onContinue,
  isShippingLoading = false,
}: ShippingStepProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    addresses,
    isLoading,
    addAddress,
    updateAddress,
    deleteAddress,
    setAsDefault,
  } = useAddresses();
  const { refreshShippingMethods } = useCheckoutShipping();

  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle adding a new address
  const handleAddAddress = async (data: ShippingAddressFormValues) => {
    setIsSubmitting(true);
    try {
      const newAddress = await addAddress({
        full_name: data.full_name,
        street: data.street,
        city: data.city,
        state: data.state,
        postal_code: data.postal_code,
        country: data.country,
        phone: data.phone,
        is_default: data.save_address,
      });

      if (newAddress) {
        onAddressSelect(newAddress);
        setShowAddressForm(false);
      }
    } catch (error) {
      console.error('Error adding address:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle updating an existing address
  const handleUpdateAddress = async (data: ShippingAddressFormValues) => {
    if (!editingAddress) return;

    setIsSubmitting(true);
    try {
      const success = await updateAddress(editingAddress.id, {
        full_name: data.full_name,
        street: data.street,
        city: data.city,
        state: data.state,
        postal_code: data.postal_code,
        country: data.country,
        phone: data.phone,
        is_default: data.save_address,
      });

      if (success) {
        // Find the updated address in the list
        const updatedAddress = addresses.find(addr => addr.id === editingAddress.id);
        if (updatedAddress) {
          onAddressSelect(updatedAddress);
        }
        setEditingAddress(null);
      }
    } catch (error) {
      console.error('Error updating address:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission (add or update)
  const handleSubmitAddress = async (data: ShippingAddressFormValues) => {
    if (editingAddress) {
      await handleUpdateAddress(data);
    } else {
      await handleAddAddress(data);
    }
  };

  // Handle continue button click
  const handleContinue = () => {
    if (!selectedAddressId) {
      toast({
        title: 'Address Required',
        description: 'Please select a shipping address to continue',
        variant: 'destructive',
      });
      return;
    }
    onContinue();
  };

  return (
    <>
      {/* This component doesn't render anything visible, but forces a refresh */}
      <ForceShippingRefresh />

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Shipping Address</CardTitle>
          {!showAddressForm && !editingAddress && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAddressForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Address
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {!user ? (
            <div className="text-center py-4">
              <p className="mb-4">Please sign in to save your shipping addresses</p>
              <Button variant="default" onClick={() => {}}>
                Sign In
              </Button>
            </div>
          ) : showAddressForm || editingAddress ? (
            <div className="space-y-4">
              <ShippingAddressForm
                onSubmit={handleSubmitAddress}
                initialData={editingAddress ? {
                  full_name: editingAddress.full_name,
                  street: editingAddress.street,
                  city: editingAddress.city,
                  state: editingAddress.state || '',
                  postal_code: editingAddress.postal_code,
                  country: editingAddress.country,
                  phone: editingAddress.phone,
                  save_address: editingAddress.is_default,
                } : undefined}
                isLoading={isSubmitting}
                buttonText={editingAddress ? 'Update Address' : 'Save Address'}
              />
              <Button
                variant="outline"
                className="w-full mt-2"
                onClick={() => {
                  setShowAddressForm(false);
                  setEditingAddress(null);
                }}
              >
                Cancel
              </Button>
            </div>
          ) : addresses.length > 0 ? (
            <AddressList
              addresses={addresses}
              selectedAddressId={selectedAddressId}
              onSelect={onAddressSelect}
              onEdit={(address) => setEditingAddress(address)}
              onDelete={deleteAddress}
              onSetDefault={setAsDefault}
              isLoading={isLoading}
            />
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-4">You don't have any saved addresses yet.</p>
              <Button
                variant="default"
                onClick={() => setShowAddressForm(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New Address
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Shipping Method</CardTitle>
          <ShippingRefreshButton onRefresh={refreshShippingMethods} />
        </CardHeader>
        <CardContent>
          {isShippingLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Loading shipping options...</p>
              </div>
            </div>
          ) : (
            <ShippingMethodSelector
              methods={shippingMethods}
              selectedMethodId={selectedShippingMethodId}
              onSelect={onShippingMethodSelect}
              disabled={!selectedAddressId}
            />
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end mt-6">
        <Button onClick={handleContinue} disabled={!selectedAddressId}>
          Continue to Payment
        </Button>
      </div>
    </>
  );
}

