import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { generateContent, AIProvider } from '@/lib/ai';
import { sendNewsletter } from '@/integrations/supabase/newsletter';
import { getStoreIntelligence, generateSmartNewsletterPrompt, generateNewsletterHTML, StoreIntelligence } from '@/lib/newsletter-intelligence';
import { newsletterTemplates, generateNewsletterFromTemplate, NewsletterTemplateData } from '@/lib/newsletter-templates';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import ProductImageSelector from '@/components/newsletter/ProductImageSelector';
import ImageInserter from '@/components/admin/ImageInserter';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  ArrowLeft,
  Send,
  Wand2,
  Loader2,
  Eye,
  Save,
  Brain,
  Package,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  ImagePlus
} from 'lucide-react';

const NewsletterEditorPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Newsletter content state
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [htmlContent, setHtmlContent] = useState('');

  // AI generation state
  const [aiProvider, setAiProvider] = useState<AIProvider>('gemini');
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAiDialog, setShowAiDialog] = useState(false);
  const [useSmartGeneration, setUseSmartGeneration] = useState(true);
  const [storeIntelligence, setStoreIntelligence] = useState<StoreIntelligence | null>(null);
  const [isLoadingIntelligence, setIsLoadingIntelligence] = useState(false);

  // Sending state
  const [isSending, setIsSending] = useState(false);
  const [showSendDialog, setShowSendDialog] = useState(false);

  // Product image selector state
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [showImageInserter, setShowImageInserter] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [insertionMode, setInsertionMode] = useState<'append' | 'cursor' | 'replace'>('append');

  // Template selection state
  const [selectedTemplate, setSelectedTemplate] = useState('classic');
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // Regenerate HTML when template changes
  useEffect(() => {
    if (content || selectedProducts.length > 0) {
      regenerateHtmlContent();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTemplate]);

  // Load store intelligence
  const loadStoreIntelligence = async () => {
    try {
      setIsLoadingIntelligence(true);
      const intelligence = await getStoreIntelligence(30); // Last 30 days
      setStoreIntelligence(intelligence);
      return intelligence;
    } catch (error) {
      console.error('Error loading store intelligence:', error);
      toast({
        title: "Intelligence Loading Failed",
        description: "Could not load store data, using basic generation",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoadingIntelligence(false);
    }
  };

  // Generate newsletter content using AI
  const generateWithAI = async () => {
    if (!useSmartGeneration && !aiPrompt.trim()) {
      toast({
        title: "Input Required",
        description: "Please enter a topic or prompt for the AI",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsGenerating(true);

      let prompt: string;
      let currentIntelligence = storeIntelligence;

      if (useSmartGeneration) {
        // Load store intelligence if not already loaded
        if (!currentIntelligence) {
          currentIntelligence = await loadStoreIntelligence();
        }

        if (currentIntelligence) {
          prompt = generateSmartNewsletterPrompt(currentIntelligence, aiPrompt || undefined, 'informative');
        } else {
          // Fallback to basic generation with a helpful prompt
          prompt = aiPrompt || 'Create a newsletter about our latest CBD and cannabis products, featuring new arrivals, special offers, and educational content for our customers.';
          toast({
            title: "Using Basic Generation",
            description: "Could not load store data, using basic AI generation instead.",
            variant: "default"
          });
        }
      } else {
        prompt = aiPrompt;
      }

      const generatedContent = await generateContent({
        provider: aiProvider,
        contentType: 'newsletter',
        topic: prompt,
        title: subject,
        tone: 'informative',
        length: 'medium'
      });

      // Extract subject line if AI provided one
      const lines = generatedContent.split('\n');
      const subjectLine = lines.find(line =>
        line.toLowerCase().includes('subject:') ||
        line.toLowerCase().includes('subject line:')
      );

      if (subjectLine && !subject) {
        const extractedSubject = subjectLine.replace(/subject:?\s*/i, '').trim();
        setSubject(extractedSubject);
      }

      // Set the generated content
      setContent(generatedContent);

      // Generate enhanced HTML version with real product data
      if (useSmartGeneration && currentIntelligence) {
        const enhancedHtml = await generateNewsletterHTML(generatedContent, currentIntelligence);
        setHtmlContent(enhancedHtml);
      } else {
        const basicHtml = convertMarkdownToBasicHtml(generatedContent);
        setHtmlContent(basicHtml);
      }

      setShowAiDialog(false);

      toast({
        title: "Content Generated",
        description: `Successfully generated newsletter content using ${aiProvider}.`,
      });

    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Convert markdown to basic HTML for email
  const convertMarkdownToBasicHtml = (markdown: string): string => {
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/!\[(.*?)\]\((.*?)\)/gim, '<img src="$2" alt="$1" style="max-width: 100%; height: auto; margin: 16px 0; border-radius: 8px;" />')
      .replace(/\n\n/gim, '</p><p>')
      .replace(/\n/gim, '<br>')
      .replace(/^(.*)$/gim, '<p>$1</p>')
      .replace(/<p><\/p>/gim, '')
      .replace(/<p><h/gim, '<h')
      .replace(/<\/h([1-6])><\/p>/gim, '</h$1>')
      .replace(/<p><img/gim, '<img')
      .replace(/<img([^>]*)><\/p>/gim, '<img$1>');
  };

  // Handle product selection from image selector
  const handleProductSelect = (product: any, mode: 'append' | 'cursor' | 'replace' = insertionMode) => {
    // Add to selected products for template rendering
    setSelectedProducts(prev => {
      const exists = prev.find(p => p.id === product.id);
      if (exists && mode !== 'replace') {
        toast({
          title: "Product Already Added",
          description: `${product.name} is already in your newsletter`,
          variant: "default"
        });
        return prev;
      }
      return exists && mode === 'replace' ? prev : [...prev, product];
    });

    const imageMarkdown = `\n\n![${product.name}](${getFullImageUrl(product.image)})\n\n**${product.name}** - £${product.sale_price || product.price}${product.sale_price ? ` (was £${product.price})` : ''}\n\n`;

    // Insert based on mode
    setContent(prevContent => {
      switch (mode) {
        case 'cursor':
          // Insert at cursor position
          return prevContent.slice(0, cursorPosition) + imageMarkdown + prevContent.slice(cursorPosition);
        case 'replace':
          // Replace all content
          return imageMarkdown;
        case 'append':
        default:
          // Append to end
          return prevContent + imageMarkdown;
      }
    });

    toast({
      title: "Product Added",
      description: `Added ${product.name} to your newsletter${mode === 'cursor' ? ' at cursor position' : mode === 'replace' ? ' (replaced content)' : ''}`,
    });
  };

  // Track cursor position in textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
    setCursorPosition(e.target.selectionStart);
    // Debounce the HTML regeneration
    setTimeout(() => regenerateHtmlContent(e.target.value), 500);
  };

  // Handle cursor position updates
  const handleTextareaSelect = (e: React.SyntheticEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    setCursorPosition(target.selectionStart);
  };

  // Insert text at cursor position
  const insertAtCursor = (text: string) => {
    setContent(prevContent => {
      const newContent = prevContent.slice(0, cursorPosition) + text + prevContent.slice(cursorPosition);
      setCursorPosition(cursorPosition + text.length);
      return newContent;
    });
  };

  // Handle custom image insertion
  const handleImageInsert = (markdown: string) => {
    if (insertionMode === 'cursor') {
      insertAtCursor(markdown);
    } else {
      setContent(prevContent => prevContent + markdown);
    }

    toast({
      title: "Image Inserted",
      description: `Custom image added to newsletter${insertionMode === 'cursor' ? ' at cursor position' : ''}`,
    });
  };

  // Get full image URL helper
  const getFullImageUrl = (imagePath: string): string => {
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    const bucketName = 'product-images';
    return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${imagePath}`;
  };

  // Regenerate HTML content using selected template
  const regenerateHtmlContent = (contentText?: string, templateId?: string) => {
    const currentContent = contentText || content;
    const currentTemplate = templateId || selectedTemplate;

    try {
      const templateData: NewsletterTemplateData = {
        subject: subject || 'Newsletter',
        content: currentContent,
        products: selectedProducts.map(product => ({
          id: product.id,
          name: product.name,
          price: product.price,
          sale_price: product.sale_price,
          image: getFullImageUrl(product.image)
        })),
        unsubscribeUrl: 'https://bitsnbongs.com/unsubscribe'
      };

      let templateHtml = generateNewsletterFromTemplate(currentTemplate, templateData);

      // For preview, modify the CSS to remove width constraints
      templateHtml = templateHtml.replace(
        'max-width: 600px !important;',
        'max-width: 100% !important;'
      ).replace(
        'max-width: 600px;',
        'max-width: 100%;'
      ).replace(
        'margin: 0 auto !important;',
        'margin: 0 !important;'
      ).replace(
        'margin: 0 auto;',
        'margin: 0;'
      ).replace(
        'box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);',
        'box-shadow: none;'
      );

      setHtmlContent(templateHtml);
    } catch (error) {
      console.error('Error generating template:', error);
      // Fallback to basic HTML
      const basicHtml = convertMarkdownToBasicHtml(currentContent);
      setHtmlContent(basicHtml);
    }
  };

  // Send newsletter to subscribers
  const handleSendNewsletter = async () => {
    if (!subject.trim() || !content.trim()) {
      toast({
        title: "Missing Content",
        description: "Please provide both a subject and content for the newsletter",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSending(true);

      // Generate original template HTML for email (without preview modifications)
      let emailHtml = htmlContent || convertMarkdownToBasicHtml(content);

      if (selectedTemplate !== 'classic' || selectedProducts.length > 0) {
        const templateData: NewsletterTemplateData = {
          subject: subject || 'Newsletter',
          content: content,
          products: selectedProducts.map(product => ({
            id: product.id,
            name: product.name,
            price: product.price,
            sale_price: product.sale_price,
            image: getFullImageUrl(product.image)
          })),
          unsubscribeUrl: 'https://bitsnbongs.com/unsubscribe'
        };
        emailHtml = generateNewsletterFromTemplate(selectedTemplate, templateData);
      }

      const result = await sendNewsletter(
        subject,
        content,
        emailHtml
      );

      if (result.error) {
        throw new Error(result.error);
      }

      toast({
        title: "Newsletter Sent",
        description: `Newsletter sent successfully to ${result.recipientCount} subscribers!`,
      });

      setShowSendDialog(false);
      navigate('/admin/newsletter');

    } catch (error) {
      console.error('Error sending newsletter:', error);
      toast({
        title: "Send Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          onClick={() => navigate('/admin/newsletter')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Newsletter
        </Button>
        <h1 className="text-3xl font-bold">Create Newsletter</h1>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
        {/* Editor Panel */}
        <div className="xl:col-span-2">
          <Card>
          <CardHeader>
            <CardTitle>Newsletter Content</CardTitle>
            <CardDescription>
              Create your newsletter content or use AI to generate it
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Subject line */}
            <div>
              <Label htmlFor="subject">Subject Line</Label>
              <Input
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter newsletter subject"
              />
            </div>

            {/* Content editor */}
            <div>
              <Label htmlFor="content">Content (Markdown)</Label>
              <Textarea
                id="content"
                value={content}
                onChange={handleTextareaChange}
                onSelect={handleTextareaSelect}
                onKeyUp={handleTextareaSelect}
                onClick={handleTextareaSelect}
                className="min-h-[300px] font-mono text-sm"
                placeholder="Write your newsletter content here..."
              />
            </div>

            {/* Template Selection */}
            <div>
              <Label htmlFor="template">Newsletter Template</Label>
              <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a template" />
                </SelectTrigger>
                <SelectContent>
                  {newsletterTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-gray-500">{template.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Selected Products Display */}
            {selectedProducts.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <Label className="text-sm font-medium">Featured Products ({selectedProducts.length})</Label>
                <div className="flex flex-wrap gap-2 mt-3">
                  {selectedProducts.map(product => (
                    <div key={product.id} className="flex items-center gap-2 bg-white border rounded-md px-3 py-2 text-sm shadow-sm">
                      <span className="truncate max-w-[200px]" title={product.name}>
                        {product.name}
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-5 w-5 p-0 hover:bg-red-100 hover:text-red-600"
                        onClick={() => {
                          setSelectedProducts(prev => prev.filter(p => p.id !== product.id));
                          regenerateHtmlContent();
                        }}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Image Insertion Controls */}
            <div className="bg-blue-50 rounded-lg p-4">
              <Label className="text-sm font-medium mb-3 block">Image Insertion Options</Label>

              {/* Insertion Mode Selection */}
              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-gray-600">Insertion Mode</Label>
                  <div className="flex gap-2 mt-1">
                    <Button
                      size="sm"
                      variant={insertionMode === 'append' ? 'default' : 'outline'}
                      onClick={() => setInsertionMode('append')}
                    >
                      Append to End
                    </Button>
                    <Button
                      size="sm"
                      variant={insertionMode === 'cursor' ? 'default' : 'outline'}
                      onClick={() => setInsertionMode('cursor')}
                    >
                      At Cursor
                    </Button>
                  </div>
                </div>

                {/* Quick Insert Options */}
                <div className="flex gap-2 flex-wrap">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => insertAtCursor('\n\n## New Section\n\n')}
                  >
                    + Section Header
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowImageInserter(true)}
                  >
                    + Custom Image
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => insertAtCursor('\n\n**Special Offer:** Your offer text here\n\n')}
                  >
                    + Special Offer
                  </Button>
                </div>

                {cursorPosition > 0 && (
                  <div className="text-xs text-gray-500">
                    Cursor position: {cursorPosition}
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={() => setShowAiDialog(true)}
                variant="outline"
              >
                <Wand2 className="mr-2 h-4 w-4" />
                Generate with AI
              </Button>
              <Button
                onClick={() => setShowProductSelector(true)}
                variant="outline"
              >
                <ImagePlus className="mr-2 h-4 w-4" />
                Add Product Image {insertionMode === 'cursor' ? '(At Cursor)' : '(At End)'}
              </Button>
              <Button
                onClick={() => regenerateHtmlContent()}
                variant="outline"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Preview
              </Button>
              <Button
                onClick={() => setShowSendDialog(true)}
                disabled={!subject.trim() || !content.trim()}
              >
                <Send className="mr-2 h-4 w-4" />
                Send Newsletter
              </Button>
            </div>
          </CardContent>
        </Card>
        </div>

        {/* Preview Panel */}
        <div className="xl:col-span-3">
          <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              See how your newsletter will look to subscribers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="html" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="html">HTML Preview</TabsTrigger>
                <TabsTrigger value="text">Plain Text</TabsTrigger>
              </TabsList>
              <TabsContent value="html" className="mt-4">
                <div className="border rounded-md p-6 min-h-[600px] max-h-[700px] bg-white overflow-auto newsletter-preview">
                  {subject && (
                    <div className="mb-4 p-2 bg-gray-100 rounded">
                      <strong>Subject:</strong> {subject}
                    </div>
                  )}
                  <div
                    dangerouslySetInnerHTML={{
                      __html: htmlContent || convertMarkdownToBasicHtml(content) || '<p class="text-gray-500">No content yet...</p>'
                    }}
                  />
                </div>
              </TabsContent>
              <TabsContent value="text" className="mt-4">
                <div className="border rounded-md p-6 min-h-[600px] max-h-[700px] bg-white font-mono text-sm whitespace-pre-wrap overflow-auto">
                  {subject && (
                    <div className="mb-4 font-bold">
                      Subject: {subject}
                    </div>
                  )}
                  {content || 'No content yet...'}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
        </div>
      </div>

      {/* AI Generation Dialog */}
      <Dialog open={showAiDialog} onOpenChange={setShowAiDialog}>
        <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Generate Newsletter with AI
            </DialogTitle>
            <DialogDescription>
              Use AI to generate engaging newsletter content based on your actual store data.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            {/* Smart Generation Toggle */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="smart-generation"
                checked={useSmartGeneration}
                onCheckedChange={setUseSmartGeneration}
              />
              <Label htmlFor="smart-generation" className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Use Smart Generation (includes real store data)
              </Label>
            </div>

            {/* Store Intelligence Preview */}
            {useSmartGeneration && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Store Intelligence
                  </h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadStoreIntelligence}
                    disabled={isLoadingIntelligence}
                  >
                    {isLoadingIntelligence ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {storeIntelligence ? (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Package className="h-3 w-3" />
                        <span>Total Products: {storeIntelligence.totalActiveProducts}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-3 w-3 text-green-600" />
                        <span>New Products: {storeIntelligence.newProducts.length}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-3 w-3 text-orange-600" />
                        <span>Low Stock: {storeIntelligence.lowStockProducts.length}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <span className="font-medium">Top Categories:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {storeIntelligence.topCategories.slice(0, 3).map((cat, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {cat.name} ({cat.count})
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    Click refresh to load current store data for smarter generation.
                  </p>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="ai-prompt">
                {useSmartGeneration ? 'Theme or Focus (Optional)' : 'Topic or Prompt'}
              </Label>
              <Input
                id="ai-prompt"
                placeholder={useSmartGeneration
                  ? "E.g., Focus on new arrivals, seasonal promotions, or leave blank for general update"
                  : "E.g., Monthly newsletter about new CBD products"
                }
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
              />
              <p className="text-xs text-gray-500">
                {useSmartGeneration
                  ? "Optional theme to focus the newsletter on. AI will use real store data automatically."
                  : "Describe what you want the newsletter to be about."
                }
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ai-provider">AI Provider</Label>
              <Select
                value={aiProvider}
                onValueChange={(value) => setAiProvider(value as AIProvider)}
              >
                <SelectTrigger id="ai-provider">
                  <SelectValue placeholder="Select AI provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini">Google Gemini</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAiDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={generateWithAI}
              disabled={isGenerating || (!useSmartGeneration && !aiPrompt.trim())}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  {useSmartGeneration ? 'Generate Smart Newsletter' : 'Generate'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Send Confirmation Dialog */}
      <Dialog open={showSendDialog} onOpenChange={setShowSendDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Send Newsletter</DialogTitle>
            <DialogDescription>
              Are you sure you want to send this newsletter to all active subscribers?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-2">
              <p><strong>Subject:</strong> {subject}</p>
              <p><strong>Content Preview:</strong></p>
              <div className="text-sm text-gray-600 max-h-32 overflow-y-auto border rounded p-2">
                {content.substring(0, 200)}...
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSendDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSendNewsletter}
              disabled={isSending}
            >
              {isSending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Newsletter
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Product Image Selector */}
      <ProductImageSelector
        open={showProductSelector}
        onOpenChange={setShowProductSelector}
        onProductSelect={handleProductSelect}
      />

      {/* Custom Image Inserter */}
      <ImageInserter
        open={showImageInserter}
        onOpenChange={setShowImageInserter}
        onInsert={handleImageInsert}
        insertionMode={insertionMode}
      />
    </div>
  );
};

export default NewsletterEditorPage;
