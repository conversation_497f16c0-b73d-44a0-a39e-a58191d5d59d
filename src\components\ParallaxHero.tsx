
import { useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const ParallaxHero = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const bgLayerRef = useRef<HTMLDivElement>(null);
  const midLayerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!parallaxRef.current) return;
      
      const scrollPosition = window.scrollY;
      const parallaxFactor = 0.5;
      
      // Apply parallax effect to different layers
      if (bgLayerRef.current) {
        bgLayerRef.current.style.transform = `translateY(${scrollPosition * 0.1}px)`;
      }
      
      if (midLayerRef.current) {
        midLayerRef.current.style.transform = `translateY(${scrollPosition * 0.2}px)`;
      }
      
      if (contentRef.current) {
        contentRef.current.style.transform = `translateY(${scrollPosition * parallaxFactor}px)`;
        contentRef.current.style.opacity = `${1 - (scrollPosition * 0.002)}`;
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <section ref={parallaxRef} className="relative h-screen overflow-hidden">
      {/* Background layer - moves slowest */}
      <div ref={bgLayerRef} className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1500673922987-e212871fec22')] bg-cover bg-center transition-transform duration-200 ease-out"></div>
      
      {/* Dark overlay with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-sage-900/80 to-clay-900/80"></div>
      
      {/* Middle layer with smoke effect - moves at medium speed */}
      <div ref={midLayerRef} className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1557683316-973673baf926')] bg-cover bg-center opacity-30 transition-transform duration-200 ease-out"></div>
      
      {/* Content layer - moves fastest */}
      <div ref={contentRef} className="relative h-full flex flex-col items-center justify-center text-center px-4 transition-all duration-200 ease-out">
        <div className="max-w-3xl mx-auto">
          <div className="animate-fade-in">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight">
              Premium CBD & Smoking Accessories
            </h1>
            <p className="text-xl md:text-2xl mb-10 text-white/90 max-w-2xl mx-auto leading-relaxed">
              Discover our curated collection of high-quality CBD products and artisanal smoking accessories for the modern enthusiast.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                asChild 
                size="lg" 
                className="bg-sage-500 hover:bg-sage-600 text-white px-8 py-6 text-lg transform transition-transform duration-300 hover:scale-105"
              >
                <Link to="/shop">Explore Products</Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="bg-transparent border-2 border-white text-white hover:bg-white/10 px-8 py-6 text-lg backdrop-blur-sm transform transition-transform duration-300 hover:scale-105"
              >
                <Link to="/blog">Read Our Blog</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll indicator animation */}
      <div className="absolute bottom-8 left-0 right-0 flex justify-center animate-bounce">
        <div className="w-8 h-12 rounded-full border-2 border-white flex items-start justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default ParallaxHero;
