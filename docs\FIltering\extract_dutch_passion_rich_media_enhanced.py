"""
Enhanced Dutch Passion Rich Media Extractor

This script extracts rich media elements from Dutch Passion product pages with improved selectors:
- Terpene profile charts and percentages
- Cannabinoid content indicators
- Product icons and badges
- Product images
"""

import requests
import json
import re
import os
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# Base URL
BASE_URL = "https://dutch-passion.com"
SEEDS_URL = "https://dutch-passion.com/en/cannabis-seeds"

# Headers to mimic a browser
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Connection": "keep-alive",
    "Referer": "https://dutch-passion.com/",
    "DNT": "1",  # Do Not Track
}

# Create directories for media
def create_directories():
    os.makedirs("dutch_passion_media/icons", exist_ok=True)
    os.makedirs("dutch_passion_media/product_images", exist_ok=True)
    os.makedirs("dutch_passion_media/terpene_charts", exist_ok=True)
    os.makedirs("dutch_passion_media/cannabinoid_charts", exist_ok=True)

# Function to get page content with age verification bypass
def get_page_content(url):
    print(f"Fetching content from: {url}")
    
    # First request to get cookies
    session = requests.Session()
    response = session.get(url, headers=HEADERS)
    
    # Check if we need to handle age verification
    if "ARE YOU AGED 18 OR OVER?" in response.text:
        print("Handling age verification...")
        
        # Extract any necessary tokens or form data
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Simulate clicking the "Enter" button by making a POST request
        verification_url = urljoin(BASE_URL, "/en/age-verification")
        verification_data = {
            "age_verification": "true",
            "redirect": "/en/cannabis-seeds"
        }
        
        # Send POST request to handle age verification
        response = session.post(verification_url, data=verification_data, headers=HEADERS)
        
        # Get the main page again after verification
        response = session.get(url, headers=HEADERS)
    
    return response.text, session

# Function to download an image
def download_image(session, url, save_path):
    try:
        response = session.get(url, headers=HEADERS)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            print(f"Downloaded image to {save_path}")
            return True
        else:
            print(f"Failed to download image: {url}, status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading image {url}: {e}")
        return False

# Function to extract filename from URL
def get_filename_from_url(url):
    parsed_url = urlparse(url)
    path = parsed_url.path
    filename = os.path.basename(path)
    return filename

# Function to extract terpene profiles
def extract_terpene_profiles(soup, product_id, session):
    terpene_data = {}
    
    # Look for terpene chart - using more general selectors
    terpene_sections = soup.select('.terpene-chart, .cannabinoid-profile, .terpenes-section, .product-info img[src*="terpene"], .product-info img[src*="chart"], .product-description img[src*="terpene"]')
    
    if terpene_sections:
        print(f"Found terpene sections for product {product_id}")
        
        # Extract terpene percentages from text
        product_text = soup.get_text()
        
        # Common terpenes to look for
        terpenes = ["Myrcene", "Limonene", "Caryophyllene", "Pinene", "Linalool", "Terpinolene", "Humulene"]
        
        for terpene in terpenes:
            # Look for percentage patterns like "Myrcene: 0.5%" or "Myrcene (0.5%)"
            pattern = rf"{terpene}[:\s]*(\d+(?:\.\d+)?)%"
            match = re.search(pattern, product_text, re.IGNORECASE)
            if match:
                terpene_data[terpene] = match.group(1) + "%"
        
        # Try to find and download terpene chart image
        for section in terpene_sections:
            if section.name == 'img':
                img_elem = section
            else:
                img_elem = section.select_one('img')
                
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    filename = f"terpene_chart_{product_id}.jpg"
                    save_path = os.path.join("dutch_passion_media/terpene_charts", filename)
                    
                    if download_image(session, img_url, save_path):
                        terpene_data['chart_image'] = save_path
                        break
    
    # If no specific terpene data found, look for it in the product description
    if not terpene_data:
        description = soup.select_one('#description, .product-description, .product-info')
        if description:
            text = description.get_text()
            
            # Look for terpene mentions
            terpene_section = re.search(r'(?:terpene|aroma).*?profile', text, re.IGNORECASE)
            if terpene_section:
                section_text = text[terpene_section.start():terpene_section.start() + 500]  # Get some context
                
                # Extract terpene percentages
                percentages = re.findall(r'(\w+)[:\s]*(\d+(?:\.\d+)?)%', section_text)
                for name, value in percentages:
                    if len(name) > 3:  # Avoid short abbreviations
                        terpene_data[name] = value + "%"
    
    return terpene_data

# Function to extract cannabinoid profiles
def extract_cannabinoid_profiles(soup, product_id, session):
    cannabinoid_data = {}
    
    # Look for cannabinoid information - using more general selectors
    cannabinoid_sections = soup.select('.cannabinoid-section, .thc-section, .cbd-section, .product-info img[src*="thc"], .product-info img[src*="cbd"], .product-info img[src*="cannabinoid"]')
    
    if cannabinoid_sections:
        print(f"Found cannabinoid sections for product {product_id}")
        
        # Extract cannabinoid percentages from text
        product_text = soup.get_text()
        
        # Look for THC percentage
        thc_match = re.search(r'THC[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if thc_match:
            cannabinoid_data['THC'] = thc_match.group(1) + "%"
        
        # Look for CBD percentage
        cbd_match = re.search(r'CBD[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if cbd_match:
            cannabinoid_data['CBD'] = cbd_match.group(1) + "%"
        
        # Look for CBG percentage
        cbg_match = re.search(r'CBG[:\s]*(\d+(?:\.\d+)?)%', product_text, re.IGNORECASE)
        if cbg_match:
            cannabinoid_data['CBG'] = cbg_match.group(1) + "%"
        
        # Try to find and download cannabinoid chart image
        for section in cannabinoid_sections:
            if section.name == 'img':
                img_elem = section
            else:
                img_elem = section.select_one('img')
                
            if img_elem:
                img_url = img_elem.get('src') or img_elem.get('data-src')
                if img_url:
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    filename = f"cannabinoid_chart_{product_id}.jpg"
                    save_path = os.path.join("dutch_passion_media/cannabinoid_charts", filename)
                    
                    if download_image(session, img_url, save_path):
                        cannabinoid_data['chart_image'] = save_path
                        break
    
    # If no specific cannabinoid data found, try to extract from text
    if not cannabinoid_data:
        # Look for THC/CBD mentions in product description
        description = soup.select_one('#description, .product-description, .product-info')
        if description:
            text = description.get_text().lower()
            
            # Extract THC percentage with more flexible pattern
            thc_patterns = [
                r'thc[^\d]*(\d+(?:\.\d+)?)%',
                r'thc[^\d]*content[^\d]*(\d+(?:\.\d+)?)%',
                r'thc[^\d]*level[^\d]*(\d+(?:\.\d+)?)%',
                r'(\d+(?:\.\d+)?)%[^\d]*thc'
            ]
            
            for pattern in thc_patterns:
                thc_match = re.search(pattern, text)
                if thc_match:
                    cannabinoid_data['THC'] = thc_match.group(1) + "%"
                    break
            
            # Extract CBD percentage with more flexible pattern
            cbd_patterns = [
                r'cbd[^\d]*(\d+(?:\.\d+)?)%',
                r'cbd[^\d]*content[^\d]*(\d+(?:\.\d+)?)%',
                r'cbd[^\d]*level[^\d]*(\d+(?:\.\d+)?)%',
                r'(\d+(?:\.\d+)?)%[^\d]*cbd'
            ]
            
            for pattern in cbd_patterns:
                cbd_match = re.search(pattern, text)
                if cbd_match:
                    cannabinoid_data['CBD'] = cbd_match.group(1) + "%"
                    break
    
    return cannabinoid_data

# Function to extract product icons
def extract_product_icons(soup, product_id, session):
    icons_data = {}
    
    # Look for product characteristic icons - using more general selectors
    icon_sections = soup.select('.product-characteristics, .product-icons, .product-features, .product-info ul, .product-attributes')
    
    if icon_sections:
        print(f"Found icon sections for product {product_id}")
        
        for section in icon_sections:
            try:
                icon_items = section.select('li, .icon-item, .characteristic-item, .feature-item')
                
                for i, item in enumerate(icon_items):
                    icon_name = ""
                    icon_value = ""
                    icon_image = None
                    
                    # Try to get icon name
                    name_elem = item.select_one('.icon-name, .characteristic-name, .name, span, strong')
                    if name_elem:
                        icon_name = name_elem.text.strip()
                    else:
                        # If no specific name element, use the whole text
                        icon_name = item.text.strip()
                    
                    # Try to get icon value
                    value_elem = item.select_one('.icon-value, .characteristic-value, .value')
                    if value_elem:
                        icon_value = value_elem.text.strip()
                    
                    # Try to get icon image
                    img_elem = item.select_one('img')
                    if img_elem:
                        img_url = img_elem.get('src') or img_elem.get('data-src')
                        if img_url:
                            if not img_url.startswith('http'):
                                img_url = urljoin(BASE_URL, img_url)
                            
                            # Use icon name for filename if available, otherwise use index
                            if icon_name:
                                safe_name = re.sub(r'[^\w\-_]', '_', icon_name.lower())
                                filename = f"icon_{safe_name}_{product_id}.png"
                            else:
                                filename = f"icon_{i}_{product_id}.png"
                            
                            save_path = os.path.join("dutch_passion_media/icons", filename)
                            
                            if download_image(session, img_url, save_path):
                                icon_image = save_path
                    
                    # Add to icons data
                    if icon_name or icon_value or icon_image:
                        icon_key = icon_name if icon_name else f"icon_{i}"
                        icons_data[icon_key] = {
                            "name": icon_name,
                            "value": icon_value,
                            "image": icon_image
                        }
            except Exception as e:
                print(f"Error extracting icon section: {e}")
    
    # Look for standalone icons in the product info section
    standalone_icons = soup.select('.product-info img:not([src*="terpene"]):not([src*="thc"]):not([src*="cbd"])')
    
    for i, icon in enumerate(standalone_icons):
        try:
            img_url = icon.get('src') or icon.get('data-src')
            if img_url and not img_url.startswith('data:'):
                if not img_url.startswith('http'):
                    img_url = urljoin(BASE_URL, img_url)
                
                # Skip large images (likely product images, not icons)
                if 'large' in img_url.lower() or 'product' in img_url.lower():
                    continue
                
                filename = f"standalone_icon_{i}_{product_id}.png"
                save_path = os.path.join("dutch_passion_media/icons", filename)
                
                if download_image(session, img_url, save_path):
                    # Try to get icon name from alt text or nearby text
                    icon_name = icon.get('alt', '')
                    if not icon_name:
                        parent_text = icon.parent.get_text().strip()
                        if parent_text:
                            icon_name = parent_text[:30]  # Limit length
                    
                    icons_data[f"standalone_icon_{i}"] = {
                        "name": icon_name,
                        "value": "",
                        "image": save_path
                    }
        except Exception as e:
            print(f"Error extracting standalone icon: {e}")
    
    return icons_data

# Function to extract product images
def extract_product_images(soup, product_id, session):
    images_data = []
    
    # Look for product images - using more general selectors
    image_containers = soup.select('.product-images, .product-gallery, .images-container, .product-cover, #product-images, .product-image-container')
    
    if not image_containers:
        # If no specific container found, look for images directly
        image_containers = [soup]
    
    print(f"Searching for product images for product {product_id}")
    
    for container in image_containers:
        try:
            # Find all product images with various selectors
            img_elements = container.select('img.product-image, .product-gallery img, .carousel-item img, .product-cover img, .js-qv-product-cover, .product-img')
            
            if not img_elements:
                # Try more general image selectors
                img_elements = container.select('img[src*="product"], img[src*="seeds"], img[src*="cannabis"]')
            
            for i, img in enumerate(img_elements):
                img_url = img.get('src') or img.get('data-src')
                if img_url:
                    # Skip data URLs
                    if img_url.startswith('data:'):
                        continue
                        
                    if not img_url.startswith('http'):
                        img_url = urljoin(BASE_URL, img_url)
                    
                    # Skip small thumbnails, icons, or logos
                    if any(x in img_url.lower() for x in ['thumbnail', 'icon', 'logo', 'small']):
                        continue
                    
                    # Skip images that
(Content truncated due to size limit. Use line ranges to read in chunks)