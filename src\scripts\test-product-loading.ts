import { supabase } from '../lib/supabase';

async function testProductLoading() {
  console.log('Testing product loading...');
  
  try {
    // Test basic product query
    const { data: productsData, error } = await supabase
      .from('products')
      .select('id, name, price, sale_price, image')
      .eq('is_active', true)
      .not('image', 'is', null)
      .neq('image', '')
      .limit(5);

    if (error) {
      console.error('Error loading products:', error);
      console.error('Error details:', error.message, error.details);
      return;
    }

    console.log('Successfully loaded products:', productsData?.length || 0);
    console.log('Sample products:', productsData?.slice(0, 2));

    // Test if images are accessible
    if (productsData && productsData.length > 0) {
      const firstProduct = productsData[0];
      console.log('First product image URL:', firstProduct.image);
      
      // Test image accessibility
      if (firstProduct.image) {
        try {
          const response = await fetch(firstProduct.image, { method: 'HEAD' });
          console.log('Image accessibility test:', response.ok ? 'SUCCESS' : 'FAILED');
          console.log('Image response status:', response.status);
        } catch (imageError) {
          console.error('Image fetch error:', imageError);
        }
      }
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testProductLoading();
