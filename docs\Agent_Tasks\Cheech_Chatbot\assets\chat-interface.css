/**
 * Chat Interface Styles
 * 
 * Styles for the Cheech chatbot interface components.
 */

/* Chat Container */
.chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Chat <PERSON> */
.chat-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #4CAF50;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1002;
}

.chat-button:hover {
  transform: scale(1.05);
  background-color: #45a049;
}

.chat-button-open {
  background-color: #f44336;
}

.chat-button-open:hover {
  background-color: #d32f2f;
}

.chat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.chat-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-icon {
  font-size: 24px;
  font-weight: bold;
}

/* Chat Window */
.chat-window {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 350px;
  height: 500px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1001;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat Header */
.chat-header {
  background-color: #4CAF50;
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.chat-title {
  display: flex;
  align-items: center;
}

.header-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  border: 2px solid white;
}

.chat-title h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f5f5f5;
}

/* Message Bubbles */
.message-bubble {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  max-width: 80%;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.user-message {
  margin-left: auto;
  flex-direction: row-reverse;
}

.assistant-message {
  margin-right: auto;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  padding: 10px 14px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.user-message .message-content {
  background-color: #4CAF50;
  color: white;
  border-top-right-radius: 4px;
}

.assistant-message .message-content {
  background-color: white;
  color: #333;
  border-top-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-timestamp {
  font-size: 10px;
  color: #999;
  margin-top: 4px;
  align-self: flex-end;
}

.user-message .message-timestamp {
  margin-right: 8px;
}

.assistant-message .message-timestamp {
  margin-left: 8px;
}

/* Product Cards */
.product-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.product-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 8px;
}

.product-image {
  width: 80px;
  height: 80px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 8px 12px;
  flex: 1;
}

.product-info h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.product-price {
  font-weight: bold;
  color: #4CAF50;
  margin: 4px 0;
  font-size: 14px;
}

.product-rating {
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 12px;
}

.star {
  color: #ccc;
  margin-right: 1px;
}

.star.filled {
  color: #FFD700;
}

.rating-count {
  color: #666;
  margin-left: 4px;
  font-size: 11px;
}

.view-product-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 4px;
  transition: background-color 0.2s;
}

.view-product-button:hover {
  background-color: #45a049;
}

/* Suggestion Chips */
.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

.suggestion-chip {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 13px;
  color: #4CAF50;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.suggestion-chip:hover {
  background-color: #f0f9f0;
  border-color: #4CAF50;
}

/* Chat Input */
.chat-input {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid #eee;
}

.chat-input textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 10px 14px;
  font-size: 14px;
  resize: none;
  outline: none;
  max-height: 100px;
  font-family: inherit;
}

.chat-input textarea:focus {
  border-color: #4CAF50;
}

.send-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #45a049;
}

.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Typing Indicator */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  float: left;
  margin: 0 1px;
  background-color: #9E9EA1;
  display: block;
  border-radius: 50%;
  opacity: 0.4;
}

.typing-indicator span:nth-of-type(1) {
  animation: 1s blink infinite 0.3333s;
}

.typing-indicator span:nth-of-type(2) {
  animation: 1s blink infinite 0.6666s;
}

.typing-indicator span:nth-of-type(3) {
  animation: 1s blink infinite 0.9999s;
}

@keyframes blink {
  50% {
    opacity: 1;
  }
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .chat-window {
    width: calc(100vw - 40px);
    height: 60vh;
    bottom: 70px;
  }
}

/* Accessibility Focus Styles */
button:focus, textarea:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .chat-window {
    background-color: #222;
  }
  
  .chat-messages {
    background-color: #333;
  }
  
  .assistant-message .message-content {
    background-color: #444;
    color: #fff;
  }
  
  .chat-input {
    background-color: #222;
  }
  
  .chat-input textarea {
    background-color: #333;
    color: #fff;
    border-color: #555;
  }
  
  .suggestion-chips {
    background-color: #222;
  }
  
  .suggestion-chip {
    background-color: #333;
    color: #4CAF50;
    border-color: #555;
  }
  
  .product-card {
    background-color: #333;
  }
  
  .product-info h3 {
    color: #fff;
  }
}
