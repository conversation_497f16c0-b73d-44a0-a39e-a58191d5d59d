import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';

interface TransitionLayoutProps {
  children: React.ReactNode;
}

const TransitionLayout: React.FC<TransitionLayoutProps> = ({ children }) => {
  const [searchParams] = useSearchParams();
  const [isVisible, setIsVisible] = useState(true);
  const childrenRef = useRef(children);
  
  // Keep track of previous search params
  const prevParamsRef = useRef<URLSearchParams | null>(null);
  
  // Listen for search params changes, but ignore search query changes
  useEffect(() => {
    // Get current params as an object
    const currentParams = Object.fromEntries(searchParams.entries());
    const prevParams = prevParamsRef.current ? Object.fromEntries(prevParamsRef.current.entries()) : {};
    
    // Update the ref with current params
    prevParamsRef.current = searchParams;
    
    // Check if only the search query parameter changed
    const onlySearchChanged = (
      // Check if only the search or search-related parameters changed
      Object.keys(currentParams).length === Object.keys(prevParams).length &&
      Object.keys(currentParams).every(key => {
        // Ignore changes to search-related parameters
        if (key === 'q' || key === 'search' || key === 'query') return true;
        return currentParams[key] === prevParams[key];
      })
    );
    
    // Skip transition if only the search query changed
    if (onlySearchChanged && prevParamsRef.current !== null) {
      childrenRef.current = children;
      return;
    }
    
    // Start transition - fade out
    setIsVisible(false);
    
    // After fade out completes, update content and fade back in
    const timer = setTimeout(() => {
      childrenRef.current = children;
      setIsVisible(true);
    }, 200); // Shorter duration for better responsiveness
    
    return () => clearTimeout(timer);
  }, [searchParams, children]);
  
  // Directly render children without state to avoid rendering issues
  return (
    <div 
      className={`w-full transition-opacity duration-200 ease-in-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      {children}
    </div>
  );
};

export default TransitionLayout;
