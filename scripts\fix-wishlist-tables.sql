-- SQL script to fix the wishlist_items table structure
-- Run this in the Supabase SQL Editor

-- First, let's check the current structure of the wishlist_items table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'wishlist_items';

-- Drop the existing wishlist_items table if it's not properly structured
DROP TABLE IF EXISTS wishlist_items;

-- Create the wishlist_items table with the correct structure
CREATE TABLE wishlist_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  wishlist_id UUID NOT NULL REFERENCES wishlists(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(wishlist_id, product_id)
);

-- Add RLS policies for wishlist_items
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT - users can only see their own wishlist items
CREATE POLICY "Users can view their own wishlist items" 
ON wishlist_items FOR SELECT 
USING (
  wishlist_id IN (
    SELECT id FROM wishlists WHERE user_id = auth.uid()
  )
);

-- Policy for INSERT - users can only add items to their own wishlists
CREATE POLICY "Users can add items to their own wishlists" 
ON wishlist_items FOR INSERT 
WITH CHECK (
  wishlist_id IN (
    SELECT id FROM wishlists WHERE user_id = auth.uid()
  )
);

-- Policy for DELETE - users can only delete their own wishlist items
CREATE POLICY "Users can delete their own wishlist items" 
ON wishlist_items FOR DELETE 
USING (
  wishlist_id IN (
    SELECT id FROM wishlists WHERE user_id = auth.uid()
  )
);

-- Verify the table structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'wishlist_items';
