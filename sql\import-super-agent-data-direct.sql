-- Direct SQL import of Super Agent data
-- Run this in Supabase SQL Editor if the script continues to have issues

-- First, let's see what columns actually exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'seed_product_attributes' 
ORDER BY ordinal_position;

-- Insert/Update seed attributes for the Super Agent products
-- 420 Fastbuds Auto Californian Snow (Feminised x5)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-10 weeks',
  '18-22%',
  '<1%',
  'Sativa dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fastbuds Auto Californian Snow%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- 420 Fastbuds Auto Grapefruit (Feminised x5)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '20-24%',
  '<1%',
  'Sativa dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fastbuds Auto Grapefruit%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- 420 Fastbuds Auto Lemon AK (Feminised x5)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '20-22%',
  '<1%',
  'Sativa dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fastbuds Auto Lemon AK%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- 420 Fastbuds Auto Purple Punch (10 Feminised)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '20-25%',
  '<1%',
  'Indica dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fastbuds Auto Purple Punch%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- 420 Fastbuds Auto Rhino Ryder (Feminised x5)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '20-25%',
  '<1%',
  'Indica dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fastbuds Auto Rhino Ryder%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- 420 Fast Buds: Blackberry (Auto)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '15-20%',
  '<1%',
  'Indica dominant'
FROM products p 
WHERE p.name ILIKE '%420 Fast Buds%Blackberry%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- Green Crack (5 Female)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-9 weeks',
  '18-22%',
  '<1%',
  'Sativa dominant'
FROM products p 
WHERE p.name ILIKE '%Green Crack%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- Gorilla Cookies (5 Female)
INSERT INTO seed_product_attributes (
  product_id, 
  seed_type, 
  flowering_time, 
  thc_level, 
  cbd_level, 
  effect
)
SELECT 
  p.id,
  'Autoflower (Feminized)',
  '8-10 weeks',
  '28.5%',
  '<1%',
  'Hybrid'
FROM products p 
WHERE p.name ILIKE '%Gorilla Cookies%'
ON CONFLICT (product_id) DO UPDATE SET
  seed_type = EXCLUDED.seed_type,
  flowering_time = EXCLUDED.flowering_time,
  thc_level = EXCLUDED.thc_level,
  cbd_level = EXCLUDED.cbd_level,
  effect = EXCLUDED.effect;

-- Check results
SELECT 
  p.name,
  spa.seed_type,
  spa.flowering_time,
  spa.thc_level,
  spa.effect
FROM products p
JOIN seed_product_attributes spa ON p.id = spa.product_id
WHERE p.name ILIKE '%420 Fast%' OR p.name ILIKE '%Green Crack%' OR p.name ILIKE '%Gorilla%'
ORDER BY p.name;
