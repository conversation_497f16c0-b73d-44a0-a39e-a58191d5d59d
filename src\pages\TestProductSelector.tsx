import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import ProductImageSelector from '@/components/newsletter/ProductImageSelector';

const TestProductSelector = () => {
  const [showSelector, setShowSelector] = useState(false);

  const handleProductSelect = (product: any) => {
    console.log('Selected product:', product);
    alert(`Selected: ${product.name} - £${product.price}`);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Product Selector Test</h1>
      <p className="mb-4">This is a test page to verify the ProductImageSelector component works.</p>
      
      <Button onClick={() => setShowSelector(true)}>
        Open Product Selector
      </Button>

      <ProductImageSelector
        open={showSelector}
        onOpenChange={setShowSelector}
        onProductSelect={handleProductSelect}
      />
    </div>
  );
};

export default TestProductSelector;
