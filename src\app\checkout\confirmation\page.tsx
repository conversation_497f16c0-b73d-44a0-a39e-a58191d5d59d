'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, ShoppingBag, Home } from 'lucide-react';
import { useCart } from '@/hooks/useCart';

export default function OrderConfirmationPage() {
  const router = useRouter();
  const { items } = useCart();

  // If cart is not empty, redirect to checkout
  useEffect(() => {
    if (items.length > 0) {
      router.push('/checkout');
    }
  }, [items, router]);

  // Generate a random order number for demo purposes
  const orderNumber = `BNB-${Math.floor(100000 + Math.random() * 900000)}`;

  return (
    <div className="container max-w-3xl py-12">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold">Thank You for Your Order!</h1>
        <p className="text-gray-500 mt-2">
          Your order has been received and is being processed.
        </p>
      </div>

      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center pb-4 border-b">
              <div>
                <p className="text-sm text-gray-500">Order Number</p>
                <p className="font-medium">{orderNumber}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Order Date</p>
                <p className="font-medium">{new Date().toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Order Status</p>
                <p className="font-medium text-green-600">Confirmed</p>
              </div>
            </div>

            <div>
              <p className="font-medium mb-2">Order Details</p>
              <p className="text-sm text-gray-500">
                We've sent a confirmation email to your email address with all the details of your order.
              </p>
            </div>

            <div>
              <p className="font-medium mb-2">Shipping Information</p>
              <p className="text-sm text-gray-500">
                You will receive a shipping confirmation email with tracking information once your order has been shipped.
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-4 pt-2">
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => router.push('/shop')}
          >
            <ShoppingBag className="h-4 w-4 mr-2" />
            Continue Shopping
          </Button>
          <Button
            className="w-full sm:w-auto"
            onClick={() => router.push('/')}
          >
            <Home className="h-4 w-4 mr-2" />
            Return to Home
          </Button>
        </CardFooter>
      </Card>

      <div className="text-center text-sm text-gray-500">
        <p>
          If you have any questions about your order, please contact our customer service team at{' '}
          <a href="mailto:<EMAIL>" className="text-primary hover:underline">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
}
