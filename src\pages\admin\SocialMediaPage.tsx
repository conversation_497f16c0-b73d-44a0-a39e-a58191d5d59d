/**
 * Social Media AI Dashboard
 *
 * "This is your last chance. After this, there is no going back." 🔴
 *
 * The ultimate control center for AI-powered social media domination
 * Generate viral content across all platforms with a single click
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { useSupabase } from '@/lib/supabase/provider';
import { toast } from '@/components/ui/use-toast';
import InstagramPostPreview from '@/components/social/InstagramPostPreview';
import {
  Zap,
  Instagram,
  Music,
  Facebook,
  Twitter,
  Linkedin,
  TrendingUp,
  Eye,
  Heart,
  MessageCircle,
  Share,
  Clock,
  Target,
  Sparkles,
  Rocket
} from 'lucide-react';
import { socialMediaAI } from '@/services/ai/social/SocialMediaAI';
import { instagramAdapter } from '@/services/ai/social/adapters/InstagramAdapter';
import { tiktokAdapter } from '@/services/ai/social/adapters/TikTokAdapter';
import { aiServiceManager } from '@/services/ai/AIServiceManager';
import { ImageScrapingIntegration } from '@/services/ai/integrations/ImageScrapingIntegration';

interface ProductData {
  id: string;
  name: string;
  category: string;
  description?: string;
  price?: number;
  features?: string[];
  image?: string;
  additional_images?: string[];
  slug?: string;
}

interface GeneratedCampaign {
  platform: string;
  content: any;
  estimated_reach: number;
  viral_potential: number;
  engagement_prediction: number;
}

const SocialMediaPage: React.FC = () => {
  const { supabase } = useSupabase();
  const [products, setProducts] = useState<ProductData[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<ProductData | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['instagram', 'tiktok']);
  const [campaignTheme, setCampaignTheme] = useState('product_showcase');
  const [generatedCampaigns, setGeneratedCampaigns] = useState<GeneratedCampaign[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [isScrapingImages, setIsScrapingImages] = useState(false);
  const [scrapedImages, setScrapedImages] = useState<{[key: string]: any[]}>({});
  const [manualImages, setManualImages] = useState<{[key: string]: string[]}>({});
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Initialize AI service and fetch products
  useEffect(() => {
    const initializeServices = async () => {
      // Initialize AI service manager
      try {
        console.log('🔄 Initializing AI Service Manager...');

        // Check if we have any API keys available
        const config = {
          deepseek_key: import.meta.env.VITE_DEEPSEEK_API_KEY || '',
          gemini_key: import.meta.env.VITE_GEMINI_API_KEY || '',
          openrouter_key: import.meta.env.VITE_OPENROUTER_API_KEY || '',
        };

        console.log('🔑 API Keys available:', {
          deepseek: !!config.deepseek_key,
          gemini: !!config.gemini_key,
          openrouter: !!config.openrouter_key
        });

        await aiServiceManager.initialize(config);
        console.log('✅ AI Service Manager initialized successfully');

        // Test if processRequest is available
        console.log('🧪 Testing AI Service Manager:', typeof aiServiceManager.processRequest);

      } catch (error) {
        console.error('❌ AI Service Manager initialization failed:', error);
        console.warn('⚠️ Using fallback content for social media generation');
      }
    };

    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);

        // First, fetch all categories to create a lookup map
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('id, name');

        if (categoriesError) {
          console.error('Error fetching categories:', categoriesError);
          toast({
            title: 'Error',
            description: 'Failed to load categories',
            variant: 'destructive',
          });
          return;
        }

        // Create category lookup map
        const categoryMap = new Map();
        categoriesData?.forEach(cat => {
          categoryMap.set(cat.id, cat.name);
        });

        console.log('🔍 Category map:', categoryMap);

        // Fetch products with images and slug
        const { data, error } = await supabase
          .from('products')
          .select('id, name, description, price, category_id, image, additional_images, slug')
          .eq('is_active', true)
          .limit(100);

        console.log('Products fetched:', data?.length, 'products');

        if (error) {
          console.error('Supabase error details:', error);
          console.error('Error message:', error.message);
          console.error('Error code:', error.code);
          toast({
            title: 'Error',
            description: `Failed to load products: ${error.message}`,
            variant: 'destructive',
          });
          return;
        }

        const formattedProducts: ProductData[] = data.map(product => ({
          id: product.id,
          name: product.name,
          category: categoryMap.get(product.category_id) || 'uncategorized', // Use actual category name from map
          description: product.description || '',
          price: product.price || 0,
          features: [], // Could be extracted from description or separate field
          image: product.image || '',
          additional_images: product.additional_images || [],
          slug: product.slug || ''
        }));

        console.log('🔍 Raw product data from DB:', data?.slice(0, 2));
        console.log('🔍 Formatted products with categories:', formattedProducts.slice(0, 3));
        setProducts(formattedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast({
          title: 'Error',
          description: 'Failed to load products',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingProducts(false);
      }
    };

    // Initialize services and fetch products
    const initialize = async () => {
      await initializeServices();
      await fetchProducts();
    };

    initialize();
  }, [supabase]);

  const platforms = [
    { id: 'instagram', name: 'Instagram', icon: Instagram, color: 'bg-gradient-to-r from-purple-500 to-pink-500' },
    { id: 'tiktok', name: 'TikTok', icon: Music, color: 'bg-black' },
    { id: 'facebook', name: 'Facebook', icon: Facebook, color: 'bg-blue-600' },
    { id: 'twitter', name: 'Twitter', icon: Twitter, color: 'bg-blue-400' },
    { id: 'linkedin', name: 'LinkedIn', icon: Linkedin, color: 'bg-blue-700' }
  ];

  const campaignThemes = [
    { id: 'product_showcase', name: 'Product Showcase', description: 'Highlight product features and benefits' },
    { id: 'lifestyle', name: 'Lifestyle', description: 'Show product in lifestyle context' },
    { id: 'educational', name: 'Educational', description: 'Educate audience about product category' },
    { id: 'behind_scenes', name: 'Behind the Scenes', description: 'Show quality and craftsmanship' },
    { id: 'customer_stories', name: 'Customer Stories', description: 'Feature customer testimonials' }
  ];

  const handleGenerateCampaign = async () => {
    if (!selectedProduct) return;

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedCampaigns([]);

    try {
      const campaigns: GeneratedCampaign[] = [];
      const progressStep = 100 / selectedPlatforms.length;

      for (let i = 0; i < selectedPlatforms.length; i++) {
        const platform = selectedPlatforms[i];
        setGenerationProgress((i + 0.5) * progressStep);

        // Simulate AI generation delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        let content;

        // Debug: Log what we're passing to the adapters
        console.log('🔍 Selected Product being passed to adapter:', selectedProduct);
        console.log('🔍 Product name:', selectedProduct?.name);
        console.log('🔍 Product category:', selectedProduct?.category);

        // Add product URL to the product data for all platforms
        const productWithUrl = {
          ...selectedProduct,
          product_url: getProductUrl(selectedProduct),
          shop_link: getProductUrl(selectedProduct)
        };

        if (platform === 'instagram') {
          content = await instagramAdapter.generateInstagramCampaign(productWithUrl);
        } else if (platform === 'tiktok') {
          content = await tiktokAdapter.generateTikTokCampaign(productWithUrl);
        } else {
          // Generate generic social content for other platforms
          console.log(`🔍 Generating content for ${platform} using socialMediaAI...`);
          try {
            const platformContent = await socialMediaAI.generatePlatformContent(productWithUrl, platform, campaignTheme);
            console.log(`✅ Generated ${platform} content:`, platformContent);

            // Transform the content to match the expected structure
            content = {
              content: platformContent.content,
              platform: platformContent.platform,
              metadata: platformContent.metadata
            };

          } catch (error) {
            console.error(`❌ Failed to generate ${platform} content:`, error);
            // Create fallback content structure that matches what the UI expects
            const productUrl = getProductUrl(selectedProduct);
            content = {
              content: {
                caption: `🌟 Discover ${selectedProduct.name} - Premium ${selectedProduct.category}

Perfect for ${selectedProduct.category} enthusiasts! Our ${selectedProduct.name} delivers exceptional quality and value.

✅ Premium quality guaranteed
✅ Fast, secure delivery
✅ Expert customer support
✅ Only £${selectedProduct.price}

🛒 Shop now: ${productUrl}

Experience the difference quality makes!`,
                hashtags: [`#${selectedProduct.category.replace(/[^a-zA-Z0-9]/g, '')}`, '#Quality', '#Premium', '#BitsNBongs'],
                image_suggestions: [
                  `${selectedProduct.name} product shot`,
                  `Lifestyle image with ${selectedProduct.name}`,
                  `${selectedProduct.category} collection showcase`
                ],
                optimal_post_time: '12:00',
                engagement_prediction: 75,
                call_to_action: `Shop ${selectedProduct.name} now!`,
                product_url: productUrl
              },
              platform: platform,
              metadata: {
                character_count: 250,
                hashtag_count: 4,
                estimated_reach: 1000,
                viral_potential: 70
              }
            };
          }
        }

        // Extract metrics from content structure
        const metrics = content.metadata || content.content?.metadata || {};

        campaigns.push({
          platform,
          content: content.content || content,
          estimated_reach: metrics.estimated_reach || content.estimated_reach || Math.floor(Math.random() * 5000) + 1000,
          viral_potential: metrics.viral_potential || content.viral_potential || Math.floor(Math.random() * 40) + 60,
          engagement_prediction: metrics.engagement_prediction || content.engagement_prediction || Math.floor(Math.random() * 30) + 70
        });

        setGenerationProgress((i + 1) * progressStep);
      }

      setGeneratedCampaigns(campaigns);
    } catch (error) {
      console.error('Campaign generation failed:', error);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId]
    );
  };

  const handleExportContent = (campaign: GeneratedCampaign) => {
    const exportData = {
      product: selectedProduct?.name,
      platform: campaign.platform,
      content: campaign.content,
      metrics: {
        estimated_reach: campaign.estimated_reach,
        viral_potential: campaign.viral_potential,
        engagement_prediction: campaign.engagement_prediction
      },
      generated_at: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${campaign.platform}-campaign-${selectedProduct?.name?.replace(/\s+/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Content Exported',
      description: `${campaign.platform} campaign content has been downloaded`,
    });
  };

  const handleSchedulePost = (campaign: GeneratedCampaign) => {
    // This would integrate with scheduling service
    toast({
      title: 'Scheduling Feature',
      description: 'Post scheduling will be available in the next update!',
    });
  };

  const handlePublishNow = (campaign: GeneratedCampaign) => {
    // This would integrate with social media APIs
    toast({
      title: 'Publishing Feature',
      description: 'Direct publishing will be available in the next update!',
    });
  };

  const handleScrapeImages = async (campaign: GeneratedCampaign) => {
    if (!selectedProduct) return;

    setIsScrapingImages(true);

    try {
      console.log('🖼️ Starting image scraping for:', selectedProduct.name);
      console.log('🔍 Product details:', {
        id: selectedProduct.id,
        name: selectedProduct.name,
        category: selectedProduct.category
      });

      // Check if ImageScrapingIntegration is available
      try {
        // Initialize Agent 2's image scraping integration
        const imageIntegration = new ImageScrapingIntegration();

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Image scraping timeout after 15 seconds')), 15000);
        });

        // Scrape images for the selected product with timeout
        const scrapingPromise = imageIntegration.scrapeProductImages({
          product_id: selectedProduct.id,
          product_name: selectedProduct.name,
          category: selectedProduct.category,
          max_images: 3, // Further reduced for speed
          min_quality: 0.5, // Lower threshold
          preferred_sources: ['seedsman.com', 'cbdoil.co.uk']
        });

        const result = await Promise.race([scrapingPromise, timeoutPromise]) as any;

        console.log('🔍 Scraping result:', result);

        if (result.success && result.images && result.images.length > 0) {
          // Store scraped images for this campaign
          setScrapedImages(prev => ({
            ...prev,
            [`${campaign.platform}-${selectedProduct.id}`]: result.images
          }));

          toast({
            title: '🎯 Images Found!',
            description: `Found ${result.images.length} high-quality images. Cost savings: ${result.cost_savings || '£0'}`,
          });

          console.log('✅ Image scraping successful:', result);
        } else {
          throw new Error('No images found in scraping result');
        }
      } catch (scrapingError) {
        console.warn('⚠️ Image scraping service unavailable, using mock images:', scrapingError);

        // Fallback: Create mock scraped images based on product
        const mockImages = [
          {
            url: `https://picsum.photos/300/300?random=${selectedProduct.id.slice(0, 8)}`,
            alt: `${selectedProduct.name} - Demo Image 1`,
            quality_score: 0.85,
            source: 'picsum.photos',
            file_size: '45KB',
            dimensions: '300x300'
          },
          {
            url: `https://picsum.photos/300/300?random=${selectedProduct.id.slice(8, 16)}`,
            alt: `${selectedProduct.name} - Demo Image 2`,
            quality_score: 0.78,
            source: 'picsum.photos',
            file_size: '42KB',
            dimensions: '300x300'
          },
          {
            url: `https://picsum.photos/300/300?random=${selectedProduct.id.slice(16, 24)}`,
            alt: `${selectedProduct.name} - Demo Image 3`,
            quality_score: 0.72,
            source: 'picsum.photos',
            file_size: '38KB',
            dimensions: '300x300'
          }
        ];

        // Store mock images
        setScrapedImages(prev => ({
          ...prev,
          [`${campaign.platform}-${selectedProduct.id}`]: mockImages
        }));

        toast({
          title: '🔄 Demo Images Generated',
          description: `Image scraping service is unavailable. Generated ${mockImages.length} demo images. Use manual upload for real images.`,
        });
      }
    } catch (error) {
      console.error('❌ Image scraping failed:', error);

      toast({
        title: 'Image Scraping Failed',
        description: 'Image scraping is currently unavailable. Please use the manual upload option to add images.',
        variant: 'destructive',
      });
    } finally {
      setIsScrapingImages(false);
    }
  };

  const getPlatformIcon = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform ? platform.icon : Instagram;
  };

  const getPlatformColor = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform ? platform.color : 'bg-gray-500';
  };

  // Helper function to get product URL
  const getProductUrl = (product: ProductData) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/product/${product.slug || product.id}`;
  };

  // Helper function to get product image URL
  const getProductImageUrl = (imageField: string) => {
    if (!imageField) return null;

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';

    if (imageField.startsWith('http')) {
      return imageField;
    } else if (imageField.startsWith('/storage/')) {
      return `${supabaseUrl}${imageField}`;
    } else if (imageField.startsWith('product-images/')) {
      return `${supabaseUrl}/storage/v1/object/public/${imageField}`;
    } else {
      return `${supabaseUrl}/storage/v1/object/public/product-images/${imageField}`;
    }
  };

  // Handle manual image upload
  const handleManualImageUpload = async (campaign: GeneratedCampaign, file: File) => {
    if (!selectedProduct) {
      toast({
        title: 'No Product Selected',
        description: 'Please select a product first',
        variant: 'destructive',
      });
      return;
    }

    console.log('📤 Starting manual image upload:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      campaign: campaign.platform,
      product: selectedProduct.name
    });

    setIsUploadingImage(true);

    try {
      // Validate file
      if (!file.type.startsWith('image/')) {
        throw new Error('Please select an image file');
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        throw new Error('Image file is too large. Please select a file under 10MB');
      }

      // Generate a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `social-${campaign.platform}-${selectedProduct.id}-${Date.now()}.${fileExt}`;
      const filePath = `product-images/${fileName}`;

      console.log('📤 Uploading to Supabase:', filePath);

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error('❌ Supabase upload error:', error);
        throw error;
      }

      console.log('✅ Upload successful:', data);

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);

      console.log('🔗 Public URL:', publicUrlData);

      if (publicUrlData?.publicUrl) {
        // Add to manual images
        const campaignKey = `${campaign.platform}-${selectedProduct.id}`;
        setManualImages(prev => ({
          ...prev,
          [campaignKey]: [...(prev[campaignKey] || []), publicUrlData.publicUrl]
        }));

        toast({
          title: '✅ Image Uploaded Successfully!',
          description: `Your custom image has been added to the ${campaign.platform} campaign`,
        });

        console.log('✅ Image added to campaign:', campaignKey);
      } else {
        throw new Error('Failed to get public URL for uploaded image');
      }
    } catch (error) {
      console.error('❌ Manual image upload failed:', error);

      let errorMessage = 'There was an error uploading your image.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Upload Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Rocket className="w-8 h-8 text-red-500" />
            Social Media AI Revolution
          </h1>
          <p className="text-muted-foreground">
            Generate viral content across all platforms with AI-powered precision
          </p>
        </div>
        <Badge variant="secondary" className="bg-red-100 text-red-700">
          🔴 Red Pill Activated
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Campaign Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Product Selection */}
              <div>
                <Label>Select Product</Label>
                <Select onValueChange={(value) => {
                  const product = products.find(p => p.id === value);
                  setSelectedProduct(product || null);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder={isLoadingProducts ? "Loading products..." : "Choose a product..."} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingProducts ? (
                      <SelectItem value="loading" disabled>Loading products...</SelectItem>
                    ) : products.length === 0 ? (
                      <SelectItem value="no-products" disabled>No products found</SelectItem>
                    ) : (
                      products.map(product => (
                        <SelectItem key={product.id} value={product.id}>
                          <div>
                            <div className="font-medium">{product.name}</div>
                            <div className="text-xs text-muted-foreground">{product.category} • £{product.price}</div>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Platform Selection */}
              <div>
                <Label>Select Platforms</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {platforms.map(platform => {
                    const Icon = platform.icon;
                    const isSelected = selectedPlatforms.includes(platform.id);

                    return (
                      <div
                        key={platform.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-all ${
                          isSelected
                            ? 'border-primary bg-primary/10'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handlePlatformToggle(platform.id)}
                      >
                        <div className="flex items-center gap-2">
                          <Icon className="w-4 h-4" />
                          <span className="text-sm font-medium">{platform.name}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Campaign Theme */}
              <div>
                <Label>Campaign Theme</Label>
                <Select value={campaignTheme} onValueChange={setCampaignTheme}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {campaignThemes.map(theme => (
                      <SelectItem key={theme.id} value={theme.id}>
                        <div>
                          <div className="font-medium">{theme.name}</div>
                          <div className="text-xs text-muted-foreground">{theme.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Generate Button */}
              <Button
                onClick={handleGenerateCampaign}
                disabled={!selectedProduct || selectedPlatforms.length === 0 || isGenerating}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                {isGenerating ? (
                  <>
                    <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                    Generating AI Magic...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    Generate Viral Campaign
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Selected Product Info */}
          {selectedProduct && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Selected Product
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Product Image */}
                {selectedProduct.image && (
                  <div className="flex justify-center">
                    <img
                      src={getProductImageUrl(selectedProduct.image) || ''}
                      alt={selectedProduct.name}
                      className="w-32 h-32 object-cover rounded-lg border"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                )}

                {/* Product Details */}
                <div className="space-y-2">
                  <div>
                    <Label className="text-xs text-muted-foreground">Product Name</Label>
                    <div className="font-medium">{selectedProduct.name}</div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Category</Label>
                    <div className="text-sm">{selectedProduct.category}</div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Price</Label>
                    <div className="text-sm font-medium">£{selectedProduct.price}</div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Product Link</Label>
                    <div className="text-xs">
                      <a
                        href={getProductUrl(selectedProduct)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 break-all"
                      >
                        {getProductUrl(selectedProduct)}
                      </a>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {isGenerating && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>AI Generation Progress</span>
                    <span>{Math.round(generationProgress)}%</span>
                  </div>
                  <Progress value={generationProgress} className="h-2" />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Generated Content Panel */}
        <div className="lg:col-span-2">
          {generatedCampaigns.length > 0 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Generated Campaigns</h2>
                <div className="flex gap-2">
                  {generatedCampaigns.map(campaign => {
                    const Icon = getPlatformIcon(campaign.platform);
                    return (
                      <div key={campaign.platform} className={`p-2 rounded-lg ${getPlatformColor(campaign.platform)}`}>
                        <Icon className="w-4 h-4 text-white" />
                      </div>
                    );
                  })}
                </div>
              </div>

              <Tabs defaultValue={generatedCampaigns[0]?.platform} className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  {generatedCampaigns.map(campaign => {
                    const Icon = getPlatformIcon(campaign.platform);
                    return (
                      <TabsTrigger key={campaign.platform} value={campaign.platform} className="flex items-center gap-1">
                        <Icon className="w-4 h-4" />
                        <span className="hidden sm:inline">{campaign.platform}</span>
                      </TabsTrigger>
                    );
                  })}
                </TabsList>

                {generatedCampaigns.map(campaign => (
                  <TabsContent key={campaign.platform} value={campaign.platform} className="space-y-4">
                    {/* Campaign Metrics */}
                    <div className="grid grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2">
                            <Eye className="w-4 h-4 text-blue-500" />
                            <div>
                              <div className="text-sm text-muted-foreground">Est. Reach</div>
                              <div className="font-semibold">{campaign.estimated_reach.toLocaleString()}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="w-4 h-4 text-green-500" />
                            <div>
                              <div className="text-sm text-muted-foreground">Viral Potential</div>
                              <div className="font-semibold">{campaign.viral_potential}%</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2">
                            <Heart className="w-4 h-4 text-red-500" />
                            <div>
                              <div className="text-sm text-muted-foreground">Engagement</div>
                              <div className="font-semibold">{campaign.engagement_prediction}%</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Platform-Specific Content */}
                    {campaign.platform === 'instagram' && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Instagram className="w-5 h-5" />
                            Instagram Campaign
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label className="font-semibold">Main Post Caption</Label>
                            <Textarea
                              value={campaign.content.main_post?.caption || 'Generated Instagram caption...'}
                              readOnly
                              className="mt-1"
                              rows={6}
                            />
                          </div>

                          <div>
                            <Label className="font-semibold">Hashtags</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {(campaign.content.main_post?.hashtags || []).slice(0, 10).map((tag: string, index: number) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div>
                            <Label className="font-semibold">Story Series ({campaign.content.story_series?.length || 0} stories)</Label>
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-1">
                              {(campaign.content.story_series || []).map((story: any, index: number) => (
                                <Card key={index} className="p-3">
                                  <div className="text-sm font-medium">Story {index + 1}</div>
                                  <div className="text-xs text-muted-foreground mt-1">{story.caption}</div>
                                </Card>
                              ))}
                            </div>
                          </div>

                          {/* Instagram Post Preview */}
                          <div className="mb-6">
                            <Label className="font-semibold">Post Preview</Label>
                            <div className="mt-2">
                              <InstagramPostPreview
                                caption={campaign.content.main_post?.caption || 'Generated Instagram caption...'}
                                hashtags={(campaign.content.main_post?.hashtags || []).slice(0, 10)}
                                images={[
                                  // First try to use manual images
                                  ...(manualImages[`${campaign.platform}-${selectedProduct?.id}`] || []),
                                  // Then use scraped images
                                  ...(scrapedImages[`${campaign.platform}-${selectedProduct?.id}`]
                                    ? scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].map((img: any) => img.url)
                                    : []),
                                  // If no images, use product image
                                  ...(selectedProduct?.image ? [getProductImageUrl(selectedProduct.image) || ''] : [])
                                ]}
                                productName={selectedProduct?.name || 'Product'}
                              />
                            </div>
                          </div>

                          {/* Instagram Image Scraping */}
                          <div>
                            <div className="flex items-center justify-between">
                              <Label className="font-semibold">Product Images</Label>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleScrapeImages(campaign)}
                                disabled={isScrapingImages}
                                className="text-xs"
                              >
                                {isScrapingImages ? (
                                  <>
                                    <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                                    Scraping...
                                  </>
                                ) : (
                                  <>
                                    🖼️ Find Real Images
                                  </>
                                )}
                              </Button>
                            </div>

                            {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`] ? (
                              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-2">
                                {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].slice(0, 6).map((image: any, index: number) => (
                                  <div key={index} className="relative group">
                                    <img
                                      src={image.url}
                                      alt={image.alt}
                                      className="w-full h-24 object-cover rounded-lg border"
                                      onError={(e) => {
                                        (e.target as HTMLImageElement).style.display = 'none';
                                      }}
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                      <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                        Quality: {Math.round(image.quality_score * 100)}%
                                        <br />
                                        Source: {image.source}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="p-3 bg-gray-50 rounded-lg mt-2 text-center text-sm text-muted-foreground">
                                Click "Find Real Images" to scrape high-quality product photos
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {campaign.platform === 'tiktok' && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Music className="w-5 h-5" />
                            TikTok Campaign
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label className="font-semibold">Viral Hook</Label>
                            <div className="p-3 bg-gray-50 rounded-lg mt-1">
                              <div className="font-medium text-lg">{campaign.content.primary_video?.hook || 'Generated viral hook...'}</div>
                            </div>
                          </div>

                          <div>
                            <Label className="font-semibold">Video Script</Label>
                            <div className="space-y-2 mt-1">
                              {(campaign.content.primary_video?.script || []).map((line: string, index: number) => (
                                <div key={index} className="flex items-start gap-2">
                                  <Badge variant="outline" className="text-xs">{index + 1}</Badge>
                                  <div className="text-sm">{line}</div>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div>
                            <Label className="font-semibold">Hashtag Challenges</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {(campaign.content.hashtag_challenges || []).map((challenge: string, index: number) => (
                                <Badge key={index} variant="secondary" className="text-xs bg-black text-white">
                                  {challenge}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* TikTok Post Preview */}
                          <div className="mb-6">
                            <Label className="font-semibold">Post Preview</Label>
                            <div className="mt-2">
                              <InstagramPostPreview
                                caption={campaign.content.primary_video?.hook || 'Generated TikTok hook...'}
                                hashtags={(campaign.content.hashtag_challenges || []).slice(0, 10)}
                                images={[
                                  // First try to use manual images
                                  ...(manualImages[`${campaign.platform}-${selectedProduct?.id}`] || []),
                                  // Then use scraped images
                                  ...(scrapedImages[`${campaign.platform}-${selectedProduct?.id}`]
                                    ? scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].map((img: any) => img.url)
                                    : []),
                                  // If no images, use product image
                                  ...(selectedProduct?.image ? [getProductImageUrl(selectedProduct.image) || ''] : [])
                                ]}
                                productName={selectedProduct?.name || 'Product'}
                                storeName="TikTok"
                              />
                            </div>
                          </div>

                          {/* TikTok Image Scraping */}
                          <div>
                            <div className="flex items-center justify-between">
                              <Label className="font-semibold">Video Thumbnails & Product Images</Label>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleScrapeImages(campaign)}
                                disabled={isScrapingImages}
                                className="text-xs"
                              >
                                {isScrapingImages ? (
                                  <>
                                    <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                                    Scraping...
                                  </>
                                ) : (
                                  <>
                                    🖼️ Find Real Images
                                  </>
                                )}
                              </Button>
                            </div>

                            {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`] ? (
                              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-2">
                                {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].slice(0, 6).map((image: any, index: number) => (
                                  <div key={index} className="relative group">
                                    <img
                                      src={image.url}
                                      alt={image.alt}
                                      className="w-full h-24 object-cover rounded-lg border"
                                      onError={(e) => {
                                        (e.target as HTMLImageElement).style.display = 'none';
                                      }}
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                      <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                        Quality: {Math.round(image.quality_score * 100)}%
                                        <br />
                                        Source: {image.source}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="p-3 bg-gray-50 rounded-lg mt-2 text-center text-sm text-muted-foreground">
                                Click "Find Real Images" for video thumbnails and product shots
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Generic Platform Content for Facebook, Twitter, LinkedIn, etc. */}
                    {!['instagram', 'tiktok'].includes(campaign.platform) && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2 capitalize">
                            {campaign.platform === 'facebook' && <Facebook className="w-5 h-5" />}
                            {campaign.platform === 'twitter' && <Twitter className="w-5 h-5" />}
                            {campaign.platform === 'linkedin' && <Linkedin className="w-5 h-5" />}
                            {!['facebook', 'twitter', 'linkedin'].includes(campaign.platform) && <Share className="w-5 h-5" />}
                            {campaign.platform.charAt(0).toUpperCase() + campaign.platform.slice(1)} Campaign
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label className="font-semibold">Post Content</Label>
                            <Textarea
                              value={campaign.content.caption || 'Generated content...'}
                              readOnly
                              className="mt-1"
                              rows={8}
                            />
                          </div>

                          <div>
                            <Label className="font-semibold">Hashtags</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {(campaign.content.hashtags || []).map((tag: string, index: number) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Post Preview */}
                          <div className="mb-6">
                            <Label className="font-semibold">Post Preview</Label>
                            <div className="mt-2">
                              <InstagramPostPreview
                                caption={campaign.content.caption || 'Generated caption...'}
                                hashtags={(campaign.content.hashtags || []).slice(0, 10)}
                                images={[
                                  // First try to use manual images
                                  ...(manualImages[`${campaign.platform}-${selectedProduct?.id}`] || []),
                                  // Then use scraped images
                                  ...(scrapedImages[`${campaign.platform}-${selectedProduct?.id}`]
                                    ? scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].map((img: any) => img.url)
                                    : []),
                                  // If no images, use product image
                                  ...(selectedProduct?.image ? [getProductImageUrl(selectedProduct.image) || ''] : [])
                                ]}
                                productName={selectedProduct?.name || 'Product'}
                                storeName={campaign.platform.charAt(0).toUpperCase() + campaign.platform.slice(1)}
                              />
                            </div>
                          </div>

                          {/* Images Section */}
                          <div>
                            <div className="flex items-center justify-between mb-3">
                              <Label className="font-semibold">Campaign Images</Label>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleScrapeImages(campaign)}
                                  disabled={isScrapingImages}
                                  className="text-xs"
                                >
                                  {isScrapingImages ? (
                                    <>
                                      <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                                      Scraping...
                                    </>
                                  ) : (
                                    <>
                                      🖼️ Find Images
                                    </>
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  disabled={isUploadingImage}
                                  className="text-xs"
                                  onClick={() => {
                                    const input = document.createElement('input');
                                    input.type = 'file';
                                    input.accept = 'image/*';
                                    input.multiple = true;
                                    input.onchange = (e) => {
                                      const files = Array.from((e.target as HTMLInputElement).files || []);
                                      files.forEach(file => handleManualImageUpload(campaign, file));
                                    };
                                    input.click();
                                  }}
                                >
                                  {isUploadingImage ? (
                                    <>
                                      <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                                      Uploading...
                                    </>
                                  ) : (
                                    <>
                                      📤 Upload Images
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>

                            {/* Drag and Drop Upload Zone */}
                            <div
                              className="border-2 border-dashed border-gray-300 rounded-lg p-4 mb-3 text-center hover:border-gray-400 transition-colors cursor-pointer"
                              onClick={() => {
                                const input = document.createElement('input');
                                input.type = 'file';
                                input.accept = 'image/*';
                                input.multiple = true;
                                input.onchange = (e) => {
                                  const files = Array.from((e.target as HTMLInputElement).files || []);
                                  files.forEach(file => handleManualImageUpload(campaign, file));
                                };
                                input.click();
                              }}
                              onDragOver={(e) => {
                                e.preventDefault();
                                e.currentTarget.classList.add('border-blue-400', 'bg-blue-50');
                              }}
                              onDragLeave={(e) => {
                                e.preventDefault();
                                e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50');
                              }}
                              onDrop={(e) => {
                                e.preventDefault();
                                e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50');
                                const files = Array.from(e.dataTransfer.files);
                                files.filter(file => file.type.startsWith('image/')).forEach(file => {
                                  handleManualImageUpload(campaign, file);
                                });
                              }}
                            >
                              <div className="text-gray-500">
                                <div className="text-2xl mb-2">📁</div>
                                <div className="text-sm">
                                  <span className="font-medium">Click to upload</span> or drag and drop images here
                                </div>
                                <div className="text-xs mt-1">PNG, JPG, GIF up to 10MB</div>
                              </div>
                            </div>

                            {/* Product Images from Database */}
                            {selectedProduct?.image && (
                              <div className="mb-3">
                                <div className="flex items-center justify-between">
                                  <Label className="text-xs text-muted-foreground">Product Images</Label>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-xs"
                                    onClick={() => {
                                      // Add product image to campaign
                                      if (selectedProduct.image) {
                                        const campaignKey = `${campaign.platform}-${selectedProduct.id}`;
                                        const productImageUrl = getProductImageUrl(selectedProduct.image) || '';

                                        // Only add if not already in the list
                                        setManualImages(prev => {
                                          const currentImages = prev[campaignKey] || [];
                                          if (!currentImages.includes(productImageUrl)) {
                                            toast({
                                              title: '✅ Product Image Added',
                                              description: 'Product image has been added to your campaign',
                                            });
                                            return {
                                              ...prev,
                                              [campaignKey]: [...currentImages, productImageUrl]
                                            };
                                          }
                                          return prev;
                                        });
                                      }
                                    }}
                                  >
                                    ➕ Use in Post
                                  </Button>
                                </div>
                                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-1">
                                  <div className="relative group">
                                    <img
                                      src={getProductImageUrl(selectedProduct.image) || ''}
                                      alt={selectedProduct.name}
                                      className="w-full h-24 object-cover rounded-lg border"
                                      onError={(e) => {
                                        (e.target as HTMLImageElement).style.display = 'none';
                                      }}
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                      <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                        <div className="mb-2">Product Image</div>
                                        <div className="flex gap-1 justify-center">
                                          <Button
                                            size="sm"
                                            variant="secondary"
                                            className="text-xs h-6 px-2"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              navigator.clipboard.writeText(getProductImageUrl(selectedProduct.image) || '');
                                              toast({ title: 'Image URL copied!' });
                                            }}
                                          >
                                            📋
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="secondary"
                                            className="text-xs h-6 px-2"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              window.open(getProductUrl(selectedProduct), '_blank');
                                            }}
                                          >
                                            🔗
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  {selectedProduct.additional_images?.slice(0, 2).map((img, index) => (
                                    <div key={index} className="relative group">
                                      <img
                                        src={getProductImageUrl(img) || ''}
                                        alt={`${selectedProduct.name} ${index + 2}`}
                                        className="w-full h-24 object-cover rounded-lg border"
                                        onError={(e) => {
                                          (e.target as HTMLImageElement).style.display = 'none';
                                        }}
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                        <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                          <div className="mb-2">Additional Image</div>
                                          <Button
                                            size="sm"
                                            variant="secondary"
                                            className="text-xs h-6 px-2"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              // Add additional image to campaign
                                              const campaignKey = `${campaign.platform}-${selectedProduct.id}`;
                                              const additionalImageUrl = getProductImageUrl(img) || '';
                                              setManualImages(prev => {
                                                const currentImages = prev[campaignKey] || [];
                                                if (!currentImages.includes(additionalImageUrl)) {
                                                  toast({
                                                    title: '✅ Additional Image Added',
                                                    description: 'Additional product image has been added to your campaign',
                                                  });
                                                  return {
                                                    ...prev,
                                                    [campaignKey]: [...currentImages, additionalImageUrl]
                                                  };
                                                }
                                                return prev;
                                              });
                                            }}
                                          >
                                            ➕ Use
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Manual Uploaded Images */}
                            {manualImages[`${campaign.platform}-${selectedProduct?.id}`] && (
                              <div className="mb-3">
                                <Label className="text-xs text-muted-foreground">Uploaded Images</Label>
                                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-1">
                                  {manualImages[`${campaign.platform}-${selectedProduct?.id}`].map((imageUrl, index) => (
                                    <div key={index} className="relative group">
                                      <img
                                        src={imageUrl}
                                        alt={`Uploaded ${index + 1}`}
                                        className="w-full h-24 object-cover rounded-lg border"
                                        onError={(e) => {
                                          (e.target as HTMLImageElement).style.display = 'none';
                                        }}
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                        <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                          <div className="mb-2">Custom Upload</div>
                                          <div className="flex gap-1 justify-center">
                                            <Button
                                              size="sm"
                                              variant="secondary"
                                              className="text-xs h-6 px-2"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                navigator.clipboard.writeText(imageUrl);
                                                toast({ title: 'Image URL copied!' });
                                              }}
                                            >
                                              📋
                                            </Button>
                                            <Button
                                              size="sm"
                                              variant="destructive"
                                              className="text-xs h-6 px-2"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                const campaignKey = `${campaign.platform}-${selectedProduct?.id}`;
                                                setManualImages(prev => ({
                                                  ...prev,
                                                  [campaignKey]: prev[campaignKey]?.filter((_, i) => i !== index) || []
                                                }));
                                                toast({ title: 'Image removed!' });
                                              }}
                                            >
                                              🗑️
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Scraped Images */}
                            {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`] ? (
                              <div className="mb-3">
                                <Label className="text-xs text-muted-foreground">Scraped Images</Label>
                                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-1">
                                  {scrapedImages[`${campaign.platform}-${selectedProduct?.id}`].slice(0, 6).map((image: any, index: number) => (
                                    <div key={index} className="relative group">
                                      <img
                                        src={image.url}
                                        alt={image.alt}
                                        className="w-full h-24 object-cover rounded-lg border"
                                        onError={(e) => {
                                          (e.target as HTMLImageElement).style.display = 'none';
                                        }}
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                                        <div className="text-white text-xs opacity-0 group-hover:opacity-100 text-center p-1">
                                          Quality: {Math.round(image.quality_score * 100)}%
                                          <br />
                                          Source: {image.source}
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              /* AI Image Suggestions Fallback */
                              campaign.content.image_suggestions && (
                                <div className="mb-3">
                                  <Label className="text-xs text-muted-foreground">AI Image Suggestions</Label>
                                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-1">
                                    {campaign.content.image_suggestions.map((suggestion: string, index: number) => (
                                      <Card key={index} className="p-3">
                                        <div className="text-sm">{suggestion}</div>
                                      </Card>
                                    ))}
                                  </div>
                                </div>
                              )
                            )}
                          </div>

                          {campaign.content.call_to_action && (
                            <div>
                              <Label className="font-semibold">Call to Action</Label>
                              <div className="p-3 bg-blue-50 rounded-lg mt-1">
                                <div className="font-medium text-blue-800">{campaign.content.call_to_action}</div>
                              </div>
                            </div>
                          )}

                          {/* Product Link */}
                          {selectedProduct && (
                            <div>
                              <Label className="font-semibold">Product Link</Label>
                              <div className="p-3 bg-green-50 rounded-lg mt-1">
                                <div className="text-sm text-green-800 break-all">
                                  <a
                                    href={getProductUrl(selectedProduct)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:underline"
                                  >
                                    {getProductUrl(selectedProduct)}
                                  </a>
                                </div>
                                <div className="text-xs text-green-600 mt-1">
                                  Include this link in your social media posts
                                </div>
                              </div>
                            </div>
                          )}

                          {campaign.content.optimal_post_time && (
                            <div>
                              <Label className="font-semibold">Optimal Post Time</Label>
                              <div className="p-2 bg-gray-50 rounded mt-1">
                                <div className="text-sm">{campaign.content.optimal_post_time}</div>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleSchedulePost(campaign)}
                      >
                        <Clock className="w-4 h-4 mr-2" />
                        Schedule Post
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleExportContent(campaign)}
                      >
                        <Share className="w-4 h-4 mr-2" />
                        Export Content
                      </Button>
                      <Button
                        className="flex-1 bg-green-600 hover:bg-green-700"
                        onClick={() => handlePublishNow(campaign)}
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Publish Now
                      </Button>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          ) : (
            <Card className="h-96 flex items-center justify-center">
              <div className="text-center space-y-4">
                <Sparkles className="w-16 h-16 mx-auto text-gray-400" />
                <div>
                  <h3 className="text-lg font-semibold">Ready to Generate Viral Content?</h3>
                  <p className="text-muted-foreground">Select a product and platforms to get started</p>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default SocialMediaPage;
