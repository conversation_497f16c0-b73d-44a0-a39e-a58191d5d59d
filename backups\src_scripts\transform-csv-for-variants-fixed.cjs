/**
 * CSV Transformer for Variant-Based Product System
 * 
 * This script transforms a standard Wix-exported CSV into two separate CSVs:
 * 1. products.csv - Contains base product information
 * 2. variants.csv - Contains variant information
 * 
 * It also handles image filename transformations and flags products without images.
 * 
 * Usage:
 * 1. Place your input CSV in the data directory
 * 2. Run: node src/scripts/transform-csv-for-variants-fixed.cjs [input-file.csv] [output-path.csv]
 * 3. Find output CSVs in the data/output directory
 */

const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');

// Define configuration (could be moved to a config file)
const CONFIG = {
  inputDir: './data',
  outputDir: './data/output',
  inputFileName: 'catalog_products.csv', // Default file name
  productsOutputFileName: 'products-transformed.csv',
  variantsOutputFileName: 'product_variants.csv',
};

// Handle command line arguments
if (process.argv.length > 2) {
  // If a full path is provided, use it directly
  if (process.argv[2].includes('/') || process.argv[2].includes('\\')) {
    CONFIG.inputFilePath = process.argv[2];
  } else {
    // Otherwise, assume it's just a filename in the data directory
    CONFIG.inputFileName = process.argv[2];
  }
}

// If output path is provided
if (process.argv.length > 3) {
  const outputPath = process.argv[3];
  const outputDir = path.dirname(outputPath);
  CONFIG.outputDir = outputDir;
}

/**
 * Transform image URL to match the expected format in Supabase
 * @param {string} url - The image URL to transform
 * @returns {string} - The transformed URL
 */
function transformImageUrl(url) {
  if (!url) return null;
  
  // Extract the filename from the URL
  const filename = url.split('/').pop();
  if (!filename) return null;
  
  // Handle specific format from catalog: 7caa35_6a5c1b6181f543109d047a419a70a6aa~mv2.jpg
  // Remove ~mv2 from image URLs but preserve the original filename structure
  let transformedFilename = filename.replace(/~mv2/g, '');
  
  // Also handle ~cv2 suffix that might be in some files
  transformedFilename = transformedFilename.replace(/~cv2/g, '');
  
  // Convert to .webp if it's a jpg or png
  if (transformedFilename.match(/\.(jpg|jpeg|png)$/i)) {
    transformedFilename = transformedFilename.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  
  // Use the correct Supabase storage URL without duplicate product-images paths
  return `https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/${transformedFilename}`;
}

/**
 * Parse price from string to number
 * @param {string} priceStr - Price as string
 * @returns {number} - Price as number
 */
function parsePrice(priceStr) {
  if (!priceStr) return 0;
  
  // Remove currency symbols, commas, etc.
  const cleanPrice = priceStr.toString().replace(/[^0-9.]/g, '');
  const price = parseFloat(cleanPrice);
  
  return isNaN(price) ? 0 : price;
}

/**
 * Extract option values from a comma-separated string
 * @param {string} optionStr - Option values as comma-separated string
 * @returns {string[]} - Array of option values
 */
function extractOptionValues(optionStr) {
  if (!optionStr) return [];
  
  return optionStr
    .split(',')
    .map(val => val.trim())
    .filter(val => val !== 'DROP_DOWN' && val !== '');
}

/**
 * Generate a URL-friendly slug from a string
 * @param {string} str - String to slugify
 * @returns {string} - Slugified string
 */
function generateSlug(str) {
  return slugify(str, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
}

/**
 * Determine the best display type for an option based on its name
 * @param {string} optionName - The name of the option
 * @returns {string} - The display type (color_swatch, button, dropdown)
 */
function getDisplayTypeForOption(optionName) {
  const name = optionName.toLowerCase();
  
  if (name.includes('color') || name.includes('colour')) {
    return 'color_swatch';
  } else if (name.includes('size')) {
    return 'button';
  } else {
    return 'dropdown';
  }
}

// Main transformation function
async function transformCsv() {
  console.log('Starting CSV transformation...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Read input CSV
  let inputPath;
  if (CONFIG.inputFilePath) {
    // Use the direct file path if provided
    inputPath = CONFIG.inputFilePath;
  } else {
    // Otherwise construct the path from inputDir and inputFileName
    inputPath = path.join(CONFIG.inputDir, CONFIG.inputFileName);
  }
  
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    return;
  }
  
  try {
    const inputCsv = fs.readFileSync(inputPath, 'utf8');
    
    // Parse CSV
    const { data } = Papa.parse(inputCsv, {
      header: true,
      skipEmptyLines: true,
    });
    
    console.log(`Found ${data.length} rows in input CSV`);
    
    // Group rows by product
    const productGroups = {};
    const variantRows = {};
    
    // First pass: identify base products and variants
    data.forEach(row => {
      // Check if this is a variant (Choice) or a base product (Product)
      if (row.fieldType === 'Choice') {
        // For variants, extract the base product ID (remove _v1, _v2, etc.)
        const baseProductId = row.handleId.split('_v')[0];
        
        // Store variant rows separately
        if (!variantRows[baseProductId]) {
          variantRows[baseProductId] = [];
        }
        variantRows[baseProductId].push(row);
      } else {
        // For base products, use the handleId directly
        const productId = row.handleId;
        if (!productGroups[productId]) {
          productGroups[productId] = [];
        }
        productGroups[productId].push(row);
      }
    });
    
    // Second pass: add variant rows to their base product groups
    Object.keys(variantRows).forEach(baseProductId => {
      if (productGroups[baseProductId]) {
        productGroups[baseProductId] = productGroups[baseProductId].concat(variantRows[baseProductId]);
      }
    });
    
    console.log(`Grouped into ${Object.keys(productGroups).length} unique products`);
    
    // Transform products and create variants
    const transformedProducts = [];
    const productVariants = [];
    const now = new Date().toISOString();
    
    // Process each product group
    Object.entries(productGroups).forEach(([productKey, group]) => {
      // Use the first row as the base product
      const baseRow = group[0];
      const productId = uuidv4();
      const productName = baseRow.name;
      
      // Create option definitions object for the product
      const optionDefinitions = {};
      const optionNames = [];
      
      // Process option 1
      if (baseRow.productOptionName1 && baseRow.productOptionDescription1) {
        const optionName = baseRow.productOptionName1;
        // Extract option values from comma-separated string
        const optionValues = extractOptionValues(baseRow.productOptionDescription1);
        
        if (optionValues.length > 0) {
          // Add to option definitions
          optionDefinitions[optionName] = optionValues;
          optionNames.push(optionName);
        }
      }
      
      // Process option 2
      if (baseRow.productOptionName2 && baseRow.productOptionDescription2) {
        const optionName = baseRow.productOptionName2;
        const optionValues = extractOptionValues(baseRow.productOptionDescription2);
        
        if (optionValues.length > 0) {
          optionDefinitions[optionName] = optionValues;
          optionNames.push(optionName);
        }
      }
      
      // Process option 3
      if (baseRow.productOptionName3 && baseRow.productOptionDescription3) {
        const optionName = baseRow.productOptionName3;
        const optionValues = extractOptionValues(baseRow.productOptionDescription3);
        
        if (optionValues.length > 0) {
          optionDefinitions[optionName] = optionValues;
          optionNames.push(optionName);
        }
      }
      
      // Transform image URLs
      let transformedImageUrl = null;
      let additionalImageUrls = [];
      
      if (baseRow.productImageUrl) {
        const imageUrls = baseRow.productImageUrl.split(';');
        
        // First image is the main product image
        if (imageUrls.length > 0 && imageUrls[0]) {
          transformedImageUrl = transformImageUrl(imageUrls[0].trim());
          console.log(`Transformed image URL: ${transformedImageUrl}`);
        }
        
        // Additional images
        if (imageUrls.length > 1) {
          additionalImageUrls = imageUrls.slice(1)
            .map(url => url.trim())
            .filter(url => url) // Remove empty strings
            .map(url => transformImageUrl(url));
        }
      }
      
      // Create transformed product
      const transformedProduct = {
        id: productId,
        name: productName,
        slug: generateSlug(productName),
        description: baseRow.description || '',
        price: parsePrice(baseRow.price),
        sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)) : null,
        image: transformedImageUrl || null,
        additional_images: JSON.stringify(additionalImageUrls),
        category_id: null, // Would need to be populated based on your category structure
        brand_id: null, // Would need to be populated based on your brand structure
        sku: baseRow.sku || null,
        stock_quantity: baseRow.inventory === 'InStock' ? 100 : parseInt(baseRow.inventory || '0'),
        weight: null,
        in_stock: baseRow.inventory === 'InStock' ? true : (parseInt(baseRow.inventory || '0') > 0),
        is_featured: false,
        is_new: false,
        is_active: transformedImageUrl ? true : false,
        // Only include option_definitions if there are actually options defined
        option_definitions: Object.keys(optionDefinitions).length > 0 ? JSON.stringify(optionDefinitions) : '{}',
        created_at: now,
        updated_at: now,
      };
      
      transformedProducts.push(transformedProduct);
      
      // Create variants
      if (Object.keys(optionDefinitions).length > 0) {
        // If only one option, create variants for each value
        if (optionNames.length === 1) {
          const optionName = optionNames[0];
          const optionValues = optionDefinitions[optionName];
          
          optionValues.forEach(value => {
            const variantId = uuidv4();
            
            // Look for a matching variant row in the group that has this option value
            const matchingVariantRow = group.find(row => 
              row.fieldType === 'Choice' && 
              row[`productOptionDescription${optionName.match(/\d+$/) ? optionName.match(/\d+$/)[0] : '1'}`] === value
            );
            
            // Base price from the main product
            const basePrice = parsePrice(baseRow.price);
            
            // If we have a matching variant row, use its price as the final price
            // This is the absolute price, not an adjustment
            const variantPrice = matchingVariantRow ? parsePrice(matchingVariantRow.price) : basePrice;
            
            // Handle sale prices similarly
            const variantSalePrice = matchingVariantRow && matchingVariantRow.discountValue ? 
              (parsePrice(matchingVariantRow.price) - parsePrice(matchingVariantRow.discountValue)) : 
              (baseRow.discountValue ? (basePrice - parsePrice(baseRow.discountValue)) : null);
            
            // For debugging
            if (matchingVariantRow) {
              console.log(`Variant ${value}: Base price: ${basePrice}, Final price: ${variantPrice}`);
            }
            
            // Use the variant's stock quantity if available, otherwise use the base stock
            const variantStock = matchingVariantRow ? 
              (matchingVariantRow.inventory === 'InStock' ? 100 : parseInt(matchingVariantRow.inventory || '0')) :
              (baseRow.inventory === 'InStock' ? 100 : parseInt(baseRow.inventory || '0'));
            
            // Use the variant's SKU if available, otherwise generate one
            const variantSku = matchingVariantRow && matchingVariantRow.sku ? 
              matchingVariantRow.sku : 
              (baseRow.sku ? `${baseRow.sku}-${value.substring(0, 2).replace(/\s+/g, '')}` : null);
            
            const variant = {
              id: variantId,
              product_id: productId,
              variant_name: `${productName} - ${value}`,
              sku: variantSku,
              price: variantPrice,
              sale_price: variantSalePrice,
              stock_quantity: variantStock,
              in_stock: variantStock > 0,
              image: transformedImageUrl || null,
              option_combination: JSON.stringify({ [optionName]: value }),
              is_active: transformedImageUrl ? true : false,
              external_id: matchingVariantRow ? matchingVariantRow.handleId : `${baseRow.handleId}_var_${value.replace(/\s+/g, '')}`,
              created_at: now,
              updated_at: now,
            };
            
            productVariants.push(variant);
          });
        }
        // If two options, create variants for each combination
        else if (optionNames.length === 2) {
          const option1Name = optionNames[0];
          const option2Name = optionNames[1];
          const option1Values = optionDefinitions[option1Name];
          const option2Values = optionDefinitions[option2Name];
          
          option1Values.forEach(value1 => {
            option2Values.forEach(value2 => {
              const variantId = uuidv4();
              const optionCombination = { 
                [option1Name]: value1,
                [option2Name]: value2
              };
              
              // Look for a matching variant row with this combination
              const matchingVariantRow = group.find(row => {
                if (row.fieldType !== 'Choice') return false;
                
                const rowOption1 = row[`productOptionDescription${option1Name.match(/\d+$/) ? option1Name.match(/\d+$/)[0] : '1'}`];
                const rowOption2 = row[`productOptionDescription${option2Name.match(/\d+$/) ? option2Name.match(/\d+$/)[0] : '2'}`];
                
                return rowOption1 === value1 && rowOption2 === value2;
              });
              
              // Base price from the main product
              const basePrice = parsePrice(baseRow.price);
              
              // If we have a matching variant row, use its price as the final price
              const variantPrice = matchingVariantRow ? parsePrice(matchingVariantRow.price) : basePrice;
              
              // Handle sale prices
              const variantSalePrice = matchingVariantRow && matchingVariantRow.discountValue ? 
                (parsePrice(matchingVariantRow.price) - parsePrice(matchingVariantRow.discountValue)) : 
                (baseRow.discountValue ? (basePrice - parsePrice(baseRow.discountValue)) : null);
              
              // Stock quantity
              const variantStock = matchingVariantRow ? 
                (matchingVariantRow.inventory === 'InStock' ? 100 : parseInt(matchingVariantRow.inventory || '0')) :
                (baseRow.inventory === 'InStock' ? 100 : parseInt(baseRow.inventory || '0'));
              
              // SKU
              const variantSku = matchingVariantRow && matchingVariantRow.sku ? 
                matchingVariantRow.sku : 
                (baseRow.sku ? `${baseRow.sku}-${value1.substring(0, 2)}-${value2.substring(0, 2)}`.replace(/\s+/g, '') : null);
              
              const variant = {
                id: variantId,
                product_id: productId,
                variant_name: `${productName} - ${value1} / ${value2}`,
                sku: variantSku,
                price: variantPrice,
                sale_price: variantSalePrice,
                stock_quantity: variantStock,
                in_stock: variantStock > 0,
                image: transformedImageUrl || null,
                option_combination: JSON.stringify(optionCombination),
                is_active: transformedImageUrl ? true : false,
                external_id: matchingVariantRow ? matchingVariantRow.handleId : `${baseRow.handleId}_var_${value1}_${value2}`.replace(/\s+/g, ''),
                created_at: now,
                updated_at: now,
              };
              
              productVariants.push(variant);
            });
          });
        }
        // If three options, create variants for each combination
        else if (optionNames.length === 3) {
          const option1Name = optionNames[0];
          const option2Name = optionNames[1];
          const option3Name = optionNames[2];
          const option1Values = optionDefinitions[option1Name];
          const option2Values = optionDefinitions[option2Name];
          const option3Values = optionDefinitions[option3Name];
          
          option1Values.forEach(value1 => {
            option2Values.forEach(value2 => {
              option3Values.forEach(value3 => {
                const variantId = uuidv4();
                const optionCombination = { 
                  [option1Name]: value1,
                  [option2Name]: value2,
                  [option3Name]: value3
                };
                
                // Look for a matching variant row with this combination
                const matchingVariantRow = group.find(row => {
                  if (row.fieldType !== 'Choice') return false;
                  
                  const rowOption1 = row[`productOptionDescription${option1Name.match(/\d+$/) ? option1Name.match(/\d+$/)[0] : '1'}`];
                  const rowOption2 = row[`productOptionDescription${option2Name.match(/\d+$/) ? option2Name.match(/\d+$/)[0] : '2'}`];
                  const rowOption3 = row[`productOptionDescription${option3Name.match(/\d+$/) ? option3Name.match(/\d+$/)[0] : '3'}`];
                  
                  return rowOption1 === value1 && rowOption2 === value2 && rowOption3 === value3;
                });
                
                // Base price from the main product
                const basePrice = parsePrice(baseRow.price);
                
                // If we have a matching variant row, use its price as the final price
                const variantPrice = matchingVariantRow ? parsePrice(matchingVariantRow.price) : basePrice;
                
                // Handle sale prices
                const variantSalePrice = matchingVariantRow && matchingVariantRow.discountValue ? 
                  (parsePrice(matchingVariantRow.price) - parsePrice(matchingVariantRow.discountValue)) : 
                  (baseRow.discountValue ? (basePrice - parsePrice(baseRow.discountValue)) : null);
                
                // Stock quantity
                const variantStock = matchingVariantRow ? 
                  (matchingVariantRow.inventory === 'InStock' ? 100 : parseInt(matchingVariantRow.inventory || '0')) :
                  (baseRow.inventory === 'InStock' ? 100 : parseInt(baseRow.inventory || '0'));
                
                // SKU
                const variantSku = matchingVariantRow && matchingVariantRow.sku ? 
                  matchingVariantRow.sku : 
                  (baseRow.sku ? `${baseRow.sku}-${value1.substring(0, 1)}-${value2.substring(0, 1)}-${value3.substring(0, 1)}`.replace(/\s+/g, '') : null);
                
                const variant = {
                  id: variantId,
                  product_id: productId,
                  variant_name: `${productName} - ${value1} / ${value2} / ${value3}`,
                  sku: variantSku,
                  price: variantPrice,
                  sale_price: variantSalePrice,
                  stock_quantity: variantStock,
                  in_stock: variantStock > 0,
                  image: transformedImageUrl || null,
                  option_combination: JSON.stringify(optionCombination),
                  is_active: transformedImageUrl ? true : false,
                  external_id: matchingVariantRow ? matchingVariantRow.handleId : `${baseRow.handleId}_var_${value1}_${value2}_${value3}`.replace(/\s+/g, ''),
                  created_at: now,
                  updated_at: now,
                };
                
                productVariants.push(variant);
              });
            });
          });
        }
      }
    });
    
    // Convert to CSV
    const productsOutput = Papa.unparse(transformedProducts);
    const variantsOutput = Papa.unparse(productVariants);
    
    // Write to output files
    const productsOutputPath = path.join(CONFIG.outputDir, CONFIG.productsOutputFileName);
    const variantsOutputPath = path.join(CONFIG.outputDir, CONFIG.variantsOutputFileName);
    
    fs.writeFileSync(productsOutputPath, productsOutput);
    fs.writeFileSync(variantsOutputPath, variantsOutput);
    
    console.log(`Transformation complete!`);
    console.log(`- Products CSV: ${productsOutputPath}`);
    console.log(`- Variants CSV: ${variantsOutputPath}`);
    console.log(`- ${transformedProducts.length} products and ${productVariants.length} variants created`);
    
    // Stats
    const productsWithoutImages = transformedProducts.filter(p => p.is_active === false).length;
    console.log(`- ${productsWithoutImages} products flagged as inactive (no images)`);
  } catch (err) {
    console.error('Error processing file:', err);
    throw err;
  }
}

// Run the transformation
transformCsv().catch(err => {
  console.error('Error during transformation:', err);
  process.exit(1);
});
