/**
 * This file provides direct access to images that work without CORS issues
 * by using a combination of techniques including direct data URIs and Google Image Search
 */

// Import the category-based image selection logic
import { getTopicCategory } from './searchImages';
// Import Google Image Search functionality
import { getRandomImageForQuery, isGoogleImageSearchConfigured } from './googleImageSearch';

// Define a set of pre-vetted image URLs organized by category
const CATEGORY_IMAGES: Record<string, string[]> = {
  'vaping': [
    'https://cdn.pixabay.com/photo/2019/06/25/04/01/smoke-4297364_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/electronic-cigarette-925759_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/smoking-925758_1280.jpg',
  ],
  'cbd oil': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/05/13/22/29/cbd-5169412_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
  ],
  'anxiety': [
    'https://cdn.pixabay.com/photo/2017/08/10/03/47/guy-2617866_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/03/26/21/54/yoga-2176668_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/06/00/27/yoga-2587066_1280.jpg',
  ],
  'sleep': [
    'https://cdn.pixabay.com/photo/2016/11/18/17/20/woman-1835799_1280.jpg',
    'https://cdn.pixabay.com/photo/2016/11/29/06/16/home-1867772_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/08/02/00/49/people-2569234_1280.jpg',
  ],
  'pain': [
    'https://cdn.pixabay.com/photo/2021/01/05/06/40/acupuncture-5889256_1280.jpg',
    'https://cdn.pixabay.com/photo/2014/09/25/20/32/healing-460546_1280.jpg',
    'https://cdn.pixabay.com/photo/2017/04/09/12/45/massage-2215920_1280.jpg',
  ],
  'general': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/39/cbd-4391539_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/04/15/11/47/cbd-4129323_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
  ],
};

// Keep track of recently used images to avoid repetition
let recentlyUsedImages: string[] = [];

/**
 * Generate a data URI SVG image
 * @param text Text to display on the image
 * @returns A data URI string containing an SVG image
 */
export function generateDataUriImage(text: string): string {
  // Generate a color based on the text
  const hashCode = text.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0);
  }, 0);
  
  // Create a color from the hash
  const hue = hashCode % 360;
  const bgColor = `hsl(${hue}, 70%, 60%)`;
  const textColor = 'white';
  
  // Create a simple SVG with the text
  const words = text.split(' ').slice(0, 3).join(' ');
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
      <rect width="800" height="600" fill="${bgColor}" />
      <text x="400" y="300" font-family="Arial" font-size="32" text-anchor="middle" fill="${textColor}">${words}</text>
    </svg>
  `;
  
  // Convert to a data URI
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
}

/**
 * Convert an image URL to a data URI using the Canvas API
 * This helps avoid CORS issues by loading the image locally
 * @param imageUrl The URL of the image to convert
 * @returns Promise resolving to a data URI
 */
export function convertImageToDataUri(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    // Allow cross-origin loading
    img.crossOrigin = 'anonymous';
    
    // Set a timeout to handle stalled image loading
    const timeoutId = setTimeout(() => {
      console.warn(`Image load timeout for ${imageUrl}`);
      reject(new Error(`Timeout loading image from ${imageUrl}`));
    }, 10000); // 10 second timeout
    
    img.onload = () => {
      clearTimeout(timeoutId);
      try {
        // Create a canvas element
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Draw the image on the canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }
        
        ctx.drawImage(img, 0, 0);
        
        // Convert the canvas to a data URI
        const dataUri = canvas.toDataURL('image/jpeg', 0.8);
        resolve(dataUri);
      } catch (error) {
        console.error('Error converting image to data URI:', error);
        reject(error);
      }
    };
    
    img.onerror = (error) => {
      clearTimeout(timeoutId);
      // Use a more discreet log level for expected errors
      console.warn(`Image load error for ${imageUrl}`);
      
      // Check if the error is due to CORS
      if (imageUrl.startsWith('http') && !imageUrl.includes(window.location.hostname)) {
        console.warn('Possible CORS issue with external image');
      }
      
      reject(new Error(`Failed to load image from ${imageUrl}`));
    };
    
    // Set the source of the image
    img.src = imageUrl;
  });
}

/**
 * Get a random image URL for a specific category
 * @param category The category to get an image for
 * @returns A random image URL from the category
 */
export function getRandomImage(category: string): string {
  // Default to general category if the requested one doesn't exist
  const categoryImages = CATEGORY_IMAGES[category] || CATEGORY_IMAGES['general'];
  
  // Filter out recently used images if possible
  const availableImages = categoryImages.filter(img => !recentlyUsedImages.includes(img));
  
  // If all images have been recently used, reset tracking and use all images
  const imagesToUse = availableImages.length > 0 ? availableImages : categoryImages;
  
  // Get a random image from the available ones
  const randomIndex = Math.floor(Math.random() * imagesToUse.length);
  const selectedImage = imagesToUse[randomIndex];
  
  // Update the recently used images list
  recentlyUsedImages.push(selectedImage);
  
  // Keep only the last 5 images in the history to avoid memory issues
  if (recentlyUsedImages.length > 5) {
    recentlyUsedImages = recentlyUsedImages.slice(-5);
  }
  
  return selectedImage;
}

/**
 * Get an image for a blog post based on its topic
 * @param topic The blog topic
 * @returns An image URL based on the topic category
 */
export function getImageForTopic(topic: string): string {
  // Use our category system to find a relevant image
  const category = getTopicCategory(topic);
  return getRandomImage(category);
}

/**
 * Get an image for a blog post based on its topic asynchronously
 * This allows for more advanced image searching when needed
 * @param topic The blog topic
 * @returns A Promise resolving to an image URL or data URI
 */
export async function getImageForTopicAsync(topic: string): Promise<string> {
  try {
    // First try Google Image Search if it's configured
    if (isGoogleImageSearchConfigured()) {
      try {
        console.log('Using Google Image Search for:', topic);
        const googleImage = await getRandomImageForQuery(topic);
        
        if (googleImage) {
          // Use the image proxy to handle CORS issues
          try {
            // Import the image proxy utilities
            const { getSafeImageUrl, mightHaveCorsIssues } = await import('./imageProxy');
            
            // Check if the image might have CORS issues
            if (mightHaveCorsIssues(googleImage)) {
              console.log('Image might have CORS issues, using proxy:', googleImage);
              const safeUrl = await getSafeImageUrl(googleImage);
              return safeUrl;
            }
            
            return googleImage;
          } catch (proxyError) {
            console.warn('Failed to use image proxy, using direct URL:', proxyError);
            return googleImage;
          }
        }
      } catch (googleError) {
        console.warn('Google Image Search failed, falling back to category images:', googleError);
        // Continue to fallback method
      }
    }
    
    // Fallback to category-based images
    const imageUrl = getImageForTopic(topic);
    
    // Try to use the image proxy for category images too
    try {
      // Import the image proxy utilities
      const { getSafeImageUrl } = await import('./imageProxy');
      const safeUrl = await getSafeImageUrl(imageUrl);
      console.log('Successfully processed image through proxy');
      return safeUrl;
    } catch (proxyError) {
      console.warn('Failed to use image proxy for category image, falling back to direct conversion:', proxyError);
      
      // Try direct conversion as a fallback
      try {
        const dataUri = await convertImageToDataUri(imageUrl);
        console.log('Successfully converted category image to data URI');
        return dataUri;
      } catch (conversionError) {
        console.warn('Failed to convert category image to data URI, falling back to direct URL:', conversionError);
        return imageUrl;
      }
    }
  } catch (error) {
    console.error('Error getting image for topic:', error);
    
    // Final fallback to SVG if everything else fails
    return generateDataUriImage(topic);
  }
}

/**
 * Search for images related to a specific topic
 * @param topic The topic to search for
 * @param count Number of images to return
 * @returns Promise resolving to an array of image URLs
 */
export async function searchImagesForTopic(topic: string, count: number = 5): Promise<string[]> {
  // Sanitize the topic to remove special characters that might cause issues
  const sanitizedTopic = topic.replace(/[^a-zA-Z0-9 ]/g, ' ').trim();
  console.log(`Searching for images related to: ${sanitizedTopic}`);
  
  // Import our enhanced image fetcher
  const { processImageUrl, fetchMultipleImages } = await import('@/utils/enhancedImageFetcher');
  
  // Track all found images
  let foundImages: string[] = [];
  
  // Try Google Image Search first if configured
  if (isGoogleImageSearchConfigured()) {
    try {
      console.log(`Attempting Google Image Search for: ${sanitizedTopic}`);
      
      // Try different search variations to get more results
      const searchVariations = [
        sanitizedTopic,
        `${sanitizedTopic} CBD`,
        `${sanitizedTopic} product`,
        `${sanitizedTopic} high quality`,
      ];
      
      // Search for each variation and collect unique results
      for (const query of searchVariations) {
        if (foundImages.length >= count * 2) break; // Don't fetch too many
        
        try {
          const image = await getRandomImageForQuery(query);
          if (image && !foundImages.some(img => img === image)) {
            console.log(`Found image for query: "${query}"`);
            foundImages.push(image);
          }
        } catch (error) {
          console.warn(`Search failed for query: "${query}"`, error);
        }
      }
      
      console.log(`Found ${foundImages.length} unique images from Google`);
      
      // Process all found images in parallel with concurrency control
      if (foundImages.length > 0) {
        try {
          const processedImages = await fetchMultipleImages(foundImages, 3);
          // Filter out any empty results (failed fetches)
          foundImages = processedImages.filter(Boolean);
          console.log(`Successfully processed ${foundImages.length} images`);
        } catch (error) {
          console.warn('Error processing images, using direct URLs:', error);
        }
      }
      
      // If we have enough results, return them
      if (foundImages.length >= count) {
        return foundImages.slice(0, count);
      }
    } catch (error) {
      console.warn(`Google Image Search failed for: ${sanitizedTopic}`, error);
    }
  } else {
    console.log('Google Image Search not configured, using fallback options');
  }
  
  // Fallback to our pre-defined images if we didn't find enough
  if (foundImages.length < count) {
    const category = getTopicCategory(topic);
    console.log(`Using category: ${category} for topic: ${topic}`);
    
    // Get all available category images
    const categoryImages = [...(CATEGORY_IMAGES[category] || []), ...(CATEGORY_IMAGES['general'] || [])];
    
    // Filter out any duplicates and images we already have
    const uniqueCategoryImages = [...new Set(categoryImages)].filter(img => 
      !foundImages.some(existing => existing === img)
    );
    
    // Add as many as we need to reach the requested count
    const needed = count - foundImages.length;
    const additionalImages = uniqueCategoryImages.slice(0, needed);
    
    // Process the additional images
    if (additionalImages.length > 0) {
      try {
        const processedImages = await fetchMultipleImages(additionalImages, 3);
        // Add only successfully processed images
        foundImages = [...foundImages, ...processedImages.filter(Boolean)];
      } catch (error) {
        console.warn('Error processing fallback images, using direct URLs:', error);
        foundImages = [...foundImages, ...additionalImages];
      }
    }
  }
  
  // Make sure we don't return more than requested
  return foundImages.slice(0, count);
}
