import { supabase } from '@/integrations/supabase/client';
import { DiscountCode, DiscountCodeFormData, DiscountValidationResult } from '@/types/discount';

// Fetch all discount codes
export async function getDiscountCodes(): Promise<DiscountCode[]> {
  const { data, error } = await supabase
    .from('discount_codes')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching discount codes:', error);
    throw error;
  }

  return data as DiscountCode[];
}

// Fetch a single discount code by ID
export async function getDiscountCodeById(id: string): Promise<DiscountCode | null> {
  const { data, error } = await supabase
    .from('discount_codes')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching discount code with ID ${id}:`, error);
    return null;
  }

  return data as DiscountCode;
}

// Create a new discount code
export async function createDiscountCode(discountCode: DiscountCodeFormData): Promise<DiscountCode> {
  const { data, error } = await supabase
    .from('discount_codes')
    .insert(discountCode)
    .select()
    .single();

  if (error) {
    console.error('Error creating discount code:', error);
    throw error;
  }

  return data as DiscountCode;
}

// Update an existing discount code
export async function updateDiscountCode(id: string, discountCode: DiscountCodeFormData): Promise<DiscountCode> {
  const { data, error } = await supabase
    .from('discount_codes')
    .update(discountCode)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating discount code with ID ${id}:`, error);
    throw error;
  }

  return data as DiscountCode;
}

// Delete a discount code
export async function deleteDiscountCode(id: string): Promise<void> {
  const { error } = await supabase
    .from('discount_codes')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting discount code with ID ${id}:`, error);
    throw error;
  }
}

// Validate a discount code
export async function validateDiscountCode(code: string, orderTotal: number): Promise<DiscountValidationResult> {
  // Fetch the discount code
  const { data, error } = await supabase
    .from('discount_codes')
    .select('*')
    .eq('code', code.toUpperCase())
    .eq('is_active', true)
    .single();

  if (error || !data) {
    return {
      isValid: false,
      message: 'Invalid discount code'
    };
  }

  const discountCode = data as DiscountCode;

  // Check if the code is expired
  const now = new Date();
  if (discountCode.start_date && new Date(discountCode.start_date) > now) {
    return {
      isValid: false,
      message: 'This discount code is not active yet'
    };
  }

  if (discountCode.end_date && new Date(discountCode.end_date) < now) {
    return {
      isValid: false,
      message: 'This discount code has expired'
    };
  }

  // Check usage limit
  if (discountCode.usage_limit !== null && discountCode.usage_count >= discountCode.usage_limit) {
    return {
      isValid: false,
      message: 'This discount code has reached its usage limit'
    };
  }

  // Check minimum order amount
  if (orderTotal < discountCode.minimum_order_amount) {
    return {
      isValid: false,
      message: `This discount code requires a minimum order of £${discountCode.minimum_order_amount.toFixed(2)}`
    };
  }

  // Calculate the discount amount
  let discountAmount = 0;
  if (discountCode.discount_type === 'percentage') {
    discountAmount = (orderTotal * discountCode.discount_value) / 100;
  } else {
    discountAmount = Math.min(discountCode.discount_value, orderTotal);
  }

  // Increment usage count
  await supabase
    .from('discount_codes')
    .update({ usage_count: discountCode.usage_count + 1 })
    .eq('id', discountCode.id);

  return {
    isValid: true,
    message: 'Discount applied successfully',
    discount: {
      code: discountCode.code,
      discountType: discountCode.discount_type,
      discountValue: discountCode.discount_value,
      discountAmount
    }
  };
}
