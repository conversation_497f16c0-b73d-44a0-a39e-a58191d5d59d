import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DiscountCode, DiscountCodeFormData, DiscountType } from '@/types/discount';
import { createDiscountCode, updateDiscountCode } from '@/services/discountService';

interface DiscountCodeFormProps {
  discountCode: DiscountCode | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function DiscountCodeForm({
  discountCode,
  onSuccess,
  onCancel,
}: DiscountCodeFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<DiscountCodeFormData>({
    code: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 10,
    minimum_order_amount: 0,
    start_date: null,
    end_date: null,
    usage_limit: null,
    is_active: true,
  });

  // Initialize form with discount code data if editing
  useEffect(() => {
    if (discountCode) {
      setFormData({
        id: discountCode.id,
        code: discountCode.code,
        description: discountCode.description || '',
        discount_type: discountCode.discount_type,
        discount_value: discountCode.discount_value,
        minimum_order_amount: discountCode.minimum_order_amount,
        start_date: discountCode.start_date,
        end_date: discountCode.end_date,
        usage_limit: discountCode.usage_limit,
        is_active: discountCode.is_active,
      });
    }
  }, [discountCode]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numberValue = value === '' ? null : Number(value);
    setFormData((prev) => ({ ...prev, [name]: numberValue }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data
      if (!formData.code) {
        throw new Error('Discount code is required');
      }

      if (formData.discount_value <= 0) {
        throw new Error('Discount value must be greater than 0');
      }

      if (formData.discount_type === 'percentage' && formData.discount_value > 100) {
        throw new Error('Percentage discount cannot exceed 100%');
      }

      // Format the code to uppercase
      const updatedFormData = {
        ...formData,
        code: formData.code.toUpperCase(),
      };

      // Create or update the discount code
      if (discountCode) {
        await updateDiscountCode(discountCode.id, updatedFormData);
      } else {
        await createDiscountCode(updatedFormData);
      }

      onSuccess();
    } catch (error: any) {
      console.error('Error saving discount code:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save discount code',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Code */}
        <div className="space-y-2">
          <Label htmlFor="code">Discount Code</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleChange}
            placeholder="e.g., SUMMER20"
            className="uppercase"
            required
          />
          <p className="text-xs text-muted-foreground">
            This is the code customers will enter at checkout
          </p>
        </div>

        {/* Discount Type */}
        <div className="space-y-2">
          <Label htmlFor="discount_type">Discount Type</Label>
          <Select
            value={formData.discount_type}
            onValueChange={(value) => handleSelectChange('discount_type', value as DiscountType)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select discount type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="percentage">Percentage (%)</SelectItem>
              <SelectItem value="fixed_amount">Fixed Amount (£)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Discount Value */}
        <div className="space-y-2">
          <Label htmlFor="discount_value">
            {formData.discount_type === 'percentage' ? 'Percentage Off (%)' : 'Amount Off (£)'}
          </Label>
          <Input
            id="discount_value"
            name="discount_value"
            type="number"
            value={formData.discount_value}
            onChange={handleNumberChange}
            min={0}
            max={formData.discount_type === 'percentage' ? 100 : undefined}
            step={formData.discount_type === 'percentage' ? 1 : 0.01}
            required
          />
        </div>

        {/* Minimum Order Amount */}
        <div className="space-y-2">
          <Label htmlFor="minimum_order_amount">Minimum Order Amount (£)</Label>
          <Input
            id="minimum_order_amount"
            name="minimum_order_amount"
            type="number"
            value={formData.minimum_order_amount}
            onChange={handleNumberChange}
            min={0}
            step={0.01}
            required
          />
          <p className="text-xs text-muted-foreground">
            Set to 0 for no minimum order requirement
          </p>
        </div>

        {/* Start Date */}
        <div className="space-y-2">
          <Label htmlFor="start_date">Start Date (Optional)</Label>
          <Input
            id="start_date"
            name="start_date"
            type="datetime-local"
            value={formData.start_date || ''}
            onChange={handleChange}
          />
        </div>

        {/* End Date */}
        <div className="space-y-2">
          <Label htmlFor="end_date">End Date (Optional)</Label>
          <Input
            id="end_date"
            name="end_date"
            type="datetime-local"
            value={formData.end_date || ''}
            onChange={handleChange}
          />
        </div>

        {/* Usage Limit */}
        <div className="space-y-2">
          <Label htmlFor="usage_limit">Usage Limit (Optional)</Label>
          <Input
            id="usage_limit"
            name="usage_limit"
            type="number"
            value={formData.usage_limit === null ? '' : formData.usage_limit}
            onChange={handleNumberChange}
            min={0}
            step={1}
            placeholder="Unlimited"
          />
          <p className="text-xs text-muted-foreground">
            Leave empty for unlimited usage
          </p>
        </div>

        {/* Active Status */}
        <div className="flex items-center justify-between space-x-2 pt-6">
          <Label htmlFor="is_active">Active</Label>
          <Switch
            id="is_active"
            checked={formData.is_active}
            onCheckedChange={(checked) => handleSwitchChange('is_active', checked)}
          />
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Enter a description for this discount code"
          rows={3}
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : discountCode ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
}
