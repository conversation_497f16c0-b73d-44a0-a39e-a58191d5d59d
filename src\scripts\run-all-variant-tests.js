// This script runs all the variant tests
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Running all variant tests...');

// Function to run a script and wait for it to complete
async function runScript(scriptName) {
  return new Promise((resolve, reject) => {
    console.log(`\n📋 Running ${scriptName}...`);
    
    const scriptPath = path.join(__dirname, scriptName);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      shell: true
    });
    
    child.on('close', (code) => {
      console.log(`\n✅ ${scriptName} completed with code ${code}`);
      resolve(code);
    });
    
    child.on('error', (err) => {
      console.error(`\n❌ ${scriptName} failed:`, err);
      reject(err);
    });
  });
}

// Run all scripts in sequence
async function runAllTests() {
  try {
    // Step 1: Run the variant count migration
    await runScript('run-variant-count-migration.js');
    
    // Step 2: Run the variant test
    await runScript('run-variant-test.js');
    
    console.log('\n🎉 All variant tests completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Go to the Products page in the admin UI');
    console.log('2. Find a product with variants (look for the variant badge)');
    console.log('3. Click Edit to open the product form');
    console.log('4. Test the variant management UI');
    console.log('5. Try adding, editing, and deleting variants');
    console.log('6. Try bulk generating variants');
    
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
  }
}

runAllTests();
