/**
 * MCPErrorHandler.ts
 * 
 * Error handling for MCP Playwright server
 * Provides error classification, retries, and recovery strategies
 */

/**
 * MCP error types
 */
export enum MCPErrorType {
  CONNECTION = 'connection',
  NAVIGATION = 'navigation',
  ELEMENT = 'element',
  TIMEOUT = 'timeout',
  EVALUATION = 'evaluation',
  UNKNOWN = 'unknown'
}

/**
 * MCP error
 */
export class MCPError extends Error {
  type: MCPErrorType;
  originalError: Error | null;
  retryable: boolean;
  context: Record<string, any>;

  /**
   * Constructor
   * @param message - Error message
   * @param type - Error type
   * @param originalError - Original error
   * @param retryable - Whether error is retryable
   * @param context - Error context
   */
  constructor(
    message: string,
    type: MCPErrorType = MCPErrorType.UNKNOWN,
    originalError: Error | null = null,
    retryable: boolean = false,
    context: Record<string, any> = {}
  ) {
    super(message);
    this.name = 'MCPError';
    this.type = type;
    this.originalError = originalError;
    this.retryable = retryable;
    this.context = context;
  }
}

/**
 * MCP error handler configuration
 */
interface MCPErrorHandlerConfig {
  maxRetries?: number;
  retryDelay?: number;
  logErrors?: boolean;
}

/**
 * Handles errors from MCP server
 */
export class MCPErrorHandler {
  private maxRetries: number;
  private retryDelay: number;
  private logErrors: boolean;

  /**
   * Constructor
   * @param config - Error handler configuration
   */
  constructor(config: MCPErrorHandlerConfig = {}) {
    this.maxRetries = config.maxRetries || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.logErrors = config.logErrors !== false;
  }

  /**
   * Classify error
   * @param error - Error to classify
   * @returns Classified MCP error
   */
  classifyError(error: Error): MCPError {
    const message = error.message.toLowerCase();
    
    // Connection errors
    if (
      message.includes('connect') ||
      message.includes('network') ||
      message.includes('socket')
    ) {
      return new MCPError(
        `Connection error: ${error.message}`,
        MCPErrorType.CONNECTION,
        error,
        true
      );
    }
    
    // Navigation errors
    if (
      message.includes('navigate') ||
      message.includes('url') ||
      message.includes('page')
    ) {
      return new MCPError(
        `Navigation error: ${error.message}`,
        MCPErrorType.NAVIGATION,
        error,
        true
      );
    }
    
    // Element errors
    if (
      message.includes('element') ||
      message.includes('selector') ||
      message.includes('click') ||
      message.includes('type')
    ) {
      return new MCPError(
        `Element error: ${error.message}`,
        MCPErrorType.ELEMENT,
        error,
        true
      );
    }
    
    // Timeout errors
    if (
      message.includes('timeout') ||
      message.includes('timed out')
    ) {
      return new MCPError(
        `Timeout error: ${error.message}`,
        MCPErrorType.TIMEOUT,
        error,
        true
      );
    }
    
    // Evaluation errors
    if (
      message.includes('evaluate') ||
      message.includes('script') ||
      message.includes('execution')
    ) {
      return new MCPError(
        `Evaluation error: ${error.message}`,
        MCPErrorType.EVALUATION,
        error,
        false
      );
    }
    
    // Unknown errors
    return new MCPError(
      `Unknown error: ${error.message}`,
      MCPErrorType.UNKNOWN,
      error,
      false
    );
  }

  /**
   * Handle error
   * @param error - Error to handle
   * @param context - Error context
   * @returns Classified MCP error
   */
  handleError(error: Error, context: Record<string, any> = {}): MCPError {
    // Classify error
    const mcpError = this.classifyError(error);
    
    // Add context
    mcpError.context = { ...mcpError.context, ...context };
    
    // Log error
    if (this.logErrors) {
      this.logError(mcpError);
    }
    
    return mcpError;
  }

  /**
   * Execute function with retry
   * @param fn - Function to execute
   * @param context - Error context
   * @returns Function result
   */
  async withRetry<T>(fn: () => Promise<T>, context: Record<string, any> = {}): Promise<T> {
    let lastError: MCPError | null = null;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        // Handle error
        const mcpError = this.handleError(error, {
          ...context,
          attempt,
          maxRetries: this.maxRetries
        });
        
        lastError = mcpError;
        
        // Check if error is retryable and we have retries left
        if (mcpError.retryable && attempt < this.maxRetries) {
          // Wait before retry
          await this.delay(this.retryDelay * attempt);
          continue;
        }
        
        // No more retries or error is not retryable
        throw mcpError;
      }
    }
    
    // This should never happen due to the throw above
    throw lastError || new MCPError('Unknown error during retry');
  }

  /**
   * Log error
   * @param error - Error to log
   */
  private logError(error: MCPError): void {
    console.error(`[MCP Error] ${error.message}`, {
      type: error.type,
      retryable: error.retryable,
      context: error.context,
      stack: error.stack
    });
  }

  /**
   * Delay execution
   * @param ms - Milliseconds to delay
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
