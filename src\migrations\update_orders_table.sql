-- Update orders table with new columns
ALTER TABLE public.orders 
ADD COLUMN IF NOT EXISTS subtotal DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS tax DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS shipping DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS discount DECIMAL,
ADD COLUMN IF NOT EXISTS discount_code TEXT,
ADD COLUMN IF NOT EXISTS shipping_address_id UUID REFERENCES public.addresses(id),
ADD COLUMN IF NOT EXISTS billing_address_id UUID REFERENCES public.addresses(id),
ADD COLUMN IF NOT EXISTS payment_method TEXT NOT NULL DEFAULT 'card',
ADD COLUMN IF NOT EXISTS payment_status TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS tracking_number TEXT,
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Update order_items table with new columns
ALTER TABLE public.order_items
ADD COLUMN IF NOT EXISTS variant_id UUID REFERENCES public.product_variants(id),
ADD COLUMN IF NOT EXISTS total DECIMAL NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS options JSONB;
