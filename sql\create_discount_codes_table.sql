-- Create discount_codes table
CREATE TABLE IF NOT EXISTS discount_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT NOT NULL UNIQUE,
  description TEXT,
  discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
  discount_value DECIMAL(10, 2) NOT NULL,
  minimum_order_amount DECIMAL(10, 2) DEFAULT 0,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  usage_limit INTEGER DEFAULT NULL,
  usage_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_discount_codes_code ON discount_codes(code);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_discount_codes_updated_at()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_discount_codes_updated_at
BEFORE UPDATE ON discount_codes
FOR EACH ROW
EXECUTE FUNCTION update_discount_codes_updated_at();

-- Create table for product-specific discount codes (optional - for future use)
CREATE TABLE IF NOT EXISTS discount_code_products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  discount_code_id UUID NOT NULL REFERENCES discount_codes(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(discount_code_id, product_id)
);

-- Create table for category-specific discount codes (optional - for future use)
CREATE TABLE IF NOT EXISTS discount_code_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  discount_code_id UUID NOT NULL REFERENCES discount_codes(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(discount_code_id, category_id)
);

-- Add some example discount codes (optional)
INSERT INTO discount_codes (code, description, discount_type, discount_value, minimum_order_amount, start_date, end_date, usage_limit, is_active)
VALUES 
('WELCOME10', 'Welcome discount - 10% off your first order', 'percentage', 10, 0, NOW(), NOW() + INTERVAL '30 days', NULL, TRUE),
('SUMMER20', 'Summer sale - 20% off all orders', 'percentage', 20, 50, NOW(), NOW() + INTERVAL '60 days', 100, TRUE),
('FREESHIP', 'Free shipping - £5 off your order', 'fixed_amount', 5, 25, NOW(), NOW() + INTERVAL '90 days', NULL, TRUE);
