
import { format } from 'date-fns';
import { Order } from '@/types/database';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, ShoppingBag } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface OrdersListProps {
  orders: Order[];
  isLoading: boolean;
}

export default function OrdersList({ orders, isLoading }: OrdersListProps) {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!orders || orders.length === 0) {
    return (
      <div className="text-center py-8">
        <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No orders yet</h3>
        <p className="text-muted-foreground mb-4">You haven't placed any orders yet.</p>
        <Button onClick={() => navigate('/shop')}>Start Shopping</Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <div 
          key={order.id}
          className="border rounded-lg p-4 hover:border-primary transition-colors"
        >
          <div className="flex flex-col sm:flex-row justify-between mb-2">
            <div>
              <h3 className="font-medium text-sm">Order #{order.id.substring(0, 8)}</h3>
              <p className="text-sm text-muted-foreground">
                Placed on {formatDate(order.created_at)}
              </p>
            </div>
            <div className="mt-2 sm:mt-0">
              <Badge className={`text-xs ${getStatusColor(order.status)}`}>
                {order.status}
              </Badge>
            </div>
          </div>
          
          <div className="mt-2 flex justify-between items-end">
            <p className="font-medium">£{order.total.toFixed(2)}</p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate(`/account/orders/${order.id}`)}
            >
              View Details
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}
