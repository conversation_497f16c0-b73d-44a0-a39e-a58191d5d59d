// Category banner image mapping
// This file provides default banner images for categories when they don't have their own images

import { 
  ALL_PRODUCTS_BANNER,
  ALL_PRODUCTS_DESCRIPTION 
} from '@/config/bannerConfig';

// Map of category slugs to banner images
export const categoryBannerImages: Record<string, string> = {
  // Main categories
  'flowers': 'https://images.unsplash.com/photo-1567592333705-c8bd775d6e17?q=80&w=1920&auto=format&fit=crop',
  'edibles': 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?q=80&w=1920&auto=format&fit=crop',
  'concentrates': 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?q=80&w=1920&auto=format&fit=crop',
  'accessories': 'https://images.unsplash.com/photo-1587016615333-6e3f2b1f687c?q=80&w=1920&auto=format&fit=crop',
  'merchandise': 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=1920&auto=format&fit=crop',
  
  // Subcategories
  'indica': 'https://images.unsplash.com/photo-1603909223429-69bb7101f94e?q=80&w=1920&auto=format&fit=crop',
  'sativa': 'https://images.unsplash.com/photo-1536689318884-9924a5ac9d66?q=80&w=1920&auto=format&fit=crop',
  'hybrid': 'https://images.unsplash.com/photo-1585071550721-fdb362ae2b8d?q=80&w=1920&auto=format&fit=crop',
  'gummies': 'https://images.unsplash.com/photo-1582354065827-2f8be7c1e7c1?q=80&w=1920&auto=format&fit=crop',
  'chocolates': 'https://images.unsplash.com/photo-1606312619070-d48b4c652a52?q=80&w=1920&auto=format&fit=crop',
  'beverages': 'https://images.unsplash.com/photo-1595981267035-7b04ca84a82d?q=80&w=1920&auto=format&fit=crop',
  'oils': 'https://images.unsplash.com/photo-1559654745-409a4a1bdd02?q=80&w=1920&auto=format&fit=crop',
  'vapes': 'https://images.unsplash.com/photo-1560372610-f4ab6c4b1d2e?q=80&w=1920&auto=format&fit=crop',
  'pipes': 'https://images.unsplash.com/photo-1556212435-71efeb6d4c0c?q=80&w=1920&auto=format&fit=crop',
  'grinders': 'https://images.unsplash.com/photo-1603822415054-e0a0bd0d36c7?q=80&w=1920&auto=format&fit=crop',
  'apparel': 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=1920&auto=format&fit=crop',
  
  // Default banner for "All Products" - imported from config file for easy customization
  'all': ALL_PRODUCTS_BANNER
};

// Map of category slugs to descriptions
export const categoryDescriptions: Record<string, string> = {
  // Main categories
  'flowers': 'Explore our premium selection of high-quality cannabis flowers, carefully cultivated for maximum potency and flavor.',
  'edibles': 'Delicious treats infused with premium cannabis. Perfect for those who prefer not to smoke.',
  'concentrates': 'Highly potent cannabis extracts for experienced users looking for maximum effect.',
  'accessories': 'Everything you need to enhance your cannabis experience, from pipes to grinders and more.',
  'merchandise': 'Show your love for our brand with our exclusive collection of apparel and accessories.',
  
  // Subcategories
  'indica': 'Relaxing and sedative strains perfect for evening use and stress relief.',
  'sativa': 'Energizing and uplifting strains ideal for daytime use and creativity.',
  'hybrid': 'Balanced strains offering the best of both worlds - relaxation and energy.',
  'gummies': 'Delicious gummy treats infused with precise doses of cannabis.',
  'chocolates': 'Premium chocolate infused with high-quality cannabis for a delightful experience.',
  'beverages': 'Refreshing drinks infused with cannabis for a smoke-free alternative.',
  'oils': 'Versatile cannabis oils for precise dosing and multiple consumption methods.',
  'vapes': 'Convenient and discreet vaporizers for on-the-go consumption.',
  'pipes': 'Classic and modern pipes for the traditional cannabis enthusiast.',
  'grinders': 'High-quality grinders to perfectly prepare your cannabis flowers.',
  'apparel': 'Stylish clothing and accessories featuring our brand designs.',
  
  // Default description for "All Products" - imported from config file for easy customization
  'all': ALL_PRODUCTS_DESCRIPTION
};
