// This script runs the add_variant_count_function.sql migration
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    console.log('🚀 Running variant count function migration...');

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'migrations', 'add_variant_count_function.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSql });

    if (error) {
      throw error;
    }

    console.log('✅ Migration completed successfully!');

    // Test the function
    const { data: testData, error: testError } = await supabase.rpc('get_variant_counts_per_product');

    if (testError) {
      console.error('❌ Function created but test failed:', testError);
      return;
    }

    console.log(`✅ Function test successful! Found ${testData.length} products with variants.`);
    console.log('Sample data:', testData.slice(0, 3));

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

runMigration();
