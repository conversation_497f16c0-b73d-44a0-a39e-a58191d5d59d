import React, { useState, useEffect, useRef } from 'react';
import { Loader2, ZoomIn, X, Minimize, Plus, Minus } from 'lucide-react';
import { TransformWrapper, TransformComponent, ReactZoomPanPinchRef } from "react-zoom-pan-pinch";

interface ProductImageGalleryProps {
  mainImage?: string;
  additionalImages?: string[];
  productName?: string;
}

export const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  mainImage = '',
  additionalImages = [],
  productName = 'Product',
}) => {
  const [selectedImage, setSelectedImage] = useState<string | undefined>(mainImage);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showZoom, setShowZoom] = useState(false);
  const transformRef = useRef<ReactZoomPanPinchRef>(null);
  const [backgroundStyle] = useState("bg-gradient-to-b from-gray-50/80 to-white/80");

  // Ensure additionalImages is always an array
  const safeAdditionalImages = Array.isArray(additionalImages) ? additionalImages : [];

  // Combine all images into one array, ensuring each image URL is valid
  const allImages = [mainImage, ...safeAdditionalImages]
    .filter(url => url && typeof url === 'string' && url.trim() !== '') as string[];

  // Remove any duplicate images
  const uniqueImages = [...new Set(allImages)];

  // Debug output to check images
  console.log('Unique images:', uniqueImages);

  useEffect(() => {
    // Reset selected image when mainImage changes
    if (mainImage) {
      setSelectedImage(mainImage);
      
      // Preload the image to check if it loads successfully
      const img = new Image();
      img.src = mainImage;
      
      // Start with loading state
      setImageLoading(true);
      setImageError(false);
      
      // Set up event handlers for the image
      img.onload = () => {
        setImageLoading(false);
        setImageError(false);
      };
      
      img.onerror = () => {
        setImageLoading(false);
        setImageError(true);
      };
    }
  }, [mainImage]);

  // Normalize image URLs to handle common issues
  useEffect(() => {
    if (selectedImage) {
      // If the URL has duplicated path segments like "product-images/product-images/",
      // normalize it to avoid loading issues
      const normalizedUrl = selectedImage.replace(/(\/[^/]+)\/\1\//, '$1/');
      if (normalizedUrl !== selectedImage) {
        console.log('Normalized URL:', normalizedUrl);
        setSelectedImage(normalizedUrl);
      }
    }
  }, [selectedImage]);

  const handleImageLoad = () => {
    // Immediately set loading to false when the image loads
    setImageLoading(false);
    setImageError(false);
  };

  const toggleZoom = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowZoom(!showZoom);
  };

  const closeZoom = () => {
    setShowZoom(false);
  };

  const zoomIn = () => {
    if (transformRef.current) {
      transformRef.current.zoomIn();
    }
  };

  const zoomOut = () => {
    if (transformRef.current) {
      transformRef.current.zoomOut();
    }
  };

  const resetZoom = () => {
    if (transformRef.current) {
      transformRef.current.resetTransform();
    }
  };

  // If no images are provided, use a placeholder
  if (uniqueImages.length === 0) {
    return (
      <div className="bg-gray-100 rounded-xl flex items-center justify-center p-8 h-[400px]">
        <p className="text-gray-400">No product images available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main image container */}
      <div className={`relative rounded-xl overflow-hidden shadow-md ${backgroundStyle}`} style={{ minHeight: '400px' }}>
        {/* Loading indicator */}
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="mt-2 text-sm text-gray-500">Loading image...</span>
            </div>
          </div>
        )}

        {/* Zoom toggle button */}
        <button
          onClick={(e) => toggleZoom(e)}
          className={`absolute top-2 right-2 z-10 p-2 bg-white/80 rounded-full shadow-md transition-all hover:bg-white ${showZoom ? 'ring-2 ring-primary' : ''}`}
          aria-label="Toggle zoom"
          type="button"
        >
          <ZoomIn size={18} className="text-gray-600" />
        </button>

        {/* Zoom view */}
        {showZoom ? (
          <div className="absolute inset-0 bg-white z-10">
            <div className="absolute top-2 right-2 z-20 flex space-x-2">
              <button
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); zoomIn(); }}
                className="p-2 bg-white/80 rounded-full shadow-md transition-all hover:bg-white/100"
                aria-label="Zoom in"
                type="button"
              >
                <Plus size={18} className="text-gray-600" />
              </button>
              <button
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); zoomOut(); }}
                className="p-2 bg-white/80 rounded-full shadow-md transition-all hover:bg-white/100"
                aria-label="Zoom out"
                type="button"
              >
                <Minus size={18} className="text-gray-600" />
              </button>
              <button
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); resetZoom(); }}
                className="p-2 bg-white/80 rounded-full shadow-md transition-all hover:bg-white/100"
                aria-label="Reset zoom"
                type="button"
              >
                <Minimize size={18} className="text-gray-600" />
              </button>
              <button
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); closeZoom(); }}
                className="p-2 bg-white/80 rounded-full shadow-md transition-all hover:bg-white/100"
                aria-label="Close zoom"
                type="button"
              >
                <X size={18} className="text-gray-600" />
              </button>
            </div>

            <TransformWrapper
              initialScale={1.2}
              minScale={0.5}
              maxScale={5}
              centerOnInit
              ref={transformRef}
              wheel={{ step: 0.1 }}
              pinch={{ step: 0.1 }}
              doubleClick={{ step: 0.5 }}
            >
              <TransformComponent
                wrapperStyle={{ width: '100%', height: '100%' }}
                contentStyle={{ width: '100%', height: '100%' }}
              >
                {imageError ? (
                  <div className="flex flex-col items-center justify-center h-full">
                    <p className="text-gray-500 mb-2">Image could not be loaded</p>
                    <img 
                      src="/images/placeholder-image.png" 
                      alt="Placeholder" 
                      className="w-32 h-32 opacity-50"
                      onError={(e) => {
                        // If even the placeholder fails, just show nothing
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                ) : (
                  <img
                    src={selectedImage || uniqueImages[0]}
                    alt={productName}
                    className="w-full h-full object-contain"
                    onLoad={handleImageLoad}
                    onError={() => {
                      setImageLoading(false);
                      setImageError(true);
                    }}
                  />
                )}
              </TransformComponent>
            </TransformWrapper>
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center p-4">
            {imageError ? (
              <div className="flex flex-col items-center justify-center h-full">
                <p className="text-gray-500 mb-2">Image could not be loaded</p>
                <img 
                  src="/images/placeholder-image.png" 
                  alt="Placeholder" 
                  className="w-32 h-32 opacity-50"
                  onError={(e) => {
                    // If even the placeholder fails, just show nothing
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            ) : (
              <img
                src={selectedImage || uniqueImages[0]}
                alt={productName}
                className={`w-auto h-auto max-w-full max-h-full ${imageLoading ? 'opacity-50' : 'opacity-100'}`}
                style={{
                  objectFit: 'contain',
                  width: '100%',
                  height: '100%',
                  transition: 'opacity 0.2s ease-in-out'
                }}
                onLoad={handleImageLoad}
                onError={() => {
                  console.log('Image failed to load:', selectedImage || uniqueImages[0]);
                  setImageLoading(false);
                  setImageError(true);
                }}
              />
            )}
          </div>
        )}
      </div>

      {/* Thumbnail gallery - completely separate from the zoom component */}
      {uniqueImages.length > 1 && (
        <div className="flex flex-wrap gap-2 pb-2 mt-4">
          {uniqueImages.map((image, index) => (
            <button
              key={`thumb-${index}`}
              onClick={() => {
                console.log('Thumbnail clicked:', image);
                
                // Only change if it's a different image
                if (image !== selectedImage) {
                  // Preload the image
                  const img = new Image();
                  img.src = image;
                  
                  // Set loading state
                  setImageLoading(true);
                  setImageError(false);
                  
                  // Set up handlers
                  img.onload = () => {
                    setSelectedImage(image);
                    setImageLoading(false);
                  };
                  
                  img.onerror = () => {
                    setSelectedImage(image);
                    setImageLoading(false);
                    setImageError(true);
                  };
                }
                
                // Always close zoom when switching images
                setShowZoom(false);
              }}
              className={`flex-shrink-0 border-2 rounded-md overflow-hidden ${image === selectedImage ? 'border-primary' : 'border-transparent hover:border-gray-200'}`}
              aria-label={`View ${productName} image ${index + 1}`}
              type="button"
            >
              <div className="w-16 h-16 relative">
                <img
                  src={image}
                  alt={`${productName} thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // If thumbnail fails to load, show placeholder
                    e.currentTarget.src = '/images/placeholder-image.png';
                  }}
                />
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
