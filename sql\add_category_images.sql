-- Add image URLs to categories for banner display
-- This is a sample migration to add placeholder images to categories

-- Update main categories with placeholder images
UPDATE categories
SET 
  image = CASE 
    WHEN slug = 'flowers' THEN 'https://images.unsplash.com/photo-1567592333705-c8bd775d6e17?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'edibles' THEN 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'concentrates' THEN 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'accessories' THEN 'https://images.unsplash.com/photo-1587016615333-6e3f2b1f687c?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'merchandise' THEN 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=1920&auto=format&fit=crop'
    ELSE image
  END,
  description = CASE
    WHEN slug = 'flowers' THEN 'Explore our premium selection of high-quality cannabis flowers, carefully cultivated for maximum potency and flavor.'
    WHEN slug = 'edibles' THEN 'Delicious treats infused with premium cannabis. Perfect for those who prefer not to smoke.'
    WHEN slug = 'concentrates' THEN 'Highly potent cannabis extracts for experienced users looking for maximum effect.'
    WHEN slug = 'accessories' THEN 'Everything you need to enhance your cannabis experience, from pipes to grinders and more.'
    WHEN slug = 'merchandise' THEN 'Show your love for our brand with our exclusive collection of apparel and accessories.'
    ELSE description
  END
WHERE parent_id IS NULL;

-- Update subcategories with placeholder images
UPDATE categories
SET 
  image = CASE 
    WHEN slug = 'indica' THEN 'https://images.unsplash.com/photo-1603909223429-69bb7101f94e?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'sativa' THEN 'https://images.unsplash.com/photo-1536689318884-9924a5ac9d66?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'hybrid' THEN 'https://images.unsplash.com/photo-1585071550721-fdb362ae2b8d?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'gummies' THEN 'https://images.unsplash.com/photo-1582354065827-2f8be7c1e7c1?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'chocolates' THEN 'https://images.unsplash.com/photo-1606312619070-d48b4c652a52?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'beverages' THEN 'https://images.unsplash.com/photo-1595981267035-7b04ca84a82d?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'oils' THEN 'https://images.unsplash.com/photo-1559654745-409a4a1bdd02?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'vapes' THEN 'https://images.unsplash.com/photo-1560372610-f4ab6c4b1d2e?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'pipes' THEN 'https://images.unsplash.com/photo-1556212435-71efeb6d4c0c?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'grinders' THEN 'https://images.unsplash.com/photo-1603822415054-e0a0bd0d36c7?q=80&w=1920&auto=format&fit=crop'
    WHEN slug = 'apparel' THEN 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=1920&auto=format&fit=crop'
    ELSE image
  END,
  description = CASE
    WHEN slug = 'indica' THEN 'Relaxing and sedative strains perfect for evening use and stress relief.'
    WHEN slug = 'sativa' THEN 'Energizing and uplifting strains ideal for daytime use and creativity.'
    WHEN slug = 'hybrid' THEN 'Balanced strains offering the best of both worlds - relaxation and energy.'
    WHEN slug = 'gummies' THEN 'Delicious gummy treats infused with precise doses of cannabis.'
    WHEN slug = 'chocolates' THEN 'Premium chocolate infused with high-quality cannabis for a delightful experience.'
    WHEN slug = 'beverages' THEN 'Refreshing drinks infused with cannabis for a smoke-free alternative.'
    WHEN slug = 'oils' THEN 'Versatile cannabis oils for precise dosing and multiple consumption methods.'
    WHEN slug = 'vapes' THEN 'Convenient and discreet vaporizers for on-the-go consumption.'
    WHEN slug = 'pipes' THEN 'Classic and modern pipes for the traditional cannabis enthusiast.'
    WHEN slug = 'grinders' THEN 'High-quality grinders to perfectly prepare your cannabis flowers.'
    WHEN slug = 'apparel' THEN 'Stylish clothing and accessories featuring our brand designs.'
    ELSE description
  END
WHERE parent_id IS NOT NULL;
