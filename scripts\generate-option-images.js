import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure the options directory exists
const optionsDir = path.join(__dirname, '..', 'public', 'images', 'options');
if (!fs.existsSync(optionsDir)) {
  fs.mkdirSync(optionsDir, { recursive: true });
}

// Generate a simple SVG for each color
const colors = [
  { name: 'red', hex: '#FF5555' },
  { name: 'blue', hex: '#5555FF' },
  { name: 'green', hex: '#55AA55' },
  { name: 'yellow', hex: '#FFFF55' },
  { name: 'purple', hex: '#AA55AA' },
  { name: 'black', hex: '#333333' },
  { name: 'white', hex: '#FFFFFF' },
];

colors.forEach(color => {
  const svg = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
    <rect width="200" height="200" fill="${color.hex}" />
    <text x="100" y="100" font-family="Arial" font-size="24" fill="${color.hex === '#FFFFFF' ? '#333333' : '#FFFFFF'}" text-anchor="middle" dominant-baseline="middle">${color.name}</text>
  </svg>`;
  
  fs.writeFileSync(path.join(optionsDir, `${color.name}.svg`), svg);
  console.log(`Created ${color.name}.svg`);
});

// Generate SVGs for sizes
const sizes = ['small', 'medium', 'large', 'xlarge'];

sizes.forEach(size => {
  let fontSize = 24;
  let rectSize = 100;
  
  switch(size) {
    case 'small':
      fontSize = 16;
      rectSize = 60;
      break;
    case 'medium':
      fontSize = 20;
      rectSize = 100;
      break;
    case 'large':
      fontSize = 24;
      rectSize = 140;
      break;
    case 'xlarge':
      fontSize = 28;
      rectSize = 180;
      break;
  }
  
  const svg = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
    <rect width="200" height="200" fill="#F5F5F5" />
    <rect x="${(200 - rectSize) / 2}" y="${(200 - rectSize) / 2}" width="${rectSize}" height="${rectSize}" fill="#E0E0E0" stroke="#999999" stroke-width="2" />
    <text x="100" y="100" font-family="Arial" font-size="${fontSize}" fill="#333333" text-anchor="middle" dominant-baseline="middle">${size}</text>
  </svg>`;
  
  fs.writeFileSync(path.join(optionsDir, `${size}.svg`), svg);
  console.log(`Created ${size}.svg`);
});

console.log('All option images generated successfully!');
