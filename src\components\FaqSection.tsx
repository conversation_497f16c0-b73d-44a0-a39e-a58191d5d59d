
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface FaqItem {
  question: string;
  answer: string;
}

interface FaqSectionProps {
  faqs: FaqItem[];
  showAllLink?: boolean;
}

const FaqSection = ({ faqs, showAllLink = false }: FaqSectionProps) => {
  return (
    <section className="py-16 bg-white">
      <div className="container-custom">
        <h2 className="section-heading text-center">Frequently Asked Questions</h2>
        <p className="text-clay-700 text-center mb-10 max-w-2xl mx-auto">
          Find answers to common questions about our products and services.
        </p>
        
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left text-clay-900">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  <p className="text-clay-700">{faq.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
          
          {showAllLink && (
            <div className="mt-8 text-center">
              <Button asChild variant="outline">
                <Link to="/faq">View All FAQs</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default FaqSection;
