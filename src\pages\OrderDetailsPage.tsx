
import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useOrderDetails } from '@/hooks/useOrders';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronLeft, Calendar, Package, CreditCard, Truck } from 'lucide-react';
import { format } from 'date-fns';

export default function OrderDetailsPage() {
  const { orderId } = useParams<{ orderId: string }>();
  const { orderDetails, isLoading } = useOrderDetails(orderId || null);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  const getStatusStep = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 0;
      case 'processing':
        return 1;
      case 'shipped':
        return 2;
      case 'delivered':
        return 3;
      case 'cancelled':
        return -1;
      default:
        return 0;
    }
  };

  if (isLoading) {
    return (
      <div className="container py-8 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!orderDetails) {
    return (
      <div className="container py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Order Not Found</h2>
          <p className="text-gray-500 mb-4">The order you're looking for doesn't exist or you don't have permission to view it.</p>
          <Link to="/account">
            <Button>Back to Account</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/account" className="flex items-center text-primary hover:underline">
          <ChevronLeft size={16} className="mr-1" />
          Back to Account
        </Link>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">Order #{orderDetails.id.substring(0, 8)}...</CardTitle>
                  <p className="text-sm text-gray-500 mt-1">
                    <Calendar className="inline-block w-4 h-4 mr-1" /> 
                    Placed on {formatDate(orderDetails.created_at)}
                  </p>
                </div>
                <div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    ${orderDetails.status === 'pending' && 'bg-yellow-100 text-yellow-800'}
                    ${orderDetails.status === 'processing' && 'bg-blue-100 text-blue-800'}
                    ${orderDetails.status === 'shipped' && 'bg-purple-100 text-purple-800'}
                    ${orderDetails.status === 'delivered' && 'bg-green-100 text-green-800'}
                    ${orderDetails.status === 'cancelled' && 'bg-red-100 text-red-800'}
                  `}>
                    {orderDetails.status.charAt(0).toUpperCase() + orderDetails.status.slice(1)}
                  </span>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-6">
                {/* Order Status Tracker */}
                {orderDetails.status !== 'cancelled' && (
                  <div className="mb-8">
                    <h3 className="font-medium mb-4">Order Status</h3>
                    <div className="relative">
                      <div className="absolute left-0 top-1/2 w-full h-1 bg-gray-200 -translate-y-1/2"></div>
                      <div className="flex justify-between relative">
                        {['Pending', 'Processing', 'Shipped', 'Delivered'].map((step, index) => {
                          const isActive = getStatusStep(orderDetails.status) >= index;
                          return (
                            <div key={step} className="flex flex-col items-center relative">
                              <div className={`w-6 h-6 rounded-full z-10 flex items-center justify-center mb-1
                                ${isActive ? 'bg-primary' : 'bg-gray-300'}`}>
                                {isActive && <div className="w-2 h-2 bg-white rounded-full"></div>}
                              </div>
                              <span className={`text-xs ${isActive ? 'text-primary font-medium' : 'text-gray-500'}`}>
                                {step}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
                
                <Separator />
                
                {/* Order Items */}
                <div>
                  <h3 className="font-medium mb-4">Items</h3>
                  <div className="space-y-4">
                    {orderDetails.order_items?.map((item) => (
                      <div key={item.id} className="flex justify-between py-2 border-b">
                        <div className="flex">
                          <Package className="h-6 w-6 text-gray-400 mr-3" />
                          <div>
                            <p>{item.product?.name || 'Product'}</p>
                            <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                          </div>
                        </div>
                        <p className="font-medium">
                          ${item.price.toFixed(2)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>${orderDetails.total.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-500">
                <span>Shipping</span>
                <span>$0.00</span>
              </div>
              <div className="flex justify-between text-gray-500">
                <span>Tax</span>
                <span>Included</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>${orderDetails.total.toFixed(2)}</span>
              </div>
              
              <div className="pt-2">
                <h4 className="text-sm font-medium mb-2">Payment Information</h4>
                <div className="bg-gray-50 p-3 rounded-md flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-gray-400" />
                  <span className="text-sm">Paid with Credit Card</span>
                </div>
              </div>
              
              {orderDetails.shipping_address && (
                <div className="pt-2">
                  <h4 className="text-sm font-medium mb-2">Shipping Address</h4>
                  <div className="bg-gray-50 p-3 rounded-md flex">
                    <Truck className="h-5 w-5 mr-2 text-gray-400 flex-shrink-0" />
                    <div className="text-sm">
                      {orderDetails.shipping_address.street && <p>{orderDetails.shipping_address.street}</p>}
                      {orderDetails.shipping_address.city && (
                        <p>
                          {orderDetails.shipping_address.city}, {orderDetails.shipping_address.state} {orderDetails.shipping_address.postal_code}
                        </p>
                      )}
                      {orderDetails.shipping_address.country && <p>{orderDetails.shipping_address.country}</p>}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
