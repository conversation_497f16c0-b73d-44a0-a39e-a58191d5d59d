# Multi-Tenant Testing Environment Setup Guide

This guide walks you through setting up the isolated testing environment for validating the multi-tenant SaaS architecture.

## Prerequisites

- Supabase account
- Node.js installed
- npm or yarn installed

## Step 1: Create a Supabase Project

1. Log in to your Supabase account at [https://app.supabase.com/](https://app.supabase.com/)
2. Click "New Project"
3. Enter the following details:
   - Name: `bitsnbongs-multitenancy-test`
   - Database Password: *Create a secure password*
   - Region: *Choose the region closest to you*
4. Click "Create new project"
5. Wait for the project to be created (this may take a few minutes)

## Step 2: Get Your Supabase Credentials

1. In your new Supabase project, go to Settings > API
2. Copy the URL under "Project URL"
3. Copy the "service_role" key (Note: We're using this for testing purposes only)
4. Create a `.env` file by copying the `.env.example` file:
   ```
   cp .env.example .env
   ```
5. Update the `.env` file with your Supabase URL and key

## Step 3: Create Base Tables for Testing

Before running the migrations, you need to create the base tables that would exist in your production environment. Run the following SQL in the Supabase SQL Editor:

```sql
-- Create base tables for testing
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES public.customers(id),
    total_amount DECIMAL(10, 2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES public.products(id),
    quantity INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    contact_email TEXT,
    contact_phone TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    discount_percentage DECIMAL(5, 2),
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT NOT NULL,
    value JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.blog_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author TEXT,
    published BOOLEAN NOT NULL DEFAULT FALSE,
    published_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    display_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## Step 4: Install Dependencies

Run the following command to install the required dependencies:

```bash
npm install
```

## Step 5: Run the Migrations

The setup script will run all migrations in the correct order:

```bash
npm run setup
```

This will:
1. Run all migration files
2. Create test data for the three tenants
3. Run isolation tests
4. Run performance tests

## Step 6: Verify the Results

After running the setup script, check the console output to verify that all tests passed. You can also check the Supabase dashboard to see the created tables and data.

## Step 7: Manual Testing

You can also manually test the tenant isolation by running SQL queries in the Supabase SQL Editor:

```sql
-- Set the current tenant context (simulating a request from a specific tenant)
SET LOCAL request.jwt.claims = '{"tenant_id": "TENANT_ID_HERE"}';

-- Try to access data from another tenant
SELECT * FROM public.products WHERE tenant_id != 'TENANT_ID_HERE';
```

Replace `TENANT_ID_HERE` with the actual UUID of one of the test tenants. The query should return no results if tenant isolation is working correctly.

## Step 8: Individual Tests

You can run individual tests using the following commands:

```bash
# Run isolation tests
npm run test:isolation

# Run performance tests
npm run test:performance

# Create test data (if needed)
npm run create:testdata
```

## Troubleshooting

If you encounter any issues:

1. Check that your Supabase credentials are correct in the `.env` file
2. Verify that all base tables were created successfully
3. Check the Supabase logs for any SQL errors
4. Try running the migrations manually in the Supabase SQL Editor

## Next Steps

After successfully validating the multi-tenant architecture, document your findings in the `AGENT_2_VALIDATION_REPORT.md` file.
