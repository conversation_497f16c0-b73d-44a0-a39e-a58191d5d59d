import { useRef, useEffect, useState } from 'react';
import { motion, useMotionValue, useTransform, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChevronRight, ChevronLeft, Pause, Play } from 'lucide-react';

interface ShowcaseItem {
  id: number;
  title: string;
  description: string;
  imageUrl?: string;
  videoUrl?: string;
  bgColor: string;
  link: string;
  isVideo?: boolean;
}

const showcaseItems: ShowcaseItem[] = [
  {
    id: 1,
    title: "Premium CBD Oils",
    description: "Our full-spectrum CBD oils are crafted for maximum effectiveness and purity. Available in multiple strengths and flavors.",
    imageUrl: "/lovable-uploads/c0a66e62-59f0-4e3e-a657-071e01a19eeb.png",
    bgColor: "from-green-800/90 to-emerald-600/90",
    link: "/shop?category=cbd-products"
  },
  {
    id: 2,
    title: "RAW Rolling Papers & Accessories",
    description: "Discover our extensive collection of authentic RAW products, from their legendary rolling papers to pre-rolled cones, tips, trays, and rolling machines.",
    imageUrl: "/images/banners/RawSlide1.jpg",
    bgColor: "from-amber-800/90 to-red-700/90",
    link: "/brands/raw"
  },
  {
    id: 3,
    title: "Bits N Bongs",
    description: "Discover our premium collection of smoking accessories featuring expertly crafted glass bongs, durable acrylic options, and essential accessories to elevate your experience. From intricate artisanal designs to practical everyday pieces, our curated selection offers something for enthusiasts at every level.",
    imageUrl: "/lovable-uploads/80de80d3-04c2-4d61-b8a5-2d9e8e774478.png",
    bgColor: "from-slate-800/80 to-slate-600/80",
    link: "/shop?category=bongs"
  },
  {
    id: 4,
    title: "Seeds For Every Occasion",
    description: "Explore our diverse collection of premium cannabis seeds. Find your perfect strain today!",
    imageUrl: "/images/banners/seed1.jpg", // Fallback image
    videoUrl: "/videos/accessories-showcase.webm",
    bgColor: "from-green-900/90 to-emerald-700/90",
    link: "/shop?category=seeds",
    isVideo: true
  }
];

const ParallaxShowcase = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [autoplay, setAutoplay] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const autoplayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const x = useMotionValue(0);
  const background = useTransform(x, [-100, 0, 100], [0.2, 0, 0.2]);

  const nextSlide = () => {
    // Clear any existing timeout when manually changing slides
    if (autoplayTimeoutRef.current) {
      clearTimeout(autoplayTimeoutRef.current);
    }

    setCurrentIndex((prevIndex) =>
      prevIndex === showcaseItems.length - 1 ? 0 : prevIndex + 1
    );

    // Reset the autoplay timeout
    if (autoplay) {
      startAutoplayTimer();
    }
  };

  const prevSlide = () => {
    // Clear any existing timeout when manually changing slides
    if (autoplayTimeoutRef.current) {
      clearTimeout(autoplayTimeoutRef.current);
    }

    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? showcaseItems.length - 1 : prevIndex - 1
    );

    // Reset the autoplay timeout
    if (autoplay) {
      startAutoplayTimer();
    }
  };

  // Function to start the autoplay timer
  const startAutoplayTimer = () => {
    autoplayTimeoutRef.current = setTimeout(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === showcaseItems.length - 1 ? 0 : prevIndex + 1
      );
      startAutoplayTimer();
    }, 6000); // Change slide every 6 seconds
  };

  // Toggle autoplay on/off
  const toggleAutoplay = () => {
    setAutoplay(!autoplay);
  };

  // Start autoplay when component mounts
  useEffect(() => {
    if (autoplay) {
      startAutoplayTimer();
    }

    // Clean up the timer when component unmounts
    return () => {
      if (autoplayTimeoutRef.current) {
        clearTimeout(autoplayTimeoutRef.current);
      }
    };
  }, [autoplay]);

  // Parallax effect for the container
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const scrollPosition = window.scrollY;
      const rect = containerRef.current.getBoundingClientRect();
      const offsetTop = rect.top + scrollPosition;
      const offsetBottom = offsetTop + rect.height;

      // Check if section is in viewport
      if (scrollPosition + window.innerHeight > offsetTop &&
          scrollPosition < offsetBottom) {
        const scrollPercentage = (scrollPosition + window.innerHeight - offsetTop) /
                               (window.innerHeight + rect.height);
        const parallaxValue = (scrollPercentage - 0.5) * 100;
        x.set(parallaxValue);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [x]);

  const currentItem = showcaseItems[currentIndex];

  return (
    <div
      ref={containerRef}
      className="relative h-[70vh] min-h-[600px] overflow-hidden"
      style={{
        background: `linear-gradient(to right, rgba(0, 0, 0, ${background.get()}), rgba(0, 0, 0, ${background.get()}))`
      }}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={currentItem.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="absolute inset-0"
        >
          {currentItem.isVideo ? (
            <div className="absolute inset-0">
              <video
                className="absolute inset-0 w-full h-full object-cover"
                autoPlay
                loop
                muted
                playsInline
              >
                <source src={currentItem.videoUrl} type="video/webm" />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className={`absolute inset-0 bg-cover bg-center`}
              style={{ backgroundImage: `url(${currentItem.imageUrl})` }}
            />
          )}
          <div className={`absolute inset-0 bg-gradient-to-r ${currentItem.bgColor}`} />
        </motion.div>
      </AnimatePresence>

      <div className="absolute inset-0 flex items-center justify-center">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-white p-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentItem.id}
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 30 }}
                  transition={{ duration: 0.5 }}
                >
                  <h2 className="text-4xl md:text-5xl font-bold mb-4">{currentItem.title}</h2>
                  <p className="text-xl mb-8 text-white/80">{currentItem.description}</p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      size="lg"
                      className="bg-primary text-white hover:bg-primary/90 shadow-lg shadow-primary/30 transition-all duration-300"
                      onClick={() => window.location.href = currentItem.link}
                    >
                      {currentItem.id === 2 ? "Check Out The Range" : "Learn More"}
                    </Button>
                  </motion.div>
                </motion.div>
              </AnimatePresence>
            </div>

            <motion.div
              className="hidden lg:block relative h-80 w-full"
              style={{ perspective: 1000 }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentItem.id}
                  initial={{ opacity: 0, rotateY: 45, z: -100 }}
                  animate={{ opacity: 1, rotateY: 0, z: 0 }}
                  exit={{ opacity: 0, rotateY: -45, z: -100 }}
                  transition={{ duration: 0.7 }}
                  className="absolute inset-0 rounded-xl overflow-hidden shadow-2xl"
                  style={{ transformStyle: 'preserve-3d' }}
                >
                  {currentItem.isVideo ? (
                    <video
                      className="w-full h-full object-cover rounded-xl"
                      autoPlay
                      loop
                      muted
                      playsInline
                    >
                      <source src={currentItem.videoUrl} type="video/webm" />
                      Your browser does not support the video tag.
                    </video>
                  ) : (
                    <img
                      src={currentItem.imageUrl}
                      alt={currentItem.title}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  )}
                  {currentItem.id === 1 && (
                    <motion.div
                      className="absolute top-4 right-4"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      <div className="p-2 bg-white/20 backdrop-blur-sm rounded-full shadow-lg">
                        <motion.div
                          className="w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                        >
                          <span className="text-white font-bold text-xs">CBD</span>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}

                  {currentItem.id === 2 && (
                    <motion.div
                      className="absolute top-4 right-4"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      <div className="px-3 py-1.5 bg-red-700/80 backdrop-blur-sm rounded-lg shadow-lg">
                        <span className="text-white font-bold text-sm">AUTHENTIC</span>
                      </div>
                    </motion.div>
                  )}

                  {currentItem.id === 3 && (
                    <motion.div
                      className="absolute top-4 left-4"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      <div className="px-3 py-1.5 bg-slate-700/80 backdrop-blur-sm rounded-lg shadow-lg">
                        <span className="text-white font-semibold text-sm">ARTISANAL</span>
                      </div>
                    </motion.div>
                  )}

                  {currentItem.id === 4 && (
                    <motion.div
                      className="absolute top-4 left-4"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      <div className="px-3 py-1.5 bg-green-700/80 backdrop-blur-sm rounded-lg shadow-lg">
                        <span className="text-white font-semibold text-sm">PREMIUM SEEDS</span>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </div>

          <div className="absolute bottom-8 left-0 w-full flex justify-center gap-4">
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full bg-clay-700/90 text-white hover:bg-clay-800 shadow-lg shadow-black/30"
                onClick={prevSlide}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </motion.div>

            {/* Play/Pause Button */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full bg-clay-700/90 text-white hover:bg-clay-800 shadow-lg shadow-black/30"
                onClick={toggleAutoplay}
                title={autoplay ? "Pause slideshow" : "Play slideshow"}
              >
                {autoplay ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full bg-clay-700/90 text-white hover:bg-clay-800 shadow-lg shadow-black/30"
                onClick={nextSlide}
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParallaxShowcase;
