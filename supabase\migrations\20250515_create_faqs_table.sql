-- Create FAQs table
CREATE TABLE IF NOT EXISTS faqs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category TEXT,
  order_index INTEGER DEFAULT 0,
  is_published BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add some initial FAQs
INSERT INTO faqs (question, answer, category, order_index, is_published) VALUES
(
  'What is CBD and how does it work?',
  'CBD (Cannabidiol) is a non-psychoactive compound found in cannabis plants. It interacts with your body''s endocannabinoid system, which helps regulate various functions like sleep, pain, and immune response. Unlike THC, CBD doesn''t produce a "high" feeling. Many users report benefits for anxiety, pain relief, and sleep quality, though research is ongoing.',
  'Products',
  1,
  true
),
(
  'Are your products legal?',
  'Yes, all our products are 100% legal and comply with federal regulations. Our CBD products contain less than 0.3% THC, making them legal in most states. However, we recommend checking your local laws as regulations can vary by location. We also verify age at checkout to ensure all customers are of legal age to purchase our products.',
  'Legal',
  2,
  true
),
(
  'How should I clean my glass pieces?',
  'For best results, use isopropyl alcohol (91% or higher) and coarse salt. Fill your piece with alcohol, add salt, then seal openings and shake gently. Let it sit for 30 minutes for tough residue. Rinse thoroughly with warm water several times. For regular maintenance, clean after every few uses to prevent buildup and maintain flavor quality.',
  'Usage',
  3,
  true
),
(
  'What''s your delivery policy?',
  'We offer free shipping on all orders over $50. Standard delivery takes 3-5 business days, while express shipping (additional fee) takes 1-2 business days. We ship to all 50 states. International shipping is available to select countries. All orders are shipped discreetly in plain packaging with no indication of contents for your privacy.',
  'Shipping',
  4,
  true
);

-- Create RLS policies
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;

-- Everyone can read published FAQs
CREATE POLICY "Anyone can read published FAQs" ON faqs
  FOR SELECT USING (is_published = true);

-- Only admins can insert/update/delete FAQs
CREATE POLICY "Admins can manage FAQs" ON faqs
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );
