import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Product } from '@/types/database';
import { useWishlists } from '@/hooks/useWishlists';
import { useAuth } from '@/hooks/auth.basic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  Heart,
  Plus,
  MoreVertical,
  Trash2,
  Edit,
  ShoppingCart,
  Loader2
} from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { toast } from '@/components/ui/use-toast';

const WishlistPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    wishlists,
    activeWishlist,
    setActiveWishlist,
    isLoading,
    createWishlist,
    updateWishlist,
    deleteWishlist,
    removeFromWishlist,
    refreshWishlists,
    wishlistItems
  } = useWishlists();

  // Force refresh wishlists when component mounts
  useEffect(() => {
    refreshWishlists();
  }, []);
  const { addToCart } = useCart();

  const [newWishlistName, setNewWishlistName] = useState('');
  const [newWishlistDescription, setNewWishlistDescription] = useState('');
  const [isCreatingWishlist, setIsCreatingWishlist] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingWishlist, setEditingWishlist] = useState<{id: string, name: string, description?: string} | null>(null);

  // Redirect to auth page if not logged in
  if (!user) {
    navigate('/auth');
    return null;
  }

  const handleCreateWishlist = async () => {
    if (!newWishlistName.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please enter a name for your wishlist',
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingWishlist(true);
    try {
      await createWishlist(newWishlistName.trim(), newWishlistDescription.trim() || undefined);
      setNewWishlistName('');
      setNewWishlistDescription('');
      setIsDialogOpen(false);
    } finally {
      setIsCreatingWishlist(false);
    }
  };

  const handleUpdateWishlist = async () => {
    if (!editingWishlist || !editingWishlist.name.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please enter a name for your wishlist',
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingWishlist(true);
    try {
      await updateWishlist(editingWishlist.id, {
        name: editingWishlist.name.trim(),
        description: editingWishlist.description?.trim() || null
      });
      setEditingWishlist(null);
    } finally {
      setIsCreatingWishlist(false);
    }
  };

  const handleDeleteWishlist = async (id: string) => {
    if (confirm('Are you sure you want to delete this wishlist?')) {
      await deleteWishlist(id);
    }
  };

  // Function to move an item from wishlist to cart
  const handleMoveToCart = async (productId: string, wishlistId: string) => {
    try {
      console.log('Moving item to cart:', { productId, wishlistId });

      // Find the product in the wishlist items
      const wishlistItem = wishlistItems[wishlistId]?.find(item => item.product_id === productId);

      if (!wishlistItem || !wishlistItem.products) {
        console.error('Product not found in wishlist items');
        toast({
          title: 'Error',
          description: 'Product not found in wishlist',
          variant: 'destructive',
        });
        return;
      }

      // Add to cart first using the product object
      await addToCart(wishlistItem.products, 1);

      // Then remove from wishlist using the product ID
      await removeFromWishlist(productId, wishlistId);

      toast({
        title: 'Added to Cart',
        description: 'Item moved from wishlist to cart',
      });

      // Refresh wishlists to update the UI
      await refreshWishlists();
    } catch (error) {
      console.error('Error moving item to cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to move item to cart',
        variant: 'destructive',
      });
    }
  };

  // Function to get image URL for a product - use full URLs directly
  const getProductImageUrl = (products: any) => {
    if (!products?.image) {
      return '/placeholder-product.jpg';
    }

    // Since the database stores full URLs, use them directly
    // The URLs are already in format: https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/product-images/filename.webp
    return products.image;
  };

  if (isLoading) {
    return (
      <div className="container-custom py-12 flex justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container-custom py-12">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">My Wishlists</h1>

        {/* Only show Create Wishlist button for admin users or if no wishlists exist */}
        {(user?.app_metadata?.admin || wishlists.length === 0) && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {wishlists.length === 0 ? 'Create Your First Wishlist' : 'Create Wishlist'}
              </Button>
            </DialogTrigger>
          </Dialog>
        )}

        {/* Dialog for creating/editing wishlists */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Wishlist</DialogTitle>
              <DialogDescription>
                Create a new wishlist to save products for later.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="My Wishlist"
                  value={newWishlistName}
                  onChange={(e) => setNewWishlistName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Add a description for your wishlist"
                  value={newWishlistDescription}
                  onChange={(e) => setNewWishlistDescription(e.target.value)}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateWishlist}
                disabled={isCreatingWishlist || !newWishlistName.trim()}
              >
                {isCreatingWishlist && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Wishlist
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Wishlist Dialog */}
        <Dialog open={!!editingWishlist} onOpenChange={(open) => !open && setEditingWishlist(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Wishlist</DialogTitle>
              <DialogDescription>
                Update your wishlist details.
              </DialogDescription>
            </DialogHeader>

            {editingWishlist && (
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Name</Label>
                  <Input
                    id="edit-name"
                    placeholder="My Wishlist"
                    value={editingWishlist.name}
                    onChange={(e) => setEditingWishlist({...editingWishlist, name: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description (Optional)</Label>
                  <Textarea
                    id="edit-description"
                    placeholder="Add a description for your wishlist"
                    value={editingWishlist.description || ''}
                    onChange={(e) => setEditingWishlist({...editingWishlist, description: e.target.value})}
                  />
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingWishlist(null)}>
                Cancel
              </Button>
              <Button
                onClick={handleUpdateWishlist}
                disabled={isCreatingWishlist || !editingWishlist?.name.trim()}
              >
                {isCreatingWishlist && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {wishlists.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <h2 className="text-2xl font-bold mb-2">No Wishlists Yet</h2>
          <p className="text-gray-500 mb-6">Create your first wishlist to start saving products for later.</p>
          <Button onClick={() => setIsDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Wishlist
          </Button>
        </div>
      ) : (
        <Tabs
          defaultValue={activeWishlist?.id || wishlists[0]?.id}
          onValueChange={setActiveWishlist}
          className="w-full"
        >
          <TabsList className="mb-6 overflow-x-auto flex w-full">
            {wishlists.map((wishlist) => (
              <TabsTrigger
                key={wishlist.id}
                value={wishlist.id}
                className="flex-shrink-0"
              >
                {wishlist.name}
                {wishlist.is_default && (
                  <span className="ml-2 text-xs bg-secondary text-secondary-foreground px-1 py-0.5 rounded">
                    Default
                  </span>
                )}
              </TabsTrigger>
            ))}
          </TabsList>

          {wishlists.map((wishlist) => (
            <TabsContent key={wishlist.id} value={wishlist.id} className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold">{wishlist.name}</h2>
                  {wishlist.description && (
                    <p className="text-gray-500 mt-1">{wishlist.description}</p>
                  )}
                </div>

                {!wishlist.is_default && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setEditingWishlist({
                        id: wishlist.id,
                        name: wishlist.name,
                        description: wishlist.description
                      })}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteWishlist(wishlist.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>

              {/* Wishlist items section */}

              {/* Get wishlist items from the wishlistItems state */}
              {wishlistItems && wishlistItems[wishlist.id] && wishlistItems[wishlist.id].length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {wishlistItems[wishlist.id].map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <div className="relative">
                        <AspectRatio ratio={1/1}>
                          {/* Use the same image loading logic as in ProductCard */}
                          <div className="relative h-full w-full">
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                              <Loader2 className="h-8 w-8 animate-spin text-sage-500" />
                            </div>
                            <img
                              src={getProductImageUrl(item.products)}
                              alt={item.products?.name || `Product ${item.product_id}`}
                              className="h-full w-full object-cover object-center"
                              onClick={() => navigate(`/shop/${item.products?.slug || item.product_id}`)}
                              style={{ opacity: 0, transition: 'opacity 0.3s ease' }}
                              onLoad={(e) => {
                                // Hide loader when image loads
                                const target = e.target as HTMLImageElement;
                                target.style.opacity = '1';
                                target.previousElementSibling?.classList.add('hidden');
                              }}
                              onError={(e) => {
                                // Show placeholder on error
                                const target = e.target as HTMLImageElement;
                                target.src = '/placeholder-product.jpg';
                                target.previousElementSibling?.classList.add('hidden');
                              }}
                            />
                          </div>
                        </AspectRatio>
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2"
                          onClick={() => removeFromWishlist(item.product_id, wishlist.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <CardHeader className="p-4 pb-0">
                        <CardTitle className="text-base font-medium truncate">
                          {item.products?.name || `Product ID: ${item.product_id}`}
                        </CardTitle>
                      </CardHeader>

                      <CardContent className="p-4 pt-2">
                        <div className="flex items-center mb-4">
                          {item.products?.sale_price ? (
                            <>
                              <span className="font-bold text-red-600 mr-2">
                                £{Number(item.products.sale_price).toFixed(2)}
                              </span>
                              <span className="text-gray-500 text-sm line-through">
                                £{Number(item.products?.price).toFixed(2)}
                              </span>
                            </>
                          ) : item.products?.price ? (
                            <span className="font-bold">
                              £{Number(item.products.price).toFixed(2)}
                            </span>
                          ) : (
                            <span className="font-bold">£0.00</span>
                          )}
                        </div>

                        <Button
                          className="w-full bg-sage-500 hover:bg-sage-600 text-white"
                          onClick={() => handleMoveToCart(item.product_id, wishlist.id)}
                        >
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          Add to Cart
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <Heart className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-xl font-medium mb-2">This wishlist is empty</h3>
                  <p className="text-gray-500 mb-6">Browse our shop and add items to your wishlist</p>
                  <Button onClick={() => navigate('/shop')}>
                    Shop Now
                  </Button>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
};

export default WishlistPage;

