<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cannabis Seeds - Filter System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Cannabis Seeds</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Shop</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
            </nav>
            <div class="cart">
                <a href="#"><i class="fas fa-shopping-cart"></i> <span id="cart-count">0</span></a>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="page-title">
            <h2>Cannabis Seeds</h2>
        </div>

        <div class="shop-container">
            <aside class="filter-sidebar">
                <h3>Filter</h3>
                
                <!-- Flowertime Filter -->
                <div class="filter-section">
                    <div class="filter-title">
                        <h4>Flowertime</h4>
                        <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>
                    </div>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" data-filter="flowertime" data-value="7-weeks">
                            <span>7 weeks</span>
                            <span class="count">(15)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="flowertime" data-value="8-weeks">
                            <span>8 weeks</span>
                            <span class="count">(43)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="flowertime" data-value="9-weeks">
                            <span>9 weeks</span>
                            <span class="count">(47)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="flowertime" data-value="10-weeks">
                            <span>10 weeks</span>
                            <span class="count">(15)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="flowertime" data-value="11-weeks">
                            <span>11 weeks</span>
                            <span class="count">(5)</span>
                        </label>
                        <div class="more-options hidden">
                            <label class="filter-option">
                                <input type="checkbox" data-filter="flowertime" data-value="12-weeks">
                                <span>12 weeks</span>
                                <span class="count">(4)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" data-filter="flowertime" data-value="13-weeks">
                                <span>13 weeks</span>
                                <span class="count">(2)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" data-filter="flowertime" data-value="14-weeks">
                                <span>14 weeks</span>
                                <span class="count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" data-filter="flowertime" data-value="9-11-weeks">
                                <span>9 - 11 weeks</span>
                                <span class="count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" data-filter="flowertime" data-value="9-12-weeks">
                                <span>9 - 12 weeks</span>
                                <span class="count">(1)</span>
                            </label>
                        </div>
                        <div class="toggle-more">
                            <span class="show-more">see more</span>
                            <span class="show-less hidden">see less</span>
                        </div>
                    </div>
                </div>

                <!-- Yield Filter -->
                <div class="filter-section">
                    <div class="filter-title">
                        <h4>Yield</h4>
                        <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>
                    </div>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" data-filter="yield" data-value="xxl">
                            <span>XXL</span>
                            <span class="count">(25)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="yield" data-value="xl">
                            <span>XL</span>
                            <span class="count">(68)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="yield" data-value="l">
                            <span>L</span>
                            <span class="count">(36)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="yield" data-value="m">
                            <span>M</span>
                            <span class="count">(2)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="yield" data-value="m-l">
                            <span>M/L</span>
                            <span class="count">(1)</span>
                        </label>
                    </div>
                </div>

                <!-- THC Filter -->
                <div class="filter-section">
                    <div class="filter-title">
                        <h4>THC</h4>
                        <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>
                    </div>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" data-filter="thc" data-value="extremely-high">
                            <span>Extremely high</span>
                            <span class="count">(29)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="thc" data-value="very-high">
                            <span>Very High</span>
                            <span class="count">(68)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="thc" data-value="high">
                            <span>High</span>
                            <span class="count">(20)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="thc" data-value="medium">
                            <span>Medium</span>
                            <span class="count">(8)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="thc" data-value="low">
                            <span>Low</span>
                            <span class="count">(7)</span>
                        </label>
                    </div>
                </div>

                <!-- Seed Type Filter -->
                <div class="filter-section">
                    <div class="filter-title">
                        <h4>Seed Type</h4>
                        <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>
                    </div>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" data-filter="seed-type" data-value="autoflower">
                            <span>Autoflower</span>
                            <span class="count">(53)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="seed-type" data-value="feminized">
                            <span>Feminized</span>
                            <span class="count">(68)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="seed-type" data-value="regular">
                            <span>Regular</span>
                            <span class="count">(28)</span>
                        </label>
                    </div>
                </div>

                <!-- Effect Filter -->
                <div class="filter-section">
                    <div class="filter-title">
                        <h4>Effect</h4>
                        <span class="toggle-icon"><i class="fas fa-chevron-down"></i></span>
                    </div>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" data-filter="effect" data-value="hybrid">
                            <span>Hybrid</span>
                            <span class="count">(59)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="effect" data-value="sativa">
                            <span>Sativa</span>
                            <span class="count">(37)</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" data-filter="effect" data-value="indica">
                            <span>Indica</span>
                            <span class="count">(33)</span>
                        </label>
                    </div>
                </div>

                <!-- Additional filters can be added here -->
            </aside>

            <div class="product-container">
                <div class="product-header">
                    <div class="product-count">
                        <span id="product-total">54</span> Products
                    </div>
                    <div class="product-sort">
                        <label for="sort-select">Sort by</label>
                        <select id="sort-select">
                            <option value="relevance">Relevance</option>
                            <option value="name-asc">Name, A to Z</option>
                            <option value="name-desc">Name, Z to A</option>
                            <option value="price-asc">Price, low to high</option>
                            <option value="price-desc">Price, high to low</option>
                        </select>
                    </div>
                </div>

                <div class="product-grid" id="product-grid">
                    <!-- Products will be loaded dynamically via JavaScript -->
                </div>

                <div class="pagination">
                    <a href="#" class="active">1</a>
                    <a href="#">2</a>
                    <a href="#">3</a>
                    <span>...</span>
                    <a href="#">8</a>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 Cannabis Seeds. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/products.js"></script>
    <script src="js/filter.js"></script>
</body>
</html>
