// Script to update category images and descriptions
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Read the SQL file
const sqlFilePath = path.join(__dirname, '..', 'sql', 'add_category_images.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// Split the SQL into separate statements
const statements = sqlContent
  .split(';')
  .map(statement => statement.trim())
  .filter(statement => statement.length > 0);

async function executeSQL() {
  console.log('Updating category images and descriptions...');
  
  try {
    // Execute each SQL statement
    for (const statement of statements) {
      const { data, error } = await supabase.rpc('exec_sql', {
        query: statement + ';'
      });
      
      if (error) {
        console.error('Error executing SQL:', error);
      } else {
        console.log('SQL executed successfully:', statement.substring(0, 50) + '...');
      }
    }
    
    console.log('Category updates completed!');
  } catch (error) {
    console.error('Error:', error);
  }
}

executeSQL();
