import { createContext, useContext, useState, ReactNode } from 'react';
import { toast } from '@/components/ui/use-toast';
import { Wishlist } from '@/types/database';

// This is a simplified version of the useWishlists hook for the client demo
// It disables all database operations and returns mock data

interface WishlistsContextType {
  wishlists: Wishlist[];
  activeWishlist: Wishlist | null;
  isLoading: boolean;
  createWishlist: (name: string, description?: string) => Promise<void>;
  updateWishlist: (id: string, data: Partial<Wishlist>) => Promise<void>;
  deleteWishlist: (id: string) => Promise<void>;
  setActiveWishlist: (wishlistId: string) => void;
  addToWishlist: (productId: string, wishlistId?: string) => Promise<void>;
  removeFromWishlist: (wishlistItemId: string) => Promise<void>;
  isInWishlist: (productId: string, wishlistId?: string) => boolean;
  getDefaultWishlist: () => Wishlist | null;
}

const WishlistsContext = createContext<WishlistsContextType | undefined>(undefined);

export const WishlistsProvider = ({ children }: { children: ReactNode }) => {
  // Mock data for the demo
  const [wishlists] = useState<Wishlist[]>([]);
  const [activeWishlist] = useState<Wishlist | null>(null);
  const [isLoading] = useState(false);

  // Mock functions that do nothing but show a toast message
  const createWishlist = async (name: string) => {
    toast({
      title: 'Demo Mode',
      description: 'Wishlist functionality is disabled in demo mode',
    });
  };

  const updateWishlist = async (id: string, data: Partial<Wishlist>) => {
    toast({
      title: 'Demo Mode',
      description: 'Wishlist functionality is disabled in demo mode',
    });
  };

  const deleteWishlist = async (id: string) => {
    toast({
      title: 'Demo Mode',
      description: 'Wishlist functionality is disabled in demo mode',
    });
  };

  const setActiveWishlistById = (wishlistId: string) => {
    // Do nothing
  };

  const addToWishlist = async (productId: string, wishlistId?: string) => {
    toast({
      title: 'Demo Mode',
      description: 'Wishlist functionality is disabled in demo mode',
    });
  };

  const removeFromWishlist = async (wishlistItemId: string) => {
    toast({
      title: 'Demo Mode',
      description: 'Wishlist functionality is disabled in demo mode',
    });
  };

  const isInWishlist = (productId: string, wishlistId?: string) => {
    return false;
  };

  const getDefaultWishlist = () => {
    return null;
  };

  return (
    <WishlistsContext.Provider 
      value={{ 
        wishlists, 
        activeWishlist,
        isLoading, 
        createWishlist,
        updateWishlist,
        deleteWishlist,
        setActiveWishlist: setActiveWishlistById,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        getDefaultWishlist
      }}
    >
      {children}
    </WishlistsContext.Provider>
  );
};

export const useWishlists = () => {
  const context = useContext(WishlistsContext);
  if (context === undefined) {
    throw new Error('useWishlists must be used within a WishlistsProvider');
  }
  return context;
};
