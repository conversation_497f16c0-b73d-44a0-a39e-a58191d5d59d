// This script fixes dropdown selection issues by directly manipulating the DOM
// It's a temporary solution until we can properly fix the UI components

export function fixDropdowns() {
  // Wait for the DOM to be fully loaded
  setTimeout(() => {
    console.log('Running dropdown fix script');
    
    // Find all select elements on the page
    const selects = document.querySelectorAll('select');
    
    // For each select element, ensure it's properly initialized
    selects.forEach(select => {
      // Add a click handler to ensure the dropdown opens
      select.addEventListener('click', (e) => {
        console.log('Select clicked:', select.id);
        // Force the select to be focusable
        select.setAttribute('tabindex', '0');
        select.focus();
      });
      
      // Add a change handler to ensure the value is properly set
      select.addEventListener('change', (e) => {
        console.log('Select changed:', select.id, 'to value:', select.value);
        
        // Create and dispatch a custom event to notify React
        const event = new CustomEvent('selectchange', { 
          detail: { 
            id: select.id, 
            value: select.value 
          },
          bubbles: true
        });
        select.dispatchEvent(event);
        
        // If this is the category dropdown, reset the subcategory dropdown
        if (select.id === 'category_id') {
          const subcategorySelect = document.getElementById('subcategory_id') as HTMLSelectElement;
          if (subcategorySelect) {
            // Reset the subcategory dropdown
            subcategorySelect.value = '';
            
            // Trigger a change event on the subcategory dropdown
            const event = new Event('change', { bubbles: true });
            subcategorySelect.dispatchEvent(event);
          }
        }
      });
      
      console.log('Enhanced select element:', select.id);
    });
    
    console.log('Dropdown fix script completed');
  }, 1000); // Wait 1 second for the DOM to be fully loaded
}
