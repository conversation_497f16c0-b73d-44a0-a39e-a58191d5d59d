
import { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Leaf, Shield, Award, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ParallaxFeatures = () => {
  const featuresRef = useRef<HTMLDivElement>(null);
  const floatingElements = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!featuresRef.current || !floatingElements.current) return;
      
      const scrollPosition = window.scrollY;
      const viewportHeight = window.innerHeight;
      const rect = featuresRef.current.getBoundingClientRect();
      const sectionTop = rect.top + scrollPosition;
      
      // Parallax effect for the features section
      if (scrollPosition + viewportHeight > sectionTop) {
        const relativeScroll = (scrollPosition + viewportHeight - sectionTop) * 0.1;
        featuresRef.current.style.transform = `translateY(${relativeScroll}px)`;
      }
      
      // Animated floating elements
      const elements = Array.from(floatingElements.current.children);
      elements.forEach((el, index) => {
        const speed = (index % 3 + 1) * 0.02;
        const direction = index % 2 === 0 ? 1 : -1;
        const x = Math.sin(scrollPosition * speed) * 15 * direction;
        const y = Math.cos(scrollPosition * speed) * 15;
        (el as HTMLElement).style.transform = `translate(${x}px, ${y}px)`;
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  const features: Feature[] = [
    {
      icon: <Leaf className="h-6 w-6 text-sage-500" />,
      title: "100% Organic",
      description: "All our CBD products are made from the finest organic hemp, grown without pesticides or harmful chemicals."
    },
    {
      icon: <Shield className="h-6 w-6 text-sage-500" />,
      title: "Lab Tested",
      description: "Every product is rigorously lab tested to ensure purity, potency and quality you can trust."
    },
    {
      icon: <Award className="h-6 w-6 text-sage-500" />,
      title: "Premium Quality",
      description: "We pride ourselves on offering only the highest quality products and accessories for our customers."
    },
    {
      icon: <Heart className="h-6 w-6 text-sage-500" />,
      title: "Customer First",
      description: "Our knowledgeable staff is always ready to help you find the perfect product for your needs."
    }
  ];
  
  return (
    <section className="py-24 bg-gradient-to-b from-white to-sage-50 relative overflow-hidden">
      {/* Floating decorative elements */}
      <div ref={floatingElements} className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-[10%] h-24 w-24 rounded-full bg-sage-300/20 blur-xl"></div>
        <div className="absolute bottom-32 left-[20%] h-32 w-32 rounded-full bg-sage-400/20 blur-xl"></div>
        <div className="absolute top-40 right-[15%] h-40 w-40 rounded-full bg-clay-300/20 blur-xl"></div>
        <div className="absolute bottom-20 right-[25%] h-24 w-24 rounded-full bg-clay-400/20 blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-72 w-72 rounded-full bg-sage-200/30 blur-3xl"></div>
      </div>
      
      <div className="container-custom relative z-10">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-16"
        >
          <h2 className="section-heading">Why Choose Our Products</h2>
          <p className="text-clay-700 max-w-2xl mx-auto text-lg">
            We're committed to providing premium CBD products and accessories 
            that enhance your wellness journey.
          </p>
        </motion.div>
        
        <div ref={featuresRef} className="relative z-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                whileHover={{ y: -8, boxShadow: "0 20px 25px -5px rgba(108, 132, 80, 0.1)" }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-sage-200 hover:border-sage-400 transition-all duration-300">
                  <CardContent className="pt-6">
                    <div className="mb-4 inline-flex items-center justify-center rounded-full bg-sage-100 p-3">
                      {feature.icon}
                    </div>
                    <h3 className="font-semibold text-xl mb-2 text-clay-800">{feature.title}</h3>
                    <p className="text-clay-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          
          <motion.div 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.7 }}
            className="flex justify-center mt-12"
          >
            <Button asChild size="lg" className="group">
              <Link to="/shop">
                Browse Our Products
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ParallaxFeatures;
