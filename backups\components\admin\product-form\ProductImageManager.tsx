import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Trash2, ArrowUp, ArrowDown, Wand2, Loader2, Image } from 'lucide-react';
import { ImageUploader } from './ImageUploader';
import { cn } from '@/lib/utils';

interface ProductImageManagerProps {
  images: string[];
  onChange: (images: string[]) => void;
  mainImage?: string;
  onMainImageChange?: (url: string) => void;
  onFindImagesWithAI?: () => void;
  isFindingImages?: boolean;
}

export function ProductImageManager({ 
  images, 
  onChange, 
  mainImage,
  onMainImageChange,
  onFindImagesWithAI,
  isFindingImages = false
}: ProductImageManagerProps) {
  // Filter out the main image from additional images to prevent duplication
  React.useEffect(() => {
    if (mainImage && images.includes(mainImage)) {
      // Remove the main image from the additional images array
      const filteredImages = images.filter(img => img !== mainImage);
      if (filteredImages.length !== images.length) {
        console.log('Removing main image from additional images to prevent duplication');
        onChange(filteredImages);
      }
    }
  }, [mainImage, images, onChange]);
  
  const addImage = (url: string) => {
    // If there's no main image set, make this the main image
    if (!mainImage && onMainImageChange) {
      onMainImageChange(url);
      return; // Don't add to additional images if it's the main image
    }
    
    // Otherwise, add to additional images if it's not already the main image
    if (url !== mainImage) {
      const newImages = [...images, url];
      onChange(newImages);
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    const removedUrl = newImages[index];
    newImages.splice(index, 1);
    onChange(newImages);
    
    // If we're removing an image that happens to be the main image too,
    // set the first available additional image as main
    if (mainImage === removedUrl && onMainImageChange) {
      if (newImages.length > 0) {
        onMainImageChange(newImages[0]);
      } else {
        onMainImageChange('');
      }
    }
  };

  const handleMoveUp = (index: number) => {
    if (index > 0) {
      const newImages = [...images];
      const movedImage = newImages[index];
      const swappedImage = newImages[index - 1];
      
      // Swap the images
      newImages[index - 1] = movedImage;
      newImages[index] = swappedImage;
      
      // Update the images array
      onChange(newImages);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index < images.length - 1) {
      const newImages = [...images];
      const movedImage = newImages[index];
      const swappedImage = newImages[index + 1];
      
      // Swap the images
      newImages[index] = swappedImage;
      newImages[index + 1] = movedImage;
      
      // Update the images array
      onChange(newImages);
    }
  };

  // This is a completely new implementation of handleSetAsMain
  const handleSetAsMain = (url: string) => {
    console.log('Setting image as main:', url);
    
    if (!onMainImageChange) return;
    
    // Store current state
    const oldMainImage = mainImage;
    const currentImages = [...images];
    console.log('Current state:', { oldMainImage, currentImages });
    
    // Prepare new state
    let newAdditionalImages = [...currentImages];
    
    // 1. Remove the new main image from additional images
    newAdditionalImages = newAdditionalImages.filter(img => img !== url);
    
    // 2. Add the old main image to additional images (if it exists and isn't already there)
    if (oldMainImage && oldMainImage.trim() !== '' && oldMainImage !== url) {
      if (!newAdditionalImages.includes(oldMainImage)) {
        newAdditionalImages.push(oldMainImage);
      }
    }
    
    console.log('New state will be:', { newMainImage: url, newAdditionalImages });
    
    // 3. Update both states in quick succession
    onChange(newAdditionalImages);
    onMainImageChange(url);
  };

  return (
    <div className="space-y-4">
      <Label htmlFor="images">
        <span className="flex items-center gap-1">
          <Image className="h-4 w-4" /> Product Images
        </span>
      </Label>
      
      {/* AI Image Search Button */}
      {onFindImagesWithAI && (
        <div className="mb-4">
          <Button
            type="button"
            variant="outline"
            onClick={onFindImagesWithAI}
            disabled={isFindingImages}
          >
            {isFindingImages ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Finding Images with AI...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Find Images with AI
              </>
            )}
          </Button>
          <p className="text-xs text-gray-500 mt-1">
            AI will search for relevant product images based on the product name and category
          </p>
        </div>
      )}
      
      {/* Upload controls */}
      <div className="flex flex-wrap gap-2 mb-4">
        <ImageUploader 
          onImageUploaded={addImage} 
          buttonText="Upload New Image"
        />
      </div>

      {/* Main image */}
      {mainImage && (
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Main Image</h3>
          <div className="relative rounded-md overflow-hidden border-2 border-primary w-full max-h-[200px] aspect-video group">
            <img 
              src={mainImage} 
              alt="Main product image" 
              className="w-full h-full object-cover"
              onError={(e) => {
                // Show a placeholder on error
                e.currentTarget.src = "https://placehold.co/600x400/e5e7eb/a1a1aa?text=Main+Image";
              }}
            />
            <div className="absolute top-1 right-1 bg-primary text-white text-xs px-2 py-0.5 rounded-sm">
              Main
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                onClick={() => onMainImageChange && onMainImageChange('')}
              >
                <Trash2 className="h-3 w-3 text-white" />
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Additional images */}
      {images.length > 0 && (
        <div>
          <h3 className="text-sm font-medium mb-2">Additional Images</h3>
          <div className="space-y-2">
            {images.map((img, index) => (
              <div 
                key={index} 
                className={cn(
                  "relative rounded-md overflow-hidden border w-full max-h-[150px] aspect-video group",
                  mainImage === img ? "border-primary" : "border-gray-200"
                )}
              >
                <img 
                  src={img} 
                  alt={`Product image ${index + 1}`} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Show a placeholder on error
                    e.currentTarget.src = "https://placehold.co/600x400/e5e7eb/a1a1aa?text=Image+Error";
                  }}
                />
                
                {/* Image controls */}
                <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-between items-center">
                  <div className="flex gap-1">
                    {/* Move up button */}
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 bg-white/20 hover:bg-white/40"
                        onClick={() => handleMoveUp(index)}
                      >
                        <ArrowUp className="h-3 w-3 text-white" />
                      </Button>
                    )}
                    
                    {/* Move down button */}
                    {index < images.length - 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 bg-white/20 hover:bg-white/40"
                        onClick={() => handleMoveDown(index)}
                      >
                        <ArrowDown className="h-3 w-3 text-white" />
                      </Button>
                    )}
                  </div>
                  
                  <div className="flex gap-1">
                    {/* Set as main image button */}
                    {mainImage !== img && onMainImageChange && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs bg-white/20 hover:bg-white/40 text-white"
                        onClick={() => handleSetAsMain(img)}
                      >
                        Set as Main
                      </Button>
                    )}
                    
                    {/* Delete button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                      onClick={() => removeImage(index)}
                    >
                      <Trash2 className="h-3 w-3 text-white" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {!mainImage && images.length === 0 && (
        <div className="flex flex-col items-center justify-center w-full h-40 rounded-md border border-dashed border-gray-300 p-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">No images added yet</p>
            <p className="text-xs text-gray-400 mt-1">Upload images or find images with AI</p>
          </div>
        </div>
      )}
      
      <p className="text-xs text-gray-500">
        Use the arrows to reorder images. The main image will be displayed as the product thumbnail.
      </p>
    </div>
  );
}
