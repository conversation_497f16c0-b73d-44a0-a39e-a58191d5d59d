
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/auth.basic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { Settings, User, Package } from 'lucide-react';
import { useOrders } from '@/hooks/useOrders';
import OrdersList from '@/components/OrdersList';
import ProfileEditForm from '@/components/ProfileEditForm';

const AccountPage = () => {
  const { user, profile, isAdmin, signOut } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("profile");
  const [isEditing, setIsEditing] = useState(false);
  const { orders, isLoading: ordersLoading } = useOrders();

  useEffect(() => {
    if (!user) {
      navigate('/auth', { replace: true });
    }
  }, [user, navigate]);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const getInitials = () => {
    if (profile?.first_name || profile?.last_name) {
      const firstInitial = profile.first_name ? profile.first_name[0] : '';
      const lastInitial = profile.last_name ? profile.last_name[0] : '';
      return (firstInitial + lastInitial).toUpperCase();
    }
    return user?.email?.[0]?.toUpperCase() || 'U';
  };

  if (!user) return null;

  return (
    <div className="container max-w-4xl py-12">
      <h1 className="text-3xl font-bold mb-8">My Account</h1>
      
      <div className="grid gap-8 md:grid-cols-[1fr_2fr]">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <Avatar className="h-24 w-24">
                  <AvatarFallback className="text-xl">{getInitials()}</AvatarFallback>
                </Avatar>
              </div>
              <CardTitle className="text-center">{profile?.first_name} {profile?.last_name}</CardTitle>
              <CardDescription className="text-center">{user.email}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {isAdmin && (
                <Button 
                  variant="outline" 
                  className="w-full mb-2 flex items-center justify-center gap-2"
                  onClick={() => navigate('/admin')}
                >
                  <Settings className="h-4 w-4" />
                  Admin Dashboard
                </Button>
              )}
              <Button variant="outline" className="w-full" onClick={handleSignOut}>
                Sign Out
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="orders" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Orders
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle>Account Information</CardTitle>
                </CardHeader>
                {isEditing ? (
                  <CardContent>
                    <ProfileEditForm 
                      onCancel={() => setIsEditing(false)}
                      onSuccess={() => {
                        setIsEditing(false);
                        toast({
                          title: "Profile Updated",
                          description: "Your profile has been updated successfully.",
                        });
                      }}
                    />
                  </CardContent>
                ) : (
                  <>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Email:</span>
                          <span>{user.email}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Name:</span>
                          <span>{profile?.first_name} {profile?.last_name || ''}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Address:</span>
                          <span>{profile?.address || 'Not provided'}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">City:</span>
                          <span>{profile?.city || 'Not provided'}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Postal Code:</span>
                          <span>{profile?.postal_code || 'Not provided'}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Country:</span>
                          <span>{profile?.country || 'Not provided'}</span>
                        </div>
                        <div className="grid grid-cols-2">
                          <span className="text-muted-foreground">Role:</span>
                          <span>{isAdmin ? 'Administrator' : 'Customer'}</span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => setIsEditing(true)}
                      >
                        Edit Profile
                      </Button>
                    </CardFooter>
                  </>
                )}
              </Card>
            </TabsContent>
            
            <TabsContent value="orders">
              <Card>
                <CardHeader>
                  <CardTitle>Your Orders</CardTitle>
                  <CardDescription>
                    View and track your order history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <OrdersList orders={orders} isLoading={ordersLoading} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;

