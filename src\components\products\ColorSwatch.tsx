import React from 'react';
import { Check } from 'lucide-react';

interface ColorSwatchProps {
  colorName: string;
  selected: boolean;
  disabled?: boolean;
  onClick: () => void;
}

// Map of color names to CSS color values or gradient definitions
const COLOR_MAP: Record<string, string> = {
  // Standard colors
  'black': '#000000',
  'white': '#ffffff',
  'red': '#ff0000',
  'blue': '#0000ff',
  'green': '#008000',
  'yellow': '#ffff00',
  'purple': '#800080',
  'orange': '#ffa500',
  'pink': '#ffc0cb',
  'brown': '#a52a2a',
  'grey': '#808080',
  'gray': '#808080',
  'silver': '#c0c0c0',
  'gold': 'linear-gradient(45deg, #bf953f, #fcf6ba, #b38728, #fbf5b7, #aa771c)',

  // Custom colors
  'rasta': 'linear-gradient(to right, #009900 33%, #ffff00 33%, #ffff00 66%, #ff0000 66%)',
  'rainbow': 'linear-gradient(to right, red, orange, yellow, green, blue, indigo, violet)',
  'multicolor': 'conic-gradient(red, yellow, lime, aqua, blue, magenta, red)',
  'tie dye': 'radial-gradient(circle, rgba(255,0,0,1) 0%, rgba(255,154,0,1) 10%, rgba(208,222,33,1) 20%, rgba(79,220,74,1) 30%, rgba(63,218,216,1) 40%, rgba(47,201,226,1) 50%, rgba(28,127,238,1) 60%, rgba(95,21,242,1) 70%, rgba(186,12,248,1) 80%, rgba(251,7,217,1) 90%, rgba(255,0,0,1) 100%)',
  'chrome': 'linear-gradient(135deg, #f6f7f8 0%, #e9ebee 50%, #d8d9db 51%, #f6f7f8 100%)',
  'metallic': 'linear-gradient(to right, #8f8f8f, #e6e6e6, #8f8f8f)',
  'copper': 'linear-gradient(to right, #b87333, #e0ac69, #b87333)',
  'bronze': 'linear-gradient(to right, #cd7f32, #e6be8a, #cd7f32)',
  'platinum': 'linear-gradient(to right, #e5e4e2, #ffffff, #e5e4e2)',
  'iridescent': 'linear-gradient(135deg, #83a4d4, #b6fbff, #83a4d4, #b39bc8)',
  'holographic': 'linear-gradient(135deg, #ff00cc, #3333ff, #00ccff, #33cc33, #ffff00, #ff9900)',
  'translucent': 'rgba(255, 255, 255, 0.5)',
  'transparent': 'rgba(255, 255, 255, 0.2)',
  'clear': 'rgba(255, 255, 255, 0.1)',
  'wood': 'linear-gradient(90deg, #a67c52 25%, #8b5a2b 25%, #8b5a2b 50%, #a67c52 50%, #a67c52 75%, #8b5a2b 75%)',
  'marble': 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 25%, #f5f5f5 50%, #e0e0e0 75%, #f5f5f5 100%)',
  'camouflage': 'linear-gradient(45deg, #4b5320 25%, #708238 25%, #708238 50%, #a9b978 50%, #a9b978 75%, #4b5320 75%)',

  // Headchef Spaceman colors
  'alien green': '#4CAF50',
  'mars red': '#D32F2F',
  'neutron purple': '#9C27B0',
  'plasma yellow': '#FFC107',
  'solar flare orange': '#FF9800',
  'space black': '#212121',
  'starburst pink': '#E91E63',

  // Additional color variations
  'light blue': '#add8e6',
  'dark blue': '#00008b',
  'light green': '#90ee90',
  'dark green': '#006400',
  'light red': '#ffcccb',
  'dark red': '#8b0000',
  'navy': '#000080',
  'teal': '#008080',
  'cyan': '#00ffff',
  'magenta': '#ff00ff',
  'lime': '#00ff00',
  'maroon': '#800000',
  'olive': '#808000',
  'aqua': '#00ffff',
  'turquoise': '#40e0d0',
  'lavender': '#e6e6fa',
  'coral': '#ff7f50',
  'crimson': '#dc143c',
  'indigo': '#4b0082',
  'violet': '#ee82ee',
  'beige': '#f5f5dc',
  'khaki': '#f0e68c',
  'tan': '#d2b48c',
};

export const ColorSwatch: React.FC<ColorSwatchProps> = ({
  colorName,
  selected,
  disabled = false,
  onClick,
}) => {
  // Normalize color name for lookup
  const normalizedColorName = colorName.toLowerCase().trim();

  // Get color style from map or use the color name as a fallback
  const getColorStyle = () => {
    // Check if we have a predefined style for this color
    if (COLOR_MAP[normalizedColorName]) {
      return COLOR_MAP[normalizedColorName];
    }

    // Check if it's a composite color (e.g., "light blue", "dark red")
    const compositeParts = normalizedColorName.split(' ');
    if (compositeParts.length > 1) {
      const lastPart = compositeParts[compositeParts.length - 1];
      if (COLOR_MAP[lastPart]) {
        // Modify the base color based on prefix
        if (compositeParts[0] === 'light') {
          return `linear-gradient(to right, ${COLOR_MAP[lastPart]}80, ${COLOR_MAP[lastPart]})`;
        } else if (compositeParts[0] === 'dark') {
          return `linear-gradient(to right, ${COLOR_MAP[lastPart]}, ${COLOR_MAP[lastPart]}80)`;
        }
      }
    }

    // Fallback: try to use the color name directly as a CSS color
    return normalizedColorName;
  };

  const colorStyle = getColorStyle();

  return (
    <button
      type="button"
      className={`relative w-12 h-12 rounded-full overflow-hidden transition-all duration-300 z-100 ${
        selected
          ? 'ring-2 ring-primary ring-offset-2 shadow-lg scale-125 transform-gpu'
          : 'border-2 border-gray-200 hover:border-gray-300 hover:shadow-md hover:scale-110'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      onClick={disabled ? undefined : onClick}
      title={colorName}
      disabled={disabled}
      aria-label={`Color: ${colorName}${selected ? ' (selected)' : ''}`}
      style={{
        transformOrigin: 'center',
      }}
    >
      {/* Color background */}
      <span
        className={`absolute inset-0 ${selected ? 'animate-pulse-subtle' : ''}`}
        style={{
          background: colorStyle,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />

      {/* Checkmark for selected color */}
      {selected && (
        <span className="absolute inset-0 flex items-center justify-center animate-fadeIn">
          <Check
            className={`h-5 w-5 drop-shadow-md ${
              ['white', 'yellow', 'light', 'beige', 'khaki', 'tan', 'clear', 'transparent', 'translucent'].some(c => normalizedColorName.includes(c))
                ? 'text-gray-800'
                : 'text-white'
            }`}
          />
        </span>
      )}

      {/* For colors that need a border to be visible against white backgrounds */}
      {['white', 'clear', 'transparent', 'translucent'].includes(normalizedColorName) && (
        <span className="absolute inset-0 border border-gray-200 rounded-full" />
      )}

      {/* Color name tooltip on hover */}
      <span className="sr-only">{colorName}</span>

      {/* Selected indicator ring */}
      {selected && (
        <span className="absolute -inset-1 rounded-full animate-ping-once opacity-30 bg-primary pointer-events-none"></span>
      )}
    </button>
  );
};
