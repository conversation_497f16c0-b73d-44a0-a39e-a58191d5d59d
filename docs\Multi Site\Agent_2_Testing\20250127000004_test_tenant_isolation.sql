-- Migration: 20250127000004_test_tenant_isolation.sql
-- Description: Creates test data and validation queries for tenant isolation testing

-- Create test function to validate tenant isolation
CREATE OR REPLACE FUNCTION tenant_management.test_tenant_isolation()
RETURNS TABLE (
    test_name TEXT,
    test_passed BOOLEAN,
    details TEXT
) AS $$
DECLARE
    tenant_a_id UUID;
    tenant_b_id UUID;
    tenant_c_id UUID;
    product_count_a INTEGER;
    product_count_b INTEGER;
    product_count_c INTEGER;
    order_count_a INTEGER;
    order_count_b INTEGER;
    order_count_c INTEGER;
    customer_count_a INTEGER;
    customer_count_b INTEGER;
    customer_count_c INTEGER;
BEGIN
    -- Get tenant IDs
    SELECT id INTO tenant_a_id FROM tenant_management.tenants WHERE subdomain = 'greenleaf';
    SELECT id INTO tenant_b_id FROM tenant_management.tenants WHERE subdomain = 'corner';
    SELECT id INTO tenant_c_id FROM tenant_management.tenants WHERE subdomain = 'wellness';
    
    -- Test 1: Count products for each tenant
    SELECT COUNT(*) INTO product_count_a FROM public.products WHERE tenant_id = tenant_a_id;
    SELECT COUNT(*) INTO product_count_b FROM public.products WHERE tenant_id = tenant_b_id;
    SELECT COUNT(*) INTO product_count_c FROM public.products WHERE tenant_id = tenant_c_id;
    
    -- Test 2: Count orders for each tenant
    SELECT COUNT(*) INTO order_count_a FROM public.orders WHERE tenant_id = tenant_a_id;
    SELECT COUNT(*) INTO order_count_b FROM public.orders WHERE tenant_id = tenant_b_id;
    SELECT COUNT(*) INTO order_count_c FROM public.orders WHERE tenant_id = tenant_c_id;
    
    -- Test 3: Count customers for each tenant
    SELECT COUNT(*) INTO customer_count_a FROM public.customers WHERE tenant_id = tenant_a_id;
    SELECT COUNT(*) INTO customer_count_b FROM public.customers WHERE tenant_id = tenant_b_id;
    SELECT COUNT(*) INTO customer_count_c FROM public.customers WHERE tenant_id = tenant_c_id;
    
    -- Return test results
    test_name := 'Tenant A Products Count';
    test_passed := product_count_a > 0;
    details := 'Found ' || product_count_a || ' products for Tenant A';
    RETURN NEXT;
    
    test_name := 'Tenant B Products Count';
    test_passed := product_count_b > 0;
    details := 'Found ' || product_count_b || ' products for Tenant B';
    RETURN NEXT;
    
    test_name := 'Tenant C Products Count';
    test_passed := product_count_c > 0;
    details := 'Found ' || product_count_c || ' products for Tenant C';
    RETURN NEXT;
    
    test_name := 'Tenant A Orders Count';
    test_passed := order_count_a > 0;
    details := 'Found ' || order_count_a || ' orders for Tenant A';
    RETURN NEXT;
    
    test_name := 'Tenant B Orders Count';
    test_passed := order_count_b > 0;
    details := 'Found ' || order_count_b || ' orders for Tenant B';
    RETURN NEXT;
    
    test_name := 'Tenant C Orders Count';
    test_passed := order_count_c > 0;
    details := 'Found ' || order_count_c || ' orders for Tenant C';
    RETURN NEXT;
    
    test_name := 'Tenant A Customers Count';
    test_passed := customer_count_a > 0;
    details := 'Found ' || customer_count_a || ' customers for Tenant A';
    RETURN NEXT;
    
    test_name := 'Tenant B Customers Count';
    test_passed := customer_count_b > 0;
    details := 'Found ' || customer_count_b || ' customers for Tenant B';
    RETURN NEXT;
    
    test_name := 'Tenant C Customers Count';
    test_passed := customer_count_c > 0;
    details := 'Found ' || customer_count_c || ' customers for Tenant C';
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Create test data insertion function
CREATE OR REPLACE FUNCTION tenant_management.create_test_data()
RETURNS VOID AS $$
DECLARE
    tenant_a_id UUID;
    tenant_b_id UUID;
    tenant_c_id UUID;
    category_id_a UUID;
    category_id_b UUID;
    category_id_c UUID;
    product_id_a UUID;
    product_id_b UUID;
    product_id_c UUID;
    customer_id_a UUID;
    customer_id_b UUID;
    customer_id_c UUID;
BEGIN
    -- Create test tenants if they don't exist
    INSERT INTO tenant_management.tenants (name, subdomain, subscription_tier)
    VALUES ('Fashion Forward', 'fashion', 'premium')
    ON CONFLICT (subdomain) DO NOTHING
    RETURNING id INTO tenant_a_id;
    
    INSERT INTO tenant_management.tenants (name, subdomain, subscription_tier)
    VALUES ('Tech Hub', 'techhub', 'standard')
    ON CONFLICT (subdomain) DO NOTHING
    RETURNING id INTO tenant_b_id;
    
    INSERT INTO tenant_management.tenants (name, subdomain, subscription_tier)
    VALUES ('Book Nook', 'booknook', 'basic')
    ON CONFLICT (subdomain) DO NOTHING
    RETURNING id INTO tenant_c_id;
    
    -- Get tenant IDs if they already exist
    IF tenant_a_id IS NULL THEN
        SELECT id INTO tenant_a_id FROM tenant_management.tenants WHERE subdomain = 'fashion';
    END IF;
    
    IF tenant_b_id IS NULL THEN
        SELECT id INTO tenant_b_id FROM tenant_management.tenants WHERE subdomain = 'techhub';
    END IF;
    
    IF tenant_c_id IS NULL THEN
        SELECT id INTO tenant_c_id FROM tenant_management.tenants WHERE subdomain = 'booknook';
    END IF;
    
    -- Create categories for each tenant
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES ('Clothing', 'Stylish apparel for all occasions', tenant_a_id)
    RETURNING id INTO category_id_a;
    
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES ('Smartphones', 'Latest smartphone technology', tenant_b_id)
    RETURNING id INTO category_id_b;
    
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES ('Fiction', 'Engaging fiction books', tenant_c_id)
    RETURNING id INTO category_id_c;
    
    -- Create products for each tenant
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES ('Cotton T-Shirt', 'Comfortable cotton t-shirt', 29.99, category_id_a, tenant_a_id)
    RETURNING id INTO product_id_a;
    
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES ('Smartphone Pro', 'Latest flagship smartphone', 899.99, category_id_b, tenant_b_id)
    RETURNING id INTO product_id_b;
    
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES ('Mystery Novel', 'Bestselling mystery novel', 14.99, category_id_c, tenant_c_id)
    RETURNING id INTO product_id_c;
    
    -- Create customers for each tenant
    INSERT INTO public.customers (name, email, phone, tenant_id)
    VALUES ('John Smith', '<EMAIL>', '555-1234', tenant_a_id)
    RETURNING id INTO customer_id_a;
    
    INSERT INTO public.customers (name, email, phone, tenant_id)
    VALUES ('Jane Doe', '<EMAIL>', '555-5678', tenant_b_id)
    RETURNING id INTO customer_id_b;
    
    INSERT INTO public.customers (name, email, phone, tenant_id)
    VALUES ('Bob Johnson', '<EMAIL>', '555-9012', tenant_c_id)
    RETURNING id INTO customer_id_c;
    
    -- Create orders for each tenant
    INSERT INTO public.orders (customer_id, total_amount, status, tenant_id)
    VALUES (customer_id_a, 49.99, 'completed', tenant_a_id);
    
    INSERT INTO public.orders (customer_id, total_amount, status, tenant_id)
    VALUES (customer_id_b, 199.99, 'processing', tenant_b_id);
    
    INSERT INTO public.orders (customer_id, total_amount, status, tenant_id)
    VALUES (customer_id_c, 29.99, 'pending', tenant_c_id);
    
    -- Create additional test data for each tenant
    -- Tenant A: Fashion Forward
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES 
        ('Shoes', 'Footwear for all occasions', tenant_a_id),
        ('Accessories', 'Fashion accessories and jewelry', tenant_a_id);
        
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES 
        ('Designer Jeans', 'Premium denim jeans', 79.99, category_id_a, tenant_a_id),
        ('Summer Dress', 'Light summer dress', 49.99, category_id_a, tenant_a_id),
        ('Running Shoes', 'Athletic running shoes', 89.99, 
            (SELECT id FROM public.categories WHERE name = 'Shoes' AND tenant_id = tenant_a_id), 
            tenant_a_id),
        ('Leather Wallet', 'Genuine leather wallet', 39.99, 
            (SELECT id FROM public.categories WHERE name = 'Accessories' AND tenant_id = tenant_a_id), 
            tenant_a_id);
    
    -- Tenant B: Tech Hub
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES 
        ('Laptops', 'High-performance laptops', tenant_b_id),
        ('Accessories', 'Tech accessories and peripherals', tenant_b_id);
        
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES 
        ('Budget Phone', 'Affordable smartphone', 299.99, category_id_b, tenant_b_id),
        ('Gaming Laptop', 'High-performance gaming laptop', 1299.99, 
            (SELECT id FROM public.categories WHERE name = 'Laptops' AND tenant_id = tenant_b_id), 
            tenant_b_id),
        ('Wireless Headphones', 'Noise-cancelling wireless headphones', 149.99, 
            (SELECT id FROM public.categories WHERE name = 'Accessories' AND tenant_id = tenant_b_id), 
            tenant_b_id),
        ('Smartwatch', 'Fitness tracking smartwatch', 199.99, 
            (SELECT id FROM public.categories WHERE name = 'Accessories' AND tenant_id = tenant_b_id), 
            tenant_b_id);
    
    -- Tenant C: Book Nook
    INSERT INTO public.categories (name, description, tenant_id)
    VALUES 
        ('Non-Fiction', 'Educational and informative books', tenant_c_id),
        ('Stationery', 'Notebooks, pens and office supplies', tenant_c_id);
        
    INSERT INTO public.products (name, description, price, category_id, tenant_id)
    VALUES 
        ('Science Fiction Novel', 'Bestselling sci-fi book', 12.99, category_id_c, tenant_c_id),
        ('Business Guide', 'Comprehensive business handbook', 24.99, 
            (SELECT id FROM public.categories WHERE name = 'Non-Fiction' AND tenant_id = tenant_c_id), 
            tenant_c_id),
        ('Notebook Set', 'Premium notebook collection', 19.99, 
            (SELECT id FROM public.categories WHERE name = 'Stationery' AND tenant_id = tenant_c_id), 
            tenant_c_id),
        ('Fountain Pen', 'Elegant writing instrument', 29.99, 
            (SELECT id FROM public.categories WHERE name = 'Stationery' AND tenant_id = tenant_c_id), 
            tenant_c_id);
END;
$$ LANGUAGE plpgsql;

-- Create performance test function
CREATE OR REPLACE FUNCTION tenant_management.test_query_performance()
RETURNS TABLE (
    query_description TEXT,
    execution_time_ms FLOAT,
    row_count INTEGER,
    uses_index BOOLEAN
) AS $$
DECLARE
    start_time TIMESTAMPTZ;
    end_time TIMESTAMPTZ;
    explain_result TEXT;
    row_count INTEGER;
    tenant_a_id UUID;
BEGIN
    -- Get tenant A ID
    SELECT id INTO tenant_a_id FROM tenant_management.tenants WHERE subdomain = 'greenleaf';
    
    -- Test 1: Query products with tenant filter
    start_time := clock_timestamp();
    EXECUTE 'SELECT COUNT(*) FROM public.products WHERE tenant_id = $1' INTO row_count USING tenant_a_id;
    end_time := clock_timestamp();
    
    EXECUTE 'EXPLAIN ANALYZE SELECT COUNT(*) FROM public.products WHERE tenant_id = $1' USING tenant_a_id INTO explain_result;
    
    query_description := 'Products query with tenant filter';
    execution_time_ms := 1000 * extract(epoch from (end_time - start_time));
    uses_index := explain_result ILIKE '%Index Scan%';
    RETURN NEXT;
    
    -- Test 2: Query orders with tenant filter
    start_time := clock_timestamp();
    EXECUTE 'SELECT COUNT(*) FROM public.orders WHERE tenant_id = $1' INTO row_count USING tenant_a_id;
    end_time := clock_timestamp();
    
    EXECUTE 'EXPLAIN ANALYZE SELECT COUNT(*) FROM public.orders WHERE tenant_id = $1' USING tenant_a_id INTO explain_result;
    
    query_description := 'Orders query with tenant filter';
    execution_time_ms := 1000 * extract(epoch from (end_time - start_time));
    uses_index := explain_result ILIKE '%Index Scan%';
    RETURN NEXT;
    
    -- Test 3: Query customers with tenant filter
    start_time := clock_timestamp();
    EXECUTE 'SELECT COUNT(*) FROM public.customers WHERE tenant_id = $1' INTO row_count USING tenant_a_id;
    end_time := clock_timestamp();
    
    EXECUTE 'EXPLAIN ANALYZE SELECT COUNT(*) FROM public.customers WHERE tenant_id = $1' USING tenant_a_id INTO explain_result;
    
    query_description := 'Customers query with tenant filter';
    execution_time_ms := 1000 * extract(epoch from (end_time - start_time));
    uses_index := explain_result ILIKE '%Index Scan%';
    RETURN NEXT;
    
    -- Test 4: Join query with tenant filter
    start_time := clock_timestamp();
    EXECUTE '
        SELECT COUNT(*) 
        FROM public.orders o
        JOIN public.customers c ON o.customer_id = c.id
        WHERE o.tenant_id = $1 AND c.tenant_id = $1
    ' INTO row_count USING tenant_a_id;
    end_time := clock_timestamp();
    
    EXECUTE '
        EXPLAIN ANALYZE
        SELECT COUNT(*) 
        FROM public.orders o
        JOIN public.customers c ON o.customer_id = c.id
        WHERE o.tenant_id = $1 AND c.tenant_id = $1
    ' USING tenant_a_id INTO explain_result;
    
    query_description := 'Join query with tenant filter';
    execution_time_ms := 1000 * extract(epoch from (end_time - start_time));
    uses_index := explain_result ILIKE '%Index Scan%';
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;
