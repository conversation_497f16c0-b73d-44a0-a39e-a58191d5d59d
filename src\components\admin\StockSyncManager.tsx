import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  FileSpreadsheet, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Package,
  TrendingUp,
  Eye,
  Download
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { stockSyncService } from '@/services/stockSyncService';
import Papa from 'papaparse';

interface StockSyncManagerProps {}

interface SyncResults {
  updated: number;
  activated: number;
  failed: number;
  unmatched: any[];
  incompleteProducts: any[];
  summary: string;
}

export function StockSyncManager({}: StockSyncManagerProps) {
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [syncResults, setSyncResults] = useState<SyncResults | null>(null);
  const [previewMode, setPreviewMode] = useState(true);
  const [progress, setProgress] = useState(0);

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (!uploadedFile) return;

    if (!uploadedFile.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: 'Invalid File Type',
        description: 'Please upload a CSV file.',
        variant: 'destructive'
      });
      return;
    }

    setFile(uploadedFile);
    parseCSVFile(uploadedFile);
  };

  // Parse CSV file
  const parseCSVFile = (file: File) => {
    setIsProcessing(true);
    
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        setCsvData(results.data);
        setIsProcessing(false);
        toast({
          title: 'CSV Parsed Successfully',
          description: `Found ${results.data.length} rows in the CSV file.`
        });
      },
      error: (error) => {
        console.error('CSV parsing error:', error);
        setIsProcessing(false);
        toast({
          title: 'CSV Parsing Failed',
          description: 'There was an error parsing the CSV file.',
          variant: 'destructive'
        });
      }
    });
  };

  // Run stock sync
  const runStockSync = async (preview: boolean = true) => {
    if (!csvData.length) {
      toast({
        title: 'No Data',
        description: 'Please upload a CSV file first.',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const results = await stockSyncService.syncStockFromCSV(csvData);
      
      clearInterval(progressInterval);
      setProgress(100);
      
      setSyncResults(results);
      
      toast({
        title: preview ? 'Preview Complete' : 'Sync Complete',
        description: results.summary
      });

    } catch (error) {
      console.error('Stock sync error:', error);
      toast({
        title: 'Sync Failed',
        description: 'There was an error during the stock synchronization.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  // Export incomplete products report
  const exportIncompleteProducts = () => {
    if (!syncResults?.incompleteProducts.length) return;

    const csvContent = Papa.unparse(syncResults.incompleteProducts.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku || 'Missing',
      issues: product.issues.join('; '),
      can_be_activated: product.canBeActivated ? 'Yes' : 'No',
      csv_match_found: product.csvMatch ? 'Yes' : 'No'
    })));

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'incomplete-products-report.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Stock Synchronization Manager
          </CardTitle>
          <CardDescription>
            Upload a CSV file from your EPOS system to sync stock levels and identify incomplete products
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="csv-upload">Upload Stock CSV File</Label>
            <div className="flex items-center gap-4">
              <Input
                id="csv-upload"
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="flex-1"
              />
              {file && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <FileSpreadsheet className="h-3 w-3" />
                  {file.name}
                </Badge>
              )}
            </div>
          </div>

          {/* CSV Data Preview */}
          {csvData.length > 0 && (
            <Alert>
              <FileSpreadsheet className="h-4 w-4" />
              <AlertDescription>
                CSV loaded successfully with {csvData.length} rows. 
                Expected columns: sku, product_name, stock_quantity, price, etc.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          {csvData.length > 0 && (
            <div className="flex gap-2">
              <Button
                onClick={() => runStockSync(true)}
                disabled={isProcessing}
                variant="outline"
              >
                <Eye className="mr-2 h-4 w-4" />
                Preview Changes
              </Button>
              <Button
                onClick={() => runStockSync(false)}
                disabled={isProcessing || !syncResults}
              >
                <Upload className="mr-2 h-4 w-4" />
                Apply Changes
              </Button>
            </div>
          )}

          {/* Progress Bar */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      {syncResults && (
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="incomplete">Incomplete Products</TabsTrigger>
            <TabsTrigger value="unmatched">Unmatched</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sync Results Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">{syncResults.updated}</div>
                    <div className="text-sm text-gray-600">Updated</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">{syncResults.activated}</div>
                    <div className="text-sm text-gray-600">Activated</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-yellow-600">{syncResults.unmatched.length}</div>
                    <div className="text-sm text-gray-600">Unmatched</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-red-600">{syncResults.failed}</div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                </div>
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap">{syncResults.summary}</pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="incomplete" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Incomplete Products Analysis</CardTitle>
                  <CardDescription>
                    Products that are inactive and could potentially be fixed
                  </CardDescription>
                </div>
                <Button onClick={exportIncompleteProducts} variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export Report
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {syncResults.incompleteProducts.slice(0, 50).map((product, index) => (
                    <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">
                          SKU: {product.sku || 'Missing'} • Issues: {product.issues.join(', ')}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {product.csvMatch && (
                          <Badge variant="outline">CSV Match</Badge>
                        )}
                        <Badge variant={product.canBeActivated ? 'default' : 'secondary'}>
                          {product.canBeActivated ? 'Can Activate' : 'Needs Work'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {syncResults.incompleteProducts.length > 50 && (
                    <div className="text-center text-sm text-gray-500 py-2">
                      Showing first 50 of {syncResults.incompleteProducts.length} products
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="unmatched" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Unmatched CSV Rows</CardTitle>
                <CardDescription>
                  Products from your CSV that couldn't be matched to existing products
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {syncResults.unmatched.slice(0, 20).map((row, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="font-medium">{row.product_name || 'Unnamed Product'}</div>
                      <div className="text-sm text-gray-500">
                        SKU: {row.sku || 'N/A'} • Stock: {row.stock_quantity || 'N/A'}
                      </div>
                    </div>
                  ))}
                  {syncResults.unmatched.length > 20 && (
                    <div className="text-center text-sm text-gray-500 py-2">
                      Showing first 20 of {syncResults.unmatched.length} unmatched rows
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Matching Strategy:</strong> Products are matched by SKU first, then by exact name, 
                      then by fuzzy name matching (70%+ similarity). Barcode matching is also supported if available.
                    </AlertDescription>
                  </Alert>
                  
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Activation Criteria:</strong> Inactive products can be automatically activated if they have 
                      a name, price, and stock quantity. Images and descriptions are recommended but not required.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

export default StockSyncManager;
