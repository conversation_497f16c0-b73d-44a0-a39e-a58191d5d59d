# Cannabis Seed Filtering Integration Plan

This document outlines the plan for integrating a specialized filtering system for cannabis seeds into our e-commerce platform, building on top of the new variant-based product system.

## Implementation Phases

### Phase 1: Core Product Import & Variant System
- Focus on correctly importing all products with proper variant pricing
- Ensure images are correctly mapped and displayed
- Implement the variant-based product system for all products

### Phase 2: Seed Category Specialization
- Identify and categorize seed products
- Implement seed-specific attributes and filtering
- Create specialized UI for the seed category

### Phase 3: Rich Media & Enhanced Features
- Add terpene profiles and cannabinoid data
- Implement advanced filtering and sorting
- Create specialized product detail views for seeds

## Phase 1: Core Product Import & Variant System

### Objectives
1. Successfully import all products with correct data
2. Implement variant-based pricing system
3. Ensure proper image mapping
4. Assign products to appropriate categories

### Database Structure
```sql
-- Core product table (existing)
-- products table with basic fields

-- Product Variants table (new)
CREATE TABLE product_variants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_name TEXT NOT NULL,
  sku TEXT,
  price DECIMAL(10, 2) NOT NULL,
  sale_price DECIMAL(10, 2),
  stock_quantity INTEGER DEFAULT 0,
  in_stock BOOLEAN DEFAULT TRUE,
  image TEXT,
  option_combination JSONB,
  external_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);
```

### Implementation Steps
1. Create database migrations for variant tables
2. Update product import process to handle variants
3. Implement image filename transformation during import
4. Create admin UI for variant management
5. Update product detail pages to display variants

## Phase 2: Seed Category Specialization

### Objectives
1. Identify and categorize all seed products
2. Create seed-specific attribute system
3. Implement product matching with Dutch Passion data
4. Build filtering UI for seed products

### Database Structure
```sql
-- Filter Categories table
CREATE TABLE filter_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  display_order INT NOT NULL,
  category_id UUID REFERENCES categories(id),
  is_active BOOLEAN DEFAULT TRUE
);

-- Filter Options table
CREATE TABLE filter_options (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID REFERENCES filter_categories(id),
  name VARCHAR(100) NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  display_order INT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE
);

-- Product-Filter Relationships table
CREATE TABLE product_filters (
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  filter_option_id UUID REFERENCES filter_options(id) ON DELETE CASCADE,
  PRIMARY KEY (product_id, filter_option_id)
);

-- Seed Product Attributes table
CREATE TABLE seed_product_attributes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  seed_type VARCHAR(50),
  flowering_time VARCHAR(50),
  yield VARCHAR(50),
  thc_level VARCHAR(50),
  cbd_level VARCHAR(50),
  effect VARCHAR(50),
  seed_family VARCHAR(100),
  is_manually_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Implementation Steps
1. Create database migrations for filter tables
2. Build product matching tool for seed attributes
3. Import Dutch Passion filter data
4. Create admin UI for seed attribute management
5. Implement frontend filtering components

## Product Matching Strategy

### Matching Algorithm
We'll use a combination of approaches to match our seed products with the Dutch Passion filter system:

1. **Fuzzy Name Matching**
   - Compare product names using string similarity algorithms
   - Suggest matches based on similarity scores
   - Allow manual confirmation of matches

2. **Attribute Extraction**
   - Extract seed type from product names and descriptions
   - Identify flowering times, effects, etc. from descriptions
   - Use pattern matching to find relevant attributes

3. **Manual Assignment**
   - Create an admin interface for manual attribute assignment
   - Allow bulk editing of attributes for multiple products
   - Provide validation to ensure consistency

### Matching Tool Implementation
```typescript
// Example implementation of product matching tool
function findSimilarProducts(yourProduct, dutchPassionProducts, threshold = 0.7) {
  const matches = [];

  // Normalize your product name
  const normalizedYourName = yourProduct.name.toLowerCase()
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s]/g, '');

  // Find matches
  dutchPassionProducts.forEach(dpProduct => {
    // Normalize Dutch Passion product name
    const normalizedDpName = dpProduct.name.toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '');

    // Calculate similarity score using Levenshtein distance
    const similarity = 1 - (levenshteinDistance(normalizedYourName, normalizedDpName) /
                           Math.max(normalizedYourName.length, normalizedDpName.length));

    // If similarity exceeds threshold, add to matches
    if (similarity >= threshold) {
      matches.push({
        product: dpProduct,
        similarity: similarity
      });
    }
  });

  // Sort matches by similarity (highest first)
  return matches.sort((a, b) => b.similarity - a.similarity);
}
```

## Phase 3: Rich Media & Enhanced Features

### Objectives
1. Add terpene profiles and cannabinoid data
2. Implement advanced visualization components
3. Create specialized product detail views for seeds

### Database Structure
```sql
-- Rich Media table
CREATE TABLE seed_rich_media (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  terpene_profile JSONB,
  cannabinoid_profile JSONB,
  chart_images JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Implementation Steps
1. Create database migrations for rich media tables
2. Build admin UI for rich media management
3. Implement frontend visualization components
4. Create specialized product detail template for seeds

## Integration with Existing Systems

### Product Import Process
The existing product import process will be enhanced to:
1. Handle variant data correctly
2. Transform image filenames to match storage conventions
3. Flag seed products for attribute assignment

### Admin UI
The admin UI will be extended with:
1. Variant management for all products
2. Seed attribute management for seed products
3. Product matching tool for seed categorization

### Frontend
The frontend will be enhanced with:
1. Variant selection UI for all products
2. Specialized filtering UI for the seeds category
3. Rich media display for seed products

## Technical Requirements

### Backend
- Supabase database migrations
- API endpoints for filter data
- Product matching algorithms

### Frontend
- React components for filtering UI
- Variant selection components
- Visualization components for rich media

## Timeline Estimate

### Phase 1: Core Product Import & Variant System
- Database migrations: 1 day
- Import process updates: 2-3 days
- Admin UI for variants: 2-3 days
- Frontend updates: 2-3 days

### Phase 2: Seed Category Specialization
- Database migrations: 1 day
- Product matching tool: 3-4 days
- Filter data import: 1 day
- Admin UI for seed attributes: 2-3 days
- Frontend filtering components: 3-4 days

### Phase 3: Rich Media & Enhanced Features
- Database migrations: 1 day
- Rich media management: 2-3 days
- Visualization components: 3-4 days
- Specialized product detail views: 2-3 days

## Next Steps

1. Complete Phase 1 implementation
   - Create database migrations for variant tables
   - Update product import process
   - Implement variant management UI

2. Prepare for Phase 2
   - Identify all seed products
   - Import Dutch Passion filter data
   - Create database structure for filters

3. Begin development of product matching tool
   - Implement fuzzy matching algorithm
   - Create attribute extraction functions
   - Build admin UI for matching

## Recommended Enhancements

Based on analysis of the Dutch Passion filtering system and current e-commerce best practices, the following enhancements are recommended to maximize the effectiveness of the seed filtering system:

### Mobile-First Approach
1. Design filter UI with mobile as the primary consideration
2. Implement collapsible filter sections that don't overwhelm mobile screens
3. Create touch-friendly filter controls with adequate spacing
4. Ensure filter application doesn't disrupt the user's scroll position
5. Add swipe gestures for quick filter application/removal

### Performance Optimization
1. Implement database indexing on filter-related tables:
   ```sql
   CREATE INDEX idx_product_filters_product_id ON product_filters(product_id);
   CREATE INDEX idx_product_filters_filter_option_id ON product_filters(filter_option_id);
   CREATE INDEX idx_filter_options_category_id ON filter_options(category_id);
   ```
2. Add client-side caching of filter results using localStorage
3. Implement debounced filter application to prevent excessive re-rendering
4. Use pagination or infinite scroll for filtered product lists
5. Optimize image loading with lazy loading and appropriate sizing

### SEO Considerations
1. Generate SEO-friendly URLs for filtered views (e.g., `/seeds/indica/high-thc/`)
2. Implement canonical URLs to prevent duplicate content issues
3. Add structured data markup for seed products with their attributes
4. Create category landing pages for popular filter combinations
5. Ensure filter changes update the browser history for proper back-button behavior

### User Experience Enhancements
1. Add filter preference storage for returning users
2. Implement "popular combinations" shortcuts based on common filter selections
3. Add visual indicators for filter compatibility (e.g., graying out incompatible options)
4. Create tooltips explaining filter categories for new users
5. Add a "compare products" feature for filtered selections

### Analytics Integration
1. Track filter usage patterns to understand customer preferences
2. Implement event tracking for filter interactions
3. Create custom dimensions for filter combinations
4. Set up conversion tracking based on filter paths
5. Build reports to identify high-performing filter combinations

### Accessibility Improvements
1. Ensure all filter controls are keyboard navigable
2. Add proper ARIA labels and roles to filter components
3. Ensure sufficient color contrast for filter UI elements
4. Provide text alternatives for any icon-based filters
5. Test with screen readers to verify accessibility

## Revised Timeline Estimates

Based on the current development pace (6 days from start to current state with a complete rebuild of the products section), here are more realistic timeline estimates:

### Phase 1: Core Product Import & Variant System
- ✅ **COMPLETED**

### Phase 2: Seed Category Specialization
- Database migrations: 1 day
- Product matching tool: 2-3 days
- Filter data import: 1 day
- Admin UI for seed attributes: 1-2 days
- Frontend filtering components: 2-3 days
- **Total: 7-10 days**

### Phase 3: Rich Media & Enhanced Features
- Database migrations: 1 day
- Rich media management: 2 days
- Visualization components: 2-3 days
- Specialized product detail views: 1-2 days
- **Total: 6-8 days**

**Overall remaining project timeline: 13-18 days** (approximately 3-4 weeks)

This timeline assumes:
- Full-time focus on this feature
- No major roadblocks or requirement changes
- Continued rapid development pace as demonstrated so far
- Parallel work on some components where possible

## Implementation Priorities

To maximize impact while managing development time, consider this prioritized approach:

1. **Must-Have Features** (Core of Phase 2)
   - Basic filter categories (Seed Type, Effect, THC level)
   - Mobile-friendly filter UI
   - Product matching system
   - SEO-friendly URLs

2. **High-Value Additions** (Remainder of Phase 2)
   - Complete seed-specific filters
   - Enhanced product detail pages
   - Filter URL parameters for sharing

3. **Polish and Advanced Features** (Phase 3)
   - Rich media visualizations
   - Characteristic icons
   - Advanced sorting and recommendations
