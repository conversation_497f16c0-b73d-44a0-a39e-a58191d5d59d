import { useState } from 'react';
import { Address } from '@/hooks/useAddresses';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Edit, Trash2, Check, Home } from 'lucide-react';

interface AddressListProps {
  addresses: Address[];
  selectedAddressId?: string;
  onSelect: (address: Address) => void;
  onEdit: (address: Address) => void;
  onDelete: (id: string) => void;
  onSetDefault: (id: string) => void;
  isLoading?: boolean;
}

export function AddressList({
  addresses,
  selectedAddressId,
  onSelect,
  onEdit,
  onDelete,
  onSetDefault,
  isLoading = false
}: AddressListProps) {
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setAddressToDelete(id);
  };

  const confirmDelete = () => {
    if (addressToDelete) {
      onDelete(addressToDelete);
      setAddressToDelete(null);
    }
  };

  const cancelDelete = () => {
    setAddressToDelete(null);
  };

  if (addresses.length === 0) {
    return (
      <Card className="border-dashed border-2 border-gray-300 bg-gray-50">
        <CardContent className="pt-6 text-center">
          <Home className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-gray-500">You don't have any saved addresses yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {addresses.map((address) => (
        <Card 
          key={address.id} 
          className={`${selectedAddressId === address.id ? 'border-primary' : ''} ${isLoading ? 'opacity-50' : ''}`}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">{address.full_name}</CardTitle>
                <CardDescription>{address.phone}</CardDescription>
              </div>
              {address.is_default && (
                <Badge variant="outline" className="ml-2">Default</Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm">
              {address.street}<br />
              {address.city}{address.state ? `, ${address.state}` : ''}<br />
              {address.postal_code}<br />
              {address.country}
            </p>
          </CardContent>
          <CardFooter className="flex justify-between pt-2">
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onEdit(address)}
                disabled={isLoading}
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
              
              <AlertDialog open={addressToDelete === address.id} onOpenChange={(open) => !open && cancelDelete()}>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDeleteClick(address.id)}
                    disabled={isLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Address</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this address? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            
            <div className="flex space-x-2">
              {!address.is_default && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => onSetDefault(address.id)}
                  disabled={isLoading}
                >
                  Set as Default
                </Button>
              )}
              
              <Button 
                variant={selectedAddressId === address.id ? "secondary" : "default"} 
                size="sm" 
                onClick={() => onSelect(address)}
                disabled={isLoading}
              >
                {selectedAddressId === address.id ? (
                  <>
                    <Check className="h-4 w-4 mr-1" />
                    Selected
                  </>
                ) : (
                  'Use This Address'
                )}
              </Button>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
