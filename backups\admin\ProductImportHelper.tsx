import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';

/**
 * This component provides options for importing products with different image handling strategies.
 * It's designed to help with bulk imports from Wix where image handling can be complex.
 */
const ProductImportHelper: React.FC = () => {
  const [importStrategy, setImportStrategy] = useState<'skip' | 'external' | 'upload'>('skip');
  const [wixDomain, setWixDomain] = useState('');
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const handleContinue = () => {
    // Store the selected strategy in localStorage so the main import page can use it
    localStorage.setItem('importStrategy', importStrategy);
    
    if (importStrategy === 'external' && wixDomain) {
      localStorage.setItem('wixDomain', wixDomain);
    }
    
    toast({
      title: 'Import Strategy Selected',
      description: `Continuing with ${getStrategyName(importStrategy)} strategy`,
    });
    
    // Navigate to the main import page
    navigate('/admin/import');
  };
  
  const getStrategyName = (strategy: string) => {
    switch (strategy) {
      case 'skip': return 'Skip Images';
      case 'external': return 'External Wix URLs';
      case 'upload': return 'Upload to Supabase';
      default: return strategy;
    }
  };
  
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Product Import Helper</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Choose Image Import Strategy</CardTitle>
          <CardDescription>
            Select how to handle product images during import
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={importStrategy}
            onValueChange={(value) => setImportStrategy(value as 'skip' | 'external' | 'upload')}
            className="space-y-6"
          >
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="skip" id="skip" />
                <Label htmlFor="skip" className="font-medium text-lg">
                  Skip Images (Recommended for 1500+ products)
                </Label>
              </div>
              <div className="ml-6 text-gray-600">
                <p>Import products without images initially. Use placeholder images instead.</p>
                <p className="mt-1">✅ <span className="font-medium">Benefits:</span> Fastest import, no need to extract Wix images</p>
                <p>✅ You can gradually add images later using the admin interface</p>
                <p>✅ Perfect for quickly getting your product catalog online</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="external" id="external" />
                <Label htmlFor="external" className="font-medium text-lg">
                  Use External Wix URLs
                </Label>
              </div>
              <div className="ml-6 text-gray-600">
                <p>Reference images directly from Wix's CDN servers.</p>
                <p className="mt-1">✅ <span className="font-medium">Benefits:</span> No need to download/upload images</p>
                <p>⚠️ <span className="font-medium">Warning:</span> Requires the Wix site to remain active</p>
                <p>⚠️ Images will break if the Wix site is taken down</p>
              </div>
              
              {importStrategy === 'external' && (
                <div className="ml-6 mt-4 p-4 border rounded-md bg-gray-50">
                  <Label htmlFor="wixDomain" className="mb-2 block text-sm font-medium">
                    Wix Site Domain (optional)
                  </Label>
                  <Input
                    id="wixDomain"
                    value={wixDomain}
                    onChange={(e) => setWixDomain(e.target.value)}
                    placeholder="e.g., mysite.wixsite.com"
                    className="w-full max-w-md"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave blank to use default Wix CDN URLs
                  </p>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="upload" id="upload" />
                <Label htmlFor="upload" className="font-medium text-lg">
                  Upload to Supabase (Standard)
                </Label>
              </div>
              <div className="ml-6 text-gray-600">
                <p>Upload images to your own Supabase storage.</p>
                <p className="mt-1">✅ <span className="font-medium">Benefits:</span> Full control over your images</p>
                <p>✅ Images won't break if Wix site is taken down</p>
                <p>⚠️ <span className="font-medium">Warning:</span> For 1500+ products, you'll need to extract all Wix images first</p>
                <p>⚠️ This process could take several hours</p>
              </div>
            </div>
          </RadioGroup>
          
          <div className="mt-8">
            <Button onClick={handleContinue} size="lg">
              Continue to Import
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Bulk Import Tips</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium text-lg">Recommended Approach for 1500+ Products:</h3>
            <ol className="list-decimal ml-6 mt-2 space-y-2">
              <li>Import products without images first (using "Skip Images" option)</li>
              <li>Categorize all products using the bulk category assignment feature</li>
              <li>Gradually add images for your most important products first</li>
              <li>Consider prioritizing featured or best-selling products</li>
            </ol>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">CSV Format Requirements:</h3>
            <p className="mt-1">Your CSV should include these columns:</p>
            <ul className="list-disc ml-6 mt-2">
              <li><code>name</code> - Product name</li>
              <li><code>description</code> - Product description</li>
              <li><code>price</code> - Product price</li>
              <li><code>productImageUrl</code> - Image URL or filename</li>
              <li><code>collection</code> - Category name (optional)</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductImportHelper;
