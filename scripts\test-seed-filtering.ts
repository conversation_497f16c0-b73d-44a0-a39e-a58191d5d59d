#!/usr/bin/env tsx
/**
 * Test script for seed filtering system
 * This script validates the seed identification logic and filtering functions
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSeedIdentification() {
  console.log('🧪 Testing seed identification logic...\n');
  
  // Test cases for seed identification
  const testCases = [
    // Should be identified as seeds
    { name: 'White Widow Feminised Seeds', expected: true },
    { name: 'Auto Gorilla x5', expected: true },
    { name: 'Northern Lights Regular Seeds', expected: true },
    { name: 'Blueberry Auto (3 seeds)', expected: true },
    { name: '<PERSON><PERSON> Kush Feminized', expected: true },
    
    // Should NOT be identified as seeds
    { name: 'Glass Bong 25cm', expected: false },
    { name: 'Rolling Papers King Size', expected: false },
    { name: 'Herb Grinder Metal', expected: false },
    { name: 'Mylar Bags 100 Pack', expected: false },
    { name: 'Pipe Screens 20mm', expected: false },
  ];
  
  for (const testCase of testCases) {
    try {
      const { data, error } = await supabase.rpc('is_seed_product', {
        product_name: testCase.name
      });
      
      if (error) {
        console.error(`❌ Error testing "${testCase.name}":`, error);
        continue;
      }
      
      const result = data === testCase.expected;
      const icon = result ? '✅' : '❌';
      const status = result ? 'PASS' : 'FAIL';
      
      console.log(`${icon} ${status}: "${testCase.name}" -> ${data} (expected: ${testCase.expected})`);
      
    } catch (err) {
      console.error(`❌ Exception testing "${testCase.name}":`, err);
    }
  }
}

async function testSeedProductRetrieval() {
  console.log('\n🔍 Testing seed product retrieval...\n');
  
  try {
    // Test getting all active seed products
    const { data: activeSeeds, error: activeError } = await supabase.rpc('get_seed_products', {
      p_active_only: true
    });
    
    if (activeError) {
      console.error('❌ Error getting active seeds:', activeError);
    } else {
      console.log(`✅ Found ${activeSeeds?.length || 0} active seed products`);
    }
    
    // Test getting all seed products (active + inactive)
    const { data: allSeeds, error: allError } = await supabase.rpc('get_seed_products', {
      p_active_only: false
    });
    
    if (allError) {
      console.error('❌ Error getting all seeds:', allError);
    } else {
      console.log(`✅ Found ${allSeeds?.length || 0} total seed products`);
    }
    
    // Test getting inactive seed products
    const { data: inactiveSeeds, error: inactiveError } = await supabase.rpc('find_inactive_seed_products');
    
    if (inactiveError) {
      console.error('❌ Error getting inactive seeds:', inactiveError);
    } else {
      console.log(`✅ Found ${inactiveSeeds?.length || 0} inactive seed products`);
    }
    
  } catch (err) {
    console.error('❌ Exception in seed product retrieval:', err);
  }
}

async function testFilterCategories() {
  console.log('\n📂 Testing filter categories...\n');
  
  try {
    const { data: categories, error } = await supabase.rpc('get_filter_categories');
    
    if (error) {
      console.error('❌ Error getting filter categories:', error);
    } else {
      console.log(`✅ Found ${categories?.length || 0} filter categories:`);
      categories?.forEach(cat => {
        console.log(`   - ${cat.display_name} (${cat.name})`);
      });
    }
    
  } catch (err) {
    console.error('❌ Exception getting filter categories:', err);
  }
}

async function testFilterOptions() {
  console.log('\n🎛️ Testing filter options...\n');
  
  const testCategories = ['seed_type', 'flowering_time', 'effect'];
  
  for (const categoryName of testCategories) {
    try {
      const { data: options, error } = await supabase.rpc('get_filter_options_for_category', {
        p_category_name: categoryName
      });
      
      if (error) {
        console.error(`❌ Error getting options for ${categoryName}:`, error);
      } else {
        console.log(`✅ ${categoryName} has ${options?.length || 0} options:`);
        options?.forEach(opt => {
          console.log(`   - ${opt.display_name} (${opt.name})`);
        });
      }
      
    } catch (err) {
      console.error(`❌ Exception getting options for ${categoryName}:`, err);
    }
  }
}

async function runDatabaseCheck() {
  console.log('\n🔧 Checking database setup...\n');
  
  // Check if tables exist
  const tables = [
    'filter_categories',
    'filter_options', 
    'product_filters',
    'seed_product_attributes'
  ];
  
  for (const table of tables) {
    try {
      const { error } = await supabase.from(table).select('*').limit(1);
      
      if (error) {
        console.log(`❌ Table ${table}: ${error.message}`);
      } else {
        console.log(`✅ Table ${table}: OK`);
      }
      
    } catch (err) {
      console.log(`❌ Table ${table}: Exception - ${err}`);
    }
  }
}

async function runAllTests() {
  console.log('🌱 Seed Filtering System Test Suite\n');
  console.log('=====================================\n');
  
  await runDatabaseCheck();
  await testFilterCategories();
  await testFilterOptions();
  await testSeedIdentification();
  await testSeedProductRetrieval();
  
  console.log('\n🏁 Test suite completed!');
  console.log('\nIf you see errors above, run the setup script first:');
  console.log('npm run setup-seed-filtering');
}

// Run the tests
runAllTests().catch(console.error);
