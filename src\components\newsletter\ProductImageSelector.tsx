import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
// import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Search,
  Package,
  Plus,
  Loader2,
  Image as ImageIcon
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  sale_price?: number;
  image?: string;
  category?: string;
  brand?: string;
}

interface ProductImageSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProductSelect: (product: Product) => void;
}

const ProductImageSelector: React.FC<ProductImageSelectorProps> = ({
  open,
  onOpenChange,
  onProductSelect
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  // Load products when dialog opens
  useEffect(() => {
    if (open) {
      console.log('ProductImageSelector opened, loading products...');
      loadProducts();
    }
  }, [open]);

  // Filter products based on search term and category
  useEffect(() => {
    let filtered = products;

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory]);

  const loadProducts = async () => {
    try {
      setIsLoading(true);

      const { data: productsData, error } = await supabase
        .from('products')
        .select('id, name, price, sale_price, image')
        .eq('is_active', true)
        .not('image', 'is', null)
        .neq('image', '')
        .limit(20);

      if (error) {
        console.error('Error loading products:', error);
        console.error('Error details:', error.message, error.details);
        return;
      }

      console.log('Raw products data:', productsData);

      const formattedProducts: Product[] = productsData?.map(product => ({
        id: product.id,
        name: product.name,
        price: product.price,
        sale_price: product.sale_price,
        image: product.image,
        category: 'Product', // Simplified for now
        brand: undefined
      })) || [];

      console.log('Formatted products:', formattedProducts.length);
      setProducts(formattedProducts);

      // Set basic categories for now
      setCategories(['All Products']);

    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getFullImageUrl = (imagePath: string): string => {
    // Most images already have full URLs from the database
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    // Fallback for relative paths
    const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    const bucketName = 'product-images';
    return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${imagePath}`;
  };

  const handleProductSelect = (product: Product) => {
    onProductSelect(product);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Add Product Image
          </DialogTitle>
          <DialogDescription>
            Select a product to add its image to your newsletter.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Products</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by name, category, or brand..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Products Grid */}
          <div className="h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading products...</span>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="flex items-center justify-center h-32 text-gray-500">
                <ImageIcon className="h-6 w-6 mr-2" />
                No products with images found
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-2">
                {filteredProducts.map(product => (
                  <div
                    key={product.id}
                    className="border rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleProductSelect(product)}
                  >
                    {product.image && (
                      <img
                        src={getFullImageUrl(product.image)}
                        alt={product.name}
                        className="w-full h-32 object-cover rounded-md mb-2"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                        }}
                      />
                    )}
                    <h4 className="font-medium text-sm line-clamp-2 mb-1">
                      {product.name}
                    </h4>
                    <div className="flex items-center justify-between text-xs">
                      <span className="font-semibold text-green-600">
                        £{product.sale_price || product.price}
                        {product.sale_price && (
                          <span className="line-through text-gray-400 ml-1">
                            £{product.price}
                          </span>
                        )}
                      </span>
                      {product.category && (
                        <Badge variant="secondary" className="text-xs">
                          {product.category}
                        </Badge>
                      )}
                    </div>
                    <Button
                      size="sm"
                      className="w-full mt-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleProductSelect(product);
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Image
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProductImageSelector;
