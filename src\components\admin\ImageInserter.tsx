import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ImagePlus, Link, Type, Palette } from 'lucide-react';

interface ImageInserterProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInsert: (markdown: string) => void;
  insertionMode: 'append' | 'cursor';
}

const ImageInserter: React.FC<ImageInserterProps> = ({
  open,
  onOpenChange,
  onInsert,
  insertionMode
}) => {
  const [imageUrl, setImageUrl] = useState('');
  const [altText, setAltText] = useState('');
  const [caption, setCaption] = useState('');
  const [alignment, setAlignment] = useState<'left' | 'center' | 'right'>('center');
  const [size, setSize] = useState<'small' | 'medium' | 'large' | 'full'>('medium');
  const [style, setStyle] = useState<'basic' | 'rounded' | 'shadow' | 'border'>('basic');

  const handleInsert = () => {
    if (!imageUrl.trim()) return;

    let markdown = '';
    
    // Build the image markdown based on options
    const alt = altText || 'Newsletter Image';
    
    // Basic image markdown
    let imageMarkdown = `![${alt}](${imageUrl})`;
    
    // Add styling attributes if needed (for HTML templates)
    if (size !== 'medium' || alignment !== 'center' || style !== 'basic') {
      const styleAttrs = [];
      
      // Size styling
      switch (size) {
        case 'small':
          styleAttrs.push('width: 200px');
          break;
        case 'large':
          styleAttrs.push('width: 500px');
          break;
        case 'full':
          styleAttrs.push('width: 100%');
          break;
        default:
          styleAttrs.push('width: 300px');
      }
      
      // Alignment
      if (alignment === 'center') {
        styleAttrs.push('margin: 0 auto', 'display: block');
      } else if (alignment === 'right') {
        styleAttrs.push('float: right', 'margin-left: 20px');
      } else if (alignment === 'left') {
        styleAttrs.push('float: left', 'margin-right: 20px');
      }
      
      // Style effects
      switch (style) {
        case 'rounded':
          styleAttrs.push('border-radius: 12px');
          break;
        case 'shadow':
          styleAttrs.push('box-shadow: 0 4px 12px rgba(0,0,0,0.15)');
          break;
        case 'border':
          styleAttrs.push('border: 2px solid #e5e5e5');
          break;
      }
      
      // For HTML templates, we can add a style attribute
      imageMarkdown = `<img src="${imageUrl}" alt="${alt}" style="${styleAttrs.join('; ')}" />`;
    }
    
    // Build final markdown with spacing and caption
    markdown = '\n\n';
    
    if (alignment === 'center') {
      markdown += `<div style="text-align: center;">\n${imageMarkdown}\n`;
      if (caption) {
        markdown += `<p style="font-style: italic; color: #666; margin-top: 8px; font-size: 14px;">${caption}</p>\n`;
      }
      markdown += '</div>';
    } else {
      markdown += imageMarkdown;
      if (caption) {
        markdown += `\n<p style="font-style: italic; color: #666; margin-top: 8px; font-size: 14px;">${caption}</p>`;
      }
    }
    
    markdown += '\n\n';
    
    onInsert(markdown);
    
    // Reset form
    setImageUrl('');
    setAltText('');
    setCaption('');
    setAlignment('center');
    setSize('medium');
    setStyle('basic');
    
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ImagePlus className="h-5 w-5" />
            Insert Custom Image {insertionMode === 'cursor' ? '(At Cursor)' : '(At End)'}
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Image URL */}
          <div className="space-y-2">
            <Label htmlFor="image-url" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              Image URL
            </Label>
            <Input
              id="image-url"
              placeholder="https://example.com/image.jpg"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
            />
          </div>

          {/* Alt Text */}
          <div className="space-y-2">
            <Label htmlFor="alt-text" className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              Alt Text (for accessibility)
            </Label>
            <Input
              id="alt-text"
              placeholder="Describe the image"
              value={altText}
              onChange={(e) => setAltText(e.target.value)}
            />
          </div>

          {/* Caption */}
          <div className="space-y-2">
            <Label htmlFor="caption">Caption (optional)</Label>
            <Textarea
              id="caption"
              placeholder="Image caption or description"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              rows={2}
            />
          </div>

          {/* Layout Options */}
          <div className="grid grid-cols-2 gap-4">
            {/* Size */}
            <div className="space-y-2">
              <Label>Size</Label>
              <Select value={size} onValueChange={(value: any) => setSize(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Small (200px)</SelectItem>
                  <SelectItem value="medium">Medium (300px)</SelectItem>
                  <SelectItem value="large">Large (500px)</SelectItem>
                  <SelectItem value="full">Full Width</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Alignment */}
            <div className="space-y-2">
              <Label>Alignment</Label>
              <Select value={alignment} onValueChange={(value: any) => setAlignment(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="center">Center</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Style */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Style
            </Label>
            <Select value={style} onValueChange={(value: any) => setStyle(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="rounded">Rounded Corners</SelectItem>
                <SelectItem value="shadow">Drop Shadow</SelectItem>
                <SelectItem value="border">Border</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Preview */}
          {imageUrl && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <Label className="text-sm font-medium mb-2 block">Preview</Label>
              <div style={{ textAlign: alignment }}>
                <img
                  src={imageUrl}
                  alt={altText || 'Preview'}
                  style={{
                    width: size === 'small' ? '100px' : size === 'large' ? '200px' : size === 'full' ? '100%' : '150px',
                    borderRadius: style === 'rounded' ? '12px' : '0',
                    boxShadow: style === 'shadow' ? '0 4px 12px rgba(0,0,0,0.15)' : 'none',
                    border: style === 'border' ? '2px solid #e5e5e5' : 'none',
                    maxWidth: '100%',
                    height: 'auto'
                  }}
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
                {caption && (
                  <p style={{ fontStyle: 'italic', color: '#666', marginTop: '8px', fontSize: '12px' }}>
                    {caption}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleInsert} disabled={!imageUrl.trim()}>
            Insert Image
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageInserter;
