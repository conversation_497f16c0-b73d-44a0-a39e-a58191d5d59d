import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface PageTransitionProps {
  children: React.ReactNode;
}

const PageTransition: React.FC<PageTransitionProps> = ({ children }) => {
  const location = useLocation();
  const [displayChildren, setDisplayChildren] = useState(children);
  const [transitionStage, setTransitionStage] = useState('fadeIn');
  
  useEffect(() => {
    // When location changes, trigger the fade out
    if (children !== displayChildren) {
      setTransitionStage('fadeOut');
    }
  }, [children, displayChildren, location]);
  
  // After fade out completes, update children and fade back in
  const onAnimationEnd = () => {
    if (transitionStage === 'fadeOut') {
      setDisplayChildren(children);
      setTransitionStage('fadeIn');
    }
  };
  
  return (
    <div 
      className={`transition-opacity duration-300 ${transitionStage === 'fadeIn' ? 'opacity-100' : 'opacity-0'}`}
      onAnimationEnd={onAnimationEnd}
    >
      {displayChildren}
    </div>
  );
};

export default PageTransition;
