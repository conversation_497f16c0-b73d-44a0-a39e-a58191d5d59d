import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Loader2, RefreshCw } from 'lucide-react';
import { customSupabase } from '@/integrations/supabase/customClient';
import { toast } from '@/components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { ProductVariant, Product } from '@/types/database-with-variants';

// Using the imported customSupabase client

interface OptionDefinition {
  name: string;
  values: string[];
}

interface VariantPreview {
  option_combination: Record<string, string>;
  price: number;
  price_adjustment: number; // UI-only field, not stored in database
  stock_quantity: number;
  in_stock: boolean;
  is_active: boolean;
}

interface BulkVariantGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  basePrice: number;
  onSuccess: () => void;
}

export function BulkVariantGenerator({
  isOpen,
  onClose,
  productId,
  productName,
  basePrice,
  onSuccess
}: BulkVariantGeneratorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [optionDefinitions, setOptionDefinitions] = useState<OptionDefinition[]>([]);
  const [variantPreviews, setVariantPreviews] = useState<VariantPreview[]>([]);
  const [globalSettings, setGlobalSettings] = useState({
    price: basePrice,
    price_adjustment: 0,
    stock_quantity: 10,
    in_stock: true,
    is_active: true
  });

  // Load option definitions and generate variant previews
  useEffect(() => {
    if (isOpen) {
      console.log('BulkVariantGenerator opened for product ID:', productId);
      fetchOptionDefinitions();
    }
  }, [isOpen, productId]);

  const fetchOptionDefinitions = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await customSupabase
        .from('products')
        .select('option_definitions')
        .eq('id', productId)
        .single();

      if (error) {
        throw error;
      }

      console.log('Fetched option_definitions:', data?.option_definitions);

      if (data && data.option_definitions && Object.keys(data.option_definitions).length > 0) {
        // Convert from object to array format
        const optionsArray = Object.entries(data.option_definitions).map(
          ([name, values]) => ({
            name,
            values: values as string[],
          })
        );
        console.log('Converted to optionsArray:', optionsArray);
        setOptionDefinitions(optionsArray);

        // Generate variant previews
        generateVariantPreviews(optionsArray);
      } else {
        console.log('No option definitions found or empty object');
        setOptionDefinitions([]);
        setVariantPreviews([]);
      }
    } catch (error) {
      console.error('Error fetching option definitions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product options',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Generate all possible variant combinations from options
  const generateVariantPreviews = (options: OptionDefinition[]) => {
    // Filter out empty options
    const validOptions = options.filter(
      option => option.name && option.values.length > 0
    );

    if (validOptions.length === 0) {
      setVariantPreviews([]);
      return;
    }

    // Generate all combinations
    const generateCombinations = (
      optionIndex: number,
      currentCombination: Record<string, string>
    ): Record<string, string>[] => {
      if (optionIndex >= validOptions.length) {
        return [currentCombination];
      }

      const option = validOptions[optionIndex];
      const combinations: Record<string, string>[] = [];

      // For each value of the current option
      option.values.forEach(value => {
        if (value.trim() === '') return; // Skip empty values

        // Create a new combination with this value
        const newCombination = {
          ...currentCombination,
          [option.name]: value
        };

        // Recursively generate combinations for the next option
        const nextCombinations = generateCombinations(
          optionIndex + 1,
          newCombination
        );

        combinations.push(...nextCombinations);
      });

      return combinations;
    };

    // Start generating combinations from the first option
    const combinations = generateCombinations(0, {});

    // Create variant previews from combinations
    const previews = combinations.map(combination => ({
      option_combination: combination,
      price: globalSettings.price,
      price_adjustment: globalSettings.price_adjustment,
      stock_quantity: globalSettings.stock_quantity,
      in_stock: globalSettings.in_stock,
      is_active: globalSettings.is_active
    }));

    setVariantPreviews(previews);
  };

  // Update all variant previews with global settings
  const updateAllVariants = (field: keyof typeof globalSettings, value: any) => {
    // Update global settings
    setGlobalSettings({
      ...globalSettings,
      [field]: value
    });

    // Update all variant previews
    setVariantPreviews(previews =>
      previews.map(preview => ({
        ...preview,
        [field]: value
      }))
    );
  };

  // Update a specific variant preview
  const updateVariantPreview = (index: number, field: string, value: any) => {
    const updatedPreviews = [...variantPreviews];
    updatedPreviews[index] = {
      ...updatedPreviews[index],
      [field]: value
    };
    setVariantPreviews(updatedPreviews);
  };

  // Format option combination for display
  const formatOptionCombination = (optionCombination: Record<string, string>) => {
    return Object.entries(optionCombination)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
  };

  // Generate variant name from option combination
  const generateVariantName = (optionCombination: Record<string, string>) => {
    return Object.values(optionCombination).join(' / ');
  };

  // Handle form submission to create all variants
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Check if we have variants to create
      if (variantPreviews.length === 0) {
        toast({
          title: 'No Variants',
          description: 'There are no variants to create. Add options to the product first.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Prepare variants data for insertion
      const variantsToCreate = variantPreviews.map(preview => {
        // Calculate the final price directly to ensure it's correct
        const finalPrice = basePrice + (preview.price_adjustment || 0);

        // Create the variant object without price_adjustment field
        // since it doesn't exist in the database schema
        return {
          product_id: productId,
          variant_name: generateVariantName(preview.option_combination),
          sku: null, // Can be updated individually later
          price: finalPrice, // Use the calculated final price
          sale_price: null,
          stock_quantity: preview.stock_quantity,
          in_stock: preview.in_stock,
          image: null, // Can be updated individually later
          option_combination: preview.option_combination, // This is already a JavaScript object
          is_active: preview.is_active,
          // Add required fields for new variants
          external_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      });

      // Log the data we're about to send
      console.log('Variants to create:', JSON.stringify(variantsToCreate, null, 2));

      try {
        // Insert all variants
        const { data, error } = await customSupabase
          .from('product_variants')
          .insert(variantsToCreate);

        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }

        console.log('Variants created successfully:', data);
      } catch (insertError) {
        console.error('Error inserting variants:', insertError);
        throw insertError;
      }

      toast({
        title: 'Success',
        description: `Created ${variantsToCreate.length} variants successfully`,
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating variants:', error);
      toast({
        title: 'Error',
        description: 'Failed to create variants',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <div>
              <DialogTitle>Generate Variants for {productName}</DialogTitle>
              <DialogDescription>
                Automatically create variants based on product options.
              </DialogDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                fetchOptionDefinitions();
              }}
              disabled={isLoading}
              title="Refresh option definitions from database"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="ml-2">Refresh</span>
            </Button>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {optionDefinitions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No options defined for this product.</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Add options in the product settings first to generate variants.
                </p>
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h3 className="text-sm font-medium text-yellow-800">Troubleshooting</h3>
                  <ol className="text-sm text-yellow-700 mt-2 ml-4 list-decimal">
                    <li className="mb-1">Make sure you've added option types (e.g., Size, Color)</li>
                    <li className="mb-1">Make sure you've added values to each option</li>
                    <li className="mb-1">Click the "Save Options" button in the Product Options section</li>
                    <li className="mb-1">Then click "Refresh" above to reload the options</li>
                  </ol>
                </div>
              </div>
            ) : (
              <>
                {/* Global Settings */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Default Settings for All Variants</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="base-price-display">Base Product Price</Label>
                        <input
                          id="base-price-display"
                          type="text"
                          value={`£${basePrice.toFixed(2)}`}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                        />
                      </div>

                      <div>
                        <Label htmlFor="global-price-adjustment">Price Adjustment (£)</Label>
                        <p className="text-xs text-gray-500">Amount to add to base price</p>
                        <div className="relative">
                          <input
                            id="global-price-adjustment"
                            type="number"
                            step="0.01"
                            value={globalSettings.price_adjustment || 0}
                            onChange={(e) => {
                              const value = e.target.value;
                              const adjustment = value === '' ? 0 : Number(value);
                              const finalPrice = basePrice + adjustment;

                              // First update the global settings
                              setGlobalSettings({
                                ...globalSettings,
                                price: finalPrice
                              });

                              // Then update price_adjustment in a separate update
                              setTimeout(() => {
                                setGlobalSettings({
                                  ...globalSettings,
                                  price_adjustment: adjustment,
                                  price: finalPrice
                                });

                                // Update all variants
                                const updatedPreviews = variantPreviews.map(preview => ({
                                  ...preview,
                                  price_adjustment: adjustment,
                                  price: finalPrice
                                }));
                                setVariantPreviews(updatedPreviews);
                              }, 0);
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                      </div>

                      <div className="mt-4">
                        <Label htmlFor="global-final-price">Final Price (£)</Label>
                        <div className="relative">
                          <input
                            id="global-final-price"
                            type="text"
                            value={`£${(basePrice + (globalSettings.price_adjustment || 0)).toFixed(2)}`}
                            readOnly
                            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                          />
                        </div>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="global-stock">Stock Quantity</Label>
                      <input
                        id="global-stock"
                        type="number"
                        value={globalSettings.stock_quantity}
                        onChange={(e) => updateAllVariants('stock_quantity', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="global-in-stock"
                        checked={globalSettings.in_stock}
                        onCheckedChange={(checked) => updateAllVariants('in_stock', checked)}
                      />
                      <Label htmlFor="global-in-stock">In Stock</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="global-is-active"
                        checked={globalSettings.is_active}
                        onCheckedChange={(checked) => updateAllVariants('is_active', checked)}
                      />
                      <Label htmlFor="global-is-active">Active</Label>
                    </div>
                  </div>
                </div>

                {/* Variant Previews */}
                {variantPreviews.length > 0 ? (
                  <div className="max-h-[400px] overflow-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Options</TableHead>
                          <TableHead className="text-right">Price Adjustment (£)</TableHead>
                          <TableHead className="text-right">Final Price (£)</TableHead>
                          <TableHead className="text-center">Stock</TableHead>
                          <TableHead className="text-center">In Stock</TableHead>
                          <TableHead className="text-center">Active</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {variantPreviews.map((preview, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {formatOptionCombination(preview.option_combination)}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="relative w-24">
                                <input
                                  type="number"
                                  step="0.01"
                                  value={preview.price_adjustment || 0}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    const adjustment = value === '' ? 0 : Number(value);
                                    const finalPrice = basePrice + adjustment;

                                    // First update price
                                    const updatedPreviews = [...variantPreviews];
                                    updatedPreviews[index] = {
                                      ...updatedPreviews[index],
                                      price: finalPrice
                                    };
                                    setVariantPreviews(updatedPreviews);

                                    // Then update price_adjustment in a separate update
                                    setTimeout(() => {
                                      const updatedPreviews = [...variantPreviews];
                                      updatedPreviews[index] = {
                                        ...updatedPreviews[index],
                                        price_adjustment: adjustment
                                      };
                                      setVariantPreviews(updatedPreviews);
                                    }, 0);
                                  }}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-right"
                                />
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end">
                                <span className="mr-1">£</span>
                                <input
                                  type="text"
                                  value={(basePrice + (preview.price_adjustment || 0)).toFixed(2)}
                                  readOnly
                                  className="w-20 text-right bg-gray-50 px-3 py-2 border border-gray-300 rounded-md"
                                />
                              </div>
                            </TableCell>
                            <TableCell className="text-center">
                              <input
                                type="number"
                                value={preview.stock_quantity}
                                onChange={(e) => updateVariantPreview(index, 'stock_quantity', parseInt(e.target.value))}
                                className="w-16 text-center px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Switch
                                checked={preview.in_stock}
                                onCheckedChange={(checked) => updateVariantPreview(index, 'in_stock', checked)}
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Switch
                                checked={preview.is_active}
                                onCheckedChange={(checked) => updateVariantPreview(index, 'is_active', checked)}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">
                      No valid option combinations found. Please add values to your options.
                    </p>
                  </div>
                )}
              </>
            )}

            <DialogFooter>
              <div className="flex justify-between w-full">
                <div>
                  <span className="text-sm text-muted-foreground">
                    {variantPreviews.length} variants will be created
                  </span>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" type="button" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || variantPreviews.length === 0}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Variants...
                      </>
                    ) : (
                      `Create ${variantPreviews.length} Variants`
                    )}
                  </Button>
                </div>
              </div>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
