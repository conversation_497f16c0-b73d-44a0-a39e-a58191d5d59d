const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input files
const productsFile = 'transformed_products.csv';
const variantsFile = 'transformed_variants_final.csv';
const outputFile = 'test_variants.csv';

// Store product IDs
const productIds = new Set();
const variants = [];

// CSV writer
const csvWriter = createObjectCsvWriter({
  path: outputFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Read products file
console.log(`Reading products from ${productsFile}...`);
fs.createReadStream(productsFile)
  .pipe(csv())
  .on('data', (row) => {
    productIds.add(row.id);
  })
  .on('end', () => {
    console.log(`Found ${productIds.size} products`);
    
    // Read variants file
    console.log(`Reading variants from ${variantsFile}...`);
    fs.createReadStream(variantsFile)
      .pipe(csv())
      .on('data', (row) => {
        variants.push(row);
      })
      .on('end', async () => {
        console.log(`Read ${variants.length} variants from CSV`);
        
        // Select a few variants for testing
        const testVariants = variants.slice(0, 5);
        
        // Write test variants to CSV
        console.log(`Writing ${testVariants.length} test variants to ${outputFile}...`);
        await csvWriter.writeRecords(testVariants);
        console.log(`Created test variants file: ${outputFile}`);
        
        // Print the test variants
        console.log('\nTest variants:');
        testVariants.forEach(variant => {
          console.log(`- id: ${variant.id}, product_id: ${variant.product_id}, variant_name: ${variant.variant_name}, price: ${variant.price}`);
        });
      });
  });
