// <PERSON><PERSON>t to force refresh the shipping methods cache
// Run this script after making changes to shipping methods in the admin panel

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function refreshShippingCache() {
  console.log('Refreshing shipping methods cache...');
  
  try {
    // Clear browser cache by updating a timestamp in the settings table
    const timestamp = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('settings')
      .upsert({ 
        key: 'shipping_cache_timestamp', 
        value: timestamp,
        updated_at: timestamp
      }, { 
        onConflict: 'key' 
      });
      
    if (error) {
      throw error;
    }
    
    console.log('✅ Shipping cache timestamp updated to:', timestamp);
    console.log('✅ Shipping methods cache has been refreshed');
    console.log('\nThe next time users load the checkout page, they will see the updated shipping methods.');
    
  } catch (error) {
    console.error('❌ Error refreshing shipping cache:', error.message);
    process.exit(1);
  }
}

// Run the function
refreshShippingCache();
