import React, { useState, useEffect } from 'react';
import { useEPOSIntegration } from '@/hooks/useEPOSIntegration';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, RefreshCw, Upload, Download, Tag, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import StockSyncManager from './StockSyncManager';

interface EPOSIntegrationProps {
  // Optional props if needed
}

export function EPOSIntegration({}: EPOSIntegrationProps) {
  const {
    isSyncing,
    unmappedProducts,
    isLoadingUnmapped,
    syncWithEPOS,
    mapProductsToEPOS,
    bulkUpdateSKUs
  } = useEPOSIntegration();

  const [skuUpdates, setSkuUpdates] = useState<{ id: string; sku: string }[]>([]);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [fileData, setFileData] = useState<any[]>([]);

  // Initialize SKU updates from unmapped products
  useEffect(() => {
    if (unmappedProducts) {
      setSkuUpdates(
        unmappedProducts.map(product => ({
          id: product.id,
          sku: product.sku || ''
        }))
      );
    }
  }, [unmappedProducts]);

  // Handle SKU input change
  const handleSkuChange = (id: string, value: string) => {
    setSkuUpdates(prev =>
      prev.map(item =>
        item.id === id ? { ...item, sku: value } : item
      )
    );
  };

  // Handle saving SKUs
  const handleSaveSKUs = () => {
    // Filter out empty SKUs
    const validUpdates = skuUpdates.filter(update => update.sku.trim() !== '');
    if (validUpdates.length > 0) {
      bulkUpdateSKUs(validUpdates);
    }
  };

  // Handle file upload for SKU mapping
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  // Process uploaded CSV/Excel file
  const processFile = async () => {
    if (!uploadFile) return;

    setIsProcessingFile(true);
    try {
      // This is a placeholder - in a real implementation, you would:
      // 1. Parse the CSV/Excel file
      // 2. Extract product SKUs and IDs
      // 3. Map them to your products

      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Sample data structure
      const sampleData = [
        { productName: 'Sample Product 1', sku: 'SKU001', eposId: 'EPOS001' },
        { productName: 'Sample Product 2', sku: 'SKU002', eposId: 'EPOS002' },
      ];

      setFileData(sampleData);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      setIsProcessingFile(false);
    }
  };

  // Apply mappings from file
  const applyMappings = () => {
    // This would map the processed file data to your products
    // For now, it's just a placeholder
    alert('This would apply the mappings from the uploaded file');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>EPOS Integration</CardTitle>
        <CardDescription>
          Manage integration with your Electronic Point of Sale system
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="sync">
          <TabsList className="mb-4">
            <TabsTrigger value="sync">Synchronization</TabsTrigger>
            <TabsTrigger value="stock">Stock Sync</TabsTrigger>
            <TabsTrigger value="sku">SKU Management</TabsTrigger>
            <TabsTrigger value="import">Import/Export</TabsTrigger>
          </TabsList>

          <TabsContent value="sync" className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-md bg-muted/20">
              <div>
                <h3 className="text-lg font-medium">Product Synchronization</h3>
                <p className="text-sm text-muted-foreground">
                  Sync product inventory and prices with your EPOS system
                </p>
              </div>
              <Button
                onClick={() => syncWithEPOS()}
                disabled={isSyncing}
              >
                {isSyncing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Sync Now
                  </>
                )}
              </Button>
            </div>

            <div className="p-4 border rounded-md bg-amber-50 dark:bg-amber-950/20">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium text-amber-800 dark:text-amber-400">Integration Setup</h3>
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    To complete the EPOS integration, you'll need to:
                  </p>
                  <ol className="list-decimal list-inside text-sm text-amber-700 dark:text-amber-300 mt-2 space-y-1">
                    <li>Ensure all products have unique SKUs</li>
                    <li>Configure your EPOS API credentials in the environment settings</li>
                    <li>Perform an initial sync to map products between systems</li>
                  </ol>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="stock" className="space-y-4">
            <StockSyncManager />
          </TabsContent>

          <TabsContent value="sku" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Products Missing SKUs</h3>
              <Button onClick={handleSaveSKUs} variant="outline">
                <Tag className="mr-2 h-4 w-4" />
                Save SKUs
              </Button>
            </div>

            {isLoadingUnmapped ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : unmappedProducts && unmappedProducts.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>Current SKU</TableHead>
                      <TableHead>New SKU</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {unmappedProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>{product.name}</TableCell>
                        <TableCell>
                          {product.sku ? (
                            product.sku
                          ) : (
                            <Badge variant="outline" className="text-amber-600 bg-amber-50">
                              Missing
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Input
                            value={skuUpdates.find(u => u.id === product.id)?.sku || ''}
                            onChange={(e) => handleSkuChange(product.id, e.target.value)}
                            placeholder="Enter SKU"
                            className="max-w-[200px]"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                All products have SKUs assigned. Great job!
              </div>
            )}
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <div className="grid gap-4">
              <div className="p-4 border rounded-md">
                <h3 className="text-lg font-medium mb-2">Import SKUs from EPOS</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Upload a CSV or Excel file with product SKUs exported from your EPOS system
                </p>

                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="skuFile">Select File</Label>
                    <Input
                      id="skuFile"
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileChange}
                      disabled={isProcessingFile}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={processFile}
                      disabled={!uploadFile || isProcessingFile}
                    >
                      {isProcessingFile ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Upload className="mr-2 h-4 w-4" />
                          Process File
                        </>
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      onClick={applyMappings}
                      disabled={fileData.length === 0}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Apply Mappings
                    </Button>
                  </div>
                </div>

                {fileData.length > 0 && (
                  <div className="mt-4 border rounded-md overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Product Name</TableHead>
                          <TableHead>SKU</TableHead>
                          <TableHead>EPOS ID</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {fileData.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell>{item.productName}</TableCell>
                            <TableCell>{item.sku}</TableCell>
                            <TableCell>{item.eposId}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}