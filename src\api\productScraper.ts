/**
 * Product Scraper Utility
 * 
 * This module provides functions to search for and scrape product information
 * from various e-commerce sites to gather accurate product descriptions.
 */

import { load } from 'cheerio';

/**
 * Search for product information using Google Search
 * @param productName The product name to search for
 * @param category Optional category to refine the search
 * @returns Promise resolving to an array of potential product URLs
 */
export async function searchForProductPages(productName: string, category?: string): Promise<string[]> {
  try {
    console.log(`Searching for product pages: ${productName} ${category || ''}`);
    
    // Check if API keys are available
    const apiKey = import.meta.env.VITE_GOOGLE_API_KEY;
    const cxId = import.meta.env.VITE_GOOGLE_CX_ID;
    
    if (!apiKey || !cxId) {
      console.info('Missing Google API key or Custom Search Engine ID');
      console.log('VITE_GOOGLE_API_KEY available:', !!apiKey);
      console.log('VITE_GOOGLE_CX_ID available:', !!cxId);
      return [];
    }
    
    // Create search query
    const searchQuery = encodeURIComponent(`${productName} ${category || ''} product description`);
    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${cxId}&q=${searchQuery}`;
    
    console.log(`Making search request to: ${searchUrl.replace(apiKey, '[REDACTED]')}`);
    
    // Use Google Search to find relevant product pages
    const response = await fetch(searchUrl);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.info(`Google search failed (${response.status}): ${errorText}`);
      return [];
    }
    
    const data = await response.json();
    console.log(`Search results received. Total results: ${data.searchInformation?.totalResults || 'unknown'}`);
    
    // Extract URLs from search results
    if (!data.items || !Array.isArray(data.items)) {
      console.log('No search items found in response');
      return [];
    }
    
    const urls = data.items.map((item: any) => item.link) || [];
    console.log(`Found ${urls.length} URLs from search results`);
    
    // Filter out unwanted domains (social media, etc.)
    const filteredUrls = urls.filter((url: string) => {
      try {
        const domain = new URL(url).hostname.toLowerCase();
        // Exclude social media, video platforms, etc.
        return !domain.includes('facebook.com') && 
               !domain.includes('twitter.com') && 
               !domain.includes('instagram.com') && 
               !domain.includes('youtube.com') && 
               !domain.includes('tiktok.com');
      } catch (e) {
        return false;
      }
    });
    
    console.log(`Filtered to ${filteredUrls.length} relevant URLs`);
    const resultUrls = filteredUrls.slice(0, 5); // Return top 5 results
    console.log('Final URLs to scrape:', resultUrls);
    
    return resultUrls;
  } catch (error) {
    console.info('Error searching for product pages');
    return [];
  }
}

/**
 * Create a completely silent fetch function that suppresses all console errors
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise resolving to a Response object
 */
async function silentFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Use a more robust approach to suppress all console errors
  // We'll use an iframe to isolate the fetch request and prevent errors from bubbling up
  return new Promise<Response>((resolve) => {
    // Save original console methods
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    const originalConsoleLog = console.log;
    
    // Create no-op functions
    const noOp = () => {};
    
    try {
      // Replace all console methods with no-op functions
      console.error = noOp;
      console.warn = noOp;
      
      // Use a timeout to ensure the fetch completes or fails silently
      const timeoutId = setTimeout(() => {
        // Restore console methods
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
        console.log = originalConsoleLog;
        
        // Resolve with a fake response if timeout occurs
        resolve(new Response('', { status: 404, statusText: 'Timeout' }));
      }, 15000); // 15 second timeout
      
      // Make the fetch request inside a try-catch to catch any synchronous errors
      try {
        fetch(url, options)
          .then(response => {
            // Clear timeout and restore console methods
            clearTimeout(timeoutId);
            console.error = originalConsoleError;
            console.warn = originalConsoleWarn;
            console.log = originalConsoleLog;
            
            // Resolve with the actual response
            resolve(response);
          })
          .catch(() => {
            // Clear timeout and restore console methods
            clearTimeout(timeoutId);
            console.error = originalConsoleError;
            console.warn = originalConsoleWarn;
            console.log = originalConsoleLog;
            
            // Resolve with a fake response for any network error
            resolve(new Response('', { status: 404, statusText: 'Silent Error' }));
          });
      } catch (syncError) {
        // Clear timeout and restore console methods
        clearTimeout(timeoutId);
        console.error = originalConsoleError;
        console.warn = originalConsoleWarn;
        console.log = originalConsoleLog;
        
        // Resolve with a fake response for any synchronous error
        resolve(new Response('', { status: 404, statusText: 'Silent Error' }));
      }
    } catch (e) {
      // Restore console methods
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      console.log = originalConsoleLog;
      
      // Resolve with a fake response for any other error
      resolve(new Response('', { status: 404, statusText: 'Silent Error' }));
    }
  });
}

/**
 * Scrapes a product description from a given URL
 * Uses multiple serverless proxy approaches to avoid CORS issues
 * @param url The URL to scrape
 * @returns Promise resolving to the scraped product description or null if not found
 */
export async function scrapeProductDescription(url: string): Promise<string | null> {
  try {
    console.log(`Attempting to scrape product description from: ${url}`);
    
    // Try multiple proxy services in sequence until one works
    const proxyServices = [
      // First try a direct fetch with no-cors mode
      async () => {
        try {
          console.log('Trying direct fetch with no-cors mode');
          const response = await fetch(url, {
            mode: 'no-cors',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          // If we get here without error, we might have a response, but it's opaque
          // We can't actually read the content with no-cors mode, so this is just a test
          console.log('Direct fetch succeeded but content is opaque');
          throw new Error('Direct fetch with no-cors mode succeeded but content is opaque');
        } catch (error) {
          console.log('Direct fetch failed, trying proxies');
          throw error;
        }
      },
      
      // Try cors-anywhere proxy
      async () => {
        const corsAnywhereUrl = `https://cors-anywhere.herokuapp.com/${url}`;
        console.log(`Trying cors-anywhere proxy: ${corsAnywhereUrl}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        try {
          const response = await fetch(corsAnywhereUrl, {
            signal: controller.signal,
            headers: {
              'Origin': window.location.origin,
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`cors-anywhere proxy failed with status ${response.status}`);
          }
          
          const html = await response.text();
          console.log(`Successfully fetched HTML via cors-anywhere (${html.length} bytes)`);
          return html;
        } catch (error) {
          clearTimeout(timeoutId);
          console.warn('cors-anywhere proxy failed:', error);
          throw error;
        }
      },
      
      // Try allorigins proxy
      async () => {
        const allOriginsUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
        console.log(`Trying allorigins proxy: ${allOriginsUrl}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        try {
          const response = await fetch(allOriginsUrl, {
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`allorigins proxy failed with status ${response.status}`);
          }
          
          const html = await response.text();
          console.log(`Successfully fetched HTML via allorigins (${html.length} bytes)`);
          return html;
        } catch (error) {
          clearTimeout(timeoutId);
          console.warn('allorigins proxy failed:', error);
          throw error;
        }
      },
      
      // Try corsproxy.io
      async () => {
        const corsProxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;
        console.log(`Trying corsproxy.io: ${corsProxyUrl}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        try {
          const response = await fetch(corsProxyUrl, {
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`corsproxy.io failed with status ${response.status}`);
          }
          
          const html = await response.text();
          console.log(`Successfully fetched HTML via corsproxy.io (${html.length} bytes)`);
          return html;
        } catch (error) {
          clearTimeout(timeoutId);
          console.warn('corsproxy.io failed:', error);
          throw error;
        }
      },
      
      // Try thingproxy
      async () => {
        const thingProxyUrl = `https://thingproxy.freeboard.io/fetch/${url}`;
        console.log(`Trying thingproxy: ${thingProxyUrl}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        try {
          const response = await fetch(thingProxyUrl, {
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`thingproxy failed with status ${response.status}`);
          }
          
          const html = await response.text();
          console.log(`Successfully fetched HTML via thingproxy (${html.length} bytes)`);
          return html;
        } catch (error) {
          clearTimeout(timeoutId);
          console.warn('thingproxy failed:', error);
          throw error;
        }
      }
    ];
    
    // Try each proxy service in sequence until one works
    let html = null;
    for (const proxyService of proxyServices) {
      try {
        html = await proxyService();
        if (html) break;
      } catch (error) {
        // Continue to the next proxy service
        console.log('Moving to next proxy service');
      }
    }
    
    // If all proxies failed, return null
    if (!html) {
      console.warn('All proxy services failed');
      return null;
    }
    
    // Parse the HTML with cheerio
    console.log('Parsing HTML content with cheerio');
    const $ = load(html);
    
    // Try 20 common product description selectors
    console.log('Trying common product description selectors');
    const selectors = [
      '.product-description', '#product-description',
      '.description', '#description',
      '.product-details', '#product-details',
      '.product-info', '#product-info',
      '.product-content', '#product-content',
      '[itemprop="description"]',
      '.details', '#details',
      '.specs', '#specs',
      '.features', '#features',
      '.overview', '#overview',
      '.product-overview', '#product-overview',
      '.product-features', '#product-features',
      '.product-specs', '#product-specs'
    ];
    
    for (const selector of selectors) {
      const element = $(selector);
      if (element.length > 0) {
        const text = element.text().trim();
        if (text.length > 50) { // Ensure it's substantial
          console.log(`Found description with selector: ${selector} (${text.length} chars)`);
          return text;
        }
      }
    }
    
    console.log('No description found with common selectors, trying alternative methods');
    
    // Try to find the product description based on page structure
    // First, look for the product title
    const titleSelectors = [
      'h1.product-title', 'h1.title', 'h1.product-name', 
      '.product-title', '#product-title',
      '[itemprop="name"]', '.product-name', '#product-name'
    ];
    
    let productTitle = null;
    for (const selector of titleSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        productTitle = element;
        break;
      }
    }
    
    // If we found the title, look for nearby paragraphs
    if (productTitle) {
      const paragraphs = productTitle.parent().find('p');
      if (paragraphs.length > 0) {
        const paragraphTexts = paragraphs.map((i, el) => $(el).text().trim()).get();
        const filteredTexts = paragraphTexts.filter(text => text.length > 50);
        
        if (filteredTexts.length > 0) {
          const combinedText = filteredTexts.join('\n\n');
          console.log(`Found description from title-adjacent paragraphs (${combinedText.length} chars)`);
          return combinedText;
        }
      }
    }
    
    // Last resort: look for any substantial paragraph on the page
    const allParagraphs = $('p');
    console.log(`Searching through ${allParagraphs.length} paragraphs on the page`);
    
    if (allParagraphs.length > 0) {
      const paragraphTexts = allParagraphs.map((i, el) => $(el).text().trim()).get();
      const longParagraphs = paragraphTexts.filter(text => text.length > 100);
      
      console.log(`Found ${longParagraphs.length} substantial paragraphs (>100 chars)`);
      
      if (longParagraphs.length > 0) {
        const result = longParagraphs.slice(0, 3).join('\n\n');
        console.log(`Using ${Math.min(3, longParagraphs.length)} longest paragraphs as description (${result.length} chars)`);
        return result;
      }
    }
    
    // If we get here, we couldn't find a description
    console.log('No suitable product description found on page');
    return null;
  } catch (error) {
    console.info('Error scraping product description:', error);
    return null;
  }
}

/**
 * Get product descriptions from multiple sources
 * @param productName The product name to search for
 * @param category Optional category to refine the search
 * @returns Promise resolving to an array of scraped descriptions
 */
export async function getProductDescriptions(productName: string, category?: string): Promise<string[]> {
  try {
    // Find relevant product pages
    const urls = await searchForProductPages(productName, category);
    
    if (urls.length === 0) {
      return [];
    }
    
    const descriptionPromises = urls.map(url => scrapeProductDescription(url));
    const descriptions = await Promise.all(descriptionPromises);
    
    // Filter out null results and empty strings
    return descriptions.filter(desc => desc !== null && desc.trim() !== '') as string[];
  } catch (error) {
    console.info('Error getting product descriptions');
    return [];
  }
}
