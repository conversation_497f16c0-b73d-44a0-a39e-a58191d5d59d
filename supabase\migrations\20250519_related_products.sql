-- Create related_products table
CREATE TABLE IF NOT EXISTS related_products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  related_product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(product_id, related_product_id)
);

-- Add RLS policies for related_products
ALTER TABLE related_products ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view related products
CREATE POLICY "Authenticated users can view related products" ON related_products
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert related products
CREATE POLICY "Authenticated users can insert related products" ON related_products
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update their own related products
CREATE POLICY "Authenticated users can update related products" ON related_products
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Allow authenticated users to delete related products
CREATE POLICY "Authenticated users can delete related products" ON related_products
  FOR DELETE USING (auth.role() = 'authenticated');

-- Create function to update product relationships
CREATE OR REPLACE FUNCTION update_product_relationships(
  p_product_id UUID,
  p_related_product_ids UUID[]
)
RETURNS VOID AS $$
BEGIN
  -- Delete existing relationships for this product
  DELETE FROM related_products WHERE product_id = p_product_id;
  
  -- Insert new relationships with sort order
  FOR i IN 1..array_length(p_related_product_ids, 1) LOOP
    INSERT INTO related_products (
      product_id,
      related_product_id,
      sort_order
    ) VALUES (
      p_product_id,
      p_related_product_ids[i],
      i
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to get related products with fallback to random products
CREATE OR REPLACE FUNCTION get_related_products(
  p_product_id UUID,
  p_limit INTEGER DEFAULT 4
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  slug TEXT,
  description TEXT,
  price NUMERIC,
  sale_price NUMERIC,
  image TEXT,
  category_id UUID,
  is_active BOOLEAN,
  in_stock BOOLEAN
) AS $$
DECLARE
  v_category_id UUID;
  v_related_count INTEGER;
BEGIN
  -- Get the category of the current product
  SELECT category_id INTO v_category_id FROM products WHERE id = p_product_id;
  
  -- First, try to get user-selected related products
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.slug,
    p.description,
    p.price,
    p.sale_price,
    p.image,
    p.category_id,
    p.is_active,
    p.in_stock
  FROM related_products rp
  JOIN products p ON rp.related_product_id = p.id
  WHERE rp.product_id = p_product_id
    AND p.is_active = TRUE
    AND p.image IS NOT NULL
  ORDER BY rp.sort_order
  LIMIT p_limit;
  
  -- Check how many related products we got
  GET DIAGNOSTICS v_related_count = ROW_COUNT;
  
  -- If we didn't get enough related products, add random products from the same category
  IF v_related_count < p_limit THEN
    RETURN QUERY
    SELECT 
      p.id,
      p.name,
      p.slug,
      p.description,
      p.price,
      p.sale_price,
      p.image,
      p.category_id,
      p.is_active,
      p.in_stock
    FROM products p
    WHERE p.id != p_product_id
      AND p.category_id = v_category_id
      AND p.is_active = TRUE
      AND p.image IS NOT NULL
      AND NOT EXISTS (
        SELECT 1 FROM related_products rp 
        WHERE rp.product_id = p_product_id AND rp.related_product_id = p.id
      )
    ORDER BY RANDOM()
    LIMIT (p_limit - v_related_count);
  END IF;
  
  -- If we still don't have enough products, get random products from any category
  GET DIAGNOSTICS v_related_count = ROW_COUNT;
  
  IF v_related_count < p_limit THEN
    RETURN QUERY
    SELECT 
      p.id,
      p.name,
      p.slug,
      p.description,
      p.price,
      p.sale_price,
      p.image,
      p.category_id,
      p.is_active,
      p.in_stock
    FROM products p
    WHERE p.id != p_product_id
      AND p.is_active = TRUE
      AND p.image IS NOT NULL
      AND NOT EXISTS (
        SELECT 1 FROM related_products rp 
        WHERE rp.product_id = p_product_id AND rp.related_product_id = p.id
      )
    ORDER BY RANDOM()
    LIMIT (p_limit - v_related_count);
  END IF;
END;
$$ LANGUAGE plpgsql;
