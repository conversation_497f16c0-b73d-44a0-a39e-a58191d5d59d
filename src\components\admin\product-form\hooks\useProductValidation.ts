
import { Product } from "@/types/database";
import { toast } from "@/components/ui/use-toast";

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function useProductValidation() {
  const validateForm = (formData: Partial<Product>): ValidationResult => {
    const errors: string[] = [];
    
    // Check required fields
    if (!formData.name?.trim()) {
      errors.push("Product name is required");
    }
    
    if (!formData.price || formData.price <= 0) {
      errors.push("Please enter a valid price");
    }
    
    if (formData.sale_price !== null && formData.sale_price !== undefined) {
      if (formData.sale_price < 0) {
        errors.push("Sale price cannot be negative");
      }
      
      if (formData.sale_price >= formData.price!) {
        errors.push("Sale price must be less than regular price");
      }
    }
    
    // Display toast for each error
    if (errors.length > 0) {
      toast({
        title: "Validation Error",
        description: errors[0], // Display the first error
        variant: "destructive",
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };
  
  return { validateForm };
}
