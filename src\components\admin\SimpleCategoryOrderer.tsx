import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowUp, ArrowDown, Edit, Trash, Save } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Category } from '@/types/database';

interface CategoryItemProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
  onMoveUp: (category: Category) => void;
  onMoveDown: (category: Category) => void;
  isFirst: boolean;
  isLast: boolean;
}

const CategoryItem: React.FC<CategoryItemProps> = ({ 
  category, 
  onEdit, 
  onDelete, 
  onMoveUp, 
  onMoveDown,
  isFirst,
  isLast
}) => {
  return (
    <div className="mb-2">
      <Card className="border shadow-sm">
        <CardContent className="p-3 flex items-center">
          {category.image ? (
            <div className="w-10 h-10 mr-3 overflow-hidden rounded">
              <AspectRatio ratio={1}>
                <img 
                  src={category.image} 
                  alt={category.name} 
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    e.currentTarget.src = 'https://placehold.co/100x100?text=No+Image';
                  }}
                />
              </AspectRatio>
            </div>
          ) : (
            <div className="w-10 h-10 mr-3 bg-gray-200 rounded flex items-center justify-center">
              <span className="text-xs text-gray-500">No img</span>
            </div>
          )}
          
          <div className="flex-grow">
            <h3 className="font-medium">{category.name}</h3>
            <p className="text-xs text-gray-500">
              {category.parent_id ? 'Subcategory' : 'Main Category'}
              {category.display_order && ` (Order: ${category.display_order})`}
            </p>
          </div>
          
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onMoveUp(category)}
              disabled={isFirst}
            >
              <ArrowUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onMoveDown(category)}
              disabled={isLast}
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(category)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(category)}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface SimpleCategoryOrdererProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
  parentId: string | null;
  title: string;
}

const SimpleCategoryOrderer: React.FC<SimpleCategoryOrdererProps> = ({ 
  categories, 
  onEdit, 
  onDelete,
  parentId,
  title
}) => {
  const queryClient = useQueryClient();
  const [localCategories, setLocalCategories] = useState<Category[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  
  // Initialize local categories
  useEffect(() => {
    // Filter categories by parent_id
    const filtered = categories.filter(cat => cat.parent_id === parentId);
    
    // Sort by display_order
    const sorted = [...filtered].sort((a, b) => {
      // Handle undefined display_order values
      const orderA = a.display_order || 999;
      const orderB = b.display_order || 999;
      return orderA - orderB;
    });
    
    setLocalCategories(sorted);
    setHasChanges(false);
  }, [categories, parentId]);
  
  // Move a category up in the order
  const handleMoveUp = (category: Category) => {
    const index = localCategories.findIndex(cat => cat.id === category.id);
    if (index <= 0) return; // Already at the top
    
    const newCategories = [...localCategories];
    // Swap with the previous item
    [newCategories[index], newCategories[index - 1]] = [newCategories[index - 1], newCategories[index]];
    
    // Update display order values
    const updatedCategories = newCategories.map((cat, idx) => ({
      ...cat,
      display_order: idx + 1 // 1-based indexing
    }));
    
    setLocalCategories(updatedCategories);
    setHasChanges(true);
  };
  
  // Move a category down in the order
  const handleMoveDown = (category: Category) => {
    const index = localCategories.findIndex(cat => cat.id === category.id);
    if (index === -1 || index >= localCategories.length - 1) return; // Already at the bottom
    
    const newCategories = [...localCategories];
    // Swap with the next item
    [newCategories[index], newCategories[index + 1]] = [newCategories[index + 1], newCategories[index]];
    
    // Update display order values
    const updatedCategories = newCategories.map((cat, idx) => ({
      ...cat,
      display_order: idx + 1 // 1-based indexing
    }));
    
    setLocalCategories(updatedCategories);
    setHasChanges(true);
  };
  
  // Save the updated order to the database
  const updateCategoryOrder = useMutation({
    mutationFn: async () => {
      console.log('Saving category order:', localCategories);
      
      // Create updates with new display_order values
      const updates = localCategories.map((category, index) => ({
        id: category.id,
        display_order: index + 1 // 1-based indexing
      }));
      
      // Log the updates we're about to make
      console.log('Category updates to apply:', updates);
      
      // Update each category's display_order
      for (const update of updates) {
        console.log(`Updating category ${update.id} to display_order ${update.display_order}`);
        
        const { error } = await supabase
          .from('categories')
          .update({ display_order: update.display_order } as any)
          .eq('id', update.id);
        
        if (error) {
          console.error('Error updating category order:', error);
          throw error;
        }
      }
      
      return updates;
    },
    onSuccess: () => {
      toast({
        title: "Order saved",
        description: "Category order has been updated successfully.",
      });
      
      // Refresh the categories data
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      setHasChanges(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: `Failed to update category order: ${error.message}`,
        variant: "destructive",
      });
    },
  });
  
  const handleSaveOrder = async () => {
    if (!hasChanges) {
      toast({
        title: "No changes",
        description: "No changes to save.",
      });
      return;
    }
    
    setIsSaving(true);
    try {
      await updateCategoryOrder.mutateAsync();
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{title}</h2>
        <Button 
          onClick={handleSaveOrder}
          disabled={isSaving || !hasChanges}
          size="sm"
          variant="outline"
        >
          {isSaving ? (
            <>Saving...</>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Order
            </>
          )}
        </Button>
      </div>
      
      {localCategories.length > 0 ? (
        <div>
          {localCategories.map((category, index) => (
            <CategoryItem
              key={category.id}
              category={category}
              onEdit={onEdit}
              onDelete={onDelete}
              onMoveUp={handleMoveUp}
              onMoveDown={handleMoveDown}
              isFirst={index === 0}
              isLast={index === localCategories.length - 1}
            />
          ))}
        </div>
      ) : (
        <p className="text-gray-500 italic">No categories found</p>
      )}
    </div>
  );
};

export default SimpleCategoryOrderer;
