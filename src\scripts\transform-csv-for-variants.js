/**
 * CSV Transformer for Variant-Based Product System
 * 
 * This script transforms a standard Wix-exported CSV into two separate CSVs:
 * 1. products.csv - Contains base product information
 * 2. variants.csv - Contains variant information
 * 
 * It also handles image filename transformations and flags products without images.
 * 
 * Usage:
 * 1. Place your input CSV in the data directory
 * 2. Run: node src/scripts/transform-csv-for-variants.js
 * 3. Find output CSVs in the data/output directory
 */

const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const { v4: uuidv4 } = require('uuid');
const slugify = require('slugify');

// Define configuration (could be moved to a config file)
const CONFIG = {
  inputDir: './data',
  outputDir: './data/output',
  inputFileName: 'sample-products.csv', // Changed to use our sample file
  productsOutputFileName: 'products-transformed.csv',
  variantsOutputFileName: 'variants.csv',
};

// Define types
/**
 * @typedef {Object} WixProduct
 * @property {string} handleId
 * @property {string} fieldType
 * @property {string} name
 * @property {string} description
 * @property {string} productImageUrl
 * @property {string} collection
 * @property {string} sku
 * @property {string} ribbon
 * @property {string} price
 * @property {string} surcharge
 * @property {string} visible
 * @property {string} discountMode
 * @property {string} discountValue
 * @property {string} inventory
 * @property {string} weight
 * @property {string} cost
 * @property {string} brand
 * @property {string} [productOptionName1]
 * @property {string} [productOptionType1]
 * @property {string} [productOptionDescription1]
 * @property {string} [productOptionName2]
 * @property {string} [productOptionType2]
 * @property {string} [productOptionDescription2]
 * @property {string} [productOptionName3]
 * @property {string} [productOptionType3]
 * @property {string} [productOptionDescription3]
 * @property {string} [additionalInfoTitle1]
 * @property {string} [additionalInfoDescription1]
 * @property {string} [additionalInfoTitle2]
 * @property {string} [additionalInfoDescription2]
 * @property {string} [additionalInfoTitle3]
 * @property {string} [additionalInfoDescription3]
 */

/**
 * @typedef {Object} TransformedProduct
 * @property {string} id
 * @property {string} name
 * @property {string} slug
 * @property {string} description
 * @property {string} price
 * @property {string|null} sale_price
 * @property {string|null} image
 * @property {string} additional_images
 * @property {string|null} category_id
 * @property {string|null} brand_id
 * @property {string|null} sku
 * @property {string} stock_quantity
 * @property {string|null} weight
 * @property {string} in_stock
 * @property {string} is_featured
 * @property {string} is_new
 * @property {string} is_active
 * @property {string} option_definitions
 * @property {string} created_at
 * @property {string} updated_at
 */

/**
 * @typedef {Object} ProductVariant
 * @property {string} id
 * @property {string} product_id
 * @property {string} variant_name
 * @property {string|null} sku
 * @property {string} price
 * @property {string|null} sale_price
 * @property {string} stock_quantity
 * @property {string} in_stock
 * @property {string|null} image
 * @property {string} option_combination
 * @property {string} is_active
 * @property {string|null} external_id
 * @property {string} created_at
 * @property {string} updated_at
 */

// Helper functions
/**
 * Transform image URL by removing ~mv2 and converting to .webp
 * @param {string} url - The image URL to transform
 * @returns {string} - The transformed URL
 */
function transformImageUrl(url) {
  if (!url) return '';
  
  // Remove ~mv2 from image URLs
  url = url.replace(/~mv2/g, '');
  
  // Convert to .webp if it's a jpg or png
  if (url.match(/\.(jpg|jpeg|png)$/i)) {
    url = url.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  
  return url;
}

/**
 * Extract option values from option description
 * @param {string} optionDescription - The option description string
 * @returns {string[]} - Array of option values
 */
function extractOptionValues(optionDescription) {
  if (!optionDescription) return [];
  
  // Split by semicolons, commas, or pipes
  const values = optionDescription.split(/[;,|]/).map(v => v.trim()).filter(Boolean);
  
  return values;
}

/**
 * Parse price string to number
 * @param {string} price - The price string
 * @returns {number} - The parsed price
 */
function parsePrice(price) {
  if (!price) return 0;
  
  // Remove currency symbols and convert to number
  const cleanPrice = price.replace(/[£$€]/g, '').trim();
  return parseFloat(cleanPrice) || 0;
}

/**
 * Generate slug from product name
 * @param {string} name - The product name
 * @returns {string} - The generated slug
 */
function generateSlug(name) {
  return slugify(name, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
}

// Main transformation function
async function transformCsv() {
  console.log('Starting CSV transformation...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Read input CSV
  const inputPath = path.join(CONFIG.inputDir, CONFIG.inputFileName);
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    return;
  }
  
  const inputCsv = fs.readFileSync(inputPath, 'utf8');
  
  // Parse CSV
  const { data } = Papa.parse(inputCsv, {
    header: true,
    skipEmptyLines: true,
  });
  
  console.log(`Found ${data.length} rows in input CSV`);
  
  // Group products by name (base products)
  const productGroups = new Map();
  
  data.forEach(row => {
    const name = row.name;
    if (!productGroups.has(name)) {
      productGroups.set(name, []);
    }
    productGroups.get(name).push(row);
  });
  
  console.log(`Grouped into ${productGroups.size} unique products`);
  
  // Transform products and create variants
  const transformedProducts = [];
  const productVariants = [];
  
  productGroups.forEach((rows, productName) => {
    // Use the first row as the base product
    const baseRow = rows[0];
    const productId = uuidv4();
    const now = new Date().toISOString();
    
    // Extract option definitions
    const optionDefinitions = {};
    
    if (baseRow.productOptionName1 && baseRow.productOptionDescription1) {
      optionDefinitions[baseRow.productOptionName1] = extractOptionValues(baseRow.productOptionDescription1);
    }
    
    if (baseRow.productOptionName2 && baseRow.productOptionDescription2) {
      optionDefinitions[baseRow.productOptionName2] = extractOptionValues(baseRow.productOptionDescription2);
    }
    
    if (baseRow.productOptionName3 && baseRow.productOptionDescription3) {
      optionDefinitions[baseRow.productOptionName3] = extractOptionValues(baseRow.productOptionDescription3);
    }
    
    // Transform image URL
    const transformedImageUrl = transformImageUrl(baseRow.productImageUrl);
    
    // Create transformed product
    const transformedProduct = {
      id: productId,
      name: productName,
      slug: generateSlug(productName),
      description: baseRow.description || '',
      price: parsePrice(baseRow.price).toString(),
      sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
      image: transformedImageUrl || null,
      additional_images: '[]', // No additional images in base CSV
      category_id: null, // Will need to be set manually or in a separate process
      brand_id: null, // Will need to be set manually or in a separate process
      sku: baseRow.sku || null,
      stock_quantity: baseRow.inventory || '0',
      weight: baseRow.weight || null,
      in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
      is_featured: 'false',
      is_new: 'false',
      is_active: transformedImageUrl ? 'true' : 'false', // Flag products without images as inactive
      option_definitions: JSON.stringify(optionDefinitions),
      created_at: now,
      updated_at: now,
    };
    
    transformedProducts.push(transformedProduct);
    
    // Create variants
    if (Object.keys(optionDefinitions).length > 0) {
      // If we have option definitions, create variants
      const optionNames = Object.keys(optionDefinitions);
      
      // For simple cases with just one option
      if (optionNames.length === 1) {
        const optionName = optionNames[0];
        const values = optionDefinitions[optionName];
        
        values.forEach(value => {
          const variantId = uuidv4();
          const optionCombination = { [optionName]: value };
          
          const variant = {
            id: variantId,
            product_id: productId,
            variant_name: `${productName} - ${value}`,
            sku: baseRow.sku ? `${baseRow.sku}-${value.substring(0, 2)}` : null,
            price: baseRow.price,
            sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
            stock_quantity: baseRow.inventory || '0',
            in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
            image: transformedImageUrl || null,
            option_combination: JSON.stringify(optionCombination),
            is_active: transformedImageUrl ? 'true' : 'false',
            external_id: baseRow.handleId || null,
            created_at: now,
            updated_at: now,
          };
          
          productVariants.push(variant);
        });
      } 
      // For products with multiple options, we'd need to create combinations
      // This is a simplified approach - for real data you might need more complex logic
      else if (optionNames.length > 1) {
        // Create a default variant
        const variantId = uuidv4();
        const optionCombination = optionNames.reduce((acc, name) => {
          acc[name] = optionDefinitions[name][0] || '';
          return acc;
        }, {});
        
        const variant = {
          id: variantId,
          product_id: productId,
          variant_name: `${productName} - Default`,
          sku: baseRow.sku || null,
          price: baseRow.price,
          sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
          stock_quantity: baseRow.inventory || '0',
          in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
          image: transformedImageUrl || null,
          option_combination: JSON.stringify(optionCombination),
          is_active: transformedImageUrl ? 'true' : 'false',
          external_id: baseRow.handleId || null,
          created_at: now,
          updated_at: now,
        };
        
        productVariants.push(variant);
      }
    } else {
      // If no option definitions, create a default variant
      const variantId = uuidv4();
      
      const variant = {
        id: variantId,
        product_id: productId,
        variant_name: productName,
        sku: baseRow.sku || null,
        price: baseRow.price,
        sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
        stock_quantity: baseRow.inventory || '0',
        in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
        image: transformedImageUrl || null,
        option_combination: '{}',
        is_active: transformedImageUrl ? 'true' : 'false',
        external_id: baseRow.handleId || null,
        created_at: now,
        updated_at: now,
      };
      
      productVariants.push(variant);
    }
  });
  
  // Write output CSVs
  const productsOutputPath = path.join(CONFIG.outputDir, CONFIG.productsOutputFileName);
  const variantsOutputPath = path.join(CONFIG.outputDir, CONFIG.variantsOutputFileName);
  
  const productsOutput = Papa.unparse(transformedProducts);
  const variantsOutput = Papa.unparse(productVariants);
  
  fs.writeFileSync(productsOutputPath, productsOutput);
  fs.writeFileSync(variantsOutputPath, variantsOutput);
  
  console.log(`Transformation complete!`);
  console.log(`- Products CSV: ${productsOutputPath}`);
  console.log(`- Variants CSV: ${variantsOutputPath}`);
  console.log(`- ${transformedProducts.length} products and ${productVariants.length} variants created`);
  
  // Stats
  const productsWithoutImages = transformedProducts.filter(p => p.is_active === 'false').length;
  console.log(`- ${productsWithoutImages} products flagged as inactive (no images)`);
}

// Run the transformation
transformCsv().catch(err => {
  console.error('Error during transformation:', err);
  process.exit(1);
});
