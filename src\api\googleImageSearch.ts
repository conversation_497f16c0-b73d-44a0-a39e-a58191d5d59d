/**
 * Google Image Search API
 * This module provides utilities to search for images using Google's Custom Search API
 */

// The Google API key for Custom Search - never hardcode API keys
const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;

// You'll need to create a Custom Search Engine and get its ID
// Visit: https://programmablesearchengine.google.com/controlpanel/create
let GOOGLE_CX_ID = import.meta.env.VITE_GOOGLE_CX_ID;

// Track if Google Search is enabled (can be disabled temporarily on errors)
let _isGoogleSearchEnabled = true;

// If no CX ID is provided, we'll show a warning
if (!GOOGLE_CX_ID) {
  console.warn('No Google Custom Search Engine ID (CX) provided. Please set VITE_GOOGLE_CX_ID in your .env file.');
}

/**
 * Search for images using Google's Custom Search JSON API
 * @param query The search query
 * @param count Number of images to return (max 10)
 * @returns Promise resolving to an array of image URLs
 */
export async function searchGoogleImages(query: string, count: number = 5): Promise<string[]> {
  // Validate inputs
  if (!query) {
    console.info('No search query provided');
    return [];
  }

  if (!GOOGLE_CX_ID) {
    console.info('Google Custom Search Engine ID (CX) is required');
    return [];
  }

  if (!GOOGLE_API_KEY) {
    console.info('Google API Key is required');
    return [];
  }

  // Ensure count is within valid range (1-10)
  const safeCount = Math.min(Math.max(1, count), 10);
  
  // Refine query to improve relevance
  let refinedQuery = query;
  if (!query.toLowerCase().includes('product')) {
    refinedQuery += ' product';
  }
  
  try {
    // Construct the API URL according to the documentation
    const apiUrl = new URL('https://www.googleapis.com/customsearch/v1');
    
    // Required parameters
    apiUrl.searchParams.append('key', GOOGLE_API_KEY);
    apiUrl.searchParams.append('cx', GOOGLE_CX_ID);
    apiUrl.searchParams.append('q', refinedQuery);
    
    // Optional parameters
    apiUrl.searchParams.append('searchType', 'image');
    apiUrl.searchParams.append('num', safeCount.toString());
    apiUrl.searchParams.append('safe', 'active');
    apiUrl.searchParams.append('imgSize', 'large');
    apiUrl.searchParams.append('imgType', 'photo');
    apiUrl.searchParams.append('alt', 'json');
    
    const requestUrl = apiUrl.toString();
    console.log('Making Google Custom Search JSON API request:', requestUrl);
    
    // Make the API request with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    try {
      const response = await fetch(requestUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        console.info(`Google Custom Search API returned status: ${response.status}`);
        return [];
      }
      
      const data = await response.json();

      // Check if we have search results
      if (!data.items || data.items.length === 0) {
        console.log('No image search results found');
        return [];
      }

      // Log search stats
      if (data.searchInformation) {
        console.log(
          `Search stats: ${data.searchInformation.totalResults} results in ${data.searchInformation.searchTime} seconds`
        );
      }

      // Filter out irrelevant images and extract URLs
      const images = data.items
        .filter((item: any) => {
          // Skip images from irrelevant domains
          const irrelevantDomains = ['pinterest', 'facebook', 'twitter', 'instagram', 'tiktok'];
          const isIrrelevant = irrelevantDomains.some(domain => item.displayLink?.includes(domain));
          return !isIrrelevant;
        })
        .map((item: any) => {
          console.log(`Image result: ${item.title || 'Untitled'} - ${item.link}`);
          return item.link;
        });

      console.log(`Found ${images.length} images from Google Custom Search`);
      return images;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.info('Error fetching from Google Custom Search API');
      return [];
    }
  } catch (error) {
    console.info('Error in image search process');
    return [];
  }
}

/**
 * Get a random image URL for a specific query
 * @param query The search query
 * @returns Promise resolving to an image URL or null if none found
 */
export async function getRandomImageForQuery(query: string): Promise<string | null> {
  try {
    const images = await searchGoogleImages(query, 5);
    
    if (!images || images.length === 0) {
      console.info(`No images found for query: "${query}"`);
      return null;
    }
    
    const randomIndex = Math.floor(Math.random() * images.length);
    const selectedImage = images[randomIndex];
    
    console.log(`Selected image ${randomIndex + 1} of ${images.length} for query: "${query}"`);
    return selectedImage;
  } catch (error) {
    console.info(`Error getting random image for query "${query}"`);
    
    // Try a more generic query if the specific one fails
    if (query.includes(' ')) {
      const simplifiedQuery = query.split(' ')[0];
      console.log(`Trying simplified query: "${simplifiedQuery}"`);
      
      try {
        const fallbackImages = await searchGoogleImages(simplifiedQuery, 3);
        if (fallbackImages.length > 0) {
          const fallbackImage = fallbackImages[0];
          console.log(`Found fallback image for simplified query: "${simplifiedQuery}"`);
          return fallbackImage;
        }
      } catch (fallbackError) {
        console.info('Fallback image search also failed');
      }
    }
    
    return null;
  }
}

/**
 * Check if Google Image Search is configured
 * @returns true if Google Image Search is configured, false otherwise
 */
export function isGoogleImageSearchConfigured(): boolean {
  // Check if all required values are present
  const isConfigured = Boolean(GOOGLE_API_KEY && GOOGLE_CX_ID && _isGoogleSearchEnabled);
  
  // Log detailed information about the configuration status
  if (!isConfigured) {
    if (!GOOGLE_API_KEY) {
      console.info('Google Image Search not configured: Missing API key');
    }
    if (!GOOGLE_CX_ID) {
      console.info('Google Image Search not configured: Missing Custom Search Engine ID');
    }
    if (!_isGoogleSearchEnabled) {
      console.info('Google Image Search temporarily disabled due to previous errors');
    }
  }
  
  return isConfigured;
}

/**
 * Set the Custom Search Engine ID programmatically
 * This is useful if you want to set it from user input or configuration
 * @param cxId The Custom Search Engine ID
 */
export function setCustomSearchEngineId(cxId: string): void {
  if (cxId) {
    GOOGLE_CX_ID = cxId;
    console.log('Custom Search Engine ID set successfully');
  } else {
    console.info('Invalid Custom Search Engine ID provided');
  }
}

/**
 * Test the Google Custom Search API configuration
 * This function makes a simple test request to diagnose any issues
 * @returns Promise resolving to a diagnostic message
 */
export async function testGoogleSearchApi(): Promise<string> {
  console.log('Testing Google Custom Search API configuration...');
  console.log(`API Key: ${GOOGLE_API_KEY ? '✓ Present' : '✗ Missing'}`);
  console.log(`CX ID: ${GOOGLE_CX_ID ? '✓ Present' : '✗ Missing'}`);
  
  if (!GOOGLE_API_KEY || !GOOGLE_CX_ID) {
    return 'Configuration incomplete: Missing API Key or CX ID';
  }
  
  try {
    // Make a simple test request with minimal parameters
    const testUrl = new URL('https://www.googleapis.com/customsearch/v1');
    testUrl.searchParams.append('key', GOOGLE_API_KEY);
    testUrl.searchParams.append('cx', GOOGLE_CX_ID);
    testUrl.searchParams.append('q', 'test');
    
    console.log('Making test request to:', testUrl.toString());
    
    const response = await fetch(testUrl.toString());
    const data = await response.json();
    
    if (response.ok) {
      console.log('Test successful!');
      return `API test successful! Found ${data.searchInformation?.totalResults || 0} results`;
    } else {
      console.info('Test failed');
      
      if (data.error) {
        if (data.error.code === 403) {
          return `Error 403 Forbidden: ${data.error.message}. This usually means the API key doesn't have the Custom Search API enabled or billing isn't set up.`;
        } else {
          return `Error ${data.error.code}: ${data.error.message}`;
        }
      }
      
      return `Test failed with status ${response.status}`;
    }
  } catch (error) {
    console.info('Test error');
    return `Test error: ${error instanceof Error ? error.message : String(error)}`;
  }
}
