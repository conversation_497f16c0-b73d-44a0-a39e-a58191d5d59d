-- Create the wishlists table
CREATE TABLE IF NOT EXISTS public.wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create the wishlist_items table
CREATE TABLE IF NOT EXISTS public.wishlist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wishlist_id UUID NOT NULL REFERENCES public.wishlists(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Ensure a product can only be in a wishlist once
    CONSTRAINT wishlist_items_wishlist_product_unique UNIQUE (wishlist_id, product_id)
);

-- Add RLS policies for wishlists
ALTER TABLE public.wishlists ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to view their own wishlists
CREATE POLICY "Users can view their own wishlists" 
    ON public.wishlists 
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Policy to allow users to insert their own wishlists
CREATE POLICY "Users can create their own wishlists" 
    ON public.wishlists 
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to update their own wishlists
CREATE POLICY "Users can update their own wishlists" 
    ON public.wishlists 
    FOR UPDATE 
    USING (auth.uid() = user_id);

-- Policy to allow users to delete their own wishlists
CREATE POLICY "Users can delete their own wishlists" 
    ON public.wishlists 
    FOR DELETE 
    USING (auth.uid() = user_id);

-- Add RLS policies for wishlist items
ALTER TABLE public.wishlist_items ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to view their own wishlist items
CREATE POLICY "Users can view their own wishlist items" 
    ON public.wishlist_items 
    FOR SELECT 
    USING (EXISTS (
        SELECT 1 FROM public.wishlists
        WHERE wishlists.id = wishlist_items.wishlist_id
        AND wishlists.user_id = auth.uid()
    ));

-- Policy to allow users to insert items to their own wishlists
CREATE POLICY "Users can add items to their own wishlists" 
    ON public.wishlist_items 
    FOR INSERT 
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.wishlists
        WHERE wishlists.id = wishlist_items.wishlist_id
        AND wishlists.user_id = auth.uid()
    ));

-- Policy to allow users to delete items from their own wishlists
CREATE POLICY "Users can delete items from their own wishlists" 
    ON public.wishlist_items 
    FOR DELETE 
    USING (EXISTS (
        SELECT 1 FROM public.wishlists
        WHERE wishlists.id = wishlist_items.wishlist_id
        AND wishlists.user_id = auth.uid()
    ));

-- Add wishlists and wishlist_items to public schema
GRANT ALL ON public.wishlists TO postgres, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.wishlists TO authenticated;
GRANT SELECT ON public.wishlists TO anon;

GRANT ALL ON public.wishlist_items TO postgres, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.wishlist_items TO authenticated;
GRANT SELECT ON public.wishlist_items TO anon;

-- Create a function to create a default wishlist for new users
CREATE OR REPLACE FUNCTION public.create_default_wishlist()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.wishlists (user_id, name, is_default)
    VALUES (NEW.id, 'My Wishlist', true);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to create a default wishlist when a new user is created
DROP TRIGGER IF EXISTS create_default_wishlist_trigger ON auth.users;
CREATE TRIGGER create_default_wishlist_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.create_default_wishlist();
