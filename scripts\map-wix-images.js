/**
 * WIX Image Mapping Utility
 * 
 * This script helps map WIX image references from a CSV export to the downloaded image files.
 * It creates a mapping that can be used during the product import process.
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Configuration - update these paths as needed
const CSV_FILE_PATH = path.join(__dirname, '../docs/catalog_products.csv');
const IMAGES_DIRECTORY = path.join(__dirname, '../public/images/products/wix-imports');
const OUTPUT_MAP_PATH = path.join(__dirname, '../docs/image-mapping.json');

// Function to extract image references from the CSV
async function extractImageReferences() {
  const imageReferences = new Set();
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(CSV_FILE_PATH)
      .pipe(csv())
      .on('data', (row) => {
        if (row.productImageUrl) {
          // Split multiple image references (they're separated by semicolons)
          const images = row.productImageUrl.split(';');
          images.forEach(img => {
            if (img.trim()) {
              imageReferences.add(img.trim());
            }
          });
        }
      })
      .on('end', () => {
        console.log(`Found ${imageReferences.size} unique image references in the CSV`);
        resolve(Array.from(imageReferences));
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

// Function to get all downloaded image files
function getDownloadedImages() {
  try {
    // Make sure the directory exists
    if (!fs.existsSync(IMAGES_DIRECTORY)) {
      console.log(`Creating directory: ${IMAGES_DIRECTORY}`);
      fs.mkdirSync(IMAGES_DIRECTORY, { recursive: true });
      return [];
    }
    
    const files = fs.readdirSync(IMAGES_DIRECTORY);
    console.log(`Found ${files.length} image files in the directory`);
    return files;
  } catch (error) {
    console.error('Error reading image directory:', error);
    return [];
  }
}

// Function to create the mapping
function createMapping(imageReferences, downloadedFiles) {
  const mapping = {};
  
  // Extract the base part of the WIX image reference (before the extension)
  imageReferences.forEach(ref => {
    // The pattern is typically like: 7caa35_da4d80640eeb4b93ac9b80ca1b16daad~mv2.jpg
    const baseRef = ref.split('.')[0]; // Get the part before the extension
    
    // Look for matching downloaded files
    const matchingFiles = downloadedFiles.filter(file => {
      // Check if the downloaded file starts with the same base reference
      return file.startsWith(baseRef);
    });
    
    if (matchingFiles.length > 0) {
      mapping[ref] = matchingFiles[0]; // Use the first match
    } else {
      mapping[ref] = null; // No match found
    }
  });
  
  return mapping;
}

// Function to save the mapping to a JSON file
function saveMapping(mapping) {
  try {
    fs.writeFileSync(OUTPUT_MAP_PATH, JSON.stringify(mapping, null, 2));
    console.log(`Mapping saved to ${OUTPUT_MAP_PATH}`);
    
    // Count successful and failed mappings
    const successful = Object.values(mapping).filter(val => val !== null).length;
    const failed = Object.values(mapping).filter(val => val === null).length;
    
    console.log(`Successfully mapped: ${successful} images`);
    console.log(`Failed to map: ${failed} images`);
    
    return { successful, failed };
  } catch (error) {
    console.error('Error saving mapping:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Starting WIX image mapping process...');
    
    // Extract image references from CSV
    const imageReferences = await extractImageReferences();
    
    // Get downloaded image files
    const downloadedFiles = getDownloadedImages();
    
    // Create mapping
    const mapping = createMapping(imageReferences, downloadedFiles);
    
    // Save mapping
    const result = saveMapping(mapping);
    
    console.log('Mapping process completed!');
    return result;
  } catch (error) {
    console.error('Error in mapping process:', error);
    throw error;
  }
}

// Run the script
main().catch(console.error);
