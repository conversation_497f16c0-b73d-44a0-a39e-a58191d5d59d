import React from 'react';
import { Layers } from 'lucide-react';

interface VariantBadgeProps {
  count: number;
  className?: string;
}

export const VariantBadge: React.FC<VariantBadgeProps> = ({ 
  count, 
  className = '' 
}) => {
  if (count <= 0) return null;
  
  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${className || 'bg-primary/10 text-primary'}`}>
      <Layers className="h-3 w-3" />
      <span>{count} {count === 1 ? 'Variant' : 'Variants'}</span>
    </div>
  );
};
