/**
 * Rate Limiter Implementation
 * 
 * This file implements the RateLimiter interface for the Social Media Publishing System.
 * It manages API usage within platform-specific rate limits to prevent rate limit errors.
 */

import { RateLimiter } from './social_media_publisher';
import { SocialPlatform } from './social_media_types';

/**
 * Interface for database operations related to rate limiting
 */
interface RateLimitDatabase {
  getAccountPlatform(accountId: string): Promise<SocialPlatform>;
  recordApiUsage(accountId: string, operation: string, tokens: number): Promise<void>;
  getApiUsage(accountId: string, operation: string, windowMs: number): Promise<number>;
}

/**
 * Rate limit configuration for a specific operation
 */
interface RateLimitConfig {
  tokensPerInterval: number;
  interval: number; // in milliseconds
  burstCapacity?: number;
}

/**
 * Rate limit configurations for each platform and operation
 */
interface PlatformRateLimits {
  [platform: string]: {
    [operation: string]: RateLimitConfig;
  };
}

/**
 * Implementation of the RateLimiter interface using the token bucket algorithm
 */
export class TokenBucketRateLimiter implements RateLimiter {
  private database: RateLimitDatabase;
  private rateLimits: PlatformRateLimits;
  private bucketCache: Map<string, { tokens: number; lastRefill: number }> = new Map();
  
  /**
   * Create a new TokenBucketRateLimiter
   * @param database The rate limit database
   * @param rateLimits Rate limit configurations
   */
  constructor(database: RateLimitDatabase, rateLimits: PlatformRateLimits) {
    this.database = database;
    this.rateLimits = rateLimits;
  }
  
  /**
   * Check if operation is within rate limits and update usage
   * @param accountId The account ID
   * @param operation The operation to check
   * @returns Promise resolving to boolean indicating if operation is allowed
   */
  async checkAndUpdateRateLimit(accountId: string, operation: string): Promise<boolean> {
    try {
      // Get platform for this account
      const platform = await this.database.getAccountPlatform(accountId);
      
      // Get rate limit config for this platform and operation
      const config = this.getRateLimitConfig(platform, operation);
      if (!config) {
        // No rate limit configured, allow the operation
        return true;
      }
      
      // Get or create bucket for this account and operation
      const bucketKey = `${accountId}:${operation}`;
      let bucket = this.bucketCache.get(bucketKey);
      
      if (!bucket) {
        // Initialize bucket with full tokens
        bucket = {
          tokens: config.burstCapacity || config.tokensPerInterval,
          lastRefill: Date.now()
        };
        this.bucketCache.set(bucketKey, bucket);
      }
      
      // Refill bucket based on elapsed time
      this.refillBucket(bucket, config);
      
      // Check if we have enough tokens for this operation
      if (bucket.tokens >= 1) {
        // Consume a token
        bucket.tokens -= 1;
        
        // Record API usage in database
        await this.database.recordApiUsage(accountId, operation, 1);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Rate limit check failed:', error);
      // In case of error, allow the operation to proceed
      // The API might reject it if it actually exceeds rate limits
      return true;
    }
  }
  
  /**
   * Get estimated time until operation is allowed
   * @param accountId The account ID
   * @param operation The operation to check
   * @returns Promise resolving to milliseconds until operation is allowed
   */
  async getTimeUntilAllowed(accountId: string, operation: string): Promise<number> {
    try {
      // Get platform for this account
      const platform = await this.database.getAccountPlatform(accountId);
      
      // Get rate limit config for this platform and operation
      const config = this.getRateLimitConfig(platform, operation);
      if (!config) {
        // No rate limit configured, operation is allowed immediately
        return 0;
      }
      
      // Get bucket for this account and operation
      const bucketKey = `${accountId}:${operation}`;
      let bucket = this.bucketCache.get(bucketKey);
      
      if (!bucket) {
        // Bucket not found, operation is allowed immediately
        return 0;
      }
      
      // Refill bucket based on elapsed time
      this.refillBucket(bucket, config);
      
      // If we have tokens, operation is allowed immediately
      if (bucket.tokens >= 1) {
        return 0;
      }
      
      // Calculate time until next token is available
      const tokenRefillRate = config.tokensPerInterval / config.interval;
      const timeForOneToken = 1 / tokenRefillRate;
      
      return Math.ceil(timeForOneToken * 1000);
    } catch (error) {
      console.error('Get time until allowed failed:', error);
      // In case of error, assume operation is allowed immediately
      return 0;
    }
  }
  
  /**
   * Refill a token bucket based on elapsed time
   * @param bucket The bucket to refill
   * @param config The rate limit configuration
   */
  private refillBucket(
    bucket: { tokens: number; lastRefill: number },
    config: RateLimitConfig
  ): void {
    const now = Date.now();
    const elapsed = now - bucket.lastRefill;
    
    if (elapsed <= 0) {
      return;
    }
    
    // Calculate tokens to add based on elapsed time
    const tokensToAdd = (elapsed * config.tokensPerInterval) / config.interval;
    
    if (tokensToAdd > 0) {
      bucket.tokens = Math.min(
        bucket.tokens + tokensToAdd,
        config.burstCapacity || config.tokensPerInterval
      );
      bucket.lastRefill = now;
    }
  }
  
  /**
   * Get rate limit configuration for a platform and operation
   * @param platform The platform
   * @param operation The operation
   * @returns Rate limit configuration or undefined if not configured
   */
  private getRateLimitConfig(platform: SocialPlatform, operation: string): RateLimitConfig | undefined {
    // Get platform-specific rate limits
    const platformLimits = this.rateLimits[platform];
    if (!platformLimits) {
      return this.getDefaultRateLimit(platform);
    }
    
    // Get operation-specific rate limit
    const operationLimit = platformLimits[operation];
    if (!operationLimit) {
      // Fall back to default operation for the platform
      return platformLimits['default'] || this.getDefaultRateLimit(platform);
    }
    
    return operationLimit;
  }
  
  /**
   * Get default rate limit for a platform
   * @param platform The platform
   * @returns Default rate limit configuration
   */
  private getDefaultRateLimit(platform: SocialPlatform): RateLimitConfig {
    // Default rate limits for each platform
    switch (platform) {
      case 'instagram':
        return { tokensPerInterval: 200, interval: 3600000 }; // 200 per hour
      case 'facebook':
        return { tokensPerInterval: 200, interval: 3600000 }; // 200 per hour
      case 'twitter':
        return { tokensPerInterval: 300, interval: 900000 }; // 300 per 15 minutes
      case 'tiktok':
        return { tokensPerInterval: 100, interval: 3600000 }; // 100 per hour
      default:
        return { tokensPerInterval: 60, interval: 3600000 }; // 60 per hour (conservative)
    }
  }
  
  /**
   * Clear rate limit cache for an account
   * @param accountId The account ID
   */
  clearCache(accountId: string): void {
    // Remove all buckets for this account
    for (const key of this.bucketCache.keys()) {
      if (key.startsWith(`${accountId}:`)) {
        this.bucketCache.delete(key);
      }
    }
  }
  
  /**
   * Clear all rate limit cache
   */
  clearAllCache(): void {
    this.bucketCache.clear();
  }
}

/**
 * Default rate limit configurations for all platforms
 */
export const defaultRateLimits: PlatformRateLimits = {
  instagram: {
    default: { tokensPerInterval: 200, interval: 3600000 }, // 200 per hour
    publishPost: { tokensPerInterval: 25, interval: 3600000 }, // 25 per hour
    mediaUpload: { tokensPerInterval: 50, interval: 3600000 }, // 50 per hour
    getPostStatus: { tokensPerInterval: 50, interval: 3600000 }, // 50 per hour
    getPostAnalytics: { tokensPerInterval: 30, interval: 3600000 } // 30 per hour
  },
  facebook: {
    default: { tokensPerInterval: 200, interval: 3600000 }, // 200 per hour
    publishPost: { tokensPerInterval: 25, interval: 3600000 }, // 25 per hour
    getPostStatus: { tokensPerInterval: 50, interval: 3600000 }, // 50 per hour
    getPostAnalytics: { tokensPerInterval: 30, interval: 3600000 } // 30 per hour
  },
  twitter: {
    default: { tokensPerInterval: 300, interval: 900000 }, // 300 per 15 minutes
    publishPost: { tokensPerInterval: 200, interval: 10800000 }, // 200 per 3 hours
    mediaUpload: { tokensPerInterval: 30, interval: 3600000 }, // 30 per hour
    getPostStatus: { tokensPerInterval: 900, interval: 900000 }, // 900 per 15 minutes
    getPostAnalytics: { tokensPerInterval: 450, interval: 900000 } // 450 per 15 minutes
  },
  tiktok: {
    default: { tokensPerInterval: 100, interval: 3600000 }, // 100 per hour
    publishPost: { tokensPerInterval: 10, interval: 3600000 }, // 10 per hour
    getPostStatus: { tokensPerInterval: 30, interval: 3600000 }, // 30 per hour
    getPostAnalytics: { tokensPerInterval: 20, interval: 3600000 } // 20 per hour
  }
};
