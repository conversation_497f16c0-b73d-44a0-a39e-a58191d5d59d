import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import slugify from 'slugify';
import { useToast } from '@/components/ui/use-toast';

const TestProductsAdder: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const { toast } = useToast();

  // Define test products with local image paths
  const testProducts = [
    {
      name: 'Premium Black Shredder',
      description: 'High-quality 2-piece large black shredder with sharp teeth for perfect grinding. Made from durable aircraft-grade aluminum with a smooth finish.',
      price: 19.99,
      sale_price: null,
      localImage: '/product-images/2-piece-large-black-shredder.jpg',
      category_id: null,
      in_stock: true,
      is_featured: true,
      is_new: true,
      is_best_seller: false,
    },
    {
      name: 'Strawberry Dream Glass Pipe',
      description: 'Beautiful hand-blown glass pipe with strawberry-inspired design. Features a deep bowl and comfortable grip for an enhanced smoking experience.',
      price: 24.99,
      sale_price: 19.99,
      localImage: '/product-images/Ztrawberry_2g2e-9l.jpg.jpg',
      category_id: null,
      in_stock: true,
      is_featured: true,
      is_new: false,
      is_best_seller: true,
    },
    {
      name: 'Deluxe Water Pipe',
      description: 'Premium water pipe with percolator for smooth filtration. Made from high-quality borosilicate glass with reinforced joints for durability.',
      price: 89.99,
      sale_price: null,
      localImage: '/product-images/XGVQZFZ2RH3_1.jpg',
      category_id: null,
      in_stock: true,
      is_featured: true,
      is_new: true,
      is_best_seller: true,
    }
  ];

  // Function to add test categories
  const addTestCategories = async () => {
    const categories = [
      { name: 'Grinders', slug: 'grinders', description: 'High-quality grinders for all your needs' },
      { name: 'Glass Pipes', slug: 'glass-pipes', description: 'Beautiful hand-blown glass pipes' },
      { name: 'Water Pipes', slug: 'water-pipes', description: 'Premium water pipes and bongs' },
    ];
    
    const { data, error } = await supabase
      .from('categories')
      .upsert(categories, { onConflict: 'slug' })
      .select();
    
    if (error) {
      console.error('Error adding categories:', error);
      return null;
    }
    
    return data;
  };

  // Function to fetch and upload an image to Supabase storage
  const uploadImageToStorage = async (localPath: string): Promise<string | null> => {
    try {
      console.log('Uploading image from path:', localPath);
      
      // For testing, we'll directly use the local paths for now
      // This allows us to test with images in the public directory
      return localPath;
      
      /* Uncomment this code to actually upload to Supabase storage
      // Remove leading slash if present
      const cleanPath = localPath.startsWith('/') ? localPath.substring(1) : localPath;
      
      // Fetch the image from public directory
      const response = await fetch(cleanPath);
      if (!response.ok) {
        console.error(`Failed to fetch image from ${cleanPath}`);
        return null;
      }
      
      const blob = await response.blob();
      const fileName = localPath.split('/').pop() || 'product-image.jpg';
      const uniqueFileName = `${Date.now()}-${fileName}`;
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(uniqueFileName, blob, {
          contentType: blob.type,
          cacheControl: '3600'
        });
      
      if (error) {
        console.error('Error uploading image to storage:', error);
        return null;
      }
      
      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('product-images')
        .getPublicUrl(uniqueFileName);
      
      return publicUrlData.publicUrl;
      */
    } catch (error) {
      console.error('Error in uploadImageToStorage:', error);
      return null;
    }
  };

  // Function to add test products
  const addTestProducts = async (categories: any[]) => {
    // Map category names to IDs
    const categoryMap = categories.reduce((map, category) => {
      map[category.name.toLowerCase()] = category.id;
      return map;
    }, {} as Record<string, string>);
    
    // Upload images and create products
    const productsPromises = testProducts.map(async (product) => {
      // Upload image to storage
      const imageUrl = await uploadImageToStorage(product.localImage);
      
      const slug = slugify(product.name, { lower: true, strict: true });
      let categoryId = null;
      
      if (product.name.toLowerCase().includes('shredder') || product.name.toLowerCase().includes('grinder')) {
        categoryId = categoryMap['grinders'];
      } else if (product.name.toLowerCase().includes('glass pipe')) {
        categoryId = categoryMap['glass pipes'];
      } else if (product.name.toLowerCase().includes('water pipe') || product.name.toLowerCase().includes('bong')) {
        categoryId = categoryMap['water pipes'];
      }
      
      // Create product object without localImage property
      const { localImage, ...productData } = product;
      
      return {
        ...productData,
        slug,
        category_id: categoryId,
        image: imageUrl,
      };
    });
    
    // Wait for all image uploads and product preparations
    const productsWithCategories = await Promise.all(productsPromises);
    
    // Insert products
    const { data, error } = await supabase
      .from('products')
      .upsert(productsWithCategories, { onConflict: 'slug' })
      .select();
    
    if (error) {
      console.error('Error adding products:', error);
      return null;
    }
    
    return data;
  };

  // Handle adding test data
  const handleAddTestData = async () => {
    setIsLoading(true);
    setResult(null);
    
    try {
      const categories = await addTestCategories();
      if (!categories) {
        throw new Error('Failed to add categories');
      }
      
      const products = await addTestProducts(categories);
      if (!products) {
        throw new Error('Failed to add products');
      }
      
      setResult(`Successfully added ${categories.length} categories and ${products.length} products!`);
      toast({
        title: 'Success!',
        description: `Added ${products.length} test products to the database.`,
      });
    } catch (error: any) {
      setResult(`Error: ${error.message}`);
      toast({
        title: 'Error',
        description: `Failed to add test products: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add Test Products</CardTitle>
        <CardDescription>
          Add sample products to the database for testing purposes.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          This will add 3 test products and 3 categories to the database. If they already exist, they will be updated.
        </p>
        {result && (
          <div className={`p-3 rounded-md mb-4 ${result.startsWith('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
            {result}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleAddTestData} 
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </>
          ) : (
            'Add Test Products'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TestProductsAdder;
