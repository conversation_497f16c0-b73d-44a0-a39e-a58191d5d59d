# Filter System Design for Cannabis Seeds Website

## Database Schema

### Products Table
```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    seed_type ENUM('Autoflower', 'Feminized', 'Regular') NOT NULL,
    image_url VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Filter Categories Table
```sql
CREATE TABLE filter_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    display_order INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);
```

### Filter Options Table
```sql
CREATE TABLE filter_options (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    display_order INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (category_id) REFERENCES filter_categories(id)
);
```

### Product-Filter Relationships Table
```sql
CREATE TABLE product_filters (
    product_id INT NOT NULL,
    filter_option_id INT NOT NULL,
    PRIMARY KEY (product_id, filter_option_id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (filter_option_id) REFERENCES filter_options(id)
);
```

## Initial Data Population

Based on the Dutch Passion website analysis, we'll populate the filter categories and options with:

1. **Flowertime**
   - 7 weeks
   - 8 weeks
   - 9 weeks
   - 10 weeks
   - 11 weeks
   - 12 weeks
   - 13 weeks
   - 14 weeks
   - 9-11 weeks
   - 9-12 weeks

2. **Yield**
   - XXL
   - XL
   - L
   - M
   - M/L

3. **THC**
   - Extremely high
   - Very High
   - High
   - Medium
   - Low

4. **Seed Type**
   - Autoflower
   - Feminized
   - Regular

5. **Effect**
   - Hybrid
   - Sativa
   - Indica

6. **Seed Family**
   - Afghani Kush
   - Blue Family
   - CBD Rich
   - Classics
   - Dutch Outdoor

7. **Lifecycle**
   - 8 weeks
   - 9 weeks
   - 10 weeks
   - 10-13 weeks
   - 11 weeks

8. **CBD**
   - Low
   - 2%
   - 8%
   - 9%
   - 10%
   - 10% - 13%

9. **Other**
   - Prize Winner

## Frontend Implementation

### Filter UI Components

1. **Filter Sidebar**
   - Collapsible categories
   - Checkbox options with product counts
   - "See more"/"See less" toggles for long lists
   - Mobile-responsive design

2. **Product Grid**
   - Responsive card layout
   - Product image
   - Name and price
   - Seed type indicator
   - Add to cart functionality

### Filter Logic Implementation

1. **Client-side Filtering**
   - JavaScript-based filtering for immediate response
   - URL parameter updates to maintain state
   - Filter combination using AND logic between categories
   - Dynamic product count updates

2. **Server-side Filtering (Alternative)**
   - API endpoints for filtered product queries
   - SQL query generation based on selected filters
   - Pagination support for large result sets

## Product Categorization Tool

To help categorize the client's existing products according to the Dutch Passion filter system:

1. **CSV Import/Export**
   - Import existing product data
   - Export template with filter categories
   - Export categorized products

2. **Batch Categorization Interface**
   - Select multiple products
   - Apply filter attributes in bulk
   - Preview changes before saving

3. **Automated Matching Suggestions**
   - Name-based matching algorithm
   - Similar product recommendations
   - Confidence scoring for suggestions

## Implementation Technologies

1. **Backend**
   - PHP/Laravel or Node.js/Express
   - MySQL or PostgreSQL database
   - RESTful API endpoints

2. **Frontend**
   - HTML5, CSS3, JavaScript
   - Vue.js or React for dynamic components
   - Responsive design with mobile support

3. **Deployment**
   - Docker containerization
   - CI/CD pipeline integration
   - Performance optimization
