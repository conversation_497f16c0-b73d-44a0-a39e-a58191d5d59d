export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  sale_price: number | null;
  cost_price: number | null;
  image: string | null;
  additional_images: string[] | null;
  sku: string | null;
  barcode?: string | null;
  quantity?: number | null;
  is_active: boolean;
  in_stock: boolean;
  is_featured: boolean;
  is_new: boolean;
  is_best_seller?: boolean;
  rating?: number;
  review_count?: number;
  created_at: string;
  updated_at: string | null;
  category_id: string | null;
  subcategory_id: string | null;
  brand_id: string | null;
  stock_quantity: number | null;
  weight: number | null;
  dimensions: string | null;
  option_name1: string | null;
  option_type1: string | null;
  option_description1: string | null;
  option_name2: string | null;
  option_type2: string | null;
  option_description2: string | null;
  option_name3: string | null;
  option_type3: string | null;
  option_description3: string | null;
  option_value1: string | null;
  option_value2: string | null;
  option_value3: string | null;
  option_price_adjustment1: string | null;
  option_price_adjustment2: string | null;
  option_price_adjustment3: string | null;
  external_id: string | null;
  last_synced_at: string | null;
  sync_status: string | null;
  external_stock_quantity: number | null;
  // New field for variant-based system
  option_definitions: Record<string, string[]> | null;
  [key: string]: any;
}

export interface ProductVariant {
  id: string;
  product_id: string;
  variant_name: string;
  sku: string | null;
  price: number;
  price_adjustment?: number; // UI-only field, not stored in database
  sale_price: number | null;
  stock_quantity: number | null;
  in_stock: boolean;
  image: string | null;
  option_combination: Record<string, string>;
  external_id: string | null;
  created_at: string;
  updated_at: string | null;
  is_active: boolean;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface Brand {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  logo: string | null;
  created_at: string;
  updated_at: string | null;
}

export interface RelatedProduct {
  id: string;
  product_id: string;
  related_product_id: string;
  display_order: number;
  created_at: string;
}

export interface Profile {
  id: string;
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url: string | null;
  created_at: string;
  updated_at: string | null;
}

// Define the database schema
export interface Database {
  public: {
    Tables: {
      categories: {
        Row: Category;
      };
      products: {
        Row: Product;
      };
      product_variants: {
        Row: ProductVariant;
      };
      related_products: {
        Row: RelatedProduct;
      };
      profiles: {
        Row: Profile;
      };
    };
  };
}

// Helper type for Supabase client
export type Tables = Database['public']['Tables'];
