"""
Dutch Passion Website Scraper

This script scrapes product data and filter options from the Dutch Passion cannabis seeds website.
It captures product details and their relationships to filter categories.
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Setup Chrome options
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

# Function to extract filter categories and options
def extract_filters(driver):
    print("Extracting filter categories and options...")
    filter_data = {}
    
    # Wait for filters to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CLASS_NAME, "filter-section"))
    )
    
    # Get all filter sections
    filter_sections = driver.find_elements(By.CLASS_NAME, "filter-section")
    
    for section in filter_sections:
        try:
            # Get category name
            category_name = section.find_element(By.CLASS_NAME, "filter-title").text.strip()
            filter_data[category_name] = []
            
            # Get all options in this category
            options = section.find_elements(By.CLASS_NAME, "filter-option")
            
            for option in options:
                option_text = option.text.strip()
                # Extract option name and count (format: "Option name (count)")
                if "(" in option_text and ")" in option_text:
                    name = option_text.split("(")[0].strip()
                    count = option_text.split("(")[1].split(")")[0].strip()
                    filter_data[category_name].append({
                        "name": name,
                        "count": count,
                        "value": option.get_attribute("data-value") if option.get_attribute("data-value") else name
                    })
        except Exception as e:
            print(f"Error extracting filter section: {e}")
    
    return filter_data

# Function to extract product data
def extract_products(driver, page_count=3):
    print("Extracting product data...")
    all_products = []
    
    for page in range(1, page_count + 1):
        if page > 1:
            # Navigate to next page
            try:
                next_page_url = f"https://dutch-passion.com/en/cannabis-seeds?page={page}"
                driver.get(next_page_url)
                time.sleep(2)  # Wait for page to load
            except Exception as e:
                print(f"Error navigating to page {page}: {e}")
                break
        
        # Wait for products to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "product-card"))
        )
        
        # Get all product cards
        product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
        
        for card in product_cards:
            try:
                product = {}
                
                # Extract basic product info
                product["name"] = card.find_element(By.CLASS_NAME, "product-title").text.strip()
                
                # Extract price
                price_element = card.find_element(By.CLASS_NAME, "product-price")
                price_text = price_element.text.strip()
                product["price"] = price_text
                
                # Extract seed type
                try:
                    seed_type_element = card.find_element(By.CLASS_NAME, "seed-type")
                    product["seed_type"] = seed_type_element.text.strip()
                except:
                    product["seed_type"] = "Unknown"
                
                # Extract product URL for detailed info
                product_link = card.find_element(By.TAG_NAME, "a").get_attribute("href")
                product["url"] = product_link
                
                # Extract image URL
                try:
                    img_element = card.find_element(By.TAG_NAME, "img")
                    product["image_url"] = img_element.get_attribute("src")
                except:
                    product["image_url"] = ""
                
                # Extract data attributes that might contain filter information
                for attr in card.get_property("attributes"):
                    attr_name = attr.get("name")
                    if attr_name and attr_name.startswith("data-"):
                        product[attr_name] = attr.get("value")
                
                all_products.append(product)
            except Exception as e:
                print(f"Error extracting product: {e}")
    
    return all_products

# Function to get detailed product information
def get_product_details(driver, products, sample_size=5):
    print(f"Getting detailed information for {sample_size} sample products...")
    detailed_products = []
    
    # Only process a sample of products to avoid excessive requests
    for product in products[:sample_size]:
        try:
            driver.get(product["url"])
            time.sleep(2)  # Wait for page to load
            
            # Wait for product details to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "product-details"))
            )
            
            # Create a copy of the basic product info
            detailed_product = product.copy()
            
            # Extract product specifications/attributes
            specs = {}
            spec_elements = driver.find_elements(By.CLASS_NAME, "product-specification")
            
            for spec in spec_elements:
                try:
                    label = spec.find_element(By.CLASS_NAME, "spec-label").text.strip()
                    value = spec.find_element(By.CLASS_NAME, "spec-value").text.strip()
                    specs[label] = value
                except:
                    pass
            
            detailed_product["specifications"] = specs
            
            # Extract product description
            try:
                description = driver.find_element(By.CLASS_NAME, "product-description").text.strip()
                detailed_product["description"] = description
            except:
                detailed_product["description"] = ""
            
            detailed_products.append(detailed_product)
        except Exception as e:
            print(f"Error getting details for product {product['name']}: {e}")
    
    return detailed_products

# Main function to run the scraper
def main():
    print("Starting Dutch Passion website scraper...")
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Navigate to the website
        driver.get("https://dutch-passion.com/en/cannabis-seeds")
        time.sleep(2)  # Wait for page to load
        
        # Handle age verification if present
        try:
            enter_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Enter')]"))
            )
            enter_button.click()
            time.sleep(1)
        except:
            print("Age verification not found or already handled")
        
        # Extract filter data
        filter_data = extract_filters(driver)
        
        # Extract product data (first 3 pages)
        products = extract_products(driver, page_count=3)
        
        # Get detailed information for a sample of products
        detailed_products = get_product_details(driver, products, sample_size=5)
        
        # Save data to JSON files
        with open('dutch_passion_filters.json', 'w') as f:
            json.dump(filter_data, f, indent=2)
        
        with open('dutch_passion_products.json', 'w') as f:
            json.dump(products, f, indent=2)
        
        with open('dutch_passion_detailed_products.json', 'w') as f:
            json.dump(detailed_products, f, indent=2)
        
        print(f"Scraping completed. Extracted {len(filter_data)} filter categories and {len(products)} products.")
        print("Data saved to JSON files.")
        
    except Exception as e:
        print(f"Error during scraping: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
