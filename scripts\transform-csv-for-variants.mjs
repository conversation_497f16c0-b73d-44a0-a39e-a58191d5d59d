// CSV Transformer for Variant-Based Product System
// This script transforms the existing CSV file into a new format suitable for our variant-based system

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import <PERSON> from 'papaparse';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const INPUT_CSV_PATH = path.join(__dirname, '../docs/catalog_products.csv');
const OUTPUT_CSV_PATH = path.join(__dirname, '../docs/transformed_products.csv');

// Main function
async function transformCsv() {
  try {
    console.log('Starting CSV transformation...');
    
    // Read the input CSV file
    const csvFile = fs.readFileSync(INPUT_CSV_PATH, 'utf8');
    
    // Parse the CSV
    const { data } = Papa.parse(csvFile, {
      header: true,
      skipEmptyLines: true,
    });
    
    console.log(`Read ${data.length} rows from input CSV`);
    
    // Group products by handleId to identify variants
    const productGroups = {};
    
    data.forEach(row => {
      const handleId = row.handleId;
      
      if (!productGroups[handleId]) {
        productGroups[handleId] = [];
      }
      
      productGroups[handleId].push(row);
    });
    
    console.log(`Grouped into ${Object.keys(productGroups).length} unique products`);
    
    // Transform the data into our new format
    const transformedProducts = [];
    const transformedVariants = [];
    
    for (const [handleId, rows] of Object.entries(productGroups)) {
      // Use the first row as the base product
      const baseProduct = rows[0];
      
      // Skip if not a product type
      if (baseProduct.fieldType !== 'Product') {
        continue;
      }
      
      // Transform images
      const transformedImages = transformImages(baseProduct.productImageUrl);
      
      // Check if product has images and set is_active accordingly
      const hasImages = transformedImages.length > 0;
      
      // Create the transformed product
      const product = {
        id: handleId,
        name: baseProduct.name,
        slug: slugify(baseProduct.name, { lower: true, strict: true }),
        description: baseProduct.description || '',
        price: parseFloat(baseProduct.price) || 0,
        sale_price: calculateSalePrice(baseProduct),
        image: transformedImages.length > 0 ? transformedImages[0] : '',
        additional_images: transformedImages.length > 1 ? JSON.stringify(transformedImages.slice(1)) : '[]',
        category_id: '', // Will need to be mapped later
        brand_id: '', // Will need to be mapped later
        sku: baseProduct.sku || '',
        stock_quantity: baseProduct.inventory === 'InStock' ? '10' : '0',
        in_stock: baseProduct.inventory === 'InStock' ? 'TRUE' : 'FALSE',
        is_featured: 'FALSE',
        is_new: 'FALSE',
        is_best_seller: 'FALSE',
        is_active: hasImages && (baseProduct.visible === 'TRUE') ? 'TRUE' : 'FALSE',
        option_name1: baseProduct.productOptionName1 || '',
        option_type1: baseProduct.productOptionType1 || '',
        option_description1: baseProduct.productOptionDescription1 || '',
        option_name2: baseProduct.productOptionName2 || '',
        option_type2: baseProduct.productOptionType2 || '',
        option_description2: baseProduct.productOptionDescription2 || '',
        option_name3: baseProduct.productOptionName3 || '',
        option_type3: baseProduct.productOptionType3 || '',
        option_description3: baseProduct.productOptionDescription3 || '',
        option_definitions: createOptionDefinitions(baseProduct),
        external_id: handleId,
      };
      
      // Log products without images
      if (!hasImages) {
        console.log(`Product without images (marked inactive): ${baseProduct.name}`);
      }
      
      transformedProducts.push(product);
      
      // Extract variants if options exist
      if (baseProduct.productOptionName1 && baseProduct.productOptionDescription1) {
        const optionValues1 = baseProduct.productOptionDescription1.split(';').map(v => v.trim());
        
        // Create variants for each option value
        optionValues1.forEach(value1 => {
          const variant = {
            product_id: handleId,
            variant_name: value1,
            sku: `${baseProduct.sku || handleId}-${value1.replace(/\s+/g, '-').toLowerCase()}`,
            price: parseFloat(baseProduct.price) || 0, // Default to base price, will be updated later
            sale_price: calculateSalePrice(baseProduct),
            stock_quantity: baseProduct.inventory === 'InStock' ? '10' : '0', // Default value
            in_stock: baseProduct.inventory === 'InStock' ? 'TRUE' : 'FALSE',
            image: transformedImages.length > 0 ? transformedImages[0] : '',
            option1_name: baseProduct.productOptionName1,
            option1_value: value1,
            option2_name: baseProduct.productOptionName2 || '',
            option2_value: '',
            option3_name: baseProduct.productOptionName3 || '',
            option3_value: '',
          };
          
          transformedVariants.push(variant);
        });
      }
    }
    
    console.log(`Created ${transformedProducts.length} transformed products`);
    console.log(`Created ${transformedVariants.length} transformed variants`);
    
    // Update variant prices based on any variant rows in the original CSV
    data.forEach(row => {
      if (row.fieldType === 'Variant') {
        const parentId = row.handleId;
        const variantName = extractVariantName(row.name);
        
        if (variantName) {
          // Find the matching variant
          const matchingVariant = transformedVariants.find(v => 
            v.product_id === parentId && v.variant_name === variantName
          );
          
          if (matchingVariant) {
            // Update the variant price
            matchingVariant.price = parseFloat(row.price) || matchingVariant.price;
            matchingVariant.sale_price = calculateSalePrice(row);
            matchingVariant.stock_quantity = row.inventory === 'InStock' ? '10' : '0';
            matchingVariant.in_stock = row.inventory === 'InStock' ? 'TRUE' : 'FALSE';
          }
        }
      }
    });
    
    // Create the products CSV
    const productsFields = [
      'id', 'name', 'description', 'base_price', 'sale_price', 'main_image', 
      'additional_images', 'category', 'brand', 'sku', 'in_stock', 'is_active',
      'option_name1', 'option_values1', 'option_name2', 'option_values2', 
      'option_name3', 'option_values3'
    ];
    
    const productsCsv = Papa.unparse({
      fields: productsFields,
      data: transformedProducts
    });
    
    // Create the variants CSV
    const variantsFields = [
      'product_id', 'variant_name', 'sku', 'price', 'sale_price', 'stock_quantity',
      'in_stock', 'image', 'option1_name', 'option1_value', 'option2_name', 
      'option2_value', 'option3_name', 'option3_value'
    ];
    
    const variantsCsv = Papa.unparse({
      fields: variantsFields,
      data: transformedVariants
    });
    
    // Write the products CSV
    fs.writeFileSync(
      path.join(__dirname, '../docs/transformed_products.csv'), 
      productsCsv
    );
    
    // Write the variants CSV
    fs.writeFileSync(
      path.join(__dirname, '../docs/transformed_variants.csv'), 
      variantsCsv
    );
    
    console.log('CSV transformation complete!');
    console.log(`Products CSV written to: ${path.join(__dirname, '../docs/transformed_products.csv')}`);
    console.log(`Variants CSV written to: ${path.join(__dirname, '../docs/transformed_variants.csv')}`);
  } catch (error) {
    console.error('Error transforming CSV:', error);
  }
}

// Helper function to transform image URLs
function transformImages(imageUrls) {
  if (!imageUrls) return [];
  
  return imageUrls.split(';')
    .map(url => {
      // Extract the filename
      const urlParts = url.split('/');
      let filename = urlParts[urlParts.length - 1];
      
      // Remove ~mv2 and change extension to .webp
      filename = filename.replace(/~mv2\.(jpg|jpeg|png|gif|webp)$/i, '.webp');
      
      // If no ~mv2 but has extension, still change to .webp
      filename = filename.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
      
      return filename;
    })
    .filter(Boolean); // Remove empty entries
}

// Helper function to calculate sale price
function calculateSalePrice(product) {
  if (product.discountMode === 'PERCENT' && product.discountValue) {
    const discountValue = parseFloat(product.discountValue);
    const originalPrice = parseFloat(product.price);
    if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
      return (originalPrice * (1 - discountValue / 100)).toFixed(2);
    }
  }
  return '';
}

// Helper function to extract variant name from product name
function extractVariantName(name) {
  if (!name) return null;
  
  // Look for common variant patterns in the name
  const patterns = [
    /(\d+\s*Pack)/i,
    /(Small|Medium|Large|XL|XXL)/i,
    /(Red|Blue|Green|Yellow|Black|White)/i
  ];
  
  for (const pattern of patterns) {
    const match = name.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

// Run the transformation
transformCsv();
