// fix-dropdown-options.mjs
// <PERSON>ript to fix product options where "DROP_DOWN" is incorrectly showing as an option value
// This script will replace "DROP_DOWN" with the actual option values from the CSV file

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Using Supabase key:', supabaseKey ? 'Key found (not showing for security)' : 'No key found');

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to the CSV file
const csvFilePath = path.join(process.cwd(), 'docs', 'catalog_products.csv');

// Map to store product options from CSV
const productOptionsMap = new Map();

// Read the CSV file and extract product options
async function readProductOptionsFromCSV() {
  console.log('Reading product options from CSV file...');
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        // Extract product details
        const name = row.name;
        const sku = row.sku || '';
        
        // Check for product options (up to 6 options as seen in the CSV headers)
        const options = [];
        
        for (let i = 1; i <= 6; i++) {
          const optionName = row[`productOptionName${i}`];
          const optionType = row[`productOptionType${i}`];
          const optionDescription = row[`productOptionDescription${i}`];
          
          if (optionName && optionType === 'DROP_DOWN' && optionDescription) {
            options.push({
              name: optionName,
              values: optionDescription.split(';').map(v => v.trim())
            });
          }
        }
        
        // Store product options if they exist
        if (options.length > 0) {
          productOptionsMap.set(name, {
            sku,
            name,
            options
          });
        }
      })
      .on('end', () => {
        console.log(`Finished reading CSV. Found ${productOptionsMap.size} products with options.`);
        resolve(productOptionsMap);
      })
      .on('error', (error) => {
        console.error('Error reading CSV:', error);
        reject(error);
      });
  });
}

// Fix product options in the database
async function fixProductOptions() {
  console.log('Starting to fix product options...');
  
  // Get all products from the database
  const { data: products, error } = await supabase
    .from('products')
    .select('*');
  
  if (error) {
    console.error('Error fetching products:', error);
    return;
  }
  
  console.log(`Found ${products.length} products in the database.`);
  
  // Count products with "DROP_DOWN" in option_type fields
  const productsWithDropdown = products.filter(p => 
    p.option_type1 === 'DROP_DOWN' || 
    p.option_type2 === 'DROP_DOWN' || 
    p.option_type3 === 'DROP_DOWN'
  );
  
  console.log(`Found ${productsWithDropdown.length} products with 'DROP_DOWN' in option_type fields.`);
  
  let updatedCount = 0;
  let skippedCount = 0;
  
  // Process each product with DROP_DOWN options
  for (const product of productsWithDropdown) {
    console.log(`Processing product: ${product.name} (ID: ${product.id})`);
    
    // Try to find matching product in CSV data
    let csvProduct = null;
    
    // Try exact name match
    if (productOptionsMap.has(product.name)) {
      csvProduct = productOptionsMap.get(product.name);
      console.log(`Found exact name match in CSV for: ${product.name}`);
    } else {
      // Try case-insensitive name match
      for (const [csvName, csvData] of productOptionsMap.entries()) {
        if (csvName.toLowerCase() === product.name.toLowerCase()) {
          csvProduct = csvData;
          console.log(`Found case-insensitive name match in CSV for: ${product.name}`);
          break;
        }
      }
    }
    
    // If no match by name, try partial match
    if (!csvProduct) {
      for (const [csvName, csvData] of productOptionsMap.entries()) {
        if (product.name.includes(csvName) || csvName.includes(product.name)) {
          csvProduct = csvData;
          console.log(`Found partial name match in CSV: ${csvName} for product: ${product.name}`);
          break;
        }
      }
    }
    
    if (csvProduct) {
      // Create update object
      const updates = {};
      
      // Check each option field
      for (let i = 1; i <= 3; i++) {
        const typeKey = `option_type${i}`;
        const nameKey = `option_name${i}`;
        
        if (product[typeKey] === 'DROP_DOWN') {
          // Find matching option in CSV data
          const optionName = product[nameKey];
          const csvOption = csvProduct.options.find(opt => opt.name === optionName);
          
          if (csvOption) {
            // Replace 'DROP_DOWN' with the actual option values
            updates[typeKey] = csvOption.values.join(';');
            console.log(`Updating ${typeKey} for ${product.name} from 'DROP_DOWN' to: ${csvOption.values.join(';')}`);
          }
        }
      }
      
      if (Object.keys(updates).length > 0) {
        // Update the product in the database
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skippedCount++;
        } else {
          console.log(`Successfully updated options for: ${product.name}`);
          updatedCount++;
        }
      } else {
        console.log(`No updates needed for: ${product.name}`);
        skippedCount++;
      }
    } else {
      console.log(`Could not find matching product in CSV for: ${product.name}`);
      
      // If we can't find a match in the CSV, but the product has "DROP_DOWN" as an option type,
      // we'll check if it already has option_description values we can use
      const updates = {};
      
      for (let i = 1; i <= 3; i++) {
        const typeKey = `option_type${i}`;
        const descKey = `option_description${i}`;
        
        if (product[typeKey] === 'DROP_DOWN' && product[descKey]) {
          // Use the description as the option values
          updates[typeKey] = product[descKey];
          console.log(`Using existing description as option values for ${product.name}: ${product[descKey]}`);
        }
      }
      
      if (Object.keys(updates).length > 0) {
        // Update the product in the database
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skippedCount++;
        } else {
          console.log(`Successfully updated options for: ${product.name} using existing descriptions`);
          updatedCount++;
        }
      } else {
        skippedCount++;
      }
    }
  }
  
  console.log('\nUpdate complete!');
  console.log(`Updated ${updatedCount} products with options.`);
  console.log(`Skipped ${skippedCount} products that couldn't be updated.`);
}

// Main function
async function main() {
  try {
    await readProductOptionsFromCSV();
    await fixProductOptions();
  } catch (error) {
    console.error('Error running script:', error);
  }
}

// Run the script
main();
