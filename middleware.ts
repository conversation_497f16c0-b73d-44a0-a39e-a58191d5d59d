// Next.js Middleware for Multi-Tenant Routing
// Handles tenant detection, routing, and context setting

import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { TenantUtils } from './lib/utils/tenant';

/**
 * Multi-tenant middleware configuration
 */
const TENANT_CONFIG = {
  // Base domain for the application
  baseDomain: process.env.NEXT_PUBLIC_BASE_DOMAIN || 'bitsnbongs.com',
  
  // Default tenant (for main domain)
  defaultTenant: 'bitsnbongs',
  
  // Protected routes that require authentication
  protectedRoutes: [
    '/dashboard',
    '/admin',
    '/settings',
    '/analytics',
    '/orders',
    '/products/manage'
  ],
  
  // Public routes that don't require tenant context
  publicRoutes: [
    '/auth',
    '/login',
    '/signup',
    '/forgot-password',
    '/reset-password',
    '/api/auth',
    '/_next',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml'
  ],
  
  // Admin-only routes
  adminRoutes: [
    '/admin',
    '/settings/billing',
    '/settings/users',
    '/analytics/advanced'
  ]
};

/**
 * Extract tenant information from request
 */
function extractTenantInfo(request: NextRequest) {
  const host = request.headers.get('host') || '';
  const pathname = request.nextUrl.pathname;
  
  // Extract tenant from subdomain or custom domain
  let tenantSlug = TenantUtils.extractTenantFromHost(host);
  
  // If no tenant from host, check for tenant in path (fallback)
  if (!tenantSlug && pathname.startsWith('/t/')) {
    const pathParts = pathname.split('/');
    tenantSlug = pathParts[2];
  }
  
  // Use default tenant if none found
  if (!tenantSlug) {
    tenantSlug = TENANT_CONFIG.defaultTenant;
  }
  
  return {
    slug: tenantSlug,
    isSubdomain: host.includes('.') && !host.startsWith('www.'),
    isCustomDomain: !host.includes(TENANT_CONFIG.baseDomain) && !host.includes('localhost'),
    host,
    pathname
  };
}

/**
 * Check if route is public (doesn't require authentication)
 */
function isPublicRoute(pathname: string): boolean {
  return TENANT_CONFIG.publicRoutes.some(route => 
    pathname.startsWith(route)
  );
}

/**
 * Check if route is protected (requires authentication)
 */
function isProtectedRoute(pathname: string): boolean {
  return TENANT_CONFIG.protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
}

/**
 * Check if route requires admin access
 */
function isAdminRoute(pathname: string): boolean {
  return TENANT_CONFIG.adminRoutes.some(route => 
    pathname.startsWith(route)
  );
}

/**
 * Main middleware function
 */
export async function middleware(request: NextRequest) {
  const tenantInfo = extractTenantInfo(request);
  const { pathname } = request.nextUrl;
  
  // Skip middleware for public assets and API routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }
  
  // Create Supabase client for middleware
  const response = NextResponse.next();
  const supabase = createMiddlewareClient({ req: request, res: response });
  
  try {
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session error:', sessionError);
    }
    
    // Handle authentication for protected routes
    if (isProtectedRoute(pathname)) {
      if (!session) {
        // Redirect to login with return URL
        const loginUrl = new URL('/auth/login', request.url);
        loginUrl.searchParams.set('returnTo', pathname);
        return NextResponse.redirect(loginUrl);
      }
    }
    
    // Validate tenant exists and user has access
    if (session && tenantInfo.slug !== TENANT_CONFIG.defaultTenant) {
      try {
        // Check if tenant exists
        const { data: tenant, error: tenantError } = await supabase
          .from('tenants')
          .select('id, name, slug, status, domain, subdomain')
          .eq('slug', tenantInfo.slug)
          .single();
        
        if (tenantError || !tenant) {
          // Tenant not found, redirect to tenant selection or 404
          if (isProtectedRoute(pathname)) {
            return NextResponse.redirect(new URL('/dashboard/select-tenant', request.url));
          } else {
            return NextResponse.redirect(new URL('/404', request.url));
          }
        }
        
        // Check if tenant is active
        if (tenant.status !== 'active') {
          return NextResponse.redirect(new URL('/tenant-suspended', request.url));
        }
        
        // Check user access to tenant for protected routes
        if (isProtectedRoute(pathname)) {
          const { data: hasAccess } = await supabase.rpc('user_has_tenant_access', {
            user_uuid: session.user.id,
            tenant_uuid: tenant.id
          });
          
          if (!hasAccess) {
            return NextResponse.redirect(new URL('/unauthorized', request.url));
          }
          
          // Check admin access for admin routes
          if (isAdminRoute(pathname)) {
            const { data: userTenants } = await supabase.rpc('get_user_tenants', {
              user_uuid: session.user.id
            });
            
            const userTenant = userTenants?.find((ut: any) => ut.tenant_id === tenant.id);
            const isAdmin = userTenant?.role === 'owner' || userTenant?.role === 'admin';
            
            if (!isAdmin) {
              return NextResponse.redirect(new URL('/dashboard', request.url));
            }
          }
          
          // Set tenant context in database session
          await supabase.rpc('set_tenant_context', {
            tenant_uuid: tenant.id
          });
        }
        
        // Add tenant info to response headers
        response.headers.set('x-tenant-id', tenant.id);
        response.headers.set('x-tenant-slug', tenant.slug);
        response.headers.set('x-tenant-name', tenant.name);
      } catch (error) {
        console.error('Tenant validation error:', error);
        
        // On error, redirect to safe page
        if (isProtectedRoute(pathname)) {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }
    }
    
    // Handle subdomain routing
    if (tenantInfo.isSubdomain && tenantInfo.slug !== TENANT_CONFIG.defaultTenant) {
      // Rewrite URL to include tenant context
      const url = request.nextUrl.clone();
      url.pathname = `/t/${tenantInfo.slug}${pathname}`;
      return NextResponse.rewrite(url);
    }
    
    // Handle custom domain routing
    if (tenantInfo.isCustomDomain) {
      // For custom domains, we need to look up the tenant by domain
      if (session) {
        try {
          const { data: tenant } = await supabase
            .from('tenants')
            .select('id, slug')
            .eq('domain', tenantInfo.host)
            .single();
          
          if (tenant) {
            const url = request.nextUrl.clone();
            url.pathname = `/t/${tenant.slug}${pathname}`;
            response.headers.set('x-tenant-id', tenant.id);
            response.headers.set('x-tenant-slug', tenant.slug);
            return NextResponse.rewrite(url);
          }
        } catch (error) {
          console.error('Custom domain lookup error:', error);
        }
      }
    }
    
    return response;
    
  } catch (error) {
    console.error('Middleware error:', error);
    
    // On critical error, allow request to continue
    return NextResponse.next();
  }
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

/**
 * Utility functions for use in API routes and pages
 */
export class MiddlewareUtils {
  /**
   * Get tenant info from request headers (set by middleware)
   */
  static getTenantFromHeaders(request: NextRequest | Request) {
    const headers = request.headers;
    return {
      id: headers.get('x-tenant-id'),
      slug: headers.get('x-tenant-slug'),
      name: headers.get('x-tenant-name')
    };
  }
  
  /**
   * Get tenant info from Next.js request
   */
  static getTenantFromNextRequest(req: any) {
    return {
      id: req.headers['x-tenant-id'],
      slug: req.headers['x-tenant-slug'],
      name: req.headers['x-tenant-name']
    };
  }
  
  /**
   * Validate tenant access in API routes
   */
  static async validateTenantAccess(
    supabase: any,
    userId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const { data } = await supabase.rpc('user_has_tenant_access', {
        user_uuid: userId,
        tenant_uuid: tenantId
      });
      return data || false;
    } catch (error) {
      console.error('Tenant access validation error:', error);
      return false;
    }
  }
  
  /**
   * Set tenant context in API routes
   */
  static async setTenantContext(
    supabase: any,
    tenantId: string
  ): Promise<void> {
    try {
      await supabase.rpc('set_tenant_context', {
        tenant_uuid: tenantId
      });
    } catch (error) {
      console.error('Set tenant context error:', error);
      throw new Error('Failed to set tenant context');
    }
  }
}

export default middleware;