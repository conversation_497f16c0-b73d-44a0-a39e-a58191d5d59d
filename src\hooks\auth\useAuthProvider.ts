import { useState, useEffect, useCallback, useRef } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Profile } from '@/types/database';
import { toast } from '@/components/ui/use-toast';

export const useAuthProvider = () => {
  // State declarations
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isInitializing, setIsInitializing] = useState<boolean>(true);
  
  // Use a ref to prevent multiple initialization calls
  const initialized = useRef(false);

  // Separate function to fetch user data
  const fetchUserData = useCallback(async (userId: string) => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      console.log('Fetching user data for ID:', userId);
      
      // Fetch admin status
      const { data: adminStatus, error: adminError } = await supabase
        .rpc('is_admin', { user_id: userId });
      
      if (adminError) {
        console.error('Error checking admin status:', adminError);
      } else {
        console.log('Admin status result:', adminStatus);
        setIsAdmin(!!adminStatus);
      }
      
      // Fetch profile data
      const { data: profileData, error: profileError } = await supabase
        .rpc('get_profile', { user_id: userId });
      
      if (profileError) {
        console.error('Error fetching profile:', profileError);
      } else if (profileData && profileData.length > 0) {
        console.log('Profile data received:', profileData[0]);
        setProfile(profileData[0] as Profile);
      } else {
        console.log('No profile data found');
      }
    } catch (error) {
      console.error('Error in fetchUserData:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    if (initialized.current) {
      return;
    }
    
    initialized.current = true;
    
    const initAuth = async () => {
      try {
        console.log('Setting up auth state listener');
        
        // Set up auth state change listener first
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, currentSession) => {
            console.log('Auth event:', event);
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            
            if (currentSession?.user) {
              // Use setTimeout to prevent recursion issues
              setTimeout(() => {
                fetchUserData(currentSession.user.id);
              }, 0);
            } else {
              setProfile(null);
              setIsAdmin(false);
            }
          }
        );
        
        // Then check for initial session - AFTER setting up listener
        console.log('Checking for existing session');
        const { data } = await supabase.auth.getSession();
        
        // Only if we don't already have a user from onAuthStateChange
        if (!user) {
          setSession(data.session);
          setUser(data.session?.user ?? null);
          
          if (data.session?.user) {
            await fetchUserData(data.session.user.id);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        console.log('Auth initialization completed');
        setIsInitializing(false);
      }
    };
    
    initAuth();
    
    return () => {
      // Cleanup function
    };
  }, [fetchUserData, user]);

  // Authentication methods
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      
      toast({
        title: 'Welcome back!',
        description: 'You have successfully signed in.',
      });
    } catch (error: any) {
      toast({
        title: 'Error signing in',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) throw error;
      
      toast({
        title: 'Account created!',
        description: 'Please verify your email to continue.',
      });
    } catch (error: any) {
      toast({
        title: 'Error signing up',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast({
        title: 'Signed out',
        description: 'You have been signed out successfully.',
      });
    } catch (error: any) {
      toast({
        title: 'Error signing out',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: Partial<Profile>) => {
    try {
      setIsLoading(true);
      
      if (!user) throw new Error('No user logged in');

      // Use RPC function to update profile securely
      const { error } = await supabase.rpc('update_user_profile', {
        p_first_name: data.first_name,
        p_last_name: data.last_name,
        p_address: data.address,
        p_city: data.city,
        p_postal_code: data.postal_code,
        p_country: data.country
      });

      if (error) throw error;
      
      // Refresh profile
      fetchUserData(user.id);
      
      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      });
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast({
        title: 'Error updating profile',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    user,
    profile,
    isLoading,
    isAdmin,
    isInitializing,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };
};
