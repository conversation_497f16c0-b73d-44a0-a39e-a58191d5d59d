/**
 * Image handling service for dealing with CORS issues
 * This module provides utilities to handle images in a way that avoids CORS restrictions
 */

// Import the Canvas API utility for direct image conversion
import { convertImageToDataUri, generateDataUriImage } from './directImageUrl';

// Track cache of converted images
interface ImageCache {
  [url: string]: {
    dataUri: string;
    timestamp: number;
  };
}

// Simple in-memory cache for converted images
const imageCache: ImageCache = {};
const CACHE_EXPIRY = 3600000; // 1 hour in milliseconds

/**
 * Check if a URL is an image URL
 * @param url The URL to check
 * @returns Boolean indicating if the URL is likely an image
 */
export function isImageUrl(url: string): boolean {
  if (!url) return false;
  
  // Check for common image extensions
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
  const lowercaseUrl = url.toLowerCase();
  
  return imageExtensions.some(ext => lowercaseUrl.endsWith(ext)) || 
         lowercaseUrl.includes('/image') || 
         lowercaseUrl.includes('/photo');
}

/**
 * Check if a domain is likely to have CORS issues
 * @param url The URL to check
 * @returns Boolean indicating if the domain might have CORS issues
 */
export function mightHaveCorsIssues(url: string): boolean {
  if (!url || url.startsWith('data:')) return false;
  
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    
    // List of domains that we know often have CORS issues
    const problematicDomains = [
      'pixabay.com',
      'cdn.pixabay.com',
      'unsplash.com',
      'images.unsplash.com',
      'pexels.com',
      'images.pexels.com'
    ];
    
    return problematicDomains.some(domain => 
      hostname === domain || hostname.endsWith(`.${domain}`)
    );
  } catch (error) {
    console.error('Error checking domain for CORS issues:', error);
    return true; // Assume issues if we can't parse the URL
  }
}

/**
 * Get a cached data URI for an image if available
 * @param imageUrl The original image URL
 * @returns The cached data URI or null if not cached
 */
export function getCachedImage(imageUrl: string): string | null {
  const cached = imageCache[imageUrl];
  
  if (cached) {
    const now = Date.now();
    
    // Check if the cache entry is still valid
    if (now - cached.timestamp < CACHE_EXPIRY) {
      return cached.dataUri;
    } else {
      // Remove expired cache entry
      delete imageCache[imageUrl];
    }
  }
  
  return null;
}

/**
 * Cache a data URI for an image URL
 * @param imageUrl The original image URL
 * @param dataUri The data URI to cache
 */
export function cacheImage(imageUrl: string, dataUri: string): void {
  imageCache[imageUrl] = {
    dataUri,
    timestamp: Date.now()
  };
  
  // Clean up old cache entries if cache gets too large
  const MAX_CACHE_SIZE = 50;
  const cacheKeys = Object.keys(imageCache);
  
  if (cacheKeys.length > MAX_CACHE_SIZE) {
    // Sort by timestamp (oldest first)
    cacheKeys.sort((a, b) => imageCache[a].timestamp - imageCache[b].timestamp);
    
    // Remove the oldest entries
    const keysToRemove = cacheKeys.slice(0, cacheKeys.length - MAX_CACHE_SIZE);
    keysToRemove.forEach(key => delete imageCache[key]);
  }
}

/**
 * Get a safe image URL that avoids CORS issues
 * @param imageUrl The original image URL
 * @returns A promise resolving to a safe image URL (data URI or original URL)
 */
export async function getSafeImageUrl(imageUrl: string): Promise<string> {
  // Don't process if the URL is already a data URI
  if (!imageUrl || imageUrl.startsWith('data:')) {
    return imageUrl;
  }
  
  // Check cache first
  const cached = getCachedImage(imageUrl);
  if (cached) {
    return cached;
  }
  
  // If the URL might have CORS issues, try to convert it to a data URI
  if (mightHaveCorsIssues(imageUrl)) {
    try {
      const dataUri = await convertImageToDataUri(imageUrl);
      cacheImage(imageUrl, dataUri);
      return dataUri;
    } catch (error) {
      console.warn(`Failed to convert image to data URI: ${imageUrl}`, error);
      // Fall back to original URL and let the browser try to load it
      return imageUrl;
    }
  }
  
  // Return the original URL if no conversion is needed
  return imageUrl;
}

/**
 * Generate a fallback image as a data URI when other methods fail
 * @param text Text to display in the fallback image
 * @returns A data URI string containing an SVG image
 */
export function generateFallbackImage(text: string = 'Image Unavailable'): string {
  return generateDataUriImage(text);
}

/**
 * Legacy function to maintain compatibility with existing code
 * @param imageUrl The original image URL
 * @param fallbackText Optional text for fallback image
 * @returns The image URL or a fallback data URI
 */
export function getProxiedImageUrl(imageUrl: string, fallbackText?: string): string {
  if (!imageUrl) {
    return generateFallbackImage(fallbackText || 'No Image URL');
  }
  
  // If it's already a data URI, return as is
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }
  
  // For invalid URLs, return a fallback
  try {
    new URL(imageUrl);
    return imageUrl; // Return the original URL and let getSafeImageUrl handle it asynchronously
  } catch (error) {
    console.error('Invalid image URL:', imageUrl);
    return generateFallbackImage(fallbackText || 'Invalid Image URL');
  }
}
