/**
 * Utility to migrate product images from external URLs to Supabase storage
 * This script can be run to ensure all product images are stored in Supabase
 */

import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";
import { v4 as uuidv4 } from "uuid";

/**
 * Check if a URL is from Supabase storage
 */
export function isSupabaseStorageUrl(url: string): boolean {
  return url?.includes('supabase.co/storage');
}

/**
 * Save an image from a URL to Supabase storage
 */
export async function saveImageToStorage(imageUrl: string, imageName: string): Promise<string> {
  try {
    console.log(`Migrating image to storage: ${imageName}`);
    
    // Skip if the image is already from Supabase storage
    if (isSupabaseStorageUrl(imageUrl)) {
      console.log('Image is already in Supabase storage:', imageUrl);
      return imageUrl;
    }
    
    // Create a safe filename
    const safeImageName = imageName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
    const fileName = `${safeImageName}-${Math.random().toString(36).substring(2, 8)}.jpg`;
    const filePath = `product-images/${fileName}`;
    
    // Direct fetch method
    console.log('Fetching image directly:', imageUrl);
    
    // Try direct fetch first
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }
    
    // Check if the response is an image
    const contentType = response.headers.get('Content-Type');
    if (!contentType || !contentType.startsWith('image/')) {
      throw new Error('The URL did not return a valid image');
    }
    
    // Convert to blob
    const blob = await response.blob();
    
    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('product-images')
      .upload(filePath, blob, { contentType });
    
    if (uploadError) throw uploadError;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(filePath);
    
    console.log(`Image saved successfully: ${publicUrl}`);
    return publicUrl;
    
  } catch (error) {
    console.error('Error saving image to storage:', error);
    
    // Try using a CORS proxy as fallback
    try {
      console.log('Trying with CORS proxy');
      const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(imageUrl)}`;
      
      const proxyResponse = await fetch(proxyUrl);
      if (!proxyResponse.ok) {
        throw new Error(`Proxy fetch failed: ${proxyResponse.statusText}`);
      }
      
      const blob = await proxyResponse.blob();
      const safeImageName = imageName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const fileName = `${safeImageName}-proxy-${Math.random().toString(36).substring(2, 8)}.jpg`;
      const filePath = `product-images/${fileName}`;
      
      const { error: uploadError } = await supabase.storage
        .from('product-images')
        .upload(filePath, blob, { contentType: 'image/jpeg' });
      
      if (uploadError) throw uploadError;
      
      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);
      
      console.log(`Image saved via proxy: ${publicUrl}`);
      return publicUrl;
      
    } catch (proxyError) {
      console.error('Proxy method failed:', proxyError);
      
      // Return the original URL as fallback
      return imageUrl;
    }
  }
}

/**
 * Migrate a single product's images to Supabase storage
 */
export async function migrateProductImages(product: Product): Promise<Product> {
  if (!product) return product;
  
  try {
    console.log(`Migrating images for product: ${product.name} (${product.id})`);
    
    // Migrate main image if it exists and is not already in Supabase
    let updatedProduct = { ...product };
    
    if (product.image && !isSupabaseStorageUrl(product.image)) {
      console.log(`Migrating main image: ${product.image}`);
      const savedImageUrl = await saveImageToStorage(product.image, product.name || 'product');
      updatedProduct.image = savedImageUrl;
    }
    
    // Migrate additional images if they exist
    if (product.additional_images && Array.isArray(product.additional_images)) {
      const migratedAdditionalImages = await Promise.all(
        product.additional_images.map(async (imageUrl, index) => {
          if (!imageUrl || isSupabaseStorageUrl(imageUrl)) {
            return imageUrl;
          }
          
          console.log(`Migrating additional image ${index + 1}: ${imageUrl}`);
          return await saveImageToStorage(imageUrl, `${product.name || 'product'}-${index + 1}`);
        })
      );
      
      updatedProduct.additional_images = migratedAdditionalImages;
    }
    
    // Update the product in the database
    const { error } = await supabase
      .from('products')
      .update({
        image: updatedProduct.image,
        additional_images: updatedProduct.additional_images
      })
      .eq('id', product.id);
    
    if (error) {
      console.error(`Error updating product ${product.id}:`, error);
      throw error;
    }
    
    console.log(`Successfully migrated images for product: ${product.name}`);
    return updatedProduct;
    
  } catch (error) {
    console.error(`Error migrating images for product ${product.id}:`, error);
    return product;
  }
}

/**
 * Migrate all product images to Supabase storage
 */
export async function migrateAllProductImages(): Promise<{ 
  success: boolean; 
  migrated: number; 
  failed: number; 
  total: number;
}> {
  try {
    console.log('Starting migration of all product images to Supabase storage');
    
    // Fetch all products
    const { data: products, error } = await supabase
      .from('products')
      .select('*');
    
    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
    
    if (!products || products.length === 0) {
      console.log('No products found to migrate');
      return { success: true, migrated: 0, failed: 0, total: 0 };
    }
    
    console.log(`Found ${products.length} products to check for migration`);
    
    let migrated = 0;
    let failed = 0;
    
    // Process products in batches to avoid overwhelming the system
    const batchSize = 5;
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      console.log(`Processing batch ${i / batchSize + 1} of ${Math.ceil(products.length / batchSize)}`);
      
      await Promise.all(
        batch.map(async (product) => {
          try {
            // Check if any images need migration
            const needsMigration = 
              (product.image && !isSupabaseStorageUrl(product.image)) || 
              (product.additional_images && Array.isArray(product.additional_images) && 
                product.additional_images.some(url => url && !isSupabaseStorageUrl(url)));
            
            if (needsMigration) {
              await migrateProductImages(product);
              migrated++;
              console.log(`✅ Successfully migrated product: ${product.name}`);
            } else {
              console.log(`⏭️ Skipping product, no migration needed: ${product.name}`);
            }
          } catch (error) {
            console.error(`❌ Failed to migrate product ${product.id}:`, error);
            failed++;
          }
        })
      );
      
      // Small delay between batches to avoid rate limiting
      if (i + batchSize < products.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`Migration complete. Migrated: ${migrated}, Failed: ${failed}, Total: ${products.length}`);
    return { 
      success: true, 
      migrated, 
      failed, 
      total: products.length 
    };
    
  } catch (error) {
    console.error('Error in migrateAllProductImages:', error);
    return { 
      success: false, 
      migrated: 0, 
      failed: 0, 
      total: 0 
    };
  }
}
