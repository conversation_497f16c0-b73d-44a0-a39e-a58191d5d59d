/**
 * CSV Transformer for Variant-Based Product System
 * 
 * This script transforms a standard Wix-exported CSV into two separate CSVs:
 * 1. products.csv - Contains base product information
 * 2. variants.csv - Contains variant information
 * 
 * It also handles image filename transformations and flags products without images.
 * 
 * Usage:
 * 1. Place your input CSV in the data directory
 * 2. Run: npx ts-node src/scripts/transform-csv-for-variants.ts
 * 3. Find output CSVs in the data/output directory
 */

import * as fs from 'fs';
import * as path from 'path';
import * as Papa from 'papaparse';
import { v4 as uuidv4 } from 'uuid';
import slugify from 'slugify';

// Define configuration (could be moved to a config file)
const CONFIG = {
  inputDir: './data',
  outputDir: './data/output',
  inputFileName: 'products.csv',
  productsOutputFileName: 'products-transformed.csv',
  variantsOutputFileName: 'variants.csv',
};

// Define types
interface WixProduct {
  handleId: string;
  fieldType: string;
  name: string;
  description: string;
  productImageUrl: string;
  collection: string;
  sku: string;
  ribbon: string;
  price: string;
  surcharge: string;
  visible: string;
  discountMode: string;
  discountValue: string;
  inventory: string;
  weight: string;
  cost: string;
  brand: string;
  // Product options
  productOptionName1?: string;
  productOptionType1?: string;
  productOptionDescription1?: string;
  productOptionName2?: string;
  productOptionType2?: string;
  productOptionDescription2?: string;
  productOptionName3?: string;
  productOptionType3?: string;
  productOptionDescription3?: string;
  // Additional information
  additionalInfoTitle1?: string;
  additionalInfoDescription1?: string;
  additionalInfoTitle2?: string;
  additionalInfoDescription2?: string;
  additionalInfoTitle3?: string;
  additionalInfoDescription3?: string;
  // Any other fields
  [key: string]: any;
}

interface TransformedProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: string;
  sale_price: string | null;
  image: string | null;
  additional_images: string;
  category_id: string | null;
  brand_id: string | null;
  sku: string | null;
  stock_quantity: string;
  weight: string | null;
  in_stock: string;
  is_featured: string;
  is_new: string;
  is_active: string;
  option_definitions: string; // JSON stringified object
  created_at: string;
  updated_at: string;
  // Additional fields
  [key: string]: any;
}

interface ProductVariant {
  id: string;
  product_id: string;
  variant_name: string;
  sku: string | null;
  price: string;
  sale_price: string | null;
  stock_quantity: string;
  in_stock: string;
  image: string | null;
  option_combination: string; // JSON stringified object
  is_active: string;
  external_id: string | null;
  created_at: string;
  updated_at: string;
}

// Helper functions
function transformImageUrl(url: string): string {
  if (!url) return '';
  
  // Remove ~mv2 from image URLs
  url = url.replace(/~mv2/g, '');
  
  // Convert to .webp if it's a jpg or png
  if (url.match(/\.(jpg|jpeg|png)$/i)) {
    url = url.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  }
  
  return url;
}

function extractOptionValues(optionDescription: string): string[] {
  if (!optionDescription) return [];
  
  // Split by semicolons, commas, or pipes
  const values = optionDescription.split(/[;,|]/).map(v => v.trim()).filter(Boolean);
  
  return values;
}

function parsePrice(price: string): number {
  if (!price) return 0;
  
  // Remove currency symbols and convert to number
  const cleanPrice = price.replace(/[£$€]/g, '').trim();
  return parseFloat(cleanPrice) || 0;
}

function generateSlug(name: string): string {
  return slugify(name, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
}

// Main transformation function
async function transformCsv() {
  console.log('Starting CSV transformation...');
  
  // Ensure output directory exists
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
  
  // Read input CSV
  const inputPath = path.join(CONFIG.inputDir, CONFIG.inputFileName);
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    return;
  }
  
  const inputCsv = fs.readFileSync(inputPath, 'utf8');
  
  // Parse CSV
  const { data } = Papa.parse<WixProduct>(inputCsv, {
    header: true,
    skipEmptyLines: true,
  });
  
  console.log(`Found ${data.length} rows in input CSV`);
  
  // Group products by name (base products)
  const productGroups = new Map<string, WixProduct[]>();
  
  data.forEach(row => {
    const name = row.name;
    if (!productGroups.has(name)) {
      productGroups.set(name, []);
    }
    productGroups.get(name)?.push(row);
  });
  
  console.log(`Grouped into ${productGroups.size} unique products`);
  
  // Transform products and create variants
  const transformedProducts: TransformedProduct[] = [];
  const productVariants: ProductVariant[] = [];
  
  productGroups.forEach((rows, productName) => {
    // Use the first row as the base product
    const baseRow = rows[0];
    const productId = uuidv4();
    const now = new Date().toISOString();
    
    // Extract option definitions
    const optionDefinitions: Record<string, string[]> = {};
    
    if (baseRow.productOptionName1 && baseRow.productOptionDescription1) {
      optionDefinitions[baseRow.productOptionName1] = extractOptionValues(baseRow.productOptionDescription1);
    }
    
    if (baseRow.productOptionName2 && baseRow.productOptionDescription2) {
      optionDefinitions[baseRow.productOptionName2] = extractOptionValues(baseRow.productOptionDescription2);
    }
    
    if (baseRow.productOptionName3 && baseRow.productOptionDescription3) {
      optionDefinitions[baseRow.productOptionName3] = extractOptionValues(baseRow.productOptionDescription3);
    }
    
    // Transform image URL
    const transformedImageUrl = transformImageUrl(baseRow.productImageUrl);
    
    // Create transformed product
    const transformedProduct: TransformedProduct = {
      id: productId,
      name: productName,
      slug: generateSlug(productName),
      description: baseRow.description || '',
      price: parsePrice(baseRow.price).toString(),
      sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
      image: transformedImageUrl || null,
      additional_images: '[]', // No additional images in base CSV
      category_id: null, // Will need to be set manually or in a separate process
      brand_id: null, // Will need to be set manually or in a separate process
      sku: baseRow.sku || null,
      stock_quantity: baseRow.inventory || '0',
      weight: baseRow.weight || null,
      in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
      is_featured: 'false',
      is_new: 'false',
      is_active: transformedImageUrl ? 'true' : 'false', // Flag products without images as inactive
      option_definitions: JSON.stringify(optionDefinitions),
      created_at: now,
      updated_at: now,
    };
    
    transformedProducts.push(transformedProduct);
    
    // Create variants
    if (Object.keys(optionDefinitions).length > 0) {
      // If we have option definitions, create variants
      const optionNames = Object.keys(optionDefinitions);
      
      // For simple cases with just one option
      if (optionNames.length === 1) {
        const optionName = optionNames[0];
        const values = optionDefinitions[optionName];
        
        values.forEach(value => {
          const variantId = uuidv4();
          const optionCombination = { [optionName]: value };
          
          const variant: ProductVariant = {
            id: variantId,
            product_id: productId,
            variant_name: `${productName} - ${value}`,
            sku: baseRow.sku ? `${baseRow.sku}-${value.substring(0, 2)}` : null,
            price: baseRow.price,
            sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
            stock_quantity: baseRow.inventory || '0',
            in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
            image: transformedImageUrl || null,
            option_combination: JSON.stringify(optionCombination),
            is_active: transformedImageUrl ? 'true' : 'false',
            external_id: baseRow.handleId || null,
            created_at: now,
            updated_at: now,
          };
          
          productVariants.push(variant);
        });
      } 
      // For products with multiple options, we'd need to create combinations
      // This is a simplified approach - for real data you might need more complex logic
      else if (optionNames.length > 1) {
        // Create a default variant
        const variantId = uuidv4();
        const optionCombination = optionNames.reduce((acc, name) => {
          acc[name] = optionDefinitions[name][0] || '';
          return acc;
        }, {} as Record<string, string>);
        
        const variant: ProductVariant = {
          id: variantId,
          product_id: productId,
          variant_name: `${productName} - Default`,
          sku: baseRow.sku || null,
          price: baseRow.price,
          sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
          stock_quantity: baseRow.inventory || '0',
          in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
          image: transformedImageUrl || null,
          option_combination: JSON.stringify(optionCombination),
          is_active: transformedImageUrl ? 'true' : 'false',
          external_id: baseRow.handleId || null,
          created_at: now,
          updated_at: now,
        };
        
        productVariants.push(variant);
      }
    } else {
      // If no option definitions, create a default variant
      const variantId = uuidv4();
      
      const variant: ProductVariant = {
        id: variantId,
        product_id: productId,
        variant_name: productName,
        sku: baseRow.sku || null,
        price: baseRow.price,
        sale_price: baseRow.discountValue ? (parsePrice(baseRow.price) - parsePrice(baseRow.discountValue)).toString() : null,
        stock_quantity: baseRow.inventory || '0',
        in_stock: (parseInt(baseRow.inventory || '0') > 0).toString(),
        image: transformedImageUrl || null,
        option_combination: '{}',
        is_active: transformedImageUrl ? 'true' : 'false',
        external_id: baseRow.handleId || null,
        created_at: now,
        updated_at: now,
      };
      
      productVariants.push(variant);
    }
  });
  
  // Write output CSVs
  const productsOutputPath = path.join(CONFIG.outputDir, CONFIG.productsOutputFileName);
  const variantsOutputPath = path.join(CONFIG.outputDir, CONFIG.variantsOutputFileName);
  
  const productsOutput = Papa.unparse(transformedProducts);
  const variantsOutput = Papa.unparse(productVariants);
  
  fs.writeFileSync(productsOutputPath, productsOutput);
  fs.writeFileSync(variantsOutputPath, variantsOutput);
  
  console.log(`Transformation complete!`);
  console.log(`- Products CSV: ${productsOutputPath}`);
  console.log(`- Variants CSV: ${variantsOutputPath}`);
  console.log(`- ${transformedProducts.length} products and ${productVariants.length} variants created`);
  
  // Stats
  const productsWithoutImages = transformedProducts.filter(p => p.is_active === 'false').length;
  console.log(`- ${productsWithoutImages} products flagged as inactive (no images)`);
}

// Run the transformation
transformCsv().catch(err => {
  console.error('Error during transformation:', err);
  process.exit(1);
});
