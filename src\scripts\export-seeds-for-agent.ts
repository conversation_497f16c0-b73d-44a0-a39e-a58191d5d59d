// Export seed products for Super Agent data enrichment
// This script exports all seed products (active and inactive) to CSV format
// for the Super Agent to scrape missing data and descriptions

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface SeedProduct {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  category_id: string | null;
  brand_id: string | null;
  price: number;
  image: string | null;
  created_at: string;
}

interface SeedAttributes {
  product_id: string;
  seed_type: string | null;
  flowering_time: string | null;
  yield: string | null;
  thc_level: string | null;
  cbd_level: string | null;
  effect: string | null;
  seed_family: string | null;
  is_manually_verified: boolean;
}

// Function to identify seed products by name patterns
function isSeedProduct(name: string): boolean {
  const lowerName = name.toLowerCase();

  // NEGATIVE keywords - if ANY of these are found, it's NOT a seed
  const excludeKeywords = [
    'rolling', 'papers', 'bong', 'pipe', 'grinder', 'lighter', 'ashtray',
    'vaporizer', 'vape', 'oil', 'wax', 'shatter', 'rosin', 'hash',
    'edible', 'gummy', 'chocolate', 'cookie', 'brownie', 'drink',
    'tincture', 'capsule', 'topical', 'cream', 'balm', 'lotion',
    'clothing', 'shirt', 't-shirt', 'hoodie', 'hat', 'cap', 'bag',
    'sticker', 'poster', 'book', 'magazine', 'dvd', 'cd',
    'scale', 'container', 'jar', 'storage', 'rolling tray', 'mat',
    'cleaner', 'cleaning', 'solution', 'alcohol', 'brush', 'tool',
    'filter', 'screen', 'mesh', 'net', 'carbon', 'fan', 'light',
    'tent', 'grow light', 'nutrient', 'fertilizer', 'soil', 'pot',
    'hydroponic', 'ph meter', 'thermometer', 'humidity', 'timer',
    'glass', 'ceramic', 'metal', 'plastic', 'silicone', 'wood'
  ];

  // First check for exclusions - if found, definitely not a seed
  if (excludeKeywords.some(keyword => lowerName.includes(keyword))) {
    return false;
  }

  // Direct seed indicators
  const directSeedKeywords = [
    'seed', 'seeds', 'auto', 'feminised', 'feminized', 'regular',
    'strain', 'cannabis seeds', 'hemp seeds', 'autoflower', 'photoperiod'
  ];

  // Common strain names that indicate seeds
  const strainNames = [
    'gorilla glue', 'og kush', 'white widow', 'northern lights', 'blue dream',
    'girl scout cookies', 'sour diesel', 'ak-47', 'jack herer', 'amnesia',
    'cheese', 'skunk', 'haze', 'kush', 'diesel', 'cookies', 'glue',
    'widow', 'dream', 'lights', 'berry', 'lemon', 'purple', 'white',
    'blue', 'green', 'gold', 'jack', 'gelato', 'zkittlez', 'cherry',
    'strawberry', 'grape', 'orange', 'banana', 'mango', 'pineapple',
    'candy', 'sweet', 'sour', 'critical', 'big bud', 'power plant',
    'chocolate', 'vanilla', 'caramel', 'mint', 'pine', 'citrus'
  ];

  // Seed-specific terms that indicate it's a seed product
  const seedTerms = [
    'female', 'male', 'pack', 'x1', 'x3', 'x5', 'x10', 'x12',
    '(1)', '(3)', '(5)', '(10)', '(12)', '1 seed', '3 seeds', '5 seeds', '10 seeds',
    'indica', 'sativa', 'hybrid', 'autoflowering', 'feminised', 'feminized'
  ];

  // Known seed breeders/brands
  const seedBreeders = [
    "barney's farm", "barney's", 'paradise seeds', 'paradise', 'dutch passion',
    'sensi seeds', 'sensi', 'greenhouse seeds', 'greenhouse', 'royal queen seeds',
    'royal queen', 'fastbuds', '420 fastbuds', 'dinafem', 'sweet seeds',
    'humboldt seed', 'dna genetics', 'serious seeds', 'mr nice', 'subcool',
    'emerald triangle', 'cali connection', 'reserva privada', 'advanced seeds',
    'world of seeds', 'bomb seeds', 'big buddha', 'nirvana', 'white label'
  ];

  // Check for direct seed keywords
  if (directSeedKeywords.some(keyword => lowerName.includes(keyword))) {
    return true;
  }

  // Check for strain names combined with seed terms
  const hasStrainName = strainNames.some(strain => lowerName.includes(strain));
  const hasSeedTerm = seedTerms.some(term => lowerName.includes(term));

  if (hasStrainName && hasSeedTerm) {
    return true;
  }

  // Check for known seed breeders
  if (seedBreeders.some(breeder => lowerName.includes(breeder))) {
    return true;
  }

  // Check for common seed naming patterns
  const seedPatterns = [
    /\d+\s*(female|feminised|feminized|regular|auto)/i,
    /\(.*?(female|feminised|feminized|regular|auto).*?\)/i,
    /x\d+/i, // x5, x10 etc
    /(pack|seeds?)\s*of\s*\d+/i,
    /\d+\s*(pack|seeds?)/i,
    /(auto|feminised|feminized)\s+\w+/i, // "Auto Gorilla" etc
    /\w+\s+(auto|feminised|feminized)/i   // "Gorilla Auto" etc
  ];

  if (seedPatterns.some(pattern => pattern.test(name))) {
    return true;
  }

  return false;
}

// Function to determine what data needs to be enriched
function analyzeDataNeeds(product: SeedProduct, attributes: SeedAttributes | null) {
  const hasValidImage = product.image &&
    product.image !== 'null' &&
    product.image !== '' &&
    !product.image.includes('placeholder') &&
    !product.image.includes('no-image');

  const needsPriceResearch = product.is_active && (
    !product.price ||
    product.price <= 0 ||
    product.price > 200 // Unusually high, might need verification
  );

  const needs = {
    needs_description: !product.description || product.description.trim().length < 50,
    needs_image: !hasValidImage,
    needs_price_research: needsPriceResearch,
    needs_seed_type: !attributes?.seed_type,
    needs_flowering_time: !attributes?.flowering_time,
    needs_yield: !attributes?.yield,
    needs_thc_level: !attributes?.thc_level,
    needs_cbd_level: !attributes?.cbd_level,
    needs_effect: !attributes?.effect,
    needs_seed_family: !attributes?.seed_family,
    needs_any_attributes: false
  };

  needs.needs_any_attributes = needs.needs_seed_type || needs.needs_flowering_time ||
    needs.needs_yield || needs.needs_thc_level || needs.needs_cbd_level ||
    needs.needs_effect || needs.needs_seed_family;

  return needs;
}

// Function to escape CSV values
function escapeCsvValue(value: any): string {
  if (value === null || value === undefined) return '';

  const stringValue = String(value);
  // If the value contains commas, quotes, or newlines, wrap it in quotes and escape internal quotes
  if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }
  return stringValue;
}

async function exportSeedsForAgent() {
  try {
    console.log('🌱 Starting seed products export for Super Agent...');

    // Get all products (we'll filter for seeds)
    console.log('📊 Fetching all products...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, description, is_active, category_id, brand_id, price, image, created_at')
      .order('name');

    if (productsError) {
      throw new Error(`Error fetching products: ${productsError.message}`);
    }

    console.log(`📦 Found ${products?.length || 0} total products`);

    // Filter for seed products
    const seedProducts = products?.filter(product => isSeedProduct(product.name)) || [];
    console.log(`🌱 Identified ${seedProducts.length} seed products`);

    // Get existing seed attributes
    console.log('🔍 Fetching existing seed attributes...');
    const { data: attributes, error: attributesError } = await supabase
      .from('seed_product_attributes')
      .select('*');

    if (attributesError) {
      console.warn(`Warning: Could not fetch seed attributes: ${attributesError.message}`);
    }

    // Create a map of product_id to attributes
    const attributesMap = new Map<string, SeedAttributes>();
    if (attributes) {
      attributes.forEach(attr => {
        attributesMap.set(attr.product_id, attr);
      });
    }

    // Prepare CSV data
    const csvHeaders = [
      'product_id',
      'product_name',
      'current_description',
      'is_active',
      'current_price_gbp',
      'current_image_url',
      'has_image',
      'current_seed_type',
      'current_flowering_time',
      'current_yield',
      'current_thc_level',
      'current_cbd_level',
      'current_effect',
      'current_seed_family',
      'needs_description',
      'needs_image',
      'needs_price_research',
      'needs_seed_type',
      'needs_flowering_time',
      'needs_yield',
      'needs_thc_level',
      'needs_cbd_level',
      'needs_effect',
      'needs_seed_family',
      'needs_any_attributes',
      'priority_level',
      'agent_notes'
    ];

    const csvRows: string[] = [csvHeaders.join(',')];

    let activeCount = 0;
    let inactiveCount = 0;
    let needsDescriptionCount = 0;
    let needsAttributesCount = 0;
    let needsImageCount = 0;
    let needsPriceResearchCount = 0;

    // Process each seed product
    for (const product of seedProducts) {
      const attributes = attributesMap.get(product.id);
      const needs = analyzeDataNeeds(product, attributes);

      // Count statistics
      if (product.is_active) activeCount++;
      else inactiveCount++;

      if (needs.needs_description) needsDescriptionCount++;
      if (needs.needs_any_attributes) needsAttributesCount++;
      if (needs.needs_image) needsImageCount++;
      if (needs.needs_price_research) needsPriceResearchCount++;

      // Determine priority level
      let priority = 'LOW';
      if (product.is_active && (needs.needs_description || needs.needs_any_attributes || needs.needs_image)) {
        priority = 'HIGH';
      } else if (product.is_active) {
        priority = 'MEDIUM';
      }

      // Generate agent notes
      const notes = [];
      if (needs.needs_description) notes.push('Missing description');
      if (needs.needs_any_attributes) notes.push('Missing attributes');
      if (needs.needs_image) notes.push('Missing/poor image');
      if (needs.needs_price_research) notes.push('Price research needed');
      if (!product.is_active) notes.push('Inactive product');

      const row = [
        escapeCsvValue(product.id),
        escapeCsvValue(product.name),
        escapeCsvValue(product.description),
        escapeCsvValue(product.is_active),
        escapeCsvValue(product.price),
        escapeCsvValue(product.image),
        escapeCsvValue(product.image ? 'TRUE' : 'FALSE'),
        escapeCsvValue(attributes?.seed_type),
        escapeCsvValue(attributes?.flowering_time),
        escapeCsvValue(attributes?.yield),
        escapeCsvValue(attributes?.thc_level),
        escapeCsvValue(attributes?.cbd_level),
        escapeCsvValue(attributes?.effect),
        escapeCsvValue(attributes?.seed_family),
        escapeCsvValue(needs.needs_description),
        escapeCsvValue(needs.needs_image),
        escapeCsvValue(needs.needs_price_research),
        escapeCsvValue(needs.needs_seed_type),
        escapeCsvValue(needs.needs_flowering_time),
        escapeCsvValue(needs.needs_yield),
        escapeCsvValue(needs.needs_thc_level),
        escapeCsvValue(needs.needs_cbd_level),
        escapeCsvValue(needs.needs_effect),
        escapeCsvValue(needs.needs_seed_family),
        escapeCsvValue(needs.needs_any_attributes),
        escapeCsvValue(priority),
        escapeCsvValue(notes.join('; '))
      ];

      csvRows.push(row.join(','));
    }

    // Write CSV file
    const outputPath = path.join(__dirname, '../../exports/seeds-for-agent.csv');
    const exportDir = path.dirname(outputPath);

    // Create exports directory if it doesn't exist
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, csvRows.join('\n'), 'utf8');

    // Print summary
    console.log('\n✅ Export completed successfully!');
    console.log('📊 Summary:');
    console.log(`   Total seed products: ${seedProducts.length}`);
    console.log(`   Active products: ${activeCount}`);
    console.log(`   Inactive products: ${inactiveCount}`);
    console.log(`   Need descriptions: ${needsDescriptionCount}`);
    console.log(`   Need attributes: ${needsAttributesCount}`);
    console.log(`   Need images: ${needsImageCount}`);
    console.log(`   Need price research: ${needsPriceResearchCount}`);
    console.log(`\n📁 File saved to: ${outputPath}`);
    console.log('\n🤖 Ready for Super Agent processing!');

  } catch (error) {
    console.error('❌ Export failed:', error);
    process.exit(1);
  }
}

// Run the export
exportSeedsForAgent();
