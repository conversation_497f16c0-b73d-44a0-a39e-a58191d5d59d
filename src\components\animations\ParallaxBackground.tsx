
import { useRef, useEffect } from 'react';

interface ParallaxBackgroundProps {
  imageUrl: string;
  speed?: number;
  opacity?: number;
  className?: string;
}

const ParallaxBackground = ({ 
  imageUrl, 
  speed = 0.2, 
  opacity = 0.5,
  className = '' 
}: ParallaxBackgroundProps) => {
  const bgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!bgRef.current) return;
      const scrollPosition = window.scrollY;
      bgRef.current.style.transform = `translateY(${scrollPosition * speed}px)`;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div 
      ref={bgRef}
      className={`absolute inset-0 bg-cover bg-center transition-transform duration-200 ease-out ${className}`}
      style={{ 
        backgroundImage: `url(${imageUrl})`,
        opacity: opacity,
      }}
    />
  );
};

export default ParallaxBackground;
