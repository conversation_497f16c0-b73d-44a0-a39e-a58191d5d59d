import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';

interface ShippingRefreshButtonProps {
  onRefresh?: () => void;
}

export function ShippingRefreshButton({ onRefresh }: ShippingRefreshButtonProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const handleRefresh = () => {
    setIsRefreshing(true);
    
    // More aggressive cache clearing strategy
    console.log('Performing deep refresh of shipping data');
    
    // Step 1: Clear all shipping-related queries from React Query
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    queryClient.removeQueries({ queryKey: ['shipping-methods'] });
    
    // Step 2: Clear localStorage timestamps to force refresh
    localStorage.removeItem('last_shipping_refresh');
    localStorage.setItem('shipping_cache_bust', Date.now().toString());
    
    // Step 3: Call the onRefresh callback if provided
    if (onRefresh) {
      onRefresh();
    }
    
    // Step 4: Force a delay to ensure the database queries are fresh
    setTimeout(() => {
      // Step 5: Only now show a confirmation toast
      toast({
        title: 'Shipping options refreshed',
        description: 'The latest shipping options have been loaded from the database.',
        duration: 3000,
      });
      
      // Step 6: Reset refreshing state
      setIsRefreshing(false);
    }, 1500);
  };

  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleRefresh}
      disabled={isRefreshing}
      className="text-xs flex items-center gap-1"
    >
      <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
      {isRefreshing ? 'Refreshing...' : 'Refresh Options'}
    </Button>
  );
} 