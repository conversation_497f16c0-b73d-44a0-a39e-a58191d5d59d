import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';

interface ShippingRefreshButtonProps {
  onRefresh?: () => void;
}

export function ShippingRefreshButton({ onRefresh }: ShippingRefreshButtonProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const handleRefresh = () => {
    setIsRefreshing(true);
    
    // Clear shipping caches
    queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
    localStorage.setItem('shipping_cache_bust', Date.now().toString());
    
    // Call the onRefresh callback if provided
    if (onRefresh) {
      onRefresh();
    }

    // Show a toast message
    toast({
      title: 'Shipping options refreshed',
      description: 'The latest shipping options have been loaded',
      duration: 3000,
    });

    // Reset the refreshing state after a delay
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleRefresh}
      disabled={isRefreshing}
      className="text-xs flex items-center gap-1"
    >
      <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
      {isRefreshing ? 'Refreshing...' : 'Refresh Options'}
    </Button>
  );
} 