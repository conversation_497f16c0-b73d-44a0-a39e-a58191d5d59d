const fs = require('fs');
const csv = require('csv-parser');

// Input file
const inputFile = 'transformed_variants_final.csv';

// Process the CSV file
console.log(`Reading variants from ${inputFile}...`);
const variants = [];

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    variants.push(row);
  })
  .on('end', () => {
    console.log(`Read ${variants.length} variants from CSV`);
    
    // Check for empty or invalid values
    console.log('\nChecking for empty or invalid values...');
    
    // Check required fields
    const requiredFields = ['id', 'product_id', 'variant_name', 'price', 'in_stock', 'option_combination', 'is_active', 'created_at', 'updated_at'];
    const emptyFields = {};
    const invalidFields = {};
    
    variants.forEach((variant, index) => {
      requiredFields.forEach(field => {
        // Check for empty values
        if (variant[field] === undefined || variant[field] === null || variant[field] === '') {
          if (!emptyFields[field]) {
            emptyFields[field] = [];
          }
          emptyFields[field].push(index);
        }
        
        // Check for invalid values
        if (field === 'price' && isNaN(parseFloat(variant[field]))) {
          if (!invalidFields[field]) {
            invalidFields[field] = [];
          }
          invalidFields[field].push(index);
        }
        
        // Check for invalid boolean values
        if ((field === 'in_stock' || field === 'is_active') && 
            (variant[field] !== 'true' && variant[field] !== 'false' && 
             variant[field] !== true && variant[field] !== false)) {
          if (!invalidFields[field]) {
            invalidFields[field] = [];
          }
          invalidFields[field].push(index);
        }
        
        // Check for invalid date values
        if ((field === 'created_at' || field === 'updated_at') && 
            variant[field] && isNaN(Date.parse(variant[field]))) {
          if (!invalidFields[field]) {
            invalidFields[field] = [];
          }
          invalidFields[field].push(index);
        }
        
        // Check for invalid JSON in option_combination
        if (field === 'option_combination' && variant[field]) {
          try {
            JSON.parse(variant[field]);
          } catch (e) {
            if (!invalidFields[field]) {
              invalidFields[field] = [];
            }
            invalidFields[field].push(index);
          }
        }
      });
    });
    
    // Report empty fields
    if (Object.keys(emptyFields).length > 0) {
      console.log('\nEmpty required fields:');
      Object.entries(emptyFields).forEach(([field, indices]) => {
        console.log(`- ${field}: ${indices.length} rows`);
        if (indices.length <= 5) {
          console.log(`  Rows: ${indices.join(', ')}`);
        } else {
          console.log(`  First 5 rows: ${indices.slice(0, 5).join(', ')}...`);
        }
      });
    } else {
      console.log('\nNo empty required fields found');
    }
    
    // Report invalid fields
    if (Object.keys(invalidFields).length > 0) {
      console.log('\nInvalid field values:');
      Object.entries(invalidFields).forEach(([field, indices]) => {
        console.log(`- ${field}: ${indices.length} rows`);
        if (indices.length <= 5) {
          console.log(`  Rows: ${indices.join(', ')}`);
          indices.forEach(index => {
            console.log(`    Row ${index}: ${variants[index][field]}`);
          });
        } else {
          console.log(`  First 5 rows: ${indices.slice(0, 5).join(', ')}...`);
          indices.slice(0, 5).forEach(index => {
            console.log(`    Row ${index}: ${variants[index][field]}`);
          });
        }
      });
    } else {
      console.log('\nNo invalid field values found');
    }
    
    // Check for duplicate IDs
    console.log('\nChecking for duplicate IDs...');
    const idCounts = {};
    variants.forEach(variant => {
      if (!idCounts[variant.id]) {
        idCounts[variant.id] = 0;
      }
      idCounts[variant.id]++;
    });
    
    const duplicateIds = Object.entries(idCounts)
      .filter(([id, count]) => count > 1)
      .map(([id, count]) => ({ id, count }));
    
    if (duplicateIds.length > 0) {
      console.log(`Found ${duplicateIds.length} duplicate IDs:`);
      duplicateIds.forEach(({ id, count }) => {
        console.log(`- ${id}: ${count} occurrences`);
      });
    } else {
      console.log('No duplicate IDs found');
    }
    
    // Check for special characters in IDs
    console.log('\nChecking for special characters in IDs...');
    const specialCharRegex = /[^a-zA-Z0-9-_]/;
    const idsWithSpecialChars = variants
      .filter(variant => specialCharRegex.test(variant.id) || specialCharRegex.test(variant.product_id))
      .map(variant => ({ id: variant.id, product_id: variant.product_id }));
    
    if (idsWithSpecialChars.length > 0) {
      console.log(`Found ${idsWithSpecialChars.length} IDs with special characters:`);
      idsWithSpecialChars.slice(0, 5).forEach(({ id, product_id }) => {
        console.log(`- id: ${id}, product_id: ${product_id}`);
      });
      if (idsWithSpecialChars.length > 5) {
        console.log(`  ... and ${idsWithSpecialChars.length - 5} more`);
      }
    } else {
      console.log('No IDs with special characters found');
    }
  });
