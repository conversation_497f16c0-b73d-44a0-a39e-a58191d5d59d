-- Migration: 20250127000001_create_tenant_system.sql
-- Description: Creates the base tenant system tables and functions

-- Create schema for tenant management
CREATE SCHEMA IF NOT EXISTS tenant_management;

-- Create tenants table
CREATE TABLE tenant_management.tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    subdomain TEXT NOT NULL UNIQUE,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    settings JSONB NOT NULL DEFAULT '{}'::JSONB,
    contact_email TEXT,
    contact_phone TEXT,
    subscription_tier TEXT NOT NULL DEFAULT 'basic',
    subscription_status TEXT NOT NULL DEFAULT 'active',
    subscription_expiry TIMESTAMPTZ,
    max_users INTEGER NOT NULL DEFAULT 5,
    max_products INTEGER NOT NULL DEFAULT 100,
    CONSTRAINT valid_subdomain CHECK (subdomain ~* '^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?$')
);

-- Create tenant users table (for tenant admins)
CREATE TABLE tenant_management.tenant_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenant_management.tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'admin',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(tenant_id, user_id)
);

-- Create function to get current tenant
CREATE OR REPLACE FUNCTION tenant_management.get_current_tenant_id()
RETURNS UUID AS $$
DECLARE
    tenant_id UUID;
    host_header TEXT;
    subdomain TEXT;
BEGIN
    -- Extract subdomain from host header
    host_header := current_setting('request.headers')::json->>'host';
    
    -- Extract subdomain (everything before first '.')
    IF host_header IS NOT NULL AND position('.' in host_header) > 0 THEN
        subdomain := split_part(host_header, '.', 1);
        
        -- Look up tenant by subdomain
        SELECT id INTO tenant_id
        FROM tenant_management.tenants
        WHERE tenants.subdomain = subdomain
        AND tenants.active = TRUE;
        
        RETURN tenant_id;
    END IF;
    
    -- Fallback to JWT claim if available
    BEGIN
        tenant_id := nullif(current_setting('request.jwt.claims', true)::json->>'tenant_id', '')::UUID;
        RETURN tenant_id;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NULL;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate tenant access
CREATE OR REPLACE FUNCTION tenant_management.validate_tenant_access(tenant_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_tenant_id UUID;
    is_admin BOOLEAN;
BEGIN
    -- Check if user is superadmin
    BEGIN
        is_admin := current_setting('request.jwt.claims', true)::json->>'is_admin' = 'true';
        IF is_admin THEN
            RETURN TRUE;
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            is_admin := FALSE;
    END;
    
    -- Get tenant ID from JWT claim
    BEGIN
        user_tenant_id := nullif(current_setting('request.jwt.claims', true)::json->>'tenant_id', '')::UUID;
        
        -- Check if user's tenant matches requested tenant
        IF user_tenant_id = tenant_id THEN
            RETURN TRUE;
        END IF;
        
        -- Check if user is associated with the tenant in tenant_users
        RETURN EXISTS (
            SELECT 1
            FROM tenant_management.tenant_users
            WHERE tenant_id = tenant_id
            AND user_id = auth.uid()
        );
    EXCEPTION
        WHEN OTHERS THEN
            RETURN FALSE;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for performance
CREATE INDEX idx_tenants_subdomain ON tenant_management.tenants(subdomain);
CREATE INDEX idx_tenant_users_tenant_id ON tenant_management.tenant_users(tenant_id);
CREATE INDEX idx_tenant_users_user_id ON tenant_management.tenant_users(user_id);

-- Add comment to explain the purpose of this migration
COMMENT ON SCHEMA tenant_management IS 'Schema for managing multi-tenant functionality';
COMMENT ON TABLE tenant_management.tenants IS 'Stores information about each tenant in the system';
COMMENT ON TABLE tenant_management.tenant_users IS 'Maps users to tenants with roles';
COMMENT ON FUNCTION tenant_management.get_current_tenant_id() IS 'Returns the current tenant ID based on request context';
COMMENT ON FUNCTION tenant_management.validate_tenant_access(UUID) IS 'Validates if the current user has access to the specified tenant';
