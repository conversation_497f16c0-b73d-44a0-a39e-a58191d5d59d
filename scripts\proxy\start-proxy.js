// start-proxy.js - Helper script to start the image proxy server
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory name (ES modules don't have __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the server file path
const serverPath = path.join(__dirname, 'server.js');

// Start the server
console.log('Starting image proxy server...');
const server = spawn('node', [serverPath], { stdio: 'inherit' });

server.on('close', (code) => {
  console.log(`Image proxy server exited with code ${code}`);
});

// Handle termination signals
process.on('SIGINT', () => {
  console.log('Stopping image proxy server...');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('Stopping image proxy server...');
  server.kill('SIGTERM');
});