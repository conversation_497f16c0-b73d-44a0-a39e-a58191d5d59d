#!/usr/bin/env tsx
/**
 * Setup script for seed filtering system
 * This script applies the necessary database migrations for the seed filtering system
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration(filename: string) {
  console.log(`📄 Running migration: ${filename}`);
  
  try {
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', filename);
    const sql = readFileSync(migrationPath, 'utf8');
    
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`❌ Error running ${filename}:`, error);
      return false;
    }
    
    console.log(`✅ Successfully applied ${filename}`);
    return true;
  } catch (err) {
    console.error(`❌ Failed to read or execute ${filename}:`, err);
    return false;
  }
}

async function setupSeedFiltering() {
  console.log('🌱 Setting up seed filtering system...\n');
  
  const migrations = [
    '20250121_create_seed_filtering_system.sql',
    '20250121_create_seed_filtering_functions.sql'
  ];
  
  let success = true;
  
  for (const migration of migrations) {
    const result = await runMigration(migration);
    if (!result) {
      success = false;
      break;
    }
  }
  
  if (success) {
    console.log('\n🎉 Seed filtering system setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Wait for Super Agent to complete data enrichment');
    console.log('2. Import enriched data using the import scripts');
    console.log('3. Test the filtering system in the admin panel');
  } else {
    console.log('\n❌ Setup failed. Please check the errors above.');
    process.exit(1);
  }
}

// Alternative: Direct SQL execution if RPC doesn't work
async function runSqlDirect(sql: string, description: string) {
  console.log(`📄 ${description}`);
  
  try {
    const { error } = await supabase.from('_migrations').select('*').limit(1);
    
    // If we can't access _migrations, try direct execution
    const { error: execError } = await supabase.rpc('exec', { sql });
    
    if (execError) {
      console.error(`❌ Error: ${execError.message}`);
      return false;
    }
    
    console.log(`✅ ${description} completed`);
    return true;
  } catch (err) {
    console.error(`❌ Failed: ${err}`);
    return false;
  }
}

async function setupSeedFilteringDirect() {
  console.log('🌱 Setting up seed filtering system (direct SQL)...\n');
  
  try {
    // Read migration files
    const systemSql = readFileSync(
      join(process.cwd(), 'supabase', 'migrations', '20250121_create_seed_filtering_system.sql'),
      'utf8'
    );
    
    const functionsSql = readFileSync(
      join(process.cwd(), 'supabase', 'migrations', '20250121_create_seed_filtering_functions.sql'),
      'utf8'
    );
    
    // Execute migrations
    const systemResult = await runSqlDirect(systemSql, 'Creating seed filtering tables');
    if (!systemResult) return;
    
    const functionsResult = await runSqlDirect(functionsSql, 'Creating seed filtering functions');
    if (!functionsResult) return;
    
    console.log('\n🎉 Seed filtering system setup completed successfully!');
    
  } catch (err) {
    console.error('❌ Setup failed:', err);
    process.exit(1);
  }
}

// Run the setup
if (process.argv.includes('--direct')) {
  setupSeedFilteringDirect();
} else {
  setupSeedFiltering();
}
