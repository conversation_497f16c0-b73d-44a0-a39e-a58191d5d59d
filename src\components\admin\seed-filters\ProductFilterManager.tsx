import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSupabase } from "@/lib/supabase/provider";
import { createClient } from '@supabase/supabase-js';
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, RefreshCw, Save, Loader2 } from "lucide-react";
import { SimpleProductForm } from "@/components/admin/SimpleProductForm";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Define types
interface Product {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  category_id: string | null;
  brand_id: string | null;
  image: string | null;
}

interface FilterCategory {
  id: string;
  name: string;
  display_name: string;
}

interface FilterOption {
  id: string;
  category_id: string;
  name: string;
  display_name: string;
}

interface Brand {
  id: string;
  name: string;
}

export function ProductFilterManager() {
  const { supabase } = useSupabase();

  // Create admin client for write operations that bypass RLS
  const adminSupabase = useMemo(() => {
    const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIyMjg4MCwiZXhwIjoyMDYyNzk4ODgwfQ.qTP5PkAm36xONeuieVTYuu7zmNGHhzUzzmgH6lWxKdo';

    return createClient(supabaseUrl, serviceRoleKey);
  }, []);

  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

  // State for filters
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("all");
  const [selectedBrandId, setSelectedBrandId] = useState<string>("all");
  const [activeFilter, setActiveFilter] = useState<string>("all"); // "active", "inactive", or "all"
  const [isEditingProduct, setIsEditingProduct] = useState<boolean>(false);
  const [isEditingFilters, setIsEditingFilters] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [isScanningProducts, setIsScanningProducts] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const queryClient = useQueryClient();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch product categories
  const { data: productCategories = [] } = useQuery({
    queryKey: ["product-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");

      if (error) {
        console.error("Error fetching categories:", error);
        return [];
      }

      return data || [];
    },
  });

  // Fetch brands
  const { data: brands = [] } = useQuery({
    queryKey: ["brands"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("brands")
        .select("*")
        .order("name");

      if (error) {
        console.error("Error fetching brands:", error);
        return [];
      }

      return data || [];
    },
  });

  // Fetch seed products with filtering
  const fetchProducts = useCallback(async () => {
    try {
      console.log('Fetching products with filters:', {
        activeFilter,
        selectedCategoryId,
        searchQuery: debouncedSearchQuery
      });

      // Determine the category ID to filter by
      const categoryId = selectedCategoryId === "all" ? null : selectedCategoryId;

      // Set active only based on active filter
      const showActiveOnly = activeFilter === "active";
      const showInactiveOnly = activeFilter === "inactive";

      // Use RPC call to get seed products
      const { data, error } = await supabase.rpc('get_seed_products', {
        p_active_only: showActiveOnly,
        p_category_id: categoryId,
        p_search_term: debouncedSearchQuery || null
      });

      if (error) {
        console.error('Error fetching products:', error);
        return [];
      }

      // If we're showing only active products, we're done
      if (activeFilter === "active") {
        return data || [];
      }

      // If we need inactive products (either all or inactive only)
      // Use the find_inactive_seed_products function to get inactive seed products
      const { data: inactiveProducts, error: inactiveError } = await supabase.rpc('find_inactive_seed_products');

      if (inactiveError) {
        console.error('Error fetching inactive products:', inactiveError);
      }

      // Combine active and inactive products based on filter
      const activeProductsArray = activeFilter === "inactive" ? [] : (Array.isArray(data) ? data : []);

      // Filter inactive products to only include seed-related ones
      // This uses name matching to identify likely seed products
      let inactiveProductsArray = [];
      if (Array.isArray(inactiveProducts)) {
        inactiveProductsArray = inactiveProducts.filter(product => {
          const name = product.name?.toLowerCase() || '';
          // Check if product name contains seed-related keywords
          return (
            name.includes('seed') ||
            name.includes('germination') ||
            name.includes('sprouting') ||
            name.includes('plant') ||
            name.includes('garden') ||
            name.includes('grow') ||
            name.includes('flower') ||
            name.includes('herb')
          );
        });
      }

      // Create a Set of IDs to prevent duplicates
      const productIds = new Set();

      // Add active products first (if not showing inactive only)
      const combinedProducts = [];
      if (activeFilter !== "inactive") {
        for (const product of activeProductsArray) {
          if (product.id && !productIds.has(product.id)) {
            productIds.add(product.id);
            combinedProducts.push(product);
          }
        }
      }

      // Then add filtered inactive products (if not showing active only)
      if (activeFilter !== "active") {
        for (const product of inactiveProductsArray) {
          if (product.id && !productIds.has(product.id)) {
            productIds.add(product.id);
            combinedProducts.push(product);
          }
        }
      }

      console.log(`Found ${activeProductsArray.length} active and ${inactiveProductsArray.length} seed-related inactive products`);

      // Apply brand filter if selected
      let filteredProducts = combinedProducts;
      if (selectedBrandId !== "all") {
        filteredProducts = filteredProducts.filter(product => product.brand_id === selectedBrandId);
      }

      return filteredProducts;
    } catch (error) {
      console.error('Error in fetchProducts:', error);
      return [];
    }
  }, [supabase, activeFilter, selectedCategoryId, selectedBrandId, debouncedSearchQuery]);

  // Fetch products query
  const {
    data: products = [],
    isLoading,
    isFetching,
    error,
    refetch
  } = useQuery({
    queryKey: ['seed-products', selectedCategoryId, selectedBrandId, activeFilter, debouncedSearchQuery, currentPage],
    queryFn: fetchProducts,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Log any errors
  useEffect(() => {
    if (error) {
      console.error("Products query error:", error);
    }
  }, [error]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    if (!products || products.length === 0) return 1;
    return Math.ceil(products.length / pageSize);
  }, [products, pageSize]);

  // Calculate pagination
  const paginatedProducts = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return products?.slice(start, end) || [];
  }, [products, currentPage, pageSize]);

  // Handler for opening the product edit modal
  const handleEditProduct = (product: Product) => {
    console.log('Opening product edit modal for:', product.name);
    setSelectedProduct(product);
    setIsEditingProduct(true);
  };

  // Handle product status toggle
  const handleToggleProductStatus = async (product: Product) => {
    try {
      // Update the product status in the database
      const { error } = await supabase
        .from("products")
        .update({
          is_active: !product.is_active
        })
        .eq("id", product.id);

      if (error) {
        toast.error(`Error updating product status: ${error.message}`);
        return;
      }

      // Success
      toast.success(`Product ${product.is_active ? 'deactivated' : 'activated'} successfully`);
      // Invalidate both queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['seed-products'] });
    } catch (error) {
      console.error("Error toggling product status:", error);
      toast.error("Failed to update product status");
    }
  };

  // Check if product is actually a seed product
  const isSeedProduct = (product: any) => {
    const name = product.name?.toLowerCase() || '';

    // Exclude non-seed items first
    const excludeKeywords = [
      'bong', 'pipe', 'grinder', 'rolling', 'paper', 'filter', 'tip',
      'lighter', 'ashtray', 'scale', 'jar', 'container', 'bag', 'mylar',
      'gauge', 'mesh', 'screen', 'tool', 'accessory', 'vaporizer', 'vape',
      'cleaner', 'solution', 'brush', 'mat', 'tray', 'storage', 'brass', 'cone'
    ];

    for (const keyword of excludeKeywords) {
      if (name.includes(keyword)) {
        return false;
      }
    }

    // Include seed-related keywords
    const includeKeywords = [
      'seed', 'auto', 'feminised', 'feminized', 'fastbuds', 'strain',
      'genetics', 'cannabis', 'hemp', 'indica', 'sativa', 'hybrid'
    ];

    return includeKeywords.some(keyword => name.includes(keyword));
  };

  // Extract seed attributes from product name and description
  const extractSeedAttributes = (product: any) => {
    const name = product.name?.toLowerCase() || '';
    const description = product.description?.toLowerCase() || '';
    const combined = `${name} ${description}`;

    // Skip if not a seed product
    if (!isSeedProduct(product)) {
      return [];
    }

    const attributes = [];

    // Seed Type Detection
    if (name.includes('auto') || name.includes('autoflower')) {
      attributes.push({ category: 'seed_type', value: 'autoflower', display: 'Autoflower' });
    } else if (name.includes('feminised') || name.includes('feminized') || name.includes('female')) {
      attributes.push({ category: 'seed_type', value: 'feminised', display: 'Feminised' });
    } else {
      attributes.push({ category: 'seed_type', value: 'regular', display: 'Regular' });
    }

    // THC Level Detection (from description)
    const thcMatch = combined.match(/thc[:\s]*(\d+(?:\.\d+)?)[%\s]/i);
    if (thcMatch) {
      const thcValue = parseFloat(thcMatch[1]);
      if (thcValue < 15) {
        attributes.push({ category: 'thc_level', value: 'low', display: 'Low (0-15%)' });
      } else if (thcValue < 20) {
        attributes.push({ category: 'thc_level', value: 'medium', display: 'Medium (15-20%)' });
      } else {
        attributes.push({ category: 'thc_level', value: 'high', display: 'High (20%+)' });
      }
    }

    // CBD Level Detection
    const cbdMatch = combined.match(/cbd[:\s]*(\d+(?:\.\d+)?)[%\s]/i);
    if (cbdMatch) {
      const cbdValue = parseFloat(cbdMatch[1]);
      if (cbdValue < 1) {
        attributes.push({ category: 'cbd_level', value: 'low', display: 'Low (<1%)' });
      } else if (cbdValue < 5) {
        attributes.push({ category: 'cbd_level', value: 'medium', display: 'Medium (1-5%)' });
      } else {
        attributes.push({ category: 'cbd_level', value: 'high', display: 'High (5%+)' });
      }
    }

    // Flowering Time Detection
    const floweringMatch = combined.match(/(\d+)[-–](\d+)\s*weeks?/i);
    if (floweringMatch) {
      const weeks = parseInt(floweringMatch[1]);
      if (weeks <= 8) {
        attributes.push({ category: 'flowering_time', value: 'fast', display: 'Fast (6-8 weeks)' });
      } else if (weeks <= 10) {
        attributes.push({ category: 'flowering_time', value: 'medium', display: 'Medium (8-10 weeks)' });
      } else {
        attributes.push({ category: 'flowering_time', value: 'slow', display: 'Slow (10+ weeks)' });
      }
    }

    // Effect Detection
    if (combined.includes('sativa')) {
      attributes.push({ category: 'effect', value: 'energetic', display: 'Energetic/Uplifting' });
    } else if (combined.includes('indica')) {
      attributes.push({ category: 'effect', value: 'relaxing', display: 'Relaxing/Sedating' });
    } else if (combined.includes('hybrid')) {
      attributes.push({ category: 'effect', value: 'balanced', display: 'Balanced' });
    }

    return attributes;
  };

  // Get or create filter option using admin client
  const getOrCreateFilterOption = async (categoryName: string, value: string, displayName: string) => {
    try {
      // First get the category
      const { data: category } = await adminSupabase
        .from('filter_categories')
        .select('id')
        .eq('name', categoryName)
        .single();

      if (!category) {
        console.warn(`Category not found: ${categoryName}`);
        return null;
      }

      // Check if option exists (suppress error for not found)
      const { data: existingOption, error: checkError } = await adminSupabase
        .from('filter_options')
        .select('id')
        .eq('category_id', category.id)
        .eq('name', value)
        .maybeSingle();

      // Handle check errors gracefully
      if (checkError && checkError.code !== 'PGRST116') {
        console.warn(`Error checking existing option: ${checkError.message}`);
      }

      if (existingOption) {
        return existingOption.id;
      }

      // Create new option
      const { data: newOption, error } = await adminSupabase
        .from('filter_options')
        .insert({
          category_id: category.id,
          name: value,
          display_name: displayName,
          display_order: 0,
          is_active: true,
          product_count: 0
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating filter option:', error);
        return null;
      }

      return newOption.id;
    } catch (error) {
      console.error('Error in getOrCreateFilterOption:', error);
      return null;
    }
  };

  // Run matching logic for seed products based on current filter
  const runProductMatching = async () => {
    try {
      console.log('🔧 Starting product matching process...');
      console.log(`📊 Current filter: ${activeFilter}`);

      // Respect the current active filter selection
      const showActiveOnly = activeFilter === "active";
      const showInactiveOnly = activeFilter === "inactive";

      // Get seed products based on current filter
      const { data: seedProducts, error } = await supabase.rpc('get_seed_products', {
        p_active_only: showActiveOnly,
        p_category_id: null,
        p_search_term: null
      });

      if (error) {
        console.error('Error fetching seed products:', error);
        toast.error('Failed to fetch products for matching');
        return;
      }

      // Handle inactive products separately if needed
      let allProducts = seedProducts || [];

      if (showInactiveOnly) {
        // For inactive products, we need to get them differently since RPC might not return them
        console.log('🔍 Fetching inactive products separately...');
        const { data: inactiveProducts, error: inactiveError } = await adminSupabase
          .from('products')
          .select('*')
          .eq('is_active', false);

        if (inactiveError) {
          console.error('Error fetching inactive products:', inactiveError);
          toast.error('Failed to fetch inactive products');
          return;
        }

        // Filter to only seed-related inactive products
        allProducts = (inactiveProducts || []).filter(product => {
          const name = product.name?.toLowerCase() || '';
          return (
            name.includes('seed') ||
            name.includes('auto') ||
            name.includes('feminised') ||
            name.includes('feminized') ||
            name.includes('fastbuds') ||
            name.includes('strain') ||
            name.includes('genetics') ||
            name.includes('cannabis') ||
            name.includes('hemp') ||
            name.includes('indica') ||
            name.includes('sativa') ||
            name.includes('hybrid')
          );
        });
      }

      if (!allProducts || allProducts.length === 0) {
        const filterText = activeFilter === "active" ? "active" : activeFilter === "inactive" ? "inactive" : "all";
        toast.info(`No ${filterText} seed products found to process`);
        return;
      }

      console.log(`Found ${allProducts.length} ${activeFilter} seed products to process`);
      let processed = 0;
      let errors = 0;

      for (const product of allProducts) {
        try {
          console.log(`Processing: ${product.name}`);

          // Extract attributes
          const attributes = extractSeedAttributes(product);

          if (attributes.length === 0) {
            console.log(`  Skipped: ${product.name} (not a seed product or no attributes found)`);
            continue;
          }

          // Clear existing filter relationships
          await adminSupabase
            .from('product_filters')
            .delete()
            .eq('product_id', product.id);

          // Create new filter relationships
          for (const attr of attributes) {
            const optionId = await getOrCreateFilterOption(attr.category, attr.value, attr.display);

            if (optionId) {
              await adminSupabase
                .from('product_filters')
                .insert({
                  product_id: product.id,
                  filter_option_id: optionId
                });

              console.log(`  ✅ Linked to ${attr.category}: ${attr.display}`);
            }
          }

          processed++;
        } catch (err) {
          console.error(`Error processing ${product.name}:`, err);
          errors++;
        }
      }

      console.log(`🎉 Matching complete: ${processed} processed, ${errors} errors`);
      toast.success(`Product matching complete! Processed ${processed} products`);

      // Refresh the UI
      queryClient.invalidateQueries({ queryKey: ['seed-products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });

    } catch (error) {
      console.error('Error in runProductMatching:', error);
      toast.error('Failed to run product matching');
    }
  };

  // Render the component
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Product Filter Manager</CardTitle>
          <CardDescription>
            Manage filters for your products
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col space-y-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Input
                  placeholder="Search products..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <div className="w-full sm:w-48">
                  <Select
                    value={selectedCategoryId}
                    onValueChange={setSelectedCategoryId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {productCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="w-full sm:w-48">
                  <Select
                    value={selectedBrandId}
                    onValueChange={setSelectedBrandId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Brand" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Brands</SelectItem>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Active/Inactive Filter Controls */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 bg-muted p-4 rounded-md border">
              <div className="flex flex-col space-y-1 min-w-[180px]">
                <Label htmlFor="active-filter" className="font-medium">Product Status</Label>
                <Select
                  value={activeFilter}
                  onValueChange={(value) => {
                    console.log('Setting active filter to:', value);
                    setActiveFilter(value);
                    // Reset to page 1 when changing filter
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger id="active-filter" className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active Products Only</SelectItem>
                    <SelectItem value="inactive">Inactive Products Only</SelectItem>
                    <SelectItem value="all">All Products</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 flex items-center gap-4">
                {activeFilter === "active" && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-sm px-3 py-1">
                    Showing active products only
                  </Badge>
                )}
                {activeFilter === "inactive" && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-sm px-3 py-1">
                    Showing inactive products only
                  </Badge>
                )}
                {activeFilter === "all" && (
                  <Badge variant="outline" className="text-sm px-3 py-1">
                    Showing both active and inactive products
                  </Badge>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  className="ml-auto"
                  onClick={runProductMatching}
                  disabled={isFetching}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
                  {isFetching ? 'Processing...' : `Rescan ${activeFilter === 'active' ? 'Active' : activeFilter === 'inactive' ? 'Inactive' : 'All'} Products`}
                </Button>
              </div>
            </div>
          </div>

          {/* Products Table */}
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : !products || products.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No products found matching your filters.</p>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Brand</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        {product.image ? (
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-16 h-16 object-cover rounded-md"
                          />
                        ) : (
                          <div className="w-16 h-16 bg-muted rounded-md flex items-center justify-center text-muted-foreground">
                            No image
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>
                        {productCategories.find(cat => cat.id === product.category_id)?.name || 'Uncategorized'}
                      </TableCell>
                      <TableCell>
                        {brands.find(brand => brand.id === product.brand_id)?.name || 'No brand'}
                      </TableCell>
                      <TableCell>
                        {product.is_active ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center space-x-2 justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditProduct(product)}
                          >
                            View Details
                          </Button>
                          <Button
                            variant={product.is_active ? "destructive" : "default"}
                            size="sm"
                            onClick={() => handleToggleProductStatus(product)}
                          >
                            {product.is_active ? "Deactivate" : "Activate"}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {!isLoading && products && products.length > 0 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {paginatedProducts.length} of {products.length} products
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  disabled={currentPage >= totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Edit Dialog - Using SimpleProductForm */}
      {isEditingProduct && selectedProduct && (
        <Dialog open={isEditingProduct} onOpenChange={setIsEditingProduct}>
          <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Seed Product</DialogTitle>
              <DialogDescription>
                Update product details and assign to the seed category
              </DialogDescription>
            </DialogHeader>

            <div className="mt-4">
              <SimpleProductForm
                product={selectedProduct as any}
                onSuccess={() => {
                  queryClient.invalidateQueries({ queryKey: ['products'] });
                  // Also invalidate the seed products query
                  queryClient.invalidateQueries({ queryKey: ['seed-products'] });
                  setIsEditingProduct(false);
                  toast.success('Product updated successfully');
                }}
                onCancel={() => setIsEditingProduct(false)}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
