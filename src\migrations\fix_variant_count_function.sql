-- Add function to get variant counts per product
CREATE OR REPLACE FUNCTION get_variant_counts_per_product()
RETURNS TABLE (product_id UUID, count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pv.product_id,
    COUNT(pv.id)::BIGINT
  FROM 
    product_variants pv
  GROUP BY 
    pv.product_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment for documentation
COMMENT ON FUNCTION get_variant_counts_per_product IS 'Returns a table of product IDs and their variant counts';
