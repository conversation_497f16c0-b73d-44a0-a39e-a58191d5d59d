import { useState, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
interface ChatBubbleProps {
  children: ReactNode;
  isOpen: boolean;
  onClose: () => void;
  onToggle: () => void;
}
const ChatBubble = ({
  children,
  isOpen,
  onClose,
  onToggle
}: ChatBubbleProps) => {
  return <div className="fixed bottom-8 right-8 z-50 flex flex-col items-end">
      {/* Main chat container */}
      {isOpen && <div className="mb-4 w-full max-w-md bg-white rounded-lg shadow-xl border border-sage-200 animate-scale-in">
          <div className="flex items-center justify-between p-4 bg-sage-100 rounded-t-lg border-b border-sage-200">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-sage-600 rounded-full flex items-center justify-center text-white text-sm mr-2">
                🌿
              </div>
              <h3 className="font-medium text-sage-800">Chat with Cheech</h3>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8 rounded-full hover:bg-sage-200">
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="p-4 max-h-[500px] overflow-y-auto">
            {children}
          </div>
        </div>}

      {/* Chat bubble button with gentle pulse animation */}
      <Button onClick={onToggle} className="rounded-full h-16 w-16 bg-sage-600 hover:bg-sage-700 flex items-center justify-center shadow-lg transition-all duration-300 animate-gentle-pulse">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      </Button>
    </div>;
};
export default ChatBubble;