// <PERSON>ript to directly inspect shipping methods in the database
import { createClient } from '@supabase/supabase-js';

// Use the same credentials as the app
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function inspectShippingMethods() {
  console.log('🔍 Inspecting shipping methods in database...');
  
  try {
    // Get shipping zones
    console.log('\n📊 Shipping Zones:');
    const { data: zones, error: zonesError } = await supabase
      .from('shipping_zones')
      .select('*');
      
    if (zonesError) {
      console.error('Error fetching zones:', zonesError);
      return;
    }
    
    zones.forEach(zone => {
      console.log(`- ${zone.name} (${zone.id}): active=${zone.is_active}`);
    });
    
    // Get ALL shipping methods directly
    console.log('\n📦 All Shipping Methods:');
    const { data: methods, error: methodsError } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (methodsError) {
      console.error('Error fetching methods:', methodsError);
      return;
    }
    
    methods.forEach(method => {
      console.log(`- ${method.name} (${method.id})`);
      console.log(`  Zone: ${method.zone_id}`);
      console.log(`  is_active: ${method.is_active} (${typeof method.is_active})`);
      console.log(`  Raw record: ${JSON.stringify(method)}`);
      console.log('---');
    });
    
    // Test the frontend logic directly
    console.log('\n🧪 Testing Frontend Filtering Logic:');
    
    // This is the strict filter we're now using
    const strictFilteredMethods = methods.filter(method => method.is_active === true);
    console.log(`\n✅ Methods passing strict filter (is_active === true): ${strictFilteredMethods.length}`);
    strictFilteredMethods.forEach(method => {
      console.log(`- ${method.name}`);
    });
    
    // This is the loose filter that was causing problems
    const looseFilteredMethods = methods.filter(method => method.is_active !== false);
    console.log(`\n⚠️ Methods passing loose filter (is_active !== false): ${looseFilteredMethods.length}`);
    looseFilteredMethods.forEach(method => {
      console.log(`- ${method.name}`);
    });
    
    // Check for "falsy but not false" values that could cause confusing behavior
    const confusingMethods = methods.filter(method => 
      !method.is_active && method.is_active !== false
    );
    
    if (confusingMethods.length > 0) {
      console.log('\n⛔ PROBLEM DETECTED: Methods with confusing is_active values:');
      confusingMethods.forEach(method => {
        console.log(`- ${method.name}: is_active=${method.is_active} (${typeof method.is_active})`);
      });
      
      // Attempt to fix directly
      console.log('\n🔧 Attempting to fix confusing values...');
      
      for (const method of confusingMethods) {
        console.log(`Updating ${method.name} to have explicit is_active=false`);
        
        const { error: updateError } = await supabase
          .from('shipping_methods')
          .update({ 
            is_active: false,
            updated_at: new Date().toISOString() 
          })
          .eq('id', method.id);
          
        if (updateError) {
          console.error(`Failed to update ${method.name}:`, updateError);
        } else {
          console.log(`✅ Successfully updated ${method.name}`);
        }
      }
    } else {
      console.log('\n✅ No confusing is_active values detected.');
    }
    
    // Ensure all inactive methods are properly marked
    const methodsToCheck = methods.filter(m => m.id !== '7e357480-9d1e-4b0c-bcc8-763d9120d7b6');
    if (methodsToCheck.length > 0) {
      console.log('\n🔒 Ensuring all non-standard shipping methods are properly marked inactive...');
      
      for (const method of methodsToCheck) {
        if (method.is_active !== false) {
          console.log(`Updating ${method.name} to have explicit is_active=false`);
          
          const { error: updateError } = await supabase
            .from('shipping_methods')
            .update({ 
              is_active: false,
              updated_at: new Date().toISOString() 
            })
            .eq('id', method.id);
            
          if (updateError) {
            console.error(`Failed to update ${method.name}:`, updateError);
          } else {
            console.log(`✅ Successfully updated ${method.name}`);
          }
        } else {
          console.log(`✓ ${method.name} already has is_active=false`);
        }
      }
    }
    
    // Verify standard shipping is active
    const standardShipping = methods.find(m => m.id === '7e357480-9d1e-4b0c-bcc8-763d9120d7b6');
    if (standardShipping && standardShipping.is_active !== true) {
      console.log('\n🔔 Ensuring Standard Shipping is marked active...');
      
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ 
          is_active: true,
          updated_at: new Date().toISOString() 
        })
        .eq('id', '7e357480-9d1e-4b0c-bcc8-763d9120d7b6');
        
      if (updateError) {
        console.error('Failed to update Standard Shipping:', updateError);
      } else {
        console.log('✅ Successfully updated Standard Shipping to active');
      }
    }
    
    // Final verification
    console.log('\n🔍 Final Verification:');
    const { data: finalMethods, error: finalError } = await supabase
      .from('shipping_methods')
      .select('*');
      
    if (finalError) {
      console.error('Error in final verification:', finalError);
      return;
    }
    
    const activeCount = finalMethods.filter(m => m.is_active === true).length;
    const inactiveCount = finalMethods.filter(m => m.is_active === false).length;
    
    console.log(`Active methods (is_active=true): ${activeCount}`);
    console.log(`Inactive methods (is_active=false): ${inactiveCount}`);
    console.log(`Total methods: ${finalMethods.length}`);
    
    if (activeCount + inactiveCount !== finalMethods.length) {
      console.log('\n⛔ WARNING: Some methods have unclear is_active status!');
    } else {
      console.log('\n✅ All methods have clear is_active status.');
    }
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the script
inspectShippingMethods()
  .catch(err => {
    console.error('Error running script:', err);
  }); 