import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Loader2 } from 'lucide-react';

// Define the Product interface
interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  is_active: boolean;
  category_id?: string;
  brand_id?: string;
  description?: string;
  // Seed-specific fields
  seed_type?: string;
  flowering_time?: string;
  yield?: string;
  thc_content?: string;
}

interface SimpleProductGridProps {
  categoryId?: string;
  subcategoryId?: string;
  brandId?: string;
  searchQuery?: string;
  seedFilters?: Record<string, string[]>;
}

// Mock seed products for testing when database connection fails
const mockSeedProducts: Product[] = [
  {
    id: 'seed-1',
    name: 'Northern Lights Feminized',
    price: 39.99,
    image: 'https://images.unsplash.com/photo-1536819114556-1e10f967fb61?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true,
    seed_type: 'feminized',
    flowering_time: '8-10',
    yield: 'high',
    thc_content: 'medium'
  },
  {
    id: 'seed-2',
    name: 'Blueberry Autoflower',
    price: 49.99,
    image: 'https://images.unsplash.com/photo-1620451955631-9a2a6a76fb62?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
    is_active: true,
    seed_type: 'autoflowering',
    flowering_time: '6-8',
    yield: 'medium',
    thc_content: 'high'
  },
  {
    id: 'seed-3',
    name: 'OG Kush Regular',
    price: 29.99,
    image: 'https://images.unsplash.com/photo-1603909223429-69bb7101f420?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true,
    seed_type: 'regular',
    flowering_time: '8-10',
    yield: 'high',
    thc_content: 'high'
  },
  {
    id: 'seed-4',
    name: 'Amnesia Haze Feminized',
    price: 44.99,
    image: 'https://images.unsplash.com/photo-1603909223358-05a646e5cf93?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true,
    seed_type: 'feminized',
    flowering_time: '10+',
    yield: 'high',
    thc_content: 'high'
  },
  {
    id: 'seed-5',
    name: 'White Widow Autoflower',
    price: 39.99,
    image: 'https://images.unsplash.com/photo-1603909223398-823d364e779f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true,
    seed_type: 'autoflowering',
    flowering_time: '6-8',
    yield: 'medium',
    thc_content: 'medium'
  },
  {
    id: 'seed-6',
    name: 'Durban Poison Regular',
    price: 34.99,
    image: 'https://images.unsplash.com/photo-1603909223225-21dec8e3b5b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true,
    seed_type: 'regular',
    flowering_time: '8-10',
    yield: 'high',
    thc_content: 'high'
  }
];

// Mock CBD products for testing
const mockCBDProducts: Product[] = [
  {
    id: 'cbd-1',
    name: 'Full Spectrum CBD Oil 1000mg',
    price: 79.99,
    image: 'https://images.unsplash.com/photo-1556762132-69bcd4e95231?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true
  },
  {
    id: 'cbd-2',
    name: 'CBD Gummies 25mg',
    price: 29.99,
    image: 'https://images.unsplash.com/photo-1611070923052-b3479d7b3b15?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    is_active: true
  }
];

const SimpleProductGrid: React.FC<SimpleProductGridProps> = ({
  categoryId,
  subcategoryId,
  brandId,
  searchQuery,
  seedFilters
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Simplified useEffect that always uses mock data
  useEffect(() => {
    console.log('SimpleProductGrid mounted with categoryId:', categoryId);
    console.log('Seed filters:', seedFilters);
    
    // Set a short timeout to simulate loading
    const timer = setTimeout(() => {
      // Check if we're in the seeds category
      const isSeedsCategory = 
        categoryId === 'cat-2' || 
        categoryId?.includes('seed') || 
        window.location.href.includes('seeds');
      
      if (isSeedsCategory) {
        console.log('Using mock seed products');
        
        // Apply seed filters if provided
        if (seedFilters && Object.keys(seedFilters).some(key => seedFilters[key].length > 0)) {
          console.log('Applying seed filters to mock data:', seedFilters);
          
          // Filter the mock seed products based on the selected filters
          const filteredProducts = mockSeedProducts.filter(product => {
            // Check each filter category
            for (const [category, values] of Object.entries(seedFilters)) {
              // Skip empty filter arrays
              if (values.length === 0) continue;
              
              // Check if the product matches any of the selected values for this category
              const productValue = product[category as keyof Product] as string;
              if (!productValue || !values.includes(productValue)) {
                return false;
              }
            }
            
            return true;
          });
          
          setProducts(filteredProducts);
        } else {
          // No filters, show all seed products
          setProducts(mockSeedProducts);
        }
      } else {
        // For other categories, use CBD products
        console.log('Using mock CBD products');
        setProducts(mockCBDProducts);
      }
      
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [categoryId, subcategoryId, brandId, searchQuery, seedFilters]);

  // Format currency helper function
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-10 w-10 animate-spin text-indigo-500" />
      </div>
    );
  }
  
  if (products.length === 0) {
    return (
      <div className="text-center py-20 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-medium text-gray-900">No products found</h3>
        <p className="mt-2 text-gray-500">
          Try adjusting your filters or check back later as we add new products to our store.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <Link to={`/product/${product.id}`} className="block">
              <div className="relative h-48 overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover transition-transform hover:scale-105"
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 truncate">{product.name}</h3>
                <p className="mt-1 text-lg font-bold text-indigo-600">{formatCurrency(product.price)}</p>
                
                {/* Show seed-specific details if available */}
                {product.seed_type && (
                  <div className="mt-2 space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <span className="font-medium mr-1">Type:</span> {product.seed_type}
                    </div>
                    {product.flowering_time && (
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium mr-1">Flowering:</span> {product.flowering_time}
                      </div>
                    )}
                  </div>
                )}
                
                <button className="mt-3 w-full bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700 transition-colors">
                  View Details
                </button>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimpleProductGrid;
