// fix-shipping-methods.js
// A direct utility to fix shipping methods in the database
// This script bypasses the React cache and directly updates the database

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function listShippingMethods() {
  try {
    const { data, error } = await supabase
      .from('shipping_methods')
      .select('*')
      .order('sort_order');
      
    if (error) throw error;
    
    console.log('\nCurrent Shipping Methods:');
    console.log('------------------------');
    
    data.forEach(method => {
      console.log(`ID: ${method.id}`);
      console.log(`Name: ${method.name}`);
      console.log(`Active: ${method.is_active ? 'Yes' : 'No'}`);
      console.log(`Zone ID: ${method.zone_id}`);
      console.log('------------------------');
    });
    
    return data;
  } catch (error) {
    console.error('Error listing shipping methods:', error);
    return [];
  }
}

async function disableShippingMethod(methodName) {
  try {
    // First find the method by name
    const { data: methods, error: findError } = await supabase
      .from('shipping_methods')
      .select('*')
      .ilike('name', `%${methodName}%`);
      
    if (findError) throw findError;
    
    if (!methods || methods.length === 0) {
      console.log(`No shipping methods found matching "${methodName}"`);
      return;
    }
    
    // If multiple methods match, show them and ask for ID
    if (methods.length > 1) {
      console.log(`Found ${methods.length} shipping methods matching "${methodName}":`);
      methods.forEach(method => {
        console.log(`ID: ${method.id}, Name: ${method.name}, Active: ${method.is_active ? 'Yes' : 'No'}`);
      });
      
      // For simplicity, we'll disable the first one that's active
      const activeMethod = methods.find(m => m.is_active);
      if (!activeMethod) {
        console.log('All matching methods are already inactive.');
        return;
      }
      
      console.log(`Disabling method: ${activeMethod.name} (ID: ${activeMethod.id})`);
      
      // Update the method to be inactive
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', activeMethod.id);
        
      if (updateError) throw updateError;
      
      console.log(`✅ Successfully disabled shipping method: ${activeMethod.name}`);
      
      // Update a timestamp in settings to invalidate any cache
      await supabase
        .from('settings')
        .upsert({ 
          key: 'shipping_cache_timestamp', 
          value: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'key' 
        });
        
      console.log('✅ Cache timestamp updated');
    } else {
      // Only one method found
      const method = methods[0];
      
      if (!method.is_active) {
        console.log(`Method "${method.name}" is already inactive.`);
        return;
      }
      
      // Update the method to be inactive
      const { error: updateError } = await supabase
        .from('shipping_methods')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', method.id);
        
      if (updateError) throw updateError;
      
      console.log(`✅ Successfully disabled shipping method: ${method.name}`);
      
      // Update a timestamp in settings to invalidate any cache
      await supabase
        .from('settings')
        .upsert({ 
          key: 'shipping_cache_timestamp', 
          value: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'key' 
        });
        
      console.log('✅ Cache timestamp updated');
    }
  } catch (error) {
    console.error('Error disabling shipping method:', error);
  }
}

async function main() {
  console.log('Shipping Methods Utility');
  console.log('=======================');
  
  // List all shipping methods
  await listShippingMethods();
  
  // Disable Next Day Delivery
  await disableShippingMethod('Next Day');
  
  // List again to confirm changes
  console.log('\nUpdated shipping methods:');
  await listShippingMethods();
  
  console.log('\n✅ Done! The changes have been applied directly to the database.');
  console.log('Please refresh your browser to see the changes in the checkout page.');
}

main();
