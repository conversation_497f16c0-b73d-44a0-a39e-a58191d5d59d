/* Enhanced Navigation Styles */

.nav-link {
  position: relative;
  padding: 0.5rem 0.75rem;
  font-weight: 500;
  color: #4a5568;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: rgba(var(--primary-rgb), 0.08);
  transform: scale(1.02);
}

.nav-link:hover svg {
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--primary);
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 2px;
  background-color: var(--primary);
  border-radius: 2px;
}

/* Icon animations */
.nav-link svg {
  transition: all 0.3s ease;
}

/* Special hover effect for the Shop dropdown */
.nav-link:has(svg.lucide-store):hover svg.lucide-chevron-down {
  transform: translateY(1px);
}

/* Dropdown Animation */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

[data-state="open"] > [data-radix-dropdown-menu-content-wrapper] {
  animation: dropdownFadeIn 0.2s ease-out;
}

/* Category item hover effects */
.dropdown-category-item {
  transition: all 0.2s ease;
}

.dropdown-category-item:hover svg {
  transform: scale(1.2);
  filter: drop-shadow(0 0 2px rgba(var(--primary-rgb), 0.3));
}

/* Subcategory hover effect */
.dropdown-subcategory-item:hover svg {
  transform: translateX(2px);
  opacity: 1;
}

/* Mobile Menu Enhancements */
.mobile-nav-link {
  display: block;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.mobile-nav-link:hover {
  background-color: rgba(var(--primary-rgb), 0.08);
}

.mobile-nav-link:hover svg {
  transform: translateY(-1px) scale(1.1);
  color: var(--primary);
}

.mobile-nav-link svg {
  transition: all 0.3s ease;
}
