import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createBrandsTable() {
  console.log('Creating brands table...');
  
  // Check if brands table already exists
  const { data: existingTables, error: tablesError } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .eq('table_name', 'brands');
    
  if (tablesError) {
    console.error('Error checking for brands table:', tablesError);
    return;
  }
  
  // If brands table already exists, skip creation
  if (existingTables && existingTables.length > 0) {
    console.log('Brands table already exists, skipping creation');
    return;
  }
  
  // Create brands table
  const { error: createError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE public.brands (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        slug TEXT NOT NULL UNIQUE,
        description TEXT,
        logo TEXT,
        website TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Add RLS policies
      ALTER TABLE public.brands ENABLE ROW LEVEL SECURITY;
      
      -- Create policies
      CREATE POLICY "Allow public read access" ON public.brands
        FOR SELECT USING (true);
        
      CREATE POLICY "Allow admin insert access" ON public.brands
        FOR INSERT WITH CHECK (auth.uid() IN (
          SELECT id FROM public.profiles WHERE is_admin = true
        ));
        
      CREATE POLICY "Allow admin update access" ON public.brands
        FOR UPDATE USING (auth.uid() IN (
          SELECT id FROM public.profiles WHERE is_admin = true
        ));
        
      CREATE POLICY "Allow admin delete access" ON public.brands
        FOR DELETE USING (auth.uid() IN (
          SELECT id FROM public.profiles WHERE is_admin = true
        ));
    `
  });
  
  if (createError) {
    console.error('Error creating brands table:', createError);
    return;
  }
  
  console.log('Brands table created successfully');
  
  // Update products table to add brand_id foreign key if it doesn't exist
  const { error: alterError } = await supabase.rpc('exec_sql', {
    sql: `
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'products' 
          AND column_name = 'brand_id'
        ) THEN
          ALTER TABLE public.products 
          ADD COLUMN brand_id UUID REFERENCES public.brands(id);
        END IF;
      END
      $$;
    `
  });
  
  if (alterError) {
    console.error('Error updating products table:', alterError);
    return;
  }
  
  console.log('Products table updated successfully');
}

// Run the migration
createBrandsTable()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
