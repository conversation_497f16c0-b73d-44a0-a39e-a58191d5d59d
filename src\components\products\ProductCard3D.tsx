
import { useState, useRef, useEffect, MouseEvent } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ShoppingBag, Star } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Product } from '@/components/ProductCard';

interface ProductCard3DProps {
  product: Product;
}

const ProductCard3D = ({ product }: ProductCard3DProps) => {
  const { id, name, slug, price, salePrice, image, rating, reviewCount, isNew, isBestSeller } = product;
  const discount = salePrice ? Math.round(((price - salePrice) / price) * 100) : 0;

  const cardRef = useRef<HTMLDivElement>(null);
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    // Get card dimensions and position
    const card = cardRef.current;
    const rect = card.getBoundingClientRect();

    // Calculate mouse position relative to card center
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;

    // Calculate rotation based on mouse position
    // Reduce the effect strength for more subtle rotation
    const rotationStrength = 10;
    const maxRotation = 10;

    const rotationX = Math.min(Math.max((y / (rect.height / 2)) * -rotationStrength, -maxRotation), maxRotation);
    const rotationY = Math.min(Math.max((x / (rect.width / 2)) * rotationStrength, -maxRotation), maxRotation);

    setRotation({ x: rotationX, y: rotationY });
  };

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => {
    setIsHovered(false);
    setRotation({ x: 0, y: 0 });
  };

  return (
    <div
      ref={cardRef}
      className="relative rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300"
      style={{
        transform: `perspective(1000px) rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) scale(${isHovered ? 1.02 : 1})`,
        transition: isHovered ? 'transform 0.1s ease-out' : 'transform 0.5s ease-out'
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative overflow-hidden group">
        <Link to={`/product/${slug}`}>
          <div className="overflow-hidden">
            <AspectRatio ratio={1/1}>
              <img
                src={image}
                alt={name}
                className="object-cover transition-all duration-500 group-hover:scale-110"
              />
            </AspectRatio>
          </div>
        </Link>

        {/* Product badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-2 z-10">
          {isNew && (
            <span className="bg-primary px-2 py-1 text-xs font-medium text-white rounded animate-pulse">
              New
            </span>
          )}
          {isBestSeller && (
            <span className="bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground rounded">
              Best Seller
            </span>
          )}
          {discount > 0 && (
            <span className="bg-destructive px-2 py-1 text-xs font-medium text-destructive-foreground rounded">
              {discount}% OFF
            </span>
          )}
        </div>

        {/* Quick add button */}
        <div className={`absolute bottom-2 left-2 right-2 transition-all duration-500 ${isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
          <Button className="w-full bg-white text-clay-900 hover:bg-clay-100 shadow-lg">
            <ShoppingBag className="mr-2 h-4 w-4" /> Quick Add
          </Button>
        </div>
      </div>

      <div className={`p-4 bg-white transition-all duration-300 ${isHovered ? 'bg-gradient-to-br from-white to-gray-50' : 'bg-white'}`}>
        <Link to={`/product/${slug}`} className="hover:text-primary">
          <h3 className="font-medium text-clay-900 mb-1 truncate">{name}</h3>
        </Link>

        <div className="flex items-center space-x-1 mb-2">
          <div className="flex items-center text-amber-400">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${i < Math.round(rating) ? 'fill-current' : ''}`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500">({reviewCount})</span>
        </div>

        <div className="flex items-center">
          {salePrice && salePrice > 0 ? (
            <>
              <span className="text-destructive font-medium mr-2">£{salePrice.toFixed(2)}</span>
              <span className="text-gray-500 text-sm line-through">£{price.toFixed(2)}</span>
            </>
          ) : (
            <span className="font-medium">£{price.toFixed(2)}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard3D;
