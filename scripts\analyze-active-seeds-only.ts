#!/usr/bin/env tsx
/**
 * Analyze missing data in ACTIVE seed products only
 * Focus on the 32 active products that customers can actually buy
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeActiveSeeds() {
  console.log('🎯 Analyzing ACTIVE seed products only...\n');
  
  try {
    // Get only ACTIVE products
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        is_active,
        description,
        image,
        sku,
        category_id,
        categories!fk_product_category(name),
        seed_product_attributes(id, seed_type, flowering_time, thc_level, effect)
      `)
      .eq('is_active', true); // Only active products
    
    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }
    
    // Filter to seed products
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('seed') || name.includes('auto') || 
             name.includes('feminised') || name.includes('feminized') ||
             name.includes('female') || name.includes('strain');
    }) || [];
    
    console.log(`📄 Found ${seedProducts.length} ACTIVE seed products\n`);
    
    // Analyze each product
    const analysis = seedProducts.map(product => {
      const missingFields = [];
      
      if (!product.description || product.description.trim().length < 50) {
        missingFields.push('description');
      }
      
      if (!product.image) {
        missingFields.push('image');
      }
      
      if (!product.sku || product.sku.trim().length === 0) {
        missingFields.push('sku');
      }
      
      if (!product.seed_product_attributes?.id) {
        missingFields.push('seed_attributes');
      }
      
      return {
        id: product.id,
        name: product.name,
        price: product.price || 0,
        description_length: product.description?.length || 0,
        has_image: !!product.image,
        has_sku: !!(product.sku && product.sku.trim().length > 0),
        has_seed_attributes: !!product.seed_product_attributes?.id,
        missing_fields: missingFields,
        missing_count: missingFields.length,
        category: product.categories?.name || 'Uncategorized'
      };
    });
    
    // Sort by price (highest first) - focus on premium products
    analysis.sort((a, b) => b.price - a.price);
    
    console.log('📊 ACTIVE Seed Products Analysis:\n');
    
    // Summary
    const totalActive = analysis.length;
    const missingDescription = analysis.filter(p => p.missing_fields.includes('description')).length;
    const missingImage = analysis.filter(p => p.missing_fields.includes('image')).length;
    const missingSeedAttributes = analysis.filter(p => p.missing_fields.includes('seed_attributes')).length;
    const missingAll = analysis.filter(p => p.missing_count >= 3).length;
    
    console.log('📈 ACTIVE Products Summary:');
    console.log(`   Total active seed products: ${totalActive}`);
    console.log(`   Missing descriptions: ${missingDescription} (${Math.round(missingDescription/totalActive*100)}%)`);
    console.log(`   Missing images: ${missingImage} (${Math.round(missingImage/totalActive*100)}%)`);
    console.log(`   Missing seed attributes: ${missingSeedAttributes} (${Math.round(missingSeedAttributes/totalActive*100)}%)`);
    console.log(`   Missing 3+ fields: ${missingAll} (${Math.round(missingAll/totalActive*100)}%)`);
    
    console.log('\n🎯 PRIORITY ORDER FOR ACTIVE PRODUCTS:\n');
    
    analysis.forEach((product, index) => {
      const status = product.missing_count === 0 ? '✅ Complete' : 
                    product.missing_count <= 2 ? '⚠️  Needs work' : '🚨 Critical';
      
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   💰 £${product.price} | ${status} | Missing: ${product.missing_fields.join(', ') || 'None'}`);
      console.log(`   📝 Description: ${product.description_length} chars | 🖼️  Image: ${product.has_image ? '✅' : '❌'} | 🌱 Attributes: ${product.has_seed_attributes ? '✅' : '❌'}`);
      console.log('');
    });
    
    // Create focused CSV for active products
    const activeCSV = [
      'name,price,missing_fields,missing_count,description_length,has_image,has_seed_attributes,category',
      ...analysis.map(p => 
        `"${p.name}",${p.price},"${p.missing_fields.join('; ')}",${p.missing_count},${p.description_length},${p.has_image},${p.has_seed_attributes},"${p.category}"`
      )
    ].join('\n');
    
    writeFileSync('docs/active-seeds-missing-data.csv', activeCSV);
    
    // Recommendations
    const criticalProducts = analysis.filter(p => p.missing_count >= 3);
    const needsWork = analysis.filter(p => p.missing_count > 0 && p.missing_count < 3);
    const complete = analysis.filter(p => p.missing_count === 0);
    
    console.log('💡 FOCUSED RECOMMENDATIONS:\n');
    console.log(`🚨 CRITICAL (${criticalProducts.length} products): Send to Super Agent`);
    console.log('   - Missing 3+ fields');
    console.log('   - High priority for enrichment');
    console.log('   - Focus on top 10 by price\n');
    
    console.log(`⚠️  NEEDS WORK (${needsWork.length} products): Manual entry`);
    console.log('   - Missing 1-2 fields');
    console.log('   - Quick fixes possible');
    console.log('   - Good candidates for manual completion\n');
    
    console.log(`✅ COMPLETE (${complete.length} products): Ready to sell`);
    console.log('   - All fields present');
    console.log('   - Good examples for templates\n');
    
    console.log('🎯 IMMEDIATE ACTION PLAN:');
    console.log('1. Super Agent: Top 10 critical products by price');
    console.log('2. Manual entry: Products missing only 1-2 fields');
    console.log('3. Use complete products as templates');
    console.log('4. Focus on active products only initially');
    
    console.log(`\n📁 Generated: docs/active-seeds-missing-data.csv`);
    
  } catch (err) {
    console.error('❌ Analysis failed:', err);
  }
}

// Run the analysis
analyzeActiveSeeds().catch(console.error);
