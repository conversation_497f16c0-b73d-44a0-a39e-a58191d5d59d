
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const Hero = () => {
  return (
    <section className="relative bg-gray-900 text-white">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1500673922987-e212871fec22')] bg-cover bg-center opacity-40"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-sage-900/70 to-clay-900/70"></div>
      
      <div className="relative container-custom py-24 md:py-32 flex flex-col items-center text-center">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
          Premium CBD &amp; Smoking Accessories
        </h1>
        <p className="text-lg md:text-xl mb-8 max-w-2xl">
          Discover our curated collection of high-quality CBD products and artisanal smoking accessories for the modern enthusiast.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button asChild size="lg" className="bg-sage-500 hover:bg-sage-600 text-white px-8">
            <Link to="/shop">Shop Products</Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="bg-transparent border-white text-white hover:bg-white/10 px-8">
            <Link to="/blog">Read Our Blog</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
