// Auth utilities to help with authentication-related tasks

/**
 * Clears all Supabase auth-related data from local storage and session storage
 * Use this function when you need to completely reset the authentication state
 */
export const clearAuthStorage = () => {
  try {
    // Clear all Supabase auth storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('supabase.auth.expires_at');
    localStorage.removeItem('supabase.auth.expires_in');
    localStorage.removeItem('supabase.auth.refresh_token');
    localStorage.removeItem('supabase.auth.access_token');
    localStorage.removeItem('supabase.auth.provider_token');
    localStorage.removeItem('supabase.auth.provider_refresh_token');
    
    // Clear any other auth-related items
    sessionStorage.removeItem('supabase.auth.token');
    
    console.log('Auth storage cleared successfully');
    return true;
  } catch (error) {
    console.error('Error clearing auth storage:', error);
    return false;
  }
};

/**
 * Force reload the page to ensure a clean state
 * This is useful after clearing auth data
 */
export const forcePageReload = () => {
  window.location.href = window.location.pathname;
};

/**
 * Clear auth storage and reload the page
 * This is a convenience function that combines clearAuthStorage and forcePageReload
 */
export const resetAuthAndReload = () => {
  clearAuthStorage();
  forcePageReload();
};
