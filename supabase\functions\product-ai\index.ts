
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { getRandomImageForQuery, searchGoogleImages, getRandomFallbackImage, isGoogleImageSearchConfigured } from "../_shared/googleImageSearch.ts";

interface RequestData {
  action: "generate-description" | "find-images";
  productName?: string;
  category?: string;
  price?: number;
  keywords?: string[];
  features?: string[];
  tone?: 'professional' | 'casual' | 'persuasive' | 'informative';
}

serve(async (req) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { action, productName, category, keywords, price, features, tone } = await req.json() as RequestData;

    // Check required fields based on action type
    if (action === "generate-description" && (!productName || !category)) {
      return new Response(
        JSON.stringify({ error: "Product name and category are required" }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    if (action === "find-images" && (!productName && !keywords)) {
      return new Response(
        JSON.stringify({ error: "Product name or keywords are required" }),
        { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Get the API keys from the environment
    const geminiApiKey = Deno.env.get("GEMINI_API_KEY");
    const googleApiKey = Deno.env.get("GOOGLE_API_KEY");
    const googleCxId = Deno.env.get("GOOGLE_CX_ID");

    // Debug environment variables
    console.log("Environment variables check:");
    console.log("- GEMINI_API_KEY:", geminiApiKey ? "Set" : "Not set");
    console.log("- GOOGLE_API_KEY:", googleApiKey ? "Set" : "Not set");
    console.log("- GOOGLE_CX_ID:", googleCxId ? "Set" : "Not set");

    if (!geminiApiKey) {
      return new Response(
        JSON.stringify({ error: "Gemini API key not configured" }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Prepare the prompt based on the action
    let prompt = "";
    if (action === "generate-description") {
      const productFeatures = features?.length ? `\nProduct features:\n${features.map(f => `- ${f}`).join('\n')}` : '';
      const productTone = tone || 'professional';

      prompt = `Generate an appealing product description for a CBD/cannabis e-commerce store.
      Product name: "${productName}"
      Product category: "${category}"
      Price: £${price || "unknown"}${productFeatures}

      The description should be:
      - Around 2-3 paragraphs
      - Compelling and marketing-oriented
      - Highlight benefits and features
      - Written in a ${productTone} tone
      - Emphasize quality, effects, and unique selling points
      - Include appropriate keywords for SEO
      - No placeholders or sample text

      Return only the description text, nothing else.`;
    } else if (action === "find-images") {
      // For image search, we'll use Google Custom Search API instead of Gemini
      // This provides more reliable and relevant product images

      const searchTerms = keywords?.length ? keywords.join(" ") : productName;
      const searchQuery = category ? `${searchTerms} ${category}` : searchTerms;

      // Check if Google Image Search is configured
      if (googleApiKey && googleCxId && isGoogleImageSearchConfigured(googleApiKey, googleCxId)) {
        try {
          console.log(`Searching for images with query: "${searchQuery}"`);
          const images = await searchGoogleImages(searchQuery, googleApiKey, googleCxId, 5);

          if (images && images.length > 0) {
            return new Response(
              JSON.stringify({ result: images }),
              { headers: { ...corsHeaders, "Content-Type": "application/json" } }
            );
          } else {
            console.log("No images found, using fallback prompt with Gemini");
          }
        } catch (error) {
          console.error("Error using Google Image Search:", error);
          console.log("Falling back to Gemini for image suggestions");
        }
      } else {
        console.log("Google Image Search not configured, using Gemini for image suggestions");
      }

      // Fallback to Gemini if Google Image Search fails or isn't configured
      prompt = `Suggest 5 high-quality stock image URLs for a product.
      Search terms: "${searchTerms}"
      ${category ? `Category: "${category}"` : ""}

      The images should be:
      - Professional looking product photos
      - Suitable for an e-commerce website
      - Realistic, not AI-generated looking
      - Free for commercial use
      - From sites like Unsplash, Pixabay, or Pexels

      Return a JSON array containing only the URLs, like this:
      ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]`;
    }

    // Call the Gemini API
    const response = await fetch(
      "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.0-pro:generateContent",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-goog-api-key": geminiApiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 1024,
          },
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API error:", errorText);
      return new Response(
        JSON.stringify({ error: "Error calling AI API", details: errorText }),
        { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    const data = await response.json();

    // Extract the text from the response
    let result;
    if (action === "generate-description") {
      result = data.candidates[0].content.parts[0].text;
    } else {
      // For find-images, try to parse the JSON array from the text
      try {
        const text = data.candidates[0].content.parts[0].text;
        // Extract JSON array from the text (handles cases where API returns markdown or extra text)
        const jsonMatch = text.match(/\[.*?\]/s);
        if (jsonMatch) {
          result = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback to category-specific images
          const productCategory = category?.toLowerCase() || "general";
          result = [
            getRandomFallbackImage(productCategory),
            getRandomFallbackImage(productCategory),
            getRandomFallbackImage(productCategory),
          ];
        }
      } catch (e) {
        console.error("Error parsing image URLs:", e);
        // Provide fallback category-specific images
        const productCategory = category?.toLowerCase() || "general";
        result = [
          getRandomFallbackImage(productCategory),
          getRandomFallbackImage(productCategory),
          getRandomFallbackImage(productCategory),
        ];
      }
    }

    return new Response(
      JSON.stringify({ result }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Function error:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error", details: error.message }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});
