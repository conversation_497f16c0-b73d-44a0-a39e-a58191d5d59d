-- Basic Shipping Setup Script (No RLS)
-- Copy and paste this into your Supabase SQL Editor

-- Create shipping zones table
CREATE TABLE IF NOT EXISTS shipping_zones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    countries JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create shipping methods table
CREATE TABLE IF NOT EXISTS shipping_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zone_id UUID NOT NULL REFERENCES shipping_zones(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    free_shipping_threshold DECIMAL(10,2),
    estimated_days_min INTEGER NOT NULL DEFAULT 1,
    estimated_days_max INTEGER NOT NULL DEFAULT 7,
    icon VARCHAR(50) NOT NULL DEFAULT 'standard',
    is_active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_shipping_zones_active ON shipping_zones(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_zones_countries ON shipping_zones USING GIN(countries);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_zone_id ON shipping_methods(zone_id);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_active ON shipping_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_shipping_methods_sort_order ON shipping_methods(sort_order);

-- Insert UK zone
INSERT INTO shipping_zones (name, description, countries, is_active)
SELECT 'United Kingdom', 'Domestic shipping within the UK', '["United Kingdom"]'::jsonb, true
WHERE NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'United Kingdom');

-- Insert EU zone
INSERT INTO shipping_zones (name, description, countries, is_active)
SELECT 'European Union', 'Shipping to EU countries',
'["Ireland", "France", "Germany", "Spain", "Italy", "Netherlands", "Belgium", "Portugal", "Austria", "Denmark", "Sweden", "Finland", "Poland", "Czech Republic", "Hungary", "Slovakia", "Slovenia", "Croatia", "Estonia", "Latvia", "Lithuania", "Luxembourg", "Malta", "Cyprus", "Bulgaria", "Romania", "Greece"]'::jsonb,
true
WHERE NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'European Union');

-- Insert Rest of Europe zone
INSERT INTO shipping_zones (name, description, countries, is_active)
SELECT 'Rest of Europe', 'Shipping to non-EU European countries', '["Norway", "Switzerland"]'::jsonb, true
WHERE NOT EXISTS (SELECT 1 FROM shipping_zones WHERE name = 'Rest of Europe');

-- Insert UK shipping methods
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Free Standard Shipping',
    'Free delivery within 3-5 business days for orders over £50',
    0.00,
    50.00,
    3,
    5,
    'free',
    1
FROM shipping_zones z
WHERE z.name = 'United Kingdom'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'Free Standard Shipping'
);

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Standard Shipping',
    'Delivery within 3-5 business days',
    5.99,
    NULL,
    3,
    5,
    'standard',
    2
FROM shipping_zones z
WHERE z.name = 'United Kingdom'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'Standard Shipping'
);

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Express Shipping',
    'Delivery within 2-3 business days',
    9.99,
    NULL,
    2,
    3,
    'express',
    3
FROM shipping_zones z
WHERE z.name = 'United Kingdom'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'Express Shipping'
);

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Next Day Delivery',
    'Order before 2pm for next day delivery',
    14.99,
    NULL,
    1,
    1,
    'nextDay',
    4
FROM shipping_zones z
WHERE z.name = 'United Kingdom'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'Next Day Delivery'
);

-- Insert EU shipping methods
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Free Shipping',
    'Free delivery within 7-10 business days for orders over £100',
    0.00,
    100.00,
    7,
    10,
    'free',
    1
FROM shipping_zones z
WHERE z.name = 'European Union'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'EU Free Shipping'
);

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Standard Shipping',
    'Delivery within 7-10 business days',
    12.99,
    NULL,
    7,
    10,
    'standard',
    2
FROM shipping_zones z
WHERE z.name = 'European Union'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'EU Standard Shipping'
);

INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'EU Express Shipping',
    'Delivery within 5-7 business days',
    19.99,
    NULL,
    5,
    7,
    'express',
    3
FROM shipping_zones z
WHERE z.name = 'European Union'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'EU Express Shipping'
);

-- Insert Rest of Europe shipping method
INSERT INTO shipping_methods (zone_id, name, description, price, free_shipping_threshold, estimated_days_min, estimated_days_max, icon, sort_order)
SELECT
    z.id,
    'Europe Standard Shipping',
    'Delivery within 10-14 business days',
    15.99,
    NULL,
    10,
    14,
    'standard',
    1
FROM shipping_zones z
WHERE z.name = 'Rest of Europe'
AND NOT EXISTS (
    SELECT 1 FROM shipping_methods sm
    WHERE sm.zone_id = z.id AND sm.name = 'Europe Standard Shipping'
);

-- Verify setup
SELECT
    'Setup Complete!' as status,
    (SELECT COUNT(*) FROM shipping_zones) as zones_created,
    (SELECT COUNT(*) FROM shipping_methods) as methods_created;

-- Show what was created
SELECT 'ZONES' as type, name, (jsonb_array_length(countries)::text || ' countries') as details
FROM shipping_zones
UNION ALL
SELECT 'METHODS' as type, sm.name, ('£' || sm.price::text || ' (' || sm.estimated_days_min::text || '-' || sm.estimated_days_max::text || ' days)') as details
FROM shipping_methods sm
JOIN shipping_zones sz ON sm.zone_id = sz.id
ORDER BY type, name;
