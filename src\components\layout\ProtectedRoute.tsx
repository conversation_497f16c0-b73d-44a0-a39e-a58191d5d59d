import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth, clearAuthStorage } from '@/hooks/auth.basic';
import { toast } from '@/components/ui/use-toast';
import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface ProtectedRouteProps {
  requireAdmin?: boolean;
}

const ProtectedRoute = ({ requireAdmin = false }: ProtectedRouteProps) => {
  // Get auth context values - using the correct property names from the auth hook
  const auth = useAuth();
  const { user, profile, isAdmin: authIsAdmin } = auth;
  // Use a local loading state to avoid TypeScript errors
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();
  
  // Sync with auth loading state
  useEffect(() => {
    // Check if auth is available
    if (auth) {
      // Use a more explicit check for the loading state
      // The property might be named loading or isLoading depending on the context
      const authLoading = 'loading' in auth ? auth.loading : auth.isLoading;
      setIsLoading(!!authLoading);
      console.log('Auth loading state:', authLoading);
    }
  }, [auth]);
  
  // Add a function to clear auth data and reload
  const handleClearAuthData = () => {
    clearAuthStorage();
    window.location.href = '/';
  };
  
  // Function to create an admin profile if one doesn't exist
  const handleCreateAdminProfile = async () => {
    if (!user) return;
    
    try {
      console.log('Creating admin profile for user:', user.id);
      
      // Create a new profile with admin privileges
      const { data, error } = await supabase
        .from('profiles')
        .upsert([
          {
            id: user.id,
            email: user.email,
            is_admin: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ])
        .select();
      
      if (error) {
        console.error('Error creating admin profile:', error);
        toast({
          title: 'Error',
          description: 'Failed to create admin profile: ' + error.message,
          variant: 'destructive'
        });
      } else {
        console.log('Admin profile created successfully:', data);
        toast({
          title: 'Success',
          description: 'Admin profile created. Please reload the page.',
        });
        
        // Reload after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (error) {
      console.error('Exception creating admin profile:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  };
  
  // Manually check admin status to ensure it matches the Navbar logic
  // This is more explicit than relying on the isAdmin from auth context
  let isAdmin = false;
  
  if (user && profile) {
    // First check for hardcoded admin user
    const knownAdminUserIds = ['a0627f38-06d9-48c2-86fc-f0fbca331e18']; // Your user ID
    const isHardcodedAdmin = user && knownAdminUserIds.includes(user.id);
    
    if (isHardcodedAdmin) {
      console.log('Admin access granted based on hardcoded user ID');
      isAdmin = true;
    } else {
      // Standard admin check for other users
      // Check profile.is_admin first (from the profiles table)
      const adminFromProfile = profile?.is_admin === true;
      
      // Then check user metadata as fallback
      const adminFromMetadata = 
        user.user_metadata?.is_admin === true ||
        user.app_metadata?.is_admin === true ||
        user.app_metadata?.admin === true ||
        user.app_metadata?.role === 'admin';
      
      isAdmin = adminFromProfile || adminFromMetadata;
    }
    
    console.log('Admin check in ProtectedRoute:', {
      isHardcodedAdmin,
      adminFromProfile: profile?.is_admin === true,
      finalAdminStatus: isAdmin,
      profileData: profile
    });
  }
  
  console.log('Protected Route Check:', { 
    path: location.pathname,
    isLoading,
    user: !!user, 
    profile: !!profile,
    isAdmin, 
    requireAdmin 
  });

  useEffect(() => {
    // Show toast if admin access is denied
    if (!isLoading && user && requireAdmin && !isAdmin) {
      toast({
        title: "Access Denied",
        description: "You don't have admin permissions to access this area.",
        variant: "destructive"
      });
    }
  }, [user, isAdmin, requireAdmin, isLoading]);
  
  // Show loading state while loading user data
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // If not logged in, redirect to login
  if (!user) {
    console.log('User not authenticated, redirecting to auth page');
    return <Navigate to="/auth" state={{ from: location.pathname }} replace />;
  }
  
  // If admin route but not admin, redirect to home
  if (requireAdmin && !isAdmin) {
    console.log('User is not admin, redirecting to home');
    
    // Add a debug component with a button to clear auth cache
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md text-center">
          <h2 className="text-xl font-bold text-red-800 mb-4">Access Denied</h2>
          <p className="text-gray-700 mb-6">You don't have admin permissions to access this area.</p>
          
          <div className="flex flex-col gap-3">
            <button 
              onClick={() => window.location.href = '/'}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
            >
              Return to Home
            </button>
            
            <button 
              onClick={handleCreateAdminProfile}
              className="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-md transition-colors"
            >
              Create Admin Profile
            </button>
            
            <button 
              onClick={handleClearAuthData}
              className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors"
            >
              Clear Auth Cache and Reload
            </button>
          </div>
          
          <div className="mt-6 p-3 bg-gray-100 rounded text-left text-xs">
            <p className="font-semibold mb-1">Debug Info:</p>
            <pre className="whitespace-pre-wrap overflow-auto max-h-40">
              {JSON.stringify({
                user: user ? { id: user.id, email: user.email } : null,
                profile: profile,
                isAdmin,
                authIsAdmin,
              }, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  }
  
  // Otherwise, render the child route
  console.log('Access granted to protected route');
  return <Outlet />;
};

export default ProtectedRoute;
