import { supabase } from '../integrations/supabase/client';
import fs from 'fs';
import path from 'path';

/**
 * This script uploads Wix images to Supabase storage
 * Run with: npx ts-node src/scripts/upload-wix-images.ts <directory-with-images>
 */

const uploadImagesToSupabase = async (directoryPath: string) => {
  try {
    console.log(`Reading images from directory: ${directoryPath}`);
    
    // Get all files in the directory
    const files = fs.readdirSync(directoryPath);
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(ext);
    });
    
    console.log(`Found ${imageFiles.length} image files`);
    
    // Upload each image to Supabase storage
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < imageFiles.length; i++) {
      const file = imageFiles[i];
      const filePath = path.join(directoryPath, file);
      
      try {
        // Read file content
        const fileContent = fs.readFileSync(filePath);
        
        // Get file extension
        const fileExt = path.extname(file).toLowerCase();
        
        // Determine content type
        let contentType = 'image/jpeg';
        if (fileExt === '.png') contentType = 'image/png';
        if (fileExt === '.webp') contentType = 'image/webp';
        if (fileExt === '.gif') contentType = 'image/gif';
        
        // Upload to Supabase
        const { data, error } = await supabase.storage
          .from('product-images')
          .upload(file, fileContent, {
            contentType,
            upsert: true
          });
        
        if (error) {
          console.error(`Error uploading ${file}:`, error);
          failCount++;
        } else {
          console.log(`Successfully uploaded ${file} (${i + 1}/${imageFiles.length})`);
          successCount++;
        }
      } catch (err) {
        console.error(`Error processing ${file}:`, err);
        failCount++;
      }
      
      // Add a small delay to avoid rate limiting
      if (i % 10 === 0 && i > 0) {
        console.log(`Processed ${i} files. Taking a short break...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`\nUpload complete!`);
    console.log(`Successfully uploaded: ${successCount} files`);
    console.log(`Failed uploads: ${failCount} files`);
    
  } catch (error) {
    console.error('Error:', error);
  }
};

// Get directory path from command line arguments
const directoryPath = process.argv[2];

if (!directoryPath) {
  console.error('Please provide a directory path as an argument');
  process.exit(1);
}

uploadImagesToSupabase(directoryPath);
