/**
 * Google Image Search API for Supabase Edge Functions
 * This module provides utilities to search for images using Google's Custom Search API
 */

// Helper function to validate if Google Image Search is properly configured
export function isGoogleImageSearchConfigured(apiKey: string, cxId: string): boolean {
  return Boolean(apiKey && cxId);
}

/**
 * Search for images using Google's Custom Search JSON API
 * @param query The search query
 * @param apiKey Google API Key
 * @param cxId Google Custom Search Engine ID
 * @param count Number of images to return (max 10)
 * @returns Promise resolving to an array of image URLs
 */
export async function searchGoogleImages(
  query: string, 
  apiKey: string, 
  cxId: string, 
  count: number = 5
): Promise<string[]> {
  // Validate inputs
  if (!query) {
    console.error('No search query provided');
    return [];
  }

  if (!isGoogleImageSearchConfigured(apiKey, cxId)) {
    console.error('Google Custom Search Engine not properly configured');
    return [];
  }

  // Ensure count is within valid range (1-10)
  const safeCount = Math.min(Math.max(1, count), 10);
  
  try {
    // Construct the API URL
    const apiUrl = new URL('https://www.googleapis.com/customsearch/v1');
    
    // Required parameters
    apiUrl.searchParams.append('key', apiKey);
    apiUrl.searchParams.append('cx', cxId);
    apiUrl.searchParams.append('q', query);
    
    // Optional parameters
    apiUrl.searchParams.append('searchType', 'image');
    apiUrl.searchParams.append('num', safeCount.toString());
    apiUrl.searchParams.append('safe', 'active');
    apiUrl.searchParams.append('imgSize', 'large');
    apiUrl.searchParams.append('imgType', 'photo');
    apiUrl.searchParams.append('alt', 'json');
    
    const requestUrl = apiUrl.toString();
    console.log('Making Google Custom Search JSON API request:', requestUrl);
    
    // Make the API request
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });
    
    // Handle response
    if (!response.ok) {
      let errorMessage = `HTTP error ${response.status}`;
      
      try {
        const errorData = await response.json();
        console.error('Google Custom Search API error:', errorData);
        
        if (errorData.error && errorData.error.message) {
          errorMessage = errorData.error.message;
        }
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
      }
      
      throw new Error(`Google Custom Search API error: ${errorMessage}`);
    }
    
    // Parse the JSON response
    const data = await response.json();
    
    // Extract image URLs from the response
    if (data.items && data.items.length > 0) {
      return data.items.map((item: any) => item.link);
    } else {
      console.warn('No image results found for query:', query);
      return [];
    }
  } catch (error) {
    console.error(`Error searching for images with query "${query}":`, error);
    return [];
  }
}

/**
 * Get a random image URL for a specific query
 * @param query The search query
 * @param apiKey Google API Key
 * @param cxId Google Custom Search Engine ID
 * @returns Promise resolving to an image URL or null if none found
 */
export async function getRandomImageForQuery(
  query: string,
  apiKey: string,
  cxId: string
): Promise<string | null> {
  try {
    // Get 3 images to have some variety
    const images = await searchGoogleImages(query, apiKey, cxId, 3);
    
    if (images.length === 0) {
      console.warn(`No images found for query: "${query}"`);
      return null;
    }
    
    // Select a random image from the results
    const randomIndex = Math.floor(Math.random() * images.length);
    const selectedImage = images[randomIndex];
    
    console.log(`Selected image ${randomIndex + 1} of ${images.length} for query: "${query}"`);
    return selectedImage;
  } catch (error) {
    console.error(`Error getting random image for query "${query}":`, error);
    
    // Try a more generic query if the specific one fails
    if (query.includes(' ')) {
      const simplifiedQuery = query.split(' ')[0];
      console.log(`Trying simplified query: "${simplifiedQuery}"`);
      
      try {
        const fallbackImages = await searchGoogleImages(simplifiedQuery, apiKey, cxId, 3);
        if (fallbackImages.length > 0) {
          const fallbackImage = fallbackImages[0];
          console.log(`Found fallback image for simplified query: "${simplifiedQuery}"`);
          return fallbackImage;
        }
      } catch (fallbackError) {
        console.error('Fallback image search also failed:', fallbackError);
      }
    }
    
    return null;
  }
}

// Fallback images for different product categories
export const fallbackImages: Record<string, string[]> = {
  'vaping': [
    'https://cdn.pixabay.com/photo/2019/06/25/04/01/smoke-4297364_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/electronic-cigarette-925759_1280.jpg',
    'https://cdn.pixabay.com/photo/2015/09/05/21/51/smoking-925758_1280.jpg',
  ],
  'cbd oil': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/40/cbd-oil-4391540_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/05/13/22/29/cbd-5169412_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
  ],
  'edibles': [
    'https://cdn.pixabay.com/photo/2020/04/08/13/49/chocolate-5017999_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/06/05/15/brownie-4605471_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/03/03/21/11/hemp-4033386_1280.jpg',
  ],
  'accessories': [
    'https://cdn.pixabay.com/photo/2019/11/16/02/41/smoking-4629019_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/16/02/40/smoking-4629018_1280.jpg',
    'https://cdn.pixabay.com/photo/2020/04/09/13/27/cannabis-5021767_1280.jpg',
  ],
  'general': [
    'https://cdn.pixabay.com/photo/2019/08/08/01/39/cbd-4391539_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/04/15/11/47/cbd-4129323_1280.jpg',
    'https://cdn.pixabay.com/photo/2019/11/19/07/50/hemp-oil-4637291_1280.jpg',
  ],
};

/**
 * Get a random fallback image for a category
 * @param category The product category
 * @returns A random image URL from the fallback collection
 */
export function getRandomFallbackImage(category: string): string {
  // Default to general category if the requested one doesn't exist
  const categoryImages = fallbackImages[category.toLowerCase()] || fallbackImages['general'];
  
  // Get a random image from the available ones
  const randomIndex = Math.floor(Math.random() * categoryImages.length);
  return categoryImages[randomIndex];
}
