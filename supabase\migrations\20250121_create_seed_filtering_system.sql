-- Create seed filtering system tables and functions
-- This migration creates the complete infrastructure for seed product filtering

-- Create filter_categories table
CREATE TABLE IF NOT EXISTS filter_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 0,
  category_id UUID REFERENCES categories(id),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create filter_options table
CREATE TABLE IF NOT EXISTS filter_options (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID REFERENCES filter_categories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category_id, name)
);

-- Create product_filters table for many-to-many relationships
CREATE TABLE IF NOT EXISTS product_filters (
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  filter_option_id UUID REFERENCES filter_options(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (product_id, filter_option_id)
);

-- Create seed_product_attributes table for detailed seed information
CREATE TABLE IF NOT EXISTS seed_product_attributes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE UNIQUE,
  seed_type VARCHAR(50),
  flowering_time VARCHAR(50),
  yield VARCHAR(50),
  thc_level VARCHAR(50),
  cbd_level VARCHAR(50),
  effect VARCHAR(50),
  seed_family VARCHAR(100),
  genetics VARCHAR(200),
  breeder VARCHAR(100),
  is_manually_verified BOOLEAN DEFAULT FALSE,
  confidence_score INTEGER DEFAULT 0, -- 0-100 confidence in data accuracy
  data_source VARCHAR(100), -- Source of the enrichment data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_filter_categories_category_id ON filter_categories(category_id);
CREATE INDEX IF NOT EXISTS idx_filter_categories_active ON filter_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_filter_options_category_id ON filter_options(category_id);
CREATE INDEX IF NOT EXISTS idx_filter_options_active ON filter_options(is_active);
CREATE INDEX IF NOT EXISTS idx_product_filters_product_id ON product_filters(product_id);
CREATE INDEX IF NOT EXISTS idx_product_filters_filter_option_id ON product_filters(filter_option_id);
CREATE INDEX IF NOT EXISTS idx_seed_attributes_product_id ON seed_product_attributes(product_id);

-- Enable RLS
ALTER TABLE filter_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE filter_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_filters ENABLE ROW LEVEL SECURITY;
ALTER TABLE seed_product_attributes ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Allow public read access for filtering
CREATE POLICY "Public can view filter categories" ON filter_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view filter options" ON filter_options
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view product filters" ON product_filters
  FOR SELECT USING (true);

CREATE POLICY "Public can view seed attributes" ON seed_product_attributes
  FOR SELECT USING (true);

-- Admin policies for authenticated users
CREATE POLICY "Authenticated users can manage filter categories" ON filter_categories
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage filter options" ON filter_options
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage product filters" ON product_filters
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage seed attributes" ON seed_product_attributes
  FOR ALL USING (auth.role() = 'authenticated');

-- Insert initial filter categories
INSERT INTO filter_categories (name, display_name, display_order) VALUES
('seed_type', 'Seed Type', 1),
('flowering_time', 'Flowering Time', 2),
('yield', 'Yield', 3),
('thc_level', 'THC Level', 4),
('cbd_level', 'CBD Level', 5),
('effect', 'Effect', 6),
('seed_family', 'Seed Family', 7),
('genetics', 'Genetics', 8)
ON CONFLICT (name) DO NOTHING;

-- Insert initial filter options for seed_type
INSERT INTO filter_options (category_id, name, display_name, display_order)
SELECT fc.id, 'autoflower', 'Autoflower', 1
FROM filter_categories fc WHERE fc.name = 'seed_type'
UNION ALL
SELECT fc.id, 'feminised', 'Feminised', 2
FROM filter_categories fc WHERE fc.name = 'seed_type'
UNION ALL
SELECT fc.id, 'regular', 'Regular', 3
FROM filter_categories fc WHERE fc.name = 'seed_type'
ON CONFLICT (category_id, name) DO NOTHING;

-- Insert initial filter options for flowering_time
INSERT INTO filter_options (category_id, name, display_name, display_order)
SELECT fc.id, 'short', 'Short (6-8 weeks)', 1
FROM filter_categories fc WHERE fc.name = 'flowering_time'
UNION ALL
SELECT fc.id, 'medium', 'Medium (8-10 weeks)', 2
FROM filter_categories fc WHERE fc.name = 'flowering_time'
UNION ALL
SELECT fc.id, 'long', 'Long (10+ weeks)', 3
FROM filter_categories fc WHERE fc.name = 'flowering_time'
ON CONFLICT (category_id, name) DO NOTHING;

-- Insert initial filter options for effect
INSERT INTO filter_options (category_id, name, display_name, display_order)
SELECT fc.id, 'relaxing', 'Relaxing', 1
FROM filter_categories fc WHERE fc.name = 'effect'
UNION ALL
SELECT fc.id, 'energizing', 'Energizing', 2
FROM filter_categories fc WHERE fc.name = 'effect'
UNION ALL
SELECT fc.id, 'balanced', 'Balanced', 3
FROM filter_categories fc WHERE fc.name = 'effect'
UNION ALL
SELECT fc.id, 'creative', 'Creative', 4
FROM filter_categories fc WHERE fc.name = 'effect'
UNION ALL
SELECT fc.id, 'euphoric', 'Euphoric', 5
FROM filter_categories fc WHERE fc.name = 'effect'
ON CONFLICT (category_id, name) DO NOTHING;
