/**
 * Configuration for image search optimization
 * Customize these terms to improve relevance for your specific industry
 */

export const IMAGE_SEARCH_CONFIG = {
  // Terms to add to searches for better context
  contextTerms: {
    // Seeds & Cannabis
    cannabis: ['legal', 'hemp', 'plant', 'seeds', 'cultivation', 'business'],
    seeds: ['hemp', 'cannabis', 'legal', 'plant', 'growing', 'cultivation'],
    hemp: ['legal', 'plant', 'seeds', 'natural', 'organic', 'business'],

    // CBD Products
    cbd: ['oil', 'wellness', 'health', 'natural', 'organic', 'products'],
    oil: ['cbd', 'hemp', 'natural', 'wellness', 'health', 'organic'],

    // Vaping & Vaporizers
    vaping: ['device', 'vaporizer', 'electronic', 'modern', 'technology'],
    vaporizer: ['device', 'vaping', 'electronic', 'modern', 'portable'],
    vape: ['device', 'electronic', 'modern', 'vaporizer', 'pen'],

    // Bongs & Water Pipes
    bong: ['glass', 'water', 'pipe', 'smoking', 'accessories'],
    bongs: ['glass', 'water', 'pipes', 'smoking', 'accessories'],
    pipe: ['glass', 'smoking', 'water', 'accessories', 'equipment'],

    // Smoking Accessories
    smoking: ['accessories', 'equipment', 'lifestyle', 'pipes', 'glass'],
    accessories: ['smoking', 'equipment', 'lifestyle', 'modern'],
    grinder: ['herb', 'accessories', 'equipment', 'metal', 'smoking'],

    // Legal & Business
    legal: ['business', 'professional', 'office', 'consultation', 'law'],
    business: ['professional', 'office', 'legal', 'modern', 'commercial'],

    // General Wellness
    wellness: ['health', 'natural', 'lifestyle', 'organic', 'holistic'],
    health: ['wellness', 'natural', 'lifestyle', 'organic', 'care'],

    // Default for other topics
    default: ['lifestyle', 'modern', 'professional']
  },

  // Terms that indicate relevant images
  includeTerms: [
    // Business & Professional
    'business', 'professional', 'office', 'corporate', 'meeting',
    'legal', 'law', 'lawyer', 'consultation', 'advice', 'commercial',

    // Health & Wellness
    'health', 'wellness', 'medical', 'natural', 'organic',
    'therapy', 'treatment', 'care', 'healing', 'holistic',

    // Cannabis & Hemp Related
    'plant', 'green', 'nature', 'leaf', 'herb', 'botanical',
    'hemp', 'cannabis', 'cbd', 'extract', 'oil', 'seeds',
    'cultivation', 'growing', 'harvest', 'farm', 'agriculture',

    // CBD Products
    'cbd', 'oil', 'tincture', 'drops', 'bottle', 'product',
    'supplement', 'wellness', 'natural', 'organic', 'pure',

    // Vaping & Vaporizers
    'vape', 'vaping', 'vaporizer', 'device', 'electronic', 'pen',
    'portable', 'battery', 'technology', 'modern', 'sleek',

    // Bongs & Water Pipes
    'bong', 'pipe', 'glass', 'water', 'smoking', 'clear',
    'transparent', 'artistic', 'crafted', 'quality',

    // Smoking Accessories
    'accessories', 'equipment', 'grinder', 'lighter', 'storage',
    'container', 'jar', 'metal', 'wood', 'ceramic', 'tools',

    // Materials & Quality
    'glass', 'metal', 'wood', 'ceramic', 'stainless', 'aluminum',
    'quality', 'premium', 'handcrafted', 'artisan', 'design',

    // Technology & Modern
    'technology', 'modern', 'innovation', 'digital', 'contemporary',
    'sleek', 'minimalist', 'elegant', 'sophisticated',

    // People & Lifestyle
    'lifestyle', 'people', 'person', 'adult', 'man', 'woman',
    'hands', 'holding', 'using', 'working', 'relaxing', 'enjoying'
  ],

  // Terms that indicate irrelevant images (only exclude clearly inappropriate content)
  excludeTerms: [
    // Only exclude clearly inappropriate or low-quality content
    'cartoon', 'anime', 'clipart', 'vector', 'illustration',

    // Children's content (for age-appropriate business)
    'child', 'baby', 'kid', 'toy', 'playground', 'school',

    // Inappropriate Content
    'sexy', 'nude', 'explicit', 'inappropriate', 'nsfw',

    // Low quality indicators
    'blurry', 'pixelated', 'low quality', 'poor resolution'
  ],

  // Pixabay categories to focus on
  preferredCategories: [
    'health', 'nature', 'business', 'science', 'places', 'people'
  ],

  // Search parameters
  searchParams: {
    minWidth: 800,
    minHeight: 600,
    imageType: 'photo',
    safeSearch: true,
    order: 'popular' // popular, latest, ec (editor's choice)
  }
};

/**
 * Get context terms for a specific topic
 */
export function getContextTerms(topic: string): string[] {
  const lowerTopic = topic.toLowerCase();

  for (const [key, terms] of Object.entries(IMAGE_SEARCH_CONFIG.contextTerms)) {
    if (lowerTopic.includes(key)) {
      return terms;
    }
  }

  return IMAGE_SEARCH_CONFIG.contextTerms.default;
}

/**
 * Check if an image is relevant based on its tags
 */
export function isImageRelevant(tags: string, originalQuery: string = ''): boolean {
  const lowerTags = tags.toLowerCase();
  const lowerQuery = originalQuery.toLowerCase();

  // Always exclude inappropriate content
  const hasExcludedTerms = IMAGE_SEARCH_CONFIG.excludeTerms.some(term =>
    lowerTags.includes(term)
  );

  if (hasExcludedTerms) {
    return false;
  }

  // For product-related queries, be more strict
  const isProductQuery = ['cannabis', 'cbd', 'vape', 'bong', 'pipe', 'smoking', 'seeds'].some(term =>
    lowerQuery.includes(term)
  );

  if (isProductQuery) {
    // For product queries, require relevant terms
    const hasIncludedTerms = IMAGE_SEARCH_CONFIG.includeTerms.some(term =>
      lowerTags.includes(term)
    );
    return hasIncludedTerms;
  } else {
    // For general topics, be more permissive - just exclude inappropriate content
    return true;
  }
}

/**
 * Calculate relevance score for an image
 */
export function calculateRelevanceScore(tags: string, originalQuery: string): number {
  const lowerTags = tags.toLowerCase();
  const queryWords = originalQuery.toLowerCase().split(/\s+/);

  let score = 0;

  // Bonus for exact query word matches
  queryWords.forEach(word => {
    if (lowerTags.includes(word)) {
      score += 10;
    }
  });

  // Bonus for include terms
  IMAGE_SEARCH_CONFIG.includeTerms.forEach(term => {
    if (lowerTags.includes(term)) {
      score += 5;
    }
  });

  // Penalty for exclude terms
  IMAGE_SEARCH_CONFIG.excludeTerms.forEach(term => {
    if (lowerTags.includes(term)) {
      score -= 20;
    }
  });

  return score;
}
