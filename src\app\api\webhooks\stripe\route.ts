import { NextRequest, NextResponse } from 'next/server';
import { getPaymentService } from '@/services/paymentService';

/**
 * Stripe webhook handler
 * This endpoint receives notifications from <PERSON><PERSON> when a payment status changes
 */
export async function POST(request: NextRequest) {
  try {
    // Get the payment service
    const paymentService = getPaymentService();
    
    // Get the request body
    const body = await request.text();
    const signature = request.headers.get('stripe-signature') || '';
    
    // In a real implementation, you would verify the webhook signature
    // const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    
    // For now, we'll parse the body manually
    const event = JSON.parse(body);
    
    // Handle the event based on its type
    if (event.type === 'payment_intent.succeeded') {
      const paymentIntent = event.data.object;
      const sessionId = paymentIntent.metadata?.session_id;
      
      if (sessionId) {
        // Verify the payment
        const isVerified = await paymentService.verifyStripePayment(
          sessionId,
          paymentIntent.id
        );
        
        if (isVerified) {
          // Process the payment
          await paymentService.processSuccessfulPayment(sessionId);
          
          console.log('Stripe payment processed successfully');
        } else {
          console.error('Stripe payment verification failed');
        }
      }
    }
    
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing Stripe webhook:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    );
  }
}