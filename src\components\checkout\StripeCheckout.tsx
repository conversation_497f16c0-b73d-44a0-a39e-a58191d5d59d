import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { CartItem } from '@/context/CartContext';
import { getPaymentService } from '@/services/paymentService';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

// This would be replaced with your actual Stripe publishable key when available
const PLACEHOLDER_STRIPE_KEY = 'pk_test_placeholder';

interface StripeCheckoutFormProps {
  clientSecret: string;
  onSuccess: (orderId: string) => void;
  onCancel: () => void;
}

function CheckoutForm({ clientSecret, onSuccess, onCancel }: StripeCheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();
  const paymentService = getPaymentService();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Trigger form validation and wallet collection
      const { error: submitError } = await elements.submit();
      if (submitError) {
        setErrorMessage(submitError.message || 'An error occurred with your payment');
        setIsLoading(false);
        return;
      }

      // Confirm the payment
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/checkout/confirmation`,
        },
        redirect: 'always',
      });

      if (error) {
        setErrorMessage(error.message || 'An error occurred with your payment');
        toast({
          title: 'Payment Error',
          description: error.message || 'There was a problem processing your payment.',
          variant: 'destructive',
        });
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Extract the session ID from the client secret
        // Client secret format: 'pi_XXX_secret_YYY'
        const sessionId = clientSecret.split('_secret_')[0];
        
        // Process the successful payment
        const orderId = await paymentService.processSuccessfulPayment(sessionId);
        
        toast({
          title: 'Payment Successful',
          description: 'Your payment has been processed successfully.',
        });
        
        onSuccess(orderId);
      }
    } catch (err) {
      console.error('Stripe payment error:', err);
      setErrorMessage('An unexpected error occurred. Please try again.');
      toast({
        title: 'Payment Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    }
    
    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />
      
      {errorMessage && (
        <Alert variant="destructive">
          <AlertTitle>Payment Error</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex flex-col space-y-2">
        <Button 
          type="submit" 
          disabled={!stripe || !elements || isLoading}
          className="w-full"
        >
          {isLoading ? 'Processing...' : 'Pay Now'}
        </Button>
        
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
      </div>
      
      <p className="text-sm text-gray-500 mt-4">
        By clicking this button, you agree to our Terms of Service and authorize
        a charge to your payment method.
      </p>
    </form>
  );
}

interface StripeCheckoutProps {
  items?: CartItem[];
  shipping?: number;
  tax?: number;
  shippingCost?: number;
  taxAmount?: number;
  onSuccess: (orderId: string) => void;
  onCancel: () => void;
}

export function StripeCheckout({
  items = [],
  shipping,
  tax,
  shippingCost,
  taxAmount,
  onSuccess,
  onCancel
}: StripeCheckoutProps) {
  // Use the appropriate props based on what's provided
  const actualShipping = shippingCost ?? shipping ?? 0;
  const actualTax = taxAmount ?? tax ?? 0;
  const [stripePromise, setStripePromise] = useState<Promise<any> | null>(null);
  const [clientSecret, setClientSecret] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const paymentService = getPaymentService();

  useEffect(() => {
    // Initialize Stripe
    // In a real implementation, you would load the key from environment variables
    setStripePromise(loadStripe(PLACEHOLDER_STRIPE_KEY));
    
    const initializeStripePayment = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Create Stripe payment session
        const { clientSecret } = await paymentService.createStripePayment(items, actualShipping, actualTax);
        setClientSecret(clientSecret);
      } catch (err) {
        console.error('Stripe initialization error:', err);
        setError('Unable to initialize Stripe checkout. Please try again.');
        toast({
          title: 'Payment Error',
          description: 'There was a problem initializing Stripe checkout.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializeStripePayment();
  }, [items, shipping, tax]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p>Initializing payment...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Payment Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!clientSecret) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Configuration Error</AlertTitle>
        <AlertDescription>
          Stripe is not properly configured. Please contact customer support.
        </AlertDescription>
      </Alert>
    );
  }

  const options = {
    clientSecret,
    appearance: {
      theme: 'stripe',
      variables: {
        colorPrimary: '#10b981', // Tailwind emerald-500
        colorBackground: '#ffffff',
        colorText: '#1f2937', // Tailwind gray-800
        colorDanger: '#ef4444', // Tailwind red-500
        fontFamily: 'ui-sans-serif, system-ui, sans-serif',
        borderRadius: '0.375rem', // Tailwind rounded-md
      },
    },
  };

  return (
    <div className="w-full">
      {stripePromise && clientSecret && (
        <Elements stripe={stripePromise} options={options}>
          <CheckoutForm 
            clientSecret={clientSecret} 
            onSuccess={onSuccess} 
            onCancel={onCancel} 
          />
        </Elements>
      )}
      
      <div className="mt-6 text-sm text-gray-500">
        <p className="mb-2">
          <strong>Note:</strong> This is a placeholder for Stripe integration. 
          When your client provides the Stripe API keys, this component will be fully functional.
        </p>
        <p>
          Your payment information is processed securely by Stripe. We do not store your 
          complete credit card details on our servers.
        </p>
      </div>
    </div>
  );
}