import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixProductOptions() {
  console.log('Starting to fix product options...');
  
  try {
    // First, check if the options column exists in the products table
    console.log('Checking if options column exists...');
    const { data: columns, error: columnsError } = await supabase
      .rpc('exec_sql', {
        query: "SELECT column_name FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'options'"
      });
    
    if (columnsError) {
      console.error('Error checking columns:', columnsError);
      // Continue anyway, we'll handle errors later
    }
    
    // If options column doesn't exist, create it
    if (!columns || columns.length === 0) {
      console.log('Options column does not exist, creating it...');
      const { error: createColumnError } = await supabase
        .rpc('exec_sql', {
          query: "ALTER TABLE products ADD COLUMN IF NOT EXISTS options JSONB DEFAULT '{}'::jsonb"
        });
      
      if (createColumnError) {
        console.error('Error creating options column:', createColumnError);
        throw createColumnError;
      }
      
      console.log('Options column created successfully');
    } else {
      console.log('Options column already exists');
    }
    // Fetch all products with legacy option format
    const { data: products, error } = await supabase
      .from('products')
      .select('id, option_name1, option_type1, option_description1, option_price_adjustment1, option_name2, option_type2, option_description2, option_price_adjustment2, option_name3, option_type3, option_description3, option_price_adjustment3, options')
      .or('option_name1.neq.null,option_name2.neq.null,option_name3.neq.null');
    
    if (error) {
      throw error;
    }
    
    console.log(`Found ${products.length} products with legacy options to process`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      // Initialize options object if it doesn't exist
      const options = product.options || {};
      let hasUpdates = false;
      
      // Process option 1
      if (product.option_name1) {
        try {
          const optionName = product.option_name1.trim();
          let optionValues = [];
          
          // Extract option values from price adjustments if available
          if (product.option_price_adjustment1) {
            try {
              // Price adjustments are typically stored as semicolon-separated values
              // like "0;+2.00;+5.00" where each value corresponds to an option value
              const priceAdjustments = product.option_price_adjustment1.split(';');
              
              // Generate placeholder option values based on the number of price adjustments
              // This is a fallback when we don't have actual option values
              optionValues = priceAdjustments.map((_, index) => `Option ${index + 1}`);
            } catch (e) {
              console.error(`Error parsing option_price_adjustment1 for product ${product.id}:`, e);
              // Default to at least one option value if parsing fails
              optionValues = ['Default Option'];
            }
          } else {
            // If no price adjustments, create at least one default option
            optionValues = ['Default Option'];
          }
          
          // Add to options object
          options[optionName] = optionValues;
          hasUpdates = true;
        } catch (e) {
          console.error(`Error processing option 1 for product ${product.id}:`, e);
        }
      }
      
      // Process option 2
      if (product.option_name2) {
        try {
          const optionName = product.option_name2.trim();
          let optionValues = [];
          
          // Extract option values from price adjustments if available
          if (product.option_price_adjustment2) {
            try {
              const priceAdjustments = product.option_price_adjustment2.split(';');
              optionValues = priceAdjustments.map((_, index) => `Option ${index + 1}`);
            } catch (e) {
              console.error(`Error parsing option_price_adjustment2 for product ${product.id}:`, e);
              optionValues = ['Default Option'];
            }
          } else {
            optionValues = ['Default Option'];
          }
          
          // Add to options object
          options[optionName] = optionValues;
          hasUpdates = true;
        } catch (e) {
          console.error(`Error processing option 2 for product ${product.id}:`, e);
        }
      }
      
      // Process option 3
      if (product.option_name3) {
        try {
          const optionName = product.option_name3.trim();
          let optionValues = [];
          
          // Extract option values from price adjustments if available
          if (product.option_price_adjustment3) {
            try {
              const priceAdjustments = product.option_price_adjustment3.split(';');
              optionValues = priceAdjustments.map((_, index) => `Option ${index + 1}`);
            } catch (e) {
              console.error(`Error parsing option_price_adjustment3 for product ${product.id}:`, e);
              optionValues = ['Default Option'];
            }
          } else {
            optionValues = ['Default Option'];
          }
          
          // Add to options object
          options[optionName] = optionValues;
          hasUpdates = true;
        } catch (e) {
          console.error(`Error processing option 3 for product ${product.id}:`, e);
        }
      }
      
      // Update the product if we have changes
      if (hasUpdates) {
        const { error: updateError } = await supabase
          .from('products')
          .update({ options })
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating options for product ${product.id}:`, updateError);
          continue;
        }
        
        updatedCount++;
        console.log(`Updated product ${product.id} with fixed options`);
      }
    }
    
    console.log(`Successfully updated ${updatedCount} products with fixed options`);
    
  } catch (error) {
    console.error('Error fixing product options:', error);
  }
}

// Run the function
fixProductOptions()
  .then(() => {
    console.log('Product options fix script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Product options fix script failed:', error);
    process.exit(1);
  });
