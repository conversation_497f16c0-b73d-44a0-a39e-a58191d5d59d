/**
 * Utility to seed default blog categories
 * Run this once to populate your blog categories table
 */

import { supabase } from '@/lib/supabase';

const DEFAULT_CATEGORIES = [
  {
    name: 'Cannabis Seeds',
    slug: 'cannabis-seeds',
    description: 'Everything about cannabis seeds, growing, and cultivation'
  },
  {
    name: 'CBD Products',
    slug: 'cbd-products',
    description: 'CBD oils, tinctures, and wellness products'
  },
  {
    name: 'Vaporizers',
    slug: 'vaporizers',
    description: 'Vaping devices, reviews, and guides'
  },
  {
    name: 'Bongs & Water Pipes',
    slug: 'bongs-water-pipes',
    description: 'Glass bongs, water pipes, and smoking accessories'
  },
  {
    name: 'Smoking Accessories',
    slug: 'smoking-accessories',
    description: 'Grinders, lighters, and other smoking equipment'
  },
  {
    name: 'Health & Wellness',
    slug: 'health-wellness',
    description: 'Health benefits, wellness tips, and lifestyle content'
  },
  {
    name: 'How-To Guides',
    slug: 'how-to-guides',
    description: 'Step-by-step guides and tutorials'
  },
  {
    name: 'Industry News',
    slug: 'industry-news',
    description: 'Latest news and updates from the cannabis industry'
  },
  {
    name: 'Legal Information',
    slug: 'legal-information',
    description: 'Legal updates, regulations, and compliance information'
  },
  {
    name: 'Product Reviews',
    slug: 'product-reviews',
    description: 'Detailed reviews of products and equipment'
  }
];

export async function seedBlogCategories(): Promise<void> {
  try {
    console.log('🌱 Seeding blog categories...');
    
    // Check if categories already exist
    const { data: existingCategories, error: checkError } = await supabase
      .from('blog_categories')
      .select('slug');
    
    if (checkError) {
      throw checkError;
    }
    
    const existingSlugs = existingCategories?.map(cat => cat.slug) || [];
    
    // Filter out categories that already exist
    const categoriesToInsert = DEFAULT_CATEGORIES.filter(
      category => !existingSlugs.includes(category.slug)
    );
    
    if (categoriesToInsert.length === 0) {
      console.log('✅ All default categories already exist');
      return;
    }
    
    // Insert new categories
    const { data, error } = await supabase
      .from('blog_categories')
      .insert(categoriesToInsert)
      .select();
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ Successfully created ${categoriesToInsert.length} blog categories:`);
    categoriesToInsert.forEach(cat => {
      console.log(`   - ${cat.name} (${cat.slug})`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding blog categories:', error);
    throw error;
  }
}

// Auto-run in development mode
if (import.meta.env.DEV) {
  // Run after a delay to ensure Supabase is initialized
  setTimeout(() => {
    seedBlogCategories().catch(console.error);
  }, 3000);
}
