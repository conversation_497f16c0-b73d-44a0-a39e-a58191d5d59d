import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import type { Product } from '@/types/database';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import CategoryBanner from '@/components/shop/CategoryBanner';
import { categoryBannerImages, categoryDescriptions } from '@/data/categoryBanners';
import TransitionLayout from '@/components/layout/TransitionLayout';
import SearchInput from '@/components/shop/SearchInput';
import SubcategoryImageGrid from '@/components/shop/SubcategoryImageGrid';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import ProductGridSimple from '@/components/products/ProductGridSimple';

// Database category type from Supabase
interface DatabaseCategory {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string | null;
}

// Extended Category interface for UI
interface Category extends DatabaseCategory {
  subcategories?: Category[];
}

// SubCategory interface
interface SubCategory extends DatabaseCategory {
  parent?: Category;
}

const ShopPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<SubCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
  const [selectedBrand, setSelectedBrand] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  
  // Refs for debouncing
  const searchTimeoutRef = useRef<number>();
  const urlUpdateTimeoutRef = useRef<number>();
  
  // Flag to prevent URL updates from triggering state changes
  const isUpdatingFromUrlRef = useRef(false);

  // Effect to scroll to top when URL parameters change
  useEffect(() => {
    // Use a small timeout to ensure the scroll happens after the transition starts
    const timer = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 50);
    
    return () => clearTimeout(timer);
  }, [searchParams]);
  
  // One-time initialization from URL parameters when categories are loaded
  useEffect(() => {
    // Only run this effect when categories are first loaded
    if (categories.length === 0 || isUpdatingFromUrlRef.current) return;
    
    const categoryParam = searchParams.get('category');
    const subcategoryParam = searchParams.get('subcategory');
    const brandParam = searchParams.get('brand');
    const queryParam = searchParams.get('search');
    
    console.log('Initial URL Parameters:', { categoryParam, subcategoryParam, brandParam, queryParam });
    
    // Set the flag to prevent recursive updates
    isUpdatingFromUrlRef.current = true;
    
    try {
      // Handle category parameter if present
      if (categoryParam) {
        const categoryBySlug = categories.find(cat => cat.slug === categoryParam);
        if (categoryBySlug) {
          console.log('Found category by slug:', categoryBySlug.name);
          setSelectedCategory(categoryBySlug.id);
        } else {
          console.log('Could not find category with slug:', categoryParam);
          setSelectedCategory('all');
        }
      } else {
        setSelectedCategory('all');
      }
      
      // Handle subcategory parameter if present
      if (subcategoryParam && subcategories.length > 0) {
        const subcategoryBySlug = subcategories.find(subcat => subcat.slug === subcategoryParam);
        if (subcategoryBySlug) {
          console.log('Found subcategory by slug:', subcategoryBySlug.name);
          setSelectedSubcategory(subcategoryBySlug.id);
        } else {
          console.log('Could not find subcategory with slug:', subcategoryParam);
          setSelectedSubcategory('all');
        }
      } else {
        setSelectedSubcategory('all');
      }
      
      // Handle brand parameter if present
      if (brandParam) {
        setSelectedBrand(brandParam);
      } else {
        setSelectedBrand('all');
      }
      
      // Handle search query if present
      if (queryParam) {
        setSearchQuery(queryParam);
      } else {
        setSearchQuery('');
      }
    } finally {
      // Reset the flag after a short delay to allow state updates to complete
      setTimeout(() => {
        isUpdatingFromUrlRef.current = false;
      }, 100);
    }
  }, [categories, subcategories, searchParams]);

  // Fetch categories and subcategories
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        // Fetch all categories
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .order('name');
          
        if (error) throw error;
        
        if (!data) {
          console.error('No category data returned');
          setLoading(false);
          return;
        }
        
        // Process the data - ensure we have all required fields
        const typedData = data as DatabaseCategory[];
        
        // Filter into main categories and subcategories
        const mainCategories = typedData.filter(cat => !cat.parent_id);
        const subCategories = typedData.filter(cat => cat.parent_id);
        
        console.log('Main categories:', mainCategories);
        console.log('Subcategories:', subCategories);
        
        // Set the categories state
        setCategories(mainCategories as Category[]);
        
        // Process subcategories with their parent references
        const processedSubcategories = subCategories.map(sub => {
          const parent = mainCategories.find(cat => cat.id === sub.parent_id);
          return {
            ...sub,
            parent: parent as Category | undefined
          } as SubCategory;
        });
        
        setSubcategories(processedSubcategories);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching categories:', error);
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Update subcategory when category changes
  useEffect(() => {
    if (selectedCategory === 'all') {
      setSelectedSubcategory('all');
    }
  }, [selectedCategory]);
  
  // Update URL based on current state without triggering state changes
  const updateURL = () => {
    // Don't update URL if we're currently processing URL parameters
    if (isUpdatingFromUrlRef.current) return;
    
    // Clear any pending URL updates
    if (urlUpdateTimeoutRef.current) {
      clearTimeout(urlUpdateTimeoutRef.current);
    }
    
    // Schedule the URL update
    urlUpdateTimeoutRef.current = window.setTimeout(() => {
      const params = new URLSearchParams();
      
      if (selectedCategory !== 'all') {
        // Find the category by ID and use its slug in the URL for better SEO
        const category = categories.find(cat => cat.id === selectedCategory);
        if (category) {
          params.set('category', category.slug);
        }
      }
      
      if (selectedSubcategory !== 'all') {
        // Find the subcategory by ID and use its slug in the URL for better SEO
        const subcategory = subcategories.find(subcat => subcat.id === selectedSubcategory);
        if (subcategory) {
          params.set('subcategory', subcategory.slug);
        }
      }
      
      if (selectedBrand !== 'all') {
        params.set('brand', selectedBrand);
      }
      
      if (searchQuery) {
        params.set('search', searchQuery);
      }
      
      // Log the current state for debugging
      console.log('Updating URL with state:', {
        selectedCategory,
        selectedSubcategory,
        params: params.toString()
      });
      
      // Set the flag to prevent recursive updates
      isUpdatingFromUrlRef.current = true;
      
      // Update the URL
      setSearchParams(params, { replace: true });
      
      // Reset the flag after a short delay
      setTimeout(() => {
        isUpdatingFromUrlRef.current = false;
      }, 100);
    }, 50); // Short delay to batch multiple state updates
  };

  // Helper function to get category image URL
  const getCategoryImageUrl = (category: Category | SubCategory) => {
    if (!category.image) {
      return categoryBannerImages[category.slug] || 'https://placehold.co/200x200?text=No+Image';
    }
    
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
    
    if (category.image.startsWith('http')) {
      return category.image;
    } else if (category.image.startsWith('/storage/')) {
      return `${supabaseUrl}${category.image}`;
    } else if (category.image.startsWith('category-images/')) {
      return `${supabaseUrl}/storage/v1/object/public/${category.image}`;
    } else {
      return `${supabaseUrl}/storage/v1/object/public/category-images/${category.image}`;
    }
  };

  if (loading) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">Shop</h1>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
        </div>
      </div>
    );
  }

  // Get relevant subcategories for the selected category
  const filteredSubcategories = selectedCategory !== 'all' 
    ? subcategories.filter((subcat: SubCategory) => subcat.parent_id === selectedCategory)
    : [];

  // Get category name for display
  const categoryName = selectedCategory !== 'all'
    ? categories.find(cat => cat.id === selectedCategory)?.name || 
      subcategories.find(cat => cat.id === selectedCategory)?.name || 'Products'
    : 'All Products';

  // Get subcategory name for display
  const subcategoryName = selectedSubcategory !== 'all'
    ? subcategories.find(subcat => subcat.id === selectedSubcategory)?.name || ''
    : '';

  // Combine for page title
  const pageTitle = subcategoryName 
    ? `${subcategoryName} - ${categoryName}`
    : categoryName;

  // Get the current category data for the banner
  const getCategoryData = () => {
    let bannerImage: string | undefined;
    let description: string | undefined;
    let isDefault = false;
    let categorySlug = 'all';
    
    // If a category is selected, use its image and description
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        categorySlug = category.slug;
        bannerImage = getCategoryImageUrl(category);
        description = category.description || categoryDescriptions[category.slug];
      }
    }
    
    // If a subcategory is selected, use its image and description instead
    if (selectedSubcategory !== 'all') {
      const subcategory = subcategories.find(subcat => subcat.id === selectedSubcategory);
      if (subcategory) {
        categorySlug = subcategory.slug;
        bannerImage = getCategoryImageUrl(subcategory);
        description = subcategory.description || categoryDescriptions[subcategory.slug];
      }
    }
    
    // If no image is available yet, use the default banner for 'all' products
    if (!bannerImage) {
      bannerImage = categoryBannerImages['all'];
      description = categoryDescriptions['all'];
      isDefault = true;
    }
    
    return { bannerImage, description, isDefault, categorySlug };
  };
  
  const { bannerImage, description, isDefault, categorySlug } = getCategoryData();
  
  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId: string) => {
    setSelectedSubcategory(subcategoryId);
    setTimeout(() => updateURL(), 0);
  };
  
  return (
    <div className="container-custom py-8">
      {/* Category Banner */}
      <CategoryBanner 
        title={pageTitle}
        image={bannerImage}
        description={description}
        isDefault={isDefault}
      />
      
      <div className="flex flex-col md:flex-row justify-between mb-8 gap-4">
        <div className="flex-1">
          <SearchInput
            initialValue={searchQuery}
            onSearch={(value) => {
              // Only update if the value has changed
              if (value !== searchQuery) {
                setSearchQuery(value);
                
                // Update URL params with the new search value
                const params: Record<string, string | null> = {};
                params.search = value || null;
                
                // Update URL without triggering a page transition
                if (value) {
                  searchParams.set('search', value);
                } else {
                  searchParams.delete('search');
                }
                
                // Update the URL without causing a page reload
                setSearchParams(searchParams, { replace: true });
              }
            }}
            placeholder="Search products..."
            className="max-w-sm"
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          {/* Category dropdown */}
          <Select
            value={selectedCategory}
            onValueChange={(value) => {
              setSelectedCategory(value);
              setSelectedSubcategory('all'); // Reset subcategory when category changes
              
              // Wait for state to update before updating URL
              setTimeout(() => updateURL(), 0);
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Subcategory Image Grid - only show if a category is selected and it has subcategories */}
      {selectedCategory !== 'all' && filteredSubcategories.length > 0 && (
        <SubcategoryImageGrid
          subcategories={filteredSubcategories}
          selectedSubcategoryId={selectedSubcategory}
          onSelectSubcategory={handleSubcategorySelect}
          categoryName={categoryName}
          getCategoryImageUrl={getCategoryImageUrl}
        />
      )}
      
      <ProductGridSimple
        categoryId={selectedCategory !== 'all' ? selectedCategory : undefined}
        subcategoryId={selectedSubcategory !== 'all' ? selectedSubcategory : undefined}
        searchQuery={searchQuery}
      />
    </div>
  );
};

export default ShopPage;
