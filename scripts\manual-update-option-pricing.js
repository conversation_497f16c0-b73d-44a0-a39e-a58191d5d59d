// Manual update script for product option pricing
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';

// Configure dotenv
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Log the Supabase connection details (without the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'undefined';
console.log(`Connecting to Supabase at: ${supabaseUrl} with key: ${maskedKey}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase URL or key is missing. Make sure your .env file is properly configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const DRY_RUN = process.argv.includes('--dry-run');

// Helper function to prompt for user input
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Main function
async function updateProductOptionPricing() {
  console.log('Starting manual product option pricing update...');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE (changes will be applied)'}`);

  try {
    // 1. Fetch products from the database with options
    console.log('Fetching products from database...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('id, name, price, option_name1, option_type1, option_values1, option_price_adjustment1')
      .not('option_name1', 'is', null)
      .not('option_values1', 'is', null);

    if (error) {
      console.error('Error fetching products:', error);
      return;
    }

    console.log(`Found ${dbProducts?.length || 0} products in database with options`);

    if (!dbProducts || dbProducts.length === 0) {
      console.log('No products found with options in the database');
      return;
    }

    // 2. Let the user search for a product
    let continueUpdating = true;
    const updatedProducts = [];

    while (continueUpdating) {
      const searchTerm = await prompt('\nEnter product name to search (or "exit" to quit): ');
      
      if (searchTerm.toLowerCase() === 'exit') {
        continueUpdating = false;
        continue;
      }

      // Search for products matching the search term
      const matchingProducts = dbProducts.filter(p => 
        p.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (matchingProducts.length === 0) {
        console.log('No matching products found. Try a different search term.');
        continue;
      }

      // Display matching products
      console.log(`\nFound ${matchingProducts.length} matching products:`);
      matchingProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name}`);
        console.log(`   Option name: ${product.option_name1}`);
        console.log(`   Option values: ${product.option_values1}`);
        console.log(`   Base price: ${product.price}`);
        console.log(`   Current price adjustments: ${product.option_price_adjustment1 || 'None'}`);
      });

      // Let the user select a product
      const selection = await prompt('\nSelect a product number to update (or 0 to search again): ');
      const selectionNum = parseInt(selection);

      if (selectionNum === 0 || isNaN(selectionNum) || selectionNum > matchingProducts.length) {
        console.log('Invalid selection. Returning to search.');
        continue;
      }

      const selectedProduct = matchingProducts[selectionNum - 1];
      console.log(`\nSelected product: ${selectedProduct.name}`);
      
      // Get option values
      const optionValues = selectedProduct.option_values1.split(';').map(v => v.trim());
      console.log(`Option values: ${optionValues.join(', ')}`);

      // Let the user enter price adjustments
      console.log('\nEnter price adjustments for each option value (e.g., +1.00, -2.50, 0):');
      
      const priceAdjustments = [];
      
      for (const optionValue of optionValues) {
        let currentAdjustment = '0';
        
        // If there are existing price adjustments, show them
        if (selectedProduct.option_price_adjustment1) {
          const existingAdjustments = selectedProduct.option_price_adjustment1.split(';');
          const index = optionValues.indexOf(optionValue);
          if (index >= 0 && index < existingAdjustments.length) {
            currentAdjustment = existingAdjustments[index];
          }
        }
        
        const adjustment = await prompt(`${optionValue} [current: ${currentAdjustment}]: `);
        
        // Validate the adjustment (should be a number, can be negative)
        const parsedAdjustment = parseFloat(adjustment);
        if (isNaN(parsedAdjustment)) {
          console.log(`Invalid adjustment value: ${adjustment}. Using 0 instead.`);
          priceAdjustments.push(0);
        } else {
          priceAdjustments.push(parsedAdjustment);
        }
      }

      // Format price adjustments as semicolon-separated string
      const priceAdjustmentStr = priceAdjustments.join(';');
      
      // Confirm the update
      console.log(`\nNew price adjustments: ${priceAdjustmentStr}`);
      const confirm = await prompt('Update this product? (y/n): ');
      
      if (confirm.toLowerCase() === 'y') {
        if (!DRY_RUN) {
          // Update the product in the database
          const { error: updateError } = await supabase
            .from('products')
            .update({ option_price_adjustment1: priceAdjustmentStr })
            .eq('id', selectedProduct.id);
            
          if (updateError) {
            console.error(`Error updating product ${selectedProduct.id}:`, updateError);
          } else {
            console.log(`Successfully updated product: ${selectedProduct.name}`);
            updatedProducts.push({
              id: selectedProduct.id,
              name: selectedProduct.name,
              priceAdjustments: priceAdjustmentStr
            });
          }
        } else {
          console.log('DRY RUN: Product would be updated with these values');
          updatedProducts.push({
            id: selectedProduct.id,
            name: selectedProduct.name,
            priceAdjustments: priceAdjustmentStr
          });
        }
      } else {
        console.log('Update cancelled.');
      }
      
      // Ask if the user wants to continue updating products
      const continuePricing = await prompt('\nContinue updating products? (y/n): ');
      if (continuePricing.toLowerCase() !== 'y') {
        continueUpdating = false;
      }
    }

    // 3. Summary of updates
    if (updatedProducts.length > 0) {
      console.log(`\n${updatedProducts.length} products were updated:`);
      updatedProducts.forEach(p => {
        console.log(`- ${p.name}: ${p.priceAdjustments}`);
      });
    } else {
      console.log('\nNo products were updated.');
    }

    console.log('\nProcess completed!');
  } catch (error) {
    console.error('Unexpected error:', error);
  } finally {
    rl.close();
  }
}

// Run the function
updateProductOptionPricing().catch(console.error);
