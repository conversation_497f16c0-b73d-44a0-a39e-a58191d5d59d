-- Test queries for seed filtering system
-- Run these to verify everything is working

-- 1. Check if tables were created
SELECT 'filter_categories' as table_name, count(*) as row_count FROM filter_categories
UNION ALL
SELECT 'filter_options', count(*) FROM filter_options
UNION ALL
SELECT 'product_filters', count(*) FROM product_filters
UNION ALL
SELECT 'seed_product_attributes', count(*) FROM seed_product_attributes;

-- 2. Test seed identification function
SELECT 
  'White Widow Feminised Seeds' as product_name,
  is_seed_product('White Widow Feminised Seeds') as is_seed
UNION ALL
SELECT 
  'Glass Bong 25cm',
  is_seed_product('Glass Bong 25cm')
UNION ALL
SELECT 
  'Auto Gorilla x5',
  is_seed_product('Auto Gorilla x5')
UNION ALL
SELECT 
  'Rolling Papers King Size',
  is_seed_product('Rolling Papers King Size');

-- 3. Get filter categories
SELECT * FROM get_filter_categories();

-- 4. Get filter options for seed_type
SELECT * FROM get_filter_options_for_category('seed_type');

-- 5. Count seed products in database
SELECT 
  'Total Products' as type,
  count(*) as count
FROM products
UNION ALL
SELECT 
  'Seed Products (by name)',
  count(*)
FROM products 
WHERE is_seed_product(name) = TRUE
UNION ALL
SELECT 
  'Active Seed Products',
  count(*)
FROM products 
WHERE is_seed_product(name) = TRUE AND is_active = TRUE;

-- 6. Test get_seed_products function
SELECT count(*) as active_seed_count
FROM get_seed_products(true);

-- 7. Sample seed products (first 10)
SELECT name, is_active, price
FROM get_seed_products(false)
LIMIT 10;
