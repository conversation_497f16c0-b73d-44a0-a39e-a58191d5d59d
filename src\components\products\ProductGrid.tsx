import React, { useState, useEffect } from 'react';
import ProductCard from './ProductCard';
import type { Product as BaseProduct } from '@/types/database';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ensureNumericPrice } from '@/lib/utils';

// Extend the base Product type to include brand information
interface Product extends BaseProduct {
  brands?: {
    id: string;
    name: string;
    slug: string;
  };
}

interface ProductGridProps {
  categoryId?: string;
  subcategoryId?: string;
  brandId?: string;
  limit?: number;
  featured?: boolean;
  newProducts?: boolean;
  bestSellers?: boolean;
  searchQuery?: string;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  categoryId,
  subcategoryId,
  brandId,
  limit = 12,
  featured = false,
  newProducts = false,
  bestSellers = false,
  searchQuery = '',
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');

  // Keep track of the latest request to prevent race conditions
  const fetchRequestRef = React.useRef(0);

  useEffect(() => {
    const fetchProducts = async () => {
      // Don't set loading to true immediately to prevent flickering on quick changes
      const currentRequestId = ++fetchRequestRef.current;

      // Only show loading state if the request takes longer than 300ms
      const loadingTimeout = setTimeout(() => {
        // Only show loading if this is still the latest request
        if (currentRequestId === fetchRequestRef.current) {
          setLoading(true);
        }
      }, 300);

      try {
        let query = supabase
          .from('products')
          .select('*, brands(id, name, slug)')
          .eq('is_active', true); // Only show active products

        // Apply filters based on category and subcategory IDs
        if (categoryId && categoryId !== 'all') {
          // Products have a category_id field that references the categories table
          query = query.eq('category_id', categoryId);
        }

        if (subcategoryId && subcategoryId !== 'all') {
          // Products have a separate subcategory_id field
          query = query.eq('subcategory_id', subcategoryId);
        }

        // Apply search query if provided
        if (searchQuery) {
          query = query.ilike('name', `%${searchQuery}%`);
        }

        if (featured) {
          query = query.eq('is_featured', true);
        }

        if (newProducts) {
          query = query.eq('is_new', true);
        }

        if (bestSellers) {
          query = query.eq('is_best_seller', true);
        }

        // Apply sorting
        switch (sortBy) {
          case 'price-low':
            query = query.order('price', { ascending: true });
            break;
          case 'price-high':
            query = query.order('price', { ascending: false });
            break;
          case 'name':
            query = query.order('name', { ascending: true });
            break;
          case 'newest':
          default:
            query = query.order('created_at', { ascending: false });
            break;
        }

        // Apply limit
        if (limit > 0) {
          query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching products:', error);
          // Only update state if this is still the latest request
          if (currentRequestId === fetchRequestRef.current) {
            setProducts([]);
            setLoading(false);
          }
          return;
        }

        if (!data || data.length === 0) {
          // Only update state if this is still the latest request
          if (currentRequestId === fetchRequestRef.current) {
            setProducts([]);
            setLoading(false);
          }
          return;
        }

        // Process the data to ensure we have all required fields
        const processedData = data.map((product: any) => {
          return {
            ...product,
            // Ensure we have a valid price - convert string to number if needed
            price: ensureNumericPrice(product.price),
            // Ensure we have a valid sale price if it exists
            sale_price: product.sale_price ? ensureNumericPrice(product.sale_price) : null,
            // Ensure we have a valid image
            image: product.image || '/images/placeholder.jpg',
          };
        });

        // Apply brand filtering client-side if needed
        let filteredData = processedData;
        if (brandId && brandId !== 'all') {
          filteredData = processedData.filter((product: Product) => {
            // Check if it matches by brand ID
            if (product.brand_id === brandId) return true;

            // Check if it matches by brand slug
            if (product.brands && product.brands.slug === brandId) return true;

            // Check if it matches by brand name (case insensitive)
            if (product.brands && product.brands.name.toLowerCase() === brandId.toLowerCase()) return true;

            return false;
          });
        }

        // Log the filtered data to debug price issues
        console.log('Products after processing:', filteredData);

        // Only update state if this is still the latest request
        if (currentRequestId === fetchRequestRef.current) {
          setProducts(filteredData as Product[]);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
        // Only update state if this is still the latest request
        if (currentRequestId === fetchRequestRef.current) {
          // Set empty products array on error to avoid showing stale data
          setProducts([]);
          setLoading(false);
        }
      } finally {
        // Clear the timeout to prevent setting loading state if request completes quickly
        clearTimeout(loadingTimeout);
      }
    };

    fetchProducts();
  }, [categoryId, subcategoryId, brandId, featured, newProducts, bestSellers, limit, sortBy, searchQuery]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-20 bg-sage-50 rounded-lg">
        <h3 className="text-xl font-medium text-gray-900">No products found</h3>
        <p className="mt-2 text-gray-500">
          {categoryId && subcategoryId ? (
            <>No products are currently available in this subcategory. Please check back later or browse other categories.</>
          ) : categoryId ? (
            <>No products are currently available in this category. Please check back later or browse other categories.</>
          ) : searchQuery ? (
            <>No products match your search for "{searchQuery}". Try different keywords or browse our categories.</>
          ) : (
            <>Try adjusting your filters or check back later as we add new products to our store.</>
          )}
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Sort options */}
      <div className="flex justify-end mb-6">
        <div className="w-48">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="name">Name</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Product grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {(bestSellers && limit > 0 ? products.slice(0, limit) : products).map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default ProductGrid;
