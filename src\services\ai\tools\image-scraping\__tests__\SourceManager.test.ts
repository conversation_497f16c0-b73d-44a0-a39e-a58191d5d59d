/**
 * SourceManager.test.ts
 * 
 * Tests for the SourceManager component
 */

import { SourceManager, SourceCategory } from '../SourceManager';
import { RetailerSource } from '../types/ImageScrapingTypes';

describe('SourceManager', () => {
  let sourceManager: SourceManager;
  
  // Custom test sources
  const testSources: RetailerSource[] = [
    {
      name: 'Test Seeds',
      base_url: 'https://test-seeds.com',
      search_path: '/search?q={query}',
      selectors: {
        product_images: ['.product-image img'],
        product_titles: ['.product-title'],
        product_links: ['.product-link']
      },
      rate_limit: {
        requests_per_minute: 10,
        delay_between_requests: 6000
      },
      categories: [SourceCategory.SEEDS],
      reliability_score: 8
    },
    {
      name: 'Test CBD',
      base_url: 'https://test-cbd.com',
      search_path: '/search?q={query}',
      selectors: {
        product_images: ['.product-image img'],
        product_titles: ['.product-title'],
        product_links: ['.product-link']
      },
      rate_limit: {
        requests_per_minute: 8,
        delay_between_requests: 7500
      },
      categories: [SourceCategory.CBD],
      reliability_score: 7
    },
    {
      name: 'Test Multi',
      base_url: 'https://test-multi.com',
      search_path: '/search?q={query}',
      selectors: {
        product_images: ['.product-image img'],
        product_titles: ['.product-title'],
        product_links: ['.product-link']
      },
      rate_limit: {
        requests_per_minute: 6,
        delay_between_requests: 10000
      },
      categories: [SourceCategory.SEEDS, SourceCategory.CBD],
      reliability_score: 9
    }
  ];

  beforeEach(() => {
    // Create source manager with test sources
    sourceManager = new SourceManager({
      defaultSources: testSources
    });
  });

  describe('getAllSources', () => {
    it('should return all sources', () => {
      const sources = sourceManager.getAllSources();
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(3);
      expect(sources[0].name).toBe('Test Seeds');
      expect(sources[1].name).toBe('Test CBD');
      expect(sources[2].name).toBe('Test Multi');
    });
  });

  describe('getSourcesByCategory', () => {
    it('should return sources for a specific category', () => {
      const seedsSources = sourceManager.getSourcesByCategory(SourceCategory.SEEDS);
      
      expect(seedsSources).toBeDefined();
      expect(seedsSources.length).toBe(2);
      expect(seedsSources[0].name).toBe('Test Seeds');
      expect(seedsSources[1].name).toBe('Test Multi');
    });

    it('should return empty array for non-existent category', () => {
      const sources = sourceManager.getSourcesByCategory('non-existent');
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(0);
    });
  });

  describe('getSourcesByReliability', () => {
    it('should return sources with minimum reliability score', () => {
      const reliableSources = sourceManager.getSourcesByReliability(8);
      
      expect(reliableSources).toBeDefined();
      expect(reliableSources.length).toBe(2);
      expect(reliableSources[0].name).toBe('Test Seeds');
      expect(reliableSources[1].name).toBe('Test Multi');
    });

    it('should return all sources when min reliability is low', () => {
      const sources = sourceManager.getSourcesByReliability(5);
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(3);
    });

    it('should return no sources when min reliability is too high', () => {
      const sources = sourceManager.getSourcesByReliability(10);
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(0);
    });
  });

  describe('getRelevantSources', () => {
    it('should return relevant sources for product with category', () => {
      const sources = sourceManager.getRelevantSources('Test CBD Oil', SourceCategory.CBD);
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(2);
      // Should be sorted by reliability score (highest first)
      expect(sources[0].name).toBe('Test Multi');
      expect(sources[1].name).toBe('Test CBD');
    });

    it('should return all sources for product without category', () => {
      const sources = sourceManager.getRelevantSources('Test Product');
      
      expect(sources).toBeDefined();
      expect(sources.length).toBe(3);
      // Should be sorted by reliability score (highest first)
      expect(sources[0].name).toBe('Test Multi');
      expect(sources[1].name).toBe('Test Seeds');
      expect(sources[2].name).toBe('Test CBD');
    });
  });

  describe('addSource', () => {
    it('should add a new source', () => {
      const newSource: RetailerSource = {
        name: 'New Source',
        base_url: 'https://new-source.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.product-image img'],
          product_titles: ['.product-title'],
          product_links: ['.product-link']
        },
        rate_limit: {
          requests_per_minute: 5,
          delay_between_requests: 12000
        },
        categories: [SourceCategory.VAPORIZERS],
        reliability_score: 6
      };
      
      sourceManager.addSource(newSource);
      
      const sources = sourceManager.getAllSources();
      expect(sources.length).toBe(4);
      expect(sources[3].name).toBe('New Source');
    });

    it('should update existing source', () => {
      const updatedSource: RetailerSource = {
        name: 'Test Seeds',
        base_url: 'https://updated-seeds.com',
        search_path: '/search?q={query}',
        selectors: {
          product_images: ['.updated-image img'],
          product_titles: ['.updated-title'],
          product_links: ['.updated-link']
        },
        rate_limit: {
          requests_per_minute: 5,
          delay_between_requests: 12000
        },
        categories: [SourceCategory.SEEDS],
        reliability_score: 10
      };
      
      sourceManager.addSource(updatedSource);
      
      const sources = sourceManager.getAllSources();
      expect(sources.length).toBe(3);
      
      const updatedSeedsSource = sources.find(s => s.name === 'Test Seeds');
      expect(updatedSeedsSource).toBeDefined();
      expect(updatedSeedsSource?.base_url).toBe('https://updated-seeds.com');
      expect(updatedSeedsSource?.reliability_score).toBe(10);
    });
  });

  describe('removeSource', () => {
    it('should remove a source', () => {
      const removed = sourceManager.removeSource('Test CBD');
      
      expect(removed).toBe(true);
      
      const sources = sourceManager.getAllSources();
      expect(sources.length).toBe(2);
      expect(sources.find(s => s.name === 'Test CBD')).toBeUndefined();
    });

    it('should return false when source not found', () => {
      const removed = sourceManager.removeSource('Non-existent Source');
      
      expect(removed).toBe(false);
      
      const sources = sourceManager.getAllSources();
      expect(sources.length).toBe(3);
    });
  });

  describe('updateSource', () => {
    it('should update a source', () => {
      const updated = sourceManager.updateSource('Test Seeds', {
        reliability_score: 10,
        base_url: 'https://updated-seeds.com'
      });
      
      expect(updated).toBe(true);
      
      const sources = sourceManager.getAllSources();
      const updatedSource = sources.find(s => s.name === 'Test Seeds');
      expect(updatedSource).toBeDefined();
      expect(updatedSource?.reliability_score).toBe(10);
      expect(updatedSource?.base_url).toBe('https://updated-seeds.com');
    });

    it('should return false when source not found', () => {
      const updated = sourceManager.updateSource('Non-existent Source', {
        reliability_score: 10
      });
      
      expect(updated).toBe(false);
    });
  });
});
