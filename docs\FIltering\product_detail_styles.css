/* Main Styles for Cannabis Seeds Product Detail Page */

/* Global Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --accent-color: #3498db;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
    --dark-gray: #777;
    --text-color: #333;
    --white: #fff;
    --black: #000;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --border-radius: 4px;
    --box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-gray);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--accent-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

button {
    cursor: pointer;
    border: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

img {
    max-width: 100%;
    height: auto;
}

/* Header Styles */
header {
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo img {
    height: 40px;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: var(--primary-color);
    font-weight: 500;
    padding: 5px 10px;
    border-radius: var(--border-radius);
}

nav ul li a.active,
nav ul li a:hover {
    background-color: var(--light-gray);
}

.header-actions {
    display: flex;
    align-items: center;
}

.search {
    display: flex;
    margin-right: 20px;
}

.search input {
    padding: 8px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    width: 200px;
}

.search button {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.cart a {
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.cart-count {
    background-color: var(--secondary-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 5px;
}

/* Breadcrumb Styles */
.breadcrumb {
    background-color: var(--white);
    padding: 10px 0;
    border-bottom: 1px solid var(--medium-gray);
    font-size: 14px;
}

/* Product Detail Container */
.product-detail-container {
    padding: 30px 0;
}

.product-detail-container .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* Product Gallery */
.product-gallery {
    position: relative;
}

.main-image {
    position: relative;
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: var(--box-shadow);
}

.main-image img {
    width: 100%;
    display: block;
}

.new-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--secondary-color);
    color: var(--white);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    line-height: 1.2;
}

.thumbnail-gallery {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: var(--secondary-color);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.brand-logo {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px;
    border-radius: var(--border-radius);
}

.brand-logo img {
    height: 40px;
}

/* Product Info */
.product-info {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.product-name {
    font-size: 28px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.product-subtitle {
    color: var(--dark-gray);
    margin-bottom: 15px;
}

.product-categories {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.category-link {
    color: var(--accent-color);
    font-size: 14px;
}

.category-dot {
    margin: 0 8px;
    color: var(--dark-gray);
}

.seed-type {
    font-weight: 500;
}

/* Product Characteristics */
.product-characteristics {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.characteristic {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.characteristic img {
    width: 32px;
    height: 32px;
    margin-bottom: 5px;
}

.characteristic span {
    font-size: 14px;
    color: var(--dark-gray);
}

/* Seed Packs */
.seed-packs {
    margin-top: 25px;
}

.seed-packs h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.pack-options {
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.pack-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    background-color: var(--light-gray);
    padding: 10px 15px;
    font-weight: 500;
    font-size: 14px;
}

.pack-option {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    padding: 15px;
    border-top: 1px solid var(--medium-gray);
    align-items: center;
}

.pack-name {
    font-weight: 500;
    display: flex;
    align-items: center;
}

.discount {
    background-color: var(--success-color);
    color: var(--white);
    padding: 2px 6px;
    border-radius: var(--border-radius);
    font-size: 12px;
    margin-left: 8px;
}

.price-per-seed,
.price-per-pack {
    color: var(--dark-gray);
}

.amount-controls {
    display: flex;
    align-items: center;
}

.decrease-btn,
.increase-btn {
    width: 30px;
    height: 30px;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.decrease-btn:hover,
.increase-btn:hover {
    background-color: var(--medium-gray);
}

.amount-input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: 1px solid var(--medium-gray);
    margin: 0 5px;
}

.total-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 15px;
    background-color: var(--light-gray);
    font-weight: 500;
}

.total-label {
    margin-right: 15px;
}

.add-to-cart {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.wishlist-btn {
    width: 50px;
    height: 50px;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--dark-gray);
}

.wishlist-btn:hover {
    background-color: var(--medium-gray);
}

.order-now-btn {
    flex: 1;
    background-color: var(--secondary-color);
    color: var(--white);
    font-weight: 500;
    font-size: 16px;
    padding: 0 20px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.order-now-btn:hover {
    background-color: #d35400;
}

.order-now-btn i {
    margin-right: 10px;
}

/* Product Details Tabs */
.product-details-tabs {
    background-color: var(--white);
    margin-top: 30px;
    box-shadow: var(--box-shadow);
}

.tabs-navigation {
    display: flex;
    border-bottom: 1px solid var(--medium-gray);
}

.tab-btn {
    padding: 15px 25px;
    background-color: transparent;
    font-weight: 500;
    color: var(--dark-gray);
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--secondary-color);
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-content {
    display: none;
    padding: 30px 0;
}

.tab-content.active {
    display: block;
}

/* Product Info Tab */
.product-info-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.product-description h2,
.cannabinoid-profile h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.description-text {
    line-height: 1.8;
}

.strain-genetics {
    color: var(--dark-gray);
    margin-bottom: 20px;
}

.thc-meter {
    margin-bottom: 20px;
}

.thc-meter-image {
    width: 100%;
    max-width: 300px;
    margin-bottom: 10px;
}

.thc-level {
    font-size: 18px;
}

.cannabinoid-percentages {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.cannabinoid-item {
    text-align: center;
    width: calc(33.333% - 10px);
}

.cannabinoid-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.cannabinoid-value {
    color: var(--dark-gray);
}

/* Terpene Profile Section */
.terpene-profile-section {
    margin-bottom: 40px;
}

.terpene-profile-section h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.terpene-profile-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.terpene-chart img {
    width: 100%;
    max-width: 400px;
}

.terpene-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.terpene-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.terpene-icon img {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
}

.terpene-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.terpene-percentage {
    color: var(--dark-gray);
}

.terpene-explanation {
    grid-column: 1 / -1;
    margin-top: 20px;
    font-size: 14px;
    color: var(--dark-gray);
    font-style: italic;
}

/* Certificate Section */
.certificate-section {
    background-color: var(--light-gray);
    padding: 30px;
    border-radius: var(--border-radius);
}

.certificate-section h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.certificate-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.certificate-logo {
    margin-right: 20px;
}

.certificate-logo img {
    height: 60px;
}

.certificate-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.certificate-strain {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 5px;
}

.certificate-genetics {
    color: var(--dark-gray);
}

.certificate-data {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.certificate-image img {
    width: 100%;
    border-radius: var(--border-radius);
}

.cannabinoids-section h3,
.characteristics-section h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.analysis-table th,
.analysis-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.analysis-table th {
    background-color: var(--light-gray);
}

.thc-highlight {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.thc-label {
    font-weight: 500;
}

.thc-value {
    font-size: 20px;
    font-weight: 500;
}

/* Specifications Tab */
.specifications-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.specification-item {
    background-color: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius);
}

.spec-label {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--dark-gray);
}

.spec-value {
    font-size: 16px;
}

/* Reviews Tab */
.reviews-container {
    max-width: 800px;
}

.review-summary {
    display: flex;
    margin-bottom: 30px;
}

.average-rating {
    flex: 1;
    text-align: center;
    padding-right: 30px;
    border-right: 1px solid var(--medium-gray);
}

.rating-number {
    font-size: 48px;
    font-weight: 500;
    line-height: 1;
    margin-bottom: 10px;
}

.rating-stars {
    color: var(--warning-color);
    font-size: 20px;
    margin-bottom: 10px;
}

.rating-count {
    color: var(--dark-gray);
    font-size: 14px;
}

.rating-breakdown {
    flex: 2;
    padding-left: 30px;
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rating-label {
    width: 60px;
}

.progress-bar {
    flex: 1;
    height: 10px;
    background-color: var(--light-gray);
    border-radius: 5px;
    margin: 0 15px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--warning-color);
}

.rating-percentage {
    width: 40px;
    text-align: right;
}

.write-review {
    margin-bottom: 30px;
    text-align: center;
}

.write-review-btn {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px 20px;
    font-weight: 500;
}

.write-review-btn:hover {
    background-color: #1a2530;
}

.review-item {
    border-bottom: 1px solid var(--medium-gray);
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.reviewer-name {
    font-weight: 500;
}

.review-date {
    color: var(--dark-gray);
    font-size: 14px;
}

.review-rating {
    color: var(--warning-color);
    margin-bottom: 10px;
}

.review-title {
    font-weight: 500;
    margin-bottom: 10px;
}

/* Related Products */
.related-products {
    padding: 40px 0;
    background-color: var(--light-gray);
}

.related-products h2 {
    margin-bottom: 30px;
    color: var(--primary-color);
    text-align: center;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.product-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card .product-image {
    height: 200px;
    overflow: hidden;
}

.product-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-card .product-info {
    padding: 15px;
    box-shadow: none;
    border-radius: 0;
    background-color: transparent;
}

.product-card .product-name {
    font-size: 16px;
    margin-bottom: 5px;
}

.product-card .product-type {
    color: var(--dark-gray);
    font-size: 14px;
    margin-bottom: 5px;
}

.product-card .product-price {
    font-weight: 500;
    color: var(--primary-color);
}

.product-card .product-icons {
    display: flex;
    gap: 10px;
    padding: 0 15px 15px;
}

.product-card .product-icons img {
    width: 20px;
    height: 20px;
}

.product-card .view-product-btn {
    display: block;
    text-align: center;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.product-card .view-product-btn:hover {
    background-color: #1a2530;
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 50px 0 20px;
}

.footer-columns {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    margin-bottom: 20px;
    font-size: 18px;
}

.footer-column p {
    color: #ccc;
    line-height: 1.6;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #ccc;
}

.footer-column ul li a:hover {
    color: var(--white);
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.social-icons a {
    color: var(--white);
    font-size: 20px;
}

.newsletter h4 {
    margin-bottom: 10px;
    font-size: 16px;
}

.newsletter form {
    display: flex;
}

.newsletter input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.newsletter button {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: 10px 15px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    font-weight: 500;
}

.newsletter button:hover {
    background-color: #d35400;
}

.footer-bottom {
    border-top: 1px solid #3a546d;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.copyright {
    color: #ccc;
}

.legal-disclaimer {
    color: #ccc;
    max-width: 600px;
}

.legal-disclaimer a {
    color: var(--white);
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .product-detail-container .container {
        grid-template-columns: 1fr;
    }
    
    .product-info-layout {
        grid-template-columns: 1fr;
    }
    
    .terpene-profile-content {
        grid-template-columns: 1fr;
    }
    
    .certificate-data {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .footer-columns {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    .header-actions {
        margin-top: 15px;
    }
    
    .search {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .search input {
        width: 100%;
    }
    
    .specifications-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .review-summary {
        flex-direction: column;
    }
    
    .average-rating {
        padding-right: 0;
        border-right: none;
        margin-bottom: 20px;
    }
    
    .rating-breakdown {
        padding-left: 0;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .copyright {
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    nav ul {
        flex-wrap: wrap;
    }
    
    nav ul li {
        margin: 5px;
    }
    
    .pack-header,
    .pack-option {
        grid-template-columns: 1fr 1fr;
        row-gap: 10px;
    }
    
    .specifications-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-columns {
        grid-template-columns: 1fr;
    }
}
