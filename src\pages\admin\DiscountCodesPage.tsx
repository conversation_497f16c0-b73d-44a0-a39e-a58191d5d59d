import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from '@/components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import { DiscountCode } from '@/types/discount';
import { getDiscountCodes, deleteDiscountCode } from '@/services/discountService';
import DiscountCodeForm from '@/components/admin/DiscountCodeForm';
import { formatDate } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

export default function DiscountCodesPage() {
  const { toast } = useToast();
  const [discountCodes, setDiscountCodes] = useState<DiscountCode[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDiscountCode, setSelectedDiscountCode] = useState<DiscountCode | null>(null);

  // Fetch discount codes
  const fetchDiscountCodes = async () => {
    setIsLoading(true);
    try {
      const codes = await getDiscountCodes();
      setDiscountCodes(codes);
    } catch (error) {
      console.error('Error fetching discount codes:', error);
      toast({
        title: 'Error',
        description: 'Failed to load discount codes',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDiscountCodes();
  }, []);

  // Handle form success
  const handleFormSuccess = () => {
    setIsFormOpen(false);
    setSelectedDiscountCode(null);
    fetchDiscountCodes();
    toast({
      title: 'Success',
      description: 'Discount code saved successfully',
    });
  };

  // Handle delete
  const handleDelete = async () => {
    if (!selectedDiscountCode) return;

    try {
      await deleteDiscountCode(selectedDiscountCode.id);
      setIsDeleteDialogOpen(false);
      setSelectedDiscountCode(null);
      fetchDiscountCodes();
      toast({
        title: 'Success',
        description: 'Discount code deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting discount code:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete discount code',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Discount Codes</h1>
        <Button onClick={() => {
          setSelectedDiscountCode(null);
          setIsFormOpen(true);
        }}>
          <Plus className="mr-2 h-4 w-4" /> Add Discount Code
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Discount Codes</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading discount codes...</p>
            </div>
          ) : discountCodes.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40">
              <p className="text-muted-foreground mb-4">No discount codes found</p>
              <Button onClick={() => {
                setSelectedDiscountCode(null);
                setIsFormOpen(true);
              }}>
                <Plus className="mr-2 h-4 w-4" /> Create Your First Discount Code
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Min. Order</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {discountCodes.map((code) => (
                  <TableRow key={code.id}>
                    <TableCell className="font-medium">{code.code}</TableCell>
                    <TableCell>
                      {code.discount_type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                    </TableCell>
                    <TableCell>
                      {code.discount_type === 'percentage' 
                        ? `${code.discount_value}%` 
                        : `£${code.discount_value.toFixed(2)}`}
                    </TableCell>
                    <TableCell>£{code.minimum_order_amount.toFixed(2)}</TableCell>
                    <TableCell>
                      {code.end_date ? formatDate(code.end_date) : 'No expiry'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={code.is_active ? "success" : "secondary"}>
                        {code.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {code.usage_count} / {code.usage_limit ?? '∞'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            setSelectedDiscountCode(code);
                            setIsFormOpen(true);
                          }}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="text-red-500"
                          onClick={() => {
                            setSelectedDiscountCode(code);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Discount Code Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {selectedDiscountCode ? 'Edit Discount Code' : 'Add Discount Code'}
            </DialogTitle>
            <DialogDescription>
              {selectedDiscountCode 
                ? 'Update the details of your discount code.' 
                : 'Create a new discount code for your customers.'}
            </DialogDescription>
          </DialogHeader>
          <DiscountCodeForm
            discountCode={selectedDiscountCode}
            onSuccess={handleFormSuccess}
            onCancel={() => setIsFormOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the discount code "{selectedDiscountCode?.code}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
