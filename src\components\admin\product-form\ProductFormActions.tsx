
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface ProductFormActionsProps {
  isSubmitting: boolean;
  isEditing: boolean;
  onCancel: () => void;
}

export function ProductFormActions({
  isSubmitting,
  isEditing,
  onCancel,
}: ProductFormActionsProps) {
  return (
    <div className="flex justify-end space-x-2">
      <Button type="button" variant="outline" onClick={onCancel}>
        Cancel
      </Button>
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Saving..." : isEditing ? "Update Product" : "Create Product"}
      </Button>
    </div>
  );
}
