# Stripe Integration Guide

This guide explains how to set up and use the Stripe integration for the Bits N Bongs e-commerce platform.

## Overview

The Stripe integration allows customers to pay for their orders using Stripe. The integration includes:

- Stripe checkout component in the payment step of the checkout process
- Payment processing service that handles Stripe transactions
- Webhook handler for Stripe payment notifications
- Database tables to store payment information (shared with PayPal integration)

## Setup Instructions

### 1. Apply Database Migration

If you haven't already applied the payment tables migration for PayPal, apply it now:

```bash
node scripts/apply-migration.js migrations/add_payment_tables.sql
```

This will create:
- `payment_sessions` table to store payment information
- Add `payment_provider` and `payment_session_id` fields to the `orders` table

### 2. Configure Stripe API Keys

Set your Stripe API keys in the environment variables:

1. Open your `.env` file
2. Add the following lines:

```
VITE_STRIPE_PUBLISHABLE_KEY=your_publishable_key
VITE_STRIPE_SECRET_KEY=your_secret_key
VITE_STRIPE_WEBHOOK_SECRET=your_webhook_secret
```

Replace the placeholder values with your actual Stripe API keys.

### 3. Install Required Dependencies

Install the Stripe JavaScript libraries:

```bash
npm install @stripe/stripe-js @stripe/react-stripe-js
```

## How It Works

### Checkout Flow

1. Customer selects Stripe as the payment method in the checkout process
2. Customer enters their payment details in the Stripe Elements form
3. A payment session is created in the database
4. The payment is processed through Stripe
5. After payment, customer is redirected to the confirmation page
6. The payment is verified and the order is created

### Webhook Handling

Stripe sends notifications to the webhook endpoint when payment status changes. The webhook handler:

1. Receives the notification from Stripe
2. Verifies the payment with Stripe
3. Updates the payment session status
4. Creates or updates the order

## Testing

To test the Stripe integration:

1. Add items to your cart
2. Proceed to checkout
3. Select Stripe as the payment method
4. Enter test card details (e.g., 4242 4242 4242 4242 for a successful payment)
5. Complete the payment
6. You should be redirected back to the confirmation page

### Test Cards

Use these test card numbers for testing different scenarios:

- **Successful payment**: 4242 4242 4242 4242
- **Requires authentication**: 4000 0025 0000 3155
- **Declined payment**: 4000 0000 0000 0002

## Future Enhancements

- Add support for saved payment methods
- Implement Stripe subscription billing
- Add Apple Pay and Google Pay support
- Implement 3D Secure authentication

## Troubleshooting

### Common Issues

- **Payment not being processed**: Check that your Stripe API keys are correctly configured
- **Webhook not receiving notifications**: Ensure your server is publicly accessible and the webhook secret is correct
- **Database errors**: Verify that the migration was applied correctly

### Logs

Check the server logs for any errors related to Stripe integration. Look for log messages with:

- "Stripe checkout error"
- "Stripe verification error"
- "Error processing Stripe webhook"