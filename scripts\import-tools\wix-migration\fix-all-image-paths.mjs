// Script to fix all product image paths in the database
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

// Log the connection details (without showing the full key for security)
const maskedKey = supabaseKey ? `${supabaseKey.substring(0, 5)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with key: ${maskedKey}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to local images
const localImagesPath = path.join(__dirname, 'public', 'images', 'products', 'wix-imports');

// Function to get all available image files
async function getAvailableImages() {
  try {
    const files = fs.readdirSync(localImagesPath);
    return files.filter(file => file.endsWith('.webp'));
  } catch (error) {
    console.error('Error reading image directory:', error);
    return [];
  }
}

// Function to update product images based on available files
async function fixAllImagePaths() {
  console.log('Starting comprehensive image path fix...');
  
  try {
    // Get all available image files
    const availableImages = await getAvailableImages();
    console.log(`Found ${availableImages.length} available image files`);
    
    // Create a map of base filenames (without extension) to full filenames
    const imageMap = {};
    availableImages.forEach(filename => {
      const baseFilename = filename.split('.')[0];
      imageMap[baseFilename] = filename;
    });
    
    // Fetch all products
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, image, additional_images');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products in the database`);
    
    // Get the list of files in the Supabase storage
    const { data: storageFiles, error: storageError } = await supabase
      .storage
      .from('product-images')
      .list();
    
    if (storageError) {
      console.error('Error listing storage files:', storageError);
    } else {
      console.log(`Found ${storageFiles.length} files in Supabase storage`);
    }
    
    // Create a map of files in Supabase storage
    const storageFileMap = {};
    if (storageFiles) {
      storageFiles.forEach(file => {
        storageFileMap[file.name] = true;
      });
    }
    
    let updated = 0;
    let skipped = 0;
    
    // Process each product
    for (const product of products) {
      let needsUpdate = false;
      const updates = {};
      
      // Function to get the correct image URL
      const getCorrectImageUrl = (imageUrl) => {
        if (!imageUrl) return null;
        
        // Check if the URL is already correct (points to Supabase storage and ends with .webp)
        if (imageUrl.includes('/storage/v1/object/public/product-images/') && imageUrl.endsWith('.webp')) {
          // Check if the file exists in storage
          const filename = imageUrl.split('/').pop();
          if (storageFileMap[filename]) {
            return imageUrl; // URL is correct and file exists
          }
        }
        
        // Extract the base filename from the URL
        let baseFilename = '';
        
        if (imageUrl.includes('/storage/v1/object/public/product-images/')) {
          // Extract filename from Supabase URL
          baseFilename = imageUrl.split('/').pop().split('.')[0];
        } else if (imageUrl.includes('~mv2')) {
          // Extract filename from Wix URL
          baseFilename = imageUrl.split('/').pop().split('.')[0];
        } else {
          // For other URLs, just use the last part
          baseFilename = imageUrl.split('/').pop().split('.')[0];
        }
        
        // Remove ~mv2 if present
        baseFilename = baseFilename.replace(/~mv2/g, '');
        
        // Check if we have this image in our local directory
        if (imageMap[baseFilename]) {
          // Construct the correct Supabase URL
          return `${supabaseUrl}/storage/v1/object/public/product-images/${imageMap[baseFilename]}`;
        }
        
        // If we don't have the exact match, try to find a similar filename
        const similarKeys = Object.keys(imageMap).filter(key => 
          key.includes(baseFilename) || baseFilename.includes(key)
        );
        
        if (similarKeys.length > 0) {
          // Use the first similar match
          return `${supabaseUrl}/storage/v1/object/public/product-images/${imageMap[similarKeys[0]]}`;
        }
        
        // If no match found, return the original URL
        return imageUrl;
      };
      
      // Check main image
      const correctedMainImage = getCorrectImageUrl(product.image);
      if (correctedMainImage && correctedMainImage !== product.image) {
        updates.image = correctedMainImage;
        needsUpdate = true;
      }
      
      // Check additional images
      if (product.additional_images && Array.isArray(product.additional_images) && product.additional_images.length > 0) {
        const updatedAdditionalImages = product.additional_images.map(getCorrectImageUrl).filter(Boolean);
        
        // Check if any additional images were updated
        const hasChanges = JSON.stringify(updatedAdditionalImages) !== JSON.stringify(product.additional_images);
        
        if (hasChanges) {
          updates.additional_images = updatedAdditionalImages;
          needsUpdate = true;
        }
      }
      
      // Update the product if needed
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('products')
          .update(updates)
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Error updating product ${product.name}:`, updateError);
          skipped++;
        } else {
          console.log(`Updated product: ${product.name}`);
          updated++;
        }
      } else {
        skipped++;
      }
      
      // Log progress every 10 products
      if ((updated + skipped) % 10 === 0) {
        console.log(`Progress: ${updated + skipped}/${products.length} (${updated} updated, ${skipped} skipped)`);
      }
    }
    
    console.log(`Finished! ${updated} products updated, ${skipped} skipped`);
  } catch (error) {
    console.error('Error fixing image paths:', error);
  }
}

// Run the script
fixAllImagePaths().catch(console.error);
