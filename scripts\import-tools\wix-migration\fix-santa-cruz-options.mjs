// fix-santa-cruz-options.mjs
// <PERSON><PERSON>t to fix product options for Santa Cruz Shredder products
// The CSV file has options in format: "Colour,DROP_DOWN,Black;Purple;Green;Blue;Grey;Gold;Rasta"
// But only "DROP_DOWN" was imported into option_type1, missing the actual values

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Using Supabase key:', supabaseKey ? 'Key found (not showing for security)' : 'No key found');

const supabase = createClient(supabaseUrl, supabaseKey);

// Define the product options we want to fix
const productOptionsToFix = [
  {
    namePattern: 'Santa Cruz Shredder -4pc Medium',
    options: [
      {
        name: 'Colour',
        type: 'DROP_DOWN',
        values: 'Black;Purple;Green;Blue;Grey;Gold;Rasta'
      }
    ]
  },
  {
    namePattern: 'Santa Cruz Shredder -4pc large',
    options: [
      {
        name: 'Colour',
        type: 'DROP_DOWN',
        values: 'Black;Gold;Green;Grey;Purple;Rasta'
      }
    ]
  }
];

// Function to fix product options
async function fixProductOptions() {
  console.log('Starting to fix product options...');
  
  // Get all products from the database
  const { data: products, error } = await supabase
    .from('products')
    .select('*');
  
  if (error) {
    console.error('Error fetching products:', error);
    return;
  }
  
  console.log(`Found ${products.length} products in the database.`);
  
  // Count products with option_type1='DROP_DOWN' but no option_description1
  const productsWithIssue = products.filter(p => 
    p.option_type1 === 'DROP_DOWN' && (!p.option_description1 || p.option_description1.trim() === '')
  );
  
  console.log(`Found ${productsWithIssue.length} products with 'DROP_DOWN' type but no option values.`);
  
  // Fix Santa Cruz Shredder products
  let updatedCount = 0;
  let skippedCount = 0;
  
  for (const productToFix of productOptionsToFix) {
    // Find products matching the name pattern
    const matchingProducts = products.filter(p => 
      p.name && p.name.includes(productToFix.namePattern)
    );
    
    console.log(`Found ${matchingProducts.length} products matching "${productToFix.namePattern}"`);
    
    for (const product of matchingProducts) {
      console.log(`Fixing options for product: ${product.name} (ID: ${product.id})`);
      
      // Create update object
      const updates = {};
      
      // Apply options
      productToFix.options.forEach((option, index) => {
        const optionNum = index + 1;
        if (optionNum <= 3) { // Database has fields for up to 3 options
          updates[`option_name${optionNum}`] = option.name;
          updates[`option_type${optionNum}`] = option.type;
          updates[`option_description${optionNum}`] = option.values;
        }
      });
      
      console.log('Updating with:', updates);
      
      // Update the product in the database
      const { error: updateError } = await supabase
        .from('products')
        .update(updates)
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.name}:`, updateError);
        skippedCount++;
      } else {
        console.log(`Successfully updated options for: ${product.name}`);
        updatedCount++;
      }
    }
  }
  
  // Now let's check for any other products with DROP_DOWN but no options
  console.log('\nChecking for other products with DROP_DOWN but no options...');
  
  const otherProductsWithIssue = products.filter(p => 
    p.option_type1 === 'DROP_DOWN' && 
    (!p.option_description1 || p.option_description1.trim() === '') &&
    !productOptionsToFix.some(fix => p.name && p.name.includes(fix.namePattern))
  );
  
  console.log(`Found ${otherProductsWithIssue.length} other products with DROP_DOWN but no options.`);
  
  if (otherProductsWithIssue.length > 0) {
    console.log('Sample products with issues:');
    otherProductsWithIssue.slice(0, 5).forEach(p => {
      console.log(`- ${p.name} (ID: ${p.id})`);
    });
  }
  
  console.log('\nUpdate complete!');
  console.log(`Updated ${updatedCount} products with options.`);
  console.log(`Skipped ${skippedCount} products that couldn't be updated.`);
  console.log(`There are still ${otherProductsWithIssue.length} products that might need options.`);
}

// Run the script
fixProductOptions().catch(error => {
  console.error('Error running script:', error);
});
