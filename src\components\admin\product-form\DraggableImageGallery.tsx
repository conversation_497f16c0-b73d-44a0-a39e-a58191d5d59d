import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Plus, X, GripVertical, Trash2, ArrowUp, ArrowDown, Wand2, Loader2 } from 'lucide-react';
import { ImageUploader } from './ImageUploader';
import { cn } from '@/lib/utils';

interface DraggableImageGalleryProps {
  images: string[];
  onChange: (images: string[]) => void;
  mainImage?: string;
  onMainImageChange?: (url: string) => void;
  onFindImagesWithAI?: () => void;
  isFindingImages?: boolean;
}

export function DraggableImageGallery({ 
  images, 
  onChange, 
  mainImage,
  onMainImageChange,
  onFindImagesWithAI,
  isFindingImages = false
}: DraggableImageGalleryProps) {
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const addImage = (url: string) => {
    const newImages = [...images, url];
    onChange(newImages);
    
    // If this is the first image and there's no main image set, make it the main image
    if (images.length === 0 && onMainImageChange && !mainImage) {
      onMainImageChange(url);
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    const removedUrl = newImages[index];
    newImages.splice(index, 1);
    onChange(newImages);
    
    // If we're removing the main image, set the first available image as main
    if (mainImage === removedUrl && onMainImageChange && newImages.length > 0) {
      onMainImageChange(newImages[0]);
    }
  };

  const handleAddImageUrl = () => {
    if (imageUrl.trim()) {
      addImage(imageUrl.trim());
      setImageUrl('');
      setShowUrlInput(false);
    }
  };

  const handleMoveUp = (index: number) => {
    if (index > 0) {
      const newImages = [...images];
      [newImages[index - 1], newImages[index]] = [newImages[index], newImages[index - 1]];
      onChange(newImages);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index < images.length - 1) {
      const newImages = [...images];
      [newImages[index], newImages[index + 1]] = [newImages[index + 1], newImages[index]];
      onChange(newImages);
    }
  };

  const handleSetAsMain = (url: string) => {
    if (onMainImageChange) {
      onMainImageChange(url);
    }
  };

  return (
    <div className="space-y-4">
      {/* AI Image Search Button */}
      {onFindImagesWithAI && (
        <div className="mb-4">
          <Button
            type="button"
            variant="outline"
            onClick={onFindImagesWithAI}
            disabled={isFindingImages}
          >
            {isFindingImages ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Finding Images with AI...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Find Images with AI
              </>
            )}
          </Button>
          <p className="text-xs text-gray-500 mt-1">
            AI will search for relevant product images based on the product name and category
          </p>
        </div>
      )}
      
      {/* Upload controls */}
      <div className="flex flex-wrap gap-2 mb-4">
        <ImageUploader 
          onImageUploaded={addImage} 
          buttonText="Upload New Image"
        />
        
        {showUrlInput ? (
          <div className="flex gap-2 items-center">
            <input
              type="text"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="Enter image URL"
              className="px-3 py-1 border rounded-md text-sm"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddImageUrl}
            >
              Add
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowUrlInput(false)}
            >
              Cancel
            </Button>
          </div>
        ) : (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowUrlInput(true)}
          >
            <Plus className="mr-1 h-4 w-4" />
            Add Image URL
          </Button>
        )}
      </div>

      {/* Image grid */}
      {(images.length > 0 || mainImage) && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
          {/* Main image */}
          {mainImage && (
            <div className="relative rounded-md overflow-hidden border-2 border-primary aspect-square group">
              <img 
                src={mainImage} 
                alt="Main product image" 
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Show a placeholder on error
                  e.currentTarget.src = "https://placehold.co/200x200/e5e7eb/a1a1aa?text=Main+Image";
                }}
              />
              <div className="absolute top-1 right-1 bg-primary text-white text-xs px-2 py-0.5 rounded-sm">
                Main
              </div>
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-end">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                  onClick={() => onMainImageChange && onMainImageChange('')}
                >
                  <Trash2 className="h-3 w-3 text-white" />
                </Button>
              </div>
            </div>
          )}
          
          {/* Additional images */}
          {images.map((img, index) => (
            <div 
              key={index} 
              className={cn(
                "relative rounded-md overflow-hidden border aspect-square group",
                mainImage === img ? "border-primary" : "border-gray-200"
              )}
            >
              <img 
                src={img} 
                alt={`Product image ${index + 1}`} 
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Show a placeholder on error
                  e.currentTarget.src = "https://placehold.co/200x200/e5e7eb/a1a1aa?text=Image+Error";
                }}
              />
              
              {/* Image controls */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1 opacity-0 group-hover:opacity-100 transition-opacity flex justify-between items-center">
                <div className="flex gap-1">
                  {/* Move up button */}
                  {index > 0 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 bg-white/20 hover:bg-white/40"
                      onClick={() => handleMoveUp(index)}
                    >
                      <ArrowUp className="h-3 w-3 text-white" />
                    </Button>
                  )}
                  
                  {/* Move down button */}
                  {index < images.length - 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 bg-white/20 hover:bg-white/40"
                      onClick={() => handleMoveDown(index)}
                    >
                      <ArrowDown className="h-3 w-3 text-white" />
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-1">
                  {/* Set as main image button */}
                  {mainImage !== img && onMainImageChange && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs bg-white/20 hover:bg-white/40 text-white"
                      onClick={() => handleSetAsMain(img)}
                    >
                      Set as Main
                    </Button>
                  )}
                  
                  {/* Delete button */}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 bg-white/20 hover:bg-red-500/80"
                    onClick={() => removeImage(index)}
                  >
                    <Trash2 className="h-3 w-3 text-white" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {!mainImage && images.length === 0 && (
        <div className="flex flex-col items-center justify-center w-full h-40 rounded-md border border-dashed border-gray-300 p-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">No images added yet</p>
            <p className="text-xs text-gray-400 mt-1">Upload images, add image URLs, or find images with AI</p>
          </div>
        </div>
      )}
      
      <p className="text-xs text-gray-500">
        Use the arrows to reorder images. The main image will be displayed as the product thumbnail.
      </p>
    </div>
  );
}
