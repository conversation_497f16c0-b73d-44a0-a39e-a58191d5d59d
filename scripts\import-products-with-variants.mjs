// Import Products with Variants
// This script imports products from a CSV file and creates variants for each product

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import Papa from 'papaparse';
import { createClient } from '@supabase/supabase-js';
import slugify from 'slugify';
import stringSimilarity from 'string-similarity';

dotenv.config();

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in environment variables');
  console.log('Available environment variables:', Object.keys(process.env).filter(key => key.includes('VITE')));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const CSV_FILE_PATH = process.argv[2] || path.join(__dirname, '../docs/catalog_products.csv');
const IMAGE_TRANSFORM_STRATEGY = process.env.IMAGE_TRANSFORM_STRATEGY || 'transform'; // 'transform', 'external', 'skip'
const IMAGE_BASE_PATH = process.env.IMAGE_BASE_PATH || '';

// Main function
async function importProductsWithVariants() {
  try {
    console.log('Starting product import with variants...');
    
    // Read and parse CSV file
    const csvFile = fs.readFileSync(CSV_FILE_PATH, 'utf8');
    const { data } = Papa.parse(csvFile, {
      header: true,
      skipEmptyLines: true,
    });
    
    console.log(`Found ${data.length} rows in CSV file`);
    
    // Group products by name to identify variants
    const productGroups = groupProductsByName(data);
    console.log(`Found ${Object.keys(productGroups).length} unique products with variants`);
    
    // Get categories and brands for mapping
    const { data: categories } = await supabase.from('categories').select('id, name');
    const { data: brands } = await supabase.from('brands').select('id, name');
    
    // Process each product group
    let importedCount = 0;
    let failedCount = 0;
    
    for (const [productName, variants] of Object.entries(productGroups)) {
      try {
        console.log(`Processing product: ${productName} with ${variants.length} variants`);
        
        // Use the first variant as the base product
        const baseProduct = variants[0];
        
        // Get category ID based on collection
        let categoryId = null;
        if (baseProduct.collection) {
          const collections = baseProduct.collection.split(';');
          if (collections.length > 0) {
            const categoryName = collections[0].trim();
            const matchingCategory = findBestMatch(categoryName, categories, 'name');
            if (matchingCategory) {
              categoryId = matchingCategory.id;
            }
          }
        }
        
        // Get brand ID if available
        let brandId = null;
        if (baseProduct.brand) {
          const brandName = baseProduct.brand.trim();
          const matchingBrand = findBestMatch(brandName, brands, 'name');
          if (matchingBrand) {
            brandId = matchingBrand.id;
          }
        }
        
        // Map base product to DB format
        const dbProduct = mapProductToDBFormat(baseProduct, categoryId, brandId);
        
        // Process main product image
        if (baseProduct.productImageUrl) {
          const images = baseProduct.productImageUrl.split(';');
          if (images.length > 0) {
            const firstImage = images[0].trim();
            dbProduct.image = processImageFilename(firstImage, baseProduct.name);
            
            // Process additional images
            if (images.length > 1) {
              dbProduct.additional_images = images
                .slice(1)
                .map(img => processImageFilename(img.trim(), baseProduct.name))
                .filter(Boolean);
            }
          }
        }
        
        // Insert the base product
        const { data: insertedProduct, error: insertError } = await supabase
          .from('products')
          .insert(dbProduct)
          .select()
          .single();
        
        if (insertError) {
          throw new Error(`Error inserting product: ${insertError.message}`);
        }
        
        console.log(`Inserted base product: ${insertedProduct.name} (${insertedProduct.id})`);
        
        // Process variants
        const productVariants = extractVariants(variants, insertedProduct.id);
        
        if (productVariants.length > 0) {
          const { error: variantError } = await supabase
            .from('product_variants')
            .insert(productVariants);
          
          if (variantError) {
            throw new Error(`Error inserting variants: ${variantError.message}`);
          }
          
          console.log(`Inserted ${productVariants.length} variants for product: ${insertedProduct.name}`);
        }
        
        importedCount++;
      } catch (error) {
        console.error(`Error processing product ${productName}:`, error);
        failedCount++;
      }
    }
    
    console.log(`Import complete: ${importedCount} products imported, ${failedCount} failed`);
  } catch (error) {
    console.error('Import failed:', error);
  }
}

// Group products by name to identify variants
function groupProductsByName(products) {
  const groups = {};
  
  products.forEach(product => {
    // Use name as the key, but remove any variant-specific information
    // This is a simplified approach - you may need to adjust based on your data
    const baseName = product.name.split(' - ')[0].trim();
    
    if (!groups[baseName]) {
      groups[baseName] = [];
    }
    
    groups[baseName].push(product);
  });
  
  return groups;
}

// Map CSV product to DB format
function mapProductToDBFormat(product, categoryId, brandId) {
  // Extract images from semicolon-separated list (Wix format)
  const images = product.productImageUrl ? product.productImageUrl.split(';') : [];
  const firstImage = images.length > 0 ? images[0].trim() : '';
  
  // Extract additional images (all images except the first one)
  const additionalImages = images.length > 1 ? images.slice(1).map(img => img.trim()) : [];
  
  // Determine if product is in stock
  const inStock = product.inventory === 'InStock';
  
  // Calculate sale price if discount is available
  let salePrice = null;
  if (product.discountMode === 'PERCENT' && product.discountValue) {
    const discountValue = parseFloat(product.discountValue);
    const originalPrice = parseFloat(product.price);
    if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
      salePrice = originalPrice * (1 - discountValue / 100);
    }
  }
  
  return {
    name: product.name,
    slug: slugify(product.name, { lower: true, strict: true }),
    description: product.description || '',
    price: parseFloat(product.price) || 0,
    sale_price: salePrice,
    image: firstImage, // This will be processed later
    additional_images: additionalImages,
    category_id: categoryId,
    brand_id: brandId,
    in_stock: inStock,
    is_featured: false, // Default values, can be updated later
    is_new: false,
    is_best_seller: false,
    
    // Map product options
    option_name1: product.productOptionName1 || null,
    option_type1: product.productOptionType1 || null,
    option_description1: product.productOptionDescription1 || null,
    option_name2: product.productOptionName2 || null,
    option_type2: product.productOptionType2 || null,
    option_description2: product.productOptionDescription2 || null,
    option_name3: product.productOptionName3 || null,
    option_type3: product.productOptionType3 || null,
    option_description3: product.productOptionDescription3 || null,
    
    // Map additional information
    additional_info_title1: product.additionalInfoTitle1 || null,
    additional_info_description1: product.additionalInfoDescription1 || null,
    additional_info_title2: product.additionalInfoTitle2 || null,
    additional_info_description2: product.additionalInfoDescription2 || null,
    additional_info_title3: product.additionalInfoTitle3 || null,
    additional_info_description3: product.additionalInfoDescription3 || null,
  };
}

// Extract variants from a group of products
function extractVariants(products, productId) {
  const variants = [];
  
  products.forEach(product => {
    // Skip products without options
    if (!product.productOptionName1 && !product.productOptionType1) {
      return;
    }
    
    // Build option combination
    const optionCombination = {};
    
    // Add option values
    if (product.productOptionName1 && product.productOptionType1) {
      const values = product.productOptionType1.split(';').map(v => v.trim());
      if (values.length > 0) {
        optionCombination.option1 = values[0]; // Use first value for now
      }
    }
    
    if (product.productOptionName2 && product.productOptionType2) {
      const values = product.productOptionType2.split(';').map(v => v.trim());
      if (values.length > 0) {
        optionCombination.option2 = values[0]; // Use first value for now
      }
    }
    
    if (product.productOptionName3 && product.productOptionType3) {
      const values = product.productOptionType3.split(';').map(v => v.trim());
      if (values.length > 0) {
        optionCombination.option3 = values[0]; // Use first value for now
      }
    }
    
    // Skip if no options found
    if (Object.keys(optionCombination).length === 0) {
      return;
    }
    
    // Create variant
    const variant = {
      product_id: productId,
      variant_name: generateVariantName(optionCombination),
      sku: product.sku || null,
      price: parseFloat(product.price) || 0,
      sale_price: calculateSalePrice(product),
      stock_quantity: product.inventory === 'InStock' ? 10 : 0, // Default value
      in_stock: product.inventory === 'InStock',
      option_combination: optionCombination,
      external_id: product.handleId || null,
      is_active: product.visible === 'TRUE'
    };
    
    // Add image if specific to this variant
    if (product.productImageUrl) {
      const images = product.productImageUrl.split(';');
      if (images.length > 0) {
        const firstImage = images[0].trim();
        variant.image = processImageFilename(firstImage, product.name);
      }
    }
    
    variants.push(variant);
  });
  
  return variants;
}

// Generate a variant name from option combination
function generateVariantName(optionCombination) {
  return Object.values(optionCombination).join(' / ');
}

// Calculate sale price for a product
function calculateSalePrice(product) {
  if (product.discountMode === 'PERCENT' && product.discountValue) {
    const discountValue = parseFloat(product.discountValue);
    const originalPrice = parseFloat(product.price);
    if (!isNaN(discountValue) && !isNaN(originalPrice) && discountValue > 0) {
      return originalPrice * (1 - discountValue / 100);
    }
  }
  return null;
}

// Process image filename based on strategy
function processImageFilename(imageUrl, productName) {
  if (!imageUrl) return '';
  
  // If skipping images, return placeholder
  if (IMAGE_TRANSFORM_STRATEGY === 'skip') {
    return '/placeholder-product.jpg';
  }
  
  // If using external URLs, return as is
  if (IMAGE_TRANSFORM_STRATEGY === 'external') {
    return imageUrl;
  }
  
  // Otherwise transform the filename
  try {
    // Extract filename from URL
    const urlParts = imageUrl.split('/');
    let filename = urlParts[urlParts.length - 1];
    
    // Remove query parameters if any
    filename = filename.split('?')[0];
    
    // Clean up the filename
    filename = filename.replace(/[^a-zA-Z0-9.-]/g, '-').toLowerCase();
    
    // Add product name prefix for better organization
    const productPrefix = slugify(productName, { lower: true, strict: true }).substring(0, 30);
    const finalFilename = `${productPrefix}-${filename}`;
    
    // Return path with base path if provided
    return IMAGE_BASE_PATH ? `${IMAGE_BASE_PATH}/${finalFilename}` : finalFilename;
  } catch (error) {
    console.error(`Error processing image filename: ${imageUrl}`, error);
    return '';
  }
}

// Find best match using string similarity
function findBestMatch(name, items, field, threshold = 0.8) {
  if (!name || !items || items.length === 0) return null;
  
  // Exact match first
  const exactMatch = items.find(item => item[field].toLowerCase() === name.toLowerCase());
  if (exactMatch) return exactMatch;
  
  // Try fuzzy matching
  const itemNames = items.map(item => item[field].toLowerCase());
  const { bestMatch, bestMatchIndex } = stringSimilarity.findBestMatch(name.toLowerCase(), itemNames);
  
  if (bestMatch.rating >= threshold) {
    return items[bestMatchIndex];
  }
  
  return null;
}

// Run the import
importProductsWithVariants();
