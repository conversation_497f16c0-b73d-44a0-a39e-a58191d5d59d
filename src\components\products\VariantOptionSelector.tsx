import React from 'react';
import { Check, ChevronDown, Package, Palette, Ruler } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface VariantOptionSelectorProps {
  optionName: string;
  values: string[];
  selectedValue: string;
  onChange: (optionName: string, value: string) => void;
  disabled?: boolean;
}

export const VariantOptionSelector: React.FC<VariantOptionSelectorProps> = ({
  optionName,
  values,
  selectedValue,
  onChange,
  disabled = false,
}) => {
  // Get appropriate icon based on option name
  const getOptionIcon = () => {
    const normalizedName = optionName.toLowerCase();

    if (normalizedName.includes('size') || normalizedName.includes('dimension')) {
      return <Ruler className="h-4 w-4 mr-2" />;
    }

    if (normalizedName.includes('color') || normalizedName.includes('colour')) {
      return <Palette className="h-4 w-4 mr-2" />;
    }

    if (normalizedName.includes('pack') || normalizedName.includes('quantity')) {
      return <Package className="h-4 w-4 mr-2" />;
    }

    // Default icon
    return null;
  };

  // Format option name for display
  const displayName = optionName.charAt(0).toUpperCase() + optionName.slice(1);

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {displayName}
      </label>
      <Select
        value={selectedValue}
        onValueChange={(value) => {
          console.log(`Pack size selected: ${value}`);
          onChange(optionName, value);
        }}
        disabled={disabled}
        className="relative z-100"
      >
        <SelectTrigger className="w-full bg-white/80 border border-gray-200 hover:border-primary/50 transition-colors relative z-100">
          <div className="flex items-center">
            {getOptionIcon()}
            <SelectValue placeholder={`Select ${displayName}`} />
          </div>
        </SelectTrigger>
        <SelectContent className="relative z-100">
          {values.filter(value => value !== 'DROP_DOWN').map((value) => (
            <SelectItem
              key={value}
              value={value}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <span>{value}</span>
                {value === selectedValue && (
                  <Check className="h-4 w-4 text-primary ml-2" />
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
