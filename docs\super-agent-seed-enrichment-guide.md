# Super Agent Seed Data Enrichment Guide

This guide explains how to use the Super Agent to enrich seed product data for the Bits N Bongs cannabis seed filtering system.

## Overview

We have ~900 seed products:
- **~200 active** (with some data but may need enrichment)
- **~700 inactive** (mostly just names, missing descriptions and attributes)

The goal is to enrich these products with proper descriptions and filter attributes to enable comprehensive filtering.

## Export Process

### 1. Run the Export Script
```bash
cd /path/to/buds-n-bot-boutique-main
npm run export-seeds-for-agent
```

This creates: `exports/seeds-for-agent.csv`

### 2. CSV Structure

The exported CSV contains these columns:

#### Product Information
- `product_id` - Unique identifier (UUID)
- `product_name` - Current product name
- `current_description` - Existing description (may be empty)
- `is_active` - Whether product is currently active
- `price` - Current price
- `image_url` - Product image URL

#### Current Attributes (may be empty)
- `current_price_gbp` - Current price in GBP
- `current_image_url` - Current product image URL
- `has_image` - TRUE/FALSE if product has an image
- `current_seed_type` - Autoflower/Feminized/Regular
- `current_flowering_time` - e.g., "8-9 weeks"
- `current_yield` - e.g., "High", "XL", "XXL"
- `current_thc_level` - e.g., "High", "Very High"
- `current_cbd_level` - e.g., "Low", "5%", "10%"
- `current_effect` - Indica/Sativa/Hybrid
- `current_seed_family` - e.g., "Kush", "Haze", "Skunk"

#### Data Needs Flags
- `needs_description` - TRUE if description is missing/poor
- `needs_image` - TRUE if image is missing/poor quality
- `needs_price_research` - TRUE if price needs market research
- `needs_seed_type` - TRUE if seed type needs identification
- `needs_flowering_time` - TRUE if flowering time missing
- `needs_yield` - TRUE if yield info missing
- `needs_thc_level` - TRUE if THC level missing
- `needs_cbd_level` - TRUE if CBD level missing
- `needs_effect` - TRUE if effect type missing
- `needs_seed_family` - TRUE if seed family missing
- `needs_any_attributes` - TRUE if any attributes missing

#### Processing Guidance
- `priority_level` - HIGH/MEDIUM/LOW priority for processing
- `agent_notes` - Summary of what needs to be done

## Super Agent Tasks

### Primary Objectives

1. **Enrich Product Descriptions**
   - For products with `needs_description = TRUE`
   - Create detailed, informative descriptions (100-300 words)
   - Include growing information, effects, characteristics
   - Use professional cannabis industry language

2. **Research Market Pricing**
   - For products with `needs_price_research = TRUE`
   - Research current UK market prices from multiple sources
   - Provide price range and recommended price
   - Include source information for verification

3. **Source Product Images**
   - For products with `needs_image = TRUE`
   - Find high-quality product images (minimum 800x600px)
   - Prefer official breeder/seedbank images
   - Provide download URLs for image acquisition

4. **Extract/Research Seed Attributes**
   - For products with `needs_any_attributes = TRUE`
   - Research the strain/product online
   - Extract the following attributes:

#### Seed Type (REQUIRED)
- **Autoflower** - Auto-flowering strains
- **Feminized** - Feminized photoperiod strains
- **Regular** - Regular photoperiod strains

#### Flowering Time
- Format: "X weeks" or "X-Y weeks"
- Examples: "8 weeks", "8-10 weeks", "10-12 weeks"

#### Yield
- **XXL** - Extremely high yield
- **XL** - Very high yield
- **L** - High yield
- **M** - Medium yield
- **M/L** - Medium to high yield

#### THC Level
- **Extremely High** - 25%+ THC
- **Very High** - 20-25% THC
- **High** - 15-20% THC
- **Medium** - 10-15% THC
- **Low** - Under 10% THC

#### CBD Level
- Format: "X%" or "Low"/"Medium"/"High"
- Examples: "Low", "5%", "10%", "15%"

#### Effect
- **Indica** - Body-focused, relaxing effects
- **Sativa** - Head-focused, energizing effects
- **Hybrid** - Balanced effects

#### Seed Family
- Examples: "Kush", "Haze", "Skunk", "OG", "Diesel", "Cookies", etc.
- Use the most recognized family/lineage

### Research Sources

#### Strain Information
- Seedbank websites (Dutch Passion, Sensi Seeds, etc.)
- Leafly strain database
- Seedfinder.eu
- Cannabis strain databases
- Breeder websites

#### Pricing Research
- UK seed banks (Attitude Seedbank, Herbies Seeds, etc.)
- European seed banks with UK shipping
- Price comparison sites
- Official breeder pricing

#### Image Sources
- Official breeder websites
- Authorized seed bank product pages
- High-quality strain databases
- Cannabis photography sites (with proper licensing)

### Priority Processing

1. **HIGH Priority** - Active products missing data
2. **MEDIUM Priority** - Active products needing enrichment
3. **LOW Priority** - Inactive products

## Return Format

### Expected CSV Structure

Return the same CSV with these additional columns filled:

#### Enriched Data Columns
- `enriched_description` - New/improved description
- `enriched_price_gbp` - Recommended price in GBP
- `market_price_low` - Lowest market price found
- `market_price_high` - Highest market price found
- `market_price_average` - Average market price
- `price_sources` - Sources used for pricing research
- `enriched_image_url` - Direct URL to use for product image
- `image_download_url` - URL for downloading high-res image
- `enriched_seed_type` - Identified seed type
- `enriched_flowering_time` - Flowering time information
- `enriched_yield` - Yield classification
- `enriched_thc_level` - THC level classification
- `enriched_cbd_level` - CBD level information
- `enriched_effect` - Effect classification
- `enriched_seed_family` - Seed family/lineage
- `enriched_lifecycle` - Additional lifecycle info (optional)
- `enriched_other` - Other notable attributes (optional)

#### Agent Metadata
- `agent_confidence` - HIGH/MEDIUM/LOW confidence in data
- `agent_notes` - Notes about sources, assumptions, etc.

### Example Enriched Record

```csv
product_id,product_name,enriched_description,enriched_price_gbp,market_price_low,market_price_high,market_price_average,price_sources,enriched_image_url,enriched_seed_type,enriched_flowering_time,enriched_yield,enriched_thc_level,enriched_cbd_level,enriched_effect,enriched_seed_family,agent_confidence,agent_notes
"abc-123","420 Fastbuds Auto Grapefruit","This autoflowering strain combines the citrusy flavors of Grapefruit with the convenience of auto genetics. Flowering in just 8-9 weeks from seed, it produces dense, resinous buds with a sweet and tangy aroma. Perfect for beginners, this strain offers a balanced high with uplifting sativa effects followed by gentle relaxation. Yields are consistently high with proper care.","45.00","39.99","49.99","44.50","Attitude Seedbank, Herbies Seeds, 420 Fastbuds official","https://420fastbuds.com/images/grapefruit-auto.jpg","Autoflower","8-9 weeks","XL","High","Low","Hybrid","Citrus","HIGH","Data sourced from 420 Fastbuds official strain info and UK seed bank pricing"
```

## Import Process

Once the Super Agent returns the enriched CSV:

```bash
npm run import-enriched-seeds path/to/enriched-seeds.csv
```

This will:
1. Update product descriptions
2. Update product prices based on market research
3. Update product images with new URLs
4. Create/update seed_product_attributes records
5. Mark attributes as manually verified
6. Provide detailed import summary

## Quality Guidelines

### Description Quality
- 100-300 words per description
- Include: effects, growing characteristics, flavor profile
- Use professional, informative tone
- Avoid medical claims
- Include flowering time and yield info in description

### Pricing Guidelines
- Research minimum 3 UK sources for each product
- Provide competitive but profitable pricing
- Consider pack sizes (single seeds vs. multi-packs)
- Flag unusual pricing for manual review
- Include source URLs in price_sources field

### Image Quality
- Minimum 800x600px resolution
- High-quality, professional product photos
- Prefer official breeder/strain images
- Avoid watermarked images
- Provide both display URL and download URL

### Attribute Accuracy
- Research thoroughly before assigning attributes
- Use consistent terminology
- When uncertain, mark confidence as LOW
- Provide source information in agent_notes

### Confidence Levels
- **HIGH** - Verified from official breeder/seedbank data
- **MEDIUM** - Consistent across multiple reliable sources
- **LOW** - Limited information, best guess based on available data

## Success Metrics

Target outcomes:
- 90%+ of active products have complete descriptions
- 85%+ of all products have basic attributes (seed_type, effect)
- 70%+ of products have detailed attributes (flowering_time, yield, THC)
- Enable comprehensive filtering for customers

## Notes for Super Agent

- Focus on HIGH priority items first
- Batch process similar strains for efficiency
- Maintain consistent terminology across products
- Flag any products that seem to be non-cannabis items
- Note any products that appear to be duplicates

This enrichment will enable a professional-grade filtering system comparable to major seed banks like Dutch Passion.
