// <PERSON>ript to upload images to Supabase storage using admin token
import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client with admin token
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseAccessToken = process.env.VITE_SUPABASE_ACCESS_TOKEN || '';

if (!supabaseAccessToken) {
  console.error('Error: VITE_SUPABASE_ACCESS_TOKEN is not set in .env file');
  process.exit(1);
}

// Log the connection details (without showing the full key for security)
const maskedToken = supabaseAccessToken ? 
  `${supabaseAccessToken.substring(0, 5)}...${supabaseAccessToken.substring(supabaseAccessToken.length - 5)}` : 
  'missing';
console.log(`Connecting to Supabase at ${supabaseUrl} with admin token: ${maskedToken}`);

// Create Supabase client with admin token
const supabase = createClient(supabaseUrl, supabaseAccessToken);

// Path to the image list file
const imageListPath = path.join(__dirname, 'image-upload-list.txt');

// Function to parse the image list file
async function parseImageList() {
  const fileContent = fs.readFileSync(imageListPath, 'utf8');
  const lines = fileContent.split('\n');
  
  const images = [];
  let startParsing = false;
  
  for (const line of lines) {
    if (line.startsWith('| Local Path')) {
      startParsing = true;
      continue;
    }
    
    if (startParsing && line.startsWith('|')) {
      const parts = line.split('|').map(part => part.trim());
      if (parts.length >= 5) {
        images.push({
          localPath: parts[1],
          uploadPath: parts[2],
          productName: parts[3],
          productId: parts[4]
        });
      }
    }
  }
  
  return images;
}

// Function to sanitize filenames by removing ~mv2
function sanitizeFilename(filename) {
  return filename.replace(/~mv2/g, '');
}

// Function to upload images in batches
async function uploadImages(batchSize = 10) {
  console.log('Starting image upload...');
  
  // Parse the image list
  const images = await parseImageList();
  console.log(`Found ${images.length} images to upload`);
  
  // Create a readline interface for user input
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  // Ask for confirmation
  rl.question(`Do you want to upload ${images.length} images to Supabase storage? (y/n) `, async (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Upload cancelled');
      rl.close();
      return;
    }
    
    // Upload images in batches
    let uploaded = 0;
    let failed = 0;
    
    for (let i = 0; i < images.length; i += batchSize) {
      const batch = images.slice(i, i + batchSize);
      
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(images.length / batchSize)}`);
      
      // Process batch in parallel
      const results = await Promise.allSettled(
        batch.map(async (img) => {
          try {
            // Check if file exists
            if (!fs.existsSync(img.localPath)) {
              console.error(`File not found: ${img.localPath}`);
              return { success: false, error: 'File not found' };
            }
            
            // Read the file
            const fileBuffer = fs.readFileSync(img.localPath);
            
            // Sanitize the upload path by removing ~mv2
            const sanitizedUploadPath = sanitizeFilename(img.uploadPath);
            
            // Upload to Supabase storage
            const { data, error } = await supabase.storage
              .from('product-images')
              .upload(sanitizedUploadPath, fileBuffer, {
                contentType: 'image/webp',
                cacheControl: '3600',
                upsert: true
              });
            
            if (error) {
              console.error(`Error uploading ${img.uploadPath}:`, error);
              return { success: false, error };
            }
            
            console.log(`Uploaded: ${sanitizedUploadPath} for product: ${img.productName}`);
            return { success: true, data };
          } catch (error) {
            console.error(`Error processing ${img.localPath}:`, error);
            return { success: false, error };
          }
        })
      );
      
      // Count successes and failures
      results.forEach(result => {
        if (result.value && result.value.success) {
          uploaded++;
        } else {
          failed++;
        }
      });
      
      console.log(`Batch progress: ${uploaded + failed}/${images.length} (${uploaded} uploaded, ${failed} failed)`);
      
      // Wait a bit between batches to avoid rate limiting
      if (i + batchSize < images.length) {
        console.log('Waiting 2 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    console.log(`Finished! ${uploaded} images uploaded, ${failed} failed`);
    rl.close();
  });
}

// Run the script
uploadImages().catch(console.error);
