# Bits N Bongs E-commerce Platform

## Project Overview

Bits N Bongs is a modern e-commerce platform specializing in specialty products. The application is built using React and Supabase for backend functionality.

## Features

- Product management with variants and options
- AI-powered content generation for product descriptions and blog posts
- Advanced color mapping system for product display
- Multi-step checkout process
- Integrated payment processing
- Admin user management
- Newsletter system
- Seed filtering system

## Development

This project uses:
- React for the frontend
- Supabase for backend functionality
- Edge Functions for AI integration
- Multiple AI providers for content generation

## Getting Started

To run this project locally:

1. Clone this repository
2. Install dependencies with `npm install`
3. Set up environment variables in `.env`
4. Start the development server with `npm run dev`

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/703cc69e-9246-428f-a4d1-93b789573c59) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
