/**
 * Cheech Chatbot Integration Example
 * 
 * This file shows how to integrate the Cheech chatbot into your BitsnBongs application
 * using the existing AI orchestration system with Gemini and Deepseek.
 */

import React, { useEffect, useState } from 'react';
import ChatInterface from './components/chatbot/ChatInterface';
import { CheechChatbot } from './components/chatbot/CheechChatbot';
import { AIServiceManager } from './services/ai/AIServiceManager';
import './styles/chat-interface.css';

interface CheechIntegrationProps {
  // Optional props for customization
  knowledgeBasePath?: string;
  productDatabasePath?: string;
  enableWebNavigation?: boolean;
}

const CheechIntegration: React.FC<CheechIntegrationProps> = ({
  knowledgeBasePath = './data/knowledge-base.json',
  productDatabasePath = './data/products.json',
  enableWebNavigation = true
}) => {
  const [chatbot, setChatbot] = useState<CheechChatbot | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    initializeChatbot();
  }, []);

  const initializeChatbot = async () => {
    try {
      // Initialize AI service manager
      const aiManager = new AIServiceManager();
      
      await aiManager.initialize({
        deepseek_key: import.meta.env.VITE_DEEPSEEK_API_KEY,
        gemini_key: import.meta.env.VITE_GEMINI_API_KEY,
        openrouter_key: import.meta.env.VITE_OPENROUTER_API_KEY
      });

      // Create chatbot instance
      const cheechInstance = new CheechChatbot({
        aiService: aiManager.getUnifiedAI(),
        baseUrl: window.location.origin,
        knowledgeBasePath,
        productDatabasePath,
        maxResponseTokens: 1024,
        temperature: 0.7
      });

      // Generate session ID
      const newSessionId = cheechInstance.createSession();

      setChatbot(cheechInstance);
      setSessionId(newSessionId);
      setIsInitialized(true);

      console.log('Cheech chatbot initialized successfully');
    } catch (err) {
      console.error('Failed to initialize Cheech chatbot:', err);
      setError('Failed to initialize chatbot. Please check your configuration.');
    }
  };

  const handleSendMessage = async (message: string) => {
    if (!chatbot || !sessionId) {
      throw new Error('Chatbot not initialized');
    }

    try {
      return await chatbot.processMessage(sessionId, message);
    } catch (err) {
      console.error('Error processing message:', err);
      throw err;
    }
  };

  if (error) {
    return (
      <div className="cheech-error">
        <p>Error: {error}</p>
        <button onClick={initializeChatbot}>Retry</button>
      </div>
    );
  }

  if (!isInitialized || !chatbot) {
    return (
      <div className="cheech-loading">
        <p>Initializing Cheech...</p>
      </div>
    );
  }

  return (
    <ChatInterface 
      sessionId={sessionId}
      onSendMessage={handleSendMessage}
    />
  );
};

export default CheechIntegration;

/**
 * Example usage in your main App component:
 * 
 * ```tsx
 * import CheechIntegration from './components/CheechIntegration';
 * 
 * function App() {
 *   return (
 *     <div className="App">
 *       {/* Your existing app content *\/}
 *       
 *       {/* Add Cheech chatbot *\/}
 *       <CheechIntegration 
 *         knowledgeBasePath="./data/custom-knowledge.json"
 *         productDatabasePath="./data/products.json"
 *         enableWebNavigation={true}
 *       />
 *     </div>
 *   );
 * }
 * ```
 */

/**
 * Environment Variables Required:
 * 
 * Add these to your .env file:
 * ```
 * VITE_DEEPSEEK_API_KEY=your_deepseek_api_key
 * VITE_GEMINI_API_KEY=your_gemini_api_key
 * VITE_OPENROUTER_API_KEY=your_openrouter_api_key
 * ```
 */

/**
 * Knowledge Base Format:
 * 
 * Create a JSON file with this structure:
 * ```json
 * [
 *   {
 *     "id": "kb-1",
 *     "question": "How do I clean my bong?",
 *     "answer": "To clean your bong, you'll need isopropyl alcohol...",
 *     "category": "cleaning",
 *     "tags": ["bong", "cleaning", "maintenance"]
 *   }
 * ]
 * ```
 */

/**
 * Product Database Format:
 * 
 * Create a JSON file with this structure:
 * ```json
 * [
 *   {
 *     "id": "p-1",
 *     "name": "Crystal Clear Cleaning Solution",
 *     "description": "Premium cleaning solution...",
 *     "price": 14.99,
 *     "imageUrl": "/images/products/crystal-clear.jpg",
 *     "category": "cleaning",
 *     "tags": ["cleaning", "solution"],
 *     "rating": 4.8,
 *     "reviewCount": 156,
 *     "inStock": true
 *   }
 * ]
 * ```
 */
