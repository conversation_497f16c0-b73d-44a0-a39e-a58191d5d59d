
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ShoppingCart, Trash } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { useSavedItems } from "@/hooks/useSavedItems";
import CartItem from "./CartItem";
import SavedItems from "./SavedItems";
import { useAuth } from "@/hooks/auth.basic";
import { useNavigate } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";

export default function CartDrawer() {
  const { cartItems, isLoading, clearCart, totalItems, subtotal } = useCart();
  
  // Wrap auth-related code in try/catch to handle possible initialization issues
  let user = null;
  try {
    const auth = useAuth();
    user = auth?.user;
  } catch (error) {
    console.error('Auth context not available yet:', error);
  }
  
  const navigate = useNavigate();
  
  // Always call useSavedItems to maintain hook order
  let savedItemsHook = { savedItems: [] };
  try {
    savedItemsHook = useSavedItems();
  } catch (error) {
    console.error('SavedItems context not available yet:', error);
  }
  
  const [activeTab, setActiveTab] = useState("cart");

  // Use the hook result only if user is authenticated
  const savedItemsContext = user ? savedItemsHook : { savedItems: [] };

  const handleCheckout = () => {
    // For now, this will just navigate to a checkout page
    navigate("/checkout");
  };

  const handleLogin = () => {
    navigate("/auth");
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="relative group"
          aria-label="Open cart"
        >
          <div className="relative">
            <ShoppingCart />
            {totalItems > 0 && (
              <Badge
                className="absolute -top-2 -right-2 px-2 min-w-[18px] h-[18px] flex items-center justify-center text-xs"
                variant="destructive"
              >
                {totalItems}
              </Badge>
            )}
          </div>
        </Button>
      </SheetTrigger>
      
      {/* Main cart drawer */}
      <SheetContent side="right" className="flex flex-col p-0 w-full max-w-md">
        {/* Header */}
        <SheetHeader className="px-4 py-2 border-b">
          <SheetTitle>Shopping Bag</SheetTitle>
        </SheetHeader>
        
        {/* Tabs */}
        <Tabs
          defaultValue="cart"
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex flex-col h-full"
        >
          <TabsList className="grid w-full grid-cols-2 px-4 py-2">
            <TabsTrigger value="cart" className="relative">
              Cart
              {totalItems > 0 && (
                <Badge className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                  {totalItems}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="saved" className="relative">
              Saved
              {savedItemsContext.savedItems.length > 0 && (
                <Badge className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                  {savedItemsContext.savedItems.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
          
          {/* Cart content area - fixed height with scrolling */}
          <div className="flex flex-col h-[calc(100vh-240px)] overflow-hidden">
            <TabsContent 
              value="cart" 
              className="flex-1 overflow-y-auto px-4 py-2"
            >
              {isLoading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : cartItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-48 text-center">
                  <ShoppingCart className="h-12 w-12 text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-2">Your cart is empty</p>
                  <SheetClose asChild>
                    <Button variant="outline" onClick={() => navigate("/shop")}>
                      Continue Shopping
                    </Button>
                  </SheetClose>
                </div>
              ) : (
                <div className="space-y-4 pb-4">
                  {cartItems.map((item) => {
                    // Make sure we're passing the right data structure to CartItem
                    const cartItemData = {
                      id: item.id,
                      product_id: item.product.id,
                      quantity: item.quantity,
                      price: item.price,
                      product: item.product,
                      variant: item.variant,
                      // If we have a variant with options, use those
                      selected_options: item.variant?.options || {}
                    };

                    return <CartItem key={item.id} item={cartItemData} />;
                  })}
                </div>
              )}
            </TabsContent>
            
            <TabsContent 
              value="saved" 
              className="flex-1 overflow-y-auto px-4 py-2"
            >
              <SavedItems />
            </TabsContent>
          </div>
          
          {/* Fixed footer with checkout button */}
          {activeTab === "cart" && cartItems.length > 0 && (
            <div className="border-t bg-white p-4 space-y-4">
              <div className="flex justify-between">
                <span className="font-medium">Subtotal</span>
                <span className="font-bold">£{subtotal.toFixed(2)}</span>
              </div>

              <div className="text-sm text-gray-500">
                <span>Shipping & taxes calculated at checkout</span>
              </div>

              <SheetFooter className="flex flex-col gap-2 mt-2">
                <SheetClose asChild>
                  <Button onClick={handleCheckout} className="w-full bg-green-600 hover:bg-green-700 text-white">
                    Proceed to Checkout
                  </Button>
                </SheetClose>

                <div className="flex gap-2 w-full">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={clearCart}
                    disabled={isLoading}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Clear Cart
                  </Button>

                  <SheetClose asChild>
                    <Button variant="ghost" className="flex-1" onClick={() => navigate("/shop")}>
                      Continue Shopping
                    </Button>
                  </SheetClose>
                </div>
              </SheetFooter>
            </div>
          )}
          
          {/* Sign in prompt for empty cart */}
          {!user && cartItems.length === 0 && activeTab === "cart" && (
            <div className="border-t p-4 mt-auto">
              <p className="text-sm text-center text-gray-500 mb-2">
                Sign in to see your saved cart
              </p>
              <SheetClose asChild>
                <Button onClick={handleLogin} className="w-full">
                  Sign In
                </Button>
              </SheetClose>
            </div>
          )}
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}
