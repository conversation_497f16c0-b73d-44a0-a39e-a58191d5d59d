// Script to transform product data from original CSV to our template format
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// File paths
const inputFile = path.join(__dirname, '../catalog_products.csv');
const outputProductsFile = path.join(__dirname, './transformed_products.csv');
const outputVariantsFile = path.join(__dirname, './transformed_variants.csv');

// Number of products to process (set to -1 for all)
const PRODUCT_LIMIT = -1; // Process all products

// Function to create a slug from a product name
function createSlug(name) {
  const baseSlug = name.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-|-$/g, '');

  // Add a short unique suffix to ensure uniqueness
  const uniqueSuffix = uuidv4().substring(0, 8);
  return `${baseSlug}-${uniqueSuffix}`;
}

// Function to transform image filename
function transformImageFilename(originalFilename) {
  if (!originalFilename) return '';

  console.log(`Original filename: ${originalFilename}`);

  // Extract just the filename from the URL if it's a full URL
  let filename = originalFilename;
  if (filename.includes('/')) {
    filename = filename.split('/').pop();
  }

  console.log(`After extracting filename: ${filename}`);

  // Remove ~mv2 or ~cv2 suffix and change extension to .webp
  const transformed = filename
    .replace(/~(mv2|cv2)(\.jpg|\.jpeg|\.png|\.gif|\.webp)?$/i, '.webp')
    .replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');

  console.log(`Transformed filename: ${transformed}`);

  return transformed;
}

// Function to create full image path
function createImagePath(filename) {
  if (!filename) return '';

  // Use the double product-images path structure as confirmed in the console logs
  return `https://pkjyjuaiokrhgbutjhla.supabase.co/storage/v1/object/public/product-images/product-images/${filename}`;
}

// Function to normalize option names (using British spelling)
function normalizeOptionName(name) {
  if (!name) return '';

  const normalized = name.trim();

  // Standardize common option names with British spelling
  if (/pack\s*size/i.test(normalized)) return "Pack Size";
  if (/colou?r/i.test(normalized)) return "Colour";
  if (/flavou?r/i.test(normalized)) return "Flavour";
  if (/size/i.test(normalized) && !/pack\s*size/i.test(normalized)) return "Size";

  // Return capitalized version of other names
  return normalized.charAt(0).toUpperCase() + normalized.slice(1).toLowerCase();
}

// Function to normalize option values
function normalizeOptionValue(optionName, value) {
  if (!value) return '';

  const normalized = value.trim();

  // Handle pack size format
  if (optionName === "Pack Size" && /\d+/.test(normalized) && !normalized.toLowerCase().includes('pack')) {
    const match = normalized.match(/(\d+)/);
    if (match) return `${match[1]} Pack`;
  }

  return normalized;
}

// Function to determine display type based on option name
function getDisplayType(optionName) {
  if (!optionName) return 'dropdown';

  const normalized = optionName.toLowerCase();

  if (normalized === 'colour') return 'swatch';
  if (normalized.includes('pack') || normalized.includes('size')) return 'visual';

  return 'dropdown'; // Default
}

// Function to extract option definitions from product
function extractOptionDefinitions(product) {
  const options = {};

  // Check for option fields (up to 3 options)
  for (let i = 1; i <= 3; i++) {
    const nameField = `productOptionName${i}`;
    // typeField is not used but kept for reference
    // const typeField = `productOptionType${i}`;
    const descField = `productOptionDescription${i}`;

    if (product[nameField] && product[nameField].trim()) {
      // Normalize the option name
      const rawOptionName = product[nameField].trim();
      const optionName = normalizeOptionName(rawOptionName);

      // Determine display_type based on the normalized option name
      const displayType = getDisplayType(optionName);

      // Extract values from description (semicolon-separated in our CSV)
      let values = [];
      if (product[descField]) {
        // First, check if the description contains the Wix-specific type (like DROP_DOWN)
        let description = product[descField];

        // If the description contains a comma followed by DROP_DOWN or similar, remove that part
        if (description.includes(',') &&
            (description.includes('DROP_DOWN') ||
             description.includes('COLOR') ||
             description.includes('COLOUR'))) {
          description = description.split(',')[1]; // Take the part after the comma
        }

        // Split by semicolons to get individual values
        values = description.split(';')
          .map(v => normalizeOptionValue(optionName, v.trim()))
          .filter(v => v);
      }

      if (values.length > 0) {
        options[optionName] = {
          name: optionName,
          display_type: displayType,
          values: values
        };

        console.log(`Found option: ${optionName} with values: ${values.join(', ')}`);
      }
    }
  }

  // The frontend expects option_definitions to be a JSONB object in the database
  // For CSV, we need to stringify it to ensure it's properly formatted
  return Object.keys(options).length > 0 ? JSON.stringify(options) : null;
}

// Function to generate variants from option combinations
function generateVariants(productId, product, optionDefinitions, variantRows = []) {
  if (!optionDefinitions) return [];

  // optionDefinitions might be a JSON string if it came from extractOptionDefinitions
  const options = typeof optionDefinitions === 'string'
    ? JSON.parse(optionDefinitions)
    : optionDefinitions;

  // If no options with values, return empty array
  if (Object.keys(options).length === 0) return [];

  // Get all option names and their values
  const optionNames = Object.keys(options);
  const optionValues = optionNames.map(name => options[name].values);

  // Generate all combinations of option values
  function generateCombinations(arrays, current = [], index = 0) {
    if (index === arrays.length) {
      return [current];
    }

    let result = [];
    for (let i = 0; i < arrays[index].length; i++) {
      result = result.concat(
        generateCombinations(arrays, [...current, arrays[index][i]], index + 1)
      );
    }
    return result;
  }

  const combinations = generateCombinations(optionValues);

  // Extract price adjustments from variant rows if available
  const variantPriceAdjustments = {};

  // Process variant rows to extract price adjustments
  if (variantRows && variantRows.length > 0) {
    console.log(`Processing ${variantRows.length} variant rows for product ${product.handleId}`);

    // For each variant row, extract the option value and price adjustment
    variantRows.forEach(variant => {
      // Find which option this variant corresponds to
      for (let i = 1; i <= 3; i++) {
        const optionValueField = `productOptionValue${i}`;
        if (variant[optionValueField] && variant[optionValueField].trim()) {
          // Normalize the option value
          const rawOptionValue = variant[optionValueField].trim();

          // Find the corresponding option name
          const optionNameField = `productOptionName${i}`;
          const rawOptionName = product[optionNameField] ? product[optionNameField].trim() : null;

          if (rawOptionName) {
            // Normalize the option name and value
            const optionName = normalizeOptionName(rawOptionName);
            const optionValue = normalizeOptionValue(optionName, rawOptionValue);
            const priceAdjustment = parseFloat(variant.surcharge) || 0;

            // Initialize if not exists
            if (!variantPriceAdjustments[optionName]) {
              variantPriceAdjustments[optionName] = {};
            }

            // Store the price adjustment for this option value
            variantPriceAdjustments[optionName][optionValue] = priceAdjustment;
            console.log(`Found variant price adjustment for ${optionName}=${optionValue}: +${priceAdjustment}`);
          }
        }
      }
    });
  }

  // Extract price adjustments from product data as fallback
  const priceAdjustments = {};

  // Check for option price adjustments in the product data
  for (let i = 1; i <= 3; i++) {
    const nameField = `productOptionName${i}`;
    const priceField = `productOptionPriceAdjustment${i}`;

    if (product[nameField] && product[priceField]) {
      // Normalize the option name
      const rawOptionName = product[nameField].trim();
      const optionName = normalizeOptionName(rawOptionName);
      const priceAdjustmentStr = product[priceField];

      // Parse price adjustments - they might be in format like "0;5;10" for multiple values
      if (priceAdjustmentStr && priceAdjustmentStr.includes(';')) {
        const adjustments = priceAdjustmentStr.split(';').map(a => parseFloat(a) || 0);

        // Store the adjustments for this option
        priceAdjustments[optionName] = {};

        // Find the normalized option in the options object
        const normalizedOptionKey = Object.keys(options).find(key =>
          key.toLowerCase() === optionName.toLowerCase()
        ) || optionName;

        if (options[normalizedOptionKey] && options[normalizedOptionKey].values) {
          options[normalizedOptionKey].values.forEach((value, index) => {
            if (index < adjustments.length) {
              priceAdjustments[optionName][value] = adjustments[index];
            }
          });

          console.log(`Found price adjustments for ${optionName} in product data`);
        }
      }
    }
  }

  // Create variants from combinations
  return combinations.map((combo) => {
    // Create option combination object
    const optionCombination = {};
    optionNames.forEach((name, i) => {
      optionCombination[name] = combo[i];
    });

    // Create variant name from combination
    const variantName = combo.join(', ');

    // Create SKU by appending option codes to base SKU
    let sku = product.sku || '';
    if (sku) {
      const optionCodes = combo.map(val => val.substring(0, 3).toUpperCase());
      sku = `${sku}-${optionCodes.join('-')}`;
    }

    // Calculate absolute price for variants
    let price = parseFloat(product.price) || 0;
    let totalAdjustment = 0;

    // Apply price adjustments based on actual option values if available
    optionNames.forEach((name, i) => {
      const optionValue = combo[i];

      // First check variant rows for price adjustments
      if (variantPriceAdjustments[name] && variantPriceAdjustments[name][optionValue] !== undefined) {
        const adjustment = variantPriceAdjustments[name][optionValue];
        price += adjustment;
        totalAdjustment += adjustment;
        console.log(`Applied variant row price adjustment for ${name}=${optionValue}: +${adjustment}`);
      }
      // Then check product data for price adjustments
      else if (priceAdjustments[name] && priceAdjustments[name][optionValue] !== undefined) {
        const adjustment = priceAdjustments[name][optionValue];
        price += adjustment;
        totalAdjustment += adjustment;
        console.log(`Applied product data price adjustment for ${name}=${optionValue}: +${adjustment}`);
      }
      // Special case for Auto Gorilla Glue 10 Pack - this is the only product with a known price variation
      if (product.name && product.name.includes('Auto Gorilla Glue') && normalizeOptionName(name).toLowerCase() === 'pack size') {
        if (optionValue === '10 Pack') {
          price = 81; // Set absolute price to £81
          console.log(`Set absolute price for Auto Gorilla Glue 10 Pack to £81`);
          totalAdjustment = 37; // This should make the frontend show +£37
        } else if (optionValue === '5 Pack') {
          price = 44; // Set absolute price to £44
          console.log(`Set absolute price for Auto Gorilla Glue 5 Pack to £44`);
          totalAdjustment = 0; // No adjustment for base price
        }
      } else {
        // For all other products, don't apply any price adjustment
        // Use the base product price for all variants
        console.log(`No price adjustment applied for ${name}=${optionValue} (using exact Wix data)`);
      }
    });

    // Create a metadata object to store additional information
    const metadata = {
      price_adjustment: totalAdjustment
    };

    return {
      id: uuidv4(),
      product_id: productId,
      variant_name: variantName,
      sku: sku,
      // Ensure price is a number, not a string
      price: parseFloat(price) || 0,
      // Ensure sale_price is a number or null, not an empty string
      sale_price: null,
      // Log the final price for debugging
      _debug_price: `Final price for ${variantName}: £${parseFloat(price) || 0} (base: £${parseFloat(product.price) || 0}, adjustment: £${totalAdjustment})`,
      stock_quantity: product.inventory === 'InStock' ? 100 : 0,
      in_stock: product.inventory === 'InStock',
      image: '', // Variants typically use the main product image
      option_combination: JSON.stringify(optionCombination),
      is_active: product.visible === 'TRUE',
      metadata: JSON.stringify(metadata), // Store metadata as JSON
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  });
}

// CSV writers
const productWriter = createCsvWriter({
  path: outputProductsFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'name', title: 'name' },
    { id: 'slug', title: 'slug' },
    { id: 'description', title: 'description' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'cost_price', title: 'cost_price' },
    { id: 'image', title: 'image' },
    { id: 'additional_images', title: 'additional_images' },
    { id: 'category_id', title: 'category_id' },
    { id: 'subcategory_id', title: 'subcategory_id' },
    { id: 'brand_id', title: 'brand_id' },
    { id: 'sku', title: 'sku' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'weight', title: 'weight' },
    { id: 'dimensions', title: 'dimensions' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'is_active', title: 'is_active' },
    { id: 'is_featured', title: 'is_featured' },
    { id: 'is_new', title: 'is_new' },
    { id: 'is_best_seller', title: 'is_best_seller' },
    { id: 'rating', title: 'rating' },
    { id: 'review_count', title: 'review_count' },
    { id: 'option_definitions', title: 'option_definitions' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

const variantWriter = createCsvWriter({
  path: outputVariantsFile,
  header: [
    { id: 'id', title: 'id' },
    { id: 'product_id', title: 'product_id' },
    { id: 'variant_name', title: 'variant_name' },
    { id: 'sku', title: 'sku' },
    { id: 'price', title: 'price' },
    { id: 'sale_price', title: 'sale_price' },
    { id: 'stock_quantity', title: 'stock_quantity' },
    { id: 'in_stock', title: 'in_stock' },
    { id: 'image', title: 'image' },
    { id: 'option_combination', title: 'option_combination' },
    { id: 'is_active', title: 'is_active' },
    { id: 'metadata', title: 'metadata' },
    { id: 'created_at', title: 'created_at' },
    { id: 'updated_at', title: 'updated_at' }
  ]
});

// Process the CSV file
console.log(`Reading products from ${inputFile}...`);
const products = [];
const variantRows = []; // Store variant rows separately
const variants = []; // Store generated variants

fs.createReadStream(inputFile)
  .pipe(csv())
  .on('data', (row) => {
    // Check if this is a product or variant row
    if (row.fieldType === 'Product' && row.name && row.name.trim()) {
      products.push(row);
    } else if (row.fieldType === 'Variant') {
      // Store variant rows to process later
      variantRows.push(row);
      console.log(`Found variant row for product ${row.handleId}`);
    }
  })
  .on('end', async () => {
    console.log(`Read ${products.length} products and ${variantRows.length} variant rows from CSV`);

    // Group variant rows by product ID
    const variantsByProduct = {};
    variantRows.forEach(variant => {
      if (!variantsByProduct[variant.handleId]) {
        variantsByProduct[variant.handleId] = [];
      }
      variantsByProduct[variant.handleId].push(variant);
    });

    // Limit the number of products if specified
    const productsToProcess = PRODUCT_LIMIT > 0 ? products.slice(0, PRODUCT_LIMIT) : products;
    console.log(`Processing ${productsToProcess.length} products...`);

    // Transform products
    const transformedProducts = [];

    for (const product of productsToProcess) {
      // Generate a UUID for the product
      const productId = uuidv4();

      // Handle main image and additional images
      let mainImagePath = '';
      let additionalImages = [];

      console.log(`Processing product: ${product.name}`);
      console.log(`Product image URL: ${product.productImageUrl}`);

      // Check if productImageUrl contains multiple images (separated by semicolons)
      if (product.productImageUrl && product.productImageUrl.includes(';')) {
        const imageUrls = product.productImageUrl.split(';');
        console.log(`Found ${imageUrls.length} images in URL`);

        // Use the first image as the main image
        if (imageUrls.length > 0 && imageUrls[0]) {
          const mainImageFilename = transformImageFilename(imageUrls[0]);
          mainImagePath = createImagePath(mainImageFilename);
          console.log(`Main image path: ${mainImagePath}`);
        }

        // Process additional images
        for (let i = 1; i < imageUrls.length; i++) {
          if (imageUrls[i] && imageUrls[i].trim()) {
            const additionalImageFilename = transformImageFilename(imageUrls[i]);
            if (additionalImageFilename) {
              const additionalImagePath = createImagePath(additionalImageFilename);
              additionalImages.push(additionalImagePath);
              console.log(`Additional image ${i}: ${additionalImagePath}`);
            }
          }
        }
      } else if (product.productImageUrl) {
        // Single image
        console.log(`Single image found`);
        const mainImageFilename = transformImageFilename(product.productImageUrl);
        mainImagePath = createImagePath(mainImageFilename);
        console.log(`Main image path: ${mainImagePath}`);
      }

      // Check if product has an image
      const hasImage = !!product.productImageUrl;

      // Log if product has no image
      if (!hasImage) {
        console.log(`Product "${product.name}" has no image and will be set to inactive`);
      }

      // Extract option definitions
      const optionDefinitions = extractOptionDefinitions(product);

      // Create transformed product
      const transformedProduct = {
        id: productId,
        name: product.name.trim(),
        slug: createSlug(product.name),
        description: product.description || '',
        // Ensure price is a number, not a string
        price: parseFloat(product.price) || 0,
        // Ensure sale_price is a number or null, not an empty string
        sale_price: product.discountValue ? parseFloat(product.discountValue) : null,
        // Ensure cost_price is a number or null, not an empty string
        cost_price: parseFloat(product.cost) || null,
        image: mainImagePath,
        additional_images: additionalImages.length > 0 ? JSON.stringify(additionalImages) : '[]',
        category_id: '', // Will be set manually
        subcategory_id: '',
        brand_id: '',
        sku: product.sku || '',
        stock_quantity: product.inventory === 'InStock' ? 100 : 0,
        weight: parseFloat(product.weight) || '',
        dimensions: '',
        in_stock: product.inventory === 'InStock',
        is_active: hasImage && product.visible === 'TRUE', // Set to inactive if no image
        is_featured: false, // Default values
        is_new: false,
        is_best_seller: false,
        rating: 5, // Default rating
        review_count: 0,
        option_definitions: optionDefinitions,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      transformedProducts.push(transformedProduct);

      // Get variant rows for this product if they exist
      const productVariantRows = variantsByProduct[product.handleId] || [];
      console.log(`Found ${productVariantRows.length} variant rows for product ${product.handleId}`);

      // Generate variants if option definitions exist
      const productVariants = generateVariants(productId, product, optionDefinitions, productVariantRows);
      variants.push(...productVariants);
    }

    // Write transformed products to CSV
    await productWriter.writeRecords(transformedProducts);
    console.log(`Wrote ${transformedProducts.length} products to ${outputProductsFile}`);

    // Write variants to CSV if any exist
    if (variants.length > 0) {
      await variantWriter.writeRecords(variants);
      console.log(`Wrote ${variants.length} variants to ${outputVariantsFile}`);
    }

    console.log('Transformation complete!');
  });
