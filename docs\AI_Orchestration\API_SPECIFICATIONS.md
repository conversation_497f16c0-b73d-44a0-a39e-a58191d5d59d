# 📡 API Specifications: Unified AI Service Interface

## Executive Summary

The **Unified AI API** provides a single, consistent interface for all AI operations across the BitsNBongs platform. This specification defines the complete API contract, including request/response schemas, authentication, rate limiting, error handling, and usage examples.

**API Design Principles:**
- 🎯 **Single Entry Point**: One API for all AI requests
- 📝 **Consistent Schema**: Standardized request/response format
- 🔒 **Secure by Default**: Built-in authentication and validation
- 📊 **Observable**: Comprehensive logging and metrics
- 🔄 **Backward Compatible**: Smooth migration from legacy systems
- ⚡ **High Performance**: Optimized for speed and efficiency

---

## 🌐 API Overview

### **Base Configuration**

```typescript
const API_CONFIG = {
  baseUrl: '/api/ai',
  version: 'v1',
  timeout: 30000, // 30 seconds
  retries: 3,
  rateLimit: {
    requests: 100,
    window: '1m'
  }
};
```

### **Core Endpoints**

| Endpoint | Method | Purpose | Auth Required |
|----------|--------|---------|---------------|
| `/api/ai/v1/process` | POST | Main AI request processing | Yes |
| `/api/ai/v1/estimate` | POST | Cost estimation | Yes |
| `/api/ai/v1/status` | GET | System health status | No |
| `/api/ai/v1/providers` | GET | Available providers | Yes |
| `/api/ai/v1/usage` | GET | Usage statistics | Yes |
| `/api/ai/v1/cache` | DELETE | Clear cache | Yes |

---

## 🔧 Core API Specifications

### **1. Main Processing Endpoint**

#### **POST /api/ai/v1/process**

The primary endpoint for all AI requests.

#### Request Schema

```typescript
interface AIRequest {
  // Required fields
  type: AIRequestType;
  content: string;
  
  // Optional configuration
  provider?: AIProvider | 'auto';
  priority?: 'low' | 'normal' | 'high' | 'critical';
  max_cost?: number;
  timeout?: number;
  
  // Context and metadata
  context?: {
    business_type?: 'cannabis' | 'general';
    target_audience?: string;
    brand_voice?: 'professional' | 'casual' | 'friendly';
    seo_keywords?: string[];
    product_category?: string;
    [key: string]: any;
  };
  
  // Request tracking
  request_id?: string;
  user_id?: string;
  session_id?: string;
  timestamp?: Date;
  
  // Advanced options
  options?: {
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
    cache?: boolean;
    fallback_enabled?: boolean;
  };
}
```

#### Request Types

```typescript
type AIRequestType = 
  // Content Generation
  | 'product_description'
  | 'blog_content'
  | 'newsletter_content'
  | 'social_media_post'
  | 'seo_optimization'
  | 'hashtag_generation'
  | 'creative_campaigns'
  
  // Analysis & Processing
  | 'sentiment_analysis'
  | 'content_analysis'
  | 'keyword_extraction'
  | 'fraud_detection'
  | 'customer_analysis'
  | 'complex_analysis'
  
  // Specialized Tasks
  | 'image_description'
  | 'voice_transcription'
  | 'translation'
  | 'summarization'
  | 'personalization';
```

#### Response Schema

```typescript
interface AIResponse {
  // Status and metadata
  success: boolean;
  request_id: string;
  timestamp: Date;
  processing_time: number;
  
  // Core response data
  content?: string;
  data?: any;
  
  // Provider information
  provider_used: AIProvider;
  provider_model?: string;
  fallback_used?: boolean;
  
  // Cost and usage
  cost: {
    estimated: number;
    actual: number;
    currency: 'USD';
    tokens_used: number;
  };
  
  // Quality metrics
  quality: {
    confidence: number;
    relevance: number;
    completeness: number;
  };
  
  // Error handling
  error?: {
    code: string;
    message: string;
    details?: any;
    retry_after?: number;
  };
  
  // Caching information
  cache: {
    hit: boolean;
    ttl?: number;
    key?: string;
  };
}
```

#### Example Request

```typescript
// Product Description Generation
const productRequest: AIRequest = {
  type: 'product_description',
  content: 'Blue Dream Cannabis Seeds - Feminized, High THC',
  provider: 'auto',
  priority: 'normal',
  context: {
    business_type: 'cannabis',
    target_audience: 'experienced growers',
    brand_voice: 'professional',
    seo_keywords: ['blue dream', 'feminized seeds', 'high thc'],
    product_category: 'cannabis_seeds'
  },
  options: {
    max_tokens: 500,
    temperature: 0.7,
    cache: true
  }
};
```

#### Example Response

```typescript
const productResponse: AIResponse = {
  success: true,
  request_id: 'req_1234567890',
  timestamp: new Date('2025-01-28T10:30:00Z'),
  processing_time: 1250,
  
  content: `Blue Dream Feminized Cannabis Seeds offer the perfect balance of relaxation and euphoria. This legendary hybrid strain combines the best of Blueberry and Haze genetics, delivering a sweet berry aroma with earthy undertones. 

With THC levels reaching 18-24%, Blue Dream provides a gentle cerebral high followed by full-body relaxation. These feminized seeds guarantee female plants, making them ideal for both novice and experienced growers.

Key Features:
• 60% Sativa / 40% Indica hybrid
• Flowering time: 9-10 weeks
• High yield potential
• Resistant to common molds
• Perfect for indoor and outdoor cultivation`,
  
  provider_used: 'deepseek',
  provider_model: 'deepseek-chat',
  fallback_used: false,
  
  cost: {
    estimated: 0.008,
    actual: 0.007,
    currency: 'USD',
    tokens_used: 347
  },
  
  quality: {
    confidence: 0.92,
    relevance: 0.95,
    completeness: 0.88
  },
  
  cache: {
    hit: false,
    ttl: 3600,
    key: 'prod_desc_blue_dream_fem'
  }
};
```

---

### **2. Cost Estimation Endpoint**

#### **POST /api/ai/v1/estimate**

Provides cost estimates before processing requests.

#### Request Schema

```typescript
interface CostEstimateRequest {
  type: AIRequestType;
  content: string;
  provider?: AIProvider | 'auto';
  options?: {
    max_tokens?: number;
    temperature?: number;
  };
}
```

#### Response Schema

```typescript
interface CostEstimateResponse {
  success: boolean;
  estimates: {
    provider: AIProvider;
    estimated_cost: number;
    estimated_tokens: number;
    estimated_time: number;
    confidence: number;
  }[];
  recommended: {
    provider: AIProvider;
    reasoning: string;
    cost_savings: number;
  };
}
```

---

### **3. System Status Endpoint**

#### **GET /api/ai/v1/status**

Returns real-time system health and provider status.

#### Response Schema

```typescript
interface SystemStatusResponse {
  success: boolean;
  timestamp: Date;
  overall_status: 'healthy' | 'degraded' | 'critical';
  
  providers: {
    [provider: string]: {
      status: 'available' | 'degraded' | 'unavailable';
      response_time: number;
      error_rate: number;
      rate_limited: boolean;
      quota_remaining?: number;
      last_checked: Date;
    };
  };
  
  system_metrics: {
    requests_per_minute: number;
    average_response_time: number;
    success_rate: number;
    cache_hit_rate: number;
  };
}
```

---

### **4. Provider Information Endpoint**

#### **GET /api/ai/v1/providers**

Returns available providers and their capabilities.

#### Response Schema

```typescript
interface ProvidersResponse {
  success: boolean;
  providers: {
    [provider: string]: {
      name: string;
      status: 'available' | 'unavailable';
      capabilities: {
        supports_streaming: boolean;
        supports_images: boolean;
        supports_function_calling: boolean;
        max_context_length: number;
        supported_formats: string[];
      };
      pricing: {
        cost_per_1k_tokens: number;
        currency: 'USD';
      };
      performance: {
        average_response_time: number;
        reliability_score: number;
      };
    };
  };
}
```

---

### **5. Usage Statistics Endpoint**

#### **GET /api/ai/v1/usage**

Returns usage statistics and analytics.

#### Query Parameters

```typescript
interface UsageQueryParams {
  period?: 'hour' | 'day' | 'week' | 'month';
  provider?: AIProvider;
  type?: AIRequestType;
  start_date?: string;
  end_date?: string;
}
```

#### Response Schema

```typescript
interface UsageResponse {
  success: boolean;
  period: string;
  
  summary: {
    total_requests: number;
    total_cost: number;
    average_response_time: number;
    success_rate: number;
  };
  
  by_provider: {
    [provider: string]: {
      requests: number;
      cost: number;
      success_rate: number;
      average_response_time: number;
    };
  };
  
  by_type: {
    [type: string]: {
      requests: number;
      cost: number;
      success_rate: number;
    };
  };
  
  trends: {
    cost_trend: 'increasing' | 'decreasing' | 'stable';
    usage_trend: 'increasing' | 'decreasing' | 'stable';
    optimization_opportunities: string[];
  };
}
```

---

## 🔒 Authentication & Security

### **Authentication Methods**

#### **1. API Key Authentication**

```typescript
// Header-based authentication
const headers = {
  'Authorization': 'Bearer your-api-key',
  'Content-Type': 'application/json'
};
```

#### **2. Session-based Authentication**

```typescript
// Cookie-based for web applications
const config = {
  withCredentials: true,
  headers: {
    'X-CSRF-Token': 'csrf-token'
  }
};
```

### **Rate Limiting**

```typescript
interface RateLimitHeaders {
  'X-RateLimit-Limit': string;      // Requests per window
  'X-RateLimit-Remaining': string;  // Remaining requests
  'X-RateLimit-Reset': string;      // Reset timestamp
  'X-RateLimit-Window': string;     // Window duration
}
```

### **Security Headers**

```typescript
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000'
};
```

---

## ⚠️ Error Handling

### **Error Response Schema**

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: Date;
    request_id?: string;
    retry_after?: number;
  };
}
```

### **Error Codes**

| Code | HTTP Status | Description | Retry |
|------|-------------|-------------|-------|
| `INVALID_REQUEST` | 400 | Malformed request | No |
| `UNAUTHORIZED` | 401 | Invalid authentication | No |
| `FORBIDDEN` | 403 | Insufficient permissions | No |
| `NOT_FOUND` | 404 | Endpoint not found | No |
| `RATE_LIMITED` | 429 | Rate limit exceeded | Yes |
| `PROVIDER_UNAVAILABLE` | 503 | All providers down | Yes |
| `QUOTA_EXCEEDED` | 503 | Provider quota exceeded | Yes |
| `TIMEOUT` | 504 | Request timeout | Yes |
| `INTERNAL_ERROR` | 500 | Server error | Yes |

### **Error Examples**

```typescript
// Rate limit exceeded
const rateLimitError: ErrorResponse = {
  success: false,
  error: {
    code: 'RATE_LIMITED',
    message: 'Rate limit exceeded. Try again later.',
    details: {
      limit: 100,
      window: '1m',
      reset_time: '2025-01-28T10:31:00Z'
    },
    timestamp: new Date(),
    retry_after: 60
  }
};

// Provider unavailable
const providerError: ErrorResponse = {
  success: false,
  error: {
    code: 'PROVIDER_UNAVAILABLE',
    message: 'All AI providers are currently unavailable',
    details: {
      attempted_providers: ['deepseek', 'gemini', 'openrouter'],
      fallback_available: false
    },
    timestamp: new Date(),
    retry_after: 30
  }
};
```

---

## 📊 Usage Examples

### **1. Blog Content Generation**

```typescript
const blogRequest = {
  type: 'blog_content',
  content: 'Write a comprehensive guide about growing cannabis indoors',
  context: {
    business_type: 'cannabis',
    target_audience: 'beginner growers',
    brand_voice: 'educational',
    seo_keywords: ['indoor growing', 'cannabis cultivation', 'grow lights']
  },
  options: {
    max_tokens: 2000,
    temperature: 0.8
  }
};

const response = await fetch('/api/ai/v1/process', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(blogRequest)
});
```

### **2. Social Media Post Generation**

```typescript
const socialRequest = {
  type: 'social_media_post',
  content: 'New strain: Purple Haze - Premium quality seeds now available',
  context: {
    business_type: 'cannabis',
    platform: 'instagram',
    brand_voice: 'friendly',
    include_hashtags: true
  },
  options: {
    max_tokens: 300,
    temperature: 0.9
  }
};
```

### **3. SEO Optimization**

```typescript
const seoRequest = {
  type: 'seo_optimization',
  content: 'Optimize this product description for search engines',
  context: {
    target_keywords: ['feminized seeds', 'high yield', 'indoor growing'],
    meta_description: true,
    title_tag: true
  }
};
```

### **4. Batch Processing**

```typescript
const batchRequests = [
  { type: 'product_description', content: 'Product 1...' },
  { type: 'product_description', content: 'Product 2...' },
  { type: 'product_description', content: 'Product 3...' }
];

const batchResponse = await Promise.all(
  batchRequests.map(request => 
    fetch('/api/ai/v1/process', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(request)
    })
  )
);
```

---

## 🚀 Performance Optimization

### **Caching Strategy**

```typescript
interface CacheConfig {
  enabled: boolean;
  ttl: number;           // Time to live in seconds
  key_strategy: 'content' | 'hash' | 'custom';
  invalidation: 'ttl' | 'manual' | 'event';
  compression: boolean;
}

// Cache key generation
const generateCacheKey = (request: AIRequest): string => {
  const key = `${request.type}:${hashContent(request.content)}:${JSON.stringify(request.context)}`;
  return btoa(key).substring(0, 32);
};
```

### **Request Optimization**

```typescript
// Optimized request with caching
const optimizedRequest = {
  ...baseRequest,
  options: {
    cache: true,
    cache_ttl: 3600,
    compression: true,
    priority: 'normal'
  }
};
```

### **Streaming Responses**

```typescript
// Enable streaming for long content
const streamingRequest = {
  type: 'blog_content',
  content: 'Long form content...',
  options: {
    stream: true,
    chunk_size: 100
  }
};

// Handle streaming response
const response = await fetch('/api/ai/v1/process', {
  method: 'POST',
  headers: headers,
  body: JSON.stringify(streamingRequest)
});

const reader = response.body?.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = new TextDecoder().decode(value);
  console.log('Received chunk:', chunk);
}
```

---

## 📈 Monitoring & Analytics

### **Request Tracking**

```typescript
interface RequestMetrics {
  request_id: string;
  timestamp: Date;
  type: AIRequestType;
  provider: AIProvider;
  processing_time: number;
  cost: number;
  success: boolean;
  cache_hit: boolean;
  user_id?: string;
}
```

### **Performance Metrics**

```typescript
interface PerformanceMetrics {
  endpoint: string;
  method: string;
  response_time: number;
  status_code: number;
  error_rate: number;
  throughput: number;
  cache_hit_rate: number;
}
```

### **Cost Analytics**

```typescript
interface CostAnalytics {
  period: string;
  total_cost: number;
  cost_by_provider: Record<string, number>;
  cost_by_type: Record<string, number>;
  optimization_savings: number;
  projected_monthly_cost: number;
}
```

---

## 🔧 SDK & Client Libraries

### **TypeScript/JavaScript SDK**

```typescript
class UnifiedAIClient {
  private apiKey: string;
  private baseUrl: string;
  
  constructor(config: { apiKey: string; baseUrl?: string }) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || '/api/ai/v1';
  }
  
  async process(request: AIRequest): Promise<AIResponse> {
    const response = await fetch(`${this.baseUrl}/process`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    return response.json();
  }
  
  async estimate(request: CostEstimateRequest): Promise<CostEstimateResponse> {
    // Implementation...
  }
  
  async getStatus(): Promise<SystemStatusResponse> {
    // Implementation...
  }
}

// Usage
const client = new UnifiedAIClient({ apiKey: 'your-api-key' });
const response = await client.process({
  type: 'product_description',
  content: 'Cannabis seeds description...'
});
```

### **React Hook**

```typescript
function useUnifiedAI() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const processRequest = useCallback(async (request: AIRequest) => {
    setLoading(true);
    setError(null);
    
    try {
      const client = new UnifiedAIClient({ apiKey: process.env.REACT_APP_AI_API_KEY });
      const response = await client.process(request);
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);
  
  return { processRequest, loading, error };
}
```

---

## 📋 Implementation Checklist

### **Phase 2 API Deliverables** ✅
- [x] Complete API specification documentation
- [x] Request/response schema definitions
- [x] Authentication and security specifications
- [x] Error handling and status codes
- [x] Usage examples and SDK patterns
- [ ] OpenAPI/Swagger documentation
- [ ] Postman collection
- [ ] API testing suite

### **Next Steps**
1. **Generate OpenAPI Spec**: Create machine-readable API documentation
2. **Build SDK**: Implement TypeScript client library
3. **Create Tests**: Comprehensive API testing suite
4. **Documentation**: Interactive API explorer

---

*API Specification Version: 1.0*  
*Last Updated: January 28, 2025*  
*Next Review: February 1, 2025*

**🐢 Tortoise Status: API specifications complete, ready for routing strategy! 🚀**