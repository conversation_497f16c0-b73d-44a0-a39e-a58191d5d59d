# 🤖 Second Agent Task Structure

## 📁 **File Structure for Second Agent Tasks**

### **🎯 Primary Focus: Image Scraping System**
```
src/services/ai/tools/
├── image-scraping/
│   ├── PlaywrightImageScraper.ts      // Main MCP-based scraper
│   ├── ImageQualityAssessor.ts        // Quality ranking & filtering
│   ├── SourceManager.ts               // Retailer source management
│   ├── BulkImageProcessor.ts          // Batch processing for 1000+ products
│   ├── ImageValidator.ts              // Image validation & verification
│   └── types/
│       ├── ImageScrapingTypes.ts      // Type definitions
│       └── SourceTypes.ts             // Source configuration types
├── mcp-integration/
│   ├── PlaywrightMCPClient.ts         // MCP Playwright client wrapper
│   ├── MCPConnectionManager.ts        // Connection management
│   └── MCPErrorHandler.ts             // Error handling for MCP
└── README.md                          // Documentation for tools
```

### **🎯 Secondary Focus: Monitoring & Analytics**
```
src/services/ai/monitoring/
├── UsageTracker.ts                    // Track AI usage per request/tenant
├── CostCalculator.ts                  // Calculate costs per provider
├── PerformanceMonitor.ts              // Response times & success rates
├── AlertManager.ts                    // Alerts for high usage/costs
├── ReportGenerator.ts                 // Generate usage reports
└── types/
    ├── MonitoringTypes.ts             // Monitoring type definitions
    └── ReportTypes.ts                 // Report structure types
```

### **🎯 Tertiary Focus: Caching System**
```
src/services/ai/cache/
├── AIResponseCache.ts                 // Cache AI responses
├── ImageCache.ts                      // Cache image search results
├── ContextCache.ts                    // Cache conversation context
├── CacheManager.ts                    // Cache lifecycle management
├── CacheStorage.ts                    // Storage abstraction layer
└── types/
    └── CacheTypes.ts                  // Cache configuration types
```

### **🎯 Future Focus: Multi-Tenant Infrastructure**
```
src/services/ai/tenant/
├── TenantConfigManager.ts             // Tenant-specific configurations
├── TenantUsageLimiter.ts             // Usage limits per tenant
├── TenantBrandVoice.ts               // Brand voice management
├── TenantAPIKeyManager.ts            // API key management per tenant
├── TenantIsolation.ts                // Data isolation utilities
└── types/
    └── TenantTypes.ts                 // Tenant-related types
```

## 🎯 **Task Priority & Timeline**

### **Week 1: Image Scraping System (HIGH PRIORITY)**
**Goal**: Solve 1000+ inactive products problem

**Files to Create**:
1. `src/services/ai/tools/image-scraping/PlaywrightImageScraper.ts`
2. `src/services/ai/tools/image-scraping/ImageQualityAssessor.ts`
3. `src/services/ai/tools/image-scraping/BulkImageProcessor.ts`
4. `src/services/ai/tools/image-scraping/SourceManager.ts`

**Integration Point**: 
- Replace calls in `src/components/admin/product-form/hooks/useProductAI.ts`
- Add bulk processing to admin products page

### **Week 2: Monitoring System (MEDIUM PRIORITY)**
**Goal**: Track AI costs and performance

**Files to Create**:
1. `src/services/ai/monitoring/UsageTracker.ts`
2. `src/services/ai/monitoring/CostCalculator.ts`
3. `src/services/ai/monitoring/PerformanceMonitor.ts`

**Integration Point**:
- Hook into UnifiedAIService (I'll provide interface)
- Add monitoring dashboard to admin area

### **Week 3: Caching Layer (MEDIUM PRIORITY)**
**Goal**: 50% faster AI responses

**Files to Create**:
1. `src/services/ai/cache/AIResponseCache.ts`
2. `src/services/ai/cache/ImageCache.ts`
3. `src/services/ai/cache/CacheManager.ts`

**Integration Point**:
- Integrate with UnifiedAIService (I'll provide hooks)
- Cache image search results

### **Week 4: Multi-Tenant Prep (LOW PRIORITY)**
**Goal**: Prepare for SaaS conversion

**Files to Create**:
1. `src/services/ai/tenant/TenantConfigManager.ts`
2. `src/services/ai/tenant/TenantUsageLimiter.ts`
3. `src/services/ai/tenant/TenantBrandVoice.ts`

## 🤝 **Coordination Interfaces**

### **Image Scraping Interface** (for integration with my AI core):
```typescript
// src/services/ai/tools/image-scraping/types/ImageScrapingTypes.ts
export interface ImageScrapingService {
  findProductImages(product: Product, options?: ImageSearchOptions): Promise<ProductImage[]>;
  bulkProcessProducts(products: Product[]): Promise<BulkProcessingReport>;
  validateImageQuality(imageUrl: string): Promise<ImageQualityScore>;
}
```

### **Monitoring Interface** (for integration with my AI core):
```typescript
// src/services/ai/monitoring/types/MonitoringTypes.ts
export interface MonitoringService {
  trackRequest(request: AIRequest, response: AIResponse): Promise<void>;
  getCostEstimate(request: AIRequest): Promise<number>;
  getUsageStats(timeframe: string): Promise<UsageStats>;
}
```

## 🚀 **Git Branch Strategy**

### **Separate Branches for Each System**:
```bash
# Image scraping work
git checkout -b feature/image-scraping-system

# Monitoring work  
git checkout -b feature/ai-monitoring-system

# Caching work
git checkout -b feature/ai-caching-layer

# Multi-tenant work
git checkout -b feature/multi-tenant-infrastructure
```

## 📋 **Detailed Task Brief for Week 1**

### **🎯 MCP Playwright Image Scraper**

**Objective**: Replace expensive Google Image Search with free, targeted scraping

**Requirements**:
1. **Target Specific Retailers**: Cannabis/CBD/vaping product sites
2. **Quality Assessment**: Rank images by relevance and quality
3. **Bulk Processing**: Handle 1000+ products efficiently
4. **Integration**: Work with existing product form

**Success Criteria**:
- ✅ 90%+ success rate finding relevant images
- ✅ £0 API costs (vs current £5+ Google costs)
- ✅ Process 1000+ products in 2-3 hours
- ✅ Seamless integration with existing useProductAI hook

**Technical Approach**:
1. Use Playwright MCP Server for web automation
2. Target retailer search pages with product queries
3. Extract product images using CSS selectors
4. Assess image quality and relevance
5. Return top 5 quality images per product

## 🤔 **Questions for Second Agent**

1. **Preferred Communication**: How should we coordinate? (Comments in code, shared docs, etc.)
2. **MCP Experience**: Familiar with Model Context Protocol and Playwright?
3. **Timeline**: Comfortable with Week 1 focus on image scraping?
4. **Integration**: Need any specific interfaces from my AI core work?

## 🎯 **Ready to Start!**

This structure ensures:
- ✅ **No file conflicts** (separate directories)
- ✅ **Clear ownership** (tools vs core AI)
- ✅ **Easy integration** (defined interfaces)
- ✅ **Parallel development** (independent work streams)

The second agent can start immediately on the image scraping system while I build the AI core! 🚀
