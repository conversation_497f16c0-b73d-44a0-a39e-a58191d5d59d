// This script applies the increment_blog_view function migration using a direct SQL query
import { createClient } from '@supabase/supabase-js';

// Use the Supabase credentials from project_configuration.md
const supabaseUrl = 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBranlqdWFpb2tyaGdidXRqaGxhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMjI4ODAsImV4cCI6MjA2Mjc5ODg4MH0.o06YMkl4sHP0sBL6cDgEZlf1akuFCx_G39zjMQuQlwQ';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// The SQL for the increment_blog_view function
const sql = `
CREATE OR REPLACE FUNCTION increment_blog_view(blog_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE blogs
  SET view_count = view_count + 1
  WHERE id = blog_id;
END;
$$ LANGUAGE plpgsql;
`;

async function applyMigration() {
  try {
    console.log('Testing database connection...');
    
    // First test if we can connect to the database
    const { data: testData, error: testError } = await supabase.from('blogs').select('id').limit(1);
    
    if (testError) {
      console.error('Error connecting to database:', testError);
      return;
    }
    
    console.log('Successfully connected to database');
    
    // Since we can't directly execute SQL through the regular client,
    // we'll use the Supabase dashboard to apply the migration
    console.log('\nMIGRATION INSTRUCTIONS:');
    console.log('======================');
    console.log('1. Log in to the Supabase dashboard at https://app.supabase.com');
    console.log('2. Select your project: Bits N Bongs');
    console.log('3. Go to the SQL Editor');
    console.log('4. Create a new query');
    console.log('5. Paste the following SQL:');
    console.log('\n' + sql);
    console.log('\n6. Click "Run" to execute the query');
    console.log('7. Verify the function was created by testing it in your application');
    
    // Test if the function exists after manual creation
    console.log('\nAfter applying the migration manually, run this script again to verify the function exists.');
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Apply the migration
applyMigration();
