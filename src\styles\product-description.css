/* Rich Text Editor Styles */
.editor-container {
  min-height: 300px;
}

.editor-container .ql-editor {
  min-height: 250px;
  font-size: 1rem;
  line-height: 1.6;
  padding: 1rem;
  background-color: #fafafa;
  transition: background-color 0.2s;
}

.editor-container .ql-editor:focus {
  background-color: #ffffff;
  outline: none;
}

.editor-container .ql-toolbar {
  border-bottom: 1px solid #e2e8f0;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}

.editor-container .ql-toolbar button {
  margin: 0 0.25rem;
}

.editor-container .ql-toolbar button:hover {
  color: #4b6043;
}

.editor-container .ql-toolbar .ql-active {
  color: #4b6043;
}

.editor-container .ql-editor.ql-blank::before {
  font-style: italic;
  color: #9ca3af;
}

/* Product Description Rich Text Styles */
.product-description {
  font-family: inherit;
  line-height: 1.6;
}

.product-description h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.product-description h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #444;
}

.product-description h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.6rem;
  color: #555;
}

.product-description p {
  margin-bottom: 1rem;
}

.product-description ul, 
.product-description ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.product-description ul {
  list-style-type: disc;
}

.product-description ol {
  list-style-type: decimal;
}

.product-description li {
  margin-bottom: 0.5rem;
}

.product-description a {
  color: #4b6043;
  text-decoration: underline;
  transition: color 0.2s;
}

.product-description a:hover {
  color: #5f7a54;
}

.product-description blockquote {
  border-left: 3px solid #ddd;
  padding-left: 1rem;
  margin-left: 0;
  color: #666;
  font-style: italic;
}

.product-description img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

.product-description table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.product-description table th,
.product-description table td {
  border: 1px solid #ddd;
  padding: 0.5rem;
}

.product-description table th {
  background-color: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .product-description h1 {
    font-size: 1.5rem;
  }
  
  .product-description h2 {
    font-size: 1.3rem;
  }
  
  .product-description h3 {
    font-size: 1.1rem;
  }
}
