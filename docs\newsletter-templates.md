# Newsletter Template System

## Overview

The newsletter template system provides professional, branded email templates for Bits N Bongs newsletters. The system includes multiple template designs with responsive layouts, brand colors, and product integration.

## Features

### 🎨 **Professional Design**
- Branded header with logo and company colors
- Responsive design that works on all devices
- Professional typography and spacing
- Email-safe CSS and HTML

### 🌿 **Brand Integration**
- Bits N Bongs green color scheme (#2D5016, #4A7C59, #8FBC8F)
- Company logo and branding
- Contact information and social links
- Professional footer with unsubscribe options

### 🛍️ **Product Showcase**
- Automatic product grid layout
- Product images, names, and pricing
- Sale price highlighting
- Call-to-action buttons

### 📱 **Mobile Responsive**
- Optimized for mobile devices
- Flexible grid layouts
- Readable typography on small screens

## Available Templates

### 1. Classic Newsletter (`classic`)
**Description**: Clean, professional layout with header, content sections, and product showcase

**Features**:
- Traditional newsletter layout
- Branded header with gradient background
- Product grid with hover effects
- Professional footer with contact info
- Social media links

**Best For**: Regular newsletters, product announcements, general updates

### 2. Modern Newsletter (`modern`)
**Description**: Contemporary design with bold typography and card-based layout

**Features**:
- Large hero section with gradient background
- Card-based product display
- Prominent call-to-action buttons
- Modern typography and spacing
- Emoji integration for visual appeal

**Best For**: Product launches, special promotions, modern brand image

### 3. Minimal Newsletter (`minimal`)
**Description**: Clean, text-focused design with subtle branding

**Features**:
- Simple header design
- Typography-focused content
- Minimal product display (list format)
- Clean, uncluttered layout
- Subtle branding

**Best For**: Educational content, simple announcements, text-heavy newsletters

## Usage

### In Newsletter Editor

1. **Select Template**: Choose from the template dropdown in the newsletter editor
2. **Add Content**: Write your newsletter content in markdown
3. **Add Products**: Use the "Add Product Image" button to include products
4. **Preview**: See real-time preview with your selected template
5. **Send**: Send the professionally formatted newsletter

### Template Data Structure

```typescript
interface NewsletterTemplateData {
  subject: string;           // Newsletter subject line
  content: string;          // Main content (markdown)
  products?: Array<{        // Optional featured products
    id: string;
    name: string;
    price: number;
    sale_price?: number;
    image: string;
  }>;
  unsubscribeUrl?: string;  // Unsubscribe link
}
```

### Programmatic Usage

```typescript
import { generateNewsletterFromTemplate } from '@/lib/newsletter-templates';

const templateData = {
  subject: 'Weekly Update',
  content: 'Your newsletter content here...',
  products: [/* product array */],
  unsubscribeUrl: 'https://bitsnbongs.com/unsubscribe'
};

const html = generateNewsletterFromTemplate('classic', templateData);
```

## Customization

### Brand Colors
The templates use a consistent color scheme:
- **Primary**: #2D5016 (Dark Green)
- **Secondary**: #4A7C59 (Medium Green)  
- **Accent**: #8FBC8F (Light Green)
- **Background**: #F8F9FA
- **Text**: #333333

### Adding New Templates

1. Create a new template object in `src/lib/newsletter-templates.ts`
2. Define the template structure and styling
3. Add to the `newsletterTemplates` array
4. Test with sample data

### Email Compatibility

Templates are designed for maximum email client compatibility:
- Inline CSS for better support
- Table-based layouts where needed
- Web-safe fonts with fallbacks
- Optimized image handling

## Testing

### Template Preview
Visit `/test-newsletter-templates` to preview all templates with sample data:
- Interactive template selection
- Live preview with sample content
- Open in new window for full testing
- Sample product integration

### Sample Data
The test page includes realistic sample data:
- Newsletter content about CBD products
- Featured products with images and pricing
- Branded header and footer
- Responsive design testing

## Best Practices

### Content Guidelines
- Keep subject lines under 50 characters
- Use clear, engaging headlines
- Include call-to-action buttons
- Balance text and visual content

### Product Integration
- Limit to 3-6 featured products per newsletter
- Use high-quality product images
- Include clear pricing information
- Add compelling product descriptions

### Design Consistency
- Stick to brand colors and fonts
- Maintain consistent spacing
- Use professional imagery
- Keep layouts clean and uncluttered

## Technical Details

### File Structure
```
src/lib/newsletter-templates.ts    # Template definitions
src/pages/TestNewsletterTemplates.tsx  # Test page
docs/newsletter-templates.md      # This documentation
```

### Dependencies
- React for component structure
- Tailwind CSS for styling
- Email-safe CSS practices
- Responsive design principles

### Performance
- Optimized HTML output
- Compressed CSS
- Efficient image handling
- Fast rendering in email clients

## Future Enhancements

### Planned Features
- Additional template designs
- Custom color scheme options
- Advanced product layouts
- A/B testing capabilities
- Template analytics

### Integration Opportunities
- CRM system integration
- Advanced personalization
- Dynamic content blocks
- Automated product recommendations
