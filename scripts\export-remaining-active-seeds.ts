#!/usr/bin/env tsx
/**
 * Export the remaining 10 active seed products for Agent #1 to complete
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function exportRemainingActiveSeeds() {
  console.log('🎯 Finding remaining active seed products for Agent #1 final mission...\n');
  
  try {
    // Get all active products
    const { data: products, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        is_active,
        description,
        image,
        sku
      `)
      .eq('is_active', true);
    
    if (error) {
      console.error('❌ Error fetching products:', error);
      return;
    }
    
    // Filter to seed products
    const seedProducts = products?.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('seed') || name.includes('auto') || 
             name.includes('feminised') || name.includes('feminized') ||
             name.includes('female') || name.includes('strain');
    }) || [];
    
    console.log(`📄 Found ${seedProducts.length} total active seed products\n`);
    
    // Products that Agent #1 has already enriched (based on the batches we've seen)
    const enrichedProducts = [
      // Batch 1
      'Sorbet Dreams 6 female',
      'Mendocino Purple Kush 5 Female',
      
      // Batch 2
      'Royal Queen of Seeds Green Gelato',
      'Royal Queen of Seeds Fat Banana', 
      'Royal Queen of Seeds Haze Berry',
      'Royal Queen of Seeds Chocolate Haze',
      'Royal Queen of Seeds Blue Mystic',
      'Royal Queen of Seeds Queen Ice',
      'Royal Queen of Seeds Royal Highness CBD',
      
      // Batch 3
      'Perfect Tree Seeds: Pink Gasoline',
      'Barney\'s Farm Runtz Muffin',
      'Humboldt Seed Company Blueberry Muffin',
      
      // Final Batch
      'Barneys Farm Peyote Critical',
      'Barneys Farm Mimosa X Orange Punch',
      'Barneys Farm Biscotti Mintz',
      'Barneys Farm Blue Sunset Sherbert',
      'Barneys Farm Wedding Cake',
      'Barneys Farm Dos Si Dos #33',
      'Barneys Farm Gorilla Zkittlez',
      'Barneys Farm Purple Punch',
      'Barneys Farm Strawberry Lemonade',
      'Barneys Farm Cookies Kush'
    ];
    
    // Find products that haven't been enriched yet
    const remainingProducts = seedProducts.filter(product => {
      return !enrichedProducts.some(enriched => 
        product.name.toLowerCase().includes(enriched.toLowerCase()) ||
        enriched.toLowerCase().includes(product.name.toLowerCase())
      );
    });
    
    console.log(`🎯 Found ${remainingProducts.length} remaining active products to enrich:\n`);
    
    // Sort by price (highest first) for priority
    remainingProducts.sort((a, b) => (b.price || 0) - (a.price || 0));
    
    // Show the list
    remainingProducts.forEach((product, index) => {
      const missingFields = [];
      if (!product.description || product.description.trim().length < 50) {
        missingFields.push('description');
      }
      if (!product.image) {
        missingFields.push('image');
      }
      if (!product.sku || product.sku.trim().length === 0) {
        missingFields.push('sku');
      }
      
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   💰 £${product.price || 0} | Missing: ${missingFields.join(', ') || 'None'}`);
      console.log('');
    });
    
    // Create CSV for Agent #1
    const csvContent = [
      'product_name,price,current_description_length,missing_fields,priority_notes',
      ...remainingProducts.map(p => {
        const missingFields = [];
        if (!p.description || p.description.trim().length < 50) missingFields.push('description');
        if (!p.image) missingFields.push('image');
        if (!p.sku || p.sku.trim().length === 0) missingFields.push('sku');
        
        const priorityNote = p.price && p.price > 50 ? 'HIGH - Premium pricing' : 
                           p.price && p.price > 30 ? 'MEDIUM - Good value' : 'LOW - Budget option';
        
        return `"${p.name}",${p.price || 0},${p.description?.length || 0},"${missingFields.join('; ')}","${priorityNote}"`;
      })
    ].join('\n');
    
    writeFileSync('docs/super_agent/remaining_active_seeds_for_agent1.csv', csvContent);
    
    console.log('📊 MISSION SUMMARY FOR AGENT #1:');
    console.log(`   🎯 Target: ${remainingProducts.length} remaining active products`);
    console.log(`   💰 Total value: £${remainingProducts.reduce((sum, p) => sum + (p.price || 0), 0)}`);
    console.log(`   🏆 Goal: 100% completion of active seed catalog`);
    console.log(`   📁 File: docs/super_agent/remaining_active_seeds_for_agent1.csv`);
    
    console.log('\n🚀 READY FOR AGENT #1 FINAL MISSION!');
    console.log('   Send him the CSV file with the completion prompt');
    console.log('   This will achieve 100% active product coverage');
    
  } catch (err) {
    console.error('❌ Export failed:', err);
  }
}

// Run the export
exportRemainingActiveSeeds().catch(console.error);
