// Download product images from URLs provided by Super Agent
// This script downloads images and uploads them to Supabase storage

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import csv from 'csv-parser';
import fetch from 'node-fetch';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface ImageDownloadRecord {
  product_id: string;
  product_name: string;
  image_download_url?: string;
  enriched_image_url?: string;
}

// Function to download image from URL
async function downloadImage(url: string): Promise<Buffer> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.statusText}`);
  }
  return Buffer.from(await response.arrayBuffer());
}

// Function to get file extension from URL or content type
function getFileExtension(url: string, contentType?: string): string {
  // Try to get extension from URL
  const urlExt = path.extname(new URL(url).pathname).toLowerCase();
  if (urlExt && ['.jpg', '.jpeg', '.png', '.webp'].includes(urlExt)) {
    return urlExt;
  }

  // Fallback to content type
  if (contentType) {
    if (contentType.includes('jpeg')) return '.jpg';
    if (contentType.includes('png')) return '.png';
    if (contentType.includes('webp')) return '.webp';
  }

  // Default fallback
  return '.jpg';
}

// Function to generate safe filename
function generateSafeFilename(productName: string, productId: string, extension: string): string {
  // Clean product name for filename
  const safeName = productName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .substring(0, 50); // Limit length

  return `${safeName}-${productId.substring(0, 8)}${extension}`;
}

async function downloadProductImages(csvFilePath: string) {
  try {
    console.log('📸 Starting product image download process...');

    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV file not found: ${csvFilePath}`);
    }

    // Read and parse CSV
    const imageRecords: ImageDownloadRecord[] = [];

    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          // Only process records that have image download URLs
          if (row.image_download_url && row.image_download_url.trim()) {
            imageRecords.push(row);
          }
        })
        .on('end', () => {
          console.log(`📊 Found ${imageRecords.length} products with image download URLs`);
          resolve();
        })
        .on('error', reject);
    });

    let downloadedCount = 0;
    let uploadedCount = 0;
    let updatedCount = 0;
    let errors = 0;

    // Create temp directory for downloads
    const tempDir = path.join(__dirname, '../../temp/images');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Process each image record
    for (const record of imageRecords) {
      try {
        console.log(`\n🔄 Processing: ${record.product_name}`);

        const downloadUrl = record.image_download_url!.trim();

        // Download the image
        console.log(`📥 Downloading from: ${downloadUrl}`);
        const imageBuffer = await downloadImage(downloadUrl);
        downloadedCount++;

        // Determine file extension
        const response = await fetch(downloadUrl, { method: 'HEAD' });
        const contentType = response.headers.get('content-type');
        const extension = getFileExtension(downloadUrl, contentType || undefined);

        // Generate filename
        const filename = generateSafeFilename(record.product_name, record.product_id, extension);
        const tempFilePath = path.join(tempDir, filename);

        // Save temporarily
        fs.writeFileSync(tempFilePath, imageBuffer);

        // Upload to Supabase storage (using existing path structure)
        console.log(`☁️ Uploading to Supabase storage...`);
        const storagePath = `product-images/${filename}`; // Match existing double path structure
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('product-images')
          .upload(storagePath, imageBuffer, {
            contentType: contentType || 'image/jpeg',
            upsert: true
          });

        if (uploadError) {
          throw new Error(`Upload failed: ${uploadError.message}`);
        }

        uploadedCount++;

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('product-images')
          .getPublicUrl(storagePath);

        const publicUrl = urlData.publicUrl;

        // Update product record with new image URL
        const { error: updateError } = await supabase
          .from('products')
          .update({ image: publicUrl })
          .eq('id', record.product_id);

        if (updateError) {
          throw new Error(`Database update failed: ${updateError.message}`);
        }

        updatedCount++;

        // Clean up temp file
        fs.unlinkSync(tempFilePath);

        console.log(`✅ Successfully processed: ${record.product_name}`);
        console.log(`   Image URL: ${publicUrl}`);

      } catch (error) {
        console.error(`❌ Error processing ${record.product_name}:`, error);
        errors++;
      }
    }

    // Clean up temp directory
    try {
      fs.rmSync(tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
      console.warn('⚠️ Could not clean up temp directory:', cleanupError);
    }

    // Print summary
    console.log('\n✅ Image download process completed!');
    console.log('📊 Summary:');
    console.log(`   Total records processed: ${imageRecords.length}`);
    console.log(`   Images downloaded: ${downloadedCount}`);
    console.log(`   Images uploaded: ${uploadedCount}`);
    console.log(`   Products updated: ${updatedCount}`);
    console.log(`   Errors: ${errors}`);

    if (errors > 0) {
      console.log('\n⚠️ Some errors occurred during processing. Please check the logs above.');
    } else {
      console.log('\n🎉 All images processed successfully!');
    }

  } catch (error) {
    console.error('❌ Image download process failed:', error);
    process.exit(1);
  }
}

// Get CSV file path from command line argument
const csvFilePath = process.argv[2];

if (!csvFilePath) {
  console.error('❌ Please provide the path to the enriched CSV file');
  console.log('Usage: npm run download-product-images path/to/enriched-seeds.csv');
  process.exit(1);
}

// Run the download process
downloadProductImages(csvFilePath);
