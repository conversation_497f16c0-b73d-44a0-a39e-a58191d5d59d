'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle } from 'lucide-react';
import { getPaymentService } from '@/services/paymentService';
import { useCart } from '@/context/CartContext';
import { useToast } from '@/components/ui/use-toast';

interface PayPalCheckoutProps {
  shippingCost: number;
  taxAmount: number;
  onSuccess: (orderId: string) => void;
  onCancel: () => void;
  disabled?: boolean;
}

export function PayPalCheckout({
  shippingCost,
  taxAmount,
  onSuccess,
  onCancel,
  disabled = false
}: PayPalCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { cartItems } = useCart();
  const { toast } = useToast();
  const paymentService = getPaymentService();

  const handlePayPalCheckout = async () => {
    if (disabled || cartItems.length === 0) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Create PayPal payment session
      const { checkoutUrl, sessionId } = await paymentService.createPayPalPayment(
        cartItems,
        shippingCost,
        taxAmount
      );
      
      // Store the session ID in localStorage for retrieval after redirect
      localStorage.setItem('paypal_session_id', sessionId);
      
      // Redirect to PayPal checkout
      window.location.href = checkoutUrl;
    } catch (err) {
      console.error('PayPal checkout error:', err);
      setError('Unable to initialize PayPal checkout. Please try again.');
      toast({
        title: 'Payment Error',
        description: 'There was a problem initializing PayPal checkout.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check for returning PayPal payment
  useEffect(() => {
    const checkPayPalReturn = async () => {
      // Check if we're returning from PayPal
      const urlParams = new URLSearchParams(window.location.search);
      const paypalSessionId = localStorage.getItem('paypal_session_id');
      const paymentStatus = urlParams.get('payment_status');
      const paypalTransactionId = urlParams.get('tx');
      
      if (paypalSessionId && paymentStatus === 'Completed' && paypalTransactionId) {
        setIsLoading(true);
        try {
          // Verify the payment with PayPal
          const isVerified = await paymentService.verifyPayPalPayment(
            paypalSessionId,
            paypalTransactionId
          );
          
          if (isVerified) {
            // Process the payment and create order
            const orderId = await paymentService.processSuccessfulPayment(paypalSessionId);
            
            // Clear the session ID
            localStorage.removeItem('paypal_session_id');
            
            // Call the success callback
            onSuccess(orderId);
            
            toast({
              title: 'Payment Successful',
              description: 'Your PayPal payment has been processed successfully.',
            });
          } else {
            throw new Error('Payment verification failed');
          }
        } catch (err) {
          console.error('PayPal verification error:', err);
          setError('Payment verification failed. Please contact customer support.');
          toast({
            title: 'Payment Verification Failed',
            description: 'We could not verify your payment. Please contact customer support.',
            variant: 'destructive',
          });
        } finally {
          setIsLoading(false);
        }
      } else if (paypalSessionId && urlParams.get('cancel') === 'true') {
        // Payment was canceled
        localStorage.removeItem('paypal_session_id');
        onCancel();
        toast({
          title: 'Payment Canceled',
          description: 'You have canceled the PayPal payment process.',
        });
      }
    };
    
    checkPayPalReturn();
  }, []);

  return (
    <div className="w-full">
      {error && (
        <div className="flex items-center gap-2 p-3 mb-4 text-sm bg-red-50 border border-red-200 rounded-md text-red-600">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}
      
      <Button
        onClick={handlePayPalCheckout}
        disabled={disabled || isLoading || cartItems.length === 0}
        className="w-full bg-[#0070ba] hover:bg-[#003087] text-white font-semibold py-2 px-4 rounded"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          <>Pay with PayPal</>
        )}
      </Button>
      
      <p className="text-xs text-gray-500 mt-2 text-center">
        By clicking this button, you will be redirected to PayPal to complete your purchase securely.
      </p>
    </div>
  );
}