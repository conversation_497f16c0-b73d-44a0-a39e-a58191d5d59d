import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON> } from "@/types/supabase";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useAuth } from "@/hooks/auth.basic";
import { Loader2, AlertCircle } from "lucide-react";
import { <PERSON><PERSON>, AlertDescription, Al<PERSON>Title } from "@/components/ui/alert";

interface StoreSettings {
  store_name: string;
  store_email: string;
  store_phone: string;
  store_address: string;
  currency: string;
  tax_rate: number;
  enable_guest_checkout: boolean;
  enable_reviews: boolean;
  maintenance_mode: boolean;
}

interface ProfileSettings {
  first_name: string;
  last_name: string;
  email: string; // This will be read-only
  address: string;
  city: string;
  postal_code: string;
  country: string;
}

export default function SettingsPage() {
  const { user, profile } = useAuth();
  
  // Create a custom profile update function since it's not in our simplified auth hook
  const updateProfile = async (profileData: Partial<ProfileSettings>) => {
    if (!user) return { error: new Error('No user logged in') };
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.id);
        
      if (error) throw error;
      return { data };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error };
    }
  };
  const [activeTab, setActiveTab] = useState("store");
  
  // Store settings form
  const {
    register: registerStore,
    handleSubmit: handleStoreSubmit,
    formState: { errors: storeErrors, isSubmitting: isStoreSubmitting },
    reset: resetStoreForm,
    setValue: setStoreValue,
  } = useForm<StoreSettings>({
    defaultValues: {
      store_name: "Bits N Bongs",
      store_email: "",
      store_phone: "",
      store_address: "",
      currency: "GBP",
      tax_rate: 20,
      enable_guest_checkout: true,
      enable_reviews: true,
      maintenance_mode: false,
    }
  });

  // Profile settings form
  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    formState: { errors: profileErrors, isSubmitting: isProfileSubmitting },
    reset: resetProfileForm,
  } = useForm<ProfileSettings>({
    defaultValues: {
      first_name: profile?.first_name || "",
      last_name: profile?.last_name || "",
      email: user?.email || "",
      address: profile?.address || "",
      city: profile?.city || "",
      postal_code: profile?.postal_code || "",
      country: profile?.country || "",
    },
  });

  // Default store settings
  const defaultSettings: StoreSettings = {
    store_name: "Bits N Bongs",
    store_email: "",
    store_phone: "",
    store_address: "",
    currency: "GBP",
    tax_rate: 20,
    enable_guest_checkout: true,
    enable_reviews: true,
    maintenance_mode: false,
  };

  // Fetch store settings
  const { 
    data: storeSettings, 
    isLoading: isLoadingSettings, 
    error: settingsError, 
    refetch: refetchSettings 
  } = useQuery({
    queryKey: ["storeSettings"],
    queryFn: async () => {
      try {
        // Use a direct SQL query to get the settings
        const { data, error } = await supabase
          .from('settings')
          .select('*')
          .eq('id', 1)
          .single();
        
        if (error) {
          console.error("Error fetching store settings:", error);
          return defaultSettings;
        }
        
        // If we have data, parse it, otherwise return defaults
        if (data && data.settings) {
          try {
            const parsedSettings = JSON.parse(data.settings);
            return {
              ...defaultSettings,
              ...parsedSettings
            };
          } catch (parseError) {
            console.error("Error parsing settings JSON:", parseError);
            return defaultSettings;
          }
        } else {
          return defaultSettings;
        }
      } catch (error) {
        console.error("Error in fetchSettings:", error);
        return defaultSettings;
      }
    },
  });

  // Update form when settings are loaded
  useEffect(() => {
    if (storeSettings) {
      resetStoreForm(storeSettings);
    }
  }, [storeSettings, resetStoreForm]);

  // Update profile form when profile changes
  useEffect(() => {
    if (profile && user) {
      resetProfileForm({
        first_name: profile.first_name || "",
        last_name: profile.last_name || "",
        email: user.email || "",
        address: profile.address || "",
        city: profile.city || "",
        postal_code: profile.postal_code || "",
        country: profile.country || "",
      });
    }
  }, [profile, user, resetProfileForm]);

  // Mutation for saving store settings
  const saveStoreSettingsMutation = useMutation({
    mutationFn: async (data: StoreSettings) => {
      try {
        // Check if settings already exist
        const { data: existingData, error: fetchError } = await supabase
          .from("settings")
          .select("*")
          .eq("key", "store_settings")
          .single();

        if (fetchError && fetchError.code !== "PGRST116") {
          throw fetchError;
        }

        // Convert StoreSettings to Json compatible format
        const jsonValue = data as unknown as Json;

        if (existingData) {
          // Update existing settings
          const { data: updateData, error: updateError } = await supabase
            .from("settings")
            .update({ value: jsonValue })
            .eq("key", "store_settings")
            .select();

          if (updateError) throw updateError;
          return updateData;
        } else {
          // Insert new settings
          const { data: insertData, error: insertError } = await supabase
            .from("settings")
            .insert({ key: "store_settings", value: jsonValue })
            .select();

          if (insertError) throw insertError;
          return insertData;
        }
      } catch (error) {
        console.error("Error saving store settings:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: "Settings updated",
        description: "Your store settings have been saved successfully.",
      });
      refetchSettings();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation for updating profile
  const updateUserProfile = useMutation({
    mutationFn: async (data: ProfileSettings) => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      
      const { error } = await supabase
        .from("profiles")
        .update({
          first_name: data.first_name,
          last_name: data.last_name,
          address: data.address,
          city: data.city,
          postal_code: data.postal_code,
          country: data.country,
          updated_at: new Date().toISOString()
        })
        .eq("id", user.id);
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (updateProfile) {
        updateProfile({
          first_name: data.first_name,
          last_name: data.last_name,
          address: data.address,
          city: data.city,
          postal_code: data.postal_code,
          country: data.country,
        });
      }
      
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update profile: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Handle form submissions
  const onStoreSubmit = (data: StoreSettings) => {
    saveStoreSettingsMutation.mutate(data);
  };

  const onProfileSubmit = (data: ProfileSettings) => {
    updateUserProfile.mutate(data);
  };

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your store and account settings.
        </p>
      </div>

      {settingsError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was a problem loading your settings. Please try again later.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="store">Store Settings</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
        </TabsList>
        
        <TabsContent value="store" className="space-y-4">
          <Card>
            <form onSubmit={handleStoreSubmit(onStoreSubmit)}>
              <CardHeader>
                <CardTitle>Store Settings</CardTitle>
                <CardDescription>
                  Configure your store's general settings and preferences.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Store Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="store_name">Store Name</Label>
                      <Input
                        id="store_name"
                        {...registerStore("store_name", { required: "Store name is required" })}
                      />
                      {storeErrors.store_name && (
                        <p className="text-sm text-red-500">{storeErrors.store_name.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="currency">Currency</Label>
                      <Input
                        id="currency"
                        {...registerStore("currency")}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="store_email">Store Email</Label>
                      <Input
                        id="store_email"
                        type="email"
                        {...registerStore("store_email")}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="store_phone">Store Phone</Label>
                      <Input
                        id="store_phone"
                        {...registerStore("store_phone")}
                      />
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="store_address">Store Address</Label>
                      <Input
                        id="store_address"
                        {...registerStore("store_address")}
                      />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Tax Settings</h3>
                  <div className="space-y-2">
                    <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                    <Input
                      id="tax_rate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      {...registerStore("tax_rate", {
                        valueAsNumber: true,
                      })}
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Store Preferences</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="enable_guest_checkout">Enable Guest Checkout</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow customers to checkout without creating an account.
                        </p>
                      </div>
                      <Switch
                        id="enable_guest_checkout"
                        checked={Boolean(storeSettings?.enable_guest_checkout)}
                        onCheckedChange={(checked) => {
                          setStoreValue("enable_guest_checkout", checked);
                        }}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="enable_reviews">Enable Product Reviews</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow customers to leave reviews on products.
                        </p>
                      </div>
                      <Switch
                        id="enable_reviews"
                        checked={Boolean(storeSettings?.enable_reviews)}
                        onCheckedChange={(checked) => {
                          setStoreValue("enable_reviews", checked);
                        }}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="maintenance_mode" className="text-red-500 font-medium">Maintenance Mode</Label>
                        <p className="text-sm text-muted-foreground">
                          Put your store in maintenance mode. Only admins can access the site.
                        </p>
                      </div>
                      <Switch
                        id="maintenance_mode"
                        checked={Boolean(storeSettings?.maintenance_mode)}
                        onCheckedChange={(checked) => {
                          setStoreValue("maintenance_mode", checked);
                        }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  disabled={isStoreSubmitting}
                  className="ml-auto"
                >
                  {isStoreSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
        
        <TabsContent value="profile" className="space-y-4">
          <Card>
            <form onSubmit={handleProfileSubmit(onProfileSubmit)}>
              <CardHeader>
                <CardTitle>Admin Profile</CardTitle>
                <CardDescription>
                  Update your personal information and account settings.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="first_name">First Name</Label>
                    <Input
                      id="first_name"
                      {...registerProfile("first_name")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="last_name">Last Name</Label>
                    <Input
                      id="last_name"
                      {...registerProfile("last_name")}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...registerProfile("email")}
                    disabled
                  />
                  <p className="text-xs text-muted-foreground">
                    Email cannot be changed. Contact support for assistance.
                  </p>
                </div>

                <Separator className="my-4" />

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    {...registerProfile("address")}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...registerProfile("city")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postal_code">Postal Code</Label>
                    <Input
                      id="postal_code"
                      {...registerProfile("postal_code")}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    {...registerProfile("country")}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  disabled={isProfileSubmitting}
                  className="ml-auto"
                >
                  {isProfileSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Update Profile"
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}