// Script to apply database migrations
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in environment variables');
  console.log('Available environment variables:', Object.keys(process.env).filter(key => key.includes('VITE')));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  try {
    console.log('Applying migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../migrations/20250519_product_variants.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .replace(/\/\*[\s\S]*?\*\/|--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (const [index, statement] of statements.entries()) {
      console.log(`Executing statement ${index + 1}/${statements.length}...`);
      
      const { error } = await supabase.rpc('postgres_execute', { 
        query: statement 
      });
      
      if (error) {
        // If postgres_execute is not available, try a direct query
        console.log('postgres_execute not available, trying direct query...');
        const { error: directError } = await supabase.from('_migrations').insert({
          name: `statement_${index + 1}`,
          sql: statement
        });
        
        if (directError) {
          console.error(`Error executing statement ${index + 1}:`, directError);
          console.log('Statement:', statement);
          
          // Try another approach - create a SQL function
          console.log('Trying to create SQL function...');
          const { error: functionError } = await supabase.from('_functions').insert({
            name: `apply_migration_${Date.now()}`,
            definition: statement
          });
          
          if (functionError) {
            console.error('Error creating function:', functionError);
            console.log('Unfortunately, we cannot apply the migration automatically.');
            console.log('Please apply the migration manually through the Supabase dashboard SQL editor.');
            console.log('Migration file:', migrationPath);
            process.exit(1);
          }
        }
      }
    }
    
    console.log('Migration applied successfully');
  } catch (error) {
    console.error('Error applying migration:', error);
    console.log('Please apply the migration manually through the Supabase dashboard SQL editor.');
  }
}

// Run the function
applyMigration();
