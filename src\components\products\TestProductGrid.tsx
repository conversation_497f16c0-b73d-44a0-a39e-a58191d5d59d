import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import ProductCard from './ProductCard';

interface TestProductGridProps {
  categoryId?: string;
  subcategoryId?: string;
  searchQuery?: string;
}

const TestProductGrid: React.FC<TestProductGridProps> = ({ 
  categoryId,
  subcategoryId,
  searchQuery
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        setDebugInfo(`Starting fetch... Category: ${categoryId || 'all'}, Subcategory: ${subcategoryId || 'all'}, Search: ${searchQuery || 'none'}`);

        // Build query with filters
        let query = supabase
          .from('products')
          .select('id, name, price, description, image, category_id, subcategory_id, brand_id, slug, in_stock, is_featured, is_new, is_best_seller, sale_price, created_at, updated_at, rating, review_count')
          .order('name');

        // Apply filters
        if (categoryId) {
          query = query.eq('category_id', categoryId);
        }

        if (subcategoryId) {
          query = query.eq('subcategory_id', subcategoryId);
        }

        // Apply search filter if provided
        if (searchQuery && searchQuery.trim() !== '') {
          query = query.ilike('name', `%${searchQuery}%`);
        }

        // Execute query
        const { data, error } = await query;

        if (error) {
          console.error('Supabase error:', error);
          setError(`Database error: ${error.message}`);
          setDebugInfo(`Error: ${JSON.stringify(error)}`);
          setLoading(false);
          return;
        }

        setDebugInfo(`Fetched ${data?.length || 0} products`);
        console.log('Test component data:', data);

        if (!data || data.length === 0) {
          setProducts([]);
          setLoading(false);
          return;
        }

        // Set products
        setProducts(data);
        setLoading(false);
      } catch (err) {
        console.error('Exception in test component:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setDebugInfo(`Exception: ${err instanceof Error ? err.message : 'Unknown'}`);
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId, subcategoryId, searchQuery]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-20 gap-4">
        <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
        <p>Loading products...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-20 bg-red-50 rounded-lg">
        <h3 className="text-xl font-medium text-red-900">Error loading products</h3>
        <p className="mt-2 text-red-500">{error}</p>
        <div className="mt-4 p-4 bg-red-100 rounded text-left">
          <pre className="whitespace-pre-wrap text-xs">{debugInfo}</pre>
        </div>
      </div>
    );
  }

  return (
    <div className="py-4">
      
      {products.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p>No products found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map(product => (
            <ProductCard 
              key={product.id}
              product={{
                id: product.id,
                name: product.name,
                price: product.price,
                description: product.description,
                category_id: product.category_id,
                subcategory_id: product.subcategory_id,
                brand_id: product.brand_id,
                slug: product.slug,
                // Add required properties with default values
                in_stock: product.in_stock ?? true,
                is_featured: product.is_featured ?? false,
                is_new: product.is_new ?? false,
                is_best_seller: product.is_best_seller ?? false,
                sale_price: product.sale_price,
                image: product.image,
                created_at: product.created_at || new Date().toISOString(),
                updated_at: product.updated_at || new Date().toISOString(),
                rating: product.rating ?? 0,
                review_count: product.review_count ?? 0
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TestProductGrid;
