import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  slug: string;
  parent_id?: string | null;
  subcategories?: Category[];
}

interface MobileCategoriesProps {
  onNavigate: () => void;
}

const MobileCategories = ({ onNavigate }: MobileCategoriesProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        // First, fetch all categories
        const { data, error } = await supabase
          .from('categories')
          .select('id, name, slug, parent_id')
          .order('name');
        
        if (error) {
          throw error;
        }
        
        if (data) {
          // Organize categories into a hierarchy
          const mainCategories = data.filter(cat => !cat.parent_id);
          const subcategories = data.filter(cat => cat.parent_id);
          
          // Add subcategories to their parent categories
          const categoriesWithSubs = mainCategories.map(mainCat => {
            const subs = subcategories.filter(subCat => subCat.parent_id === mainCat.id);
            return {
              ...mainCat,
              subcategories: subs.length > 0 ? subs : undefined
            };
          });
          
          setCategories(categoriesWithSubs);
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        setError(err.message || 'Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <Loader2 className="h-5 w-5 animate-spin mr-2" />
        <span>Loading categories...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 py-2">
        Failed to load categories
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="py-2">
        No categories found
      </div>
    );
  }

  return (
    <>
      <Link 
        to="/shop" 
        className="block nav-link font-medium"
        onClick={onNavigate}
      >
        All Products
      </Link>
      {categories.map((category) => (
        <div key={category.id}>
          <Link 
            to={`/shop?category=${category.slug}`}
            className="block nav-link font-medium"
            onClick={onNavigate}
          >
            {category.name}
          </Link>
          
          {/* Show subcategories if available */}
          {category.subcategories && category.subcategories.length > 0 && (
            <div className="pl-4">
              {category.subcategories.map(subcat => (
                <Link 
                  key={subcat.id}
                  to={`/shop?category=${category.slug}&subcategory=${subcat.slug}`}
                  className="block nav-link text-sm"
                  onClick={onNavigate}
                >
                  {subcat.name}
                </Link>
              ))}
            </div>
          )}
        </div>
      ))}
    </>
  );
};

export default MobileCategories;
