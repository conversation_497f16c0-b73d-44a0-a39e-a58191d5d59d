// Multi-Tenant Utility Functions
// Provides helper functions for tenant management and context handling

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import {
  Tenant,
  TenantUser,
  UserTenant,
  TenantContext,
  TenantError,
  TenantAccessError,
  TenantNotFoundError,
  CreateTenantForm,
  UpdateTenantForm,
  InviteTenantUserForm,
  TenantStatistics,
  TenantIsolationTestResult,
  TenantPerformanceTestResult,
  RlsPolicyValidationResult,
  TenantDataDistribution
} from '../types/tenant';

/**
 * Multi-tenant Supabase client wrapper
 * Handles tenant context and provides tenant-aware database operations
 */
export class TenantSupabaseClient {
  private client: SupabaseClient;
  private currentTenantId: string | null = null;

  constructor(
    supabaseUrl: string = process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseKey: string = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  ) {
    this.client = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Set the current tenant context
   */
  async setTenantContext(tenantId: string): Promise<void> {
    try {
      const { error } = await this.client.rpc('set_tenant_context', {
        tenant_uuid: tenantId
      });
      
      if (error) throw new TenantError('Failed to set tenant context', 'CONTEXT_ERROR');
      
      this.currentTenantId = tenantId;
    } catch (error) {
      throw new TenantError(
        `Failed to set tenant context: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CONTEXT_ERROR'
      );
    }
  }

  /**
   * Get the current tenant ID
   */
  getCurrentTenantId(): string | null {
    return this.currentTenantId;
  }

  /**
   * Get the underlying Supabase client
   */
  getClient(): SupabaseClient {
    return this.client;
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenantData: CreateTenantForm, userId: string): Promise<Tenant> {
    try {
      // Insert tenant
      const { data: tenant, error: tenantError } = await this.client
        .from('tenants')
        .insert({
          name: tenantData.name,
          slug: tenantData.slug,
          domain: tenantData.domain,
          subdomain: tenantData.subdomain,
          plan_type: tenantData.plan_type,
          status: 'active'
        })
        .select()
        .single();

      if (tenantError) throw tenantError;

      // Add user as owner
      const { error: userError } = await this.client
        .from('tenant_users')
        .insert({
          tenant_id: tenant.id,
          user_id: userId,
          role: 'owner'
        });

      if (userError) {
        // Rollback tenant creation
        await this.client.from('tenants').delete().eq('id', tenant.id);
        throw userError;
      }

      return tenant;
    } catch (error) {
      throw new TenantError(
        `Failed to create tenant: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CREATE_TENANT_ERROR'
      );
    }
  }

  /**
   * Update tenant information
   */
  async updateTenant(tenantId: string, updates: UpdateTenantForm): Promise<Tenant> {
    try {
      await this.setTenantContext(tenantId);
      
      const { data, error } = await this.client
        .from('tenants')
        .update(updates)
        .eq('id', tenantId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new TenantError(
        `Failed to update tenant: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UPDATE_TENANT_ERROR',
        tenantId
      );
    }
  }

  /**
   * Get user's tenants
   */
  async getUserTenants(userId: string): Promise<UserTenant[]> {
    try {
      const { data, error } = await this.client.rpc('get_user_tenants', {
        user_uuid: userId
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to get user tenants: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GET_TENANTS_ERROR'
      );
    }
  }

  /**
   * Get tenant by ID
   */
  async getTenant(tenantId: string): Promise<Tenant> {
    try {
      const { data, error } = await this.client
        .from('tenants')
        .select('*')
        .eq('id', tenantId)
        .single();

      if (error) throw error;
      if (!data) throw new TenantNotFoundError(tenantId);
      
      return data;
    } catch (error) {
      if (error instanceof TenantNotFoundError) throw error;
      throw new TenantError(
        `Failed to get tenant: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GET_TENANT_ERROR',
        tenantId
      );
    }
  }

  /**
   * Get tenant by slug
   */
  async getTenantBySlug(slug: string): Promise<Tenant> {
    try {
      const { data, error } = await this.client
        .from('tenants')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) throw error;
      if (!data) throw new TenantNotFoundError(slug);
      
      return data;
    } catch (error) {
      if (error instanceof TenantNotFoundError) throw error;
      throw new TenantError(
        `Failed to get tenant by slug: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GET_TENANT_ERROR'
      );
    }
  }

  /**
   * Invite user to tenant
   */
  async inviteUserToTenant(
    tenantId: string,
    inviteData: InviteTenantUserForm
  ): Promise<void> {
    try {
      await this.setTenantContext(tenantId);
      
      // In a real implementation, you would:
      // 1. Send invitation email
      // 2. Create pending invitation record
      // 3. Handle invitation acceptance
      
      // For now, we'll just add the user directly if they exist
      const { data: user } = await this.client.auth.admin.getUserByEmail(inviteData.email);
      
      if (user) {
        const { error } = await this.client
          .from('tenant_users')
          .insert({
            tenant_id: tenantId,
            user_id: user.user?.id,
            role: inviteData.role,
            permissions: inviteData.permissions || {}
          });

        if (error) throw error;
      } else {
        throw new TenantError('User not found', 'USER_NOT_FOUND');
      }
    } catch (error) {
      throw new TenantError(
        `Failed to invite user: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'INVITE_USER_ERROR',
        tenantId
      );
    }
  }

  /**
   * Check if user has access to tenant
   */
  async checkTenantAccess(userId: string, tenantId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client.rpc('user_has_tenant_access', {
        user_uuid: userId,
        tenant_uuid: tenantId
      });

      if (error) throw error;
      return data || false;
    } catch (error) {
      throw new TenantError(
        `Failed to check tenant access: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ACCESS_CHECK_ERROR',
        tenantId
      );
    }
  }

  /**
   * Get tenant statistics
   */
  async getTenantStatistics(): Promise<TenantStatistics[]> {
    try {
      const { data, error } = await this.client
        .from('tenant_statistics')
        .select('*');

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to get tenant statistics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STATISTICS_ERROR'
      );
    }
  }

  // Testing and validation methods

  /**
   * Run tenant isolation tests
   */
  async testTenantIsolation(): Promise<TenantIsolationTestResult[]> {
    try {
      const { data, error } = await this.client.rpc('test_tenant_isolation');
      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to run isolation tests: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TEST_ERROR'
      );
    }
  }

  /**
   * Run tenant performance tests
   */
  async testTenantPerformance(): Promise<TenantPerformanceTestResult[]> {
    try {
      const { data, error } = await this.client.rpc('test_tenant_performance');
      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to run performance tests: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'TEST_ERROR'
      );
    }
  }

  /**
   * Validate RLS policies
   */
  async validateRlsPolicies(): Promise<RlsPolicyValidationResult[]> {
    try {
      const { data, error } = await this.client.rpc('validate_rls_policies');
      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to validate RLS policies: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'VALIDATION_ERROR'
      );
    }
  }

  /**
   * Check tenant data distribution
   */
  async checkTenantDataDistribution(): Promise<TenantDataDistribution[]> {
    try {
      const { data, error } = await this.client.rpc('check_tenant_data_distribution');
      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new TenantError(
        `Failed to check data distribution: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'DATA_CHECK_ERROR'
      );
    }
  }

  // Tenant-aware query methods

  /**
   * Get products for current tenant
   */
  async getProducts() {
    if (!this.currentTenantId) {
      throw new TenantError('No tenant context set', 'NO_TENANT_CONTEXT');
    }
    return this.client.from('products').select('*');
  }

  /**
   * Get categories for current tenant
   */
  async getCategories() {
    if (!this.currentTenantId) {
      throw new TenantError('No tenant context set', 'NO_TENANT_CONTEXT');
    }
    return this.client.from('categories').select('*');
  }

  /**
   * Get brands for current tenant
   */
  async getBrands() {
    if (!this.currentTenantId) {
      throw new TenantError('No tenant context set', 'NO_TENANT_CONTEXT');
    }
    return this.client.from('brands').select('*');
  }

  /**
   * Get orders for current tenant
   */
  async getOrders() {
    if (!this.currentTenantId) {
      throw new TenantError('No tenant context set', 'NO_TENANT_CONTEXT');
    }
    return this.client.from('orders').select('*');
  }

  /**
   * Get blogs for current tenant
   */
  async getBlogs() {
    if (!this.currentTenantId) {
      throw new TenantError('No tenant context set', 'NO_TENANT_CONTEXT');
    }
    return this.client.from('blogs').select('*');
  }
}

/**
 * Utility functions for tenant management
 */
export class TenantUtils {
  /**
   * Extract tenant slug from hostname
   */
  static extractTenantFromHost(host: string): string | null {
    if (!host) return null;

    // Handle subdomain: tenant.bitsnbongs.com
    if (host.includes('.bitsnbongs.com') && !host.startsWith('www.')) {
      const subdomain = host.split('.')[0];
      return subdomain !== 'bitsnbongs' ? subdomain : null;
    }

    // Handle custom domain: tenant.com
    // This would require a database lookup to map custom domains to tenants
    return null;
  }

  /**
   * Generate tenant slug from name
   */
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  /**
   * Validate tenant slug format
   */
  static isValidSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 50;
  }

  /**
   * Generate subdomain URL
   */
  static generateSubdomainUrl(slug: string, baseUrl: string = 'bitsnbongs.com'): string {
    return `https://${slug}.${baseUrl}`;
  }

  /**
   * Check if user has required role for action
   */
  static hasRequiredRole(
    userRole: string,
    requiredRole: string
  ): boolean {
    const roleHierarchy = { owner: 3, admin: 2, member: 1 };
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    return userLevel >= requiredLevel;
  }

  /**
   * Format tenant display name
   */
  static formatTenantName(tenant: Tenant): string {
    return tenant.name || tenant.slug;
  }

  /**
   * Get tenant URL based on configuration
   */
  static getTenantUrl(tenant: Tenant): string {
    if (tenant.domain) {
      return `https://${tenant.domain}`;
    }
    if (tenant.subdomain) {
      return `https://${tenant.subdomain}.bitsnbongs.com`;
    }
    return `https://${tenant.slug}.bitsnbongs.com`;
  }
}

// Export singleton instance
export const tenantClient = new TenantSupabaseClient();

// Export utility functions
export const {
  extractTenantFromHost,
  generateSlug,
  isValidSlug,
  generateSubdomainUrl,
  hasRequiredRole,
  formatTenantName,
  getTenantUrl
} = TenantUtils;