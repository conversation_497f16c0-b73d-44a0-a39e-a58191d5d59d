/**
 * AI Response Types for Unified AI System
 * 
 * Defines the structure for all AI responses across the platform
 */

export interface AIResponse {
  // Core Response
  content: string;
  success: boolean;
  
  // Provider Information
  provider: string;
  model?: string;
  
  // Usage Statistics
  tokens_used?: number;
  cost?: number;
  processing_time?: number;
  
  // Quality Metrics
  confidence_score?: number;
  quality_score?: number;
  
  // Metadata
  request_id?: string;
  response_id?: string;
  timestamp: Date;
  
  // Error Information
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  
  // Additional Data
  metadata?: {
    [key: string]: any;
  };
}

export interface StreamingAIResponse {
  stream: ReadableStream<string>;
  metadata: Omit<AIResponse, 'content'>;
}

export interface BatchAIResponse {
  responses: AIResponse[];
  total_tokens: number;
  total_cost: number;
  success_rate: number;
  processing_time: number;
}

export interface AIProviderStatus {
  provider: string;
  available: boolean;
  rate_limited: boolean;
  error_rate: number;
  average_response_time: number;
  last_check: Date;
}

export interface AIUsageStats {
  provider: string;
  requests_today: number;
  tokens_today: number;
  cost_today: number;
  requests_this_month: number;
  tokens_this_month: number;
  cost_this_month: number;
  success_rate: number;
}

export interface AIContentResult {
  content: string;
  seo_data?: {
    title?: string;
    description?: string;
    keywords?: string[];
    slug?: string;
  };
  images?: {
    url: string;
    alt: string;
    caption?: string;
  }[];
  metadata?: {
    word_count?: number;
    reading_time?: number;
    sentiment?: 'positive' | 'neutral' | 'negative';
    topics?: string[];
  };
}
