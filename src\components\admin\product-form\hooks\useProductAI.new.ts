import { useState, useEffect } from "react";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Product } from "@/types/database";
import { v4 as uuidv4 } from "uuid";

// Extended Product type to include specifications and other properties
interface ExtendedProduct extends Product {
  specifications?: string | any[];
  additional_info_title1?: string;
  additional_info_description1?: string;
}

export function useProductAI(
  formData: Partial<ExtendedProduct>,
  setFormData: React.Dispatch<React.SetStateAction<Partial<ExtendedProduct>>>
) {
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isFindingImages, setIsFindingImages] = useState(false);
  const [descriptionError, setDescriptionError] = useState<string | null>(null);
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);

  // Fetch categories on mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("id, name");

        if (error) throw error;
        if (data) setCategories(data);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Handle generating product descriptions with web scraping
  const handleGenerateDescription = async () => {
    if (!formData.name) {
      toast({
        title: "Missing Information",
        description: "Please enter a product name first",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingDescription(true);
    setDescriptionError(null);

    try {
      console.log("Generating description for:", formData.name);
      
      // Get category name for better context
      const categoryName = formData.category_id 
        ? categories.find(c => c.id === formData.category_id)?.name || ''
        : '';
      
      console.log("Category:", categoryName);
      
      let scrapedDescriptions: string[] = [];
      
      // Safely try to get real product descriptions from the web
      try {
        const { getProductDescriptions } = await import("@/api/productScraper");
        scrapedDescriptions = await getProductDescriptions(formData.name, categoryName);
      } catch (scrapeError) {
        console.error("Error scraping descriptions:", scrapeError);
        // Continue even if scraping fails
        scrapedDescriptions = [];
      }
      
      console.log("Using client-side implementation for description generation");
      
      // Safely check for API key
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error("Gemini API key not configured. Please set VITE_GEMINI_API_KEY in your .env file.");
      }
      
      try {
        // Call the Gemini API with error handling
        const response = await fetch(
          "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-goog-api-key": apiKey,
            },
            body: JSON.stringify({
              contents: [
                {
                  parts: [
                    {
                      text: `Generate a compelling product description for an e-commerce store. 
                      Product name: ${formData.name}
                      Category: ${categoryName}
                      ${scrapedDescriptions.length > 0 ? `Here are some existing descriptions I found online for similar products. Please rewrite them in a unique way to avoid copyright issues while maintaining factual accuracy:\n${scrapedDescriptions.join('\n\n')}` : 'No existing descriptions found, please create a detailed and compelling description based on the product name and category.'}
                      
                      Guidelines:
                      - Write 2-3 paragraphs (150-300 words total)
                      - Include key features and benefits
                      - Use engaging, descriptive language
                      - Highlight what makes this product special
                      - Format with proper spacing between paragraphs
                      - If it's a CBD product, mention potential wellness benefits without making medical claims
                      - For smoking accessories, focus on craftsmanship, materials, and design
                      
                      Response format: Plain text paragraphs only, no HTML or markdown.`
                    }
                  ]
                }
              ],
              generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 800,
                stopSequences: []
              },
              safetySettings: [
                {
                  category: "HARM_CATEGORY_HARASSMENT",
                  threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                  category: "HARM_CATEGORY_HATE_SPEECH",
                  threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                  category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                  threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                  category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                  threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
              ]
            })
          }
        );
        
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Extract the generated text from the response
        const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
        
        if (!generatedText) {
          throw new Error("No text was generated from the API response");
        }
        
        // Update form data with the generated description
        setFormData((prev) => ({
          ...prev,
          description: generatedText
        }));
        
        toast({
          title: "Description Generated",
          description: "Product description has been generated successfully.",
          variant: "default",
        });
        
      } catch (apiError) {
        console.error("Error calling Gemini API:", apiError);
        
        // Try fallback method with basic template
        const fallbackDescription = `${formData.name} - A high-quality product designed for optimal performance and user satisfaction. ${categoryName ? `This premium ${categoryName} product offers exceptional value and reliability.` : ''} Perfect for those seeking quality and durability.`;
        
        setFormData((prev) => ({
          ...prev,
          description: fallbackDescription
        }));
        
        toast({
          title: "Basic Description Created",
          description: "Could not generate detailed description. A basic template has been used instead.",
          variant: "default",
        });
      }
      
    } catch (error) {
      console.error("Error generating description:", error);
      setDescriptionError(error instanceof Error ? error.message : String(error));
      
      toast({
        title: "Error",
        description: "Failed to generate description. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  // Handle finding product images
  const handleFindImages = async () => {
    if (!formData.name) {
      toast({
        title: "Missing Information",
        description: "Please enter a product name first",
        variant: "destructive",
      });
      return;
    }

    setIsFindingImages(true);
    
    try {
      // Create a search query that includes the product name and category
      const productName = formData.name;
      const productCategory = formData.category_id 
        ? categories.find(c => c.id === formData.category_id)?.name || ''
        : '';
      
      // Build the search query
      let searchQuery = productName;
      if (productCategory) {
        searchQuery += ` ${productCategory}`;
      }
      
      // Add quality descriptors
      searchQuery += " high quality product image";
      
      console.log("Using Google Image Search with query:", searchQuery);
      
      // Import the client-side implementation dynamically
      const { searchGoogleImages } = await import("@/api/googleImageSearch");
      
      // Call the Google Image Search API
      const images = await searchGoogleImages(searchQuery, 5);
      
      if (!images || images.length === 0) {
        console.log("No images found");
        toast({
          title: "No Images Found",
          description: "Couldn't find suitable images. Try a different product name or add it manually.",
          variant: "destructive",
        });
        return;
      }
      
      console.log("Found images:", images);
      
      // Show loading toast
      toast({
        title: "Images Found",
        description: `Found ${images.length} images. Now saving to storage...`,
        variant: "default",
      });
      
      // Save all images to Supabase storage
      console.log("Saving images to Supabase storage");
      
      try {
        // Save the main image first
        const mainImageUrl = images[0];
        const savedMainImage = await saveImageToStorage(mainImageUrl, productName || 'product');
        
        // Save additional images in parallel
        const additionalImagePromises = images.slice(1).map((imageUrl, index) => {
          return saveImageToStorage(imageUrl, `${productName || 'product'}-${index + 1}`);
        });
        
        const savedAdditionalImages = await Promise.all(additionalImagePromises);
        
        console.log("Saved main image:", savedMainImage);
        console.log("Saved additional images:", savedAdditionalImages);
        
        // Update form data with the saved image URLs
        setFormData((prev) => ({
          ...prev,
          image: savedMainImage,
          additional_images: savedAdditionalImages,
        }));

        toast({
          title: "Images Saved",
          description: `Successfully saved ${images.length} images to storage`,
          variant: "default",
        });
      } catch (storageError) {
        console.error("Error saving images to storage:", storageError);
        toast({
          title: "Storage Error",
          description: "Found images but couldn't save them to storage. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error finding images:", error);
      toast({
        title: "Error",
        description: "Failed to find images. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsFindingImages(false);
    }
  };

  // Helper function to save an image to Supabase storage
  const saveImageToStorage = async (imageUrl: string, imageName: string): Promise<string> => {
    try {
      console.log(`Saving image to storage: ${imageName}`);
      
      // Skip if the image is already from Supabase storage
      if (imageUrl.includes('supabase.co/storage')) {
        console.log('Image is already in Supabase storage:', imageUrl);
        return imageUrl;
      }
      
      // Create a safe filename
      const safeImageName = imageName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const fileName = `${safeImageName}-${Math.random().toString(36).substring(2, 8)}.jpg`;
      const filePath = `product-images/${fileName}`;
      
      // Direct fetch method (same as blog system)
      console.log('Fetching image directly:', imageUrl);
      
      // Try direct fetch first
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }
      
      // Check if the response is an image
      const contentType = response.headers.get('Content-Type');
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error('The URL did not return a valid image');
      }
      
      // Convert to blob
      const blob = await response.blob();
      
      // Upload to Supabase storage
      const { error: uploadError } = await supabase.storage
        .from('product-images')
        .upload(filePath, blob, { contentType });
      
      if (uploadError) throw uploadError;
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);
      
      console.log(`Image saved successfully: ${publicUrl}`);
      return publicUrl;
      
    } catch (error) {
      console.error('Error saving image to storage:', error);
      
      // Try using a CORS proxy as fallback
      try {
        console.log('Trying with CORS proxy');
        const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(imageUrl)}`;
        
        const proxyResponse = await fetch(proxyUrl);
        if (!proxyResponse.ok) {
          throw new Error(`Proxy fetch failed: ${proxyResponse.statusText}`);
        }
        
        const blob = await proxyResponse.blob();
        const safeImageName = imageName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
        const fileName = `${safeImageName}-proxy-${Math.random().toString(36).substring(2, 8)}.jpg`;
        const filePath = `product-images/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from('product-images')
          .upload(filePath, blob, { contentType: 'image/jpeg' });
        
        if (uploadError) throw uploadError;
        
        const { data: { publicUrl } } = supabase.storage
          .from('product-images')
          .getPublicUrl(filePath);
        
        console.log(`Image saved via proxy: ${publicUrl}`);
        return publicUrl;
        
      } catch (proxyError) {
        console.error('Proxy method failed:', proxyError);
        
        // Create a simple placeholder as last resort
        try {
          console.log('Creating placeholder image');
          
          // Create a canvas for a simple placeholder
          const canvas = document.createElement('canvas');
          canvas.width = 800;
          canvas.height = 600;
          const ctx = canvas.getContext('2d');
          
          if (!ctx) throw new Error('Could not get canvas context');
          
          // Simple gradient background
          const gradient = ctx.createLinearGradient(0, 0, 800, 600);
          gradient.addColorStop(0, '#4a9f7b'); // Sage green
          gradient.addColorStop(1, '#2c614a'); // Darker sage
          
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, 800, 600);
          
          // Product name text
          ctx.fillStyle = 'white';
          ctx.font = 'bold 32px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(imageName, 400, 300);
          
          // Convert to blob
          const blob = await new Promise<Blob>((resolve) => {
            canvas.toBlob((b) => resolve(b!), 'image/jpeg', 0.9);
          });
          
          // Upload placeholder
          const fileName = `placeholder-${Math.random().toString(36).substring(2, 8)}.jpg`;
          const filePath = `product-images/${fileName}`;
          
          const { error: uploadError } = await supabase.storage
            .from('product-images')
            .upload(filePath, blob, { contentType: 'image/jpeg' });
          
          if (uploadError) throw uploadError;
          
          const { data: { publicUrl } } = supabase.storage
            .from('product-images')
            .getPublicUrl(filePath);
          
          console.log(`Placeholder image saved: ${publicUrl}`);
          return publicUrl;
          
        } catch (placeholderError) {
          console.error('Placeholder creation failed:', placeholderError);
          return '/placeholder-product.jpg';
        }
      }
    }
  };

  return {
    isGeneratingDescription,
    isFindingImages,
    handleGenerateDescription,
    handleFindImages,
  };
}
