/**
 * Social Media AI Revolution - Core Engine
 *
 * "Welcome to the real world, Neo..." 🔴
 *
 * This is the main orchestrator for AI-powered social media content generation
 * across all platforms. Built for maximum impact and viral potential.
 */

import { aiServiceManager } from '../AIServiceManager';
import { AIRequest } from '../types/AIRequest';

export interface SocialMediaContent {
  platform: string;
  content: {
    caption: string;
    hashtags: string[];
    image_suggestions: string[];
    optimal_post_time: string;
    engagement_prediction: number;
    call_to_action: string;
  };
  metadata: {
    character_count: number;
    hashtag_count: number;
    estimated_reach: number;
    viral_potential: number;
  };
}

export interface SocialMediaCampaign {
  product_id: string;
  product_name: string;
  platforms: SocialMediaContent[];
  campaign_theme: string;
  total_estimated_reach: number;
  cost_per_platform: number;
  generation_time: number;
  provider_used: string;
}

export interface BulkSocialGeneration {
  products: string[];
  platforms: string[];
  campaign_type: 'product_launch' | 'seasonal' | 'educational' | 'promotional';
  total_content_pieces: number;
  estimated_completion_time: number;
  cost_estimate: number;
}

export class SocialMediaAI {
  private static instance: SocialMediaAI;

  // Platform configurations for optimal content
  private platformConfigs = {
    instagram: {
      max_caption_length: 2200,
      optimal_hashtags: 11,
      best_post_times: ['11:00', '14:00', '17:00'],
      content_types: ['photo', 'carousel', 'reel', 'story'],
      audience_focus: 'visual_lifestyle'
    },
    facebook: {
      max_caption_length: 63206,
      optimal_hashtags: 3,
      best_post_times: ['09:00', '13:00', '15:00'],
      content_types: ['post', 'event', 'poll', 'video'],
      audience_focus: 'community_engagement'
    },
    twitter: {
      max_caption_length: 280,
      optimal_hashtags: 2,
      best_post_times: ['08:00', '12:00', '17:00', '19:00'],
      content_types: ['tweet', 'thread', 'poll', 'space'],
      audience_focus: 'news_discussion'
    },
    tiktok: {
      max_caption_length: 150,
      optimal_hashtags: 5,
      best_post_times: ['06:00', '10:00', '19:00', '21:00'],
      content_types: ['video', 'duet', 'challenge'],
      audience_focus: 'entertainment_viral'
    },
    linkedin: {
      max_caption_length: 3000,
      optimal_hashtags: 5,
      best_post_times: ['08:00', '12:00', '17:00'],
      content_types: ['post', 'article', 'poll', 'event'],
      audience_focus: 'professional_education'
    }
  };

  private constructor() {}

  static getInstance(): SocialMediaAI {
    if (!SocialMediaAI.instance) {
      SocialMediaAI.instance = new SocialMediaAI();
    }
    return SocialMediaAI.instance;
  }

  /**
   * Generate viral social media content for a product across all platforms
   */
  async generateProductCampaign(
    productData: {
      id: string;
      name: string;
      category: string;
      description?: string;
      price?: number;
      features?: string[];
    },
    platforms: string[] = ['instagram', 'facebook', 'twitter', 'tiktok'],
    campaignTheme: string = 'product_showcase'
  ): Promise<SocialMediaCampaign> {

    const startTime = Date.now();
    const platformContent: SocialMediaContent[] = [];

    try {
      // Generate content for each platform in parallel for speed
      const contentPromises = platforms.map(platform =>
        this.generatePlatformContent(productData, platform, campaignTheme)
      );

      const results = await Promise.all(contentPromises);
      platformContent.push(...results);

      // Calculate campaign metrics
      const totalReach = platformContent.reduce((sum, content) =>
        sum + content.metadata.estimated_reach, 0
      );

      return {
        product_id: productData.id,
        product_name: productData.name,
        platforms: platformContent,
        campaign_theme: campaignTheme,
        total_estimated_reach: totalReach,
        cost_per_platform: 0.002, // Estimated AI cost per platform
        generation_time: Date.now() - startTime,
        provider_used: 'unified-ai-social'
      };

    } catch (error) {
      console.error('Social media campaign generation failed:', error);
      throw new Error(`Failed to generate social media campaign: ${error.message}`);
    }
  }

  /**
   * Generate content optimized for a specific platform
   */
  async generatePlatformContent(
    productData: any,
    platform: string,
    theme: string
  ): Promise<SocialMediaContent> {

    const config = this.platformConfigs[platform as keyof typeof this.platformConfigs];
    if (!config) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    try {
      // Build AI prompt optimized for the platform
      const prompt = this.buildPlatformPrompt(productData, platform, theme, config);

      // Generate content using unified AI
      const aiRequest: AIRequest = {
        type: 'social_media',
        content: prompt,
        context: {
          business_type: 'cannabis',
          platform: platform,
          target_audience: this.getPlatformAudience(platform),
          brand_voice: {
            tone: this.getPlatformTone(platform),
            personality: 'Engaging, authentic, and compliant',
            compliance_requirements: ['uk-cannabis-laws', 'social-media-guidelines']
          },
          format: 'structured_json'
        },
        provider: 'gemini' // Use Gemini for creative social content
      };

      // Generate real AI content using the unified AI service
      const content = await this.generateRealAIContent(productData, platform, config, aiRequest);

      return {
        platform,
        content,
        metadata: {
          character_count: content.caption.length,
          hashtag_count: content.hashtags.length,
          estimated_reach: this.calculateEstimatedReach(platform, content),
          viral_potential: this.calculateViralPotential(content, platform)
        }
      };

    } catch (error) {
      console.error(`Failed to generate ${platform} content:`, error);
      throw error;
    }
  }

  /**
   * Generate bulk social media content for multiple products
   */
  async generateBulkCampaigns(
    products: Array<{
      id: string;
      name: string;
      category: string;
      description?: string;
    }>,
    platforms: string[],
    campaignType: 'product_launch' | 'seasonal' | 'educational' | 'promotional' = 'product_launch'
  ): Promise<{
    campaigns: SocialMediaCampaign[];
    summary: BulkSocialGeneration;
  }> {

    const startTime = Date.now();
    const campaigns: SocialMediaCampaign[] = [];

    // Process in batches to avoid overwhelming the AI
    const batchSize = 3;
    const batches = this.chunkArray(products, batchSize);

    for (const batch of batches) {
      const batchPromises = batch.map(product =>
        this.generateProductCampaign(product, platforms, campaignType)
      );

      const batchResults = await Promise.all(batchPromises);
      campaigns.push(...batchResults);

      // Respectful delay between batches
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const summary: BulkSocialGeneration = {
      products: products.map(p => p.id),
      platforms,
      campaign_type: campaignType,
      total_content_pieces: campaigns.length * platforms.length,
      estimated_completion_time: Date.now() - startTime,
      cost_estimate: campaigns.length * platforms.length * 0.002
    };

    return { campaigns, summary };
  }

  /**
   * Get trending hashtags for cannabis/CBD industry
   */
  async getTrendingHashtags(
    category: string,
    platform: string,
    count: number = 10
  ): Promise<string[]> {

    // Cannabis/CBD industry hashtags by category
    const hashtagDatabase = {
      cbd: ['#CBD', '#CBDOil', '#Hemp', '#Wellness', '#NaturalHealing', '#CBDLife', '#Cannabidiol', '#HempOil', '#CBDProducts', '#PlantMedicine'],
      seeds: ['#CannabisSeeds', '#GrowYourOwn', '#Seeds', '#Cannabis', '#Cultivation', '#Genetics', '#Strains', '#GrowLife', '#Homegrown', '#SeedBank'],
      accessories: ['#SmokeShop', '#GlassArt', '#Vaporizer', '#SmokeAccessories', '#Headshop', '#GlassPipe', '#VapeLife', '#SmokeGear', '#420Accessories', '#CannabisAccessories'],
      vaporizers: ['#Vaporizer', '#VapeLife', '#DryHerbVape', '#Vaping', '#VapeCommunity', '#PortableVape', '#VapeReview', '#CleanVaping', '#VapeTech', '#VapeDaily'],
      bongs: ['#Bong', '#WaterPipe', '#GlassArt', '#SmokeShop', '#GlassPipe', '#420Glass', '#FunctionalGlass', '#SmokeGear', '#GlassCollection', '#HeadyGlass']
    };

    const categoryTags = hashtagDatabase[category.toLowerCase() as keyof typeof hashtagDatabase] || hashtagDatabase.cbd;
    const platformSpecific = this.getPlatformSpecificHashtags(platform);

    return [...categoryTags.slice(0, count - 2), ...platformSpecific].slice(0, count);
  }

  /**
   * Calculate optimal posting time for maximum engagement
   */
  getOptimalPostTime(platform: string, timezone: string = 'Europe/London'): string {
    const config = this.platformConfigs[platform as keyof typeof this.platformConfigs];
    if (!config) return '12:00';

    // Return random optimal time for the platform
    const times = config.best_post_times;
    return times[Math.floor(Math.random() * times.length)];
  }

  /**
   * Analyze content performance prediction
   */
  async analyzeContentPerformance(content: SocialMediaContent): Promise<{
    engagement_score: number;
    reach_prediction: number;
    optimization_suggestions: string[];
  }> {

    const suggestions: string[] = [];
    let engagementScore = 70; // Base score

    // Analyze caption quality
    if (content.content.caption.length < 50) {
      suggestions.push('Consider adding more detail to your caption');
      engagementScore -= 10;
    }

    // Analyze hashtag usage
    if (content.content.hashtags.length < 5) {
      suggestions.push('Add more relevant hashtags to increase discoverability');
      engagementScore -= 5;
    }

    // Analyze call-to-action
    if (!content.content.call_to_action) {
      suggestions.push('Add a clear call-to-action to drive engagement');
      engagementScore -= 10;
    }

    return {
      engagement_score: Math.max(engagementScore, 0),
      reach_prediction: content.metadata.estimated_reach,
      optimization_suggestions: suggestions
    };
  }

  // Private helper methods
  private buildPlatformPrompt(productData: any, platform: string, theme: string, config: any): string {
    return `Create engaging ${platform} content for: ${productData.name}

Product Details:
- Category: ${productData.category}
- Description: ${productData.description || 'Premium cannabis product'}
- Features: ${productData.features?.join(', ') || 'High quality, lab tested'}

Platform: ${platform}
Theme: ${theme}
Max Caption Length: ${config.max_caption_length}
Optimal Hashtags: ${config.optimal_hashtags}
Audience Focus: ${config.audience_focus}

Requirements:
- Create compelling, platform-optimized content
- Include relevant hashtags (${config.optimal_hashtags} hashtags)
- Add strong call-to-action
- Ensure UK cannabis law compliance
- Make it engaging and shareable
- Suggest optimal posting time
- Include image/video suggestions`;
  }

  /**
   * Generate real AI content using the unified AI service
   */
  private async generateRealAIContent(productData: any, platform: string, config: any, aiRequest: AIRequest): Promise<any> {
    try {
      // Check if AI service manager is available and initialized
      if (!aiServiceManager) {
        console.warn('🚨 AI Service Manager not imported, using fallback content');
        return this.generateMockContent(productData, platform, config);
      }

      // Check if generateSocialMediaContent method is available
      if (typeof aiServiceManager.generateSocialMediaContent !== 'function') {
        console.warn('🚨 generateSocialMediaContent method not available, using fallback content');
        console.log('🔍 Available methods:', Object.getOwnPropertyNames(aiServiceManager));
        return this.generateMockContent(productData, platform, config);
      }

      console.log('✅ AI Service Manager is available, attempting real AI generation...');

      // Use the AI service manager's social media method
      const aiResponse = await aiServiceManager.generateSocialMediaContent(productData, platform);

      // The generateSocialMediaContent method returns the content directly
      if (aiResponse && aiResponse.caption) {
        console.log(`✅ Real AI content generated for ${platform}`);

        // Get product URL
        const productUrl = productData.product_url || productData.shop_link;

        // Ensure caption includes product URL
        let caption = aiResponse.caption;
        if (productUrl && !caption.includes(productUrl)) {
          // Check if caption already has a call to action with a link
          if (!caption.includes('http') && !caption.includes('shop now') && !caption.includes('buy now')) {
            // Add product link at the end of the caption
            caption = caption.trim() + `\n\n🛒 Shop now: ${productUrl}`;
          }
        }

        return {
          caption: caption,
          hashtags: aiResponse.hashtags || await this.getTrendingHashtags(productData.category, platform, config.optimal_hashtags),
          image_suggestions: aiResponse.image_suggestions || [
            `${productData.name} product shot`,
            `Lifestyle image with ${productData.name}`,
            `${productData.category} collection showcase`
          ],
          optimal_post_time: this.getOptimalPostTime(platform),
          engagement_prediction: Math.floor(Math.random() * 50) + 50,
          call_to_action: this.generateCallToAction(platform),
          product_url: productUrl // Explicitly include product URL in the content
        };
      } else {
        // Fallback to mock content if AI fails
        console.warn(`AI generation failed for ${platform}, using fallback content`);
        return this.generateMockContent(productData, platform, config);
      }
    } catch (error) {
      console.error(`Real AI content generation failed for ${platform}:`, error);
      // Fallback to mock content
      return this.generateMockContent(productData, platform, config);
    }
  }

  /**
   * Extract content from AI text response when JSON parsing fails
   */
  private extractContentFromText(aiText: string, productData: any, platform: string, config: any): any {
    // Extract caption (first paragraph or up to first hashtag)
    const captionMatch = aiText.match(/^(.*?)(?=#|$)/s);
    const caption = captionMatch ? captionMatch[1].trim() : this.generateMockCaption(productData, platform);

    // Extract hashtags
    const hashtagMatches = aiText.match(/#\w+/g);
    const hashtags = hashtagMatches || [];

    // If not enough hashtags, supplement with trending ones
    const trendingHashtags = this.getTrendingHashtags(productData.category, platform, config.optimal_hashtags);
    const allHashtags = [...new Set([...hashtags, ...trendingHashtags])].slice(0, config.optimal_hashtags);

    // Get product URL
    const productUrl = productData.product_url || productData.shop_link;

    // Ensure caption includes product URL
    let finalCaption = caption || this.generateMockCaption(productData, platform);
    if (productUrl && !finalCaption.includes(productUrl)) {
      // Check if caption already has a call to action with a link
      if (!finalCaption.includes('http') && !finalCaption.includes('shop now') && !finalCaption.includes('buy now')) {
        // Add product link at the end of the caption
        finalCaption = finalCaption.trim() + `\n\n🛒 Shop now: ${productUrl}`;
      }
    }

    return {
      caption: finalCaption,
      hashtags: allHashtags,
      image_suggestions: [
        `${productData.name} product shot`,
        `Lifestyle image with ${productData.name}`,
        `${productData.category} collection showcase`
      ],
      optimal_post_time: this.getOptimalPostTime(platform),
      engagement_prediction: Math.floor(Math.random() * 50) + 50,
      call_to_action: this.generateCallToAction(platform),
      product_url: productUrl // Explicitly include product URL in the content
    };
  }

  private async generateMockContent(productData: any, platform: string, config: any): Promise<any> {
    const hashtags = await this.getTrendingHashtags(productData.category, platform, config.optimal_hashtags);

    // Get product URL
    const productUrl = productData.product_url || productData.shop_link;

    // Get base caption
    let caption = this.generateMockCaption(productData, platform);

    // Ensure caption includes product URL
    if (productUrl && !caption.includes(productUrl)) {
      // Check if caption already has a call to action with a link
      if (!caption.includes('http') && !caption.includes('shop now') && !caption.includes('buy now')) {
        // Add product link at the end of the caption
        caption = caption.trim() + `\n\n🛒 Shop now: ${productUrl}`;
      }
    }

    return {
      caption: caption,
      hashtags,
      image_suggestions: [
        `${productData.name} product shot`,
        `Lifestyle image with ${productData.name}`,
        `${productData.category} collection showcase`
      ],
      optimal_post_time: this.getOptimalPostTime(platform),
      engagement_prediction: Math.floor(Math.random() * 50) + 50,
      call_to_action: this.generateCallToAction(platform),
      product_url: productUrl // Explicitly include product URL in the content
    };
  }

  private generateMockCaption(productData: any, platform: string): string {
    const captions = {
      instagram: `✨ Discover the premium quality of ${productData.name}!

Our carefully curated ${productData.category} collection brings you the finest products for the modern cannabis enthusiast.

🌿 Lab tested for purity
🔬 Premium quality guaranteed
🚚 Fast, discreet delivery

Experience the difference quality makes!

#QualityFirst #PremiumCannabis`,

      facebook: `🌟 Introducing ${productData.name} - Premium ${productData.category}

At Bits N Bongs, we're passionate about bringing you the highest quality cannabis products. Our ${productData.name} represents the perfect blend of quality, purity, and effectiveness.

✅ Lab tested and verified
✅ Compliant with UK regulations
✅ Expert customer support
✅ Fast, secure delivery

Join thousands of satisfied customers who trust us for their cannabis needs. Visit our store today!`,

      twitter: `🔥 New arrival: ${productData.name}!

Premium ${productData.category} that sets the standard for quality. Lab tested, UK compliant, and ready to elevate your experience.

Shop now 👇`,

      tiktok: `POV: You found the perfect ${productData.category} 😍

${productData.name} hits different! Premium quality that speaks for itself.

Who else loves quality cannabis products? 🙋‍♀️`,

      linkedin: `Quality Spotlight: ${productData.name}

In the evolving cannabis industry, quality and compliance remain paramount. Our ${productData.name} exemplifies our commitment to providing premium ${productData.category} products that meet the highest standards.

Key differentiators:
• Rigorous lab testing protocols
• Full UK regulatory compliance
• Expert curation and selection
• Exceptional customer experience

The cannabis industry continues to mature, and we're proud to lead with quality-first products that our customers trust.`
    };

    return captions[platform as keyof typeof captions] || captions.instagram;
  }

  private generateCallToAction(platform: string): string {
    const ctas = {
      instagram: 'Shop now - link in bio! 🛒',
      facebook: 'Visit our store today!',
      twitter: 'Shop now 👇',
      tiktok: 'Link in bio! 🔗',
      linkedin: 'Learn more about our products'
    };

    return ctas[platform as keyof typeof ctas] || 'Shop now!';
  }

  private getPlatformAudience(platform: string): string {
    const audiences = {
      instagram: 'cannabis enthusiasts, lifestyle focused, visual learners',
      facebook: 'community members, diverse age groups, detail oriented',
      twitter: 'news followers, quick information seekers, trend aware',
      tiktok: 'young adults, entertainment seekers, viral content consumers',
      linkedin: 'professionals, business owners, industry experts'
    };

    return audiences[platform as keyof typeof audiences] || 'cannabis enthusiasts';
  }

  private getPlatformTone(platform: string): string {
    const tones = {
      instagram: 'casual',
      facebook: 'friendly',
      twitter: 'engaging',
      tiktok: 'casual',
      linkedin: 'professional'
    };

    return tones[platform as keyof typeof tones] || 'friendly';
  }

  private getPlatformSpecificHashtags(platform: string): string[] {
    const platformTags = {
      instagram: ['#InstaGood', '#PhotoOfTheDay'],
      facebook: ['#Community', '#Quality'],
      twitter: ['#Breaking', '#News'],
      tiktok: ['#FYP', '#Viral'],
      linkedin: ['#Industry', '#Professional']
    };

    return platformTags[platform as keyof typeof platformTags] || [];
  }

  private calculateEstimatedReach(platform: string, content: any): number {
    // Mock calculation based on platform and content quality
    const baseReach = {
      instagram: 500,
      facebook: 300,
      twitter: 200,
      tiktok: 1000,
      linkedin: 150
    };

    const base = baseReach[platform as keyof typeof baseReach] || 300;
    const hashtagBonus = content.hashtags.length * 50;
    const qualityBonus = content.caption.length > 100 ? 100 : 0;

    return base + hashtagBonus + qualityBonus;
  }

  private calculateViralPotential(content: any, platform: string): number {
    // Mock viral potential calculation (0-100)
    let score = 50;

    if (content.hashtags.length >= 5) score += 20;
    if (content.caption.includes('🔥') || content.caption.includes('✨')) score += 10;
    if (content.call_to_action.length > 0) score += 15;
    if (platform === 'tiktok') score += 5; // TikTok has higher viral potential

    return Math.min(score, 100);
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// Export singleton instance
export const socialMediaAI = SocialMediaAI.getInstance();
