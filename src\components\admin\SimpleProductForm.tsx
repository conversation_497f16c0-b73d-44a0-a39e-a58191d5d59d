import { Product } from "@/types/database-with-variants";
import { ProductFormToggles } from "./product-form/ProductFormToggles";
import { ProductFormActions } from "./product-form/ProductFormActions";
import { useProductForm } from "./product-form/useProductForm";
import { useCategoriesQuery } from "./product-form/useCategoriesQuery";
import { useBrandsQuery } from "./product-form/hooks/useBrandsQuery";
import { useSubcategoriesQuery } from "./product-form/hooks/useSubcategoriesQuery";
import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, Tag, Wand2, Loader2 } from "lucide-react";
import { VariantBadge, VariantsDialog, VariantForm, OptionDefinitionsManager, BulkVariantGenerator } from "./product-variants";
import { useVariantDialogState } from "./product-variants/hooks/useVariantDialogState";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ProductImageManager } from "./product-form/ProductImageManager";

// Create a custom Supabase client type that includes our custom tables
const customSupabase = supabase as any;

interface ProductFormProps {
  product: Product | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function SimpleProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const { data: categories, isLoading: categoriesLoading } = useCategoriesQuery();
  const { data: brands, isLoading: brandsLoading } = useBrandsQuery();
  const queryClient = useQueryClient();

  // Use our persistent variant dialog state hook
  const {
    isVariantsDialogOpen, setIsVariantsDialogOpen,
    isVariantFormOpen, setIsVariantFormOpen,
    selectedVariant, setSelectedVariant,
    isBulkGeneratorOpen, setIsBulkGeneratorOpen,
    handleEditVariant, handleAddVariant,
    setProductId, setProductName, setProductPrice
  } = useVariantDialogState(
    product?.id,
    product?.name,
    product?.price
  );

  const {
    formData,
    setFormData,
    isSubmitting,
    isGeneratingDescription,
    isFindingImages,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
    handleSubmit,
    handleGenerateDescription,
    handleFindImages,
    // Related products
    relatedProducts,
    handleAddRelatedProduct,
    handleRemoveRelatedProduct,
    handleReorderRelatedProducts,
  } = useProductForm({ product, onSuccess });

  // Fetch subcategories based on the selected category
  const { subcategories, filteredSubcategories, isLoading: subcategoriesLoading } = useSubcategoriesQuery(formData?.category_id);

  // No need for dropdown enhancement with standard HTML elements

  // Update product info in the dialog state when it changes
  useEffect(() => {
    if (product?.id) {
      setProductId(product.id);
      setProductName(product.name);
    }
  }, [product?.id, product?.name, setProductId, setProductName]);

  // Update product price in the dialog state when it changes
  useEffect(() => {
    if (formData?.price) {
      setProductPrice(formData.price);
    }
  }, [formData?.price, setProductPrice]);

  // Function to handle option definitions changes
  const handleOptionDefinitionsChange = (optionDefinitions: any) => {
    console.log('Option definitions changed in SimpleProductForm:', optionDefinitions);
    handleSelectChange('option_definitions', optionDefinitions);
  };

  // Function to handle variant success (create/update/delete)
  const handleVariantSuccess = () => {
    // Invalidate queries to refresh data
    if (product?.id) {
      queryClient.invalidateQueries({ queryKey: ['variants', product.id] });
      queryClient.invalidateQueries({ queryKey: ['variant-count', product.id] });
    }
  };

  // Function to swap main image
  const swapMainImage = (url: string) => {
    console.log('===== SWAP MAIN IMAGE FUNCTION =====');
    console.log('New main image URL:', url);

    // Get current state
    const currentMainImage = formData.image;
    const currentAdditionalImages = [...(formData.additional_images || [])];

    // Create a completely new form data object to avoid reference issues
    const newFormData = {
      ...formData,
      image: url,
      additional_images: []
    };

    // 1. Add all current additional images except the new main image to the new array
    newFormData.additional_images = currentAdditionalImages.filter(img => img !== url);

    // 2. If we have a current main image, add it to additional images
    if (currentMainImage && currentMainImage.trim() !== '' && currentMainImage !== url) {
      newFormData.additional_images.push(currentMainImage);
    }

    // 3. Update the form data directly
    setFormData(newFormData);

    console.log('===== END SWAP MAIN IMAGE FUNCTION =====');
  };

  // Get variant count
  const { data: variantCount = 0, isLoading: isLoadingVariants } = useQuery({
    queryKey: ['variant-count', product?.id],
    queryFn: async () => {
      if (!product?.id) return 0;

      const { count, error } = await customSupabase
        .from('product_variants')
        .select('*', { count: 'exact', head: true })
        .eq('product_id', product.id);

      if (error) {
        console.error('Error fetching variant count:', error);
        return 0;
      }

      return count || 0;
    },
    enabled: !!product?.id,
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <p className="text-sm text-muted-foreground">
              Enter the basic details of your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name || ""}
                  onChange={handleChange}
                  placeholder="Enter product name"
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="description">Description</Label>
                  {handleGenerateDescription && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleGenerateDescription}
                      disabled={isGeneratingDescription}
                      className="ml-2 whitespace-nowrap"
                    >
                      {isGeneratingDescription ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Wand2 className="mr-2 h-4 w-4" />
                          Generate with AI
                        </>
                      )}
                    </Button>
                  )}
                </div>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description || ""}
                  onChange={handleChange}
                  placeholder="Enter product description"
                  rows={5}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  name="sku"
                  value={formData.sku || ""}
                  onChange={handleChange}
                  placeholder="Enter SKU"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug || ""}
                  onChange={handleChange}
                  placeholder="auto-generated-if-empty"
                />
                <p className="text-xs text-gray-500">
                  Leave empty to auto-generate from name
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Product Images */}
        <Card>
          <CardHeader>
            <CardTitle>Product Images</CardTitle>
            <p className="text-sm text-muted-foreground">
              Upload images of your product. The first image will be the main image.
            </p>
          </CardHeader>
          <CardContent>
            <ProductImageManager
              images={formData.additional_images || []}
              onChange={(images) => {
                handleSelectChange('additional_images', images);
              }}
              mainImage={formData.image}
              onMainImageChange={(url) => {
                if (swapMainImage) {
                  swapMainImage(url);
                } else {
                  handleSelectChange('image', url);
                }
              }}
              onFindImagesWithAI={handleFindImages ? () => handleFindImages(formData.name || '') : undefined}
              isFindingImages={isFindingImages}
            />
          </CardContent>
        </Card>

        {/* Pricing & Inventory */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing & Inventory</CardTitle>
            <p className="text-sm text-muted-foreground">
              Set the pricing and inventory details.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="price">Price (£)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sale_price">Sale Price (£)</Label>
                <Input
                  id="sale_price"
                  name="sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.sale_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock_quantity">Quantity</Label>
                <Input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.stock_quantity ?? ""}
                  onChange={handleChange}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="in_stock"
                  checked={formData.in_stock ?? false}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("in_stock", checked)
                  }
                />
                <Label htmlFor="in_stock">In Stock</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Variant Management Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Product Variants</CardTitle>
              <p className="text-sm text-muted-foreground">
                Define options and create variants for this product.
                <a
                  href="#"
                  className="ml-1 text-primary hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    alert("Variant System Help:\n\n1. First, add option types (Size, Color, etc.)\n2. Add values to each option (S, M, L or Red, Blue, etc.)\n3. Then create variants using Add Variant or Bulk Generate");
                  }}
                >
                  How it works
                </a>
              </p>
            </div>
            <div className="flex gap-2">
              {product?.id ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsBulkGeneratorOpen(true)}
                    type="button"
                    title="Create all possible combinations of your options at once"
                  >
                    <Tag className="h-4 w-4 mr-1" />
                    Bulk Generate
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      handleAddVariant();
                    }}
                    type="button"
                    title="Create a single variant with specific option values"
                  >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    Add Variant
                  </Button>
                </>
              ) : (
                <div className="text-sm text-muted-foreground italic">
                  Save product to create variants
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {(
              <div className="space-y-4">
                {/* Helper section */}
                <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-4">
                  <h3 className="text-sm font-medium text-blue-800">How to Create Product Variants</h3>
                  <ol className="text-sm text-blue-700 mt-2 ml-4 list-decimal">
                    <li className="mb-1">First, click <strong>"Add Option"</strong> to define option types (e.g., Size, Color)</li>
                    <li className="mb-1">For each option, click <strong>"Add Value"</strong> to add possible values (e.g., S, M, L)</li>
                    {!product?.id && (
                      <li className="mb-1"><strong>Save the product</strong> to enable variant creation</li>
                    )}
                    {product?.id && (
                      <li className="mb-1">Then, either:
                        <ul className="ml-4 mt-1 list-disc">
                          <li>Click <strong>"Add Variant"</strong> to create individual variants, or</li>
                          <li>Click <strong>"Bulk Generate"</strong> to create all possible combinations at once</li>
                        </ul>
                      </li>
                    )}
                  </ol>
                </div>

                <OptionDefinitionsManager
                  optionDefinitions={formData.option_definitions || {}}
                  onChange={handleOptionDefinitionsChange}
                  productId={product?.id}
                  showSaveButton={!!product?.id}
                  saveInstructions={!product?.id ? "Define your options now and they'll be saved when you create the product." : undefined}
                />

                {/* Variants Summary - Only show when product exists */}
                {product?.id && (
                  <div className="flex items-center justify-between mt-4 pt-4 border-t">
                    <div>
                      <h3 className="text-sm font-medium">Variants</h3>
                      <p className="text-sm text-muted-foreground">
                        {isLoadingVariants
                          ? 'Loading variants...'
                          : variantCount > 0
                            ? `This product has ${variantCount} variant${variantCount === 1 ? '' : 's'}.`
                            : 'No variants created yet.'}
                      </p>
                    </div>
                    {variantCount > 0 && (
                      <Button
                        variant="outline"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsVariantsDialogOpen(true);
                        }}
                        type="button"
                        title="View and edit all variants for this product"
                      >
                        <VariantBadge count={variantCount} />
                        <span className="ml-2">Manage Variants</span>
                      </Button>
                    )}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Organization */}
        <Card>
          <CardHeader>
            <CardTitle>Organization</CardTitle>
            <p className="text-sm text-muted-foreground">
              Categorize and organize your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="brand_id">Brand</Label>
                <Select
                  value={formData.brand_id?.toString() ?? ""}
                  onValueChange={(value) => handleSelectChange("brand_id", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a brand" />
                  </SelectTrigger>
                  <SelectContent>
                    {brands?.map((brand) => (
                      <SelectItem key={brand.id} value={brand.id.toString()}>
                        {brand.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category_id">Category</Label>
                <select
                  id="category_id"
                  name="category_id"
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={formData.category_id?.toString() ?? ""}
                  onChange={(e) => {
                    // When category changes, reset subcategory
                    handleSelectChange("category_id", e.target.value);
                    handleSelectChange("subcategory_id", null);
                  }}
                >
                  <option value="">Select a category</option>
                  {categories?.map((category) => (
                    <option
                      key={category.id}
                      value={category.id.toString()}
                    >
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Subcategory dropdown - only show if a category is selected */}
              {formData.category_id && (
                <div className="space-y-2">
                  <Label htmlFor="subcategory_id">Subcategory</Label>
                  <select
                    id="subcategory_id"
                    name="subcategory_id"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.subcategory_id?.toString() ?? ""}
                    onChange={(e) => handleSelectChange("subcategory_id", e.target.value === "" ? null : e.target.value)}
                  >
                    <option value="">None</option>
                    {filteredSubcategories?.map((subcategory) => (
                      <option
                        key={subcategory.id}
                        value={subcategory.id.toString()}
                      >
                        {subcategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <ProductFormToggles
        formData={formData}
        handleSwitchChange={handleSwitchChange}
      />

      <ProductFormActions
        isSubmitting={isSubmitting}
        isEditing={!!product}
        onCancel={onCancel}
      />

      {/* Variants Dialog - Always render but conditionally show */}
      <VariantsDialog
        isOpen={isVariantsDialogOpen && !!product?.id}
        onClose={() => setIsVariantsDialogOpen(false)}
        productId={product?.id || ''}
        productName={product?.name || ''}
        onEdit={handleEditVariant}
        onAdd={handleAddVariant}
        onSuccess={handleVariantSuccess}
      />

      {/* Variant Form Dialog - Always render but conditionally show */}
      <VariantForm
        isOpen={isVariantFormOpen && !!product?.id}
        onClose={() => setIsVariantFormOpen(false)}
        productId={product?.id || ''}
        productName={product?.name || ''}
        productPrice={formData.price || 0}
        variant={selectedVariant}
        onSuccess={handleVariantSuccess}
      />

      {/* Bulk Variant Generator - Always render but conditionally show */}
      <BulkVariantGenerator
        isOpen={isBulkGeneratorOpen && !!product?.id}
        onClose={() => setIsBulkGeneratorOpen(false)}
        productId={product?.id || ''}
        productName={product?.name || ''}
        basePrice={formData.price || 0}
        onSuccess={handleVariantSuccess}
      />
    </form>
  );
}
