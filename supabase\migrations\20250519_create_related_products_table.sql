-- Migration to create related_products table and its relationships

-- Create the related_products table
CREATE TABLE IF NOT EXISTS public.related_products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  related_product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(product_id, related_product_id)
);

-- Add RLS policies for the related_products table
ALTER TABLE public.related_products ENABLE ROW LEVEL SECURITY;

-- Create policy for admins to manage related products
CREATE POLICY admin_manage_related_products ON public.related_products
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.is_admin = true
  ));

-- Create index for faster lookups
CREATE INDEX idx_related_products_product_id ON public.related_products(product_id);
CREATE INDEX idx_related_products_related_product_id ON public.related_products(related_product_id);
CREATE INDEX idx_related_products_display_order ON public.related_products(display_order);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_related_products_updated_at
BEFORE UPDATE ON public.related_products
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
