# Variant-Based Product System Implementation Plan

## Overview

This document outlines the plan to transition from the current option-based product system to a more robust variant-based system similar to Wix's approach. This will provide better pricing control, inventory management, and user experience.

## Current System Analysis

### Database Structure
- Products are stored in a single `products` table
- Options are stored as fields in the products table:
  - `option_name1`, `option_type1`, `option_description1`, `option_values1`, `option_price_adjustment1`
  - Up to 3 options per product
- Price adjustments are stored as text (semicolon-separated values)
- No dedicated variant storage

### Import Process
- CSV products are mapped to database format
- Options from CSV are stored in the product record
- Price adjustments are calculated but not properly stored

### Frontend Display
- Product detail page calculates final price based on selected options
- Options are displayed as dropdowns
- Inventory is not tracked per variant

## Proposed Variant-Based System

### 1. Database Schema Updates

#### Create a new product_variants table
```sql
CREATE TABLE product_variants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_name TEXT NOT NULL, -- e.g., "3 Pack" or "Red, Small"
  sku TEXT,
  price DECIMAL(10, 2) NOT NULL,
  sale_price DECIMAL(10, 2),
  stock_quantity INTEGER DEFAULT 0,
  in_stock BOOLEAN DEFAULT TRUE,
  image TEXT, -- Optional variant-specific image
  option_combination JSONB, -- Stores the specific option values for this variant
  external_id TEXT, -- For tracking the original ID from import
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
```

#### Update the products table
```sql
-- Add fields to store available options (not values)
ALTER TABLE products ADD COLUMN option_definitions JSONB;
```

### 2. Backend API Updates

#### New Endpoints
- `GET /api/products/:id/variants` - Get all variants for a product
- `POST /api/products/:id/variants` - Create a new variant
- `PUT /api/products/:id/variants/:variantId` - Update a variant
- `DELETE /api/products/:id/variants/:variantId` - Delete a variant

#### Update Existing Endpoints
- `GET /api/products/:id` - Include variant information
- `PUT /api/products/:id` - Update product and its options

### 3. Import Process Updates

#### CSV Format Adjustments
- Main product rows with base information
- Variant rows with specific pricing and options
- Clear relationships between products and their variants

#### Import Logic Changes
1. Parse main products first
2. Parse variants and associate with products
3. Store complete variant information in the database
4. Handle image name transformations:
   - Remove `~mv2` from filenames
   - Convert all extensions to `.webp`
   - Flag products without images as inactive

### 4. Admin UI Updates

#### Product Management
- Add variant management section to product edit page
- Allow adding/editing/removing variants
- Support bulk variant operations

#### Variant Management Interface
- Table view of all variants with inline editing
- Form for adding new variants
- Inventory management per variant
- Pricing controls for each variant

### 5. Frontend Updates

#### Product Detail Page
- Display variant selection UI (dropdowns, buttons, etc.)
- Show the complete price for the selected variant
- Update inventory status based on the selected variant
- Update add-to-cart functionality to include variant ID

#### Cart and Checkout
- Store variant information in cart items
- Display variant details in cart and checkout
- Pass variant information to order processing

## Implementation Phases

### Phase 1: Database and Backend Setup
1. Create database migration for the new variant structure
2. Update the product model to include variant relationships
3. Create variant model and controller
4. Implement basic API endpoints for variants

### Phase 2: Admin UI Development
1. Create variant management interface
2. Update product edit page to include variant management
3. Implement bulk variant operations
4. Add variant image management

### Phase 3: Import Process Updates
1. Update CSV parsing to handle variants
2. Implement image name transformation logic
3. Create test import with sample data
4. Add validation and error handling

### Phase 4: Frontend Updates
1. Update product detail page to display variants
2. Implement variant selection UI
3. Update price display and inventory status
4. Modify cart and checkout to handle variants

### Phase 5: Data Migration
1. Create script to convert existing products to variant-based format
2. Test migration with sample data
3. Implement full migration with validation
4. Handle edge cases and data cleanup

## Image Handling Notes

- CSV file image filenames have `~mv2` before the extension
- Extensions in CSV are `.jpg`, `.png`, or `.webp`
- All uploaded images in the storage bucket:
  - Have `~mv2` removed
  - Are converted to `.webp` format
- Products without images should be flagged as inactive

## Testing Strategy

1. Create test products with variants manually
2. Test import process with sample CSV data
3. Verify frontend display and functionality
4. Test inventory management per variant
5. Validate cart and checkout with variants
6. Perform full end-to-end testing

## Rollout Strategy

1. Implement changes in a development environment
2. Test thoroughly with sample data
3. Migrate a small batch of products (10-20) to the new system
4. Validate functionality with the migrated products
5. Proceed with full migration in batches
6. Monitor for issues and fix as needed

## Fallback Plan

If issues arise during implementation or migration:
1. Keep both systems running in parallel initially
2. Implement feature flags to switch between systems
3. Have a rollback script ready to revert to the original system if needed

---

**IMPLEMENTATION STATUS: ✅ COMPLETED**
- All phases implemented successfully
- Variant system working in production
- Import process handles variants correctly
- Admin UI supports full variant management
- Frontend displays variants properly
