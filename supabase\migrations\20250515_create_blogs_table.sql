-- Create blogs table
CREATE TABLE IF NOT EXISTS blogs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  summary TEXT,
  content TEXT NOT NULL,
  featured_image TEXT,
  author_id UUID REFERENCES auth.users(id),
  category TEXT,
  tags TEXT[],
  is_published BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  view_count INTEGER DEFAULT 0,
  reading_time INTEGER, -- estimated reading time in minutes
  seo_title TEXT,
  seo_description TEXT,
  seo_keywords TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- Create blog_images table for additional images in blog posts
CREATE TABLE IF NOT EXISTS blog_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blog_id UUID REFERENCES blogs(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  alt_text TEXT,
  caption TEXT,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_ai_metadata table for storing AI generation metadata
CREATE TABLE IF NOT EXISTS blog_ai_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blog_id UUID REFERENCES blogs(id) ON DELETE CASCADE,
  prompt_used TEXT,
  ai_model TEXT,
  generation_params JSONB,
  content_segments JSONB, -- Store which parts were AI-generated vs human-edited
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_social_shares table for tracking social media shares
CREATE TABLE IF NOT EXISTS blog_social_shares (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blog_id UUID REFERENCES blogs(id) ON DELETE CASCADE,
  platform TEXT NOT NULL, -- e.g., 'twitter', 'facebook', 'instagram'
  share_url TEXT,
  share_content TEXT,
  share_image TEXT,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  posted_at TIMESTAMP WITH TIME ZONE,
  post_status TEXT DEFAULT 'draft', -- 'draft', 'scheduled', 'posted', 'failed'
  engagement_metrics JSONB, -- likes, shares, comments, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_comments table
CREATE TABLE IF NOT EXISTS blog_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blog_id UUID REFERENCES blogs(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  parent_comment_id UUID REFERENCES blog_comments(id),
  content TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_categories table for better category management
CREATE TABLE IF NOT EXISTS blog_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  parent_id UUID REFERENCES blog_categories(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blogs_slug ON blogs(slug);
CREATE INDEX IF NOT EXISTS idx_blogs_author_id ON blogs(author_id);
CREATE INDEX IF NOT EXISTS idx_blogs_category ON blogs(category);
CREATE INDEX IF NOT EXISTS idx_blogs_is_published ON blogs(is_published);
CREATE INDEX IF NOT EXISTS idx_blogs_is_featured ON blogs(is_featured);
CREATE INDEX IF NOT EXISTS idx_blogs_published_at ON blogs(published_at);
CREATE INDEX IF NOT EXISTS idx_blog_images_blog_id ON blog_images(blog_id);
CREATE INDEX IF NOT EXISTS idx_blog_ai_metadata_blog_id ON blog_ai_metadata(blog_id);
CREATE INDEX IF NOT EXISTS idx_blog_social_shares_blog_id ON blog_social_shares(blog_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_blog_id ON blog_comments(blog_id);
CREATE INDEX IF NOT EXISTS idx_blog_comments_user_id ON blog_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON blog_categories(slug);

-- Create RLS policies
ALTER TABLE blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_ai_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_social_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;

-- Everyone can read published blogs
CREATE POLICY "Anyone can read published blogs" ON blogs
  FOR SELECT USING (is_published = true);

-- Everyone can read published blog images
CREATE POLICY "Anyone can read blog images" ON blog_images
  FOR SELECT USING (
    blog_id IN (SELECT id FROM blogs WHERE is_published = true)
  );

-- Everyone can read blog categories
CREATE POLICY "Anyone can read blog categories" ON blog_categories
  FOR SELECT USING (true);

-- Everyone can read approved comments
CREATE POLICY "Anyone can read approved comments" ON blog_comments
  FOR SELECT USING (
    is_approved = true AND
    blog_id IN (SELECT id FROM blogs WHERE is_published = true)
  );

-- Only admins can manage blogs
CREATE POLICY "Admins can manage blogs" ON blogs
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Only admins can manage blog images
CREATE POLICY "Admins can manage blog images" ON blog_images
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Only admins can manage blog AI metadata
CREATE POLICY "Admins can manage blog AI metadata" ON blog_ai_metadata
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Only admins can manage blog social shares
CREATE POLICY "Admins can manage blog social shares" ON blog_social_shares
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Only admins can manage blog categories
CREATE POLICY "Admins can manage blog categories" ON blog_categories
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Users can create comments
CREATE POLICY "Users can create comments" ON blog_comments
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND
    blog_id IN (SELECT id FROM blogs WHERE is_published = true)
  );

-- Users can update their own comments
CREATE POLICY "Users can update their own comments" ON blog_comments
  FOR UPDATE USING (
    auth.uid() = user_id
  );

-- Admins can manage all comments
CREATE POLICY "Admins can manage all comments" ON blog_comments
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE is_admin = true
    )
  );

-- Add some initial blog categories
INSERT INTO blog_categories (name, slug, description) VALUES
('CBD Products', 'cbd-products', 'Articles about CBD products, benefits, and usage'),
('Smoking Accessories', 'smoking-accessories', 'Information about various smoking accessories and tools'),
('Industry News', 'industry-news', 'Latest news and updates from the cannabis and CBD industry'),
('Health & Wellness', 'health-wellness', 'Articles about health benefits and wellness tips'),
('How-To Guides', 'how-to-guides', 'Step-by-step guides and tutorials');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_blogs_updated_at
BEFORE UPDATE ON blogs
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_ai_metadata_updated_at
BEFORE UPDATE ON blog_ai_metadata
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_social_shares_updated_at
BEFORE UPDATE ON blog_social_shares
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_comments_updated_at
BEFORE UPDATE ON blog_comments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_categories_updated_at
BEFORE UPDATE ON blog_categories
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create function to set published_at when a blog is published
CREATE OR REPLACE FUNCTION set_published_at()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_published = TRUE AND OLD.is_published = FALSE THEN
    NEW.published_at = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set published_at
CREATE TRIGGER set_blogs_published_at
BEFORE UPDATE ON blogs
FOR EACH ROW
WHEN (NEW.is_published IS DISTINCT FROM OLD.is_published)
EXECUTE FUNCTION set_published_at();
