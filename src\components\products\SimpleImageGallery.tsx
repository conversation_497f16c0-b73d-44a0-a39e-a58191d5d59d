import React, { useState } from 'react';

interface SimpleImageGalleryProps {
  mainImage?: string;
  additionalImages?: string[];
  productName?: string;
}

export const SimpleImageGallery: React.FC<SimpleImageGalleryProps> = ({
  mainImage = '',
  additionalImages = [],
  productName = 'Product',
}) => {
  const [selectedImage, setSelectedImage] = useState<string>(mainImage);
  
  // Ensure additionalImages is always an array
  const safeAdditionalImages = Array.isArray(additionalImages) ? additionalImages : [];
  
  // Combine all images into one array, ensuring each image URL is valid
  const allImages = [mainImage, ...safeAdditionalImages]
    .filter(url => url && typeof url === 'string' && url.trim() !== '') as string[];
  
  // Remove any duplicate images
  const uniqueImages = [...new Set(allImages)];
  
  if (uniqueImages.length === 0) {
    return (
      <div className="bg-gray-100 rounded-xl flex items-center justify-center p-8 h-[400px]">
        <p className="text-gray-400">No product images available</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {/* Main image display */}
      <div className="bg-white rounded-xl overflow-hidden shadow-lg">
        <img
          src={selectedImage || uniqueImages[0]}
          alt={productName}
          className="w-full h-auto object-contain"
          style={{ maxHeight: '500px' }}
        />
      </div>
      
      {/* Thumbnail gallery */}
      {uniqueImages.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {uniqueImages.map((image, index) => (
            <button
              key={`thumb-${index}`}
              onClick={() => setSelectedImage(image)}
              className={`flex-shrink-0 border-2 rounded-md overflow-hidden ${
                image === selectedImage ? 'border-blue-500' : 'border-transparent hover:border-gray-200'
              }`}
            >
              <img
                src={image}
                alt={`${productName} thumbnail ${index + 1}`}
                className="w-16 h-16 object-cover"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SimpleImageGallery;
