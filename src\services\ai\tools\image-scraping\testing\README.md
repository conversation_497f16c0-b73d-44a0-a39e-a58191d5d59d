# Isolated Testing Environment for Image Scraping System

This directory contains an isolated testing environment for the image scraping system. It allows you to run tests without affecting the main project configuration or dependencies.

## Setup

To set up the testing environment:

```bash
# Navigate to the testing directory
cd src/services/ai/tools/image-scraping/testing

# Run the setup script
node setup.js
```

This will install the necessary dependencies for testing in this isolated environment.

## Running Tests

After setup, you can run the tests with:

```bash
# Navigate to the testing directory
cd src/services/ai/tools/image-scraping/testing

# Run all tests
npm test

# Run tests in watch mode (automatically re-run when files change)
npm run test:watch

# Run a specific test file
npx jest ../__tests__/PlaywrightImageScraper.test.ts
```

## Test Files

The tests are located in the `__tests__` directory and include:

- `PlaywrightImageScraper.test.ts` - Tests the main scraper
- `ImageQualityAssessor.test.ts` - Tests image quality assessment
- `SourceManager.test.ts` - Tests retailer source management
- `BulkImageProcessor.test.ts` - Tests batch processing
- `ImageScrapingService.test.ts` - Tests the main service interface

## Isolation

This testing environment is completely isolated from the main project:

- It has its own `package.json` with testing dependencies
- It has its own Jest and TypeScript configurations
- It doesn't modify any files outside this directory
- It doesn't affect the main project's dependencies or scripts

This approach allows multiple agents to work on the project simultaneously without conflicts.

## Troubleshooting

If you encounter any issues:

1. Make sure you're in the correct directory (`src/services/ai/tools/image-scraping/testing`)
2. Try running the setup script again: `node setup.js`
3. Check for any TypeScript errors in the test files
4. Ensure Jest is properly installed in this directory
