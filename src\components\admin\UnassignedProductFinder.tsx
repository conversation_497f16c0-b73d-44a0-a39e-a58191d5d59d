import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Loader2, Pencil, Check, X, ChevronDown } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';

interface Product {
  id: string;
  name: string;
  sku: string;
  category_id: string | null;
  subcategory_id: string | null;
  image?: string;
  [key: string]: any;
}

interface Category {
  id: string;
  name: string;
  parent_id: string | null;
}

const UnassignedProductFinder: React.FC = () => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [unassignedProducts, setUnassignedProducts] = useState<Product[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [filterType, setFilterType] = useState<'no-category' | 'no-subcategory'>('no-category');
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string>('');
  const [isSaving, setIsSaving] = useState(false);
  
  // Fetch categories
  const { data: categories = [] } = useQuery<Category[]>({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data || [];
    },
  });
  
  // Get parent categories
  const parentCategories = categories.filter(c => !c.parent_id);
  
  // Get subcategories for the selected category
  const subcategories = categories.filter(c => 
    c.parent_id === selectedCategoryId
  );
  
  // Function to get image URL from product
  const getProductImageUrl = (product: Product) => {
    if (!product) return '/placeholder-product.jpg';
    
    // Default to placeholder
    let finalImageUrl = '/placeholder-product.jpg';
    
    try {
      if (!product.image) return finalImageUrl;
      
      // For data URLs, use them directly
      if (product.image.startsWith('data:')) {
        return product.image;
      }
      
      // Determine the URL to try
      let tryUrl = product.image;
      
      // If it's not a full URL, construct one
      if (!tryUrl.startsWith('http')) {
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
        
        // Fix for double product-images path
        if (tryUrl.startsWith('product-images/product-images/')) {
          // Remove the duplicate prefix
          tryUrl = tryUrl.replace('product-images/product-images/', 'product-images/');
        }
        
        if (tryUrl.startsWith('/storage/')) {
          tryUrl = `${supabaseUrl}${tryUrl}`;
        } else if (tryUrl.startsWith('product-images/')) {
          tryUrl = `${supabaseUrl}/storage/v1/object/public/${tryUrl}`;
        } else {
          tryUrl = `${supabaseUrl}/storage/v1/object/public/product-images/${tryUrl}`;
        }
      }
      
      console.log('Product image URL:', tryUrl);
      
      return tryUrl;
    } catch (error) {
      console.error('Error in image processing:', error);
      return finalImageUrl;
    }
  };
  
  const findUnassignedProducts = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('products')
        .select('id, name, sku, category_id, subcategory_id, image, is_active')
        .eq('is_active', true) // Only show active products
        .order('name');
      
      if (filterType === 'no-category') {
        // Find products without any category
        query = query.is('category_id', null);
      } else {
        // Find products with a category but no subcategory
        query = query.not('category_id', 'is', null).is('subcategory_id', null);
      }
      
      const { data, error } = await query.limit(100);
        
      if (error) {
        console.error('Error fetching unassigned products:', error);
        return;
      }
      
      setUnassignedProducts(data || []);
      setShowResults(true);
    } catch (err) {
      console.error('Error in findUnassignedProducts:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle starting to edit a product
  const handleEdit = (productId: string) => {
    setEditingProduct(productId);
    setSelectedCategoryId('');
    setSelectedSubcategoryId('');
  };
  
  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingProduct(null);
    setSelectedCategoryId('');
    setSelectedSubcategoryId('');
  };
  
  // Handle saving category assignment
  const handleSaveAssignment = async (productId: string) => {
    if (!selectedCategoryId) {
      toast({
        title: "Error",
        description: "Please select a category",
        variant: "destructive"
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Prepare the update data
      const updateData: { category_id: string; subcategory_id: string | null } = {
        category_id: selectedCategoryId,
        subcategory_id: null // Default to null
      };
      
      // Only set subcategory_id if it's not 'none' and not empty
      if (selectedSubcategoryId && selectedSubcategoryId !== 'none') {
        updateData.subcategory_id = selectedSubcategoryId;
      }
      
      console.log('Updating product with:', updateData, 'Product ID:', productId);
      
      // First verify the product exists
      const { data: productCheck, error: checkError } = await supabase
        .from('products')
        .select('id, name')
        .eq('id', productId)
        .single();
        
      if (checkError) {
        console.error('Error checking product:', checkError);
        throw new Error(`Product not found: ${productId}`);
      }
      
      console.log('Found product to update:', productCheck);
      
      // Now update the product
      const { data, error } = await supabase
        .from('products')
        .update(updateData)
        .eq('id', productId);
        
      if (error) {
        console.error('Error updating product:', error);
        throw error;
      }
      
      console.log('Update result:', data);
      
      // Verify the update worked by fetching the product again
      const { data: verifyData, error: verifyError } = await supabase
        .from('products')
        .select('id, name, category_id, subcategory_id')
        .eq('id', productId)
        .single();
        
      if (verifyError) {
        console.error('Error verifying update:', verifyError);
      } else {
        console.log('Verified product after update:', verifyData);
      }
      
      // Update local state
      setUnassignedProducts(prev => 
        prev.filter(product => product.id !== productId)
      );
      
      // Reset editing state
      setEditingProduct(null);
      setSelectedCategoryId('');
      setSelectedSubcategoryId('');
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['products'] });
      
      toast({
        title: "Success",
        description: "Product category updated successfully",
      });
    } catch (err) {
      console.error('Error updating product:', err);
      toast({
        title: "Error",
        description: "Failed to update product category",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Unassigned Product Finder</CardTitle>
        <CardDescription>
          Find products that need to be assigned to categories
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="no-category" 
                checked={filterType === 'no-category'}
                onCheckedChange={(checked) => checked && setFilterType('no-category')}
              />
              <label 
                htmlFor="no-category"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                Products without any category
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="no-subcategory" 
                checked={filterType === 'no-subcategory'}
                onCheckedChange={(checked) => checked && setFilterType('no-subcategory')}
              />
              <label 
                htmlFor="no-subcategory"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                Products with category but no subcategory
              </label>
            </div>
          </div>
          
          <Button 
            onClick={findUnassignedProducts}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching...
              </>
            ) : (
              'Find Unassigned Products'
            )}
          </Button>
          
          {showResults && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">
                Unassigned Products ({unassignedProducts.length})
                <Badge variant="outline" className="ml-2">
                  {filterType === 'no-category' ? 'No Category' : 'No Subcategory'}
                </Badge>
              </h3>
              
              {unassignedProducts.length > 0 ? (
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Image</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {unassignedProducts.map(product => (
                        <TableRow key={product.id} className={editingProduct === product.id ? 'bg-muted/50' : ''}>
                          <TableCell>
                            <div className="h-16 w-16 relative overflow-hidden rounded-md border">
                              <img 
                                src={getProductImageUrl(product)}
                                alt={product.name}
                                className="h-full w-full object-cover object-center"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                                }}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>
                            {filterType === 'no-category' ? (
                              <Badge variant="destructive">No Category</Badge>
                            ) : (
                              <Badge variant="secondary">No Subcategory</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {editingProduct === product.id ? (
                              <div className="space-y-2">
                                <div className="flex flex-col space-y-2">
                                  <Select
                                    value={selectedCategoryId}
                                    onValueChange={setSelectedCategoryId}
                                  >
                                    <SelectTrigger className="w-[200px]">
                                      <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {parentCategories.map((category) => (
                                        <SelectItem key={category.id} value={category.id}>
                                          {category.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  
                                  {selectedCategoryId && (
                                    <Select
                                      value={selectedSubcategoryId}
                                      onValueChange={setSelectedSubcategoryId}
                                      disabled={subcategories.length === 0}
                                    >
                                      <SelectTrigger className="w-[200px]">
                                        <SelectValue placeholder={subcategories.length === 0 ? "No subcategories" : "Select subcategory"} />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        {subcategories.map((subcategory) => (
                                          <SelectItem key={subcategory.id} value={subcategory.id}>
                                            {subcategory.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                </div>
                                
                                <div className="flex space-x-2">
                                  <Button 
                                    size="sm" 
                                    variant="default" 
                                    onClick={() => handleSaveAssignment(product.id)}
                                    disabled={isSaving || !selectedCategoryId}
                                  >
                                    {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <Check className="h-4 w-4 mr-1" />}
                                    Save
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    variant="outline" 
                                    onClick={handleCancelEdit}
                                  >
                                    <X className="h-4 w-4 mr-1" />
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <Button 
                                size="sm" 
                                variant="outline" 
                                onClick={() => handleEdit(product.id)}
                              >
                                <Pencil className="h-4 w-4 mr-1" />
                                Assign
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p>No unassigned products found.</p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UnassignedProductFinder;
