import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface ImportOptionsProps {
  skipImages: boolean;
  setSkipImages: (value: boolean) => void;
  useExternalUrls: boolean;
  setUseExternalUrls: (value: boolean) => void;
  wixDomain: string;
  setWixDomain: (value: string) => void;
}

const ImportOptions: React.FC<ImportOptionsProps> = ({
  skipImages,
  setSkipImages,
  useExternalUrls,
  setUseExternalUrls,
  wixDomain,
  setWixDomain
}) => {
  const handleOptionChange = (value: string) => {
    if (value === 'skip') {
      setSkipImages(true);
      setUseExternalUrls(false);
    } else if (value === 'external') {
      setSkipImages(false);
      setUseExternalUrls(true);
    } else {
      setSkipImages(false);
      setUseExternalUrls(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Image Import Options</CardTitle>
        <CardDescription>
          Choose how to handle product images during import
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup 
          value={skipImages ? 'skip' : useExternalUrls ? 'external' : 'upload'}
          onValueChange={handleOptionChange}
          className="space-y-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="skip" id="skip" />
            <Label htmlFor="skip" className="font-medium">
              Skip images (use placeholders)
            </Label>
            <p className="text-sm text-gray-500 ml-6">
              Import products without images. You can add images later using the admin interface.
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="external" id="external" />
            <Label htmlFor="external" className="font-medium">
              Use external Wix URLs
            </Label>
            <p className="text-sm text-gray-500 ml-6">
              Reference images directly from Wix's servers. This requires the Wix site to remain active.
            </p>
          </div>
          
          {useExternalUrls && (
            <div className="ml-6 mt-2">
              <Label htmlFor="wixDomain" className="mb-1 block text-sm">
                Wix Site Domain (optional)
              </Label>
              <Input
                id="wixDomain"
                value={wixDomain}
                onChange={(e) => setWixDomain(e.target.value)}
                placeholder="e.g., mysite.wixsite.com"
                className="w-full max-w-md"
              />
              <p className="mt-1 text-xs text-gray-500">
                Leave blank to use default Wix CDN URLs
              </p>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="upload" id="upload" />
            <Label htmlFor="upload" className="font-medium">
              Upload to Supabase (standard)
            </Label>
            <p className="text-sm text-gray-500 ml-6">
              Upload images to Supabase storage. This requires images to be accessible via URL.
            </p>
          </div>
        </RadioGroup>
      </CardContent>
    </Card>
  );
};

export default ImportOptions;
