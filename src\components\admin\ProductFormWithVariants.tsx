import { Product } from "@/types/database-with-variants";
import { ProductFormToggles } from "./product-form/ProductFormToggles";
import { ProductFormActions } from "./product-form/ProductFormActions";
import { useProductForm } from "./product-form/useProductForm";
import { useCategoriesQuery } from "./product-form/useCategoriesQuery";
import { useBrandsQuery } from "./product-form/hooks/useBrandsQuery";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PlusCircle, Tag, Wand2, Loader2 } from "lucide-react";
import { VariantBadge, VariantsDialog, VariantForm, OptionDefinitionsManager, BulkVariantGenerator } from "./product-variants";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ProductImageManager } from "./product-form/ProductImageManager";

// Create a custom Supabase client type that includes our custom tables
const customSupabase = supabase as any;

interface ProductFormProps {
  product: Product | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function ProductFormWithVariants({ product, onSuccess, onCancel }: ProductFormProps) {
  const { data: categories, isLoading: categoriesLoading } = useCategoriesQuery();
  const { data: brands, isLoading: brandsLoading } = useBrandsQuery();
  const queryClient = useQueryClient();

  // Variant management state
  const [isVariantsDialogOpen, setIsVariantsDialogOpen] = useState(false);
  const [isVariantFormOpen, setIsVariantFormOpen] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [isBulkGeneratorOpen, setIsBulkGeneratorOpen] = useState(false);

  const {
    formData,
    setFormData,
    isSubmitting,
    isGeneratingDescription,
    isFindingImages,
    handleChange,
    handleSwitchChange,
    handleSelectChange,
    handleSubmit,
    handleGenerateDescription,
    handleFindImages,
    // Related products
    relatedProducts,
    handleAddRelatedProduct,
    handleRemoveRelatedProduct,
    handleReorderRelatedProducts,
  } = useProductForm({ product, onSuccess });

  // Direct function to handle swapping main image with an additional image
  const swapMainImage = (newMainImageUrl: string) => {
    console.log('ProductForm.swapMainImage called with:', newMainImageUrl);

    // Get current state
    const currentMainImage = formData.image;
    const currentAdditionalImages = [...(formData.additional_images || [])];

    // Create new state
    let newAdditionalImages = [...currentAdditionalImages];

    // 1. If we have a current main image, add it to additional images (if not already there)
    if (currentMainImage && currentMainImage.trim() !== '' && currentMainImage !== newMainImageUrl) {
      if (!newAdditionalImages.includes(currentMainImage)) {
        newAdditionalImages.push(currentMainImage);
        console.log('Added previous main image to additional images:', currentMainImage);
      }
    }

    // 2. Remove the new main image from additional images
    if (newAdditionalImages.includes(newMainImageUrl)) {
      newAdditionalImages = newAdditionalImages.filter(img => img !== newMainImageUrl);
      console.log('Removed new main image from additional images:', newMainImageUrl);
    }

    // 3. Update the form data directly
    setFormData({
      ...formData,
      image: newMainImageUrl,
      additional_images: newAdditionalImages
    });

    console.log('Updated form data:', {
      mainImage: newMainImageUrl,
      additionalImages: newAdditionalImages
    });
  };

  // Fetch variant count for this product
  const { data: variantCount = 0, isLoading: isLoadingVariants } = useQuery({
    queryKey: ['variant-count', product?.id],
    queryFn: async () => {
      if (!product?.id) return 0;

      const { count, error } = await customSupabase
        .from('product_variants')
        .select('*', { count: 'exact', head: true })
        .eq('product_id', product.id);

      if (error) {
        console.error('Error fetching variant count:', error);
        return 0;
      }

      return count || 0;
    },
    enabled: !!product?.id,
  });

  // Handle option definitions changes
  const handleOptionDefinitionsChange = (optionDefinitions: Record<string, string[]>) => {
    setFormData({
      ...formData,
      option_definitions: optionDefinitions
    });
  };

  // Handle variant edit
  const handleEditVariant = (variant: any) => {
    setSelectedVariant(variant);
    setIsVariantFormOpen(true);
  };

  // Handle variant success
  const handleVariantSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['variant-count', product?.id] });
  };

  console.log("Rendering ProductForm with data:", {
    productId: product?.id,
    hasCategories: !!categories?.length,
    formDataKeys: Object.keys(formData),
    variantCount
  });

  // Instead of creating a separate component, we'll render the form fields directly
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <p className="text-sm text-muted-foreground">
              Enter the basic details of your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name || ""}
                  onChange={(e) => {
                    console.log("Name input changed:", e.target.value);
                    handleChange(e);
                  }}
                  placeholder="Enter product name"
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">Description</Label>
                <div className="relative">
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description || ""}
                    onChange={handleChange}
                    placeholder="Enter product description"
                    rows={5}
                  />
                  {handleGenerateDescription && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute right-2 bottom-2"
                      onClick={handleGenerateDescription}
                      disabled={isGeneratingDescription}
                    >
                      {isGeneratingDescription ? (
                        <>
                          <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-3.5 w-3.5 mr-1" />
                          Generate
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  name="sku"
                  value={formData.sku || ""}
                  onChange={handleChange}
                  placeholder="Enter SKU"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="barcode">Barcode (ISBN, UPC, GTIN, etc.)</Label>
                <Input
                  id="barcode"
                  name="barcode"
                  value={formData.barcode || ""}
                  onChange={handleChange}
                  placeholder="Enter barcode"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug || ""}
                  onChange={handleChange}
                  placeholder="auto-generated-if-empty"
                />
                <p className="text-xs text-gray-500">
                  Leave empty to auto-generate from name
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Product Images */}
        <Card>
          <CardHeader>
            <CardTitle>Product Images</CardTitle>
            <p className="text-sm text-muted-foreground">
              Upload images of your product. The first image will be the main image.
            </p>
          </CardHeader>
          <CardContent>
            <ProductImageManager
              images={formData.additional_images || []}
              onChange={(images) => {
                handleSelectChange('additional_images', images);
              }}
              mainImage={formData.image}
              onMainImageChange={(url) => {
                if (swapMainImage) {
                  swapMainImage(url);
                } else {
                  handleSelectChange('image', url);
                }
              }}
              onFindImagesWithAI={handleFindImages ? () => handleFindImages(formData.name || '') : undefined}
              isFindingImages={isFindingImages}
            />
          </CardContent>
        </Card>

        {/* Pricing & Inventory */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing & Inventory</CardTitle>
            <p className="text-sm text-muted-foreground">
              Set the pricing and inventory details.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="price">Price (£)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sale_price">Sale Price (£)</Label>
                <Input
                  id="sale_price"
                  name="sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.sale_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500">
                  Leave empty if there's no sale
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost_price">Cost (£)</Label>
                <Input
                  id="cost_price"
                  name="cost_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cost_price ?? ""}
                  onChange={handleChange}
                  placeholder="0.00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock_quantity">Quantity</Label>
                <Input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.stock_quantity ?? ""}
                  onChange={handleChange}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="track_inventory"
                  checked={formData.in_stock ?? false}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("in_stock", checked)
                  }
                />
                <Label htmlFor="track_inventory">In Stock</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured ?? false}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("is_featured", checked)
                  }
                />
                <Label htmlFor="is_featured">Featured</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Variant Management Section - Moved here between Pricing and Organization */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Product Variants</CardTitle>
              <p className="text-sm text-muted-foreground">
                Define options and create variants for this product. This replaces the legacy options system.
                <a
                  href="/docs/variant-system-usage.md"
                  target="_blank"
                  className="ml-1 text-primary hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open('/docs/variant-system-usage.md', '_blank');
                  }}
                >
                  View usage guide
                </a>
              </p>
            </div>
            {product?.id && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsBulkGeneratorOpen(true)}
                  type="button" // Explicitly set button type to prevent form submission
                >
                  <Tag className="h-4 w-4 mr-1" />
                  Bulk Generate
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault(); // Prevent form submission
                    setSelectedVariant(null);
                    setIsVariantFormOpen(true);
                  }}
                  type="button" // Explicitly set button type to prevent form submission
                >
                  <PlusCircle className="h-4 w-4 mr-1" />
                  Add Variant
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent>
            {!product?.id ? (
              <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
                <h3 className="text-sm font-medium text-yellow-800">Save Product First</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  You need to save the product before you can add variants. Please fill in the required fields and click "Save" to continue.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Option Definitions Manager */}
                <OptionDefinitionsManager
                  optionDefinitions={formData.option_definitions || {}}
                  onChange={handleOptionDefinitionsChange}
                />

                {/* Variants Summary */}
                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                  <div>
                    <h3 className="text-sm font-medium">Variants</h3>
                    <p className="text-sm text-muted-foreground">
                      {isLoadingVariants
                        ? 'Loading variants...'
                        : variantCount > 0
                          ? `This product has ${variantCount} variant${variantCount === 1 ? '' : 's'}.`
                          : 'No variants created yet.'}
                    </p>
                  </div>
                  {variantCount > 0 && (
                    <Button
                      variant="outline"
                      onClick={(e) => {
                        e.preventDefault(); // Prevent form submission
                        setIsVariantsDialogOpen(true);
                      }}
                      type="button" // Explicitly set button type to prevent form submission
                    >
                      <VariantBadge count={variantCount} />
                      <span className="ml-2">Manage Variants</span>
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Organization */}
        <Card>
          <CardHeader>
            <CardTitle>Organization</CardTitle>
            <p className="text-sm text-muted-foreground">
              Categorize and organize your product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="brand_id">Brand</Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.brand_id?.toString() ?? ""}
                    onValueChange={(value) => handleSelectChange("brand_id", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id.toString()}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category_id">Category</Label>
                <Select
                  value={formData.category_id?.toString() ?? ""}
                  onValueChange={(value) => handleSelectChange("category_id", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.additional_info ?? ""}
                  onChange={handleChange}
                  placeholder="Enter tags separated by commas"
                />
                <p className="text-xs text-gray-500">
                  Separate tags with commas
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <p className="text-sm text-muted-foreground">
              Add extra details about the product.
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="additional_info_title1">Section Title</Label>
                  <Input
                    id="additional_info_title1"
                    name="additional_info_title1"
                    value={formData.additional_info_title1 as string || ''}
                    onChange={handleChange}
                    placeholder="e.g. Features, Specifications"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="additional_info_description1">Section Content</Label>
                  <Textarea
                    id="additional_info_description1"
                    name="additional_info_description1"
                    value={formData.additional_info_description1 as string || ''}
                    onChange={handleChange}
                    placeholder="Enter details for this section"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Related Products Section */}
        {relatedProducts && relatedProducts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Related Products</CardTitle>
              <p className="text-sm text-muted-foreground">
                Add products that customers might also want to purchase.
              </p>
            </CardHeader>
            <CardContent>
              {onAddRelatedProduct && onRemoveRelatedProduct && onReorderRelatedProducts && (
                <div className="space-y-4">
                  <p>Select related products to display on the product page.</p>
                  <div className="flex flex-wrap gap-2">
                    {relatedProducts.map((product) => (
                      <div key={product.id} className="flex items-center gap-2 bg-gray-100 rounded-md p-2">
                        <span>{product.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => onRemoveRelatedProduct(product.id)}
                        >
                          <span className="sr-only">Remove</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                          </svg>
                        </Button>
                      </div>
                    ))}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // This would typically open a dialog to select products
                      alert('Add related products functionality would go here');
                    }}
                  >
                    Add Related Product
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      <ProductFormToggles
        formData={formData}
        handleSwitchChange={handleSwitchChange}
      />

      <ProductFormActions
        isSubmitting={isSubmitting}
        isEditing={!!product}
        onCancel={onCancel}
      />

      {/* Variants Dialog */}
      {product?.id && (
        <VariantsDialog
          isOpen={isVariantsDialogOpen}
          onClose={() => setIsVariantsDialogOpen(false)}
          productId={product.id}
          productName={product.name}
          onEdit={handleEditVariant}
          onAdd={() => {
            setSelectedVariant(null);
            setIsVariantFormOpen(true);
          }}
          onSuccess={handleVariantSuccess}
        />
      )}

      {/* Variant Form Dialog */}
      {product?.id && (
        <VariantForm
          isOpen={isVariantFormOpen}
          onClose={() => setIsVariantFormOpen(false)}
          productId={product.id}
          productName={product.name}
          productPrice={formData.price || 0}
          variant={selectedVariant}
          onSuccess={handleVariantSuccess}
        />
      )}

      {/* Bulk Variant Generator */}
      {product?.id && (
        <BulkVariantGenerator
          isOpen={isBulkGeneratorOpen}
          onClose={() => setIsBulkGeneratorOpen(false)}
          productId={product.id}
          productName={product.name}
          basePrice={formData.price || 0}
          onSuccess={handleVariantSuccess}
        />
      )}
    </form>
  );
}
