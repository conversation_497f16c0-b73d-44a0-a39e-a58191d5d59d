import { supabase } from "@/integrations/supabase/client";

// Shipping Zone interface
export interface ShippingZone {
  id: string;
  name: string;
  description?: string;
  countries: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Shipping Method interface
export interface ShippingMethod {
  id: string;
  zone_id: string;
  name: string;
  description: string;
  price: number;
  free_shipping_threshold?: number;
  estimated_days_min: number;
  estimated_days_max: number;
  icon: 'standard' | 'express' | 'nextDay' | 'free';
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
  zone?: ShippingZone;
}

// Shipping Rate Calculation
export interface ShippingCalculation {
  method: ShippingMethod;
  price: number;
  isFree: boolean;
  estimatedDelivery: string;
}

export class ShippingService {
  /**
   * Get all shipping zones
   */
  async getShippingZones(): Promise<ShippingZone[]> {
    const { data, error } = await supabase
      .from('shipping_zones')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching shipping zones:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get shipping zone by ID
   */
  async getShippingZone(id: string): Promise<ShippingZone | null> {
    const { data, error } = await supabase
      .from('shipping_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching shipping zone:', error);
      return null;
    }

    return data;
  }

  /**
   * Create shipping zone
   */
  async createShippingZone(zone: Omit<ShippingZone, 'id' | 'created_at' | 'updated_at'>): Promise<ShippingZone> {
    const { data, error } = await supabase
      .from('shipping_zones')
      .insert([zone])
      .select()
      .single();

    if (error) {
      console.error('Error creating shipping zone:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update shipping zone
   */
  async updateShippingZone(id: string, updates: Partial<ShippingZone>): Promise<ShippingZone> {
    const { data, error } = await supabase
      .from('shipping_zones')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating shipping zone:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete shipping zone
   */
  async deleteShippingZone(id: string): Promise<void> {
    const { error } = await supabase
      .from('shipping_zones')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting shipping zone:', error);
      throw error;
    }
  }

  /**
   * Get all shipping methods (for admin - includes inactive)
   */
  async getShippingMethods(): Promise<ShippingMethod[]> {
    const { data, error } = await supabase
      .from('shipping_methods')
      .select(`
        *,
        zone:shipping_zones(*)
      `)
      .order('sort_order');

    if (error) {
      console.error('Error fetching shipping methods:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get only active shipping methods (for public use)
   */
  async getActiveShippingMethods(): Promise<ShippingMethod[]> {
    const { data, error } = await supabase
      .from('shipping_methods')
      .select(`
        *,
        zone:shipping_zones!inner(*)
      `)
      .eq('is_active', true)
      .eq('zone.is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching active shipping methods:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get shipping methods for a specific zone
   */
  async getShippingMethodsByZone(zoneId: string): Promise<ShippingMethod[]> {
    const { data, error } = await supabase
      .from('shipping_methods')
      .select(`
        *,
        zone:shipping_zones(*)
      `)
      .eq('zone_id', zoneId)
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching shipping methods by zone:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Create shipping method
   */
  async createShippingMethod(method: Omit<ShippingMethod, 'id' | 'created_at' | 'updated_at' | 'zone'>): Promise<ShippingMethod> {
    const { data, error } = await supabase
      .from('shipping_methods')
      .insert([method])
      .select(`
        *,
        zone:shipping_zones(*)
      `)
      .single();

    if (error) {
      console.error('Error creating shipping method:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update shipping method
   */
  async updateShippingMethod(id: string, updates: Partial<ShippingMethod>): Promise<ShippingMethod> {
    const { data, error } = await supabase
      .from('shipping_methods')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select(`
        *,
        zone:shipping_zones(*)
      `)
      .single();

    if (error) {
      console.error('Error updating shipping method:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete shipping method
   */
  async deleteShippingMethod(id: string): Promise<void> {
    const { error } = await supabase
      .from('shipping_methods')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting shipping method:', error);
      throw error;
    }
  }

  /**
   * Calculate shipping for a country and cart total
   */
  async calculateShipping(country: string, cartTotal: number): Promise<ShippingCalculation[]> {
    // Find the zone that includes this country (getShippingZones already filters for active zones)
    const zones = await this.getShippingZones();
    const zone = zones.find(z => z.countries.includes(country));

    if (!zone) {
      // Return default shipping if no zone found
      return this.getDefaultShipping();
    }

    // Get shipping methods for this zone (getShippingMethodsByZone already filters for active methods)
    const methods = await this.getShippingMethodsByZone(zone.id);

    // Calculate shipping for each method
    const calculations: ShippingCalculation[] = methods.map(method => {
      const isFree = method.free_shipping_threshold ? cartTotal >= method.free_shipping_threshold : false;
      const price = isFree ? 0 : method.price;

      let estimatedDelivery = '';
      if (method.estimated_days_min === method.estimated_days_max) {
        estimatedDelivery = `${method.estimated_days_min} business day${method.estimated_days_min > 1 ? 's' : ''}`;
      } else {
        estimatedDelivery = `${method.estimated_days_min}-${method.estimated_days_max} business days`;
      }

      return {
        method,
        price,
        isFree,
        estimatedDelivery
      };
    });

    return calculations;
  }

  /**
   * Get default shipping methods (fallback)
   */
  private getDefaultShipping(): ShippingCalculation[] {
    const defaultMethods = [
      {
        id: 'standard-default',
        zone_id: 'default',
        name: 'Standard Shipping',
        description: 'Delivery within 3-5 business days',
        price: 5.99,
        estimated_days_min: 3,
        estimated_days_max: 5,
        icon: 'standard' as const,
        is_active: true,
        sort_order: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    return defaultMethods.map(method => ({
      method,
      price: method.price,
      isFree: false,
      estimatedDelivery: `${method.estimated_days_min}-${method.estimated_days_max} business days`
    }));
  }

  /**
   * Get shipping methods in the format expected by checkout
   * Always fetches fresh data directly from the database
   */
  async getCheckoutShippingMethods(country: string = 'United Kingdom'): Promise<any[]> {
    console.log('Fetching fresh shipping methods for checkout...');
    
    try {
      // Find the zone that includes this country
      const { data: zonesData, error: zonesError } = await supabase
        .from('shipping_zones')
        .select('*')
        .eq('is_active', true);
        
      if (zonesError) {
        console.error('Error fetching shipping zones:', zonesError);
        throw zonesError;
      }
      
      const zones = zonesData || [];
      const zone = zones.find(z => z.countries.includes(country));
      
      if (!zone) {
        console.log('No shipping zone found for country:', country);
        return [];
      }
      
      console.log(`Found shipping zone: ${zone.name} (${zone.id})`);
      
      // Get active shipping methods for this zone directly from the database
      // Use more explicit filters to ensure only truly active methods are returned
      const { data: methodsData, error: methodsError } = await supabase
        .from('shipping_methods')
        .select(`
          *,
          zone:shipping_zones(*)
        `)
        .eq('zone_id', zone.id)
        .eq('is_active', true) // Explicitly check for active methods
        .order('sort_order')
        .headers({
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        });
        
      if (methodsError) {
        console.error('Error fetching shipping methods:', methodsError);
        throw methodsError;
      }
      
      // Before filtering, log all methods to see what's coming back
      console.log('Raw shipping methods from database:', methodsData?.length || 0);
      
      // Apply multiple layers of filtering to ensure only active methods are included
      // 1. Check if is_active is strictly true
      // 2. Verify zone exists and is active
      const methods = (methodsData || []).filter(method => {
        // Method must exist and have an ID
        if (!method || !method.id) {
          console.log('Filtering out method with missing ID:', method);
          return false;
        }
        
        // Method must be explicitly active
        if (method.is_active !== true) {
          console.log('Filtering out inactive method:', method.name, method.id);
          return false;
        }
        
        // Zone must exist and be active (double-check)
        if (!method.zone || method.zone.is_active !== true) {
          console.log('Filtering out method with inactive zone:', method.name, method.id);
          return false;
        }
        
        return true;
      });
      
      console.log(`Found ${methods.length} active shipping methods for checkout after filtering`);
      if (methods.length !== (methodsData || []).length) {
        console.warn(`Filtered out ${(methodsData || []).length - methods.length} methods that were not actually active`);
      }
      
      // Format for checkout
      const formattedMethods = methods.map(method => {
        let estimatedDelivery = '';
        if (method.estimated_days_min === method.estimated_days_max) {
          estimatedDelivery = `${method.estimated_days_min} business day${method.estimated_days_min > 1 ? 's' : ''}`;
        } else {
          estimatedDelivery = `${method.estimated_days_min}-${method.estimated_days_max} business days`;
        }
        
        return {
          id: method.id,
          name: method.name,
          description: method.description,
          price: method.price,
          estimatedDays: estimatedDelivery,
          icon: method.icon,
          is_active: true // Explicitly mark as active for the frontend
        };
      });
      
      console.log('Returning formatted methods for checkout:', formattedMethods.length);
      return formattedMethods;
    } catch (error) {
      console.error('Error in getCheckoutShippingMethods:', error);
      return []; // Return empty array on error instead of crashing
    }
  }

  /**
   * Get available countries list
   */
  getAvailableCountries(): string[] {
    return [
      'United Kingdom',
      'Ireland',
      'France',
      'Germany',
      'Spain',
      'Italy',
      'Netherlands',
      'Belgium',
      'Portugal',
      'Austria',
      'Denmark',
      'Sweden',
      'Norway',
      'Finland',
      'Switzerland',
      'Poland',
      'Czech Republic',
      'Hungary',
      'Slovakia',
      'Slovenia',
      'Croatia',
      'Estonia',
      'Latvia',
      'Lithuania',
      'Luxembourg',
      'Malta',
      'Cyprus',
      'Bulgaria',
      'Romania',
      'Greece'
    ];
  }
}

// Export singleton instance
export const shippingService = new ShippingService();
