
import { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import CartDrawer from '@/components/cart/CartDrawer';
import ScrollFix from '@/components/ScrollFix';
import ChatBubble from '@/components/chat/ChatBubble';
import ChatInterface from '@/components/chat/ChatInterface';

const ShopLayout = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  return (
    <div className="flex flex-col min-h-screen">
      <ScrollFix />
      <Navbar cartComponent={<CartDrawer />} />
      <main className="flex-grow">
        <Outlet />
      </main>
      <Footer />

      {/* AI Chat Assistant */}
      <ChatBubble
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        onToggle={() => setIsChatOpen(prev => !prev)}
      >
        <ChatInterface />
      </ChatBubble>
    </div>
  );
};

export default ShopLayout;
