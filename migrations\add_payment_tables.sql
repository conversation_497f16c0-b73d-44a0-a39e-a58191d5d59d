-- Add payment_sessions table to store payment information
CREATE TABLE IF NOT EXISTS payment_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  provider TEXT NOT NULL,
  amount NUMERIC(10, 2) NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add payment_provider field to orders table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'orders' AND column_name = 'payment_provider') THEN
    ALTER TABLE orders ADD COLUMN payment_provider TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'orders' AND column_name = 'payment_session_id') THEN
    ALTER TABLE orders ADD COLUMN payment_session_id UUID REFERENCES payment_sessions(id);
  END IF;
END $$;

-- Create index on payment_sessions
CREATE INDEX IF NOT EXISTS idx_payment_sessions_provider ON payment_sessions(provider);
CREATE INDEX IF NOT EXISTS idx_payment_sessions_status ON payment_sessions(status);