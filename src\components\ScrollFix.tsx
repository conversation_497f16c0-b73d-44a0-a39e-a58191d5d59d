import { useEffect } from 'react';

/**
 * Component to fix scroll behavior issues with keyboard arrows
 * This component adds a passive event listener to ensure keyboard scrolling works properly
 */
const ScrollFix = () => {
  useEffect(() => {
    // Function to ensure the page responds to keyboard scroll events
    const enableKeyboardScroll = () => {
      // This empty handler with passive: true ensures the browser's default
      // scroll behavior works correctly with keyboard arrow keys
      window.addEventListener('keydown', () => {}, { passive: true });
      
      // Also ensure wheel events are handled properly
      window.addEventListener('wheel', () => {}, { passive: true });
    };

    enableKeyboardScroll();

    // Clean up function
    return () => {
      // No need to remove these passive listeners as they don't do anything
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default ScrollFix;
