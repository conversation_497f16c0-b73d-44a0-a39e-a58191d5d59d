// fix-product-options.mjs
// Script to fix product options that were incorrectly imported from WIX
// The CSV file has options in format: "Colour,DROP_DOWN,Black;Purple;Green;Blue;Grey;Gold;Rasta"
// But only "DROP_DOWN" was imported into option_type1, missing the actual values

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import csv from 'csv-parser';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Using Supabase key:', supabaseKey ? 'Key found (not showing for security)' : 'No key found');

const supabase = createClient(supabaseUrl, supabaseKey);

// Path to the CSV file
const csvFilePath = path.join(process.cwd(), 'docs', 'catalog_products.csv');

// Map to store product SKUs to their options
const productOptionsMap = new Map();

// Read the CSV file and extract product options
async function readProductOptionsFromCSV() {
  console.log('Reading product options from CSV file...');
  
  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        // Extract the product details
        const handleId = row.handleId;
        const name = row.name;
        const sku = row.sku || '';
        
        // Debug log to see what we're processing
        console.log(`Processing row for product: ${name} (SKU: ${sku})`);
        
        // Check for product options (up to 6 options as seen in the CSV headers)
        const options = [];
        
        for (let i = 1; i <= 6; i++) {
          const optionName = row[`productOptionName${i}`];
          const optionType = row[`productOptionType${i}`];
          const optionDescription = row[`productOptionDescription${i}`];
          
          // Debug log for option data
          if (optionName || optionType || optionDescription) {
            console.log(`Found option ${i}: Name=${optionName}, Type=${optionType}, Description=${optionDescription}`);
          }
          
          if (optionName && optionType) {
            // If we have a DROP_DOWN type with values in the description
            if (optionType === 'DROP_DOWN' && optionDescription) {
              options.push({
                name: optionName,
                type: optionType,
                values: optionDescription.split(';').map(v => v.trim())
              });
            }
          }
        }
        
        // Store all products, even those without options, for better matching
        productOptionsMap.set(handleId, {
          sku,
          name,
          options
        });
      })
      .on('end', () => {
        const productsWithOptions = Array.from(productOptionsMap.values()).filter(p => p.options.length > 0).length;
        console.log(`Finished reading CSV. Found ${productOptionsMap.size} products total.`);
        console.log(`Of these, ${productsWithOptions} products have options.`);
        resolve(productOptionsMap);
      })
      .on('error', (error) => {
        console.error('Error reading CSV:', error);
        reject(error);
      });
  });
}

// Match products in the database with products in the CSV using SKU or name
async function matchProductsAndUpdateOptions() {
  console.log('Matching products and updating options...');
  
  // Get all products from the database
  const { data: products, error } = await supabase
    .from('products')
    .select('id, name, sku, option_name1, option_type1, option_description1');
  
  if (error) {
    console.error('Error fetching products:', error);
    return;
  }
  
  console.log(`Found ${products.length} products in the database.`);
  
  // Count products with 'DROP_DOWN' but no options
  const productsWithDropdownNoOptions = products.filter(p => 
    p.option_type1 === 'DROP_DOWN' && (!p.option_description1 || p.option_description1.trim() === '')
  ).length;
  
  console.log(`Found ${productsWithDropdownNoOptions} products with 'DROP_DOWN' type but no option values.`);
  
  let updatedCount = 0;
  let skippedCount = 0;
  let productsWithOptions = 0;
  
  // First, let's count how many products in our CSV actually have options
  for (const [handleId, productData] of productOptionsMap.entries()) {
    if (productData.options && productData.options.length > 0) {
      productsWithOptions++;
    }
  }
  
  console.log(`CSV contains ${productsWithOptions} products with options.`);
  
  // For each product in the database that has 'DROP_DOWN' but no options, try to find a match in the CSV
  for (const product of products) {
    if (product.option_type1 !== 'DROP_DOWN' || (product.option_description1 && product.option_description1.trim() !== '')) {
      // Skip products that don't have the issue we're trying to fix
      continue;
    }
    
    // Try to find a matching product in our CSV data
    let matchedCsvProduct = null;
    let matchedHandleId = null;
    
    // First try to match by SKU
    if (product.sku) {
      for (const [handleId, productData] of productOptionsMap.entries()) {
        if (productData.sku === product.sku) {
          matchedCsvProduct = productData;
          matchedHandleId = handleId;
          console.log(`Found match by SKU for ${product.name} (${product.sku})`);
          break;
        }
      }
    }
    
    // If no match by SKU, try by name
    if (!matchedCsvProduct) {
      for (const [handleId, productData] of productOptionsMap.entries()) {
        // Use a more flexible name matching (case insensitive, trim whitespace)
        if (productData.name.trim().toLowerCase() === product.name.trim().toLowerCase()) {
          matchedCsvProduct = productData;
          matchedHandleId = handleId;
          console.log(`Found match by name for ${product.name}`);
          break;
        }
      }
    }
    
    // If we found a match and it has options, update the product
    if (matchedCsvProduct && matchedCsvProduct.options && matchedCsvProduct.options.length > 0) {
      const updates = {};
      
      // Map options to the database fields
      matchedCsvProduct.options.forEach((option, index) => {
        const optionNum = index + 1;
        if (optionNum <= 3) { // Database has fields for up to 3 options
          updates[`option_name${optionNum}`] = option.name;
          updates[`option_type${optionNum}`] = option.type;
          updates[`option_description${optionNum}`] = option.values.join(';');
        }
      });
      
      console.log(`Updating product ${product.name} with options:`, updates);
      
      // Update the product in the database
      const { error: updateError } = await supabase
        .from('products')
        .update(updates)
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.name}:`, updateError);
        skippedCount++;
      } else {
        console.log(`Successfully updated product options for: ${product.name}`);
        updatedCount++;
      }
    } else if (product.option_type1 === 'DROP_DOWN') {
      // This product has DROP_DOWN type but we couldn't find matching options
      console.log(`Could not find matching options for product: ${product.name} (${product.sku})`);
      skippedCount++;
    }
  }
  
  console.log(`\nUpdate complete!`);
  console.log(`Updated ${updatedCount} products with options.`);
  console.log(`Skipped ${skippedCount} products that couldn't be matched or had errors.`);
}

// Main function to run the script
async function main() {
  try {
    console.log('Starting product options fix...');
    await readProductOptionsFromCSV();
    await matchProductsAndUpdateOptions();
    console.log('Product options fix completed successfully!');
  } catch (error) {
    console.error('Error running product options fix:', error);
  }
}

// Run the script
main();
