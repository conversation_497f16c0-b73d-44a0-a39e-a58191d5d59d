import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  Truck,
  Clock,
  Zap,
  Gift,
  Globe,
  Package,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { shippingService, ShippingZone, ShippingMethod } from '@/services/shippingService';
import { useQueryClient } from '@tanstack/react-query';

interface ShippingManagerProps {}

export function ShippingManager({}: ShippingManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [zones, setZones] = useState<ShippingZone[]>([]);
  const [methods, setMethods] = useState<ShippingMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedZone, setSelectedZone] = useState<ShippingZone | null>(null);
  const [isZoneDialogOpen, setIsZoneDialogOpen] = useState(false);
  const [isMethodDialogOpen, setIsMethodDialogOpen] = useState(false);
  const [editingZone, setEditingZone] = useState<ShippingZone | null>(null);
  const [editingMethod, setEditingMethod] = useState<ShippingMethod | null>(null);

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [zonesData, methodsData] = await Promise.all([
        shippingService.getShippingZones(),
        shippingService.getShippingMethods()
      ]);
      setZones(zonesData);
      setMethods(methodsData);
    } catch (error) {
      console.error('Error loading shipping data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load shipping data',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Zone management
  const handleCreateZone = () => {
    setEditingZone(null);
    setIsZoneDialogOpen(true);
  };

  const handleEditZone = (zone: ShippingZone) => {
    setEditingZone(zone);
    setIsZoneDialogOpen(true);
  };

  const handleDeleteZone = async (zone: ShippingZone) => {
    if (!confirm(`Are you sure you want to delete the "${zone.name}" shipping zone?`)) {
      return;
    }

    try {
      await shippingService.deleteShippingZone(zone.id);
      toast({
        title: 'Success',
        description: 'Shipping zone deleted successfully'
      });
      loadData();
    } catch (error) {
      console.error('Error deleting zone:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete shipping zone',
        variant: 'destructive'
      });
    }
  };

  // Method management
  const handleCreateMethod = () => {
    setEditingMethod(null);
    setIsMethodDialogOpen(true);
  };

  const handleEditMethod = (method: ShippingMethod) => {
    setEditingMethod(method);
    setIsMethodDialogOpen(true);
  };

  const handleDeleteMethod = async (method: ShippingMethod) => {
    if (!confirm(`Are you sure you want to delete the "${method.name}" shipping method?`)) {
      return;
    }

    try {
      await shippingService.deleteShippingMethod(method.id);
      toast({
        title: 'Success',
        description: 'Shipping method deleted successfully'
      });
      loadData();
    } catch (error) {
      console.error('Error deleting method:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete shipping method',
        variant: 'destructive'
      });
    }
  };

  const getIconComponent = (iconType: string) => {
    switch (iconType) {
      case 'standard':
        return <Truck className="h-4 w-4" />;
      case 'express':
        return <Clock className="h-4 w-4" />;
      case 'nextDay':
        return <Zap className="h-4 w-4" />;
      case 'free':
        return <Gift className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(price);
  };

  // Toggle method active status
  const toggleMethodActive = async (method: ShippingMethod) => {
    try {
      // First update the method in the database
      await shippingService.updateShippingMethod(method.id, { 
        is_active: !method.is_active 
      });
      
      // Stronger cache busting: first remove all queries
      queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
      queryClient.removeQueries({ queryKey: ['shipping-methods'] });
      
      // Force the browser to clear any localStorage cache
      localStorage.setItem('shipping_cache_bust', Date.now().toString());
      localStorage.setItem('last_shipping_refresh', '0'); // Force refresh on next page load
      
      toast({
        title: `Method ${!method.is_active ? 'activated' : 'deactivated'}`,
        description: `The shipping method has been ${!method.is_active ? 'activated' : 'deactivated'} successfully. Changes will be reflected in checkout immediately.`,
      });
      
      // Wait before refreshing data to ensure the database had time to update
      setTimeout(() => {
        loadData(); // Refresh the current view data
        
        // Instead of relying on cache invalidation, actively refresh page if toggle was successful
        console.log('Shipping method update successful, triggering refresh');
      }, 500);
    } catch (error) {
      console.error('Error toggling method active status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update shipping method status.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Package className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p>Loading shipping configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Shipping Management
          </CardTitle>
          <CardDescription>
            Manage shipping zones, methods, and rates for your store
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Changes to shipping rates will affect new orders immediately.
              Existing orders will use the rates that were active when they were placed.
            </AlertDescription>
          </Alert>

          <div className="flex items-center space-x-2 mb-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                // Force invalidate all shipping data in both admin and checkout contexts
                queryClient.removeQueries({ queryKey: ['shipping-zones'] });
                queryClient.removeQueries({ queryKey: ['shipping-methods'] });
                queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
                
                // Set timestamp to force client refresh on all pages
                localStorage.setItem('shipping_cache_bust', Date.now().toString());
                
                // Reload admin data
                loadData();
                
                toast({
                  title: 'Data Refreshed',
                  description: 'All shipping data has been refreshed from the database.',
                });
              }}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh All Data</span>
            </Button>
            <span className="text-xs text-muted-foreground">
              Click to force reload all shipping data & clear caches
            </span>
          </div>

          <Tabs defaultValue="zones" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="zones">Shipping Zones</TabsTrigger>
              <TabsTrigger value="methods">Shipping Methods</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="zones" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Shipping Zones</h3>
                <Button onClick={handleCreateZone}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Zone
                </Button>
              </div>

              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Zone Name</TableHead>
                      <TableHead>Countries</TableHead>
                      <TableHead>Methods</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {zones.map((zone) => {
                      const zoneMethods = methods.filter(m => m.zone_id === zone.id);
                      return (
                        <TableRow key={zone.id}>
                          <TableCell className="font-medium">{zone.name}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {zone.countries.slice(0, 3).map((country) => (
                                <Badge key={country} variant="outline" className="text-xs">
                                  {country}
                                </Badge>
                              ))}
                              {zone.countries.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{zone.countries.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {zoneMethods.length} method{zoneMethods.length !== 1 ? 's' : ''}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={zone.is_active ? 'default' : 'secondary'}>
                              {zone.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditZone(zone)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteZone(zone)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="methods" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Shipping Methods</h3>
                <Button onClick={handleCreateMethod}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Method
                </Button>
              </div>

              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Method</TableHead>
                      <TableHead>Zone</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Free Threshold</TableHead>
                      <TableHead>Delivery Time</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {methods.map((method) => (
                      <TableRow key={method.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getIconComponent(method.icon)}
                            <div>
                              <div className="font-medium">{method.name}</div>
                              <div className="text-sm text-gray-500">{method.description}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {method.zone?.name || 'Unknown Zone'}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatPrice(method.price)}</TableCell>
                        <TableCell>
                          {method.free_shipping_threshold
                            ? formatPrice(method.free_shipping_threshold)
                            : 'None'
                          }
                        </TableCell>
                        <TableCell>
                          {method.estimated_days_min === method.estimated_days_max
                            ? `${method.estimated_days_min} day${method.estimated_days_min > 1 ? 's' : ''}`
                            : `${method.estimated_days_min}-${method.estimated_days_max} days`
                          }
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={method.is_active ? 'default' : 'secondary'}
                              className="cursor-pointer"
                              onClick={() => toggleMethodActive(method)}
                            >
                              {method.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                            <div className="h-6 w-6 p-0">
                              <Switch 
                                checked={method.is_active} 
                                className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                onCheckedChange={() => toggleMethodActive(method)}
                              />
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditMethod(method)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteMethod(method)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <ShippingPreview zones={zones} methods={methods} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Zone Dialog */}
      <ZoneDialog
        isOpen={isZoneDialogOpen}
        onClose={() => setIsZoneDialogOpen(false)}
        zone={editingZone}
        onSave={loadData}
      />

      {/* Method Dialog */}
      <MethodDialog
        isOpen={isMethodDialogOpen}
        onClose={() => setIsMethodDialogOpen(false)}
        method={editingMethod}
        zones={zones}
        onSave={loadData}
      />
    </div>
  );
}

// Shipping Preview Component
function ShippingPreview({ zones, methods }: { zones: ShippingZone[], methods: ShippingMethod[] }) {
  const [selectedCountry, setSelectedCountry] = useState('United Kingdom');
  const [cartTotal, setCartTotal] = useState(50);

  const availableCountries = shippingService.getAvailableCountries();
  
  // Find the active zone for the selected country
  const zone = zones.find(z => z.is_active && z.countries.includes(selectedCountry));
  
  // Explicitly filter out inactive methods to ensure they don't appear in the preview
  // This makes the preview exactly match what customers would see
  const availableMethods = zone 
    ? methods.filter(m => {
        return m.zone_id === zone.id && m.is_active === true;
      })
    : [];
    
  console.log('Preview methods:', availableMethods.length, 'for zone:', zone?.name, 'in country:', selectedCountry);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Shipping Preview</h3>
      <p className="text-sm text-gray-600">
        This shows exactly what your customers will see based on their location and cart total.
        <br />
        <strong>Note:</strong> Only shipping methods marked as active will appear here.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="country">Customer Country</Label>
          <Select value={selectedCountry} onValueChange={setSelectedCountry}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableCountries.map((country) => (
                <SelectItem key={country} value={country}>
                  {country}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="cart-total">Cart Total (£)</Label>
          <Input
            id="cart-total"
            type="number"
            value={cartTotal}
            onChange={(e) => setCartTotal(parseFloat(e.target.value) || 0)}
            min="0"
            step="0.01"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Available Shipping Options</CardTitle>
          <CardDescription>
            {zone ? `Shipping zone: ${zone.name}` : 'No shipping zone found for this country'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {availableMethods.length > 0 ? (
            <div className="space-y-3">
              {availableMethods.map((method) => {
                const isFree = method.free_shipping_threshold && cartTotal >= method.free_shipping_threshold;
                const price = isFree ? 0 : method.price;

                return (
                  <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getIconComponent(method.icon)}
                      <div>
                        <div className="font-medium">{method.name}</div>
                        <div className="text-sm text-gray-500">{method.description}</div>
                        <div className="text-sm text-gray-500">
                          {method.estimated_days_min === method.estimated_days_max
                            ? `${method.estimated_days_min} business day${method.estimated_days_min > 1 ? 's' : ''}`
                            : `${method.estimated_days_min}-${method.estimated_days_max} business days`
                          }
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">
                        {price === 0 ? 'FREE' : new Intl.NumberFormat('en-GB', {
                          style: 'currency',
                          currency: 'GBP'
                        }).format(price)}
                      </div>
                      {isFree && (
                        <div className="text-xs text-green-600">
                          Free shipping applied
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Package className="h-8 w-8 mx-auto mb-2" />
              <p>No shipping methods available for this location</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function getIconComponent(iconType: string) {
  switch (iconType) {
    case 'standard':
      return <Truck className="h-4 w-4" />;
    case 'express':
      return <Clock className="h-4 w-4" />;
    case 'nextDay':
      return <Zap className="h-4 w-4" />;
    case 'free':
      return <Gift className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
}

// Zone Dialog Component
function ZoneDialog({
  isOpen,
  onClose,
  zone,
  onSave
}: {
  isOpen: boolean;
  onClose: () => void;
  zone: ShippingZone | null;
  onSave: () => void;
}) {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    countries: [] as string[],
    is_active: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const availableCountries = shippingService.getAvailableCountries();

  useEffect(() => {
    if (zone) {
      setFormData({
        name: zone.name,
        description: zone.description || '',
        countries: zone.countries,
        is_active: zone.is_active
      });
    } else {
      setFormData({
        name: '',
        description: '',
        countries: [],
        is_active: true
      });
    }
  }, [zone, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (zone) {
        await shippingService.updateShippingZone(zone.id, formData);
        toast({
          title: 'Success',
          description: 'Shipping zone updated successfully'
        });
      } else {
        await shippingService.createShippingZone(formData);
        toast({
          title: 'Success',
          description: 'Shipping zone created successfully'
        });
      }
      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving zone:', error);
      toast({
        title: 'Error',
        description: 'Failed to save shipping zone',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleCountry = (country: string) => {
    setFormData(prev => ({
      ...prev,
      countries: prev.countries.includes(country)
        ? prev.countries.filter(c => c !== country)
        : [...prev.countries, country]
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {zone ? 'Edit Shipping Zone' : 'Create Shipping Zone'}
          </DialogTitle>
          <DialogDescription>
            Define a shipping zone and select the countries it covers.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="zone-name">Zone Name</Label>
            <Input
              id="zone-name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., United Kingdom, European Union"
              required
            />
          </div>

          <div>
            <Label htmlFor="zone-description">Description (Optional)</Label>
            <Textarea
              id="zone-description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of this shipping zone"
              rows={2}
            />
          </div>

          <div>
            <Label>Countries</Label>
            <div className="mt-2 max-h-48 overflow-y-auto border rounded-lg p-3">
              <div className="grid grid-cols-2 gap-2">
                {availableCountries.map((country) => (
                  <div key={country} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`country-${country}`}
                      checked={formData.countries.includes(country)}
                      onChange={() => toggleCountry(country)}
                      className="rounded"
                    />
                    <Label htmlFor={`country-${country}`} className="text-sm">
                      {country}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Selected: {formData.countries.length} countries
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="zone-active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="zone-active">Active</Label>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || formData.countries.length === 0}>
              {isSubmitting ? 'Saving...' : zone ? 'Update Zone' : 'Create Zone'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Method Dialog Component
function MethodDialog({
  isOpen,
  onClose,
  method,
  zones,
  onSave
}: {
  isOpen: boolean;
  onClose: () => void;
  method: ShippingMethod | null;
  zones: ShippingZone[];
  onSave: () => void;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    zone_id: '',
    name: '',
    description: '',
    price: 0,
    free_shipping_threshold: null as number | null,
    estimated_days_min: 1,
    estimated_days_max: 7,
    icon: 'standard' as 'standard' | 'express' | 'nextDay' | 'free',
    is_active: true,
    sort_order: 0
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (method) {
      setFormData({
        zone_id: method.zone_id,
        name: method.name,
        description: method.description,
        price: method.price,
        free_shipping_threshold: method.free_shipping_threshold,
        estimated_days_min: method.estimated_days_min,
        estimated_days_max: method.estimated_days_max,
        icon: method.icon,
        is_active: method.is_active,
        sort_order: method.sort_order
      });
    } else {
      setFormData({
        zone_id: zones[0]?.id || '',
        name: '',
        description: '',
        price: 0,
        free_shipping_threshold: null,
        estimated_days_min: 1,
        estimated_days_max: 7,
        icon: 'standard',
        is_active: true,
        sort_order: 0
      });
    }
  }, [method, zones, isOpen]);

  // Function to force a hard refresh of the browser
  const forceRefresh = () => {
    // Show a confirmation dialog
    if (confirm('To ensure changes take effect immediately, the page needs to be refreshed. Click OK to refresh now.')) {
      // Force a hard refresh to clear all caches
      window.location.reload();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (method) {
        await shippingService.updateShippingMethod(method.id, formData);
        
        // Invalidate all shipping-related caches to ensure checkout gets fresh data
        queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
        queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
        queryClient.invalidateQueries({ queryKey: ['shipping-calculations'] });
        
        toast({
          title: 'Success',
          description: 'Shipping method updated successfully'
        });
        
        // If we're changing the active status, force a refresh
        if (method.is_active !== formData.is_active) {
          setTimeout(forceRefresh, 500); // Short delay to allow the toast to show
          return; // Early return to prevent onClose which would be interrupted by refresh
        }
      } else {
        await shippingService.createShippingMethod(formData);
        
        // Invalidate all shipping-related caches
        queryClient.invalidateQueries({ queryKey: ['shipping-methods'] });
        queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
        queryClient.invalidateQueries({ queryKey: ['shipping-calculations'] });
        
        toast({
          title: 'Success',
          description: 'Shipping method created successfully'
        });
      }
      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving method:', error);
      toast({
        title: 'Error',
        description: 'Failed to save shipping method',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {method ? 'Edit Shipping Method' : 'Create Shipping Method'}
          </DialogTitle>
          <DialogDescription>
            Configure shipping method details and pricing.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="method-zone">Shipping Zone</Label>
            <Select
              value={formData.zone_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, zone_id: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a zone" />
              </SelectTrigger>
              <SelectContent>
                {zones.map((zone) => (
                  <SelectItem key={zone.id} value={zone.id}>
                    {zone.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="method-name">Method Name</Label>
            <Input
              id="method-name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Standard Shipping"
              required
            />
          </div>

          <div>
            <Label htmlFor="method-description">Description</Label>
            <Textarea
              id="method-description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description shown to customers"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="method-price">Price (£)</Label>
              <Input
                id="method-price"
                type="number"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                min="0"
                step="0.01"
                required
              />
            </div>

            <div>
              <Label htmlFor="free-threshold">Free Shipping Threshold (£)</Label>
              <Input
                id="free-threshold"
                type="number"
                value={formData.free_shipping_threshold || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  free_shipping_threshold: e.target.value ? parseFloat(e.target.value) : null
                }))}
                min="0"
                step="0.01"
                placeholder="Optional"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="days-min">Min Delivery Days</Label>
              <Input
                id="days-min"
                type="number"
                value={formData.estimated_days_min}
                onChange={(e) => setFormData(prev => ({ ...prev, estimated_days_min: parseInt(e.target.value) || 1 }))}
                min="1"
                required
              />
            </div>

            <div>
              <Label htmlFor="days-max">Max Delivery Days</Label>
              <Input
                id="days-max"
                type="number"
                value={formData.estimated_days_max}
                onChange={(e) => setFormData(prev => ({ ...prev, estimated_days_max: parseInt(e.target.value) || 7 }))}
                min="1"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="method-icon">Icon</Label>
            <Select
              value={formData.icon}
              onValueChange={(value: 'standard' | 'express' | 'nextDay' | 'free') =>
                setFormData(prev => ({ ...prev, icon: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standard">
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    Standard
                  </div>
                </SelectItem>
                <SelectItem value="express">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Express
                  </div>
                </SelectItem>
                <SelectItem value="nextDay">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Next Day
                  </div>
                </SelectItem>
                <SelectItem value="free">
                  <div className="flex items-center gap-2">
                    <Gift className="h-4 w-4" />
                    Free
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sort-order">Sort Order</Label>
              <Input
                id="sort-order"
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                min="0"
              />
            </div>

            <div className="flex items-center space-x-2 pt-6">
              <Switch
                id="method-active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              />
              <Label htmlFor="method-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.zone_id}>
              {isSubmitting ? 'Saving...' : method ? 'Update Method' : 'Create Method'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default ShippingManager;
