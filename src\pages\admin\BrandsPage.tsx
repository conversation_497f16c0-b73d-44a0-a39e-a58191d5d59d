import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Brand } from "@/types/database";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { PlusCircle, Edit, Trash, Loader2, X } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { BrandForm } from "@/components/admin/BrandForm";

export default function BrandsPage() {
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  
  // Get brand ID from URL if present
  useEffect(() => {
    const brandId = searchParams.get('edit');
    if (brandId && brandId !== 'new') {
      // Fetch the brand details if we have an ID
      const fetchBrand = async () => {
        // Use a more generic approach to avoid TypeScript errors
        // Use direct table access instead of RPC
        const { data, error } = await (supabase as any)
          .from('brands')
          .select('*')
          .eq('id', brandId)
          .single();
          
        if (error) {
          console.error('Error fetching brand:', error);
          return;
        }
        
        if (data) {
          // Cast the data to Brand type
          setSelectedBrand(data as unknown as Brand);
          setIsEditMode(true);
        }
      };
      
      fetchBrand();
    } else if (brandId === 'new') {
      setSelectedBrand(null);
      setIsEditMode(true);
    }
  }, [searchParams]);

  // Fetch brands with product counts
  const {
    data: brands,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["brands"],
    queryFn: async () => {
      // Get all brands
      const { data: brandsData, error: brandsError } = await (supabase as any)
        .from('brands')
        .select('*')
        .order('name');

      if (brandsError) {
        console.error('Error fetching brands:', brandsError);
        throw brandsError;
      }
      
      // Log the brands data
      console.log('Brands data:', brandsData);
      
      // Get all products with brand_id
      const { data: productsData, error: productsError } = await (supabase as any)
        .from('products')
        .select('brand_id');
        
      if (productsError) {
        console.error('Error fetching products:', productsError);
        // Return brands without counts if products query fails
        return brandsData;
      }
      
      // Log the products data
      console.log('Products data:', productsData);
      
      // Count products for each brand
      const brandCounts = {};
      productsData.forEach((product: any) => {
        if (product.brand_id) {
          brandCounts[product.brand_id] = (brandCounts[product.brand_id] || 0) + 1;
        }
      });
      
      // Add product counts to brands
      const brandsWithCounts = brandsData.map((brand: any) => ({
        ...brand,
        productCount: brandCounts[brand.id] || 0
      }));
      
      return brandsWithCounts as unknown as (Brand & { productCount: number })[];
    },
  });

  // Delete brand mutation
  const deleteBrand = useMutation({
    mutationFn: async (brandId: string) => {
      // Use a more generic approach to avoid TypeScript errors
      // Use direct table access instead of RPC
      const { error } = await (supabase as any)
        .from('brands')
        .delete()
        .eq('id', brandId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Brand deleted", {
        description: "The brand has been successfully deleted.",
        duration: 3000,
      });
      queryClient.invalidateQueries({ queryKey: ["brands"] });
      setIsDeleteDialogOpen(false);
    },
    onError: (error) => {
      toast.error("Error", {
        description: `Failed to delete brand: ${error instanceof Error ? error.message : "Unknown error"}`,
        duration: 5000, // Longer duration for error messages (5 seconds)
      });
    },
  });

  const handleEdit = (brand: Brand) => {
    // Update URL with brand ID to edit
    navigate(`/admin/brands?edit=${brand.id}`);
  };

  const handleDelete = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseEdit = () => {
    setSelectedBrand(null);
    setIsEditMode(false);
    // Remove edit parameter from URL
    navigate('/admin/brands');
  };
  
  const handleAddNew = () => {
    setSelectedBrand(null);
    setIsEditMode(true);
    // Update URL to indicate we're adding a new brand
    navigate('/admin/brands?edit=new');
  };
  
  const handleFormSuccess = () => {
    setSelectedBrand(null);
    setIsEditMode(false);
    // Remove edit parameter from URL
    navigate('/admin/brands');
  };

  return (
    <div className="container-custom py-8 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Brands</h1>
        {!isEditMode && (
          <Button onClick={handleAddNew}>
            <PlusCircle className="mr-2 h-4 w-4" /> Add Brand
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-10 w-10 animate-spin text-sage-500" />
        </div>
      ) : error ? (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>
              Failed to load brands. Please try again.
            </CardDescription>
          </CardHeader>
        </Card>
      ) : brands && brands.length > 0 ? (
        <Card className="w-full">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Logo</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {brands.map((brand) => (
                  <TableRow key={brand.id}>
                    <TableCell className="font-medium">{brand.name}</TableCell>
                    <TableCell>
                      {'logo' in brand && brand.logo ? (
                        <img 
                          src={brand.logo} 
                          alt={`${brand.name} logo`} 
                          className="h-8 w-auto object-contain"
                        />
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell>
                      {'productCount' in brand ? (
                        brand.productCount > 0 ? (
                          <span className="font-medium">
                            {brand.productCount} {brand.productCount === 1 ? 'product' : 'products'}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">No products</span>
                        )
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(brand)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(brand)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>No Brands Found</CardTitle>
            <CardDescription>
              You haven't added any brands to your store yet.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              onClick={handleAddNew}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add Your First Brand
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Inline Brand Form */}
      {isEditMode && (
        <Card className="w-full mt-6">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>{selectedBrand ? "Edit Brand" : "Add New Brand"}</CardTitle>
              <CardDescription>
                Enter the brand details below to {selectedBrand ? "update" : "create"} a brand.
              </CardDescription>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleCloseEdit}
              aria-label="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <BrandForm
              brand={selectedBrand as Brand}
              onSuccess={handleFormSuccess}
              onCancel={handleCloseEdit}
            />
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => setIsDeleteDialogOpen(open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              {selectedBrand && 'name' in selectedBrand ? (
                <>Are you sure you want to delete "{selectedBrand.name}"? This action cannot be undone.</>
              ) : (
                <>Are you sure you want to delete this brand? This action cannot be undone.</>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedBrand && deleteBrand.mutate(selectedBrand.id)}
              disabled={deleteBrand.isPending}
            >
              {deleteBrand.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
