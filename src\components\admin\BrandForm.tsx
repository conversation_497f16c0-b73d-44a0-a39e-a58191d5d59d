import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Brand } from "@/types/database";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

interface BrandFormProps {
  brand: Brand | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function BrandForm({ brand, onSuccess, onCancel }: BrandFormProps) {
  const queryClient = useQueryClient();
  // Initialize form data from localStorage or props
  const [formData, setFormData] = useState<Partial<Brand>>(() => {
    // If editing an existing brand, use the brand data
    if (brand) {
      return brand;
    }
    
    // If creating a new brand, check localStorage for saved form data
    const savedForm = localStorage.getItem('brandFormData');
    if (savedForm) {
      try {
        return JSON.parse(savedForm);
      } catch (e) {
        console.error('Error parsing saved form data:', e);
      }
    }
    
    // Default empty form
    return {
      name: "",
      slug: "",
      description: "",
      logo: "",
    };
  });
  
  // Save form data to localStorage whenever it changes
  useEffect(() => {
    // Don't save if we're editing an existing brand
    if (!brand) {
      localStorage.setItem('brandFormData', JSON.stringify(formData));
    }
  }, [formData, brand]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const generateUniqueSlug = (name: string) => {
    // Base slug from name
    let slug = name
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
      
    // If the slug is too short (less than 3 chars), add a timestamp to ensure uniqueness
    if (slug.length < 3) {
      // Add a timestamp suffix to ensure uniqueness
      const timestamp = new Date().getTime().toString().slice(-6);
      slug = `${slug}-${timestamp}`;
    }
    
    return slug;
  };
  
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Auto-generate slug from name if slug is empty
    if (name === "name" && (!formData.slug || formData.slug === "")) {
      const slug = generateUniqueSlug(value);
      setFormData((prev) => ({ ...prev, slug }));
    }
  };

  const saveBrand = useMutation({
    mutationFn: async (data: Partial<Brand>) => {
      // If no slug is provided, generate one from the name
      if (!data.slug && data.name) {
        data.slug = data.name
          .toLowerCase()
          .replace(/[^\w\s-]/g, "")
          .replace(/[\s_-]+/g, "-")
          .replace(/^-+|-+$/g, "");
      }

      if (brand) {
        // Update existing brand
        // Use direct table access instead of RPC
        const { data: updatedBrand, error } = await (supabase as any)
          .from('brands')
          .update({
            name: data.name,
            slug: data.slug,
            description: data.description,
            logo: data.logo,
            updated_at: new Date().toISOString()
          })
          .eq('id', brand.id)
          .select()
          .single();

        if (error) throw error;
        return updatedBrand;
      } else {
        // Check if a brand with the same slug already exists
        const { data: existingBrand, error: checkError } = await (supabase as any)
          .from('brands')
          .select('id, name')
          .eq('slug', data.slug)
          .maybeSingle();
          
        if (checkError) {
          console.error('Error checking for existing brand:', checkError);
        }
        
        // If a brand with this slug already exists, throw an error
        if (existingBrand) {
          throw new Error(`A brand with the slug "${data.slug}" already exists. Please use a different name.`);
        }
        
        // Create new brand
        const { data: newBrand, error } = await (supabase as any)
          .from('brands')
          .insert({
            name: data.name,
            slug: data.slug,
            description: data.description,
            logo: data.logo
          })
          .select()
          .single();

        if (error) {
          // Handle specific error cases
          if (error.code === '23505') { // PostgreSQL unique violation code
            throw new Error(`A brand with this name or slug already exists. Please use a different name.`);
          }
          throw error;
        }
        
        return newBrand;
      }
    },
    onSuccess: () => {
      toast.success(brand ? "Brand updated" : "Brand created", {
        description: `The brand has been successfully ${
          brand ? "updated" : "created"
        }.`,
        duration: 3000, // Explicitly set duration to 3 seconds
      });
      queryClient.invalidateQueries({ queryKey: ["brands"] });
      
      // Clear localStorage after successful submission
      localStorage.removeItem('brandFormData');
      
      // Force a small delay to ensure the toast is shown before navigation
      setTimeout(() => {
        onSuccess();
      }, 100);
    },
    onError: (error) => {
      toast.error("Error", {
        description: `Failed to ${brand ? "update" : "create"} brand: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        duration: 5000, // Longer duration for error messages
      });
      setIsSubmitting(false);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate the form data
    if (!formData.name) {
      toast({
        title: "Validation Error",
        description: "Brand name is required.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    try {
      await saveBrand.mutateAsync(formData);
    } catch (error) {
      // Error is handled in the mutation
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Brand Name*</Label>
          <Input
            id="name"
            name="name"
            value={formData.name ?? ""}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="slug">Slug</Label>
          <Input
            id="slug"
            name="slug"
            value={formData.slug ?? ""}
            onChange={handleChange}
            placeholder="auto-generated-if-empty"
          />
          <div className="text-xs text-gray-500 space-y-1">
            <p>Leave empty to auto-generate from name</p>
            <p>Must be unique across all brands</p>
            {formData.slug && formData.slug.length < 3 && (
              <p className="text-amber-500">
                Warning: Short slugs may cause conflicts. Consider using a longer, more descriptive slug.
              </p>
            )}
          </div>
        </div>



        <div className="space-y-2">
          <Label htmlFor="logo">Logo URL</Label>
          <Input
            id="logo"
            name="logo"
            value={formData.logo ?? ""}
            onChange={handleChange}
            placeholder="https://example.com/logo.png"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description ?? ""}
            onChange={handleChange}
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {brand ? "Updating..." : "Creating..."}
            </>
          ) : (
            <>{brand ? "Update" : "Create"} Brand</>
          )}
        </Button>
      </div>
    </form>
  );
}
