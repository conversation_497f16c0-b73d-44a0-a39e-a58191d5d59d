import { useEffect, useState } from 'react';
import { Product } from '@/types/database';

/**
 * Custom hook to handle product description formatting
 * Ensures that plain text descriptions are properly converted to HTML for the rich text editor
 */
export function useProductDescription(product?: Partial<Product>) {
  const [formattedDescription, setFormattedDescription] = useState<string>('');
  
  useEffect(() => {
    if (!product?.description) {
      setFormattedDescription('');
      return;
    }
    
    const description = product.description;
    
    // Check if the description already contains HTML
    const hasHtml = /<[a-z][\s\S]*>/i.test(description);
    
    if (hasHtml) {
      // Description already has HTML, use it as is
      setFormattedDescription(description);
    } else {
      // Convert plain text to HTML
      const formattedText = description
        .split('\n\n')
        .map(para => para.trim() ? `<p>${para.trim()}</p>` : '')
        .join('');
      
      setFormattedDescription(formattedText || description);
    }
  }, [product?.description]);
  
  return { formattedDescription };
}
