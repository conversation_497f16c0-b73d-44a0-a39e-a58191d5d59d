import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { newsletterTemplates, generateNewsletterFromTemplate, NewsletterTemplateData } from '@/lib/newsletter-templates';

const TestNewsletterTemplates = () => {
  const [selectedTemplate, setSelectedTemplate] = useState('classic');
  const [generatedHtml, setGeneratedHtml] = useState('');

  // Sample data for testing
  const sampleData: NewsletterTemplateData = {
    subject: 'Weekly Cannabis & CBD Newsletter',
    content: `
# Welcome to Our Weekly Update!

We're excited to share the latest news and products from the world of premium CBD and cannabis accessories.

## This Week's Highlights

🌿 **New Arrivals**: Fresh stock of premium CBD oils and edibles
🔥 **Special Offers**: 20% off all rolling papers this week
📚 **Education**: Learn about the benefits of different CBD strains

## Featured Categories

- **CBD Oils & Tinctures**: Premium quality, lab-tested products
- **Smoking Accessories**: Bongs, pipes, and vaporizers
- **Rolling Papers**: RAW, OCB, and other premium brands
- **Seeds**: Autoflowering and feminized varieties

Thank you for being part of our community!

Best regards,
The Bits N Bongs Team
    `,
    products: [
      {
        id: '1',
        name: 'Premium CBD Oil 1000mg',
        price: 45.99,
        sale_price: 35.99,
        image: '/images/products/wix-imports/7caa35_1afa94f90b494fc4ae601851b8a12712.webp'
      },
      {
        id: '2',
        name: 'RAW Classic Rolling Papers',
        price: 2.50,
        image: '/images/products/wix-imports/7caa35_da4d80640eeb4b93ac9b80ca1b16daad.webp'
      },
      {
        id: '3',
        name: 'Glass Water Pipe - Blue',
        price: 89.99,
        sale_price: 69.99,
        image: '/images/products/wix-imports/7caa35_3a19b716edab485fa14dcdaf8f4907e6.webp'
      }
    ],
    unsubscribeUrl: 'https://bitsnbongs.com/unsubscribe'
  };

  const generatePreview = () => {
    try {
      const html = generateNewsletterFromTemplate(selectedTemplate, sampleData);
      setGeneratedHtml(html);
    } catch (error) {
      console.error('Error generating template:', error);
    }
  };

  const openInNewWindow = () => {
    if (generatedHtml) {
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(generatedHtml);
        newWindow.document.close();
      }
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Newsletter Template Preview</h1>
        <p className="text-gray-600">
          Preview the different newsletter templates with sample content and products.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Template Selection</CardTitle>
            <CardDescription>
              Choose a template and generate a preview with sample data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Newsletter Template</label>
              <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a template" />
                </SelectTrigger>
                <SelectContent>
                  {newsletterTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-gray-500">{template.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Button onClick={generatePreview} className="w-full">
                Generate Preview
              </Button>
              {generatedHtml && (
                <Button onClick={openInNewWindow} variant="outline" className="w-full">
                  Open in New Window
                </Button>
              )}
            </div>

            <div className="text-sm text-gray-600">
              <h4 className="font-medium mb-2">Sample Data Includes:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Newsletter subject and content</li>
                <li>3 featured products with images</li>
                <li>Branded header and footer</li>
                <li>Responsive design for mobile</li>
                <li>Professional styling and colors</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Live Preview</CardTitle>
            <CardDescription>
              See how your newsletter will look to subscribers
            </CardDescription>
          </CardHeader>
          <CardContent>
            {generatedHtml ? (
              <div className="border rounded-md overflow-hidden">
                <iframe
                  srcDoc={generatedHtml}
                  className="w-full h-96"
                  title="Newsletter Preview"
                  style={{ border: 'none' }}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-96 bg-gray-50 rounded-md">
                <div className="text-center text-gray-500">
                  <p className="mb-2">No preview generated yet</p>
                  <p className="text-sm">Click "Generate Preview" to see the template</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Template Information */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Available Templates</CardTitle>
            <CardDescription>
              Overview of all available newsletter templates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {newsletterTemplates.map(template => (
                <div key={template.id} className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  <p className="text-xs text-gray-500">{template.preview}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestNewsletterTemplates;
