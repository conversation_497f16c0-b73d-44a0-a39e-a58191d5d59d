-- Disable <PERSON><PERSON> temporarily for testing
ALTER TABLE public.addresses DISABLE ROW LEVEL SECURITY;

-- Create a policy that allows all operations for all users (for testing)
DROP POLICY IF EXISTS "Allow all operations" ON public.addresses;
CREATE POLICY "Allow all operations" 
  ON public.addresses 
  FOR ALL 
  TO authenticated 
  USING (true);

-- Make sure permissions are granted
GRANT ALL ON public.addresses TO authenticated;
