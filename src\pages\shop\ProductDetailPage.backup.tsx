import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Product } from '@/types/database';
import { Loader2, ShoppingBag, Info, ArrowLeft, Star, StarHalf, Share2, Facebook, Twitter, Linkedin, Copy, Check } from 'lucide-react';
import { ProductImageGallery } from '@/components/products/ProductImageGallery';
import { VisualOptionCard } from '@/components/products/VisualOptionCard';
import { RelatedProducts } from '@/components/products/RelatedProducts';
import { useCart } from '@/hooks/useCart';
import { toast } from '@/components/ui/use-toast';

const ProductDetailPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [quantity, setQuantity] = useState<number>(1);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});
  const [calculatedPrice, setCalculatedPrice] = useState<number | null>(null);
  const [priceAdjustment, setPriceAdjustment] = useState<number>(0);
  const [showShareTooltip, setShowShareTooltip] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data based on slug
  const fetchProduct = async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (!slug) {
        console.error('No slug provided');
        setLoading(false);
        return;
      }
      
      // Try to fetch product by exact slug match first
      const { data: initialProductData, error: initialError } = await supabase
        .from('products')
        .select('*')
        .eq('slug', slug)
        .single();
      
      let finalProductData = initialProductData;
      
      // If not found, try case-insensitive search
      if (initialError || !finalProductData) {
        console.log('Product not found by exact slug match, trying case-insensitive search');
        const { data: products, error: searchError } = await supabase
          .from('products')
          .select('*')
          .ilike('slug', `%${slug}%`);
        
        if (searchError) throw searchError;
        
        if (products && products.length > 0) {
          // Find the closest match
          finalProductData = products.find(p => p.slug.toLowerCase() === slug.toLowerCase()) || products[0];
          console.log('Found product by case-insensitive search:', finalProductData);
        }
      }
      
      // If still not found, try partial match
      if (!finalProductData) {
        console.log('Product not found by case-insensitive search, trying partial match');
        const { data: products, error: partialError } = await supabase
          .from('products')
          .select('*')
          .ilike('slug', `%${slug.split('-')[0]}%`);
        
        if (partialError) throw partialError;
        
        if (products && products.length > 0) {
          finalProductData = products[0];
          console.log('Found product by partial match:', finalProductData);
        }
      }
      
      // Last resort: check if slug is actually a UUID
      if (!finalProductData) {
        // Only try this if the slug looks like a UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (uuidRegex.test(slug)) {
          console.log('Slug appears to be a UUID, checking product by ID');
          const { data: uuidProduct, error: uuidError } = await supabase
            .from('products')
            .select('*')
            .eq('id', slug)
            .single();
          
          if (!uuidError && uuidProduct) {
            finalProductData = uuidProduct;
            console.log('Found product by UUID:', finalProductData);
          }
        } else {
          console.log('Slug is not a UUID format, skipping UUID check');
        }
      }
      
      if (!finalProductData) {
        throw new Error('Product not found');
      }
      
      // Process the product data
      processProductData(finalProductData);
      
    } catch (error) {
      console.error('Error in fetchProduct:', error);
      setError('Product not found. Please try again or contact support.');
      setLoading(false);
    }
  };

  // Helper function to process product data
  const processProductData = (productData: Product) => {
    console.log('Processing product data:', productData);
    
    // Process images
    if (productData.image && !productData.images) {
      productData.images = [productData.image];
    }
    
    // Set the product in state
    setProduct(productData);
    
    // Calculate initial price
    if (productData.price) {
      setCalculatedPrice(productData.price);
    }
    
    // Set loading to false
    setLoading(false);
      
    // Process options from legacy format if needed
    if (!productData.options) {
      // Process options if they exist
      if (productData.option_name1) {
        const optionsByName: Record<string, Set<string>> = {};
        const priceAdjustments: Record<string, Record<string, number>> = {};
        
        // Process up to 3 options
        for (let i = 1; i <= 3; i++) {
          const nameKey = `option_name${i}` as keyof Product;
          const typeKey = `option_type${i}` as keyof Product;
          const adjustmentKey = `option_price_adjustment${i}` as keyof Product;
          
          const optionName = productData[nameKey] as string;
          const optionType = productData[typeKey] as string;
          const optionAdjustment = productData[adjustmentKey] as string;
          
          if (optionName && optionType) {
            // Initialize collections if this is the first time seeing this option name
            if (!optionsByName[optionName]) {
              optionsByName[optionName] = new Set<string>();
              priceAdjustments[optionName] = {};
            }
            
            // Handle single value case
            if (!optionType.includes(',') && !optionType.includes(';')) {
              optionsByName[optionName].add(optionType.trim());
              
              // Process price adjustment for single value
              const adjustment = parseFloat(optionAdjustment) || 0;
              priceAdjustments[optionName][optionType.trim()] = adjustment;
            }
            else {
              // Handle multiple values with separators
              const optionValues = optionType.split(/[;,]/).map(v => v.trim()).filter(Boolean);
              const adjustmentStr = optionAdjustment || '';
              const adjustments = adjustmentStr.split(/[;,]/).map(a => parseFloat(a.trim()) || 0);
              
              console.log(`Processing option ${optionName}:`, {
                values: optionValues,
                adjustments: adjustments
              });
              
              optionValues.forEach((value, index) => {
                optionsByName[optionName].add(value);
                
                if (index < adjustments.length) {
                  priceAdjustments[optionName][value] = adjustments[index];
                  console.log(`Set price adjustment for ${optionName}[${value}] = ${adjustments[index]}`);
                } else {
                  console.log(`No price adjustment found for ${optionName}[${value}] at index ${index}`);
                }
              });
            }
          }
        }
        
        // Convert sets to arrays for the final options object
        const options: Record<string, string[]> = {};
        Object.entries(optionsByName).forEach(([name, valuesSet]) => {
          options[name] = Array.from(valuesSet);
        });
        
        console.log('Collected options:', options);
        console.log('Collected price adjustments:', priceAdjustments);
        
        if (Object.keys(options).length > 0) {
          productData.options = options;
          productData.price_adjustments = priceAdjustments;
        }
      }
    }
    
    // Ensure additional_info is processed
    if (!productData.additional_info) {
      // Check for additional_info fields
      const additionalInfoParts = [];
      
      for (let i = 1; i <= 3; i++) {
        const titleKey = `additional_info_title${i}` as keyof Product;
        const descKey = `additional_info_description${i}` as keyof Product;
        
        if (productData[titleKey] && productData[descKey]) {
          additionalInfoParts.push(
            `${productData[titleKey]}: ${productData[descKey]}`
          );
        }
      }
      
      if (additionalInfoParts.length > 0) {
        productData.additional_info = additionalInfoParts.join('\n\n');
      }
      }
      
      setProduct(productData);
      
      // Initialize default options
      const defaultOptions: Record<string, string> = {};
      if (productData.options) {
        Object.keys(productData.options).forEach(optionName => {
          const optionValues = productData.options[optionName];
          if (optionValues && optionValues.length > 0) {
            defaultOptions[optionName] = optionValues[0];
          }
        });
      }
      
      setSelectedOptions(defaultOptions);
      
      // Calculate initial price
      const initialPrice = calculatePrice(productData, defaultOptions);
      setCalculatedPrice(initialPrice.finalPrice);
      setPriceAdjustment(initialPrice.adjustment);
    } catch (error) {
      console.error('Error in fetchProduct:', error);
      setLoading(false);
    }
  };

  // Calculate price based on selected options
  const calculatePrice = (prod: Product, options: Record<string, string>) => {
    if (!prod || !prod.options) {
      return { finalPrice: prod?.price || 0, adjustment: 0 };
    }
    
    let adjustment = 0;
    let basePrice = prod.sale_price || prod.price || 0;
    
    console.log('Base price:', basePrice, '(type:', typeof basePrice, ')');
    console.log('Selected options:', options);
    console.log('Available price adjustments:', prod.price_adjustments);
    
    // Apply price adjustments based on options
    if (Object.keys(options).length > 0) {
      try {
        // Try modern price_adjustments format first
        if (prod.price_adjustments && Object.keys(prod.price_adjustments).length > 0) {
          // Create a normalized map of price adjustments for case-insensitive lookup
          const normalizedAdjustments: Record<string, Record<string, number>> = {};
          
          // Normalize all price adjustment keys to lowercase and dump all values for debugging
          console.log('Raw price adjustments before normalization:', JSON.stringify(prod.price_adjustments));
          
          Object.entries(prod.price_adjustments).forEach(([optionName, values]) => {
            const normalizedName = optionName.toLowerCase();
            
            // Initialize if not exists
            if (!normalizedAdjustments[normalizedName]) {
              normalizedAdjustments[normalizedName] = {};
            }
            
            // Copy all values from this option
            if (values && typeof values === 'object') {
              Object.entries(values).forEach(([valueName, adjustment]) => {
                normalizedAdjustments[normalizedName][valueName] = Number(adjustment);
                console.log(`Mapped adjustment: ${normalizedName}[${valueName}] = ${adjustment}`);
              });
            }
          });
          
          console.log('Normalized price adjustments:', normalizedAdjustments);
          
          Object.entries(options).forEach(([optionName, selectedValue]) => {
            try {
              // Use normalized (lowercase) option name for lookup
              const normalizedName = optionName.toLowerCase();
              const optionAdjustments = normalizedAdjustments[normalizedName];
              
              if (optionAdjustments && optionAdjustments[selectedValue] !== undefined) {
                // Ensure the adjustment is a proper number
                const optionAdjustment = Number(optionAdjustments[selectedValue]);
                console.log(`Adjustment for ${normalizedName}=${selectedValue}: ${optionAdjustment}`);
                
                if (!isNaN(optionAdjustment)) {
                  adjustment += optionAdjustment;
                  console.log(`Running adjustment total: ${adjustment}`);
                } else {
                  console.warn(`Invalid adjustment value for ${normalizedName}=${selectedValue}: ${optionAdjustments[selectedValue]}`);
                }
              } else {
                console.log(`No adjustment found for ${normalizedName}=${selectedValue}`);
              }
            } catch (err) {
              console.error(`Error processing adjustment for ${optionName}=${selectedValue}:`, err);
            }
          });
        } 
        // Fall back to legacy format with option_price_adjustment fields
        else {
          for (let i = 1; i <= 3; i++) {
            const nameKey = `option_name${i}` as keyof Product;
            const typeKey = `option_type${i}` as keyof Product;
            const adjustmentKey = `option_price_adjustment${i}` as keyof Product;
            
            if (prod[nameKey] && prod[adjustmentKey] && typeof prod[nameKey] === 'string') {
              const optionName = (prod[nameKey] as string).toLowerCase(); // Normalize to lowercase
              
              // Find the matching option by checking case-insensitive
              const matchingOptionKey = Object.keys(options).find(
                key => key.toLowerCase() === optionName
              );
              
              if (matchingOptionKey) {
                const selectedValue = options[matchingOptionKey];
                const optionType = prod[typeKey] as string || '';
                const adjustmentStr = prod[adjustmentKey] as string || '';
                
                // Handle multiple values with separators
                const optionValues = optionType.split(/[;,]/).map(v => v.trim()).filter(Boolean);
                const adjustments = adjustmentStr.split(/[;,]/).map(a => parseFloat(a.trim()) || 0);
                
                const valueIndex = optionValues.indexOf(selectedValue);
                
                if (valueIndex >= 0 && valueIndex < adjustments.length) {
                  const optionAdjustment = adjustments[valueIndex];
                  console.log(`Legacy adjustment for ${optionName}=${selectedValue} at index ${valueIndex}: ${optionAdjustment}`);
                  adjustment += optionAdjustment;
                } else {
                  console.log(`No legacy adjustment found for ${optionName}=${selectedValue} (index ${valueIndex} out of range)`);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error calculating price adjustments:', error);
      }
    }
    
    // Calculate final price with proper number handling
    const finalPrice = Number(basePrice) + Number(adjustment);
    
    console.log(`Final price calculation:`);
    console.log(`- Base price: ${basePrice} (type: ${typeof basePrice})`);
    console.log(`- Total adjustment: ${adjustment} (type: ${typeof adjustment})`);
    console.log(`- Final price: ${finalPrice}`);
    
    return { finalPrice, adjustment };
  };

  // Handle option change
  const handleOptionChange = (optionName: string, value: string) => {
    // Normalize option name to lowercase to avoid case sensitivity issues
    const normalizedOptionName = optionName.toLowerCase();
    
    console.log(`Option change: ${normalizedOptionName} = ${value}`);
    
    // Create new options object with the selected value, removing any duplicates with different cases
    const newOptions: Record<string, string> = {};
    
    // First, copy all existing options except those with the same name (case-insensitive)
    Object.entries(selectedOptions).forEach(([key, val]) => {
      if (key.toLowerCase() !== normalizedOptionName) {
        newOptions[key] = val;
      }
    });
    
    // Then add the new option with normalized name
    newOptions[normalizedOptionName] = value;
    
    console.log('New options (normalized):', newOptions);
    
    // Update state using a callback to ensure we have the latest state
    setSelectedOptions(prevOptions => {
      // Recalculate price inside the callback to ensure we use the latest options
      if (product) {
        console.log('Recalculating price with normalized options:', newOptions);
        const newPrice = calculatePrice(product, newOptions);
        console.log(`New price calculation: finalPrice=${newPrice.finalPrice}, adjustment=${newPrice.adjustment}`);
        
        // Update price states immediately
        setCalculatedPrice(newPrice.finalPrice);
        setPriceAdjustment(newPrice.adjustment);
      }
      
      return newOptions;
    });
  };

  // Handle quantity change
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setQuantity(value);
    }
  };

  // Add to cart function
  const handleAddToCart = () => {
    if (!product) return;
    
    // Make sure selectedOptions is a plain object that can be serialized properly
    const serializedOptions = {};
    Object.entries(selectedOptions).forEach(([key, value]) => {
      // Ensure keys and values are strings
      serializedOptions[String(key)] = String(value);
    });
    
    // Calculate the final price to ensure it's correct
    // This recalculates to make sure we have the latest price
    const { finalPrice, adjustment } = calculatePrice(product, selectedOptions);
    
    // Prepare selected options and price information
    const optionsInfo = {
      selectedOptions: serializedOptions,
      priceAdjustment: adjustment,
      calculatedPrice: finalPrice
    };
    
    console.log('Adding to cart with options:', optionsInfo);
    console.log(`Final price: ${finalPrice} (base: ${product.price}, adjustment: ${adjustment})`);
    
    try {
      // Add to cart using context - pass product ID, quantity, and options
      addToCart(product.id, quantity, optionsInfo);
      
      // Format options for display in toast
      const optionsText = Object.entries(selectedOptions)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      
      // Format price for display
      const priceText = adjustment !== 0 
        ? `$${finalPrice.toFixed(2)} (${adjustment > 0 ? '+' : ''}$${adjustment.toFixed(2)})` 
        : `$${finalPrice.toFixed(2)}`;
      
      // Show toast notification
      toast({
        title: 'Added to Cart',
        description: `Added ${quantity} ${product.name} at ${priceText}${optionsText ? ` (${optionsText})` : ''} to your cart!`,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: 'Error',
        description: 'Failed to add item to cart',
        variant: 'destructive',
      });
    }
  };

  // Handle sharing
  const handleShare = (platform: string) => {
    if (!product) return;
    
    const url = window.location.href;
    const text = `Check out ${product.name} on Bits N Bongs!`;
    
    // Show tooltip
    setShowShareTooltip(true);
    setTimeout(() => setShowShareTooltip(false), 2000);
    
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        break;
    }
  };
  
  // Fetch product on component mount or when slug changes
  useEffect(() => {
    fetchProduct();
    // Scroll to the top of the page when the component mounts or slug changes
    window.scrollTo(0, 0);
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center">
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="text-gray-600 mb-6">Sorry, we couldn\'t find the product you\'re looking for.</p>
        <Link to="/shop" className="text-primary hover:underline flex items-center gap-2">
          <ArrowLeft size={16} />
          Back to Shop
        </Link>
      </div>
    );
  }

  // Generate cannabis leaf elements for the background
  const leafElements = [];
  for (let i = 0; i < 25; i++) { // Increased from 12 to 25 leaves
    // Generate random positions, sizes, and animation delays
    const size = Math.floor(Math.random() * 80) + 60; // Random size between 60-140px (increased)
    const left = Math.floor(Math.random() * 100); // Random left position
    const top = Math.floor(Math.random() * 100); // Random top position
    const delay = Math.floor(Math.random() * 10); // Random animation delay
    const rotateDelay = Math.floor(Math.random() * 5); // Different delay for rotation
    const opacity = (Math.random() * 0.3) + 0.15; // Random opacity between 0.15-0.45
    
    leafElements.push(
      <div 
        key={`leaf-${i}`}
        className="absolute z-0"
        style={{
          left: `${left}%`,
          top: `${top}%`,
          opacity: opacity,
          transform: `rotate(${Math.random() * 360}deg)`,
          filter: 'drop-shadow(0 0 2px rgba(108, 132, 80, 0.3))',
        }}
      >
<img 
  src="/images/Cannabis_leaf.svg" 
  alt="" 
  className="animate-float-leaf" 
  style={{
    width: `${size}px`,
    height: `${size}px`,
    animationDelay: `${delay}s`,
    animationDuration: `${15 + delay}s`,
  }}
/>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 relative overflow-hidden">
      {/* Background blur elements */}
      <div className="absolute -top-20 -right-20 w-64 h-64 bg-primary/10 rounded-full filter blur-3xl opacity-70 z-0"></div>
      <div className="absolute top-40 -left-20 w-72 h-72 bg-blue-200/20 rounded-full filter blur-3xl opacity-60 z-0"></div>
      <div className="absolute bottom-40 right-20 w-80 h-80 bg-amber-200/10 rounded-full filter blur-3xl opacity-50 z-0"></div>
      
      {/* Cannabis leaf background elements */}
      {leafElements}
      
      <div className="relative z-10">
        {/* Navigation and sharing row */}
        <div className="mb-6 flex justify-between items-center">
          <button 
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-gray-600 hover:text-primary transition-colors"
          >
            <ArrowLeft size={16} />
            Back to previous page
          </button>
          
          {/* Social sharing buttons - small and subtle */}
          <div className="flex items-center gap-2 relative">
            <button
              onClick={() => handleShare('facebook')}
              className="text-facebook hover:bg-facebook/10 p-2 rounded-full transition-colors"
              title="Share on Facebook"
            >
              <Facebook size={16} />
            </button>
            <button
              onClick={() => handleShare('twitter')}
              className="text-twitter hover:bg-twitter/10 p-2 rounded-full transition-colors"
              title="Share on Twitter"
            >
              <Twitter size={16} />
            </button>
            <button
              onClick={() => handleShare('linkedin')}
              className="text-linkedin hover:bg-linkedin/10 p-2 rounded-full transition-colors"
              title="Share on LinkedIn"
            >
              <Linkedin size={16} />
            </button>
            <button
              onClick={() => handleShare('copy')}
              className="text-gray-500 hover:bg-gray-100 p-2 rounded-full transition-colors"
              title="Copy Link"
            >
              {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
            </button>
            {showShareTooltip && (
              <div className="bg-black text-white text-xs px-2 py-1 rounded absolute right-0 -bottom-8">
                {copied ? 'Link copied!' : 'Share this product'}
              </div>
            )}
          </div>
        </div>
        
        {/* Main product grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left column - Product image */}
          <div>
            <ProductImageGallery
              mainImage={product.image}
              additionalImages={product.additional_images || []}
              productName={product.name}
            />
            
            {/* Product Specifications - Now under the image */}
            <div className="backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md mt-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-800">
                <Info size={18} className="text-primary/80" />
                Product Specifications
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.weight && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Weight:</span>
                    <span className="text-gray-800">{product.weight}</span>
                  </div>
                )}
                {product.size && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Size:</span>
                    <span className="text-gray-800">{product.size}</span>
                  </div>
                )}
                {product.dimensions && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Dimensions:</span>
                    <span className="text-gray-800">{product.dimensions}</span>
                  </div>
                )}
                {(!product.weight && !product.size && !product.dimensions) && (
                  <div className="text-gray-500 italic p-2 bg-white/30 rounded-lg">
                    No specifications available for this product.
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Right column - Product details */}
          <div>
            {/* Product Title */}
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{product.name}</h1>

            {/* Rating Stars */}
            <div className="flex items-center mb-4">
              <div className="flex text-amber-400">
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <StarHalf className="h-4 w-4 fill-current" />
              </div>
              <span className="text-sm text-gray-500 ml-1">(4.5)</span>
            </div>

            {/* Price Display */}
            <div className="mb-6 backdrop-blur-sm bg-white/30 p-5 rounded-xl shadow-lg border border-white/40">
              {product.sale_price ? (
                <div className="flex flex-wrap items-center gap-3">
                  <span className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                    £{calculatedPrice?.toFixed(2)}
                  </span>
                  <span className="text-lg line-through text-gray-500/80">
                    £{(product.price + priceAdjustment).toFixed(2)}
                  </span>
                  {priceAdjustment !== 0 && (
                    <span className="text-sm bg-white/40 text-primary px-3 py-1 rounded-full ml-auto shadow-sm border border-white/50">
                      {priceAdjustment > 0 ? '+' : '-'}£{Math.abs(priceAdjustment).toFixed(2)}
                    </span>
                  )}
                </div>
              ) : (
                <div className="flex flex-wrap items-center gap-3">
                  <span className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                    £{calculatedPrice?.toFixed(2)}
                  </span>
                  {priceAdjustment !== 0 && (
                    <span className="text-sm bg-white/40 text-primary px-3 py-1 rounded-full ml-auto shadow-sm border border-white/50">
                      {priceAdjustment > 0 ? '+' : '-'}£{Math.abs(priceAdjustment).toFixed(2)}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Brief Description */}
            <div className="mb-6 backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md">
              <h4 className="font-medium mb-3 flex items-center gap-2 text-gray-800">
                <span className="inline-block w-2 h-2 rounded-full bg-primary mr-1"></span>
                Description
              </h4>
              <div className="p-3 bg-white/30 rounded-lg">
                <p className="text-gray-700">{product.description}</p>
              </div>
            </div>

            {/* Product Options with Visual Cards */}
            {product.options && Object.keys(product.options).length > 0 && (
              <div className="space-y-7 mb-8">
                {Object.entries(product.options).map(([optionName, values]) => {
                  // Normalize option name for display
                  const displayName = optionName.charAt(0).toUpperCase() + optionName.slice(1);
                  const normalizedName = optionName.toLowerCase();
                  
                  return (
                    <div key={optionName} className="backdrop-blur-sm bg-white/20 p-4 rounded-xl border border-white/30 shadow-md">
                      <h3 className="text-lg font-medium mb-4 text-gray-800 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full bg-primary mr-2"></span>
                        {displayName}
                      </h3>
                      <div className="grid grid-cols-2 gap-3">
                        {values.map((value) => {
                          // Get price adjustment for this option
                          let adjustment = null;
                          if (product.price_adjustments) {
                            // Try to find the adjustment using normalized keys
                            const adjustments = Object.entries(product.price_adjustments)
                              .find(([key]) => key.toLowerCase() === normalizedName)?.[1];
                              
                            if (adjustments && adjustments[value] !== undefined) {
                              adjustment = Number(adjustments[value]);
                            }
                          }
                          
                          // Check if this option is selected
                          const isSelected = Object.entries(selectedOptions)
                            .some(([key, val]) => key.toLowerCase() === normalizedName && val === value);
                          
                          return (
                            <VisualOptionCard
                              key={value}
                              optionName={optionName}
                              value={value}
                              isSelected={isSelected}
                              priceAdjustment={adjustment}
                              onClick={() => {
                                console.log(`Option selected: ${optionName} = ${value}`);
                                handleOptionChange(optionName, value);
                              }}
                            />
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Quantity and Add to Cart Section */}
            <div className="backdrop-blur-sm bg-white/30 p-5 rounded-xl mb-6 shadow-lg border border-white/40">
              <div className="flex items-center justify-between mb-5">
                <label className="font-medium text-gray-700">
                  Quantity
                </label>
                <div className="flex items-center rounded-lg overflow-hidden shadow-sm">
                  <button
                    className="px-3 py-2 bg-white/50 hover:bg-white/70 transition-colors text-gray-700"
                    onClick={() => quantity > 1 && setQuantity(quantity - 1)}
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={handleQuantityChange}
                    className="w-14 text-center py-2 bg-white/80 font-medium"
                  />
                  <button
                    className="px-3 py-2 bg-white/50 hover:bg-white/70 transition-colors text-gray-700"
                    onClick={() => setQuantity(quantity + 1)}
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Add to Cart Button */}
              <div className="flex justify-center">
                <button
                  className="bg-sage-500 text-white py-2 px-4 rounded-md flex items-center justify-center gap-1.5 hover:bg-sage-600 transition-all duration-200 shadow-sm relative overflow-hidden group w-full max-w-[180px]"
                  onClick={handleAddToCart}
                >
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:animate-shimmer" />
                  <ShoppingBag size={14} className="group-hover:scale-110 transition-transform" />
                  <span className="font-medium text-xs uppercase tracking-wider">Add to Cart</span>
                </button>
              </div>
            </div>
            
            {/* Additional Information Section */}
            {product.additional_info && (
              <div className="backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md mb-6">
                <h4 className="font-medium mb-3 flex items-center gap-2 text-gray-800">
                  <span className="inline-block w-2 h-2 rounded-full bg-primary mr-1"></span>
                  Additional Information
                </h4>
                <div className="p-3 bg-white/30 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-line">{product.additional_info}</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Related Products Section */}
        <div className="mt-12">
          <RelatedProducts 
            currentProductId={product.id} 
            categoryId={product.category_id} 
            limit={4} 
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
