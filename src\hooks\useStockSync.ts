import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { stockSyncService } from '@/services/stockSyncService';

interface StockCSVRow {
  sku?: string;
  product_name?: string;
  barcode?: string;
  stock_quantity?: string | number;
  price?: string | number;
  sale_price?: string | number;
  description?: string;
  category?: string;
  brand?: string;
  [key: string]: any;
}

interface SyncResults {
  updated: number;
  activated: number;
  failed: number;
  unmatched: StockCSVRow[];
  incompleteProducts: any[];
  summary: string;
}

export function useStockSync() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [syncResults, setSyncResults] = useState<SyncResults | null>(null);

  // Mutation for stock synchronization
  const syncMutation = useMutation({
    mutationFn: async (csvData: StockCSVRow[]) => {
      setIsProcessing(true);
      try {
        const results = await stockSyncService.syncStockFromCSV(csvData);
        setSyncResults(results);
        return results;
      } finally {
        setIsProcessing(false);
      }
    },
    onSuccess: (results) => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['inactive-products'] });
      
      toast({
        title: 'Stock Sync Completed',
        description: `Updated ${results.updated} products, activated ${results.activated} products`,
      });
    },
    onError: (error) => {
      console.error('Stock sync error:', error);
      toast({
        title: 'Stock Sync Failed',
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
      setIsProcessing(false);
    },
  });

  // Function to preview changes without applying them
  const previewStockSync = async (csvData: StockCSVRow[]) => {
    setIsProcessing(true);
    try {
      // For preview, we can use the same service but not actually update the database
      // The service already analyzes everything without making changes by default
      const results = await stockSyncService.syncStockFromCSV(csvData);
      setSyncResults(results);
      
      toast({
        title: 'Preview Generated',
        description: `Found ${results.updated + results.unmatched.length} potential matches`,
      });
      
      return results;
    } catch (error) {
      console.error('Preview error:', error);
      toast({
        title: 'Preview Failed',
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  // Function to apply the sync changes
  const applyStockSync = (csvData: StockCSVRow[]) => {
    syncMutation.mutate(csvData);
  };

  // Function to clear results
  const clearResults = () => {
    setSyncResults(null);
  };

  // Function to export incomplete products report
  const exportIncompleteProducts = () => {
    if (!syncResults?.incompleteProducts.length) {
      toast({
        title: 'No Data',
        description: 'No incomplete products data to export',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Create CSV content
      const headers = ['ID', 'Name', 'SKU', 'Issues', 'Can Be Activated', 'CSV Match Found'];
      const rows = syncResults.incompleteProducts.map(product => [
        product.id,
        product.name,
        product.sku || 'Missing',
        product.issues.join('; '),
        product.canBeActivated ? 'Yes' : 'No',
        product.csvMatch ? 'Yes' : 'No'
      ]);

      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      // Download the file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `incomplete-products-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Report Exported',
        description: 'Incomplete products report has been downloaded',
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export the report',
        variant: 'destructive'
      });
    }
  };

  // Function to export unmatched products
  const exportUnmatchedProducts = () => {
    if (!syncResults?.unmatched.length) {
      toast({
        title: 'No Data',
        description: 'No unmatched products data to export',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Get all unique keys from unmatched products
      const allKeys = Array.from(
        new Set(syncResults.unmatched.flatMap(item => Object.keys(item)))
      );

      const headers = allKeys;
      const rows = syncResults.unmatched.map(item => 
        allKeys.map(key => item[key] || '')
      );

      const csvContent = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      // Download the file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `unmatched-products-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Report Exported',
        description: 'Unmatched products report has been downloaded',
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export the report',
        variant: 'destructive'
      });
    }
  };

  return {
    // State
    isProcessing,
    syncResults,
    
    // Actions
    previewStockSync,
    applyStockSync,
    clearResults,
    exportIncompleteProducts,
    exportUnmatchedProducts,
    
    // Mutation state
    isApplying: syncMutation.isPending,
    applyError: syncMutation.error,
  };
}

export default useStockSync;
