#!/usr/bin/env tsx
/**
 * Fix duplicate filter categories and consolidate product counts
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDuplicateFilterCategories() {
  console.log('🔧 FIXING DUPLICATE FILTER CATEGORIES\n');

  try {
    // Get all filter categories
    const { data: categories, error: categoriesError } = await supabase
      .from('filter_categories')
      .select('*')
      .order('name');

    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
      return;
    }

    console.log('📊 Current filter categories:');
    categories?.forEach(cat => {
      console.log(`   ${cat.name} (ID: ${cat.id})`);
    });

    // Find duplicates by name
    const categoryNames = categories?.map(c => c.name) || [];
    const duplicateNames = categoryNames.filter((name, index) => 
      categoryNames.indexOf(name) !== index
    );

    const uniqueDuplicates = [...new Set(duplicateNames)];
    
    if (uniqueDuplicates.length === 0) {
      console.log('\n✅ No duplicate categories found!');
      return;
    }

    console.log(`\n🔍 Found ${uniqueDuplicates.length} duplicate category names:`);
    uniqueDuplicates.forEach(name => console.log(`   - ${name}`));

    // Process each duplicate
    for (const duplicateName of uniqueDuplicates) {
      console.log(`\n🔧 Processing duplicate: ${duplicateName}`);
      
      const duplicateCategories = categories?.filter(c => c.name === duplicateName) || [];
      console.log(`   Found ${duplicateCategories.length} instances`);

      if (duplicateCategories.length < 2) continue;

      // Sort by creation date (keep the oldest)
      duplicateCategories.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      
      const keepCategory = duplicateCategories[0];
      const removeCategories = duplicateCategories.slice(1);

      console.log(`   Keeping: ${keepCategory.id} (created: ${keepCategory.created_at})`);
      console.log(`   Removing: ${removeCategories.map(c => c.id).join(', ')}`);

      // Get filter options for categories to remove
      for (const removeCategory of removeCategories) {
        console.log(`\n   📋 Processing options for category ${removeCategory.id}:`);
        
        const { data: options, error: optionsError } = await supabase
          .from('filter_options')
          .select('*')
          .eq('category_id', removeCategory.id);

        if (optionsError) {
          console.error(`   ❌ Error fetching options: ${optionsError.message}`);
          continue;
        }

        console.log(`   Found ${options?.length || 0} options to migrate`);

        // Migrate each option to the keep category
        for (const option of options || []) {
          console.log(`   🔄 Migrating option: ${option.value} (${option.product_count} products)`);
          
          // Check if option already exists in keep category
          const { data: existingOption } = await supabase
            .from('filter_options')
            .select('*')
            .eq('category_id', keepCategory.id)
            .eq('value', option.value)
            .single();

          if (existingOption) {
            console.log(`   ⚠️  Option already exists, consolidating product counts...`);
            
            // Update product filters to point to existing option
            const { error: updateFiltersError } = await supabase
              .from('product_filters')
              .update({ filter_option_id: existingOption.id })
              .eq('filter_option_id', option.id);

            if (updateFiltersError) {
              console.error(`   ❌ Error updating product filters: ${updateFiltersError.message}`);
            } else {
              console.log(`   ✅ Updated product filters`);
            }

            // Update product count on existing option
            const { error: updateCountError } = await supabase
              .from('filter_options')
              .update({ 
                product_count: existingOption.product_count + option.product_count 
              })
              .eq('id', existingOption.id);

            if (updateCountError) {
              console.error(`   ❌ Error updating product count: ${updateCountError.message}`);
            } else {
              console.log(`   ✅ Updated product count: ${existingOption.product_count} + ${option.product_count} = ${existingOption.product_count + option.product_count}`);
            }

            // Delete the duplicate option
            const { error: deleteOptionError } = await supabase
              .from('filter_options')
              .delete()
              .eq('id', option.id);

            if (deleteOptionError) {
              console.error(`   ❌ Error deleting duplicate option: ${deleteOptionError.message}`);
            } else {
              console.log(`   ✅ Deleted duplicate option`);
            }
          } else {
            console.log(`   🔄 Moving option to keep category...`);
            
            // Move option to keep category
            const { error: moveOptionError } = await supabase
              .from('filter_options')
              .update({ category_id: keepCategory.id })
              .eq('id', option.id);

            if (moveOptionError) {
              console.error(`   ❌ Error moving option: ${moveOptionError.message}`);
            } else {
              console.log(`   ✅ Moved option successfully`);
            }
          }
        }

        // Delete the duplicate category
        console.log(`   🗑️  Deleting duplicate category ${removeCategory.id}...`);
        const { error: deleteCategoryError } = await supabase
          .from('filter_categories')
          .delete()
          .eq('id', removeCategory.id);

        if (deleteCategoryError) {
          console.error(`   ❌ Error deleting category: ${deleteCategoryError.message}`);
        } else {
          console.log(`   ✅ Deleted duplicate category`);
        }
      }
    }

    console.log('\n🎉 DUPLICATE CLEANUP COMPLETE!');
    console.log('\n📊 Final category list:');
    
    // Get updated categories
    const { data: finalCategories } = await supabase
      .from('filter_categories')
      .select(`
        *,
        filter_options(count)
      `)
      .order('name');

    finalCategories?.forEach(cat => {
      const optionCount = cat.filter_options?.length || 0;
      console.log(`   ✅ ${cat.name} (${optionCount} options)`);
    });

    console.log('\n💡 Next steps:');
    console.log('   1. Refresh the admin interface');
    console.log('   2. Test the filtering system');
    console.log('   3. Verify product counts are correct');

  } catch (err) {
    console.error('❌ Cleanup failed:', err);
  }
}

// Run the cleanup
fixDuplicateFilterCategories().catch(console.error);
