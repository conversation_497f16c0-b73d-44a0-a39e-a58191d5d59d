import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { shippingService } from "@/services/shippingService";

/**
 * A button that forcefully refreshes shipping methods
 * This is a manual solution to ensure shipping methods are always fresh
 */
export function RefreshShippingButton() {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      
      // 1. Clear all cached queries
      queryClient.removeQueries({ queryKey: ['checkout-shipping'] });
      
      // 2. Set a cache buster timestamp
      localStorage.setItem('shipping_refresh_timestamp', Date.now().toString());
      
      // 3. Make a direct database call to get fresh shipping methods
      const freshMethods = await shippingService.getCheckoutShippingMethods('United Kingdom');
      
      // 4. Log the fresh methods
      console.log('Manually fetched fresh shipping methods:', freshMethods);
      
      // 5. Force-set the data in the query cache
      queryClient.setQueryData(['checkout-shipping', 'United Kingdom'], freshMethods);
      
      // 6. Force a refetch
      queryClient.invalidateQueries({ queryKey: ['checkout-shipping'] });
      
      console.log('Shipping methods refreshed!');
      
      // Wait a moment to give visual feedback
      setTimeout(() => {
        setIsRefreshing(false);
      }, 800);
    } catch (error) {
      console.error('Error refreshing shipping methods:', error);
      setIsRefreshing(false);
    }
  };
  
  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleRefresh}
      className="ml-2"
      disabled={isRefreshing}
    >
      <RefreshCw 
        className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} 
      />
      Refresh Options
    </Button>
  );
} 