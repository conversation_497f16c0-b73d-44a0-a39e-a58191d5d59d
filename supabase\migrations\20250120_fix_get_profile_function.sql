-- Fix get_profile function to return correct type
-- The issue is that the function returns SETOF profiles but is called expecting a record type

-- Drop the existing function
DROP FUNCTION IF EXISTS get_profile(UUID);

-- Create the corrected function that returns a single record
CREATE OR REPLACE FUNCTION get_profile(user_id UUID DEFAULT auth.uid())
RETURNS TABLE(
  id UUID,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  address TEXT,
  city TEXT,
  postal_code TEXT,
  country TEXT,
  is_admin BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT 
    p.id,
    p.email,
    p.first_name,
    p.last_name,
    p.address,
    p.city,
    p.postal_code,
    p.country,
    p.is_admin,
    p.created_at,
    p.updated_at
  FROM profiles p 
  WHERE p.id = user_id;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_profile(UUID) TO authenticated;
