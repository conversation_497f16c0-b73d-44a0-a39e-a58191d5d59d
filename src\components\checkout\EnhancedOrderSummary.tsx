import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShieldCheck, Clock, Percent } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { DiscountCodeInput } from './DiscountCodeInput';
import { AppliedDiscount } from '@/types/discount';

interface OrderSummaryProps {
  subtotal: number;
  tax: number;
  shippingCost: number;
  total: number;
  isProcessing: boolean;
  onPlaceOrder?: () => void;
  showPlaceOrderButton?: boolean;
  discount?: AppliedDiscount | null;
  onApplyDiscount?: (discount: AppliedDiscount) => void;
  onRemoveDiscount?: () => void;
  showDiscountInput?: boolean;
}

export function EnhancedOrderSummary({
  subtotal,
  tax,
  shippingCost,
  total,
  isProcessing,
  onPlaceOrder,
  showPlaceOrderButton = false,
  discount = null,
  onApplyDiscount,
  onRemoveDiscount,
  showDiscountInput = false
}: OrderSummaryProps) {
  return (
    <Card className="shadow-md border-0">
      <CardHeader className="bg-sage-50 rounded-t-lg">
        <CardTitle className="text-xl font-bold flex items-center">
          Order Summary
          <Badge variant="outline" className="ml-2 bg-sage-100 text-sage-700 border-sage-200">
            Secure Checkout
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Subtotal (inc. VAT)</span>
            <span className="font-medium">£{subtotal.toFixed(2)}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600 flex items-center">
              <Badge variant="outline" className="mr-2 bg-green-50 text-green-700 border-green-200">
                Included
              </Badge>
              VAT (20%)
            </span>
            <span className="text-gray-600">£{tax.toFixed(2)}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600">Shipping</span>
            <span className="font-medium">£{shippingCost.toFixed(2)}</span>
          </div>

          {/* Discount Code Input */}
          {showDiscountInput && onApplyDiscount && onRemoveDiscount && (
            <div className="mt-2 mb-2">
              <div className="flex items-center mb-2">
                <Percent className="h-4 w-4 text-sage-500 mr-2" />
                <span className="text-sm font-medium">Discount Code</span>
              </div>
              <DiscountCodeInput
                orderTotal={subtotal + shippingCost}
                onApplyDiscount={onApplyDiscount}
                onRemoveDiscount={onRemoveDiscount}
                appliedDiscount={discount}
                disabled={isProcessing}
              />
            </div>
          )}

          {/* Display Discount if applied */}
          {discount && (
            <div className="flex justify-between items-center text-green-600">
              <span className="flex items-center">
                <Badge variant="success" className="mr-2">
                  Discount
                </Badge>
                {discount.code}
              </span>
              <span className="font-medium">-£{discount.discountAmount.toFixed(2)}</span>
            </div>
          )}

          <Separator className="my-2" />

          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">Total</span>
            <span className="text-lg font-bold text-sage-700">
              £{(total - (discount?.discountAmount || 0)).toFixed(2)}
            </span>
          </div>

          {showPlaceOrderButton && (
            <Button
              onClick={onPlaceOrder}
              className="w-full mt-4 bg-sage-500 hover:bg-sage-600 text-white"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-r-transparent"></span>
                  Processing...
                </>
              ) : (
                'Place Order'
              )}
            </Button>
          )}

          <div className="mt-4 space-y-2">
            <div className="flex items-start space-x-2 text-xs text-gray-500">
              <ShieldCheck className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
              <p>Your payment information is processed securely. We do not store your credit card details.</p>
            </div>

            <div className="flex items-start space-x-2 text-xs text-gray-500">
              <Clock className="h-4 w-4 text-sage-500 flex-shrink-0 mt-0.5" />
              <p>Orders placed before 2pm are typically processed the same day.</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
