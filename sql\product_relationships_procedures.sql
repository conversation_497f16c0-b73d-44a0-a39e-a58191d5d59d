-- Function to get related products for a specific product
CREATE OR REPLACE FUNCTION get_related_products(product_id_param UUID)
RETURNS TABLE (
  related_product_id UUID,
  order_index INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT pr.related_product_id, pr.order_index
  FROM product_relationships pr
  WHERE pr.product_id = product_id_param
  ORDER BY pr.order_index;
END;
$$ LANGUAGE plpgsql;

-- Function to update product relationships
CREATE OR REPLACE FUNCTION update_product_relationships(
  p_product_id UUID,
  p_related_ids UUID[],
  p_relationship_type TEXT DEFAULT 'also_purchased'
) RETURNS VOID AS $$
BEGIN
  -- First delete existing relationships
  DELETE FROM product_relationships
  WHERE product_id = p_product_id;
  
  -- Then insert new relationships
  FOR i IN 1..array_length(p_related_ids, 1) LOOP
    INSERT INTO product_relationships (
      product_id,
      related_product_id,
      relationship_type,
      order_index
    ) VALUES (
      p_product_id,
      p_related_ids[i],
      p_relationship_type,
      i-1
    );
  <PERSON>ND LOOP;
END;
$$ LANGUAGE plpgsql;
