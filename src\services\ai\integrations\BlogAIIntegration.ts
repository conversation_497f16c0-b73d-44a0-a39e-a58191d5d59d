/**
 * Blog AI Integration Layer
 * 
 * Enhances existing blog generation with unified AI system capabilities
 * while maintaining backward compatibility with current blog system
 */

import { aiServiceManager } from '../AIServiceManager';
import { AIRequest } from '../types/AIRequest';

export interface BlogAIOptions {
  topic: string;
  category: string;
  target_audience?: string;
  tone?: 'professional' | 'casual' | 'educational' | 'engaging';
  length?: 'short' | 'medium' | 'long';
  include_seo?: boolean;
  include_images?: boolean;
  keywords?: string[];
}

export interface BlogAIResult {
  title: string;
  content: string;
  excerpt: string;
  seo_title?: string;
  seo_description?: string;
  keywords?: string[];
  suggested_images?: string[];
  provider_used: string;
  cost: number;
  processing_time: number;
  word_count: number;
  reading_time: number;
}

export interface BlogSeriesOptions {
  main_topic: string;
  category: string;
  number_of_posts: number;
  target_audience?: string;
  tone?: 'professional' | 'casual' | 'educational' | 'engaging';
}

export interface BlogSeriesResult {
  series_title: string;
  posts: Array<{
    title: string;
    outline: string;
    keywords: string[];
    estimated_length: number;
  }>;
  total_estimated_cost: number;
  estimated_completion_time: number;
}

export class BlogAIIntegration {
  private static instance: BlogAIIntegration;
  
  private constructor() {}
  
  static getInstance(): BlogAIIntegration {
    if (!BlogAIIntegration.instance) {
      BlogAIIntegration.instance = new BlogAIIntegration();
    }
    return BlogAIIntegration.instance;
  }
  
  /**
   * Generate blog content using unified AI system
   */
  async generateBlogPost(options: BlogAIOptions): Promise<BlogAIResult> {
    try {
      const startTime = Date.now();
      
      // Build context for AI request
      const context = {
        business_type: 'cannabis' as const,
        category: options.category,
        target_audience: options.target_audience || 'cannabis enthusiasts and newcomers',
        brand_voice: {
          tone: options.tone || 'educational',
          personality: 'Knowledgeable, trustworthy, and accessible',
          compliance_requirements: ['uk-cannabis-laws', 'educational-content-guidelines']
        },
        format: 'html' as const,
        max_length: this.getLengthLimit(options.length || 'medium')
      };
      
      // Generate main content
      const content = await aiServiceManager.generateBlogContent(options.topic, options.category);
      
      // Generate title and excerpt
      const [title, excerpt] = await Promise.all([
        this.generateTitle(options),
        this.generateExcerpt(content)
      ]);
      
      // Generate SEO data if requested
      let seoData = {};
      if (options.include_seo) {
        seoData = await this.generateSEOData(options, content);
      }
      
      // Generate image suggestions if requested
      let imageSuggestions: string[] = [];
      if (options.include_images) {
        imageSuggestions = await this.generateImageSuggestions(options);
      }
      
      const processingTime = Date.now() - startTime;
      const wordCount = this.countWords(content);
      
      return {
        title,
        content,
        excerpt,
        suggested_images: imageSuggestions,
        provider_used: 'unified-ai',
        cost: 0.003, // Estimated cost for blog generation
        processing_time: processingTime,
        word_count: wordCount,
        reading_time: Math.ceil(wordCount / 200), // Average reading speed
        ...seoData
      };
      
    } catch (error) {
      console.error('Blog AI generation failed:', error);
      
      // Fallback to basic template
      return this.generateFallbackBlog(options);
    }
  }
  
  /**
   * Generate a series of related blog posts
   */
  async generateBlogSeries(options: BlogSeriesOptions): Promise<BlogSeriesResult> {
    try {
      // Create AI request for series planning
      const request: AIRequest = {
        type: 'blog_content',
        content: `Plan a ${options.number_of_posts}-part blog series about: ${options.main_topic}`,
        context: {
          business_type: 'cannabis',
          category: options.category,
          target_audience: options.target_audience,
          brand_voice: {
            tone: options.tone || 'educational',
            personality: 'Comprehensive, structured, and engaging'
          }
        },
        provider: 'gemini' // Use Gemini for creative planning
      };
      
      // For now, generate a structured series plan
      const seriesTitle = `The Complete Guide to ${options.main_topic}`;
      const posts = this.generateSeriesOutline(options);
      
      return {
        series_title: seriesTitle,
        posts,
        total_estimated_cost: posts.length * 0.003,
        estimated_completion_time: posts.length * 10 // 10 minutes per post
      };
      
    } catch (error) {
      console.error('Blog series generation failed:', error);
      throw error;
    }
  }
  
  /**
   * Optimize existing blog content
   */
  async optimizeBlogContent(existingContent: string, options: Partial<BlogAIOptions>): Promise<{
    optimized_content: string;
    improvements: string[];
    seo_score: number;
    readability_score: number;
  }> {
    try {
      // Analyze current content
      const analysis = this.analyzeContent(existingContent);
      
      // Generate improvements using AI
      const request: AIRequest = {
        type: 'blog_content',
        content: `Optimize this blog content for better SEO and readability: ${existingContent}`,
        context: {
          business_type: 'cannabis',
          brand_voice: {
            tone: options.tone || 'professional',
            personality: 'Clear, engaging, and SEO-optimized'
          }
        },
        provider: 'deepseek' // Fast optimization
      };
      
      // For now, return analysis with suggestions
      return {
        optimized_content: existingContent, // Would be AI-optimized
        improvements: [
          'Add more relevant keywords naturally',
          'Improve heading structure (H2, H3)',
          'Add internal links to related content',
          'Include call-to-action elements',
          'Optimize meta description length'
        ],
        seo_score: analysis.seo_score,
        readability_score: analysis.readability_score
      };
      
    } catch (error) {
      console.error('Blog optimization failed:', error);
      throw error;
    }
  }
  
  /**
   * Generate social media posts from blog content
   */
  async generateSocialMediaPosts(blogContent: string, platforms: string[]): Promise<{
    [platform: string]: {
      caption: string;
      hashtags: string[];
      image_suggestion: string;
    };
  }> {
    const socialPosts: any = {};
    
    for (const platform of platforms) {
      try {
        const socialContent = await aiServiceManager.generateSocialMediaContent(
          { name: 'Blog Post', content: blogContent },
          platform
        );
        
        socialPosts[platform] = socialContent;
        
      } catch (error) {
        console.error(`Failed to generate ${platform} content:`, error);
        socialPosts[platform] = {
          caption: `Check out our latest blog post! ${blogContent.substring(0, 100)}...`,
          hashtags: ['#cannabis', '#cbd', '#blog'],
          image_suggestion: 'blog featured image'
        };
      }
    }
    
    return socialPosts;
  }
  
  private async generateTitle(options: BlogAIOptions): Promise<string> {
    // Would use AI to generate compelling titles
    // For now, create a basic title
    return `${options.topic}: A Complete Guide for ${options.target_audience || 'Cannabis Enthusiasts'}`;
  }
  
  private async generateExcerpt(content: string): Promise<string> {
    // Extract first paragraph and clean it up
    const firstParagraph = content.match(/<p[^>]*>(.*?)<\/p>/)?.[1] || '';
    const cleanText = firstParagraph.replace(/<[^>]*>/g, '');
    return cleanText.substring(0, 200) + (cleanText.length > 200 ? '...' : '');
  }
  
  private async generateSEOData(options: BlogAIOptions, content: string): Promise<{
    seo_title?: string;
    seo_description?: string;
    keywords?: string[];
  }> {
    return {
      seo_title: `${options.topic} - Expert Guide | Cannabis Education`,
      seo_description: `Learn about ${options.topic} with our comprehensive guide. Expert insights on ${options.category} for cannabis enthusiasts.`,
      keywords: [
        options.topic.toLowerCase(),
        options.category.toLowerCase(),
        'cannabis guide',
        'cbd information',
        ...(options.keywords || [])
      ]
    };
  }
  
  private async generateImageSuggestions(options: BlogAIOptions): Promise<string[]> {
    return [
      `${options.topic} featured image`,
      `${options.category} product showcase`,
      'Cannabis education infographic',
      'Step-by-step guide illustration',
      'Before and after comparison'
    ];
  }
  
  private getLengthLimit(length: 'short' | 'medium' | 'long'): number {
    switch (length) {
      case 'short': return 800;
      case 'medium': return 1500;
      case 'long': return 2500;
      default: return 1500;
    }
  }
  
  private countWords(content: string): number {
    const cleanText = content.replace(/<[^>]*>/g, '');
    return cleanText.split(/\s+/).filter(word => word.length > 0).length;
  }
  
  private generateSeriesOutline(options: BlogSeriesOptions): Array<{
    title: string;
    outline: string;
    keywords: string[];
    estimated_length: number;
  }> {
    // Generate structured outline for blog series
    const posts = [];
    
    for (let i = 1; i <= options.number_of_posts; i++) {
      posts.push({
        title: `${options.main_topic} - Part ${i}`,
        outline: `Comprehensive coverage of ${options.main_topic} aspect ${i}`,
        keywords: [options.main_topic.toLowerCase(), options.category.toLowerCase()],
        estimated_length: 1500
      });
    }
    
    return posts;
  }
  
  private analyzeContent(content: string): {
    seo_score: number;
    readability_score: number;
  } {
    // Basic content analysis
    const wordCount = this.countWords(content);
    const hasHeadings = /<h[1-6]/.test(content);
    const hasLinks = /<a\s/.test(content);
    
    let seoScore = 50;
    if (wordCount > 300) seoScore += 20;
    if (hasHeadings) seoScore += 15;
    if (hasLinks) seoScore += 15;
    
    const readabilityScore = wordCount > 100 && wordCount < 2000 ? 80 : 60;
    
    return {
      seo_score: Math.min(seoScore, 100),
      readability_score: readabilityScore
    };
  }
  
  private generateFallbackBlog(options: BlogAIOptions): BlogAIResult {
    const fallbackContent = `<h2>${options.topic}</h2><p>This comprehensive guide covers everything you need to know about ${options.topic} in the ${options.category} category. Our expert insights will help you understand the key concepts and make informed decisions.</p>`;
    
    return {
      title: `${options.topic} - Complete Guide`,
      content: fallbackContent,
      excerpt: `Learn about ${options.topic} with our expert guide covering ${options.category}.`,
      provider_used: 'fallback',
      cost: 0,
      processing_time: 0,
      word_count: this.countWords(fallbackContent),
      reading_time: 1
    };
  }
}

// Export singleton instance
export const blogAI = BlogAIIntegration.getInstance();
