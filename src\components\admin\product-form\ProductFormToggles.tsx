
import React from "react";
import { Product } from "@/types/database";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface ProductFormTogglesProps {
  formData: Partial<Product>;
  handleSwitchChange: (name: string, checked: boolean) => void;
}

export function ProductFormToggles({
  formData,
  handleSwitchChange,
}: ProductFormTogglesProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <div className="flex items-center space-x-2">
        <Switch
          id="in_stock"
          checked={formData.in_stock ?? true}
          onCheckedChange={(checked) => handleSwitchChange("in_stock", checked)}
        />
        <Label htmlFor="in_stock">In Stock</Label>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="is_featured"
          checked={formData.is_featured ?? false}
          onCheckedChange={(checked) => handleSwitchChange("is_featured", checked)}
        />
        <Label htmlFor="is_featured">Featured Product</Label>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="is_new"
          checked={formData.is_new ?? false}
          onCheckedChange={(checked) => handleSwitchChange("is_new", checked)}
        />
        <Label htmlFor="is_new">New Product</Label>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="is_best_seller"
          checked={formData.is_best_seller ?? false}
          onCheckedChange={(checked) => handleSwitchChange("is_best_seller", checked)}
        />
        <Label htmlFor="is_best_seller">Best Seller</Label>
      </div>
    </div>
  );
}
