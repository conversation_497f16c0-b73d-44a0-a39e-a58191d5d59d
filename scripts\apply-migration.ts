import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: Supabase anon key is not defined in environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    // The SQL for the increment_blog_view function
    const sql = `
    -- Create function to increment blog view count
    CREATE OR REPLACE FUNCTION increment_blog_view(blog_id UUID)
    RETURNS void AS $$
    BEGIN
      UPDATE blogs
      SET view_count = view_count + 1
      WHERE id = blog_id;
    END;
    $$ LANGUAGE plpgsql;
    `;
    
    console.log('Applying increment_blog_view function migration...');
    
    // Execute the SQL directly
    const { data, error } = await supabase.rpc('pg_query', { query: sql });
    
    if (error) {
      console.error('Error applying migration:', error);
      
      // Try alternative approach with direct query
      console.log('Trying alternative approach with direct query...');
      const { error: directError } = await supabase.from('blogs').select('id').limit(1);
      
      if (directError) {
        console.error('Error with direct query:', directError);
        return;
      }
      
      console.log('Database connection successful, but cannot execute custom SQL.');
      console.log('Please apply the migration manually through the Supabase dashboard.');
      console.log('SQL to execute:');
      console.log(sql);
      return;
    }
    
    console.log('Migration applied successfully!');
    console.log(data);
  } catch (err) {
    console.error('Error:', err);
  }
}

// Run the migration
runMigration();
