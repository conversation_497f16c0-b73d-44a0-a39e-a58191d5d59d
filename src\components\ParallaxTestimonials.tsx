
import { useRef, useEffect } from 'react';
import { Star } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { motion } from 'framer-motion';
import ParallaxSection from './animations/ParallaxSection';

const testimonials = [
  {
    id: '1',
    name: '<PERSON><PERSON> <PERSON>',
    text: "Decent shop! Boy in there is brand new! Helped me out with getting a Puffco Pivot and the Puffco hot knife! Even gave me a freebie 👌 👌",
    rating: 5
  },
  {
    id: '2',
    name: '<PERSON>',
    text: "First class. Dont go anywhere else for ur smoking essentials. They have everything. Well worth a visit.",
    rating: 5
  },
  {
    id: '3',
    name: '<PERSON>',
    text: "Fair impressed with this shop. All your smoking accessories under 1 roof 👌 👌",
    rating: 5
  },
  {
    id: '4',
    name: '<PERSON>',
    text: "Such a great shop. Went in today and got everything I needed. The guys were very welcoming and kind, helping me find exactly what I wanted. Accommodating my pickiness isn't easy as well haha.",
    rating: 5
  },
  {
    id: '5',
    name: '<PERSON>',
    text: "Great shop, good vibes throughout visit, made to feel welcome. Staff also were very helpful and had extensive knowledge of products. Looking forward to my next visit.",
    rating: 5
  },
  {
    id: '6',
    name: 'Collective Studios',
    text: "Very honest, very helpful. Thank you for being so helpful for all my needs at an unimaginable price. Honest man",
    rating: 5
  }
];

const ParallaxTestimonials = () => {
  const cardsRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!cardsRef.current) return;
      
      const cards = Array.from(cardsRef.current.children);
      const scrollPosition = window.scrollY;
      const viewportHeight = window.innerHeight;
      
      // Calculate the section position
      const rect = cardsRef.current.getBoundingClientRect();
      const sectionTop = rect.top + scrollPosition;
      const sectionCenter = sectionTop + rect.height / 2;
      
      // Apply different parallax effects to each card
      cards.forEach((card, index) => {
        const offset = (index % 3 - 1) * 20; // -20, 0, or 20 depending on index
        const parallaxY = ((scrollPosition + viewportHeight - sectionCenter) * 0.1) + offset;
        
        // Apply transform with different values for each card
        (card as HTMLElement).style.transform = `translateY(${parallaxY}px)`;
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <ParallaxSection 
      imageUrl="https://images.unsplash.com/photo-1616788373791-1d4794500236"
      overlayColor="from-clay-900/80 to-sage-900/80"
      className="py-24"
      contentClassName="container-custom"
    >
      <div className="text-center mb-12">
        <motion.h2 
          initial={{ opacity: 0, y: -20 }} 
          whileInView={{ opacity: 1, y: 0 }} 
          transition={{ duration: 0.7 }}
          className="text-4xl md:text-5xl font-bold mb-4 text-white"
        >
          What Our Customers Say
        </motion.h2>
        <motion.div 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.7 }}
          className="flex justify-center mb-2"
        >
          <div className="flex items-center text-amber-400">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="h-6 w-6 fill-current" />
            ))}
          </div>
        </motion.div>
        <motion.p 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.7 }}
          className="text-xl text-white/80 max-w-2xl mx-auto"
        >
          Discover why our customers love our products and service
        </motion.p>
      </div>
      
      <div 
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        {testimonials.slice(0, 3).map((testimonial, index) => (
          <motion.div
            key={testimonial.id}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.2, duration: 0.7 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.2)" }}
            className="p-6 rounded-xl backdrop-blur-lg bg-white/10 border border-white/20"
          >
            <div className="flex items-center mb-4">
              <Avatar className="h-12 w-12 mr-4 border-2 border-white/50">
                <AvatarFallback className="bg-sage-600/40 text-white font-bold text-lg">
                  {testimonial.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-white">{testimonial.name}</div>
                <div className="flex items-center text-amber-400 mt-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current" />
                  ))}
                </div>
              </div>
            </div>
            
            <p className="text-white/90 italic">"{testimonial.text}"</p>
          </motion.div>
        ))}
      </div>
      
      <div 
        ref={cardsRef}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6"
      >
        {testimonials.slice(3, 6).map((testimonial, index) => (
          <motion.div
            key={testimonial.id}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: (index + 3) * 0.2, duration: 0.7 }}
            whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.2)" }}
            className="p-6 rounded-xl backdrop-blur-lg bg-white/10 border border-white/20"
          >
            <div className="flex items-center mb-4">
              <Avatar className="h-12 w-12 mr-4 border-2 border-white/50">
                <AvatarFallback className="bg-sage-600/40 text-white font-bold text-lg">
                  {testimonial.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-white">{testimonial.name}</div>
                <div className="flex items-center text-amber-400 mt-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current" />
                  ))}
                </div>
              </div>
            </div>
            
            <p className="text-white/90 italic">"{testimonial.text}"</p>
          </motion.div>
        ))}
      </div>
    </ParallaxSection>
  );
};

export default ParallaxTestimonials;
