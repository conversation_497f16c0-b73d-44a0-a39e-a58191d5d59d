import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Blog, BlogCategory } from '@/types/database';
import { Link } from 'react-router-dom';
import { 
  Calendar, 
  Clock, 
  Search, 
  Tag,
  ChevronRight,
  Eye,
  BookOpen,
  Leaf
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/auth';
import { cn } from '@/lib/utils';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Parallax, Background } from 'react-parallax';

const BlogPage = () => {
  const { isAdmin } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Fetch blogs from Supabase
  const { data: blogs, isLoading: blogsLoading, error: blogsError } = useQuery({
    queryKey: ['blogs'],
    queryFn: async () => {
      try {
        // First get the blogs
        let query = supabase
          .from('blogs')
          .select('*')
          .order('published_at', { ascending: false });
        
        if (!isAdmin) {
          query = query.eq('is_published', true);
        }
        
        const { data: blogData, error } = await query;
        
        if (error) {
          console.error('Error fetching blogs:', error);
          return [];
        }

        // Then get author information for each blog
        if (blogData && blogData.length > 0) {
          const authorIds = blogData
            .filter(blog => blog.author_id)
            .map(blog => blog.author_id);
          
          if (authorIds.length > 0) {
            const { data: profilesData } = await supabase
              .from('profiles')
              .select('id, first_name, last_name')
              .in('id', authorIds);
            
            // Combine blog data with author data
            return blogData.map(blog => ({
              ...blog,
              author: profilesData?.find(profile => profile.id === blog.author_id) || null
            }));
          }
        }
        
        return blogData || [];
      } catch (error) {
        console.error('Error in blog query:', error);
        return [];
      }
    }
  });

  // Fetch blog categories
  const { data: categories = [], isLoading: categoriesLoading } = useQuery({
    queryKey: ['blog_categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('*')
        .order('name', { ascending: true });
      
      if (error) throw error;
      return data as BlogCategory[];
    }
  });

  // Filter blogs based on search query and selected category
  const filteredBlogs = blogs?.filter(blog => {
    // Skip if blog doesn't have required properties
    if (!blog || !blog.title || !blog.content) return false;
    
    const matchesSearch = searchQuery === '' || 
      blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (blog.summary ? blog.summary.toLowerCase().includes(searchQuery.toLowerCase()) : false) ||
      blog.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === null || blog.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get author name
  const getAuthorName = (blog: Blog & { author?: { first_name: string, last_name: string } }) => {
    if (blog.author?.first_name && blog.author?.last_name) {
      return `${blog.author.first_name} ${blog.author.last_name}`;
    }
    return 'Staff Writer';
  };

  // Create a truncated version of content for preview
  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // Set up parallax scroll effects
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 500], [0, 100]);
  const y2 = useTransform(scrollY, [0, 500], [0, -50]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Banner with Parallax Effect */}
      <Parallax
        blur={{ min: -15, max: 15 }}
        bgImage="/images/blog-header-bg.jpg"
        bgImageAlt="Blog Header Background"
        strength={300}
        className="overflow-hidden"
      >
        <div className="relative overflow-hidden">
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/80 z-10"></div>
          
          {/* Animated particles */}
          <div className="absolute inset-0 z-20 opacity-30">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full bg-white"
                style={{
                  width: Math.random() * 8 + 2,
                  height: Math.random() * 8 + 2,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, Math.random() * -100 - 50],
                  opacity: [0, 0.7, 0],
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  ease: "linear",
                }}
              />
            ))}
          </div>
          
          {/* Content */}
          <div className="container mx-auto px-4 py-24 md:py-32 relative z-30">
            <motion.div 
              className="max-w-3xl mx-auto text-center text-white"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div 
                className="flex items-center justify-center mb-6"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 260, damping: 20, delay: 0.2 }}
              >
                <Leaf className="h-10 w-10 text-white/90 mr-2" />
                <motion.div 
                  className="h-1 w-16 bg-white/60 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: 64 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                />
              </motion.div>
              
              <motion.h1 
                className="text-5xl md:text-6xl font-bold mb-4"
                style={{ y: y2 }}
              >
                Our Blog
              </motion.h1>
              
              <motion.p 
                className="text-xl md:text-2xl opacity-90 mb-10"
                style={{ y: y1 }}
              >
                Discover educational content about CBD, smoking culture, and product guides
              </motion.p>
              
              {/* Search Bar */}
              <motion.div 
                className="relative max-w-xl mx-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-white/70" />
                </div>
                <Input
                  type="text"
                  placeholder="Search articles..."
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus-visible:ring-white/30 h-12 text-lg"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </motion.div>
            </motion.div>
          </div>
          
          {/* Parallax height */}
          <div style={{ height: '400px' }} />
        </div>
      </Parallax>

      <div className="container mx-auto px-4 py-12">
        {/* Admin Controls */}
        {isAdmin && (
          <motion.div 
            className="flex justify-end mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Link to="/admin/blogs/new">
              <Button variant="default" className="group">
                <span>Create New Blog Post</span>
                <motion.span
                  className="ml-2 inline-block"
                  initial={{ scale: 1 }}
                  whileHover={{ scale: 1.2, rotate: 90 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  +
                </motion.span>
              </Button>
            </Link>
          </motion.div>
        )}
        
        {/* Category Filter */}
        <motion.div 
          className="mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <h2 className="text-xl font-semibold mb-4">Filter by Category</h2>
          <div className="flex flex-wrap gap-2">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Badge 
                variant={selectedCategory === null ? "default" : "outline"}
                className="cursor-pointer text-sm py-1.5 px-4"
                onClick={() => setSelectedCategory(null)}
              >
                All
              </Badge>
            </motion.div>
            {categories && categories.map((category, index) => (
              <motion.div 
                key={category.id}
                whileHover={{ scale: 1.05 }} 
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Badge 
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  className="cursor-pointer text-sm py-1.5 px-4"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Badge>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* Blog Posts */}
        {blogsLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="aspect-video w-full bg-gray-200">
                  <Skeleton className="h-full w-full" />
                </div>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-8 w-full mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-5/6" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredBlogs && filteredBlogs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredBlogs.map((blog, index) => (
              <motion.div
                key={blog.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * (index % 6), duration: 0.5 }}
                whileHover={{ y: -5 }}
              >
                <Card className="overflow-hidden h-full flex flex-col shadow-md hover:shadow-xl transition-shadow duration-300">
                  <Link to={`/blog/${blog.slug}`} className="block aspect-video w-full overflow-hidden bg-gray-100">
                    <motion.img 
                      src={blog.featured_image || '/placeholder.svg'} 
                      alt={blog.title} 
                      className="w-full h-full object-cover"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    />
                  </Link>
                  <CardContent className="p-6 flex-grow">
                    <div className="flex items-center text-sm text-gray-500 mb-3">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{formatDate(blog.published_at || blog.created_at)}</span>
                      <span className="mx-2">•</span>
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{blog.reading_time || 5} min read</span>
                    </div>
                    
                    <Link to={`/blog/${blog.slug}`}>
                      <h3 className="text-xl font-semibold mb-2 hover:text-primary transition-colors">
                        {blog.title}
                      </h3>
                    </Link>
                    
                    <p className="text-gray-600 mb-4">
                      {blog.summary || truncateContent(blog.content)}
                    </p>
                    
                    <div className="flex items-center justify-between mt-auto pt-4">
                      <div className="text-sm text-gray-500 flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        <span>{Math.floor(Math.random() * 100) + 10} views</span>
                      </div>
                      
                      <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                        <Badge variant="outline" className="text-xs py-1 px-2">
                          {blog.category || 'General'}
                        </Badge>
                      </motion.div>
                    </div>
                  </CardContent>
                  
                  <CardFooter className="px-6 py-4 bg-gray-50 border-t">
                    <Link to={`/blog/${blog.slug}`} className="text-primary text-sm font-medium group flex items-center w-full">
                      <span className="group-hover:underline">Read Article</span>
                      <motion.div
                        className="ml-1 inline-block"
                        initial={{ x: 0 }}
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </motion.div>
                      <span className="flex-grow"></span>
                      <span className="text-xs text-gray-400">By {getAuthorName(blog)}</span>
                    </Link>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500 font-medium">No blog posts found matching your criteria.</p>
            {searchQuery && (
              <Button 
                variant="link" 
                className="mt-2"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory(null);
                }}
              >
                Clear filters
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogPage;
