// Import the Supabase client
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: Supabase anon key is not defined in environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    // The SQL for the increment_blog_view function
    const sql = `
    -- Create function to increment blog view count
    CREATE OR REPLACE FUNCTION increment_blog_view(blog_id UUID)
    RETURNS void AS $$
    BEGIN
      UPDATE blogs
      SET view_count = view_count + 1
      WHERE id = blog_id;
    END;
    $$ LANGUAGE plpgsql;
    `;
    
    console.log('Applying increment_blog_view function migration...');
    
    // First try to check if we can connect to the database
    const { data: testData, error: testError } = await supabase.from('blogs').select('id').limit(1);
    
    if (testError) {
      console.error('Error connecting to database:', testError);
      return;
    }
    
    console.log('Successfully connected to database');
    
    // Try to use the stored procedure functionality if available
    console.log('Attempting to execute SQL...');
    
    try {
      // Try different approaches to execute the SQL
      
      // Approach 1: Using rpc
      const { data, error } = await supabase.rpc('pg_query', { query: sql });
      
      if (error) {
        console.log('Approach 1 failed:', error.message);
        throw error; // Move to next approach
      }
      
      console.log('Migration applied successfully using pg_query!');
      return;
    } catch (err1) {
      console.log('Trying alternative approach...');
      
      try {
        // Approach 2: Using functions API
        const { data, error } = await supabase.functions.invoke('execute-sql', {
          body: { sql }
        });
        
        if (error) {
          console.log('Approach 2 failed:', error.message);
          throw error; // Move to next approach
        }
        
        console.log('Migration applied successfully using functions API!');
        return;
      } catch (err2) {
        console.log('All automatic approaches failed');
        console.log('Please apply the migration manually through the Supabase dashboard.');
        console.log('SQL to execute:');
        console.log(sql);
      }
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

// Run the migration
runMigration();
