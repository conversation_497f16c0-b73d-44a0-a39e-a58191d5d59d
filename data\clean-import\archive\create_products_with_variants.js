const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// Input and output files
const productsFile = 'transformed_products.csv';
const variantsFile = 'transformed_variants_final.csv';
const outputProductsFile = 'products_with_variants.csv';
const outputVariantsFile = 'variants_for_selected_products.csv';

// Store products and their variants
const allProducts = [];
const allVariants = [];
const productIdsWithVariants = new Set();
const selectedProducts = [];
const selectedVariants = [];

// Process the variants file first to identify products with variants
console.log(`Reading variants from ${variantsFile}...`);
fs.createReadStream(variantsFile)
  .pipe(csv())
  .on('data', (row) => {
    allVariants.push(row);
    productIdsWithVariants.add(row.product_id);
  })
  .on('end', () => {
    console.log(`Read ${allVariants.length} variants from CSV`);
    console.log(`Found ${productIdsWithVariants.size} unique products with variants`);
    
    // Process the products file
    console.log(`Reading products from ${productsFile}...`);
    fs.createReadStream(productsFile)
      .pipe(csv())
      .on('data', (row) => {
        allProducts.push(row);
      })
      .on('end', async () => {
        console.log(`Read ${allProducts.length} products from CSV`);
        
        // Find products with variants
        for (const product of allProducts) {
          if (productIdsWithVariants.has(product.id)) {
            selectedProducts.push(product);
            
            // Limit to 5 products for testing
            if (selectedProducts.length >= 5) {
              break;
            }
          }
        }
        
        console.log(`Selected ${selectedProducts.length} products with variants`);
        
        // Find variants for selected products
        const selectedProductIds = new Set(selectedProducts.map(p => p.id));
        for (const variant of allVariants) {
          if (selectedProductIds.has(variant.product_id)) {
            selectedVariants.push(variant);
          }
        }
        
        console.log(`Found ${selectedVariants.length} variants for selected products`);
        
        // Create CSV writers
        const productsCsvWriter = createObjectCsvWriter({
          path: outputProductsFile,
          header: Object.keys(allProducts[0]).map(key => ({ id: key, title: key }))
        });
        
        const variantsCsvWriter = createObjectCsvWriter({
          path: outputVariantsFile,
          header: Object.keys(allVariants[0]).map(key => ({ id: key, title: key }))
        });
        
        // Write selected products and variants to CSV
        await productsCsvWriter.writeRecords(selectedProducts);
        console.log(`Created products file: ${outputProductsFile}`);
        
        await variantsCsvWriter.writeRecords(selectedVariants);
        console.log(`Created variants file: ${outputVariantsFile}`);
        
        // Print selected products and variants
        console.log('\nSelected products:');
        selectedProducts.forEach(product => {
          console.log(`- id: ${product.id}, name: ${product.name}`);
        });
        
        console.log('\nSelected variants (sample):');
        selectedVariants.slice(0, 10).forEach(variant => {
          console.log(`- product_id: ${variant.product_id}, variant_name: ${variant.variant_name}`);
        });
      });
  });
