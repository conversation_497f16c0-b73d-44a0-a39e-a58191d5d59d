/**
 * Utility functions for generating and managing SKUs for products
 */

/**
 * Generate a SKU based on product name, category, and brand
 * @param productName The name of the product
 * @param categoryName Optional category name
 * @param brandName Optional brand name
 * @returns A generated SKU string
 */
export function generateSKU(
  productName: string,
  categoryName?: string | null,
  brandName?: string | null
): string {
  // Clean up the product name
  const cleanName = productName
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '') // Remove non-alphanumeric characters
    .substring(0, 3); // Take first 3 characters
  
  // Get category prefix (first 2 chars)
  const categoryPrefix = categoryName
    ? categoryName.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 2)
    : 'XX';
  
  // Get brand prefix (first 2 chars)
  const brandPrefix = brandName
    ? brandName.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 2)
    : 'XX';
  
  // Generate a random 4-digit number
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  
  // Combine all parts to create the SKU
  return `${categoryPrefix}${brandPrefix}-${cleanName}${randomNum}`;
}

/**
 * Generate SKUs in bulk for multiple products
 * @param products Array of products with id, name, and optional category and brand
 * @returns Array of objects with id and generated SKU
 */
export function generateBulkSKUs(
  products: Array<{
    id: string;
    name: string;
    category_name?: string | null;
    brand_name?: string | null;
  }>
): Array<{ id: string; sku: string }> {
  // Set to track used SKUs to avoid duplicates
  const usedSKUs = new Set<string>();
  
  return products.map(product => {
    // Try to generate a unique SKU (avoid collisions)
    let sku = generateSKU(product.name, product.category_name, product.brand_name);
    let attempts = 0;
    
    // If SKU is already used, try again with a different random number
    while (usedSKUs.has(sku) && attempts < 5) {
      sku = generateSKU(product.name, product.category_name, product.brand_name);
      attempts++;
    }
    
    // If we still have a collision after 5 attempts, add a timestamp to ensure uniqueness
    if (usedSKUs.has(sku)) {
      sku = `${sku}-${Date.now().toString().substring(8, 12)}`;
    }
    
    usedSKUs.add(sku);
    return { id: product.id, sku };
  });
}

/**
 * Validate if a SKU is properly formatted
 * @param sku The SKU to validate
 * @returns Boolean indicating if the SKU is valid
 */
export function isValidSKU(sku: string): boolean {
  // Basic validation - SKU should be alphanumeric, may include hyphens or underscores
  // and should be between 4-20 characters
  const skuRegex = /^[A-Z0-9_-]{4,20}$/i;
  return skuRegex.test(sku);
}

/**
 * Format a SKU to ensure consistency
 * @param sku The SKU to format
 * @returns Formatted SKU string
 */
export function formatSKU(sku: string): string {
  // Convert to uppercase and remove any invalid characters
  return sku.toUpperCase().replace(/[^A-Z0-9_-]/g, '');
}