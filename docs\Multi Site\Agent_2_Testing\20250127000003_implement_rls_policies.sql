-- Migration: 20250127000003_implement_rls_policies.sql
-- Description: Implements Row Level Security policies for tenant isolation

-- Enable Row Level Security on all tables
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.promotions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for tenant isolation - Products
CREATE POLICY tenant_isolation_products ON public.products
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Categories
CREATE POLICY tenant_isolation_categories ON public.categories
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Customers
CREATE POLICY tenant_isolation_customers ON public.customers
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Orders
CREATE POLICY tenant_isolation_orders ON public.orders
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Inventory
CREATE POLICY tenant_isolation_inventory ON public.inventory
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Suppliers
CREATE POLICY tenant_isolation_suppliers ON public.suppliers
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Promotions
CREATE POLICY tenant_isolation_promotions ON public.promotions
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Settings
CREATE POLICY tenant_isolation_settings ON public.settings
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - Blog Posts
CREATE POLICY tenant_isolation_blog_posts ON public.blog_posts
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create policy for tenant isolation - User Profiles
CREATE POLICY tenant_isolation_user_profiles ON public.user_profiles
    USING (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id))
    WITH CHECK (tenant_id = tenant_management.get_current_tenant_id() OR tenant_management.validate_tenant_access(tenant_id));

-- Create function to validate tenant_id on update
CREATE OR REPLACE FUNCTION tenant_management.validate_tenant_id_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent changing tenant_id
    IF OLD.tenant_id != NEW.tenant_id THEN
        RAISE EXCEPTION 'Cannot change tenant_id';
    END IF;
    
    -- Ensure user has access to this tenant
    IF NOT tenant_management.validate_tenant_access(NEW.tenant_id) THEN
        RAISE EXCEPTION 'Access denied to tenant %', NEW.tenant_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to prevent tenant_id changes
CREATE TRIGGER validate_tenant_id_update_products
BEFORE UPDATE ON public.products
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_categories
BEFORE UPDATE ON public.categories
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_customers
BEFORE UPDATE ON public.customers
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_orders
BEFORE UPDATE ON public.orders
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_inventory
BEFORE UPDATE ON public.inventory
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_suppliers
BEFORE UPDATE ON public.suppliers
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_promotions
BEFORE UPDATE ON public.promotions
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_settings
BEFORE UPDATE ON public.settings
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_blog_posts
BEFORE UPDATE ON public.blog_posts
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();

CREATE TRIGGER validate_tenant_id_update_user_profiles
BEFORE UPDATE ON public.user_profiles
FOR EACH ROW EXECUTE FUNCTION tenant_management.validate_tenant_id_update();
