# Multi-Tenant E-commerce Platform Architecture

## Overview
Transform the Bits N Bongs codebase into a flexible, multi-tenant SaaS platform for any type of e-commerce business.

## Current Advantages
- ✅ Complete e-commerce functionality
- ✅ AI-powered features (content, images, newsletters)
- ✅ Admin dashboard and user management
- ✅ Template-based newsletter system
- ✅ Supabase backend (perfect for multi-tenancy)
- ✅ Component-based React architecture

## Multi-Tenant Implementation Plan

### Phase 1: Core Multi-Tenancy (2-3 weeks)

#### Database Schema
```sql
-- Tenants table
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  domain TEXT UNIQUE,
  subdomain TEXT UNIQUE,
  industry TEXT,
  logo_url TEXT,
  text_logo_url TEXT,
  favicon_url TEXT,
  primary_color TEXT DEFAULT '#2D5016',
  secondary_color TEXT DEFAULT '#4A7C59',
  accent_color TEXT DEFAULT '#8FBC8F',
  settings JSONB DEFAULT '{}',
  ai_config JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Add tenant_id to all main tables
ALTER TABLE products ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE orders ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE categories ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE brands ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE newsletters ADD COLUMN tenant_id UUID REFERENCES tenants(id);
```

#### Tenant Context System
```typescript
// Context for current tenant
interface TenantConfig {
  id: string;
  name: string;
  domain: string;
  branding: {
    logo: string;
    textLogo: string;
    colors: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
  industry: string;
  aiConfig: {
    openaiKey?: string;
    geminiKey?: string;
    deepseekKey?: string;
  };
  settings: Record<string, any>;
}
```

### Phase 2: Branding & Customization (1-2 weeks)

#### Dynamic Theming
- CSS custom properties for colors
- Logo upload and management
- Industry-specific templates
- Customizable email templates

#### Industry Templates
- **Cannabis/CBD**: Current Bits N Bongs styling
- **Fashion/Clothing**: Clean, modern aesthetic
- **Electronics**: Tech-focused design
- **General Retail**: Neutral, professional

### Phase 3: AI Configuration (1 week)

#### Per-Tenant AI Settings
```typescript
interface AIConfig {
  providers: {
    openai?: { apiKey: string; model: string; };
    gemini?: { apiKey: string; model: string; };
    deepseek?: { apiKey: string; model: string; };
  };
  contentSettings: {
    tone: string;
    industry: string;
    brandVoice: string;
  };
}
```

#### Industry-Specific Prompts
- Cannabis: Compliance-aware, educational
- Fashion: Trendy, style-focused
- Electronics: Technical, feature-focused

### Phase 4: Advanced Features (2-3 weeks)

#### Tenant Management Dashboard
- Tenant creation and configuration
- Usage analytics and billing
- Feature toggles per tenant
- Backup and migration tools

#### White-Label Options
- Custom domains
- Remove platform branding
- Custom login pages
- Tenant-specific admin areas

## Technical Implementation

### Routing Strategy
```typescript
// Domain-based routing
const getTenantFromDomain = (domain: string) => {
  // subdomain.platform.com or custom.domain.com
  if (domain.includes('.yourplatform.com')) {
    return domain.split('.')[0]; // subdomain
  }
  return lookupCustomDomain(domain);
};
```

### Data Isolation
```sql
-- Row Level Security policies
CREATE POLICY tenant_isolation ON products
  FOR ALL USING (tenant_id = current_tenant_id());

-- Function to get current tenant
CREATE OR REPLACE FUNCTION current_tenant_id()
RETURNS UUID AS $$
BEGIN
  RETURN current_setting('app.current_tenant_id')::UUID;
END;
$$ LANGUAGE plpgsql;
```

### OAuth Implementation for Multi-Tenancy

```typescript
// Tenant-Aware Authentication Service
function useAuth() {
  const { tenant } = useTenant();
  
  const signIn = async (provider: 'google' | 'apple' | 'facebook') => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback?tenant=${tenant.id}`,
        queryParams: {
          tenant_id: tenant.id
        }
      }
    });
  };
  
  // Other auth methods
  
  return { signIn, /*...*/ };
}

// Tenant-User Association on Registration
async function handleAuthCallback() {
  const { data: { session } } = await supabase.auth.getSession();
  const tenantId = new URL(window.location.href).searchParams.get('tenant');
  
  if (session?.user) {
    // Associate user with tenant
    await supabase.from('tenant_users').insert({
      tenant_id: tenantId,
      user_id: session.user.id,
      role: 'member'
    });
    
    // Update user profile with current tenant
    await supabase.from('profiles').update({
      current_tenant_id: tenantId
    }).eq('id', session.user.id);
  }
}

// JWT Custom Claims for Tenant Context (Supabase Edge Functions)
export async function customizeJWT(event) {
  const { token, user } = event;
  
  // Get user's tenant associations
  const { data: tenantUsers } = await supabaseAdmin
    .from('tenant_users')
    .select('tenant_id, role')
    .eq('user_id', user.id);
  
  // Get current tenant from profile
  const { data: profile } = await supabaseAdmin
    .from('profiles')
    .select('current_tenant_id')
    .eq('id', user.id)
    .single();
  
  // Add tenant info to JWT
  token.tenants = tenantUsers.map(tu => ({
    id: tu.tenant_id,
    role: tu.role
  }));
  
  token.current_tenant_id = profile?.current_tenant_id;
  
  return token;
}
```

```sql
-- Function to automatically set tenant context from JWT
CREATE OR REPLACE FUNCTION apply_tenant_from_jwt()
RETURNS TRIGGER AS $$
DECLARE
  tenant_id UUID;
BEGIN
  -- Extract tenant_id from JWT claims
  tenant_id := nullif(current_setting('request.jwt.claims', true)::json->>'current_tenant_id', '')::UUID;
  
  -- Set tenant context
  IF tenant_id IS NOT NULL THEN
    PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, true);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply as a trigger
CREATE TRIGGER set_tenant_context_trigger
  BEFORE SELECT ON products
  FOR EACH STATEMENT
  EXECUTE FUNCTION apply_tenant_from_jwt();
```

```jsx
// Tenant Switching UI Component
function TenantSwitcher() {
  const { user } = useAuth();
  const { currentTenant, setCurrentTenant } = useTenant();
  const [tenants, setTenants] = useState([]);
  
  useEffect(() => {
    // Fetch tenants the user belongs to
    async function fetchUserTenants() {
      const { data } = await supabase
        .from('tenant_users')
        .select(`
          tenant_id,
          role,
          tenants:tenant_id(id, name, logo_url)
        `)
        .eq('user_id', user?.id);
      
      setTenants(data.map(item => ({
        id: item.tenant_id,
        name: item.tenants.name,
        logo: item.tenants.logo_url,
        role: item.role
      })));
    }
    
    if (user) fetchUserTenants();
  }, [user]);
  
  const handleTenantChange = async (tenantId) => {
    // Update user's current tenant in profile
    await supabase
      .from('profiles')
      .update({ current_tenant_id: tenantId })
      .eq('id', user.id);
    
    // Update local state
    setCurrentTenant(tenants.find(t => t.id === tenantId));
    
    // Reload to refresh JWT and tenant context
    window.location.reload();
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <img src={currentTenant.logo} alt={currentTenant.name} />
        {currentTenant.name}
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {tenants.map(tenant => (
          <DropdownMenuItem 
            key={tenant.id}
            onClick={() => handleTenantChange(tenant.id)}
          >
            <img src={tenant.logo} alt={tenant.name} />
            {tenant.name}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/tenants/create">Create new store</Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

### Component Architecture
```typescript
// Tenant-aware components
const ProductCard = ({ product }: { product: Product }) => {
  const { branding } = useTenant();
  
  return (
    <div style={{ 
      borderColor: branding.colors.primary,
      // ... tenant-specific styling
    }}>
      {/* Product content */}
    </div>
  );
};
```

## Business Model Options

### 1. SaaS Subscription
- **Starter**: $29/month - Basic features, 100 products
- **Professional**: $99/month - AI features, 1000 products
- **Enterprise**: $299/month - Custom branding, unlimited

### 2. Revenue Sharing
- Take 2-3% of transaction fees
- Provide payment processing
- Scale with client success

### 3. Setup + Monthly
- $500 setup fee per tenant
- $49/month maintenance
- Custom development extra

## Migration Strategy

### Existing Bits N Bongs
1. Create first tenant record
2. Add tenant_id to existing data
3. Update queries to include tenant filter
4. Test thoroughly

### New Tenants
1. Tenant onboarding flow
2. Data import tools
3. Branding customization
4. Training and support

## Key Benefits

### For You (Platform Owner)
- **Recurring Revenue**: Monthly subscriptions
- **Scalable**: One codebase, many clients
- **Leverage Existing Work**: 90% already built
- **AI Differentiation**: Unique selling point

### For Clients
- **Quick Setup**: Days not months
- **AI-Powered**: Content and marketing automation
- **Professional**: Enterprise-grade features
- **Cost-Effective**: Fraction of custom development

## Next Steps

1. **Validate Market**: Survey potential clients
2. **Plan Database Migration**: Design tenant schema
3. **Build Tenant Management**: Admin interface
4. **Create Onboarding Flow**: New client setup
5. **Develop Pricing Strategy**: Market research
6. **Legal Structure**: Terms, privacy, SLA

## Estimated Timeline
- **Phase 1-2**: 4-5 weeks (core multi-tenancy)
- **Phase 3-4**: 3-4 weeks (advanced features)
- **Total**: 2-3 months to full platform

## Risk Mitigation
- Keep Bits N Bongs as primary tenant
- Gradual migration approach
- Extensive testing environment
- Backup and rollback plans
