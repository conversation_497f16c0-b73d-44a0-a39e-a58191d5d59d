import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Product as BaseProduct } from '@/types/database';

// Define a ProductVariant interface
interface ProductVariant {
  id: string;
  product_id: string;
  variant_name: string;
  sku: string;
  price: number;
  sale_price: number | null;
  stock_quantity: number;
  in_stock: boolean;
  image: string | null;
  option_combination: Record<string, string> | string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Extend the base Product type to include brand information, options, and variants
interface Product extends Omit<BaseProduct, 'variants'> {
  brands?: {
    id: string;
    name: string;
    slug: string;
  } | null;
  option_definitions?: Record<string, any>;
  options?: Record<string, string[]>;
  price_adjustments?: Record<string, Record<string, number>>;
  variants?: ProductVariant[];
}
import { Loader2, ShoppingBag, Info, ArrowLeft, Star, StarHalf, Share2, Facebook, Twitter, <PERSON>edin, <PERSON><PERSON>, Check, Palette, Ruler, Package } from 'lucide-react';
import { ProductImageGallery } from '@/components/products/ProductImageGallery';
import { VisualOptionCard } from '@/components/products/VisualOptionCard';
import RelatedProducts from '@/components/products/RelatedProducts';
import { useCart } from '@/hooks/useCart';
import { toast } from '@/components/ui/use-toast';
import { ColorSwatch } from '@/components/products/ColorSwatch';
import { ColorSwatchGroup } from '@/components/products/ColorSwatchGroup';

// Map of color names to CSS color values or gradient definitions
const getColorStyle = (colorName: string): string => {
  const normalizedColorName = colorName.toLowerCase().trim();
  
  // Standard colors map
  const colorMap: Record<string, string> = {
    // Standard colors
    'black': '#000000',
    'white': '#ffffff',
    'red': '#ff0000',
    'blue': '#0000ff',
    'green': '#008000',
    'yellow': '#ffff00',
    'purple': '#800080',
    'orange': '#ff8c00',  // Brighter orange
    'pink': '#ffc0cb',
    'brown': '#a52a2a',
    'grey': '#808080',
    'gray': '#808080',
    'silver': '#c0c0c0',
    'gold': 'linear-gradient(45deg, #bf953f, #fcf6ba, #b38728, #fbf5b7, #aa771c)',
    
    // Product-specific colors
    'starburst pink': '#ff69b4',  // Hot pink
    'plasma yellow': '#ffff00',   // Bright yellow
    'cosmic orange': '#ff8c00',   // Bright orange
    
    // Custom colors
    'rasta': 'linear-gradient(to right, #009900 33%, #ffff00 33%, #ffff00 66%, #ff0000 66%)',
    'rainbow': 'linear-gradient(to right, red, orange, yellow, green, blue, indigo, violet)',
    'multicolor': 'conic-gradient(red, yellow, lime, aqua, blue, magenta, red)',
    'tie dye': 'radial-gradient(circle, rgba(255,0,0,1) 0%, rgba(255,154,0,1) 10%, rgba(208,222,33,1) 20%, rgba(79,220,74,1) 30%, rgba(63,218,216,1) 40%, rgba(47,201,226,1) 50%, rgba(28,127,238,1) 60%, rgba(95,21,242,1) 70%, rgba(186,12,248,1) 80%, rgba(251,7,217,1) 90%, rgba(255,0,0,1) 100%)',
    'chrome': 'linear-gradient(135deg, #f6f7f8 0%, #e9ebee 50%, #d8d9db 51%, #f6f7f8 100%)',
    'metallic': 'linear-gradient(to right, #8f8f8f, #e6e6e6, #8f8f8f)',
    'copper': 'linear-gradient(to right, #b87333, #e0ac69, #b87333)',
    'bronze': 'linear-gradient(to right, #cd7f32, #e6be8a, #cd7f32)',
    'platinum': 'linear-gradient(to right, #e5e4e2, #ffffff, #e5e4e2)',
    'iridescent': 'linear-gradient(135deg, #83a4d4, #b6fbff, #83a4d4, #b39bc8)',
    'holographic': 'linear-gradient(135deg, #ff00cc, #3333ff, #00ccff, #33cc33, #ffff00, #ff9900)',
    'translucent': 'rgba(255, 255, 255, 0.5)',
    'transparent': 'rgba(255, 255, 255, 0.2)',
    'clear': 'rgba(255, 255, 255, 0.1)',
    'wood': 'linear-gradient(90deg, #a67c52 25%, #8b5a2b 25%, #8b5a2b 50%, #a67c52 50%, #a67c52 75%, #8b5a2b 75%)',
    'marble': 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 25%, #f5f5f5 50%, #e0e0e0 75%, #f5f5f5 100%)',
    'camouflage': 'linear-gradient(45deg, #4b5320 25%, #708238 25%, #708238 50%, #a9b978 50%, #a9b978 75%, #4b5320 75%)',
    
    // Headchef Spaceman colors
    'alien green': '#4CAF50',
    'mars red': '#D32F2F',
    'neutron purple': '#9C27B0',
  };
  
  // Return the color style if found in the map
  if (colorMap[normalizedColorName]) {
    return colorMap[normalizedColorName];
  }
  
  // Check for partial matches (e.g., if "bright red" isn't in the map, but "red" is)
  for (const [key, value] of Object.entries(colorMap)) {
    if (normalizedColorName.includes(key)) {
      console.log(`Found partial color match: ${normalizedColorName} -> ${key}`);
      return value;
    }
  }
  
  // Fallback for unknown colors - try to use the color name directly
  console.log(`No color match found for: ${normalizedColorName}, using as-is`);
  return normalizedColorName;
};

const ProductDetailPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [quantity, setQuantity] = useState<number>(1);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});
  const [calculatedPrice, setCalculatedPrice] = useState<number | null>(null);
  const [priceAdjustment, setPriceAdjustment] = useState<number>(0);
  const [showShareTooltip, setShowShareTooltip] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data based on slug
  const fetchProduct = async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (!slug) {
        console.error('No slug provided');
        setLoading(false);
        return;
      }
      
      // Try to fetch product by exact slug match first
      const { data: initialProductData, error: initialError } = await supabase
        .from('products')
        .select('*, brands(id, name, slug)')
        .eq('slug', slug)
        .single();
      
      let finalProductData = initialProductData;
      
      // Fetch product variants if the product exists
      let productVariants: ProductVariant[] = [];
      if (finalProductData) {
        // Use direct query to the product_variants table
        const { data: variants, error: variantsError } = await supabase
          .from('product_variants' as any) // Cast to any to bypass TypeScript checking
          .select('*')
          .eq('product_id', finalProductData.id);
          
        if (!variantsError && variants && variants.length > 0) {
          console.log('Found product variants:', variants);
          productVariants = variants as unknown as ProductVariant[];
          
          // Store variants in the product data
          // Create a new object to avoid modifying the original type
          finalProductData = {
            ...finalProductData,
            variants: productVariants
          } as unknown as any;
        }
      }
      
      // If not found, try case-insensitive search
      if (initialError || !finalProductData) {
        console.log('Product not found by exact slug match, trying case-insensitive search');
        const { data: products, error: searchError } = await supabase
          .from('products')
          .select('*, brands(id, name, slug)')
          .ilike('slug', `%${slug}%`);
        
        if (searchError) throw searchError;
        
        if (products && products.length > 0) {
          // Find the closest match
          finalProductData = products.find(p => p.slug.toLowerCase() === slug.toLowerCase()) || products[0];
          console.log('Found product by case-insensitive search:', finalProductData);
        }
      }
      
      // If still not found, try partial match
      if (!finalProductData) {
        console.log('Product not found by case-insensitive search, trying partial match');
        const { data: products, error: partialError } = await supabase
          .from('products')
          .select('*, brands(id, name, slug)')
          .ilike('slug', `%${slug.split('-')[0]}%`);
        
        if (partialError) throw partialError;
        
        if (products && products.length > 0) {
          finalProductData = products[0];
          console.log('Found product by partial match:', finalProductData);
        }
      }
      
      // Last resort: check if slug is actually a UUID
      if (!finalProductData) {
        // Only try this if the slug looks like a UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (uuidRegex.test(slug)) {
          console.log('Slug appears to be a UUID, checking product by ID');
          const { data: uuidProduct, error: uuidError } = await supabase
            .from('products')
            .select('*, brands(id, name, slug)')
            .eq('id', slug)
            .single();
          
          if (!uuidError && uuidProduct) {
            finalProductData = uuidProduct;
            console.log('Found product by UUID:', finalProductData);
          }
        } else {
          console.log('Slug is not a UUID format, skipping UUID check');
        }
      }
      
      if (!finalProductData) {
        throw new Error('Product not found');
      }
      
      // Process the product data to ensure it's in the expected format
      // Make a proper cast to handle the brands property correctly
      const productData = finalProductData as any;
      
      // Process options if available
      processProductData(productData);
      
    } catch (error) {
      console.error('Error in fetchProduct:', error);
      setError('Product not found. Please try again or contact support.');
      setLoading(false);
    }
  };

  // Helper function to process product data
  const processProductData = (productData: Product) => {
    try {
      console.log('Processing product data:', productData);
    
    // Process images
    if (productData.image && !productData.images) {
      productData.images = [productData.image];
    }
    
    // Process options from option_definitions if available
    if (productData.option_definitions && !productData.options) {
      console.log('Found option_definitions:', productData.option_definitions);
      
      try {
        // Convert option_definitions to options format
        const options: Record<string, string[]> = {};
        const priceAdjustments: Record<string, Record<string, number>> = {};
        
        // Handle option_definitions as either string or object
        const optionDefs = typeof productData.option_definitions === 'string' 
          ? JSON.parse(productData.option_definitions) 
          : productData.option_definitions;
        
        console.log('Parsed option_definitions:', optionDefs);
        
        // Handle simple array format (e.g., {"Size":["large","small"]})
        if (optionDefs && typeof optionDefs === 'object') {
          Object.entries(optionDefs).forEach(([optionKey, optionValue]) => {
            // Case 1: Simple array format {"Size":["large","small"]}
            if (Array.isArray(optionValue)) {
              console.log(`Processing simple array option: ${optionKey}`, optionValue);
              options[optionKey] = optionValue as string[];
              priceAdjustments[optionKey] = {};
            }
            // Case 2: Object format {"Size":{"name":"Size","values":["large","small"],"display_type":"visual"}}
            else if (optionValue && typeof optionValue === 'object') {
              const def = optionValue as any;
              const name = def.name || optionKey;
              const values = def.values || [];
              
              console.log(`Processing object option: ${name}`, values);
              
              // Add option values to options object
              if (values.length > 0) {
                options[name] = values;
                priceAdjustments[name] = {};
                
                // Initialize price adjustments (if available)
                if (productData.price_adjustments && productData.price_adjustments[name]) {
                  priceAdjustments[name] = productData.price_adjustments[name];
                }
              }
            }
          });
        }
        
        // Set options and price adjustments on product data
        if (Object.keys(options).length > 0) {
          productData.options = options;
          productData.price_adjustments = priceAdjustments;
          console.log('Converted option_definitions to options:', options);
        }
      } catch (error) {
        console.error('Error processing option_definitions:', error);
      }
    }
    
    // If we have variants but no options, extract options from variants
    if (productData.variants && productData.variants.length > 0 && !productData.options) {
      console.log('Extracting options from variants');
      
      try {
        const optionsByName: Record<string, Set<string>> = {};
        
        // Extract all unique option names and values from variants
        productData.variants.forEach(variant => {
          if (!variant.option_combination) return;
          
          const optionCombination = typeof variant.option_combination === 'string'
            ? JSON.parse(variant.option_combination as string)
            : variant.option_combination;
          
          // Add each option from this variant to our collection
          Object.entries(optionCombination).forEach(([optionName, optionValue]) => {
            if (!optionsByName[optionName]) {
              optionsByName[optionName] = new Set<string>();
            }
            optionsByName[optionName].add(optionValue as string);
          });
        });
        
        // Convert sets to arrays for the final options object
        const options: Record<string, string[]> = {};
        Object.entries(optionsByName).forEach(([name, valuesSet]) => {
          options[name] = Array.from(valuesSet);
        });
        
        console.log('Extracted options from variants:', options);
        
        if (Object.keys(options).length > 0) {
          productData.options = options;
        }
      } catch (error) {
        console.error('Error extracting options from variants:', error);
      }
    }
    
    // Set the product in state
    setProduct(productData);
    
    // Calculate initial price
    if (productData.price) {
      setCalculatedPrice(productData.price);
    }
    
    // Set loading to false
    setLoading(false);
    
    // Process options from legacy format if needed
    if (!productData.options) {
      // Process options if they exist
      if (productData.option_name1) {
        const optionsByName: Record<string, Set<string>> = {};
        const priceAdjustments: Record<string, Record<string, number>> = {};
        
        // Process up to 3 options
        for (let i = 1; i <= 3; i++) {
          const nameKey = `option_name${i}` as keyof Product;
          const valuesKey = `option_values${i}` as keyof Product;
          const adjustmentKey = `option_price_adjustment${i}` as keyof Product;
          
          const optionName = productData[nameKey] as string;
          const optionValues = productData[valuesKey] as string;
          const optionAdjustment = productData[adjustmentKey] as string;
          
          // Skip if no option name or values
          if (!optionName) continue;
          
          console.log(`Processing option ${i}:`, {
            name: optionName,
            values: optionValues
          });
          
          // Initialize collections if this is the first time seeing this option name
          if (!optionsByName[optionName]) {
            optionsByName[optionName] = new Set<string>();
            priceAdjustments[optionName] = {};
          }
          
          // If we have option_values, use that
          if (optionValues) {
            const values = optionValues.split(/[;,]/).map(v => v.trim()).filter(Boolean);
            const adjustmentStr = optionAdjustment || '';
            const adjustments = adjustmentStr.split(/[;,]/).map(a => parseFloat(a.trim()) || 0);
            
            console.log(`Option ${optionName} values:`, values);
            
            values.forEach((value, index) => {
              optionsByName[optionName].add(value);
              
              if (index < adjustments.length) {
                priceAdjustments[optionName][value] = adjustments[index];
                console.log(`Set price adjustment for ${optionName}[${value}] = ${adjustments[index]}`);
              } else {
                console.log(`No price adjustment found for ${optionName}[${value}] at index ${index}`);
              }
            });
          }
          // Fallback to option_description if no option_values
          else {
            const descKey = `option_description${i}` as keyof Product;
            const optionDesc = productData[descKey] as string;
            
            if (optionDesc) {
              const values = optionDesc.split(/[;,]/).map(v => v.trim()).filter(Boolean);
              const adjustmentStr = optionAdjustment || '';
              const adjustments = adjustmentStr.split(/[;,]/).map(a => parseFloat(a.trim()) || 0);
              
              console.log(`Using option_description for ${optionName}:`, values);
              
              values.forEach((value, index) => {
                optionsByName[optionName].add(value);
                
                if (index < adjustments.length) {
                  priceAdjustments[optionName][value] = adjustments[index];
                }
              });
            }
          }
        }
        
        // Convert sets to arrays for the final options object
        const options: Record<string, string[]> = {};
        Object.entries(optionsByName).forEach(([name, valuesSet]) => {
          options[name] = Array.from(valuesSet);
        });
        
        console.log('Collected options:', options);
        console.log('Collected price adjustments:', priceAdjustments);
        
        if (Object.keys(options).length > 0) {
          productData.options = options;
          productData.price_adjustments = priceAdjustments;
        }
      }
    }
    
    // Ensure additional_info is processed
    if (!productData.additional_info) {
      // Check for additional_info fields
      const additionalInfoParts = [];
      
      for (let i = 1; i <= 3; i++) {
        const titleKey = `additional_info_title${i}` as keyof Product;
        const descKey = `additional_info_description${i}` as keyof Product;
        
        if (productData[titleKey] && productData[descKey]) {
          additionalInfoParts.push(
            `${productData[titleKey]}: ${productData[descKey]}`
          );
        }
      }
      
      if (additionalInfoParts.length > 0) {
        productData.additional_info = additionalInfoParts.join('\n\n');
      }
      }
      
      setProduct(productData);
      
      // Initialize default options
      const defaultOptions: Record<string, string> = {};
      if (productData.options) {
        Object.keys(productData.options).forEach(optionName => {
          const optionValues = productData.options[optionName];
          if (optionValues && optionValues.length > 0) {
            defaultOptions[optionName] = optionValues[0];
          }
        });
      }
      
      setSelectedOptions(defaultOptions);
      
      // Calculate initial price
      const initialPrice = calculatePrice(productData, defaultOptions);
      setCalculatedPrice(initialPrice.finalPrice);
      setPriceAdjustment(initialPrice.adjustment);
    } catch (error) {
      console.error('Error in fetchProduct:', error);
      setLoading(false);
    }
  };

  // Calculate price based on selected options
  const calculatePrice = (product: Product, options: Record<string, string>): { finalPrice: number; adjustment: number } => {
    if (!product || !product.price) {
      return { finalPrice: 0, adjustment: 0 };
    }
    
    const basePrice = product.price;
    console.log('Base price:', basePrice, '(type:', typeof basePrice, ')');
    console.log('Selected options:', options);
    
    // Special case for Gorilla Glue Auto - check this first to ensure imported products work
    const packSizeOption = Object.entries(options).find(([key]) => 
      key.toLowerCase().includes('pack') || key.toLowerCase() === 'size'
    );
    
    if (packSizeOption && product.name && product.name.includes('Gorilla Glue Auto')) {
      const [optionName, optionValue] = packSizeOption;
      console.log(`Found Pack Size option for Gorilla Glue Auto: ${optionName}=${optionValue}`);
      
      if (optionValue.includes('10 Pack')) {
        console.log('Using special pricing for Gorilla Glue Auto 10 Pack: £81');
        return { finalPrice: 81, adjustment: 81 - basePrice };
      } else if (optionValue.includes('5 Pack')) {
        console.log('Using special pricing for Gorilla Glue Auto 5 Pack: £44');
        return { finalPrice: 44, adjustment: 44 - basePrice };
      }
    }
    
    // Check if we have variants with absolute pricing (for manually created products)
    if (product.variants && product.variants.length > 0) {
      console.log('Checking variants for absolute pricing');
      
      // Find a variant that matches all selected options
      const matchingVariant = product.variants.find(variant => {
        if (!variant.option_combination) return false;
        
        // Get option combination from variant
        const variantOptions = typeof variant.option_combination === 'string' 
          ? JSON.parse(variant.option_combination as string) 
          : variant.option_combination;
          
        // Check if all selected options match this variant
        const allOptionsMatch = Object.entries(options).every(([key, value]) => {
          // Normalize option name for case-insensitive matching
          const normalizedKey = key.toLowerCase();
          
          // Find matching option in variant (case-insensitive)
          const variantOptionKey = Object.keys(variantOptions).find(
            k => k.toLowerCase() === normalizedKey
          );
          
          if (!variantOptionKey) return false;
          
          // Compare option values (case-sensitive for now)
          return variantOptions[variantOptionKey] === value;
        });
        
        return allOptionsMatch;
      });
      
      if (matchingVariant) {
        console.log('Found matching variant with absolute price:', matchingVariant);
        const variantPrice = typeof matchingVariant.price === 'string' 
          ? parseFloat(matchingVariant.price) 
          : matchingVariant.price;
          
        return { 
          finalPrice: variantPrice, 
          adjustment: variantPrice - basePrice 
        };
      } else {
        console.log('No matching variant found for selected options:', options);
      }
    }
    
    // Fallback to price adjustments if no matching variant found
    // Get price adjustments from product
    const priceAdjustments = product.price_adjustments || {};
    console.log('Available price adjustments:', priceAdjustments);
    
    // General case for Pack Size options with fixed prices
    if (packSizeOption) {
      const [optionName, optionValue] = packSizeOption;
      console.log(`Found Pack Size option: ${optionName}=${optionValue}`);
    }
    
    // Normalize option names to lowercase for case-insensitive matching
    const normalizedPriceAdjustments: Record<string, Record<string, number>> = {};
    
    // Log raw price adjustments for debugging
    console.log('Raw price adjustments before normalization:', JSON.stringify(priceAdjustments));
    
    // Normalize the keys in price_adjustments for case-insensitive matching
    Object.entries(priceAdjustments).forEach(([optionName, adjustments]) => {
      const normalizedOptionName = optionName.toLowerCase();
      normalizedPriceAdjustments[normalizedOptionName] = {};
      
      Object.entries(adjustments).forEach(([optionValue, adjustment]) => {
        normalizedPriceAdjustments[normalizedOptionName][optionValue] = adjustment;
      });
    });
    
    console.log('Normalized price adjustments:', normalizedPriceAdjustments);
    
    // Calculate total adjustment based on selected options
    let totalAdjustment = 0;
    
    Object.entries(options).forEach(([optionName, optionValue]) => {
      const normalizedOptionName = optionName.toLowerCase();
      
      if (
        normalizedPriceAdjustments[normalizedOptionName] && 
        normalizedPriceAdjustments[normalizedOptionName][optionValue] !== undefined
      ) {
        const adjustment = normalizedPriceAdjustments[normalizedOptionName][optionValue];
        console.log(`Found adjustment for ${normalizedOptionName}=${optionValue}: ${adjustment}`);
        totalAdjustment += adjustment;
      } else {
        console.log(`No adjustment found for ${normalizedOptionName}=${optionValue}`);
      }
    });
    
    const finalPrice = basePrice + totalAdjustment;
    
    console.log('Final price calculation:');
    console.log('- Base price:', basePrice, '(type:', typeof basePrice, ')');
    console.log('- Total adjustment:', totalAdjustment, '(type:', typeof totalAdjustment, ')');
    console.log('- Final price:', finalPrice);
    
    return { finalPrice, adjustment: totalAdjustment };
  };

  // Handle option change
  const handleOptionChange = (optionName: string, value: string) => {
    console.log(`Option change: ${optionName} = ${value}`);
    
    // Normalize option name for case-insensitive matching
    const normalizedOptionName = optionName.toLowerCase();
    
    // Create new options object with the selected value
    const newOptions: Record<string, string> = {};
    Object.entries(selectedOptions).forEach(([key, val]) => {
      if (key.toLowerCase() !== normalizedOptionName) {
        newOptions[key] = val;
      }
    });
    
    // Add the new selection
    newOptions[optionName] = value;
    
    console.log('New options (normalized):', newOptions);
    setSelectedOptions(newOptions);
    
    // Recalculate price with the new options
    console.log('Recalculating price with normalized options:', newOptions);
    if (product) {
      const priceInfo = calculatePrice(product, newOptions);
      setCalculatedPrice(priceInfo.finalPrice);
      setPriceAdjustment(priceInfo.adjustment);
      console.log('New price calculation: finalPrice=' + priceInfo.finalPrice + ', adjustment=' + priceInfo.adjustment);
      
      // If we have variants, check if we need to update the product image
      if (product.variants && product.variants.length > 0) {
        const matchingVariant = product.variants.find(variant => {
          if (!variant.option_combination) return false;
          
          const variantOptions = typeof variant.option_combination === 'string' 
            ? JSON.parse(variant.option_combination as string) 
            : variant.option_combination;
            
          // Check if all selected options match this variant
          const allOptionsMatch = Object.entries(newOptions).every(([key, value]) => {
            const normalizedKey = key.toLowerCase();
            const variantOptionKey = Object.keys(variantOptions).find(
              k => k.toLowerCase() === normalizedKey
            );
            
            if (!variantOptionKey) return false;
            return variantOptions[variantOptionKey] === value;
          });
          
          return allOptionsMatch;
        });
        
        // If we found a matching variant with an image, update the product image
        if (matchingVariant && matchingVariant.image) {
          console.log('Found matching variant with image:', matchingVariant.image);
          // We could update the product image here if needed
        }
      }
    }
  };

  // Handle quantity change
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setQuantity(value);
    }
  };

  // Add to cart function
  const handleAddToCart = () => {
    if (!product) return;
    
    // Make sure selectedOptions is a plain object that can be serialized properly
    const serializedOptions = {};
    Object.entries(selectedOptions).forEach(([key, value]) => {
      // Ensure keys and values are strings
      serializedOptions[String(key)] = String(value);
    });
    
    // Calculate the final price to ensure it's correct
    // This recalculates to make sure we have the latest price
    const priceInfo = calculatePrice(product, selectedOptions);
    
    // Prepare selected options and price information
    const optionsInfo = {
      selectedOptions: serializedOptions,
      priceAdjustment: priceInfo.adjustment,
      calculatedPrice: priceInfo.finalPrice
    };
    
    // Check for Pack Size options which might have special pricing
    const packSizeOption = Object.entries(selectedOptions).find(([key]) => 
      key.toLowerCase().includes('pack') || key.toLowerCase() === 'size'
    );
    
    if (packSizeOption) {
      const [optionName, optionValue] = packSizeOption;
      console.log(`Adding to cart with Pack Size option: ${optionName}=${optionValue}`);
      console.log(`Using calculated price: ${priceInfo.finalPrice}`);
      
      // Ensure the calculated price is used for the cart
      optionsInfo.calculatedPrice = priceInfo.finalPrice;
    }
    
    console.log('Adding to cart with options:', optionsInfo);
    console.log(`Final price: ${priceInfo.finalPrice} (base: ${product.price}, adjustment: ${priceInfo.adjustment})`);
    
    try {
      // Add to cart using context - pass product ID, quantity, and options
      // Create a proper variant object with all the necessary information
      const variantWithOptions = {
        id: `${product.id}-variant-${Date.now()}`,
        sku: product.sku || '',
        options: {
          ...serializedOptions,
          // Include these properties for debugging, they'll be filtered out in display
          calculatedPrice: priceInfo.finalPrice,
          priceAdjustment: priceInfo.adjustment
        },
        price_adjustment: priceInfo.adjustment,
        calculated_price: priceInfo.finalPrice
      };
      
      // Pass the product, quantity, and the properly structured variant
      // Use type casting to avoid TypeScript errors with the product and variant types
      addToCart(product as any, quantity, variantWithOptions as any);
    } catch (error) {
      console.error('Error adding to cart:', error);
      
      // Show error toast
      toast({
        title: 'Error',
        description: 'Could not add item to cart. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle sharing
  const handleShare = (platform: string) => {
    if (!product) return;
    
    const url = window.location.href;
    const text = `Check out ${product.name} on Bits N Bongs!`;
    
    // Show tooltip
    setShowShareTooltip(true);
    setTimeout(() => setShowShareTooltip(false), 2000);
    
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        break;
    }
  };
  
  // Fetch product on component mount or when slug changes
  useEffect(() => {
    fetchProduct();
    // Scroll to the top of the page when the component mounts or slug changes
    window.scrollTo(0, 0);
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center">
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="text-gray-600 mb-6">Sorry, we couldn\'t find the product you\'re looking for.</p>
        <Link to="/shop" className="text-primary hover:underline flex items-center gap-2">
          <ArrowLeft size={16} />
          Back to Shop
        </Link>
      </div>
    );
  }

  // Generate cannabis leaf elements for the background
  const leafElements = [];
  for (let i = 0; i < 25; i++) { // Increased from 12 to 25 leaves
    // Generate random positions, sizes, and animation delays
    const size = Math.floor(Math.random() * 80) + 60; // Random size between 60-140px (increased)
    const left = Math.floor(Math.random() * 100); // Random left position
    const top = Math.floor(Math.random() * 100); // Random top position
    const delay = Math.floor(Math.random() * 10); // Random animation delay
    const rotateDelay = Math.floor(Math.random() * 5); // Different delay for rotation
    const opacity = (Math.random() * 0.3) + 0.15; // Random opacity between 0.15-0.45
    
    leafElements.push(
      <div 
        key={`leaf-${i}`}
        className="absolute z-0"
        style={{
          left: `${left}%`,
          top: `${top}%`,
          opacity: opacity,
          transform: `rotate(${Math.random() * 360}deg)`,
          filter: 'drop-shadow(0 0 2px rgba(108, 132, 80, 0.3))',
        }}
      >
<img 
  src="/images/Cannabis_leaf.svg" 
  alt="" 
  className="animate-float-leaf" 
  style={{
    width: `${size}px`,
    height: `${size}px`,
    animationDelay: `${delay}s`,
    animationDuration: `${15 + delay}s`,
  }}
/>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 relative overflow-hidden">
      {/* Background blur elements */}
      <div className="absolute -top-20 -right-20 w-64 h-64 bg-primary/10 rounded-full filter blur-3xl opacity-70 z-0"></div>
      <div className="absolute top-40 -left-20 w-72 h-72 bg-blue-200/20 rounded-full filter blur-3xl opacity-60 z-0"></div>
      <div className="absolute bottom-40 right-20 w-80 h-80 bg-amber-200/10 rounded-full filter blur-3xl opacity-50 z-0"></div>
      
      {/* Cannabis leaf background elements */}
      {leafElements}
      
      <div className="relative z-10">
        {/* Navigation and sharing row */}
        <div className="mb-6 flex justify-between items-center">
          <button 
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-gray-600 hover:text-primary transition-colors"
          >
            <ArrowLeft size={16} />
            Back to previous page
          </button>
          
          {/* Social sharing buttons - small and subtle */}
          <div className="flex items-center gap-2 relative">
            <button
              onClick={() => handleShare('facebook')}
              className="text-facebook hover:bg-facebook/10 p-2 rounded-full transition-colors"
              title="Share on Facebook"
            >
              <Facebook size={16} />
            </button>
            <button
              onClick={() => handleShare('twitter')}
              className="text-twitter hover:bg-twitter/10 p-2 rounded-full transition-colors"
              title="Share on Twitter"
            >
              <Twitter size={16} />
            </button>
            <button
              onClick={() => handleShare('linkedin')}
              className="text-linkedin hover:bg-linkedin/10 p-2 rounded-full transition-colors"
              title="Share on LinkedIn"
            >
              <Linkedin size={16} />
            </button>
            <button
              onClick={() => handleShare('copy')}
              className="text-gray-500 hover:bg-gray-100 p-2 rounded-full transition-colors"
              title="Copy Link"
            >
              {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
            </button>
            {showShareTooltip && (
              <div className="bg-black text-white text-xs px-2 py-1 rounded absolute right-0 -bottom-8">
                {copied ? 'Link copied!' : 'Share this product'}
              </div>
            )}
          </div>
        </div>
        
        {/* Main product grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left column - Product image */}
          <div>
            <ProductImageGallery
              mainImage={product.image}
              additionalImages={product.additional_images || []}
              productName={product.name}
            />
            
            {/* Product Specifications - Now under the image */}
            <div className="backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md mt-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-800">
                <Info size={18} className="text-primary/80" />
                Product Specifications
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.weight && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Weight:</span>
                    <span className="text-gray-800">{product.weight}</span>
                  </div>
                )}
                {product.size && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Size:</span>
                    <span className="text-gray-800">{product.size}</span>
                  </div>
                )}
                {product.dimensions && (
                  <div className="flex p-2 bg-white/30 rounded-lg">
                    <span className="font-medium w-32 text-gray-700">Dimensions:</span>
                    <span className="text-gray-800">{product.dimensions}</span>
                  </div>
                )}
                {(!product.weight && !product.size && !product.dimensions) && (
                  <div className="text-gray-500 italic p-2 bg-white/30 rounded-lg">
                    No specifications available for this product.
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Right column - Product details */}
          <div>
            {/* Product Title */}
            <h1 className="text-2xl md:text-3xl font-bold mb-2">{product.name}</h1>
            
            {/* Brand Information - if available */}
            {product.brands && (
              <div className="flex items-center mb-4 text-gray-600">
                <Link 
                  to={`/shop?brand=${product.brands.slug || product.brands.id}`}
                  className="text-sm font-medium flex items-center gap-1.5 hover:text-primary transition-colors bg-gray-100 px-2 py-1 rounded-md"
                >
                  <span className="w-2 h-2 rounded-full bg-primary/70"></span>
                  By {product.brands.name || 'Unknown Brand'}
                </Link>
              </div>
            )}

            {/* Rating Stars */}
            <div className="flex items-center mb-4">
              <div className="flex text-amber-400">
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <Star className="h-4 w-4 fill-current" />
                <StarHalf className="h-4 w-4 fill-current" />
              </div>
              <span className="text-sm text-gray-500 ml-1">(4.5)</span>
            </div>

            {/* Price Display */}
            <div className="mb-6 backdrop-blur-sm bg-white/30 p-5 rounded-xl shadow-lg border border-white/40">
              {product.sale_price ? (
                <div className="flex flex-col gap-2">
                  <div className="flex flex-wrap items-center gap-3">
                    <span className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                      £{calculatedPrice?.toFixed(2)}
                    </span>
                    <span className="text-lg line-through text-gray-500/80">
                      £{(product.price + priceAdjustment).toFixed(2)}
                    </span>
                    {priceAdjustment !== 0 && (
                      <span className="text-sm bg-white/40 text-primary px-3 py-1 rounded-full ml-auto shadow-sm border border-white/50">
                        {priceAdjustment > 0 ? '+' : '-'}£{Math.abs(priceAdjustment).toFixed(2)}
                      </span>
                    )}
                  </div>
                  
                  {/* Selected Options Display */}
                  {Object.keys(selectedOptions).length > 0 && (
                    <div className="flex flex-wrap gap-2 items-center">
                      <span className="text-sm text-gray-600">Selected:</span>
                      {Object.entries(selectedOptions).map(([option, value]) => {
                        const isColorOption = option.toLowerCase().includes('color') || option.toLowerCase().includes('colour');
                        return (
                          <div 
                            key={`${option}-${value}`}
                            style={isColorOption ? {
                              background: getColorStyle(value),
                              border: '1px solid rgba(0,0,0,0.1)',
                              color: value.toLowerCase() === 'white' ? '#333' : 'white',
                              textShadow: '0px 1px 2px rgba(0,0,0,0.5)'
                            } : undefined}
                            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${isColorOption ? 'shadow-sm' : 'bg-gray-100'}`}
                          >
                            {isColorOption && (
                              <span 
                                className="w-3 h-3 rounded-full inline-block" 
                                style={{
                                  background: getColorStyle(value),
                                  border: '1px solid rgba(0,0,0,0.1)'
                                }}
                              />
                            )}
                            <span>{value}</span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col gap-2">
                  <div className="flex flex-wrap items-center gap-3">
                    <span className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                      £{calculatedPrice?.toFixed(2)}
                    </span>
                    {priceAdjustment !== 0 && (
                      <span className="text-sm bg-white/40 text-primary px-3 py-1 rounded-full ml-auto shadow-sm border border-white/50">
                        {priceAdjustment > 0 ? '+' : '-'}£{Math.abs(priceAdjustment).toFixed(2)}
                      </span>
                    )}
                  </div>
                  
                  {/* Selected Options Display */}
                  {Object.keys(selectedOptions).length > 0 && (
                    <div className="flex flex-wrap gap-2 items-center">
                      <span className="text-sm text-gray-600">Selected:</span>
                      {Object.entries(selectedOptions).map(([option, value]) => {
                        const isColorOption = option.toLowerCase().includes('color') || option.toLowerCase().includes('colour');
                        return (
                          <div 
                            key={`${option}-${value}`}
                            style={isColorOption ? {
                              background: getColorStyle(value),
                              border: '1px solid rgba(0,0,0,0.1)',
                              color: value.toLowerCase() === 'white' ? '#333' : 'white',
                              textShadow: '0px 1px 2px rgba(0,0,0,0.5)'
                            } : undefined}
                            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${isColorOption ? 'shadow-sm' : 'bg-gray-100'}`}
                          >
                            {isColorOption && (
                              <span 
                                className="w-3 h-3 rounded-full inline-block" 
                                style={{
                                  background: getColorStyle(value),
                                  border: '1px solid rgba(0,0,0,0.1)'
                                }}
                              />
                            )}
                            <span>{value}</span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Brief Description */}
            <div className="mb-6 backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md">
              <h4 className="font-medium mb-3 flex items-center gap-2 text-gray-800">
                <span className="inline-block w-2 h-2 rounded-full bg-primary mr-1"></span>
                Description
              </h4>
              <div className="p-3 bg-white/30 rounded-lg">
                <div 
                  className="text-gray-700 product-description" 
                  dangerouslySetInnerHTML={{ __html: product.description || '' }}
                />
              </div>
            </div>

            {/* Product Options with Visual Cards */}
            {product.options && Object.keys(product.options).length > 0 && (
              <div className="space-y-7 mb-8">
                {Object.entries(product.options).map(([optionName, values]) => {
                  // Normalize option name for display
                  const displayName = optionName.charAt(0).toUpperCase() + optionName.slice(1);
                  const normalizedName = optionName.toLowerCase();
                  
                  // Determine option type based on name
                  const isColorOption = normalizedName.includes('color') || normalizedName.includes('colour');
                  const isPackSizeOption = normalizedName.includes('pack') || normalizedName === 'size';
                  
                  let optionIcon = <span className="inline-block w-2 h-2 rounded-full bg-primary mr-2"></span>;
                  if (isColorOption) {
                    optionIcon = <Palette className="h-5 w-5 mr-2 text-primary" />;
                  } else if (isPackSizeOption) {
                    optionIcon = <Package className="h-5 w-5 mr-2 text-primary" />;
                  }
                  
                  return (
                    <div key={optionName} className="backdrop-blur-sm bg-white/20 p-4 rounded-xl border border-white/30 shadow-md">
                      <h3 className="text-lg font-medium mb-4 text-gray-800 flex items-center">
                        {optionIcon}
                        {displayName}
                      </h3>
                      
                      {isColorOption ? (
                        // Render color swatches for color options
                        <div className="flex flex-wrap gap-3">
                          {values.filter(value => value !== 'DROP_DOWN').map((color) => {
                            // Check if this color is selected
                            const isSelected = Object.entries(selectedOptions)
                              .some(([key, val]) => key.toLowerCase() === normalizedName && val === color);
                            
                            return (
                              <ColorSwatch
                                key={color}
                                colorName={color}
                                selected={isSelected}
                                onClick={() => {
                                  console.log(`Color selected: ${optionName} = ${color}`);
                                  handleOptionChange(optionName, color);
                                }}
                              />
                            );
                          })}
                        </div>
                      ) : isPackSizeOption ? (
                        // Render special pack size options with better styling
                        <div className="flex flex-wrap gap-3">
                          {values.filter(value => value !== 'DROP_DOWN').map((value) => {
                            // Get price adjustment for this option
                            let adjustment = null;
                            if (product.price_adjustments) {
                              // Try to find the adjustment using normalized keys
                              const adjustments = Object.entries(product.price_adjustments)
                                .find(([key]) => key.toLowerCase() === normalizedName)?.[1];
                                
                              if (adjustments && adjustments[value] !== undefined) {
                                adjustment = Number(adjustments[value]);
                              }
                            }
                            
                            // Check if this option is selected
                            const isSelected = Object.entries(selectedOptions)
                              .some(([key, val]) => key.toLowerCase() === normalizedName && val === value);
                            
                            return (
                              <button
                                key={value}
                                onClick={() => {
                                  console.log(`Pack size selected: ${optionName} = ${value}`);
                                  handleOptionChange(optionName, value);
                                }}
                                className={`flex-1 min-w-[120px] py-3 px-4 rounded-lg transition-all duration-200 ${isSelected 
                                  ? 'bg-sage-500 text-white shadow-md transform scale-105' 
                                  : 'bg-white/50 hover:bg-white/70 text-gray-700 border border-gray-200'}`}
                              >
                                <div className="font-medium text-center">{value}</div>
                                {adjustment !== null && adjustment !== 0 && (
                                  <div className="text-xs text-center mt-1 font-medium">
                                    {adjustment > 0 ? '+' : ''}{adjustment.toFixed(2)}
                                  </div>
                                )}
                              </button>
                            );
                          })}
                        </div>
                      ) : (
                        // Render standard option cards for other options
                        <div className="grid grid-cols-2 gap-3">
                          {values.filter(value => value !== 'DROP_DOWN').map((value) => {
                            // Skip if the value is 'DROP_DOWN'
                            if (value === 'DROP_DOWN') {
                              console.log(`Skipping value 'DROP_DOWN' for option ${optionName}`);
                              return null;
                            }
                            
                            // Get price adjustment for this option
                            let adjustment = null;
                            if (product.price_adjustments) {
                              // Try to find the adjustment using normalized keys
                              const adjustments = Object.entries(product.price_adjustments)
                                .find(([key]) => key.toLowerCase() === normalizedName)?.[1];
                                
                              if (adjustments && adjustments[value] !== undefined) {
                                adjustment = Number(adjustments[value]);
                              }
                            }
                            
                            // Check if this option is selected
                            const isSelected = Object.entries(selectedOptions)
                              .some(([key, val]) => key.toLowerCase() === normalizedName && val === value);
                            
                            return (
                              <VisualOptionCard
                                key={value}
                                optionName={optionName}
                                value={value}
                                isSelected={isSelected}
                                priceAdjustment={adjustment}
                                onClick={() => {
                                  console.log(`Option selected: ${optionName} = ${value}`);
                                  handleOptionChange(optionName, value);
                                }}
                              />
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Quantity and Add to Cart Section */}
            <div className="backdrop-blur-sm bg-white/30 p-5 rounded-xl mb-6 shadow-lg border border-white/40">
              <div className="flex items-center justify-between mb-5">
                <label className="font-medium text-gray-700">
                  Quantity
                </label>
                <div className="flex items-center rounded-lg overflow-hidden shadow-sm">
                  <button
                    className="px-3 py-2 bg-white/50 hover:bg-white/70 transition-colors text-gray-700"
                    onClick={() => quantity > 1 && setQuantity(quantity - 1)}
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={handleQuantityChange}
                    className="w-14 text-center py-2 bg-white/80 font-medium"
                  />
                  <button
                    className="px-3 py-2 bg-white/50 hover:bg-white/70 transition-colors text-gray-700"
                    onClick={() => setQuantity(quantity + 1)}
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Add to Cart Button */}
              <div className="flex justify-center">
                <button
                  className="bg-sage-500 text-white py-2 px-4 rounded-md flex items-center justify-center gap-1.5 hover:bg-sage-600 transition-all duration-200 shadow-sm relative overflow-hidden group w-full max-w-[180px]"
                  onClick={handleAddToCart}
                >
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:animate-shimmer" />
                  <ShoppingBag size={14} className="group-hover:scale-110 transition-transform" />
                  <span className="font-medium text-xs uppercase tracking-wider">Add to Cart</span>
                </button>
              </div>
            </div>
            
            {/* Additional Information Section */}
            {product.additional_info && (
              <div className="backdrop-blur-sm bg-white/20 p-5 rounded-xl border border-white/30 shadow-md mb-6">
                <h4 className="font-medium mb-3 flex items-center gap-2 text-gray-800">
                  <span className="inline-block w-2 h-2 rounded-full bg-primary mr-1"></span>
                  Additional Information
                </h4>
                <div className="p-3 bg-white/30 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-line">{product.additional_info}</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Related Products Section */}
        <div className="mt-12">
          <RelatedProducts 
            productId={product.id} 
            categoryId={product.category_id} 
            limit={4} 
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
