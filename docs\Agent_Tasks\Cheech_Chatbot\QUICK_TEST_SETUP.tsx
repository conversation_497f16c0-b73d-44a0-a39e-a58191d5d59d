/**
 * Quick Test Setup for Cheech Chatbot
 * 
 * Add this to your main app to test Ch<PERSON>ch with all the audio clips!
 */

import React, { useEffect, useState } from 'react';
import { AIServiceManager } from './services/ai/AIServiceManager';

// Quick test component to get Chee<PERSON> running
const CheechQuickTest: React.FC = () => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    initializeTest();
  }, []);

  const initializeTest = async () => {
    try {
      // Initialize AI service
      const aiManager = new AIServiceManager();
      await aiManager.initialize({
        deepseek_key: import.meta.env.VITE_DEEPSEEK_API_KEY,
        gemini_key: import.meta.env.VITE_GEMINI_API_KEY,
        openrouter_key: import.meta.env.VITE_OPENROUTER_API_KEY
      });

      setIsReady(true);
      console.log('🎵 Cheech is ready to rock!');
    } catch (err) {
      setError('Failed to initialize Cheech');
      console.error(err);
    }
  };

  // Test audio function
  const testAudio = (audioFile: string) => {
    try {
      const audio = new Audio(audioFile);
      audio.volume = 0.7;
      audio.play().catch(console.log);
    } catch (error) {
      console.error('Audio test failed:', error);
    }
  };

  if (error) {
    return <div style={{ padding: '20px', color: 'red' }}>Error: {error}</div>;
  }

  if (!isReady) {
    return <div style={{ padding: '20px' }}>🎵 Initializing Cheech...</div>;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: '20px', 
      right: '20px', 
      background: 'white', 
      padding: '20px', 
      borderRadius: '10px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h3>🎵 Cheech Audio Test</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <h4>Welcome Messages:</h4>
        <button onClick={() => testAudio('/audio/hey-welcom-to-bits-n-bongs-how-can-i-help-you-man.mp3')}>
          🔊 Welcome 1
        </button>
        <button onClick={() => testAudio('/audio/hey-dude-whats-up-need-a-hand-with-anything-man.mp3')}>
          🔊 Welcome 2
        </button>
        <button onClick={() => testAudio('/audio/hey-man-how-can-i-help-you.mp3')}>
          🔊 Welcome 3
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h4>Thinking Responses:</h4>
        <button onClick={() => testAudio('/audio/no-worries-dude-ill-get-right-on-that.mp3')}>
          🔊 Quick Response
        </button>
        <button onClick={() => testAudio('/audio/no-worries-dude-ill-get-right-on-that-man-but-first-let-me-grab-a-little-snack-cause-you-know-cant-work-on-an-empty-stomach-righ.mp3')}>
          🔊 Snack Response 😂
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h4>Other Responses:</h4>
        <button onClick={() => testAudio('/audio/hey-man-this-is-what-youre-looking-fo.mp3')}>
          🔊 Found It!
        </button>
        <button onClick={() => testAudio('/audio/sorry-man-let-me-try-that-again.mp3')}>
          🔊 Error
        </button>
      </div>

      <div>
        <h4>Goodbye Messages:</h4>
        <button onClick={() => testAudio('/audio/catch-you-later-man-happy-shopping.mp3')}>
          🔊 Goodbye 1
        </button>
        <button onClick={() => testAudio('/audio/catch-you-later-man-happy-shopping-and-dont-forget-to-snag-some-munchies-for-the-road-ese.mp3')}>
          🔊 Munchies Goodbye 🍿
        </button>
      </div>

      <style>{`
        button {
          display: block;
          width: 100%;
          margin: 5px 0;
          padding: 8px;
          background: #4CAF50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }
        button:hover {
          background: #45a049;
        }
        h4 {
          margin: 10px 0 5px 0;
          font-size: 14px;
        }
      `}</style>
    </div>
  );
};

export default CheechQuickTest;

/**
 * USAGE:
 * 
 * 1. Add to your main App.tsx:
 * 
 * import CheechQuickTest from './components/CheechQuickTest';
 * 
 * function App() {
 *   return (
 *     <div className="App">
 *       {/* Your existing content *\/}
 *       
 *       {/* Add this for testing *\/}
 *       <CheechQuickTest />
 *     </div>
 *   );
 * }
 * 
 * 2. Start your dev server:
 *    npm run dev
 * 
 * 3. Look for the audio test panel in the top-right corner
 * 
 * 4. Click the buttons to test each audio clip!
 */
