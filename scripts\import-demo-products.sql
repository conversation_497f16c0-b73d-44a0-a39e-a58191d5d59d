-- SQL script to import demo products
-- Run this in the Supabase SQL Editor

-- First, clear existing products if needed
-- DELETE FROM product_variants;
-- DELETE FROM products;

-- Import products from the demo_products.csv file
-- Note: You'll need to upload the CSV file to Supabase storage first
-- Then use the COPY command to import it

-- Example:
-- COPY products(id, name, slug, description, price, image, is_active, in_stock, sku, stock_quantity)
-- FROM 'demo_products.csv'
-- WITH (FORMAT csv, HEADER true);

-- Alternatively, you can manually insert a few products:

INSERT INTO products (
  id, 
  name, 
  slug, 
  description, 
  price, 
  image, 
  is_active, 
  in_stock, 
  sku, 
  stock_quantity,
  created_at,
  updated_at,
  is_featured,
  is_new,
  is_best_seller,
  rating,
  review_count
) VALUES 
(
  '11111111-1111-1111-1111-111111111111', 
  'Auto Gorilla Glue', 
  'auto-gorilla-glue', 
  'Auto Gorilla Glue is a potent autoflowering strain known for its high THC content and sticky resinous buds.', 
  29.99, 
  '', -- Leave blank for manual upload
  true, 
  true, 
  'AGG-001', 
  100,
  NOW(),
  NOW(),
  true,
  true,
  true,
  4.5,
  12
),
(
  '22222222-2222-2222-2222-222222222222', 
  'Barneys Farm Blueberry Cheese', 
  'barneys-farm-blueberry-cheese', 
  'A delicious blend of Blueberry and Cheese genetics, offering a sweet berry aroma with hints of cheese.', 
  34.99, 
  '', -- Leave blank for manual upload
  true, 
  true, 
  'BFBC-001', 
  100,
  NOW(),
  NOW(),
  false,
  true,
  false,
  4.2,
  8
),
(
  '33333333-3333-3333-3333-333333333333', 
  'Tropicanna Banana Feminised', 
  'tropicanna-banana-feminised', 
  'A tropical delight with sweet banana and citrus flavors, perfect for relaxation.', 
  39.99, 
  '', -- Leave blank for manual upload
  true, 
  true, 
  'TBF-001', 
  100,
  NOW(),
  NOW(),
  true,
  false,
  true,
  4.7,
  15
),
(
  '44444444-4444-4444-4444-444444444444', 
  'Watermelon Zkittlez Feminised', 
  'watermelon-zkittlez-feminised', 
  'A fruity and colorful strain with sweet watermelon and candy flavors.', 
  42.99, 
  '', -- Leave blank for manual upload
  true, 
  true, 
  'WZF-001', 
  100,
  NOW(),
  NOW(),
  false,
  false,
  true,
  4.8,
  20
),
(
  '55555555-5555-5555-5555-555555555555', 
  'Tightvac Airtight Storage', 
  'tightvac-airtight-storage', 
  'Premium airtight storage container for keeping your herbs fresh and odor-free.', 
  19.99, 
  '', -- Leave blank for manual upload
  true, 
  true, 
  'TAS-001', 
  100,
  NOW(),
  NOW(),
  true,
  true,
  false,
  4.3,
  18
);

-- Now add some variants for these products

-- Auto Gorilla Glue variants
INSERT INTO product_variants (
  product_id,
  variant_name,
  sku,
  price,
  stock_quantity,
  in_stock,
  option_combination,
  created_at,
  updated_at,
  is_active
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  '3 Pack',
  'AGG-001-3PK',
  29.99,
  100,
  true,
  '{"Pack Size": "3 Pack"}',
  NOW(),
  NOW(),
  true
),
(
  '11111111-1111-1111-1111-111111111111',
  '5 Pack',
  'AGG-001-5PK',
  44.99,
  100,
  true,
  '{"Pack Size": "5 Pack"}',
  NOW(),
  NOW(),
  true
),
(
  '11111111-1111-1111-1111-111111111111',
  '10 Pack',
  'AGG-001-10PK',
  79.99,
  100,
  true,
  '{"Pack Size": "10 Pack"}',
  NOW(),
  NOW(),
  true
);

-- Barneys Farm Blueberry Cheese variants
INSERT INTO product_variants (
  product_id,
  variant_name,
  sku,
  price,
  stock_quantity,
  in_stock,
  option_combination,
  created_at,
  updated_at,
  is_active
) VALUES 
(
  '22222222-2222-2222-2222-222222222222',
  '3 Pack',
  'BFBC-001-3PK',
  34.99,
  100,
  true,
  '{"Pack Size": "3 Pack"}',
  NOW(),
  NOW(),
  true
),
(
  '22222222-2222-2222-2222-222222222222',
  '5 Pack',
  'BFBC-001-5PK',
  54.99,
  100,
  true,
  '{"Pack Size": "5 Pack"}',
  NOW(),
  NOW(),
  true
),
(
  '22222222-2222-2222-2222-222222222222',
  '10 Pack',
  'BFBC-001-10PK',
  94.99,
  100,
  true,
  '{"Pack Size": "10 Pack"}',
  NOW(),
  NOW(),
  true
);

-- Add option_definitions to the products
UPDATE products
SET option_definitions = '{
  "Pack Size": {
    "name": "Pack Size",
    "display_type": "dropdown",
    "values": ["3 Pack", "5 Pack", "10 Pack"]
  }
}'
WHERE id IN (
  '11111111-1111-1111-1111-111111111111',
  '22222222-2222-2222-2222-222222222222',
  '33333333-3333-3333-3333-333333333333',
  '44444444-4444-4444-4444-444444444444'
);

-- Add size options for the storage container
UPDATE products
SET option_definitions = '{
  "Size": {
    "name": "Size",
    "display_type": "dropdown",
    "values": ["0.12ltr", "0.29ltr", "0.57ltr", "1.3ltr", "2.35ltr"]
  }
}'
WHERE id = '55555555-5555-5555-5555-555555555555';

-- Add variants for the storage container
INSERT INTO product_variants (
  product_id,
  variant_name,
  sku,
  price,
  stock_quantity,
  in_stock,
  option_combination,
  created_at,
  updated_at,
  is_active
) VALUES 
(
  '55555555-5555-5555-5555-555555555555',
  '0.12ltr',
  'TAS-001-012',
  19.99,
  100,
  true,
  '{"Size": "0.12ltr"}',
  NOW(),
  NOW(),
  true
),
(
  '55555555-5555-5555-5555-555555555555',
  '0.29ltr',
  'TAS-001-029',
  24.99,
  100,
  true,
  '{"Size": "0.29ltr"}',
  NOW(),
  NOW(),
  true
),
(
  '55555555-5555-5555-5555-555555555555',
  '0.57ltr',
  'TAS-001-057',
  29.99,
  100,
  true,
  '{"Size": "0.57ltr"}',
  NOW(),
  NOW(),
  true
),
(
  '55555555-5555-5555-5555-555555555555',
  '1.3ltr',
  'TAS-001-13',
  34.99,
  100,
  true,
  '{"Size": "1.3ltr"}',
  NOW(),
  NOW(),
  true
),
(
  '55555555-5555-5555-5555-555555555555',
  '2.35ltr',
  'TAS-001-235',
  39.99,
  100,
  true,
  '{"Size": "2.35ltr"}',
  NOW(),
  NOW(),
  true
);
