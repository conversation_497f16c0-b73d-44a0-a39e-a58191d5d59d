/**
 * Type definitions for the Cheech Chatbot system
 */

// Message types
export type MessageRole = 'user' | 'assistant' | 'system';

export interface Message {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: Date;
}

// Product related types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
  subcategory?: string;
  tags: string[];
  rating?: number;
  reviewCount?: number;
  inStock: boolean;
}

export interface ProductCategory {
  id: string;
  name: string;
  description: string;
  subcategories: string[];
}

// RAG related types
export interface Document {
  id: string;
  content: string;
  metadata: {
    source: string;
    category?: string;
    productId?: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface QueryResult {
  query: string;
  documents: Document[];
  relevanceScores: number[];
}

// Chat session types
export interface ChatSession {
  id: string;
  userId?: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  context: {
    preferredCategories?: string[];
    viewedProducts?: string[];
    currentPage?: string;
    userPreferences?: Record<string, any>;
  };
}

// Response types
export interface ChatResponse {
  message: string;
  suggestions?: string[];
  products?: Product[];
  actions?: ChatAction[];
}

export interface ChatAction {
  type: 'navigate' | 'showProduct' | 'addToCart' | 'showCategory';
  payload: any;
}

// Knowledge base types
export interface KnowledgeEntry {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  relatedProductIds?: string[];
}

// Analytics types
export interface ChatInteraction {
  sessionId: string;
  messageId: string;
  query: string;
  response: string;
  timestamp: Date;
  helpful?: boolean;
  responseTime: number;
  suggestionsClicked?: string[];
  productsViewed?: string[];
}

// Web navigation types
export interface NavigationResult {
  url: string;
  title: string;
  instructions: string;
  screenshot?: string;
  elements?: {
    selector: string;
    description: string;
  }[];
}

// Context management types
export interface MemoryItem {
  key: string;
  value: any;
  timestamp: Date;
  importance: number;
}

export interface ConversationContext {
  recentMessages: Message[];
  userPreferences: Record<string, any>;
  memoryItems: MemoryItem[];
}

// Service interfaces
export interface RAGService {
  query(text: string, filters?: Record<string, any>): Promise<QueryResult>;
  addDocuments(documents: Document[]): Promise<void>;
  deleteDocuments(ids: string[]): Promise<void>;
}

export interface ContextService {
  getContext(sessionId: string): Promise<ConversationContext>;
  updateContext(sessionId: string, context: Partial<ConversationContext>): Promise<void>;
  addMemoryItem(sessionId: string, item: Omit<MemoryItem, 'timestamp'>): Promise<void>;
}

export interface KnowledgeService {
  search(query: string): Promise<KnowledgeEntry[]>;
  getByCategory(category: string): Promise<KnowledgeEntry[]>;
  getByTags(tags: string[]): Promise<KnowledgeEntry[]>;
}

export interface ProductService {
  search(query: string): Promise<Product[]>;
  getById(id: string): Promise<Product | null>;
  getByCategory(category: string): Promise<Product[]>;
  getRecommendations(productId: string): Promise<Product[]>;
  getRecommendationsForQuery(query: string): Promise<Product[]>;
}

export interface NavigationService {
  navigateToPage(url: string): Promise<NavigationResult>;
  findPageForQuery(query: string): Promise<NavigationResult>;
  getProductPage(productId: string): Promise<NavigationResult>;
  getCategoryPage(categoryId: string): Promise<NavigationResult>;
}

export interface AnalyticsService {
  trackInteraction(interaction: Omit<ChatInteraction, 'timestamp'>): Promise<void>;
  getPopularQueries(): Promise<{query: string; count: number}[]>;
  getAverageResponseTime(): Promise<number>;
  getSessionMetrics(sessionId: string): Promise<any>;
}
