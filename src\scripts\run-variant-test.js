// This script runs the test-variant-components.js script
const { spawn } = require('child_process');
const path = require('path');

console.log('Running test-variant-components.js script...');

// Get the path to the script
const scriptPath = path.join(__dirname, 'test-variant-components.js');

// Run the script using node
const child = spawn('node', [scriptPath], {
  stdio: 'inherit',
  shell: true
});

child.on('close', (code) => {
  console.log(`<PERSON><PERSON><PERSON> exited with code ${code}`);
});
