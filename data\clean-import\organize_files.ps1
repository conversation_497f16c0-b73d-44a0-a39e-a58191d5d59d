# Create an archive folder for files we want to keep but not in the main directory
New-Item -ItemType Directory -Force -Path "./archive"

# Files to keep in the main directory
$filesToKeep = @(
    "transformed_products.csv",
    "transformed_variants_final.csv",
    "fix_variant_prices.js",
    "transform_products.js",
    "README.md",
    "product_template.csv",
    "variants_template.csv",
    "import_products.sql",
    "organize_files.ps1"
)

# Move all other files to the archive folder
Get-ChildItem -Path "." -File | Where-Object { $filesToKeep -notcontains $_.Name } | ForEach-Object {
    Write-Host "Moving $($_.Name) to archive folder"
    Move-Item -Path $_.FullName -Destination "./archive/$($_.Name)"
}

# List the files in the main directory
Write-Host "`nFiles kept in the main directory:"
Get-ChildItem -Path "." -File | Select-Object Name

# List the files in the archive folder
Write-Host "`nFiles moved to the archive folder:"
Get-ChildItem -Path "./archive" -File | Select-Object Name
