-- Migration: 20250127000002_add_tenant_columns.sql
-- Description: Adds tenant_id columns to all relevant tables and creates foreign key constraints

-- Add tenant_id to products table
ALTER TABLE public.products 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to categories table
ALTER TABLE public.categories 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to customers table
ALTER TABLE public.customers 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to orders table
ALTER TABLE public.orders 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to inventory table
ALTER TABLE public.inventory 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to suppliers table
ALTER TABLE public.suppliers 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to promotions table
ALTER TABLE public.promotions 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to settings table
ALTER TABLE public.settings 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to blog_posts table
ALTER TABLE public.blog_posts 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Add tenant_id to user_profiles table
ALTER TABLE public.user_profiles 
ADD COLUMN tenant_id UUID REFERENCES tenant_management.tenants(id);

-- Create indexes for all tenant_id columns to improve query performance
CREATE INDEX idx_products_tenant_id ON public.products(tenant_id);
CREATE INDEX idx_categories_tenant_id ON public.categories(tenant_id);
CREATE INDEX idx_customers_tenant_id ON public.customers(tenant_id);
CREATE INDEX idx_orders_tenant_id ON public.orders(tenant_id);
CREATE INDEX idx_inventory_tenant_id ON public.inventory(tenant_id);
CREATE INDEX idx_suppliers_tenant_id ON public.suppliers(tenant_id);
CREATE INDEX idx_promotions_tenant_id ON public.promotions(tenant_id);
CREATE INDEX idx_settings_tenant_id ON public.settings(tenant_id);
CREATE INDEX idx_blog_posts_tenant_id ON public.blog_posts(tenant_id);
CREATE INDEX idx_user_profiles_tenant_id ON public.user_profiles(tenant_id);

-- Create function to automatically set tenant_id on insert
CREATE OR REPLACE FUNCTION tenant_management.set_tenant_id()
RETURNS TRIGGER AS $$
DECLARE
    current_tenant_id UUID;
BEGIN
    -- Get current tenant ID
    current_tenant_id := tenant_management.get_current_tenant_id();
    
    -- Set tenant_id if not already set
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := current_tenant_id;
    END IF;
    
    -- Validate tenant access if tenant_id is being explicitly set
    IF NEW.tenant_id IS NOT NULL AND NEW.tenant_id != current_tenant_id THEN
        IF NOT tenant_management.validate_tenant_access(NEW.tenant_id) THEN
            RAISE EXCEPTION 'Access denied to tenant %', NEW.tenant_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to automatically set tenant_id on insert for all relevant tables
CREATE TRIGGER set_tenant_id_products
BEFORE INSERT ON public.products
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_categories
BEFORE INSERT ON public.categories
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_customers
BEFORE INSERT ON public.customers
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_orders
BEFORE INSERT ON public.orders
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_inventory
BEFORE INSERT ON public.inventory
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_suppliers
BEFORE INSERT ON public.suppliers
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_promotions
BEFORE INSERT ON public.promotions
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_settings
BEFORE INSERT ON public.settings
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_blog_posts
BEFORE INSERT ON public.blog_posts
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();

CREATE TRIGGER set_tenant_id_user_profiles
BEFORE INSERT ON public.user_profiles
FOR EACH ROW EXECUTE FUNCTION tenant_management.set_tenant_id();
