import React from 'react';
import { Category } from '@/types/database';
import { Card, CardContent } from '@/components/ui/card';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { cn } from '@/lib/utils';
import { Cannabis, Sparkles, ShoppingBag } from 'lucide-react';

interface SubcategoryImageGridProps {
  subcategories: Category[];
  selectedSubcategoryId: string;
  onSelectSubcategory: (subcategoryId: string) => void;
  categoryName: string;
  getCategoryImageUrl: (category: Category) => string;
}

// Create a reusable function to generate a themed background for the "All" option
const generateThemedBackground = (categoryName: string) => {
  // Different gradient colors based on category name to add variety
  const getGradientColors = () => {
    const categoryLower = categoryName.toLowerCase();
    
    if (categoryLower.includes('accessory') || categoryLower.includes('accessories')) {
      return 'from-blue-400/20 to-purple-500/30'; // Accessories theme
    } else if (categoryLower.includes('paper') || categoryLower.includes('rolling')) {
      return 'from-amber-400/20 to-orange-500/30'; // Papers theme
    } else if (categoryLower.includes('vape') || categoryLower.includes('device')) {
      return 'from-cyan-400/20 to-blue-500/30'; // Vape theme
    } else if (categoryLower.includes('glass') || categoryLower.includes('bong')) {
      return 'from-emerald-400/20 to-green-500/30'; // Glass theme
    } else {
      return 'from-violet-400/20 to-indigo-500/30'; // Default theme
    }
  };
  
  // Choose an icon based on category
  const getIcon = () => {
    const categoryLower = categoryName.toLowerCase();
    
    if (categoryLower.includes('accessory') || categoryLower.includes('accessories')) {
      return <ShoppingBag className="w-10 h-10 text-white/70" />;
    } else if (categoryLower.includes('paper') || categoryLower.includes('rolling')) {
      return <Cannabis className="w-10 h-10 text-white/70" />;
    } else {
      return <Sparkles className="w-10 h-10 text-white/70" />;
    }
  };
  
  return {
    gradientClasses: getGradientColors(),
    icon: getIcon()
  };
};

const SubcategoryImageGrid: React.FC<SubcategoryImageGridProps> = ({
  subcategories,
  selectedSubcategoryId,
  onSelectSubcategory,
  categoryName,
  getCategoryImageUrl
}) => {
  if (subcategories.length === 0) return null;
  
  const { gradientClasses, icon } = generateThemedBackground(categoryName);

  return (
    <div className="mb-8">
      <h3 className="text-xl font-medium mb-4">{categoryName} Categories</h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {/* "All" option with themed background */}
        <Card 
          className={cn(
            "cursor-pointer transition-all overflow-hidden",
            "hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02]",
            "backdrop-blur-sm bg-white/10 border border-white/20",
            selectedSubcategoryId === 'all' ? "ring-2 ring-primary shadow-md" : ""
          )}
          onClick={() => onSelectSubcategory('all')}
        >
          <div className="relative">
            <AspectRatio ratio={16/9}>
              <div className={cn(
                "absolute inset-0 bg-gradient-to-br flex items-center justify-center",
                gradientClasses
              )}>
                <div className="absolute inset-0 bg-black/10 backdrop-blur-[1px]"></div>
                <div className="relative z-10 flex flex-col items-center justify-center space-y-2">
                  {icon}
                  <span className="text-lg font-medium text-white drop-shadow-md">All</span>
                </div>
              </div>
            </AspectRatio>
          </div>
          <CardContent className="p-3 text-center bg-white/80 backdrop-blur-sm">
            <p className="font-medium text-sm">All {categoryName}</p>
          </CardContent>
        </Card>

        {/* Subcategory options with glassmorphism */}
        {subcategories.map((subcategory) => {
          const imageUrl = getCategoryImageUrl(subcategory);
          
          return (
            <Card 
              key={subcategory.id}
              className={cn(
                "cursor-pointer transition-all overflow-hidden",
                "hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02]",
                "backdrop-blur-sm bg-white/10 border border-white/20",
                selectedSubcategoryId === subcategory.id ? "ring-2 ring-primary shadow-md" : ""
              )}
              onClick={() => onSelectSubcategory(subcategory.id)}
            >
              <div className="relative">
                <AspectRatio ratio={16/9}>
                  <img 
                    src={imageUrl} 
                    alt={subcategory.name}
                    className="object-cover w-full h-full"
                    onError={(e) => {
                      e.currentTarget.src = 'https://placehold.co/400x225?text=No+Image';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-70 transition-opacity group-hover:opacity-100">
                    {/* Gradient overlay for better text visibility */}
                  </div>
                  <div className="absolute inset-0 bg-black/10 backdrop-blur-[1px] opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div className="px-4 py-2 bg-white/20 backdrop-blur-md rounded-full border border-white/30">
                      <span className="text-white font-medium drop-shadow-md">Browse</span>
                    </div>
                  </div>
                </AspectRatio>
              </div>
              <CardContent className="p-3 text-center bg-white/80 backdrop-blur-sm">
                <p className="font-medium text-sm">{subcategory.name}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default SubcategoryImageGrid;
