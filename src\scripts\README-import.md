# Product Import Scripts

This directory contains scripts for importing products and variants into the database.

## Available Scripts

1. **import-test-subset.cjs**: Imports a small subset of products with variants for testing.
2. **import-variants-fixed.cjs**: Processes a CSV file and generates transformed CSV files for manual import.

## Usage

### Testing with a Small Subset

To import a small subset of products with variants for testing:

```bash
# Make sure you have the required environment variables set
# VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY

# Run the script with the path to your CSV file
node src/scripts/import-test-subset.cjs data/catalog_products.csv
```

This script will:
1. Clear all existing products and variants from the database
2. Import up to 10 products with variants from the CSV file
3. Set products without images to inactive

### Processing a Full CSV File

To process a full CSV file and generate transformed CSV files for manual import:

```bash
# Run the script with the path to your CSV file
node src/scripts/import-variants-fixed.cjs data/catalog_products.csv
```

This script will:
1. Process all products and variants in the CSV file
2. Generate two CSV files in the output directory:
   - products-transformed.csv: Contains all processed products
   - product_variants.csv: Contains all processed variants
3. Set products without images to inactive in the transformed CSV

## Important Notes

1. **Environment Variables**: Make sure you have the required environment variables set:
   - VITE_SUPABASE_URL: The URL of your Supabase project
   - VITE_SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY: The API key for your Supabase project

2. **Image Processing**: The scripts process image URLs to match the Supabase storage format:
   - Removes ~mv2 suffix from filenames
   - Changes file extension to .webp
   - Constructs the full URL with the Supabase storage path

3. **Inactive Products**: Products without images are set to inactive by default.

4. **Option Definitions**: The scripts extract option definitions from the product data in the CSV file.

5. **Variants**: The scripts create variants based on the option combinations in the CSV file.

## Troubleshooting

If you encounter any issues with the import process, check the following:

1. **Environment Variables**: Make sure you have the correct environment variables set.

2. **CSV Format**: Make sure your CSV file has the correct format and contains all required fields.

3. **Image URLs**: Make sure the image URLs in the CSV file are valid and accessible.

4. **Database Schema**: Make sure your database schema matches the expected schema for products and variants.

5. **Error Messages**: Check the error messages in the console for more information about the issue.

## Example CSV Format

The scripts expect a CSV file with the following format:

```
fieldType,handleId,name,description,productImageUrl,price,salePrice,inventory,weight,productOptionName1,productOptionType1,productOptionDescription1,...
Product,123456,Product Name,Product Description,https://example.com/image.jpg,19.99,17.99,100,0.5,Color,DROP_DOWN,"Red,Blue,Green",...
Variant,123456,,,,,,,,,,"Red",...
Variant,123456,,,,,,,,,,"Blue",...
Variant,123456,,,,,,,,,,"Green",...
```

Where:
- `fieldType` can be "Product", "Variant", or "Choice"
- `handleId` is the ID of the product
- `productOptionName1` through `productOptionName6` are the names of the options
- `productOptionType1` through `productOptionType6` are the types of the options (e.g., "DROP_DOWN")
- `productOptionDescription1` through `productOptionDescription6` are the values of the options
