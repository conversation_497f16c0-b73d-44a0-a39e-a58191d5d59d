import React, { useEffect, useState } from 'react';
import { Cannabis, ShoppingBag, Sparkles } from 'lucide-react';
import { BANNER_CONFIG } from '@/config/bannerConfig';
import { cn } from '@/lib/utils';

interface CategoryBannerProps {
  image?: string;
  title: string;
  description?: string; // Keeping this in the interface for backward compatibility
  isDefault?: boolean;
}

const CategoryBanner: React.FC<CategoryBannerProps> = ({ 
  image, 
  title,
  description, // Not used anymore but kept for backward compatibility
  isDefault = false 
}) => {
  const [loaded, setLoaded] = useState(false);
  const [hovered, setHovered] = useState(false);
  
  // Animation states
  useEffect(() => {
    setLoaded(true);
  }, []);
  
  // Dynamic gradient background for default banner using config settings
  const defaultBannerStyle = {
    background: `linear-gradient(135deg, ${BANNER_CONFIG.gradientStart} 0%, ${BANNER_CONFIG.gradientEnd} 100%)`,
  };

  return (
    <div 
      className="relative w-full h-32 md:h-40 lg:h-48 mb-6 overflow-hidden rounded-xl shadow-md transition-all duration-500 ease-in-out"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {/* Background image or gradient with zoom effect */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        {image ? (
          <img 
            src={image} 
            alt={`${title} category`}
            className={cn(
              "w-full h-full object-cover transition-transform duration-700 ease-in-out",
              loaded ? "opacity-100" : "opacity-0",
              hovered ? "scale-110" : "scale-100"
            )}
            onLoad={() => setLoaded(true)}
            onError={(e) => {
              // If image fails to load, apply the default style
              const target = e.currentTarget.parentElement?.parentElement;
              if (target) {
                Object.assign(target.style, defaultBannerStyle);
              }
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          // Otherwise use the default gradient background
          <div className="absolute inset-0" style={defaultBannerStyle}></div>
        )}
      </div>
      
      {/* Animated decorative elements for default banner */}
      {isDefault && (
        <div className="absolute inset-0 overflow-hidden">
          <div className={cn(
            "absolute -left-4 top-1/4 text-white opacity-0 transition-all duration-1000 ease-in-out",
            loaded ? "opacity-10 translate-x-0" : "-translate-x-20"
          )}>
            <Cannabis size={120} />
          </div>
          <div className={cn(
            "absolute right-10 bottom-10 text-white opacity-0 transition-all duration-1000 ease-in-out",
            loaded ? "opacity-10 translate-y-0" : "translate-y-20"
          )}>
            <ShoppingBag size={80} />
          </div>
          <div className={cn(
            "absolute right-1/4 top-10 text-white opacity-0 transition-all duration-1000 ease-in-out",
            loaded ? "opacity-10 translate-y-0" : "-translate-y-20"
          )}>
            <Sparkles size={60} />
          </div>
        </div>
      )}
      
      {/* Animated overlay with title */}
      <div 
        className={cn(
          "absolute inset-0 bg-black flex flex-col items-center justify-center p-4 transition-all duration-500",
          hovered ? "bg-opacity-60" : "bg-opacity-40"
        )}
      >
        <h1 className={cn(
          "text-2xl md:text-3xl lg:text-4xl font-bold text-white text-center transition-all duration-500 ease-in-out",
          loaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
          hovered ? "scale-105" : "scale-100"
        )}>
          {title}
        </h1>
      </div>
      
      {/* Animated overlay gradient */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-500",
        hovered ? "opacity-100" : "opacity-0"
      )} />
    </div>
  );
};

export default CategoryBanner;
