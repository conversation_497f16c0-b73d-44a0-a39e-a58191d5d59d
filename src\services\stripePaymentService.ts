import { supabase } from "@/integrations/supabase/client";
import { CartItem } from "@/context/CartContext";
import { loadStripe } from "@stripe/stripe-js";

/**
 * Service to handle Stripe payment processing
 */
export class StripePaymentService {
  private publishableKey: string;
  private secretKey: string;
  private webhookSecret: string;
  private currency: string;
  private returnUrl: string;
  private cancelUrl: string;
  
  constructor() {
    // Initialize with config from environment variables
    this.publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_placeholder';
    this.secretKey = import.meta.env.VITE_STRIPE_SECRET_KEY || 'sk_test_placeholder';
    this.webhookSecret = import.meta.env.VITE_STRIPE_WEBHOOK_SECRET || 'whsec_placeholder';
    this.currency = 'GBP';
    this.returnUrl = `${window.location.origin}/checkout/confirmation`;
    this.cancelUrl = `${window.location.origin}/checkout`;
  }

  /**
   * Create a Stripe payment session
   * @param items Cart items to be purchased
   * @param shipping Shipping cost
   * @param tax Tax amount
   * @returns Stripe client secret and session ID
   */
  public async createStripePayment(items: CartItem[], shipping: number, tax: number): Promise<{ clientSecret: string, sessionId: string }> {
    try {
      // Calculate total amount
      const itemTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const total = itemTotal + shipping + tax;
      
      // Generate a unique session ID
      const sessionId = crypto.randomUUID();
      
      // In a real implementation, this would call the Stripe API to create a payment intent
      // For now, we'll create a simulated Stripe client secret
      const clientSecret = `pi_${sessionId}_secret_${Math.random().toString(36).substring(2)}`;
      
      // Store the payment session in the database
      const { error } = await supabase
        .from('payment_sessions')
        .insert({
          id: sessionId,
          provider: 'stripe',
          amount: total,
          currency: this.currency,
          status: 'pending',
          metadata: {
            items: items.map(item => ({
              id: item.id,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              image: item.image
            })),
            shipping,
            tax,
            client_secret: clientSecret
          }
        });
      
      if (error) throw error;
      
      return { clientSecret, sessionId };
    } catch (err) {
      console.error('Error creating Stripe payment:', err);
      throw new Error('Failed to create Stripe payment session');
    }
  }

  /**
   * Process a successful Stripe payment confirmation
   * @param paymentIntentId The Stripe payment intent ID
   * @returns The order ID
   */
  public async processStripePaymentConfirmation(paymentIntentId: string): Promise<string> {
    try {
      // In a real implementation, this would verify the payment with Stripe API
      // For now, we'll simulate the verification process
      
      // Find the payment session with the matching client secret
      const { data: sessions, error: sessionError } = await supabase
        .from('payment_sessions')
        .select('*')
        .filter('metadata->client_secret', 'ilike', `%${paymentIntentId}%`);
      
      if (sessionError || !sessions || sessions.length === 0) {
        throw new Error('Payment session not found');
      }
      
      const session = sessions[0];
      
      // Update the payment session status
      const { error: updateError } = await supabase
        .from('payment_sessions')
        .update({ status: 'completed' })
        .eq('id', session.id);
      
      if (updateError) throw updateError;
      
      // Create an order from the payment session
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: supabase.auth.getUser()?.data?.user?.id,
          payment_provider: 'stripe',
          payment_session_id: session.id,
          status: 'processing',
          subtotal: session.metadata.items.reduce(
            (sum: number, item: any) => sum + (item.price * item.quantity), 0
          ),
          shipping_cost: session.metadata.shipping,
          tax_amount: session.metadata.tax,
          total_amount: session.amount,
          currency: session.currency
        })
        .select()
        .single();
      
      if (orderError || !order) throw orderError || new Error('Failed to create order');
      
      // Create order items
      const orderItems = session.metadata.items.map((item: any) => ({
        order_id: order.id,
        product_id: item.id,
        product_name: item.name,
        quantity: item.quantity,
        price: item.price,
        image_url: item.image
      }));
      
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);
      
      if (itemsError) throw itemsError;
      
      return order.id;
    } catch (err) {
      console.error('Error processing Stripe payment confirmation:', err);
      throw new Error('Failed to process payment confirmation');
    }
  }

  /**
   * Get the Stripe publishable key
   * @returns The Stripe publishable key
   */
  public getPublishableKey(): string {
    return this.publishableKey;
  }

  /**
   * Initialize Stripe
   * @returns A Promise that resolves to the Stripe instance
   */
  public async initializeStripe() {
    return await loadStripe(this.publishableKey);
  }
}