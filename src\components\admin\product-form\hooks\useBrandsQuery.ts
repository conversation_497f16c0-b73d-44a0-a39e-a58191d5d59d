import { useQuery } from "@tanstack/react-query";
import { Brand } from "@/types/database";

export function useBrandsQuery() {
  return useQuery({
    queryKey: ["brands"],
    queryFn: async () => {
      try {
        // Use fetch directly to avoid TypeScript issues with Supabase types
        const response = await fetch('https://pkjyjuaiokrhgbutjhla.supabase.co/rest/v1/brands?select=*&order=name', {
          headers: {
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY || '',
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`Error fetching brands: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('Fetched brands:', data);
        return data as Brand[];
      } catch (err) {
        console.error('Exception in brands query:', err);
        return [] as Brand[];
      }
    },
  });
}
