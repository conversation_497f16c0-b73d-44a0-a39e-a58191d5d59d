-- Create a settings table to store store-wide settings
CREATE TABLE IF NOT EXISTS public.settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on the key column for faster lookups
CREATE INDEX IF NOT EXISTS settings_key_idx ON public.settings (key);

-- Insert default store settings
INSERT INTO public.settings (key, value)
VALUES (
    'store_settings',
    '{
        "store_name": "Bits N Bongs",
        "store_email": "",
        "store_phone": "",
        "store_address": "",
        "currency": "GBP",
        "tax_rate": 20,
        "enable_guest_checkout": true,
        "enable_reviews": true,
        "maintenance_mode": false
    }'::jsonb
)
ON CONFLICT (key) DO NOTHING;

-- Create RLS policies for the settings table
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Only allow admins to update settings
CREATE POLICY "Allow admins to manage settings" ON public.settings
    USING (EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    ))
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    ));

-- Allow anyone to read settings
CREATE POLICY "Allow anyone to read settings" ON public.settings
    FOR SELECT USING (true);
