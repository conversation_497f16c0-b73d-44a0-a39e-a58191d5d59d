import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pkjyjuaiokrhgbutjhla.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Exchange rate from USD to GBP (this is an example rate, should be updated with current rate)
// In a production environment, you would use a currency API to get the latest rate
const USD_TO_GBP_RATE = 0.79; // Example rate: 1 USD = 0.79 GBP

async function updateCurrencyToGBP() {
  console.log('Starting to update prices from USD to GBP...');
  
  try {
    // Fetch all products
    const { data: products, error } = await supabase
      .from('products')
      .select('id, price, sale_price, cost_price');
    
    if (error) {
      throw error;
    }
    
    console.log(`Found ${products.length} products to update`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      // Convert prices from USD to GBP
      const updatedProduct = {
        price: product.price ? Math.round((product.price * USD_TO_GBP_RATE) * 100) / 100 : null,
        sale_price: product.sale_price ? Math.round((product.sale_price * USD_TO_GBP_RATE) * 100) / 100 : null,
        cost_price: product.cost_price ? Math.round((product.cost_price * USD_TO_GBP_RATE) * 100) / 100 : null
      };
      
      // Update the product with converted prices
      const { error: updateError } = await supabase
        .from('products')
        .update(updatedProduct)
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.id}:`, updateError);
        continue;
      }
      
      updatedCount++;
      
      if (updatedCount % 10 === 0) {
        console.log(`Updated ${updatedCount} products so far...`);
      }
    }
    
    console.log(`Successfully updated ${updatedCount} products with GBP prices`);
    
  } catch (error) {
    console.error('Error updating prices to GBP:', error);
  }
}

// Run the function
updateCurrencyToGBP()
  .then(() => {
    console.log('Currency update script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Currency update script failed:', error);
    process.exit(1);
  });
