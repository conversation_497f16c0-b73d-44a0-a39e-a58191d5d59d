// Newsletter HTML Templates for Bits N Bongs
// Professional email templates with branding and responsive design

export interface NewsletterTemplateData {
  subject: string;
  content: string;
  products?: Array<{
    id: string;
    name: string;
    price: number;
    sale_price?: number;
    image: string;
    description?: string;
  }>;
  headerImage?: string;
  footerText?: string;
  unsubscribeUrl?: string;
}

export interface NewsletterTemplate {
  id: string;
  name: string;
  description: string;
  preview: string;
  generate: (data: NewsletterTemplateData) => string;
}

// Brand colors and styling
const BRAND_COLORS = {
  primary: '#2D5016', // Dark green
  secondary: '#4A7C59', // Medium green
  accent: '#8FBC8F', // Light green
  background: '#F8F9FA',
  text: '#333333',
  textLight: '#666666',
  white: '#FFFFFF',
  border: '#E5E5E5'
};

// Common CSS styles for email compatibility
const EMAIL_STYLES = `
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', Arial, sans-serif;
      line-height: 1.6;
      color: ${BRAND_COLORS.text};
      background-color: ${BRAND_COLORS.background};
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: ${BRAND_COLORS.white};
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .header {
      background: linear-gradient(135deg, ${BRAND_COLORS.primary} 0%, ${BRAND_COLORS.secondary} 100%);
      padding: 30px 20px;
      text-align: center;
      color: ${BRAND_COLORS.white};
    }

    .logo {
      max-width: 200px;
      height: auto;
      margin-bottom: 15px;
    }

    .header-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .header-subtitle {
      font-size: 16px;
      opacity: 0.9;
    }

    .content {
      padding: 40px 30px;
    }

    .content h1 {
      color: ${BRAND_COLORS.primary};
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 20px;
      text-align: center;
    }

    .content h2 {
      color: ${BRAND_COLORS.primary};
      font-size: 22px;
      font-weight: 600;
      margin: 30px 0 15px 0;
      border-bottom: 2px solid ${BRAND_COLORS.accent};
      padding-bottom: 8px;
    }

    .content h3 {
      color: ${BRAND_COLORS.secondary};
      font-size: 18px;
      font-weight: 600;
      margin: 25px 0 12px 0;
    }

    .content p {
      margin-bottom: 16px;
      line-height: 1.7;
    }

    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin: 30px 0;
    }

    .product-card {
      border: 1px solid ${BRAND_COLORS.border};
      border-radius: 12px;
      overflow: hidden;
      transition: transform 0.2s ease;
      background: ${BRAND_COLORS.white};
    }

    .product-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .product-info {
      padding: 20px;
    }

    .product-name {
      font-size: 16px;
      font-weight: 600;
      color: ${BRAND_COLORS.text};
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .product-price {
      font-size: 18px;
      font-weight: 700;
      color: ${BRAND_COLORS.primary};
    }

    .product-price-sale {
      color: #E74C3C;
    }

    .product-price-original {
      text-decoration: line-through;
      color: ${BRAND_COLORS.textLight};
      font-size: 14px;
      margin-left: 8px;
    }

    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, ${BRAND_COLORS.primary} 0%, ${BRAND_COLORS.secondary} 100%);
      color: ${BRAND_COLORS.white};
      padding: 15px 30px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      text-align: center;
      margin: 20px 0;
      transition: all 0.3s ease;
    }

    .cta-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(45, 80, 22, 0.3);
    }

    .footer {
      background-color: ${BRAND_COLORS.primary};
      color: ${BRAND_COLORS.white};
      padding: 40px 30px;
      text-align: center;
    }

    .footer-content {
      margin-bottom: 20px;
    }

    .footer-links {
      margin: 20px 0;
    }

    .footer-links a {
      color: ${BRAND_COLORS.accent};
      text-decoration: none;
      margin: 0 15px;
    }

    .social-links {
      margin: 20px 0;
    }

    .social-links a {
      display: inline-block;
      margin: 0 10px;
      color: ${BRAND_COLORS.white};
      text-decoration: none;
    }

    .unsubscribe {
      font-size: 12px;
      color: ${BRAND_COLORS.accent};
      margin-top: 20px;
    }

    .unsubscribe a {
      color: ${BRAND_COLORS.accent};
    }

    @media only screen and (max-width: 600px) {
      .container {
        width: 100% !important;
      }

      .header, .content, .footer {
        padding: 20px !important;
      }

      .product-grid {
        grid-template-columns: 1fr !important;
      }

      .header-title {
        font-size: 20px !important;
      }

      .content h1 {
        font-size: 24px !important;
      }
    }
  </style>
`;

// Classic Newsletter Template
const classicTemplate: NewsletterTemplate = {
  id: 'classic',
  name: 'Classic Newsletter',
  description: 'Clean, professional layout with header, content sections, and product showcase',
  preview: 'Header with logo, main content area, product grid, footer with social links',
  generate: (data: NewsletterTemplateData) => {
    const productsHtml = data.products?.map(product => `
      <div class="product-card">
        <img src="${product.image}" alt="${product.name}" class="product-image" />
        <div class="product-info">
          <div class="product-name">${product.name}</div>
          <div class="product-price ${product.sale_price ? 'product-price-sale' : ''}">
            £${product.sale_price || product.price}
            ${product.sale_price ? `<span class="product-price-original">£${product.price}</span>` : ''}
          </div>
        </div>
      </div>
    `).join('') || '';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.subject}</title>
        ${EMAIL_STYLES}
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="header">
            <img src="/images/logo1.png" alt="Bits N Bongs" class="logo" />
            <img src="/images/text-logo.png" alt="Bits N Bongs" style="max-width: 250px; height: auto; margin: 10px 0;" />
            <div class="header-subtitle">Premium CBD Products & Smoking Accessories</div>
          </div>

          <!-- Main Content -->
          <div class="content">
            <h1>${data.subject}</h1>
            ${data.content.replace(/\n/g, '<br>')}

            ${data.products && data.products.length > 0 ? `
              <h2>Featured Products</h2>
              <div class="product-grid">
                ${productsHtml}
              </div>
              <div style="text-align: center;">
                <a href="https://bitsnbongs.com/shop" class="cta-button">Shop All Products</a>
              </div>
            ` : ''}
          </div>

          <!-- Footer -->
          <div class="footer">
            <div class="footer-content">
              <strong>BITS N BONGS</strong><br>
              78 Stanley St, Kinning Park, Glasgow G41 1JH<br>
              Phone: 0141 737 3717 | Email: <EMAIL>
            </div>

            <div class="footer-links">
              <a href="https://bitsnbongs.com/shop">Shop</a>
              <a href="https://bitsnbongs.com/about">About</a>
              <a href="https://bitsnbongs.com/contact">Contact</a>
              <a href="https://bitsnbongs.com/faq">FAQ</a>
            </div>

            <div class="social-links">
              <a href="https://www.facebook.com/Bitsnbongsuk/">Facebook</a>
              <a href="https://www.instagram.com/bitsandbongs/">Instagram</a>
              <a href="https://www.etsy.com/uk/shop/BitsNBongsUK">Etsy</a>
            </div>

            <div class="unsubscribe">
              ${data.footerText || 'You received this email because you subscribed to our newsletter.'}<br>
              <a href="${data.unsubscribeUrl || '#'}">Unsubscribe</a> |
              <a href="https://bitsnbongs.com/privacy-policy">Privacy Policy</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
};

// Modern Newsletter Template
const modernTemplate: NewsletterTemplate = {
  id: 'modern',
  name: 'Modern Newsletter',
  description: 'Contemporary design with bold typography and card-based layout',
  preview: 'Large hero section, card-based content, prominent CTAs',
  generate: (data: NewsletterTemplateData) => {
    const productsHtml = data.products?.map(product => `
      <div class="product-card modern-card">
        <img src="${product.image}" alt="${product.name}" class="product-image" />
        <div class="product-info">
          <div class="product-name">${product.name}</div>
          <div class="product-price ${product.sale_price ? 'product-price-sale' : ''}">
            £${product.sale_price || product.price}
            ${product.sale_price ? `<span class="product-price-original">£${product.price}</span>` : ''}
          </div>
          <a href="https://bitsnbongs.com/shop" class="cta-button" style="display: block; margin-top: 15px; padding: 10px; font-size: 14px;">View Product</a>
        </div>
      </div>
    `).join('') || '';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.subject}</title>
        ${EMAIL_STYLES}
        <style>
          .modern-hero {
            background: linear-gradient(135deg, ${BRAND_COLORS.primary} 0%, ${BRAND_COLORS.secondary} 50%, ${BRAND_COLORS.accent} 100%);
            padding: 60px 30px;
            text-align: center;
            color: white;
          }
          .modern-hero h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
          }
          .modern-card {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Hero Section -->
          <div class="modern-hero">
            <img src="/images/logo1.png" alt="Bits N Bongs" class="logo" />
            <img src="/images/text-logo.png" alt="Bits N Bongs" style="max-width: 250px; height: auto; margin: 10px 0;" />
            <h1>${data.subject}</h1>
            <div class="header-subtitle">Discover Premium CBD Products & Accessories</div>
          </div>

          <!-- Content -->
          <div class="content">
            ${data.content.replace(/\n/g, '<br>')}

            ${data.products && data.products.length > 0 ? `
              <h2>🌿 Featured This Week</h2>
              <div class="product-grid">
                ${productsHtml}
              </div>
            ` : ''}

            <div style="text-align: center; margin: 40px 0;">
              <a href="https://bitsnbongs.com/shop" class="cta-button" style="font-size: 18px; padding: 20px 40px;">Explore Our Collection</a>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <div class="footer-content">
              <strong>BITS N BONGS</strong><br>
              Your trusted source for premium CBD products<br>
              📍 78 Stanley St, Glasgow | 📞 0141 737 3717
            </div>

            <div class="social-links">
              <a href="https://www.facebook.com/Bitsnbongsuk/">📘 Facebook</a>
              <a href="https://www.instagram.com/bitsandbongs/">📷 Instagram</a>
              <a href="https://www.etsy.com/uk/shop/BitsNBongsUK">🛒 Etsy</a>
            </div>

            <div class="unsubscribe">
              <a href="${data.unsubscribeUrl || '#'}">Unsubscribe</a> |
              <a href="https://bitsnbongs.com/privacy-policy">Privacy Policy</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
};

// Minimal Newsletter Template
const minimalTemplate: NewsletterTemplate = {
  id: 'minimal',
  name: 'Minimal Newsletter',
  description: 'Clean, text-focused design with subtle branding',
  preview: 'Simple header, typography-focused content, minimal product display',
  generate: (data: NewsletterTemplateData) => {
    const productsHtml = data.products?.map(product => `
      <div style="border-bottom: 1px solid ${BRAND_COLORS.border}; padding: 20px 0; display: flex; align-items: center;">
        <img src="${product.image}" alt="${product.name}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 20px;" />
        <div>
          <div style="font-weight: 600; margin-bottom: 5px;">${product.name}</div>
          <div style="color: ${BRAND_COLORS.primary}; font-weight: 700;">
            £${product.sale_price || product.price}
            ${product.sale_price ? `<span style="text-decoration: line-through; color: ${BRAND_COLORS.textLight}; margin-left: 8px;">£${product.price}</span>` : ''}
          </div>
        </div>
      </div>
    `).join('') || '';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.subject}</title>
        ${EMAIL_STYLES}
      </head>
      <body>
        <div class="container">
          <!-- Simple Header -->
          <div style="padding: 40px 30px; text-align: center; border-bottom: 1px solid ${BRAND_COLORS.border};">
            <img src="/images/text-logo.png" alt="Bits N Bongs" style="max-width: 200px; height: auto; margin-bottom: 15px;" />
            <p style="color: ${BRAND_COLORS.textLight}; margin: 0;">Premium CBD Products & Smoking Accessories</p>
          </div>

          <!-- Content -->
          <div class="content">
            <h2 style="color: ${BRAND_COLORS.text}; font-size: 24px; margin-bottom: 20px;">${data.subject}</h2>
            ${data.content.replace(/\n/g, '<br>')}

            ${data.products && data.products.length > 0 ? `
              <h3 style="margin: 40px 0 20px 0; color: ${BRAND_COLORS.primary};">Featured Products</h3>
              <div>
                ${productsHtml}
              </div>
            ` : ''}
          </div>

          <!-- Simple Footer -->
          <div style="background-color: ${BRAND_COLORS.background}; padding: 30px; text-align: center; color: ${BRAND_COLORS.textLight}; font-size: 14px;">
            <p>BITS N BONGS | 78 Stanley St, Glasgow G41 1JH</p>
            <p>
              <a href="https://bitsnbongs.com" style="color: ${BRAND_COLORS.primary};">Visit Website</a> |
              <a href="${data.unsubscribeUrl || '#'}" style="color: ${BRAND_COLORS.textLight};">Unsubscribe</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
};

// Premium Modern Template (AI-designed)
const premiumModernTemplate: NewsletterTemplate = {
  id: 'premium-modern',
  name: 'Premium Modern',
  description: 'AI-designed premium template with modern aesthetics and professional styling',
  preview: 'Gradient header, elegant product cards, responsive design with dark mode support',
  generate: (data: NewsletterTemplateData) => {
    const productsHtml = data.products?.map(product => `
      <div class="product-card" style="display: inline-block; width: 100%; max-width: 180px; margin: 0 8px 16px 8px; vertical-align: top; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <div style="position: relative;">
          <img src="${product.image}" alt="${product.name}" class="product-image" style="width: 100%; height: auto; display: block;">
          ${product.sale_price ? '<div style="position: absolute; top: 8px; right: 8px;"><span class="sale-badge" style="background-color: #2D5016; color: white; font-size: 10px; padding: 2px 8px; border-radius: 20px; font-weight: 500;">SALE</span></div>' : ''}
        </div>
        <div class="product-details" style="padding: 12px;">
          <h4 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 500; color: #333333;">${product.name}</h4>
          <p style="margin: 0; font-size: 16px; font-weight: 700; color: #2D5016;">
            £${product.sale_price || product.price}
            ${product.sale_price ? `<span style="text-decoration: line-through; color: #999999; font-size: 14px; font-weight: normal; margin-left: 8px;">£${product.price}</span>` : ''}
          </p>
        </div>
      </div>
    `).join('') || '';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.subject}</title>
        <style type="text/css">
          /* Base styles */
          body, html {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.5;
            color: #333333;
          }

          /* Responsive container */
          .container {
            max-width: 600px !important;
            margin: 0 auto !important;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }

          /* Brand colors */
          .bg-dark-green {
            background-color: #2D5016 !important;
          }

          .bg-medium-green {
            background-color: #4A7C59 !important;
          }

          .bg-light-green {
            background-color: #8FBC8F !important;
          }

          .text-dark-green {
            color: #2D5016 !important;
          }

          .text-medium-green {
            color: #4A7C59 !important;
          }

          .text-light-green {
            color: #8FBC8F !important;
          }

          /* Header styles */
          .header {
            background: linear-gradient(to right, #2D5016, #4A7C59);
            padding: 24px;
            color: white;
            text-align: center;
            position: relative;
          }



          /* Hero section */
          .hero {
            background-color: #f8faf6;
            border-bottom: 1px solid #e0e0e0;
            padding: 32px 24px;
            position: relative;
          }

          .hero:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, #2D5016, #4A7C59, #8FBC8F);
          }

          /* Main content */
          .content {
            padding: 32px 24px;
          }

          /* Content heading styles */
          .content h1 {
            color: white !important;
            font-size: 28px;
            font-weight: 700 !important;
            margin: 0 0 20px 0;
            text-align: center;
            background: linear-gradient(to right, #2D5016, #4A7C59);
            padding: 20px;
            border-radius: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
          }

          .content h2 {
            color: #2D5016;
            font-size: 24px;
            font-weight: 600;
            margin: 30px 0 15px 0;
            border-bottom: 2px solid #4A7C59;
            padding-bottom: 8px;
          }

          .content h3 {
            color: #4A7C59;
            font-size: 20px;
            font-weight: 600;
            margin: 25px 0 12px 0;
          }

          /* Product grid */
          .product-grid {
            font-size: 0;
            text-align: center;
          }

          .product-card {
            display: inline-block;
            width: 100%;
            max-width: 180px;
            margin: 0 8px 16px 8px;
            vertical-align: top;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
          }

          .product-image {
            width: 100%;
            height: auto;
            background-color: #f5f5f5;
          }

          .product-details {
            padding: 12px;
          }

          .sale-badge {
            display: inline-block;
            background-color: #2D5016;
            color: white;
            font-size: 10px;
            padding: 2px 8px;
            border-radius: 20px;
            margin-bottom: 8px;
            font-weight: 500;
            letter-spacing: 0.5px;
          }

          /* Section Dividers */
          .section-divider {
            display: table;
            width: 100%;
            margin: 24px 0;
          }

          .section-divider-line {
            display: table-cell;
            width: 40%;
            vertical-align: middle;
          }

          .section-divider-line hr {
            border: none;
            height: 1px;
            background-color: #e0e0e0;
            margin: 0;
          }

          .section-divider-text {
            display: table-cell;
            text-align: center;
            padding: 0 16px;
            white-space: nowrap;
            color: #2D5016;
            font-weight: 600;
            font-size: 18px;
          }

          /* CTA section */
          .cta {
            background-color: #f8faf6;
            border-top: 1px solid #e0e0e0;
            padding: 32px 24px;
            text-align: center;
            position: relative;
          }

          .cta:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, #2D5016, #4A7C59, #8FBC8F);
          }

          .cta-button {
            display: inline-block;
            background-color: #4A7C59;
            color: white !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          /* Footer styles */
          .footer {
            background: linear-gradient(to right, #2D5016, #4A7C59);
            color: white;
            padding: 32px 24px;
            position: relative;
          }

          .footer:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background-color: rgba(255,255,255,0.1);
          }

          .social-icons {
            text-align: center;
            margin: 16px 0;
          }

          .social-icon {
            display: inline-block;
            width: 32px;
            height: 32px;
            background-color: rgba(255,255,255,0.1);
            border-radius: 50%;
            margin: 0 8px;
            text-align: center;
            line-height: 32px;
          }

          /* Divider */
          .divider {
            height: 1px;
            background-color: #4A7C59;
            margin: 24px 0;
          }

          /* Image enhancements */
          .logo-image {
            display: block;
            margin: 0 auto;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
          }

          /* Responsive styles */
          @media only screen and (max-width: 480px) {
            .product-card {
              max-width: 100%;
              margin: 0 0 16px 0;
            }
          }

          /* Dark mode support */
          @media (prefers-color-scheme: dark) {
            body {
              background-color: #121212 !important;
            }

            .container {
              background-color: #1e1e1e !important;
              box-shadow: 0 4px 10px rgba(0,0,0,0.3) !important;
            }

            .hero, .content, .cta {
              background-color: #1e1e1e !important;
              color: #e0e0e0 !important;
              border-color: #333333 !important;
            }

            .text-dark-green {
              color: #8FBC8F !important;
            }

            .product-card {
              border-color: #333333 !important;
              background-color: #2a2a2a !important;
              box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
            }

            .section-divider-line hr {
              background-color: #333333 !important;
            }
          }
        </style>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f5f5f5;">
        <!-- Preview Text -->
        <div style="display: none; max-height: 0px; overflow: hidden;">
          ${data.subject} - Latest updates from Bits N Bongs
        </div>

        <!-- Container -->
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
          <tr>
            <td align="center" style="padding: 20px 0;">
              <table class="container" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <!-- Header -->
                <tr>
                  <td class="header" align="center" style="padding: 24px; background: linear-gradient(to right, #2D5016, #4A7C59); color: white; text-align: center; position: relative;">
                    <img src="/images/logo1.png" alt="Bits N Bongs Logo" width="64" class="logo-image" style="display: block; margin: 0 auto 12px auto; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));">
                    <img src="/images/text-logow.png" alt="Bits N Bongs" width="160" class="logo-image" style="display: block; margin: 0 auto 12px auto; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));">
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #8FBC8F; font-weight: 500; letter-spacing: 0.5px;">Premium CBD Products & Smoking Accessories</p>
                  </td>
                </tr>

                <!-- Hero/Subject Section -->
                <tr>
                  <td class="hero" align="left" style="padding: 32px 24px; background-color: #f8faf6; border-bottom: 1px solid #e0e0e0; position: relative;">
                    <!-- Gradient top border -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(to right, #2D5016, #4A7C59, #8FBC8F);"></div>

                    <h2 style="margin: 0; font-size: 24px; font-weight: 600; color: #2D5016;">${data.subject}</h2>
                    <div style="margin-top: 16px; height: 4px; width: 64px; background-color: #4A7C59; border-radius: 2px;"></div>
                  </td>
                </tr>

                <!-- Main Content Area -->
                <tr>
                  <td class="content" align="left" style="padding: 32px 24px;">
                    ${data.content.replace(/\n/g, '<br>')}
                  </td>
                </tr>

                <!-- Products Showcase (if products exist) -->
                ${data.products && data.products.length > 0 ? `
                <tr>
                  <td align="left" style="padding: 0 24px 32px 24px;">
                    <!-- Section divider -->
                    <div class="section-divider">
                      <div class="section-divider-line"><hr></div>
                      <div class="section-divider-text">Featured Products</div>
                      <div class="section-divider-line"><hr></div>
                    </div>

                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td class="product-grid" style="text-align: center;">
                          ${productsHtml}
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                ` : ''}

                <!-- CTA Section -->
                <tr>
                  <td class="cta" align="center" style="padding: 32px 24px; background-color: #f8faf6; border-top: 1px solid #e0e0e0; position: relative;">
                    <!-- Gradient top border -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 1px; background: linear-gradient(to right, #2D5016, #4A7C59, #8FBC8F);"></div>

                    <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #2D5016;">Discover Our Complete Collection</h3>
                    <a href="https://bitsnbongs.com/shop" class="cta-button" style="display: inline-block; background-color: #4A7C59; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">Shop All Products</a>
                  </td>
                </tr>

                <!-- Footer -->
                <tr>
                  <td class="footer" align="center" style="padding: 32px 24px; background: linear-gradient(to right, #2D5016, #4A7C59); color: white; position: relative; overflow: hidden;">
                    <!-- Top border -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 1px; background-color: rgba(255,255,255,0.1);"></div>

                    <!-- Decorative leaf pattern -->
                    <div class="leaf-bg" style="position: absolute; bottom: 0; right: 0; width: 180px; height: 180px; opacity: 0.1;">
                      <svg viewBox="0 0 512 512" fill="white" style="width: 100%; height: 100%;">
                        <path d="M378.31,54.77C327.547,103.835,233.1,141.49,146.9,113.031,241.237,224.678,170.47,328.226,87.507,352q-7.251,2.092-14.645,3.578a236.537,236.537,0,0,0,47.548,32.381A330.914,330.914,0,0,1,172.96,411.63c82.632-3.311,127.847-45.627,139.2-80.888,24.012-74.414-23.573-140.688-68.492-173.673,58.011-13.584,141.926,1.269,159.935,87.662q-51.8-117.9,59.768-157.476c-74.643-75.434-197.358-73.092-270.392-8.492C237.95,51.486,307.949,39.966,378.31,54.77Z"/>
                      </svg>
                    </div>

                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                              <td align="center" style="padding: 0 0 20px 0;">
                                <p style="margin: 0; font-size: 14px; color: #8FBC8F; font-weight: 500;">78 Stanley St, Glasgow | 0141 737 3717</p>
                              </td>
                            </tr>
                            <tr>
                              <td align="center" style="padding: 0;">
                                <div style="font-size: 0;">
                                  <!-- Facebook Icon -->
                                  <a href="https://www.facebook.com/Bitsnbongsuk/" style="display: inline-block; margin: 0 8px; text-decoration: none;">
                                    <div style="width: 32px; height: 32px; background-color: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="white" style="width: 16px; height: 16px;">
                                        <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
                                      </svg>
                                    </div>
                                  </a>
                                  <!-- Instagram Icon -->
                                  <a href="https://www.instagram.com/bitsandbongs/" style="display: inline-block; margin: 0 8px; text-decoration: none;">
                                    <div style="width: 32px; height: 32px; background-color: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="white" style="width: 16px; height: 16px;">
                                        <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
                                      </svg>
                                    </div>
                                  </a>
                                  <!-- Etsy Icon -->
                                  <a href="https://www.etsy.com/uk/shop/BitsNBongsUK" style="display: inline-block; margin: 0 8px; text-decoration: none;">
                                    <div style="width: 32px; height: 32px; background-color: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" style="width: 16px; height: 16px;">
                                        <path d="M8,4v1.5h1c0.5,0,0.5,0.2,0.5,0.6v8.4c0,0.4-0.1,0.6-0.6,0.6H8V17h8v-1.9h-1c-0.5,0-0.6-0.2-0.6-0.6v-3.8h2.6c0.2,0,0.3,0.1,0.4,0.5l0.3,1.7h1.5v-5H17l-0.3,1.7c-0.1,0.4-0.2,0.5-0.4,0.5h-2.6V6.1c0-0.1,0-0.2,0.2-0.2h3.3c0.3,0,0.4,0.1,0.6,0.5l0.8,1.9h1.5L19.5,4H8z"/>
                                      </svg>
                                    </div>
                                  </a>
                                </div>
                              </td>
                            </tr>
                          </table>

                          <div class="divider" style="height: 1px; background-color: #4A7C59; margin: 24px 0;"></div>

                          <p style="margin: 0 0 8px 0; font-size: 12px; color: #8FBC8F;">&copy; 2025 Bits N Bongs. All rights reserved.</p>
                          <a href="${data.unsubscribeUrl || '#'}" style="font-size: 12px; color: #8FBC8F; text-decoration: underline;">Unsubscribe</a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;
  }
};

export const newsletterTemplates: NewsletterTemplate[] = [
  classicTemplate,
  modernTemplate,
  minimalTemplate,
  premiumModernTemplate
];

export const getTemplate = (templateId: string): NewsletterTemplate | undefined => {
  return newsletterTemplates.find(template => template.id === templateId);
};

export const generateNewsletterFromTemplate = (
  templateId: string,
  data: NewsletterTemplateData
): string => {
  const template = getTemplate(templateId);
  if (!template) {
    throw new Error(`Template with ID "${templateId}" not found`);
  }
  return template.generate(data);
};
