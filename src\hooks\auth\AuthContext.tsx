
import { createContext } from 'react';
import { AuthContextType } from './types';

// Create context with default undefined value
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Export a default value for testing/development
export const defaultAuthContext: AuthContextType = {
  session: null,
  user: null,
  profile: null,
  isLoading: false,
  isAdmin: false,
  isInitializing: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  updateProfile: async () => {},
};
