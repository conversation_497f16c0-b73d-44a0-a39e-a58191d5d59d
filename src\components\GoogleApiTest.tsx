import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { testGoogleSearchApi, searchGoogleImages } from '@/api/googleImageSearch';
import { Loader2 } from 'lucide-react';

/**
 * A component to test the Google Custom Search API configuration
 */
export default function GoogleApiTest() {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [testLoading, setTestLoading] = useState(false);
  const [imageResults, setImageResults] = useState<string[]>([]);
  const [imageLoading, setImageLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Run a basic API test
  const runApiTest = async () => {
    setTestLoading(true);
    setTestResult(null);
    setErrorMessage(null);
    
    try {
      const result = await testGoogleSearchApi();
      setTestResult(result);
    } catch (error) {
      setErrorMessage(`Test error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setTestLoading(false);
    }
  };

  // Test image search
  const testImageSearch = async () => {
    setImageLoading(true);
    setImageResults([]);
    setErrorMessage(null);
    
    try {
      const images = await searchGoogleImages('nature', 3);
      setImageResults(images);
      
      if (images.length === 0) {
        setErrorMessage('No images found. The API may be working but returned no results.');
      }
    } catch (error) {
      setErrorMessage(`Image search error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setImageLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Google Custom Search API Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic API Test</CardTitle>
            <CardDescription>
              Test if your Google Custom Search API is properly configured
            </CardDescription>
          </CardHeader>
          <CardContent>
            {testResult && (
              <div className="p-4 bg-gray-100 rounded-md mb-4">
                <p className="whitespace-pre-wrap">{testResult}</p>
              </div>
            )}
            
            {errorMessage && (
              <div className="p-4 bg-red-100 text-red-800 rounded-md mb-4">
                <p className="whitespace-pre-wrap">{errorMessage}</p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={runApiTest} disabled={testLoading}>
              {testLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Run API Test'
              )}
            </Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Image Search Test</CardTitle>
            <CardDescription>
              Test if image search is working correctly
            </CardDescription>
          </CardHeader>
          <CardContent>
            {imageResults.length > 0 && (
              <div className="grid grid-cols-1 gap-4 mb-4">
                {imageResults.map((url, index) => (
                  <div key={index} className="overflow-hidden rounded-md">
                    <img 
                      src={url} 
                      alt={`Test result ${index + 1}`} 
                      className="w-full h-auto object-cover"
                      onError={(e) => {
                        // Handle image load errors
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://placehold.co/400x300?text=Image+Load+Error';
                      }}
                    />
                  </div>
                ))}
              </div>
            )}
            
            {errorMessage && (
              <div className="p-4 bg-red-100 text-red-800 rounded-md mb-4">
                <p className="whitespace-pre-wrap">{errorMessage}</p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={testImageSearch} disabled={imageLoading}>
              {imageLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                'Test Image Search'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      <div className="mt-8 p-6 bg-gray-100 rounded-md">
        <h2 className="text-xl font-semibold mb-4">Troubleshooting Guide</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li><strong>403 Forbidden Error:</strong> Make sure the Custom Search API is enabled in your Google Cloud Console.</li>
          <li><strong>API Key Issues:</strong> Check if your API key has any restrictions (domain, IP, etc.).</li>
          <li><strong>Billing:</strong> The Custom Search JSON API requires billing to be enabled (even for the free tier).</li>
          <li><strong>Search Engine Configuration:</strong> Ensure your Custom Search Engine is configured to search for images.</li>
          <li><strong>Quota Limits:</strong> The free tier allows only 100 queries per day.</li>
        </ul>
      </div>
    </div>
  );
}
