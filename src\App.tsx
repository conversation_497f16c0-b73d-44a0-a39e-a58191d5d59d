
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SupabaseProvider } from "@/lib/supabase/provider";

// Context Providers
import { AuthProvider, useAuth } from "@/hooks/auth.basic";
import { CartProvider } from "@/hooks/useCart";
import { SavedItemsProvider } from "@/hooks/useSavedItems";
import { WishlistsProvider } from "@/hooks/useWishlists";

// Layouts
import ShopLayout from "@/components/layout/ShopLayout";
import AdminLayout from "@/components/layout/AdminLayout";
import ProtectedRoute from "@/components/layout/ProtectedRoute";

// Pages
import Index from "./pages/Index";
import AboutUs from "./pages/AboutUs";
import ContactPage from "./pages/ContactPage";
import NotFound from "./pages/NotFound";
import AuthPage from "./pages/auth/AuthPage";
import ShopPage from "./pages/shop/ShopPage";
import ProductDetailPage from "./pages/shop/ProductDetailPage";
import DashboardPage from "./pages/admin/DashboardPage";
import UsersPage from "./pages/admin/UsersPage";
import ProductsPage from "./pages/admin/ProductsPage";
// Product import components have been removed as they were only needed for the WIX import
import CategoriesPage from "./pages/admin/CategoriesPage";
import OrdersPage from "./pages/admin/OrdersPage";
import BrandsPage from "./pages/admin/BrandsPage";
import DiscountCodesPage from "./pages/admin/DiscountCodesPage";
import ShippingPage from "./pages/admin/ShippingPage";
import SettingsPage from "./pages/admin/SettingsPage";
import FAQsPage from "./pages/admin/FAQsPage";
import AccountPage from "./pages/AccountPage";
import EnhancedCheckoutPage from "./pages/EnhancedCheckoutPage";
import OrderDetailsPage from "./pages/OrderDetailsPage";
import WishlistPage from "./pages/WishlistPage";
import TestImagesPage from "./pages/TestImagesPage";
import FAQPage from "./pages/FAQPage";
import BlogPage from "./pages/BlogPage";
import BlogDetailPage from "./pages/BlogDetailPage";
import BlogAdminPage from "./pages/admin/BlogAdminPage";
import BlogEditorPage from "./pages/admin/BlogEditorPage";
import NewsletterPage from "./pages/admin/NewsletterPage";
import NewsletterEditorPage from "./pages/admin/NewsletterEditorPage";
import EPOSPage from "./pages/admin/EPOSPage";
import SeedFiltersPage from "./pages/admin/SeedFiltersPage";
import AIManagementPage from "./pages/admin/AIManagementPage";
import SocialMediaPage from "./pages/admin/SocialMediaPage";
import ImageScrapingPage from "./pages/admin/ImageScrapingPage";
import GoogleImageTest from "./components/GoogleImageTest";
import GoogleApiTest from "./components/GoogleApiTest";
import TestProductSelector from "./pages/TestProductSelector";
import TestNewsletterTemplates from "./pages/TestNewsletterTemplates";
import PrivacyPolicyPage from "./pages/PrivacyPolicyPage";
import TermsOfServicePage from "./pages/TermsOfServicePage";
import ShippingPolicyPage from "./pages/ShippingPolicyPage";
import ReturnsPolicyPage from "./pages/ReturnsPolicyPage";
import RawBrandPage from "./pages/brands/RawBrandPage";
import ConfirmationPage from "./pages/checkout/ConfirmationPage";

const queryClient = new QueryClient();

function App() {
  return (
    <>
      <Toaster />
      <Sonner />
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <SupabaseProvider>
            <AuthProvider>
              <CartProvider>
                <SavedItemsProvider>
                  <BrowserRouter>
                  <Routes>
                    {/* Public Routes */}
                    <Route element={<WishlistsProvider><ShopLayout /></WishlistsProvider>}>
                      <Route index element={<Index />} />
                      <Route path="about" element={<AboutUs />} />
                      <Route path="contact" element={<ContactPage />} />
                      <Route path="faq" element={<FAQPage />} />
                      <Route path="blog" element={<BlogPage />} />
                      <Route path="blog/:slug" element={<BlogDetailPage />} />
                      <Route path="shop" element={<ShopPage />} />
                      <Route path="shop/:slug" element={<ProductDetailPage />} />
                      <Route path="brands/raw" element={<RawBrandPage />} />
                      <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
                      <Route path="terms-of-service" element={<TermsOfServicePage />} />
                      <Route path="shipping-policy" element={<ShippingPolicyPage />} />
                      <Route path="returns-policy" element={<ReturnsPolicyPage />} />
                      <Route path="test-images" element={<TestImagesPage />} />
                      <Route path="test-product-selector" element={<TestProductSelector />} />
                      <Route path="test-newsletter-templates" element={<TestNewsletterTemplates />} />
                      <Route path="google-images" element={<GoogleImageTest />} />
                      <Route path="google-api-test" element={<GoogleApiTest />} />
                      <Route path="checkout/confirmation" element={<ConfirmationPage />} />
                    </Route>

                    {/* Auth Routes */}
                    <Route path="auth" element={<AuthPage />} />

                    {/* Protected User Routes */}
                    <Route element={<ProtectedRoute />}>
                      <Route element={<WishlistsProvider><ShopLayout /></WishlistsProvider>}>
                        <Route path="account" element={<AccountPage />} />
                        <Route path="checkout" element={<EnhancedCheckoutPage />} />
                        <Route path="account/orders/:orderId" element={<OrderDetailsPage />} />
                        <Route path="wishlist" element={<WishlistPage />} />
                      </Route>
                    </Route>

                    {/* Protected Admin Routes */}
                    <Route element={<ProtectedRoute requireAdmin={true} />}>
                      <Route path="admin" element={<AdminLayout />}>
                        <Route index element={<DashboardPage />} />
                        <Route path="users" element={<UsersPage />} />
                        <Route path="products" element={<ProductsPage />} />
                        {/* Product import routes removed as they were only needed for the WIX import */}
                        <Route path="categories" element={<CategoriesPage />} />
                        <Route path="brands" element={<BrandsPage />} />
                        <Route path="seed-filters" element={<SeedFiltersPage />} />
                        <Route path="orders" element={<OrdersPage />} />
                        <Route path="discount-codes" element={<DiscountCodesPage />} />
                        <Route path="shipping" element={<ShippingPage />} />
                        <Route path="settings" element={<SettingsPage />} />
                        <Route path="faqs" element={<FAQsPage />} />
                        <Route path="epos" element={<EPOSPage />} />
                        <Route path="blogs" element={<BlogAdminPage />} />
                        <Route path="blogs/new" element={<BlogEditorPage />} />
                        <Route path="blogs/edit/:id" element={<BlogEditorPage />} />
                        <Route path="newsletter" element={<NewsletterPage />} />
                        <Route path="newsletter/create" element={<NewsletterEditorPage />} />
                        <Route path="ai-management" element={<AIManagementPage />} />
                        <Route path="social-media" element={<SocialMediaPage />} />
                        <Route path="image-scraping" element={<ImageScrapingPage />} />
                      </Route>
                    </Route>

                    {/* Catch All Route */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  </BrowserRouter>
                </SavedItemsProvider>
              </CartProvider>
            </AuthProvider>
          </SupabaseProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </>
  );
}

export default App;
