import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/use-toast';

// Mock user profile
const mockUser = {
  id: 'mock-user-id',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
};

// Mock profile
const mockProfile = {
  id: 'mock-profile-id',
  user_id: 'mock-user-id',
  first_name: 'Admin',
  last_name: 'User',
  is_admin: true,
};

export const useMockAuth = () => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);

  // Initialize mock auth
  useEffect(() => {
    // Check if user is stored in localStorage
    const storedUser = localStorage.getItem('mockUser');
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser);
      setProfile(mockProfile);
      setIsAdmin(true);
      
      // Create a mock session
      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_at: Date.now() + 3600 * 1000,
        user: parsedUser
      };
      setSession(mockSession);
    }
    
    setInitializing(false);
  }, []);

  // Mock sign in function
  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    
    try {
      // Simple validation - in a real app you'd check against real credentials
      if (email === '<EMAIL>' && password === 'password') {
        // Set mock user
        setUser(mockUser);
        setProfile(mockProfile);
        setIsAdmin(true);
        
        // Store in localStorage
        localStorage.setItem('mockUser', JSON.stringify(mockUser));
        
        // Create a mock session
        const mockSession = {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          expires_at: Date.now() + 3600 * 1000,
          user: mockUser
        };
        setSession(mockSession);
        
        toast({
          title: 'Login successful',
          description: 'Welcome back, Admin!',
        });
        
        return { user: mockUser, session: mockSession };
      } else {
        throw new Error('Invalid email or password');
      }
    } catch (error) {
      toast({
        title: 'Login failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  // Mock sign out function
  const signOut = useCallback(async () => {
    setLoading(true);
    
    try {
      // Clear user data
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      setSession(null);
      
      // Remove from localStorage
      localStorage.removeItem('mockUser');
      
      toast({
        title: 'Logged out',
        description: 'You have been successfully logged out.',
      });
      
      return { error: null };
    } catch (error) {
      toast({
        title: 'Logout failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    user,
    session,
    profile,
    isAdmin,
    loading,
    initializing,
    signIn,
    signOut,
  };
};
