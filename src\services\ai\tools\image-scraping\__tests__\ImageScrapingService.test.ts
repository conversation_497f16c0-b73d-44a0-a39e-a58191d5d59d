/**
 * ImageScrapingService.test.ts
 * 
 * Tests for the ImageScrapingService component
 */

import { ImageScrapingService } from '../ImageScrapingService';
import { MCPConnectionManager } from '../../mcp-integration/MCPConnectionManager';
import { MCPErrorHandler } from '../../mcp-integration/MCPErrorHandler';
import { SourceManager } from '../SourceManager';
import { ImageQualityAssessor } from '../ImageQualityAssessor';
import { PlaywrightImageScraper } from '../PlaywrightImageScraper';
import { BulkImageProcessor } from '../BulkImageProcessor';
import { ProductImage, RetailerSource } from '../types/ImageScrapingTypes';

// Mock dependencies
jest.mock('../../mcp-integration/MCPConnectionManager');
jest.mock('../../mcp-integration/MCPErrorHandler');
jest.mock('../SourceManager');
jest.mock('../ImageQualityAssessor');
jest.mock('../PlaywrightImageScraper');
jest.mock('../BulkImageProcessor');

describe('ImageScrapingService', () => {
  let service: ImageScrapingService;
  let mockConnectionManager: jest.Mocked<MCPConnectionManager>;
  let mockErrorHandler: jest.Mocked<MCPErrorHandler>;
  let mockSourceManager: jest.Mocked<SourceManager>;
  let mockQualityAssessor: jest.Mocked<ImageQualityAssessor>;
  let mockClient: any;
  let mockScraper: jest.Mocked<PlaywrightImageScraper>;
  let mockBulkProcessor: jest.Mocked<BulkImageProcessor>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock client
    mockClient = {
      navigate: jest.fn(),
      snapshot: jest.fn(),
      waitFor: jest.fn()
    };
    
    // Create mock connection manager
    mockConnectionManager = new MCPConnectionManager() as jest.Mocked<MCPConnectionManager>;
    mockConnectionManager.getConnection = jest.fn().mockResolvedValue(mockClient);
    mockConnectionManager.releaseConnection = jest.fn();
    mockConnectionManager.closeAll = jest.fn().mockResolvedValue(undefined);
    
    // Create mock error handler
    mockErrorHandler = new MCPErrorHandler() as jest.Mocked<MCPErrorHandler>;
    mockErrorHandler.withRetry = jest.fn().mockImplementation((fn) => fn());
    
    // Create mock source manager
    mockSourceManager = new SourceManager() as jest.Mocked<SourceManager>;
    mockSourceManager.getAllSources = jest.fn().mockReturnValue([
      { name: 'Test Source', reliability_score: 8 } as RetailerSource
    ]);
    mockSourceManager.getSourcesByCategory = jest.fn().mockReturnValue([
      { name: 'Category Source', reliability_score: 7 } as RetailerSource
    ]);
    
    // Create mock quality assessor
    mockQualityAssessor = new ImageQualityAssessor() as jest.Mocked<ImageQualityAssessor>;
    mockQualityAssessor.assessImageQuality = jest.fn().mockResolvedValue({
      overall_score: 80,
      relevance_score: 85,
      technical_score: 75,
      dimensions_score: 70,
      source_reliability_score: 80,
      factors: {
        has_product_in_alt: true,
        good_dimensions: true,
        from_product_page: true,
        not_thumbnail: true,
        clear_image: true
      }
    });
    
    // Create mock scraper
    mockScraper = new PlaywrightImageScraper(null, []) as jest.Mocked<PlaywrightImageScraper>;
    mockScraper.findProductImages = jest.fn().mockResolvedValue([
      {
        url: 'https://example.com/test-image.jpg',
        alt: 'Test Product',
        quality_score: 80,
        source: 'Test Source'
      }
    ]);
    
    // Create mock bulk processor
    mockBulkProcessor = new BulkImageProcessor(null, null, null) as jest.Mocked<BulkImageProcessor>;
    mockBulkProcessor.processProducts = jest.fn().mockResolvedValue({
      total_products: 3,
      successful_products: 3,
      failed_products: 0,
      total_images_found: 6,
      average_images_per_product: 2,
      processing_time: 10,
      cost_savings: 0.3,
      errors: [],
      success_details: [
        {
          product_id: '1',
          product_name: 'Product 1',
          images_found: 2,
          best_image_url: 'https://example.com/product1-image.jpg'
        },
        {
          product_id: '2',
          product_name: 'Product 2',
          images_found: 2,
          best_image_url: 'https://example.com/product2-image.jpg'
        },
        {
          product_id: '3',
          product_name: 'Product 3',
          images_found: 2,
          best_image_url: 'https://example.com/product3-image.jpg'
        }
      ]
    });
    
    // Create service
    // @ts-ignore - We're mocking the dependencies
    service = new ImageScrapingService();
    
    // Replace private properties with mocks
    Object.defineProperty(service, 'connectionManager', { value: mockConnectionManager });
    Object.defineProperty(service, 'errorHandler', { value: mockErrorHandler });
    Object.defineProperty(service, 'sourceManager', { value: mockSourceManager });
    Object.defineProperty(service, 'qualityAssessor', { value: mockQualityAssessor });
  });

  describe('findProductImages', () => {
    it('should find images for a product', async () => {
      // Mock PlaywrightImageScraper constructor
      (PlaywrightImageScraper as jest.Mock).mockImplementation(() => mockScraper);
      
      // Define product
      const product = {
        name: 'Test Product',
        category: 'test',
        id: '123'
      };
      
      // Define options
      const options = {
        max_images: 5,
        min_quality_score: 50
      };
      
      // Find images
      const images = await service.findProductImages(product, options);
      
      // Check results
      expect(images).toBeDefined();
      expect(images.length).toBe(1);
      expect(images[0].url).toBe('https://example.com/test-image.jpg');
      expect(images[0].quality_score).toBe(80);
      
      // Check that connection was obtained and released
      expect(mockConnectionManager.getConnection).toHaveBeenCalled();
      expect(mockConnectionManager.releaseConnection).toHaveBeenCalledWith(mockClient);
      
      // Check that scraper was created with client and sources
      expect(PlaywrightImageScraper).toHaveBeenCalledWith(
        mockClient,
        expect.any(Array)
      );
      
      // Check that scraper was called with product and options
      expect(mockScraper.findProductImages).toHaveBeenCalledWith(
        product,
        expect.objectContaining(options)
      );
      
      // Check that error handler was used
      expect(mockErrorHandler.withRetry).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock PlaywrightImageScraper constructor
      (PlaywrightImageScraper as jest.Mock).mockImplementation(() => mockScraper);
      
      // Setup mock to throw error
      mockScraper.findProductImages.mockRejectedValue(new Error('Test error'));
      mockErrorHandler.withRetry.mockRejectedValue(new Error('Test error'));
      
      // Define product
      const product = {
        name: 'Test Product',
        category: 'test',
        id: '123'
      };
      
      // Find images and expect error
      await expect(service.findProductImages(product)).rejects.toThrow('Test error');
      
      // Check that connection was obtained and released
      expect(mockConnectionManager.getConnection).toHaveBeenCalled();
      expect(mockConnectionManager.releaseConnection).toHaveBeenCalledWith(mockClient);
    });
  });

  describe('bulkProcessProducts', () => {
    it('should process products in bulk', async () => {
      // Mock PlaywrightImageScraper constructor
      (PlaywrightImageScraper as jest.Mock).mockImplementation(() => mockScraper);
      
      // Mock BulkImageProcessor constructor
      (BulkImageProcessor as jest.Mock).mockImplementation(() => mockBulkProcessor);
      
      // Define products
      const products = [
        { name: 'Product 1', category: 'test', id: '1' },
        { name: 'Product 2', category: 'test', id: '2' },
        { name: 'Product 3', category: 'test', id: '3' }
      ];
      
      // Define options
      const options = {
        max_images: 3,
        min_quality_score: 60
      };
      
      // Process products
      const report = await service.bulkProcessProducts(products, options);
      
      // Check report
      expect(report).toBeDefined();
      expect(report.total_products).toBe(3);
      expect(report.successful_products).toBe(3);
      expect(report.failed_products).toBe(0);
      
      // Check that connection was obtained and released
      expect(mockConnectionManager.getConnection).toHaveBeenCalled();
      expect(mockConnectionManager.releaseConnection).toHaveBeenCalledWith(mockClient);
      
      // Check that scraper was created
      expect(PlaywrightImageScraper).toHaveBeenCalledWith(
        mockClient,
        expect.any(Array)
      );
      
      // Check that bulk processor was created
      expect(BulkImageProcessor).toHaveBeenCalledWith(
        mockScraper,
        expect.any(Object),
        expect.any(Object),
        expect.any(Object)
      );
      
      // Check that bulk processor was called with products and options
      expect(mockBulkProcessor.processProducts).toHaveBeenCalledWith(
        products,
        expect.objectContaining(options)
      );
      
      // Check that error handler was used
      expect(mockErrorHandler.withRetry).toHaveBeenCalled();
    });
  });

  describe('validateImageQuality', () => {
    it('should validate image quality', async () => {
      // Define image URL and product name
      const imageUrl = 'https://example.com/test-image.jpg';
      const productName = 'Test Product';
      
      // Validate image quality
      const qualityScore = await service.validateImageQuality(imageUrl, productName);
      
      // Check quality score
      expect(qualityScore).toBeDefined();
      expect(qualityScore.overall_score).toBe(80);
      expect(qualityScore.relevance_score).toBe(85);
      expect(qualityScore.technical_score).toBe(75);
      expect(qualityScore.dimensions_score).toBe(70);
      expect(qualityScore.source_reliability_score).toBe(80);
      
      // Check that quality assessor was called
      expect(mockQualityAssessor.assessImageQuality).toHaveBeenCalledWith(
        expect.objectContaining({
          url: imageUrl,
          alt: productName
        }),
        productName
      );
    });
  });

  describe('getAvailableSources', () => {
    it('should get all sources when no category provided', async () => {
      // Get available sources
      const sources = await service.getAvailableSources();
      
      // Check sources
      expect(sources).toBeDefined();
      expect(sources.length).toBe(1);
      expect(sources[0].name).toBe('Test Source');
      
      // Check that source manager was called
      expect(mockSourceManager.getAllSources).toHaveBeenCalled();
      expect(mockSourceManager.getSourcesByCategory).not.toHaveBeenCalled();
    });

    it('should get sources by category when category provided', async () => {
      // Get available sources for category
      const sources = await service.getAvailableSources('test');
      
      // Check sources
      expect(sources).toBeDefined();
      expect(sources.length).toBe(1);
      expect(sources[0].name).toBe('Category Source');
      
      // Check that source manager was called
      expect(mockSourceManager.getSourcesByCategory).toHaveBeenCalledWith('test');
      expect(mockSourceManager.getAllSources).not.toHaveBeenCalled();
    });
  });

  describe('checkHealth', () => {
    it('should return healthy status when connection available', async () => {
      // Check health
      const health = await service.checkHealth();
      
      // Check health status
      expect(health).toBeDefined();
      expect(health.status).toBe('healthy');
      expect(health.sources_available).toBe(1);
      expect(health.error_rate).toBe(0);
      
      // Check that connection was obtained and released
      expect(mockConnectionManager.getConnection).toHaveBeenCalled();
      expect(mockConnectionManager.releaseConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should return down status when connection unavailable', async () => {
      // Setup mock to throw error
      mockConnectionManager.getConnection.mockRejectedValue(new Error('Connection error'));
      
      // Check health
      const health = await service.checkHealth();
      
      // Check health status
      expect(health).toBeDefined();
      expect(health.status).toBe('down');
      expect(health.sources_available).toBe(1);
      expect(health.error_rate).toBe(1);
      
      // Check that connection was attempted
      expect(mockConnectionManager.getConnection).toHaveBeenCalled();
      expect(mockConnectionManager.releaseConnection).not.toHaveBeenCalled();
    });

    it('should return degraded status when no sources available', async () => {
      // Setup mock to return empty sources
      mockSourceManager.getAllSources.mockReturnValue([]);
      
      // Check health
      const health = await service.checkHealth();
      
      // Check health status
      expect(health).toBeDefined();
      expect(health.status).toBe('degraded');
      expect(health.sources_available).toBe(0);
      expect(health.error_rate).toBe(1);
      
      // Check that connection was not attempted
      expect(mockConnectionManager.getConnection).not.toHaveBeenCalled();
    });
  });

  describe('close', () => {
    it('should close all connections', async () => {
      // Close service
      await service.close();
      
      // Check that connections were closed
      expect(mockConnectionManager.closeAll).toHaveBeenCalled();
    });

    it('should handle errors when closing connections', async () => {
      // Setup mock to throw error
      mockConnectionManager.closeAll.mockRejectedValue(new Error('Close error'));
      
      // Close service (should not throw)
      await service.close();
      
      // Check that close was attempted
      expect(mockConnectionManager.closeAll).toHaveBeenCalled();
    });
  });
});
